package com.tiangong.order.remote.response;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2023/8/19 15:20
 * @Description:
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class SupplyOrderAdditionalChargesQueryResponseDTO {

    /**
     * id号
     */
    private Integer id;
    /**
     * 附加费类型
     * 0:其他
     */
    private Integer additionalChargesType;
    /**
     * 附加费名称
     */
    private String additionalChargesName;
    /**
     * 订单id
     */
    private Integer orderId;
    /**
     * 供货单id
     */
    private Integer supplyOrderId;
    /**
     * 供货单编号
     */
    private String supplyOrderCode;
    /**
     * 日期
     */
    private Date additionalChargesDate;
    /**
     * 数量
     */
    private Integer quantity;
    /**
     * 附加费
     */
    private BigDecimal additionalCharges;

}
