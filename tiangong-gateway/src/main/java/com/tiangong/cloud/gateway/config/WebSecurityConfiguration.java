//package com.mongoso.cloud.gateway.config;
//
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.security.config.annotation.method.configuration.EnableReactiveMethodSecurity;
//import org.springframework.security.config.annotation.web.reactive.EnableWebFluxSecurity;
//import org.springframework.security.config.web.server.ServerHttpSecurity;
//import org.springframework.security.web.server.SecurityWebFilterChain;
//import org.springframework.security.web.server.csrf.CookieServerCsrfTokenRepository;
//import org.springframework.stereotype.Component;

/**
 * 如果网关引入  spring-boot-starter-security
 * 就需要这个配置，关闭csrf
 */
//@Component
//@EnableWebFluxSecurity
//class WebSecurityConfiguration {
//
//    @Bean
//    SecurityWebFilterChain SecurityWebFilterChain(ServerHttpSecurity http) {
//
//        http.csrf().disable();
//        return http.build();
//    }
//}