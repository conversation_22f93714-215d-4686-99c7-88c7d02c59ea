package ${package}.${moduleName}.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import ${package}.${moduleName}.mapper.${className}Mapper;
import ${package}.${moduleName}.domain.entity.${className}Entity;
import ${package}.${moduleName}.domain.req.${className}Req;
import ${package}.${moduleName}.domain.resp.${className}Resp;
import ${package}.${moduleName}.service.${className}Service;
import ${package}.common.domain.LoginUser;
import ${package}.common.enums.result.ResultEnum;
import ${package}.common.exception.SysException;
import ${package}.commonbean.utils.PageVo;
import ${package}.commonbean.config.security.TokenManager;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * ${comments}
 *
 * <AUTHOR>
 * @email ${email}
 * @date ${datetime}
 */
@Service
public class ${className}ServiceImpl extends ServiceImpl<${className}Mapper, ${className}Entity> implements ${className}Service {

    @Autowired
    private ${className}Mapper ${classname}Mapper;

    @Autowired
    HttpServletRequest request;

    /**
     * ${comments}新增
     */
    @Override
    @Transactional
    public int ${classname}Add(${className}Req req) {
        LoginUser loginUser = TokenManager.getUser(request);
            ${className}Entity entity = new ${className}Entity();
        BeanUtils.copyProperties(req, entity);
        entity.setCreatedDt(new Date());
        entity.setCreatedBy(loginUser.getFullUserName());
        // ===========开始业务==============

        // ===========结束业务==============
        int insert = ${classname}Mapper.insert(entity);
        if (insert != 1) {
            throw new SysException(ResultEnum.E_1001.getCode(), ResultEnum.E_1001.getMessage());
        }
        return insert;
    }

    /**
     * ${comments}删除
     */
    @Override
    @Transactional
    public int ${classname}Del(${className}Req req) {
        LoginUser loginUser = TokenManager.getUser(request);
        // ===========开始业务==============

        // ===========结束业务==============
        int insert = ${classname}Mapper.deleteById(#foreach($column in $columns)#if($velocityCount==1)req.get${column.attrName}()#end#end);
        if (insert != 1) {
            throw new SysException(ResultEnum.E_1002.getCode(), ResultEnum.E_1002.getMessage());
        }
        return insert;
    }

    /**
     * ${comments}编辑
     */
    @Override
    @Transactional
    public int ${classname}Edit(${className}Req req) {
        LoginUser loginUser = TokenManager.getUser(request);
            ${className}Entity entity = new ${className}Entity();
        BeanUtils.copyProperties(req, entity);
        entity.setUpdatedDt(new Date());
        entity.setUpdatedBy(loginUser.getFullUserName());
        // ===========开始业务==============

        // ===========结束业务==============
        int insert = ${classname}Mapper.updateById(entity);
        if (insert != 1) {
            throw new SysException(ResultEnum.E_1003.getCode(), ResultEnum.E_1003.getMessage());
        }
        return insert;
    }

    /**
     * ${comments}详情
     */
    @Override
    public ${className}Resp ${classname}Detail(${className}Req req) {
        LoginUser loginUser = TokenManager.getUser(request);
            ${className}Entity entity = ${classname}Mapper.selectById(#foreach($column in $columns)#if($velocityCount==1)req.get${column.attrName}()#end#end);
            ${className}Resp resp = new ${className}Resp();
        BeanUtils.copyProperties(entity, resp);
        // ===========开始业务==============

        // ===========结束业务==============
        return resp;
    }

    /**
     * ${comments}列表（全部）
     */
    @Override
    public List<${className}Resp> ${classname}List(${className}Req req) {
        LoginUser loginUser = TokenManager.getUser(request);
        List<${className}Entity> list = ${classname}Mapper.selectList(null);
        List<${className}Resp> collect = list.stream().map((item) -> {
                ${className}Resp resp = new ${className}Resp();
            BeanUtils.copyProperties(item, resp);
            return resp;
        }).collect(Collectors.toList());

        return collect;
    }

    /**
     * ${comments}列表（分页）
     */
    @Override
    public PageVo ${classname}Page(${className}Req req) {
        LoginUser loginUser = TokenManager.getUser(request);
        Page<${className}Entity> teacherPage = new Page<${className}Entity>(req.getPage(), req.getPageSize());
        QueryWrapper<${className}Entity> wrapper = new QueryWrapper<>();
        // ===========开始业务==============

        // ===========结束业务==============
        IPage<${className}Entity> ipage = ${classname}Mapper.selectPage(teacherPage, wrapper);

        List<${className}Resp> collect = ipage.getRecords().stream().map((item) -> {
                ${className}Resp resp = new ${className}Resp();
            BeanUtils.copyProperties(item, resp);
            return resp;
        }).collect(Collectors.toList());

        return PageVo.result(ipage, collect);
    }


}