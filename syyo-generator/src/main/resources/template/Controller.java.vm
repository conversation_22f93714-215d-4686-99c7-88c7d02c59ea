package ${package}.${moduleName}.controller;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import ${package}.${moduleName}.domain.req.${className}Req;
import ${package}.${moduleName}.service.${className}Service;
import ${package}.common.domain.ResultVo;
import ${package}.common.utils.R;

/**
 * ${comments}
 *
 * <AUTHOR>
 * @email ${email}
 * @date ${datetime}
 */
@RestController
@RequestMapping("/${moduleName}/${classname}")
public class ${className}Controller {

    @Autowired
    private ${className}Service ${classname}Service;

    /**
     * ${comments}新增
     */
    @PostMapping("/${classname}Add")
    @PreAuthorize("@syyo.check('${moduleName}:${classname}')")
    public ResultVo add(@RequestBody ${className}Req req) {
        return R.ok(${classname}Service.${classname}Add(req));
    }

    /**
     * ${comments}删除
     */
    @PostMapping("/${classname}Del")
    @PreAuthorize("@syyo.check('${moduleName}:${classname}')")
    public ResultVo del(@RequestBody ${className}Req req) {
        return R.ok(${classname}Service.${classname}Del(req));
    }

    /**
     * ${comments}编辑
     */
    @PostMapping("/${classname}Edit")
    @PreAuthorize("@syyo.check('${moduleName}:${classname}')")
    public ResultVo edit(@RequestBody ${className}Req req) {
        return R.ok(${classname}Service.${classname}Edit(req));
    }

    /**
     * ${comments}详情
     */
    @PostMapping("/${classname}Detail")
    public ResultVo findOne(@RequestBody ${className}Req req) {
        return R.ok(${classname}Service.${classname}Detail(req));
    }

    /**
     * ${comments}列表（全部）
     */
    @PostMapping("/${classname}List")
    public ResultVo findAll(@RequestBody ${className}Req req) {
        return R.ok(${classname}Service.${classname}List(req));
    }

    /**
     * ${comments}列表（分页）
     */
    @PostMapping("/${classname}Page")
    public ResultVo findPage(@RequestBody ${className}Req req) {
        return R.ok(${classname}Service.${classname}Page(req));
    }
}
