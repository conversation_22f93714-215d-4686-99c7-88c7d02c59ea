<template>
    <div>
        <!--卡片-->
        <el-card class="box-card">
            <!-- 搜索与添加区域 -->
            <el-button v-hasPermi="['admin','${moduleName}:${pathName}:add']" type="primary" small class="addBtn"
                       style="margin:10px" size="mini" icon="el-icon-plus" @click="addDialogVisible = true">新增
            </el-button>
            <!--表格-->
            <el-table :data="listForm" stripe border style="width: 100%">
                #foreach($column in $columns)
                    <el-table-column prop="${column.attrname}" header-align="center" align="center"
                                     label="${column.comments}"/>
                #end
                <!--操作-->
                <el-table-column label="操作">
                    <template slot-scope="scope">
                        <el-button v-hasPermi="['admin','${moduleName}:${pathName}:edit']" type="text" size="mini"
                                   @click="editBtn(scope.row.${pk.attrname})">修改
                        </el-button>
                        <el-button v-hasPermi="['admin','${moduleName}:${pathName}:del']" type="text" size="mini"
                                   @click="delBtn(scope.row.${pk.attrname})">修改
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>

            <!--分页区域-->
            <el-pagination
                    background
                    :current-page="pageNumber"
                    :page-sizes="[1, 2, 5, 10]"
                    :page-size="pageSize"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="total"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
            />

        </el-card>

        <!--添加对话框-->
        <el-dialog title="添加" :close-on-click-modal="false" :visible.sync="addDialogVisible" width="580px"
                   @close="addHandleClose">
      <span>
        <el-form ref="addFormRef" :model="addForm" :rules="addFormRules" label-width="100px">
            #foreach($column in $columns)
          <el-form-item label="${column.comments}" prop="${column.attrname}">
            <el-input v-model="addForm.${column.attrname}"/>
          </el-form-item>
            #end
        </el-form>
          <!--底部区域-->
      </span>
            <span slot="footer" class="dialog-footer">
        <el-button @click="addDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="addClick">确 定</el-button>
      </span>
        </el-dialog>

        <!--编辑对话框-->
        <el-dialog title="编辑" :close-on-click-modal="false" :visible.sync="editDialogVisible" width="580px"
                   @close="editHandleClose">
      <span>
        <el-form ref="editFormRef" :model="editForm" label-width="100px">
            #foreach($column in $columns)
          <el-form-item label="${column.comments}" prop="${column.attrname}">
            <el-input v-model="editForm.${column.attrname}"/>
          </el-form-item>
            #end
        </el-form>
          <!--底部区域-->
      </span>
            <span slot="footer" class="dialog-footer">
        <el-button @click="editDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="editClick">确 定</el-button>
      </span>
        </el-dialog>

    </div>
</template>

<script>
import {add, edit, del, get${className}, list${className} } from '@/api/${moduleName}/${classname}'

export default {
    name: '${className}',
    data() {
        return {
            addDialogVisible: false,
            editDialogVisible: false,
            pageNumber: 1,
            pageSize: 10,
            total: 0,
            listForm: [],
            addForm: {
        #foreach($column in $columns)
            ${column.attrname}:
            '',
        #end
    },
        editForm: {
        }
    ,
        addFormRules: {
            #foreach($column in $columns)
                ${column.attrname}:
                [
                    {required: true, message: '请输入${column.comments}', trigger: 'blur'},
                ],
            #end
        }
    }
    },
    created() {
        // 初始化列表
        this.initList${className}()
    },

methods: {
    // 添加按钮
    addBtn() {
        this.addDialogVisible = true
    },

    // 确认添加
addClick() {
        #
        [[this.$refs.addFormRef.validate(valid = > {]]
#
if (!valid) return
add(this.addForm).then(res = > {
if(res.code === 20000
)
{
#
[[this.$message.success(res.message)]]
#
// 刷新数据
this.initList$
{
className
}
()
}
// 关闭对话框
this.addDialogVisible = false
})
#
[[
})]]#
},

    // 编辑按钮
    editBtn(id) {
        this.editDialogVisible = true
        get$
        {
            className
        }
        (id).then(res = > {
            if(res.code === 20000
    )
        {
            this.editForm = res.data
        }
    })
    },

    // 确认编辑
editClick() {
        #
        [[this.$refs.editFormRef.validate(valid = > {]]
    #
    if (!valid) return
    edit(this.editForm).then(res = > {
    if(res.code === 20000
    )
    {
    #
    [[this.$message.success(res.message)]]
    #
    // 刷新数据
    this.initList$
    {
    className
    }
    ()
    }
    // 关闭对话框
    this.editDialogVisible = false
    })
    #
    [[
    }) ]]#
    },


        // 删除按钮
        delBtn(id) {
            this.$confirm('此操作将永久删除该用户, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() = > {
                // 确认操作
                del(id).then(res = > {
                    if(res.code === 20000
        )
            {
                #
                        [[this.$message.success(res.message)]]
                #
                        // 刷新数据
                        this.initList${className}()
            }
        })
        })
        },

        // 监听当前页的事件
        handleCurrentChange(pageIndex) {
            this.pageNumber = pageIndex
            this.initList${className}()
        },

        // 监听当前页的条数事件
        handleSizeChange(pageSize) {
            this.pageSize = pageSize
            this.initList${className}()
        },

        // 监听添加对话框的关闭事件，重置表单内容
        addHandleClose() {
            this.$refs.addFormRef.resetFields()
        },

        // 监听编辑对话框的关闭事件,重置表单内容
        editHandleClose() {
            this.$refs.editFormRef.resetFields()
        },

        // 初始化数据
        initList${className}() {
            const params = {
                pageNum: this.pageNumber,
                pageSize: this.pageSize
            }
            list${className}(params).then(res = > {
                this.listForm = res.data.records
                this.total = res.data.total
                this.pageNum = res.data.current
            }
        )
        }
    }
    }
    </script>

    <style scoped>
    </style>
