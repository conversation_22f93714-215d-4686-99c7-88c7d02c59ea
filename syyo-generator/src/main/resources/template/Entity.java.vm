package ${package}.${moduleName}.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;

    #foreach ($column in $columns)
        #if($column.attrType == 'LocalDateTime')
        import java.time.LocalDateTime;
        #end
        #if($column.attrType == 'LocalDate')
        import java.time.LocalDate;
        #end
        #if($column.attrType == 'Date')
        import java.util.Date;
        #end
        #if($column.attrType == 'BigDecimal')
        import java.math.BigDecimal;
        #end
    #end

import lombok.Data;

/**
 * ${comments}
 * 表的实体类，参数和表字段一一对应
 * <AUTHOR>
 * @email ${email}
 * @date ${datetime}
 */
@Data
@TableName("${tableName}")
public class ${className}Entity extends Model<${className}Entity> {

    private static final long serialVersionUID = 1L;

    #foreach ($column in $columns)
        /**
         * $column.comments
         */
        #if($column.columnName == $pk.columnName)
        @TableId(value = "${column.columnName}", type = IdType.AUTO)
        #end
    private $column.attrType $column.attrname;
    #end

}
