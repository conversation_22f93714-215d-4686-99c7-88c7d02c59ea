package ${package}.${moduleName}.domain.req;

import ${package}.common.domain.BasePage;
import lombok.Data;

    #foreach ($column in $columns)
        #if($column.attrType == 'LocalDateTime')
        import java.time.LocalDateTime;
        #end
        #if($column.attrType == 'LocalDate')
        import java.time.LocalDate;
        #end
        #if($column.attrType == 'Date')
        import java.util.Date;
        #end
        #if($column.attrType == 'BigDecimal')
        import java.math.BigDecimal;
        #end
    #end

import java.io.Serializable;

/**
 * ${comments}
 * 请求参数
 * <AUTHOR>
 * @email ${email}
 * @date ${datetime}
 */
@Data
public class ${className}Req extends BasePage implements Serializable {

    private static final long serialVersionUID = 1L;

    #foreach ($column in $columns)
        /**
         * $column.comments
         */
        private $column.attrType $column.attrname;
    #end
}