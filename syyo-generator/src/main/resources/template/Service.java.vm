package ${package}.${moduleName}.service;


import com.baomidou.mybatisplus.extension.service.IService;

import ${package}.${moduleName}.domain.entity.${className}Entity;
import ${package}.${moduleName}.domain.req.${className}Req;
import ${package}.${moduleName}.domain.resp.${className}Resp;
import ${package}.commonbean.utils.PageVo;

import java.util.List;

/**
 * ${comments}
 *
 * <AUTHOR>
 * @email ${email}
 * @date ${datetime}
 */
public interface ${className}Service extends IService<${className}Entity> {

    /**
     * ${comments}新增
     */
    int ${classname}Add(${className}Req req);

    /**
     * ${comments}删除
     */
    int ${classname}Del(${className}Req req);

    /**
     * ${comments}编辑
     */
    int ${classname}Edit(${className}Req req);

    /**
     * ${comments}详情
     */
        ${className}Resp ${classname}Detail(${className}Req req);

    /**
     * ${comments}列表（全部）
     */
    List<${className}Resp> ${classname}List(${className}Req req);

    /**
     * ${comments}列表（分页）
     */
    PageVo ${classname}Page(${className}Req req);

}

