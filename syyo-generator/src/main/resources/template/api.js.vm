import request from '@/utils/request'

export function add(data) {
    return request({
        url: '/${moduleName}/${pathName}',
        method: 'post',
        data
    })
}

export function del(id) {
    return request({
        url: '/${moduleName}/${pathName}/' + id,
        method: 'delete'
    })
}

export function edit(data) {
    return request({
        url: '/${moduleName}/${pathName}',
        method: 'put',
        data
    })
}

export function get${className}(id) {
    return request({
        url: '/${moduleName}/${pathName}/' + id,
        method: 'get'
    })
}

export function list${className}(params) {
    return request({
        url: '/${moduleName}/${pathName}/list/' + params.pageNum + '/' + params.pageSize,
        method: 'get',
        params
    })
}

export default {add, edit, del, get${className}, list${className} }