/*Grid*/
.ui-jqgrid {
    position: relative;
    border: 1px solid #ddd; /*default*/
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    border-radius: 3px;
}

.ui-jqgrid .ui-jqgrid-view {
    position: relative;
    left: 0;
    top: 0;
    padding: 0;
}

.ui-jqgrid .ui-common-table {
}

/* Caption*/
.ui-jqgrid .ui-jqgrid-titlebar {
    font-weight: normal;
    min-height: 37px;
    padding: 4px 8px;
    position: relative;
    margin-right: 2px;
    border-bottom: 1px solid #ddd; /*default*/

}

.ui-jqgrid .ui-jqgrid-caption {
    text-align: left;
}

.ui-jqgrid .ui-jqgrid-title {
    padding-top: 5px;
    vertical-align: middle;
}

.ui-jqgrid .ui-jqgrid-titlebar-close {
    color: inherit;
    position: absolute;
    top: 50%;
    margin: -10px 7px 0 0;
    padding: 1px;
    cursor: pointer;
}

.ui-jqgrid .ui-jqgrid-titlebar-close span {
    display: block;
    margin: 1px;
}

.ui-jqgrid .ui-jqgrid-titlebar-close:hover {
}

/* Header*/
.ui-jqgrid .ui-jqgrid-hdiv {
    position: relative;
    margin: 0;
    padding: 0;
    overflow: hidden;
}

.ui-jqgrid .ui-jqgrid-hbox {
    float: left;
    padding-right: 20px;
}

.ui-jqgrid .ui-jqgrid-htable {
    margin-bottom: 0;
    table-layout: fixed;
    border-top: none;
}

.ui-jqgrid .ui-jqgrid-htable thead th {
    overflow: hidden;
    border-bottom: none;
    padding-right: 2px;
}

.ui-jqgrid .ui-jqgrid-htable thead th div {
    overflow: hidden;
    position: relative;
}

.ui-th-column, .ui-jqgrid .ui-jqgrid-htable th.ui-th-column {
    overflow: hidden;
    white-space: nowrap;
}

.ui-th-column-header,
.ui-jqgrid .ui-jqgrid-htable th.ui-th-column-header {
    overflow: hidden;
    white-space: nowrap;
}

.ui-th-ltr, .ui-jqgrid .ui-jqgrid-htable th.ui-th-ltr {
}

.ui-th-rtl, .ui-jqgrid .ui-jqgrid-htable th.ui-th-rtl {
    text-align: center;
}

.ui-first-th-ltr {
}

.ui-first-th-rtl {
}

.ui-jqgrid tr.jqg-first-row-header th {
    height: auto;
    border-top: none;
    padding-bottom: 0;
    padding-top: 0;
    border-bottom: none;
    padding-right: 2px;
    text-align: center;
}

.ui-jqgrid tr.jqg-second-row-header th,
.ui-jqgrid tr.jqg-third--row-header th {
    border-top: none;
    text-align: center;
}

.ui-jqgrid .ui-th-div-ie {
    white-space: nowrap;
    zoom: 1;
    height: 17px;
}

.ui-jqgrid .ui-jqgrid-resize {
    height: 20px !important;
    position: relative;
    cursor: e-resize;
    display: inline;
    overflow: hidden;
}

.ui-jqgrid .ui-grid-ico-sort {
    margin-left: 5px;
    overflow: hidden;
    position: absolute;
    right: auto;
    font-size: 12px;
}

.ui-jqgrid .ui-icon-asc {
    margin-top: -3px;
}

.ui-jqgrid .ui-icon-desc {
    margin-top: 4px;
}

.ui-jqgrid .ui-i-asc {
    margin-top: 0;
}

.ui-jqgrid .ui-i-desc {
    margin-top: 0;
    margin-right: 13px;
}

.ui-jqgrid .ui-single-sort-asc {
    margin-top: 0;
}

.ui-jqgrid .ui-single-sort-desc {
}

.ui-jqgrid .ui-jqgrid-sortable {
    cursor: pointer;
}

.ui-jqgrid tr.ui-search-toolbar th {
}

.ui-jqgrid .ui-search-table td.ui-search-clear {
}

.ui-jqgrid tr.ui-search-toolbar td > input {
}

.ui-jqgrid tr.ui-search-toolbar select {
}

/* Body */
.ui-jqgrid .table-bordered,
.ui-jqgrid .table-bordered td,
.ui-jqgrid .table-bordered th.ui-th-ltr {
    border-left: 0px none !important;
}

.ui-jqgrid .table-bordered th.ui-th-rtl {
    border-right: 0px none !important;
}

.ui-jqgrid .table-bordered tr.ui-row-rtl td {
    border-right: 0px none !important;
    border-left: 1px solid #ddd !important;
}

div.tablediv > .table-bordered {
    border-left: 1px solid #ddd !important;
}

.ui-jqgrid .ui-jqgrid-bdiv table.table-bordered td {
    border-top: 0px none;
}

.ui-jqgrid .ui-jqgrid-bdiv {
    position: relative;
    margin: 0;
    padding: 0;
    overflow: auto;
    text-align: left;
}

.ui-jqgrid .ui-jqgrid-btable {
    table-layout: fixed;
    border-left: none;
    border-top: none;
    margin-bottom: 0px
}

.ui-jqgrid tr.jqgrow {
    outline-style: none;
}

.ui-jqgrid tr.jqgroup {
    outline-style: none;
}

.ui-jqgrid .ui-jqgrid-btable tbody tr.jqgrow td {
    overflow: hidden;
    white-space: pre;
    padding-right: 2px;
}

.ui-jqgrid tr.jqgfirstrow td {
    height: auto;
    border-top: none;
    padding-bottom: 0;
    padding-top: 0;
    border-bottom: none;
    padding-right: 2px;
}

.ui-jqgrid tr.jqgroup td {
    white-space: nowrap;
}

.ui-jqgrid tr.jqfoot td {
    white-space: nowrap;
}

.ui-jqgrid tr.ui-row-ltr td {
}

.ui-jqgrid tr.ui-row-rtl td {
}

.ui-jqgrid td.jqgrid-rownum {
}

.ui-jqgrid .ui-jqgrid-resize-mark {
    width: 2px;
    left: 0;
    background-color: #777;
    cursor: e-resize;
    cursor: col-resize;
    position: absolute;
    top: 0;
    height: 100px;
    overflow: hidden;
    display: none;
    border: 0 none;
    z-index: 99999;

}

/* Footer */
.ui-jqgrid .ui-jqgrid-sdiv {
    position: relative;
    margin: 0;
    padding: 0;
    overflow: hidden;
    border-left: 0 none !important;
    border-top: 0 none !important;
    border-right: 0 none !important;
}

.ui-jqgrid .ui-jqgrid-ftable {
    table-layout: fixed;
    margin-bottom: 0;
}

.ui-jqgrid tr.footrow td {
    font-weight: bold;
    overflow: hidden;
    white-space: nowrap;
    padding-right: 2px;
    border-bottom: 0px none;
}

.ui-jqgrid tr.footrow-ltr td {
    text-align: left;
}

.ui-jqgrid tr.footrow-rtl td {
    text-align: right;
}

/* Pager*/
.ui-jqgrid .ui-jqgrid-pager,
.ui-jqgrid .ui-jqgrid-toppager {
    border-left-width: 0px;
    border-top: 1px solid #ddd;
    /*padding : 4px 0px;*/
    position: relative;
    /*height: auto; */
    white-space: nowrap;
    overflow: hidden;
}

.ui-jqgrid .ui-jqgrid-toppager {
    border-top-width: 0;
    border-bottom: 1px solid #ddd;
}

.ui-jqgrid .ui-jqgrid-toppager .ui-pager-control,
.ui-jqgrid .ui-jqgrid-pager .ui-pager-control {
    position: relative;
    border-left: 0;
    border-bottom: 0;
    border-top: 0;
    /*height: 30px;*/
}

.ui-jqgrid .ui-pg-table {
    position: relative;
    padding: 1px 0;
    width: auto;
    margin: 0;
}

.ui-jqgrid .ui-pg-table td {
    font-weight: normal;
    vertical-align: middle;
    /*padding:0px 6px;*/
}

.ui-jqgrid .ui-pager-control .ui-pager-table td {
    border-top: 0px none !important;
    min-height: 20px !important;
}

.ui-jqgrid .ui-pg-button {
    height: auto;
}

.ui-jqgrid .ui-paging-pager td {
    padding: 0 5px;
}

.ui-jqgrid .ui-pg-button span {
    display: block;
    margin: 0px 2px;
    float: left;
}

.ui-jqgrid .ui-pg-button:hover {
}

.ui-jqgrid .ui-disabled:hover {
}

.ui-jqgrid .ui-pg-input,
.ui-jqgrid .ui-jqgrid-toppager .ui-pg-input {
    display: inline-block;
    height: auto;
    width: auto;
    font-size: .9em;
    margin: 0;
    line-height: inherit;
    border: none;
    padding: 0px 3px
}

.ui-jqgrid .ui-pg-selbox,
.ui-jqgrid .ui-jqgrid-toppager .ui-pg-selbox {
    font-size: .9em;
    line-height: inherit;
    display: block;
    height: 22px;
    margin: 0;
    padding: 3px 0px 3px 3px;
    border: none;
}

.ui-jqgrid .ui-separator {
    height: 18px;
    border: none;
    border-left: 2px solid #ccc; /*default*/

}

.ui-separator-li {
    height: 2px;
    border: none;
    border-top: 2px solid #ccc; /*default*/
    margin: 0;
    padding: 0;
    width: 100%
}

.ui-jqgrid .ui-jqgrid-pager .ui-pg-div,
.ui-jqgrid .ui-jqgrid-toppager .ui-pg-div {
    float: left;
    position: relative;
}

.ui-jqgrid .ui-jqgrid-pager .ui-pg-button,
.ui-jqgrid .ui-jqgrid-toppager .ui-pg-button {
    cursor: pointer;
}

.ui-jqgrid .ui-jqgrid-pager .ui-pg-div span,
.ui-jqgrid .ui-jqgrid-toppager .ui-pg-div span {
    float: left;
}

.ui-jqgrid td input,
.ui-jqgrid td select,
.ui-jqgrid td textarea {
    margin: 0;
}

.ui-jqgrid td textarea {
    width: auto;
    height: auto;
}

.ui-jqgrid .ui-jqgrid-pager .ui-pager-table,
.ui-jqgrid .ui-jqgrid-toppager .ui-pager-table {
    width: 100%;
    table-layout: fixed;
    height: 100%;
}

.ui-jqgrid .ui-jqgrid-pager .ui-paging-info,
.ui-jqgrid .ui-jqgrid-toppager .ui-paging-info {
    font-weight: normal;
    height: auto;
    margin-top: 3px;
    margin-right: 4px;
    display: inline;
}

.ui-jqgrid .ui-jqgrid-pager .ui-paging-pager,
.ui-jqgrid .ui-jqgrid-toppager .ui-paging-pager {
    table-layout: auto;
    height: 100%;
}

.ui-jqgrid .ui-jqgrid-pager .navtable,
.ui-jqgrid .ui-jqgrid-toppager .navtable {
    float: left;
    table-layout: auto;
    height: 100%;
}

.ui-jqgrid .ui-jqgrid-pager .navtable td,
.ui-jqgrid .ui-jqgrid-toppager .navtable td {
    padding: 0 5px;
}

/*Subgrid*/

.ui-jqgrid .ui-jqgrid-btable .ui-sgcollapsed span {
    display: block;
}

.ui-jqgrid .ui-subgrid {
    margin: 0;
    padding: 0;
    width: 100%;
}

.ui-jqgrid .ui-subgrid table {
    table-layout: fixed;
}

.ui-jqgrid .ui-subgrid tr.ui-subtblcell td {
}

.ui-jqgrid .ui-subgrid td.subgrid-data {
    border-top: 0 none !important;
}

.ui-jqgrid .ui-subgrid td.subgrid-cell {
    vertical-align: middle
}

.ui-jqgrid a.ui-sghref {
    text-decoration: none;
    color: #010101; /*default*/
}

.ui-jqgrid .ui-th-subgrid {
    height: 20px;
}

.tablediv > .row {
    margin: 0 0
}

/* loading */
.ui-jqgrid .loading {
    position: absolute;
    top: 45%;
    left: 45%;
    width: auto;
    z-index: 101;
    padding: 6px;
    margin: 5px;
    text-align: center;
    display: none;
    border: 1px solid #ddd; /*default*/
    font-size: 14px;
    background-color: #d9edf7;
}

.ui-jqgrid .jqgrid-overlay {
    display: none;
    z-index: 100;
}

/* IE * html .jqgrid-overlay {width: expression(this.parentNode.offsetWidth+'px');height: expression(this.parentNode.offsetHeight+'px');} */
* .jqgrid-overlay iframe {
    position: absolute;
    top: 0;
    left: 0;
    z-index: -1;
}

/* IE width: expression(this.parentNode.offsetWidth+'px');height: expression(this.parentNode.offsetHeight+'px');}*/
/* end loading div */

/* Toolbar */
.ui-jqgrid .ui-userdata {
    padding: 4px 0px;
    overflow: hidden;
    min-height: 32px;
}

.ui-jqgrid .ui-userdata-top {
    border-left-width: 0px; /*default*/
    border-bottom: 1px solid #ddd;
}

.ui-jqgrid .ui-userdata-bottom {
    border-left-width: 0px; /*default*/
    border-top: 1px solid #ddd;
}

/*Modal Window */
.ui-jqdialog {
}

.ui-jqdialog {
    display: none;
    width: 500px;
    position: absolute;
    /*padding: 5px; */
    overflow: visible;
}

.ui-jqdialog .ui-jqdialog-titlebar {
    padding: .1em .1em;
    min-height: 35px;
}

.ui-jqdialog .ui-jqdialog-title {
    margin: .3em 0 .2em;
    font-weight: bold;
    padding-left: 6px;
    padding-right: 6px;
}

.ui-jqdialog .ui-jqdialog-titlebar-close {
    position: absolute;
    top: 0%;
    margin: 3px 5px 0 0;
    padding: 8px;
    cursor: pointer;
}

.ui-jqdialog .ui-jqdialog-titlebar-close span {
}

.ui-jqdialog .ui-jqdialog-titlebar-close:hover,
.ui-jqdialog .ui-jqdialog-titlebar-close:focus {
    padding: 8px;
}

.ui-jqdialog-content, .ui-jqdialog .ui-jqdialog-content {
    border: 0;
    padding: .3em .2em;
    background: none;
    height: auto;
}

.ui-jqdialog .ui-jqconfirm {
    padding: .4em 1em;
    border-width: 3px;
    position: absolute;
    bottom: 10px;
    right: 10px;
    overflow: visible;
    display: none;
    height: 120px;
    width: 220px;
    text-align: center;
    background-color: #fff;
    border-radius: 4px;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
}

.ui-jqdialog > .ui-resizable-se {
}

.ui-jqgrid > .ui-resizable-se {
}

/* end Modal window*/
/* Form edit */
.ui-jqdialog-content .FormGrid {
    margin: 0 8px 0 8px;
    overflow: auto;
    position: relative;
}

.ui-jqdialog-content .EditTable {
    width: 100%;
    margin-bottom: 0;
}

.ui-jqdialog-content .DelTable {
    width: 100%;
    margin-bottom: 0;
}

.EditTable td input,
.EditTable td select,
.EditTable td textarea {
    width: 98%;
    display: inline-block;
}

.EditTable td textarea {
    width: auto;
    height: auto;
}

.EditTable .FormData td {
    height: 37px !important;
}

.ui-jqdialog-content td.EditButton {
    text-align: right;
    padding: 5px 5px 5px 0;
}

.ui-jqdialog-content td.navButton {
    text-align: center;
    border-left: 0 none;
    border-top: 0 none;
    border-right: 0 none;
    padding-bottom: 5px;
    padding-top: 5px;
}

.ui-jqdialog-content input.FormElement {
    padding: .5em .3em;
    margin-bottom: 5px
}

.ui-jqdialog-content select.FormElement {
    padding: .3em;
    margin-bottom: 3px;
}

.ui-jqdialog-content .data-line {
    padding-top: .1em;
    border: 0 none;
}

.ui-jqdialog-content .CaptionTD {
    vertical-align: middle;
    border: 0 none;
    padding: 2px;
    white-space: nowrap;
}

.ui-jqdialog-content .DataTD {
    padding: 2px;
    border: 0 none;
    vertical-align: top;
}

.ui-jqdialog-content .form-view-data {
    white-space: pre
}

.fm-button {
}

.fm-button-icon-left {
    margin-left: 4px;
    margin-right: 4px;
}

.fm-button-icon-right {
    margin-left: 4px;
    margin-right: 4px;
}

.fm-button-icon-left {
}

.fm-button-icon-right {
}

#nData, #pData {
    margin-left: 4px;
    margin-right: 4px;
}

#sData span, #cData span {
    margin-left: 5px;
}

/* End Eorm edit */
/*.ui-jqgrid .edit-cell {}*/
.ui-jqgrid .selected-row,
div.ui-jqgrid .selected-row td {
    font-style: normal;
}

/* inline edit actions button*/
.ui-inline-del, .ui-inline-cancel {
    margin-left: 14px;
}

.ui-jqgrid .inline-edit-cell {
}

/* Tree Grid */
.ui-jqgrid .tree-wrap {
    float: left;
    position: relative;
    height: 18px;
    white-space: nowrap;
    overflow: hidden;
}

.ui-jqgrid .tree-minus {
    position: absolute;
    height: 18px;
    width: 18px;
    overflow: hidden;
}

.ui-jqgrid .tree-plus {
    position: absolute;
    height: 18px;
    width: 18px;
    overflow: hidden;
}

.ui-jqgrid .tree-leaf {
    position: absolute;
    height: 18px;
    width: 18px;
    overflow: hidden;
}

.ui-jqgrid .treeclick {
    cursor: pointer;
}

/* moda dialog */
* iframe.jqm {
    position: absolute;
    top: 0;
    left: 0;
    z-index: -1;
}

/*	 width: expression(this.parentNode.offsetWidth+'px');height: expression(this.parentNode.offsetHeight+'px');}*/
.ui-jqgrid-dnd tr td {
    border-right-width: 1px;
    border-right-color: inherit;
    border-right-style: solid;
    height: 20px
}

/* RTL Support */
.ui-jqgrid .ui-jqgrid-caption-rtl {
    text-align: right;
}

.ui-jqgrid .ui-jqgrid-hbox-rtl {
    float: right;
    /*padding-left: 20px;*/
}

.ui-jqgrid .ui-jqgrid-resize-ltr {
    float: right;
    margin: -2px -2px -2px 0;
    height: 100%;
}

.ui-jqgrid .ui-jqgrid-resize-rtl {
    float: left;
    margin: -2px -2px -2px -0px;
}

.ui-jqgrid .ui-sort-rtl {

}

.ui-jqgrid .tree-wrap-ltr {
    float: left;
}

.ui-jqgrid .tree-wrap-rtl {
    float: right;
}

.ui-jqgrid .ui-ellipsis {
    -moz-text-overflow: ellipsis;
    text-overflow: ellipsis;
}

/* Toolbar Search Menu. Nav menu */
.ui-search-menu,
.ui-nav-menu {
    position: absolute;
    padding: 2px 5px;
    z-index: 99999;
}

.ui-search-menu.ui-menu .ui-menu-item,
.ui-nav-menu.ui-menu .ui-menu-item {
    list-style-image: none;
    padding-right: 0;
    padding-left: 0;
}

.ui-search-menu.ui-menu .ui-menu-item a,
.ui-nav-menu.ui-menu .ui-menu-item a {
    display: block;
}

.ui-search-menu.ui-menu .ui-menu-item a.g-menu-item:hover,
.ui-nav-menu.ui-menu .ui-menu-item a.g-menu-item:hover {
    margin: -1px;
    font-weight: normal;
}

.ui-jqgrid .ui-search-table {
    padding: 0;
    border: 0 none;
    height: 20px;
    width: 100%;
}

.ui-jqgrid .ui-search-table .ui-search-oper {
    width: 20px;
}

a.g-menu-item, a.soptclass, a.clearsearchclass {
    cursor: pointer;
}

.ui-jqgrid .ui-jqgrid-view input,
.ui-jqgrid .ui-jqgrid-view select,
.ui-jqgrid .ui-jqgrid-view textarea,
.ui-jqgrid .ui-jqgrid-view button {
    /*font-size: 11px*/
}

.ui-jqgrid .ui-scroll-popup {
    width: 100px;
}

.ui-search-table select,
.ui-search-table input {
    padding: 4px 3px;
}

.ui-disabled {
    opacity: .35;
    filter: Alpha(Opacity=35); /* support: IE8 */
    background-image: none;
}

.ui-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    opacity: .3;
    filter: Alpha(Opacity=30); /* support: IE8 */
}

.ui-jqgrid-pager .ui-pg-table .ui-pg-button:hover,
.ui-jqgrid-toppager .ui-pg-table .ui-pg-button:hover {
    background-color: #ddd;
}

.ui-jqgrid-corner {
    border-radius: 5px
}

.ui-resizable-handle {
    /*position: absolute;*/
    display: block;
    left: 97%;
}

.ui-jqdialog .ui-resizable-se {
    width: 12px;
    height: 12px;
    right: -5px;
    bottom: -5px;
    background-position: 16px 16px;
}

.ui-resizable-se {
    cursor: se-resize;
    width: 12px;
    height: 12px;
    right: 1px;
    bottom: 1px;
}

.ui-top-corner {
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
}

.ui-bottom-corner {
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
}

.ui-search-table {
    margin-bottom: 0;
}

.ui-search-table .columns, .ui-search-table .operators {
    padding-right: 5px;
}

.opsel {
    float: left;
    width: 100px;
    margin-right: 5px;
}

.add-group, .add-rule, .delete-group {
    width: 14%;
    margin-right: 5px;
}

.delete-rule {
    width: 15px;
}

ul.ui-search-menu, ul.ui-nav-menu {
    list-style-type: none;
}

ul.ui-search-menu li a,
ul.ui-nav-menu li a,
.soptclass,
.clearsearchclass {
    text-decoration: none;
    color: #010101;
}

ul.ui-search-menu li a:hover, ul.ui-nav-menu li a:hover, a.soptclass:hover, a.clearsearchclass:hover {
    background-color: #ddd;
    padding: 1px 1px;
    text-decoration: none;
}

ul.ui-search-menu li, ul.ui-nav-menu li {
    padding: 5px 5px;
}

.ui-menu-item hr {
    margin-bottom: 0px;
    margin-top: 0px;
}

.searchFilter .ui-search-table td,
.searchFilter .ui-search-table th {
    border-top: 0px none !important;
}

.searchFilter .queryresult {
    margin-bottom: 5px;
}

.searchFilter .queryresult tr td {
    border-top: 0px none;
}

.ui-search-label {
    padding-left: 5px;
}

.frozen-div, .frozen-bdiv {
    background-color: #fff;
}

/*
.ui-jqgrid .ui-jqgrid-caption,
.ui-jqgrid .ui-jqgrid-pager,
.ui-jqgrid .ui-jqgrid-toppager,
.ui-jqgrid .ui-jqgrid-htable thead th,
.ui-jqgrid .ui-userdata-top,
.ui-jqgrid .ui-userdata-bottom,
.ui-jqgrid .ui-jqgrid-hdiv,
.ui-jqdialog .ui-jqdialog-titlebar
{
    background-image: none, linear-gradient(to bottom, #fff 0px, #e0e0e0 100%);
    background-repeat: repeat-x;
    border-color: #ccc;
    text-shadow: 0 1px 0 #fff;
}
*/
/* Column menu */
.ui-jqgrid .ui-jqgrid-htable .colmenu {
    position: absolute;
    right: 1px;
    height: 100%;
    color: black;
}

.ui-jqgrid .ui-jqgrid-htable .colmenu-rtl {
    right: auto;
    left: 1px;
}

.ui-jqgrid .ui-jqgrid-htable .colmenuspan {
    display: inline-block;
}

.ui-jqgrid .ui-jqgrid-htable .ui-th-div {
    height: 17px;
    margin-top: 5px;
    display: inine-block;
}

.column-menu, .ui-search-menu {
    padding: 10px 15px;
}

.column-menu .divider {
    background-color: #e5e5e5;
    height: 1px;
    padding: 0 0;
    margin: 5px 0;
    overflow: hidden;
}

.ui-menu-item .ui-common-table .menu_icon {
    padding-right: 8px;
    padding-left: 8px;
}

td.menu_text {
    width: auto;
    padding-left: 1px;
    white-space: nowrap;
}

#search_menu .ui-menu-item div {
    padding: 3px 0;
    white-space: nowrap;
}

#search_menu .search_buttons {
    display: inline-block;
    width: 50%;
    padding: 3px 3px;
}

.search_buttons .btn-default:hover {
    padding-bottom: 6px;
    padding-top: 6px;
}

.search_buttons #bs_reset {
    margin-right: 3px;
}

.search_buttons #bs_search {
    margin-left: 3px;
}