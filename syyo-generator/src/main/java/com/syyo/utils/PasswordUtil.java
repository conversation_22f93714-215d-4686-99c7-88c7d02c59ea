package com.syyo.utils;

/**
 * @author: wang
 * @date: 2021/1/15 16:33
 * @Description:
 */
public class PasswordUtil {
    public static void main(String[] args) {
//        String password = getPassword(16, true, true, false);
//        System.out.println(password);


        String aa = "p_aa_bb";


        int i = aa.indexOf("_");


        String substring = aa.substring(aa.indexOf("_") + 1, aa.length());
        System.out.println(substring);


    }

    public static String getPassword() {
        return getPassword(16, true, true, true);
    }

    public static String getPassword(int num) {
        return getPassword(num, true, true, true);
    }

    public static String getPassword(int num, boolean inNum) {
        return getPassword(num, inNum, true, true);
    }

    public static String getPassword(int num, boolean inNum, boolean inStr) {
        return getPassword(num, inNum, inStr, true);
    }

    /**
     * 根据条件获得复杂密码
     *
     * @param num    密码的位数 6-32位
     * @param inNum  是否包含数字，默认为true
     * @param inStr  是否包含大写字母，默认为true
     * @param inSpec 是否包含特殊字符，默认为true
     *               默认包含小写字母，没有条件时，将全部使用小写字母
     * @return 生成的密码
     */
    public static String getPassword(int num, boolean inNum, boolean inStr, boolean inSpec) {
        String[] strs = new String[num];
        // 密码的第一位为字母
        if (inStr) {
            strs[0] = getRandomStr();
        } else {
            strs[0] = getRandomStr().toLowerCase();
        }
        // 生成从第二位开始的密码
        for (int i = 1; i < num; i++) {
            String funcIndex = getFormIndex();
            // funcIndex=num时候，调用生成数字的方法；=str时，生成大写字母；=spec时，生成特殊字符。
            if (funcIndex.equals("num") && inNum) {
                strs[i] = String.valueOf(getRandomNum());
            } else if (funcIndex.equals("str") && inStr) {
                strs[i] = getRandomStr();
            } else if (funcIndex.equals("spec") && inSpec) {
                strs[i] = getSpecStr();
            } else {
                // 当不满足生成的条件时，生成小写字母
                strs[i] = getRandomStr().toLowerCase();
            }
        }
        String str = "";
        for (String s : strs) {
            str += s;
        }
        return str;
    }

    private static int getRandomNum() {
        int num = (int) (Math.random() * 10);
        return num;
    }

    private static String getRandomStr() {
        String[] strs = {
                "Q", "W", "E", "R", "T",
                "Y", "U", "I", "O", "P",
                "A", "S", "D", "F", "G", "H", "J", "K",
                "L", "Z", "X", "C", "V", "B", "N", "M"};
        int strIndex = (int) (Math.random() * 26);
        int strUpper = ((int) (Math.random() * 100)) % 2;
        if (strUpper != 1) {
            return strs[strIndex].toLowerCase();
        }
        return strs[strIndex];
    }

    private static String getSpecStr() {
        String[] strs = {"!", "@", "_", "#", "$"};
        int strIndex = (int) (Math.random() * 5);
        return strs[strIndex];
    }

    private static String getFormIndex() {
        int formIndex = (int) (Math.random() * 16);
        if ((formIndex % 3) == 0) {
            return "num";
        } else if ((formIndex % 5) == 1) {
            return "spec";
        } else {
            return "str";
        }
    }

}
