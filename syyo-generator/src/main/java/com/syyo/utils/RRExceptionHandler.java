package com.syyo.utils;

import com.alibaba.fastjson.JSON;
import com.syyo.domain.ResultVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerExceptionResolver;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 异常处理器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2016年10月27日 下午10:16:19
 */
@Component
public class RRExceptionHandler implements HandlerExceptionResolver {
    private Logger logger = LoggerFactory.getLogger(getClass());

    @Override
    public ModelAndView resolveException(HttpServletRequest request,
                                         HttpServletResponse response, Object handler, Exception ex) {


        ResultVo<Object> resultVo = new ResultVo<>();


        try {
            response.setContentType("application/json;charset=utf-8");
            response.setCharacterEncoding("utf-8");

            if (ex instanceof RRException) {
                resultVo.setCode(((RRException) ex).getCode());
                resultVo.setMessage(((RRException) ex).getMsg());
            } else if (ex instanceof DuplicateKeyException) {
                resultVo.setCode(500);
                resultVo.setMessage("数据库中已存在该记录");
            } else {
                resultVo.setMessage("error");
            }
            //记录异常日志
            logger.error(ex.getMessage(), ex);
            String json = JSON.toJSONString(resultVo);
            response.getWriter().print(json);
        } catch (Exception e) {
            logger.error("RRExceptionHandler 异常处理失败", e);
        }
        return new ModelAndView();
    }
}
