package com.syyo.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Auther: wang
 * @Date: 2019/9/30 17:15
 * @Description: 状态枚举
 */
@AllArgsConstructor
@Getter
public enum ResultEnum {

    OK(20000, "成功"),
    FAIL(9999, "失败"),
    E_90000(90000, "参数不能为空"),
    E_90001(90001, "Token认证失败，原因：token无效或者过期！"),
    E_90003(90003, "您没有该功能的权限，请联系管理员！"),
    E_90004(90004, "文件导入失败，安全码过期！"),
    E_90005(90005, "验证码错误或者过期！"),
    E_90006(90006, "您没有该部门的权限，请联系管理员！"),


    E_10001(10001, "新增sql执行错误！"),
    E_10002(10002, "删除sql执行错误！"),
    E_10003(10003, "编辑sql执行错误！"),


    PLACEHOLDER(null, null);// 占位符,方便快速编写代码（无其他业务作用）

    private Integer code;
    private String message;


}
