package com.syyo.common.exception;

/**
 * @Auther: wang
 * @Date: 2019/6/28 16:47
 * @Description: 异常
 */
public class SysException extends RuntimeException {

    private Integer code;
    private String message;


    public SysException() {
    }

    public SysException(String message, Throwable cause, String message1) {
        super(message, cause);
        this.message = message1;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    @Override
    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
