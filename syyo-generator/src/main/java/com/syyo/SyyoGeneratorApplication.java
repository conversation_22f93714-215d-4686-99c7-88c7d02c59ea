package com.syyo;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;

/**
 * @author: wang
 * @date: 2021/1/18 14:25
 * @Description:
 */
@SpringBootApplication
@MapperScan("com.syyo.mapper")
public class SyyoGeneratorApplication {

    public static void main(String[] args) {

        ConfigurableApplicationContext run = SpringApplication.run(SyyoGeneratorApplication.class, args);
        String port = run.getEnvironment().getProperty("server.port");

        System.out.println("                        \n" +
                "    '----'        ||      //        \n" +
                "  //      \\\\      ||    //      \n" +
                " ||        ||     ||   //          \n" +
                " ||        ||     ||   \\\\           \n" +
                "  \\\\      //      ||    \\\\             \n" +
                "    '----'        ||      \\\\              ");

        System.out.println(" 代码生成器启动成功,浏览器输入：http://localhost:" + port);
    }

}
