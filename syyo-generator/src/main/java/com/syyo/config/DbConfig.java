/**
 * Copyright (c) 2018 人人开源 All rights reserved.
 * <p>
 * https://www.renren.io
 * <p>
 * 版权所有，侵权必究！
 */

package com.syyo.config;


import com.syyo.mapper.*;
import com.syyo.utils.RRException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * 数据库配置
 */
@Configuration
public class DbConfig {
    @Value("${syyo.database: mysql}")
    private String database;
    @Autowired
    private MySQLGeneratorMapper mySQLGeneratorMapper;
    @Autowired
    private OracleGeneratorMapper oracleGeneratorMapper;
    @Autowired
    private SQLServerGeneratorMapper sqlServerGeneratorMapper;
    @Autowired
    private PostgreSQLGeneratorMapper postgreSQLGeneratorMapper;

    @Bean
    @Primary
    public GeneratorMapper getGeneratorDao() {
        if ("mysql".equalsIgnoreCase(database)) {
            return mySQLGeneratorMapper;
        } else if ("oracle".equalsIgnoreCase(database)) {
            return oracleGeneratorMapper;
        } else if ("sqlserver".equalsIgnoreCase(database)) {
            return sqlServerGeneratorMapper;
        } else if ("postgresql".equalsIgnoreCase(database)) {
            return postgreSQLGeneratorMapper;
        } else {
            throw new RRException("不支持当前数据库：" + database);
        }
    }

}
