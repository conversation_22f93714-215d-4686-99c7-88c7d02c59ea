package com.syyo.domain;

import java.util.Objects;

/**
 * @Auther: wang
 * @Date: 2019/6/29 09:50
 * @Description: 统一返回实体类
 */

public class ResultVo<T> {

    private Integer code; //状态码
    private String message; //提示信息
    private T data; //响应数据

    public ResultVo() {
    }

    public ResultVo(Integer code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ResultVo<?> resultVo = (ResultVo<?>) o;
        return Objects.equals(code, resultVo.code) &&
                Objects.equals(message, resultVo.message) &&
                Objects.equals(data, resultVo.data);
    }

    @Override
    public int hashCode() {
        return Objects.hash(code, message, data);
    }
}