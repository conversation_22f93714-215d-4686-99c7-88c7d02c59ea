package com.tiangong.mgs.framework.operatelog.core.filter;

import com.alibaba.fastjson.JSONObject;
import com.tiangong.util.SM2Utils;
import com.tiangong.util.SM4Utils;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import javax.servlet.ReadListener;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import java.io.*;
import java.nio.charset.Charset;

/**
 * <AUTHOR>
 * @date 2023/2/17 17:03
 * <p>
 * 参数包装servlet，解决参数流读取的问题
 */
@Slf4j
public class RequestWrapper extends HttpServletRequestWrapper {

    private byte[] body = null;
    public String encryptParams;

    private Integer isJudgmentPermissions;

    public RequestWrapper(HttpServletRequest request, boolean isEncrypt) throws IOException {
        super(request);
        String sessionStream = getBodyString(request, isEncrypt);
        if (!StrUtilX.isEmpty(sessionStream)) {
            body = sessionStream.getBytes(Charset.forName("UTF-8"));
        }
    }

    public String getBodyString() {
        if (body != null) {
            return new String(body, Charset.forName("UTF-8"));
        }
        return "";
    }

    /**
     * 获取请求Body
     *
     * @param request
     * @param isEncrypt
     * @return
     */
    public String getBodyString(final HttpServletRequest request, boolean isEncrypt) {
        String bodyString = getBodyString(request);
        if (isEncrypt) {
            if (StrUtilX.isEmpty(bodyString)) {
                return bodyString;
            }
            String secretKey = SM2Utils.decryptData(request.getHeader("secretKey"));

            if (bodyString.contains("params")) {
                JSONObject paramsJsonObject = JSONObject.parseObject(bodyString);
                Object params = paramsJsonObject.get("params");
                isJudgmentPermissions = 0;
                if (StringUtils.isEmpty(params)) {
                    return bodyString;
                }
                encryptParams = params.toString();
                bodyString = SM4Utils.decrypt(params.toString(), secretKey);
                if (StrUtilX.isNotEmpty(bodyString)) {
                    isJudgmentPermissions = 1;
                }
            }

        }
        return bodyString;
    }

    /**
     * 获取请求Body
     *
     * @param request
     * @return
     */
    public String getBodyString(final HttpServletRequest request) {
        StringBuilder sb = new StringBuilder();
        InputStream inputStream = null;
        BufferedReader reader = null;
        try {
            inputStream = cloneInputStream(request.getInputStream());
            reader = new BufferedReader(new InputStreamReader(inputStream, Charset.forName("UTF-8")));
            String line = "";
            while ((line = reader.readLine()) != null) {
                sb.append(line);
            }
        } catch (IOException e) {
            log.error("获取请求Body异常", e);
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.error("获取请求Body异常", e);
                }
            }
            if (reader != null) {
                try {
                    reader.close();
                } catch (IOException e) {
                    log.error("获取请求Body异常", e);
                }
            }
        }
        return sb.toString();
    }

    /**
     * Description: 复制输入流</br>
     *
     * @param inputStream
     * @return</br>
     */
    public InputStream cloneInputStream(ServletInputStream inputStream) {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        int len;
        try {
            while ((len = inputStream.read(buffer)) > -1) {
                byteArrayOutputStream.write(buffer, 0, len);
            }
            byteArrayOutputStream.flush();
        } catch (IOException e) {
            log.error("复制输入流异常", e);
        }
        return new ByteArrayInputStream(byteArrayOutputStream.toByteArray());
    }

    @Override
    public BufferedReader getReader() throws IOException {
        return new BufferedReader(new InputStreamReader(getInputStream()));
    }

    @Override
    public ServletInputStream getInputStream() throws IOException {
        if (body == null) {//处理空指针
            body = new byte[]{};
        }
        final ByteArrayInputStream bais = new ByteArrayInputStream(body);
        return new ServletInputStream() {

            @Override
            public int read() throws IOException {
                return bais.read();
            }

            @Override
            public boolean isFinished() {
                return false;
            }

            @Override
            public boolean isReady() {
                return false;
            }

            @Override
            public void setReadListener(ReadListener readListener) {

            }
        };
    }

    public Integer getIsJudgmentPermissions() {
        return isJudgmentPermissions;
    }

}
