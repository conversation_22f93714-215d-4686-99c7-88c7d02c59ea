package com.tiangong.mgs.framework.operatelog.core.filter;

import com.alibaba.fastjson.JSONObject;
import com.tiangong.cloud.common.constant.HttpConstant;
import com.tiangong.cloud.common.domain.LoginUser;
import com.tiangong.cloud.common.enums.result.ResultEnum;
import com.tiangong.cloud.common.utils.SpringContextHolder;
import com.tiangong.keys.RedisKey;
import com.tiangong.mgs.framework.operatelog.utils.JWTUtil;
import com.tiangong.util.SM2Utils;
import com.tiangong.util.SM3Utils;
import com.tiangong.util.SM4Utils;
import com.tiangong.util.StrUtilX;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.ServletResponse;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023/2/17 16:59
 * <p>
 * 参数日志接口过滤器
 * 用于记录每个接口的请求参数
 * 目前只支持post，json格式
 */
@Component
@Slf4j
@AllArgsConstructor
@WebFilter(urlPatterns = "/*", filterName = "channelFilter")
public class ParamLogFilter extends OncePerRequestFilter {

    public static final ThreadLocal<String> body = new ThreadLocal<>();
    private static final String UNKNOWN = "unknown";
    private static final String url = "/common/operateLog/operateLogAdd";

    // 不做解密处理
    private static final Set<String> ALLOWED_PATHS = Collections.unmodifiableSet(new HashSet<>(
            Arrays.asList("/common/stateSecret/getSecretKey",
                    "/common/operateLog/operateLogAdd",
                    "/common/stateSecret/verifySecretKey",
                    "/common/auth/login",
                    "/common/auth/sendVerificationCode",
                    "/h5/getCode",
                    "/h5/area/getRegion",
                    "/h5/forgetPassword",
                    "/h5/login",
                    "/hdp/user/login")));
    // 响应参数加密处理
    private static final Set<String> PATHS = Collections.unmodifiableSet(new HashSet<>(
            Arrays.asList("/common/auth/login","/common/auth/sendVerificationCode")));

    private final String applicationName;



    @Override
    protected void doFilterInternal(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, FilterChain filterChain) throws ServletException, IOException {

        String servletPath = httpServletRequest.getServletPath();
        String contentType = httpServletRequest.getContentType();

        if (!StrUtilX.isEmpty(contentType) && contentType.startsWith("multipart/")) {
            filterChain.doFilter(httpServletRequest, httpServletResponse);
            return;
        }
        boolean isEncrypt = true;//正式
        //boolean isEncrypt = false;//测试
        for (String allowedPath : ALLOWED_PATHS) {
            if (servletPath.contains(allowedPath)) {
                isEncrypt = false;
                break;
            }
        }
        // 非json请求不做处理
        if (!StrUtilX.isEmpty(contentType) && !contentType.contains("application/json")) {
            isEncrypt = false;
        }
        // 非post请求不加密
        if (!httpServletRequest.getMethod().equalsIgnoreCase("post")) {
            isEncrypt = false;
        }

        //log.info("servletPath:{},contentType:{},isEncrypt:{},secretKey:{}",servletPath,contentType,isEncrypt,httpServletRequest.getHeader("secretKey"));
        RequestWrapper requestWrapper = new RequestWrapper(httpServletRequest, isEncrypt);
        if (isEncrypt) {
            //请求参数解密
            String params = requestWrapper.encryptParams;
            String signature = httpServletRequest.getHeader("signature");
            if (!SM3Utils.verifySignature(params, signature)) {
                httpServletResponse.setContentType("application/json;charset=utf-8");
                // 添加cors返回请求头,不然会有cors跨域问题
                String header = httpServletRequest.getHeader("Origin");
                httpServletResponse.setHeader("Access-Control-Allow-Origin", header);
                PrintWriter writer = httpServletResponse.getWriter();
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("code", ResultEnum.E_9012.getCode());
                jsonObject.put("msg", ResultEnum.E_9012.getMessage());
                String result = jsonObject.toString();
                writer.write(result);
                writer.close();
                return;
            }

            String secretKey = SM2Utils.decryptData(httpServletRequest.getHeader("secretKey"));
            if (!StringUtils.isEmpty(secretKey) && "false".equals(verifySecretKey(httpServletRequest, secretKey))) {
                JSONObject jsonObject = new JSONObject();
                httpServletResponse.setContentType("application/json;charset=utf-8");
                // 添加cors返回请求头,不然会有cors跨域问题
                String header = httpServletRequest.getHeader("Origin");
                httpServletResponse.setHeader("Access-Control-Allow-Origin", header);
                PrintWriter writer = httpServletResponse.getWriter();
                jsonObject.put("code", ResultEnum.E_9013.getCode());
                jsonObject.put("msg", ResultEnum.E_9013.getMessage());
                String result = jsonObject.toString();
                writer.write(result);
                writer.close();
                return;
            }
        }

        if (requestWrapper.getIsJudgmentPermissions() != null && requestWrapper.getIsJudgmentPermissions() == 1) {
            //api路径判断权限
            String token = resolveToken(httpServletRequest);
            String header = httpServletRequest.getHeader("Origin");
            StringRedisTemplate redisTemplate = SpringContextHolder.getBean(StringRedisTemplate.class);
            // 获取登录用户
            LoginUser loginUser = JWTUtil.getUserNotThrow(httpServletRequest);
            if (loginUser == null || !redisTemplate.hasKey(RedisKey.LOGIN_TOKEN + token)) {

                expiredToken(httpServletResponse, header);
                return;
            }

            Set<String> userAllowVisitApiUrl = redisTemplate.opsForSet().members(RedisKey.USER_ALLOW_VISIT_API_URL + loginUser.getUserAccount());
            if (userAllowVisitApiUrl == null) {
                unauthorizedLog(httpServletRequest, loginUser);
                noAccess(httpServletResponse, header);
                return;
            }
            boolean flage = false;
            if (userAllowVisitApiUrl.contains(httpServletRequest.getRequestURI())) {
                flage = true;
            }
            if (!flage) {
                for (String userAllowVisitApiUrlStr : userAllowVisitApiUrl) {
                    if (httpServletRequest.getRequestURI().endsWith(userAllowVisitApiUrlStr)) {
                        flage = true;
                        break;
                    }
                }
                if (!flage) {
                    unauthorizedLog(httpServletRequest, loginUser);
                    noAccess(httpServletResponse, header);
                    return;
                }
            }
        }

        // 将请求参数保存到当前线程里
        String requestURI = httpServletRequest.getRequestURI();
//        if (!url.equals(requestURI)){
        if (!ALLOWED_PATHS.contains(requestURI)) {
            String bodyString = requestWrapper.getBodyString();
            body.set(bodyString);
        }

//        if (!url.equals(requestURI)){
        if (PATHS.contains(requestURI)) {
            //isEncrypt = true;//正式
        }


        //请求响应参数加密
        if (isEncrypt) {
            //移除secretKey
            String secretKey = SM2Utils.decryptData(httpServletRequest.getHeader("secretKey"));
            if (StrUtilX.isEmpty(secretKey)) {
                try {
                    filterChain.doFilter(requestWrapper, httpServletResponse);
                } catch (Exception e) {
                    log.error("filterChain.doFilter error!", e);
                }

                return;
            }
            ResponseWrapper responseWrapper = new ResponseWrapper((httpServletResponse));
            filterChain.doFilter(requestWrapper, responseWrapper);

            removeSecretKey(httpServletRequest, secretKey);

            String resp = responseWrapper.getTextContent(); //获取接口返回内容
            if (!StrUtilX.isEmpty(resp) && (resp.contains("\"code\"") || resp.contains("\"failCode\""))) {
                resp = SM4Utils.encrypt(resp, secretKey);
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("data", resp);
                String result = jsonObject.toString();
                httpServletResponse.getOutputStream().write(result.getBytes(StandardCharsets.UTF_8));
                return;
            } else {
                filterChain.doFilter(requestWrapper, httpServletResponse);
            }
        } else {
            filterChain.doFilter(requestWrapper, httpServletResponse);
        }
    }

    public void unauthorizedLog(HttpServletRequest request, LoginUser loginUser) {
//        OperateLogReqDTO newDO = new OperateLogReqDTO();
//        newDO.setCreatedDt(LocalDateTime.now());
//        newDO.setLogName("越权访问");
//        newDO.setLogType("error");
//        newDO.setLogLevel(0);
//        newDO.setOperationType(18);
//        newDO.setOperationResult(0);
//        newDO.setApplicationName("tiangong-common-server");
//        newDO.setRequestMethod(request.getMethod());
//        newDO.setRequestIp(IpUtil.getIpAddress(request));
//        newDO.setRequestHeader(request.getHeader("User-Agent"));
//        newDO.setRequestUrl(request.getRequestURI());
//        newDO.setCreatedBy(loginUser.getFullUserName());
//        newDO.setUserAccount(loginUser.getUserAccount());
//        operateLogRemote.OperateLogAdd(newDO);
    }

    public static String getIp(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || UNKNOWN.equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || UNKNOWN.equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || UNKNOWN.equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        String comma = ",";
        String localhost = "127.0.0.1";
        if (ip.contains(comma)) {
            ip = ip.split(",")[0];
        }
        if (localhost.equals(ip)) {
            // 获取本机真正的ip地址
            try {
                ip = InetAddress.getLocalHost().getHostAddress();
            } catch (UnknownHostException e) {
                log.error("获取本机真正的ip地址异常", e);
            }
        }
        return ip;
    }

    /**
     * token过期或者无效，直接返回 90001
     *
     * @param response
     * @throws IOException
     */
    private void expiredToken(ServletResponse response, String header) throws IOException {
        JSONObject jsonObject = new JSONObject();
        HttpServletResponse resp = (HttpServletResponse) response;
        resp.setContentType("application/json;charset=utf-8");
        // 添加cors返回请求头,不然会有cors跨域问题
        resp.setHeader("Access-Control-Allow-Origin", header);
        PrintWriter writer = response.getWriter();
        jsonObject.put("result", 0);
        jsonObject.put("failCode", ResultEnum.E_9001.getCode());
        jsonObject.put("failReason", ResultEnum.E_9001.getMessage());
        String result = jsonObject.toString();
        writer.write(result);
        writer.close();
    }

    private void noAccess(ServletResponse response, String header) throws IOException {
        JSONObject jsonObject = new JSONObject();
        HttpServletResponse resp = (HttpServletResponse) response;
        resp.setContentType("application/json;charset=utf-8");
        // 添加cors返回请求头,不然会有cors跨域问题
        resp.setHeader("Access-Control-Allow-Origin", header);
        PrintWriter writer = response.getWriter();
        jsonObject.put("result", 0);
        jsonObject.put("failCode", ResultEnum.E_9011.getCode());
        jsonObject.put("failReason", ResultEnum.E_9011.getMessage());
        String result = jsonObject.toString();
        writer.write(result);
        writer.close();
    }

    private String resolveToken(HttpServletRequest request) {
//      SecurityProperties properties = SpringContextHolder.getBean(SecurityProperties.class);
        return request.getHeader(HttpConstant.HEADER);
    }

    public void removeSecretKey(HttpServletRequest httpServletRequest, String secretKey) {
        // 获取登录用户
        LoginUser loginUser = JWTUtil.getUserNotThrow(httpServletRequest);
        if (loginUser != null) {
            StringRedisTemplate redisTemplate = SpringContextHolder.getBean(StringRedisTemplate.class);
            redisTemplate.opsForHash().delete(StrUtilX.concat(RedisKey.USER_SECRET_KEY, loginUser.getUserId().toString()), secretKey);
        }
    }

    /**
     * 验证密钥
     */
    public String verifySecretKey(HttpServletRequest httpServletRequest, String secretKey) {
        LoginUser loginUser = JWTUtil.getUserNotThrow(httpServletRequest);
        if (loginUser == null) {
            return "true";
        }
        StringRedisTemplate redisTemplate = SpringContextHolder.getBean(StringRedisTemplate.class);
        Object object = redisTemplate.opsForHash().get(StrUtilX.concat(RedisKey.USER_SECRET_KEY, loginUser.getUserId().toString()), secretKey);
        if (object != null) {
            return "true";
        } else {
            return "false";
        }
    }

}

