package com.tiangong.mgs.framework.operatelog.auto;


import com.tiangong.mgs.framework.operatelog.core.filter.ParamLogFilter;
import com.tiangong.mgs.framework.operatelog.core.interceptor.RequestDestructionInterceptorConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

@Component
public class MgsOperateLogAutoConfiguration {

    @Value("${spring.application.name:server}")
    private String applicationName;

    @Bean
    public ParamLogFilter paramLogFilter() {
        return new ParamLogFilter(applicationName);
    }

    @Bean
    public RequestDestructionInterceptorConfig requestDestructionInterceptorConfig() {
        return new RequestDestructionInterceptorConfig(applicationName);
    }

}
