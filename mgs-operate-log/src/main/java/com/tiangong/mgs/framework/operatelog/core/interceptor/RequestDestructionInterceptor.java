package com.tiangong.mgs.framework.operatelog.core.interceptor;

import com.tiangong.cloud.common.utils.SpringContextHolder;
import com.tiangong.enums.RequestUrlEnums;
import com.tiangong.keys.RedisKey;
import com.tiangong.mgs.framework.operatelog.core.filter.ParamLogFilter;
import com.tiangong.operatelog.dto.OperateLogReqDTO;
import com.tiangong.util.StrUtilX;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * @Description:
 * @ClassName: RequestDestructionInterceptor
 * @Author: fonkes
 * @CreateDate: 2023/3/19 15:39
 * @Version: 1.0
 */
@Slf4j
@AllArgsConstructor
public class RequestDestructionInterceptor implements HandlerInterceptor {

    private static final String UNKNOWN = "unknown";
    private static final String url = "/common/operateLog/operateLogAdd";

    public static ThreadLocal<Integer> threadLocalObj = new ThreadLocal<>();

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
//        log.info("after==========");
        int operationResult;
        if (threadLocalObj.get() != null && threadLocalObj.get() == 1) {
            operationResult = 1;
        } else {
            operationResult = 0;
        }
        threadLocalObj.remove();

        int logSettingStatus = 0;
        StringRedisTemplate stringRedisTemplate = SpringContextHolder.getBean(StringRedisTemplate.class);
        String logSetting = stringRedisTemplate.opsForValue().get(RedisKey.LOG_SETTING);
        if (StrUtilX.isNotEmpty(logSetting)) {
            logSettingStatus = Integer.parseInt(logSetting);
        }

        if (logSettingStatus == 1) {
            // 不是写日志的请求，写入日志
            String requestURI = request.getRequestURI();
            if (!url.equals(requestURI)) {
                doAddlog(request, ex, operationResult);
            }
        }
    }

    private void doAddlog(HttpServletRequest request, Exception ex, Integer operationResult) {
        OperateLogReqDTO newDO = new OperateLogReqDTO();
        Boolean isMatch = false;

        RequestUrlEnums requestUrlEnumByRequestUrl = RequestUrlEnums.getRequestUrlEnumByRequestUrl(request.getRequestURI());
        if (requestUrlEnumByRequestUrl != null) {
            newDO.setLogName(requestUrlEnumByRequestUrl.getLogName());
            newDO.setLogLevel(requestUrlEnumByRequestUrl.getLogLevel());
            newDO.setOperationType(requestUrlEnumByRequestUrl.getOperationType());
            if (operationResult == 1) {
                newDO.setOperationResult(1);
            } else {
                newDO.setOperationResult(0);
            }
            isMatch = true;
        } else {//枚举类匹配不到,归类为越权操作
            isMatch = false;
        }

        String body = ParamLogFilter.body.get();

        if (ex != null) {
            logType = "error";
            error = ex.getMessage();
            newDO.setOperationResult(0);
        }
        if (isMatch) {
            String ip = getIp(request);
            String ua = request.getHeader("User-Agent");
        }
    }

    /**
     * 获取ip地址
     */
    public static String getIp(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || UNKNOWN.equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || UNKNOWN.equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || UNKNOWN.equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        String comma = ",";
        String localhost = "127.0.0.1";
        if (ip.contains(comma)) {
            ip = ip.split(",")[0];
        }
        if (localhost.equals(ip)) {
            // 获取本机真正的ip地址
            try {
                ip = InetAddress.getLocalHost().getHostAddress();
            } catch (UnknownHostException e) {
                log.error("获取本机真正的ip地址异常", e);
            }
        }
        return ip;
    }


}
