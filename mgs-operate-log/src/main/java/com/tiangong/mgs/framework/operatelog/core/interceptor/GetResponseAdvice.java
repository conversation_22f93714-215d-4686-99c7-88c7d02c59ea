package com.tiangong.mgs.framework.operatelog.core.interceptor;


import com.tiangong.common.Response;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;


/**
 * <AUTHOR>
 * @Date 2023/4/14 17:03
 * @Description:
 */
@ControllerAdvice
public class GetResponseAdvice implements ResponseBodyAdvice<Object> {
    @Override
    public boolean supports(MethodParameter methodParameter, Class<? extends HttpMessageConverter<?>> aClass) {
        //System.out.println("supports" + methodParameter.getMethod().getDeclaringClass().getName());
        if ("com.tiangong.operatelog.controller.OperateLogController".equals(methodParameter.getMethod().getDeclaringClass().getName())) {//日志直接返回，不需要拦截
            return false;
        }
        return true;
    }

    /**
     * @Description: 该方法是拦截到返回值（即response中的数据），然后操作返回值，并返回
     **/
    @Override
    public Object beforeBodyWrite(Object body, MethodParameter methodParameter, MediaType mediaType, Class<? extends HttpMessageConverter<?>> aClass, ServerHttpRequest serverHttpRequest, ServerHttpResponse serverHttpResponse) {
        //System.out.println("进入beforeBodyWrite方法");
        //ServletServerHttpResponse responseTemp = (ServletServerHttpResponse) serverHttpResponse;
        //HttpServletResponse response = responseTemp.getServletResponse();
        if (body instanceof Response) {
            Response result = (Response) body;
            //System.out.println("beforeBodyWrite Response:" + JSON.toJSONString(result));
            RequestDestructionInterceptor.threadLocalObj.set(result.getResult());
        }
//        if (body instanceof ResultVo) {
//            ResultVo result = (ResultVo) body;
//            //System.out.println("beforeBodyWrite ResultVo:" + JSON.toJSONString(result));
//            RequestDestructionInterceptor.threadLocalObj.set(result.getResult());
//        }
        return body;
    }
}
