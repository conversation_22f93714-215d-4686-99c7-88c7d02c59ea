package com.tiangong.mgs.framework.operatelog.utils;

import com.alibaba.fastjson.JSON;
import com.tiangong.cloud.common.constant.HttpConstant;
import com.tiangong.cloud.common.domain.LoginUser;
import com.tiangong.util.StrUtilX;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.io.Decoders;
import io.jsonwebtoken.security.Keys;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.security.Key;

/**
 * <AUTHOR>
 * @Date 2023/4/8 16:10
 * @Description:
 */
@Slf4j
@Component
public class JWTUtil implements InitializingBean {

    private final JWTSecurityProperties jwtSecurityProperties;//security配置类

    private static Key key;

    public JWTUtil(JWTSecurityProperties jwtSecurityProperties) {
        this.jwtSecurityProperties = jwtSecurityProperties;
    }

    @Override
    public void afterPropertiesSet() {
        //启动项目时，自动生成key
        byte[] keyBytes = Decoders.BASE64.decode(jwtSecurityProperties.getBase64Secret());
        key = Keys.hmacShaKeyFor(keyBytes);
    }

    /**
     * 解析token获取登录用户对象
     */
    public static LoginUser getUserNotThrow(HttpServletRequest request) {
        String token = request.getHeader(HttpConstant.HEADER);
        if (StrUtilX.isEmpty(token)) {
            return null;
        }
        LoginUser loginUser = null;
        try {
            Claims claims = Jwts.parser()
                    .setSigningKey(key)
                    .parseClaimsJws(token)
                    .getBody();
            claims.getSubject();
            String subject = claims.getSubject();
            loginUser = JSON.parseObject(subject, LoginUser.class);
        } catch (Exception e) {
            log.error("解析token获取登录用户对象异常", e);
        }
        return loginUser;
    }

}
