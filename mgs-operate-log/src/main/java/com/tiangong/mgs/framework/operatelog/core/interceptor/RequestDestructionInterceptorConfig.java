package com.tiangong.mgs.framework.operatelog.core.interceptor;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * @Description:
 * @ClassName: RequestDestructionInterceptorConfig
 * @Author: fonkes
 * @CreateDate: 2023/3/19 17:24
 * @Version: 1.0
 */
@AllArgsConstructor
@Configuration
@Slf4j
public class RequestDestructionInterceptorConfig implements WebMvcConfigurer {

    private final String applicationName;

    @Bean
    public RequestDestructionInterceptor requestDestructionInterceptor() {
        return new RequestDestructionInterceptor(applicationName);
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(requestDestructionInterceptor())
                .addPathPatterns("/**")
                .excludePathPatterns("/common/auth/**", "/common/operateLog/**");
    }
}
