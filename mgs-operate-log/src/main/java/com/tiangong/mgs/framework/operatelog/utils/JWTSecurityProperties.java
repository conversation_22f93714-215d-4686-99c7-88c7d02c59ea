package com.tiangong.mgs.framework.operatelog.utils;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2019年11月28日
 * <p>
 * 全局security参数配置
 */
@Data
@Configuration
public class JWTSecurityProperties {

    /*    ========= 权限配置 ===========  */
    @Value("${tiangong.security.switch}")
    private Boolean securitySwitch; //securitySwitch 开关

    @Value("${tiangong.security.secret}")
    private String base64Secret; //base码

    @Value("${tiangong.security.tokentime}")
    private Integer tokenTime; //token的有效期,


}
