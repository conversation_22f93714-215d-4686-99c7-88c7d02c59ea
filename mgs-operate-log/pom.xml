<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>tiangong-parent</artifactId>
        <groupId>com.tiangong</groupId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>mgs-operate-log</artifactId>


    <dependencies>
        <!-- Web 相关 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <scope>provided</scope>
        </dependency>

        <!-- 公共组件 -->
        <dependency>
            <groupId>com.tiangong</groupId>
            <artifactId>tiangong-common</artifactId>
            <version>1.0.0</version>
        </dependency>

        <!-- 业务组件 -->
        <dependency>
            <groupId>com.tiangong</groupId>
            <artifactId>tiangong-common-client</artifactId>
            <version>1.0.0</version>
        </dependency>

        <!--jwt-->
        <dependency>
            <groupId>com.10duke.client.jwt</groupId>
            <artifactId>jjwt</artifactId>
            <version>1.1.0</version>
        </dependency>

    </dependencies>
</project>