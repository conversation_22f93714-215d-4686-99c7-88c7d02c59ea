package com.tiangong.settle.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

import java.io.Serializable;

/**
 * 结算单表
 * 返回参数
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-08 11:54:58
 */
@Data
public class SettleOrderResp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增长主键id
     */
    private Long id;
    /**
     * 商家编码 天宫商家编码
     */
    private String merchantCode;
    /**
     * 供应商编码 天宫供应商编码
     */
    private String supplierCode;

    /**
     * 入住日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date checkInDate;

    /**
     * 离店日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date checkOutDate;

    /**
     * 订单编码 天宫订单编码
     */
    private String orderCode;
    /**
     * 订单创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date orderDt;
    /**
     * 供货单编码 天宫供货单编码
     */
    private String supplyOrderCode;
    /**
     * 天宫供货单创建时间 天宫供货单创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date tgSupplyOrderDt;
    /**
     * 订单通商家编码
     */
    private String ddtMerchantCode;
    /**
     * 订单通客户编码 订单通客户编码
     */
    private String ddtCustomerCode;
    /**
     * 订单通订单编码 订单通订单编码
     */
    private String ddtOrderCode;
    /**
     * 订单通订单创建时间 订单通订单创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date ddtOrderDt;
    /**
     * 结算时间 结算时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date settleDt;
    /**
     * 结算时间控制类型 [1:下单后 2:入住当日之后 3:离店当日之后 4:离店周结 5:离店半月结 6:离店月结]
     */
    private Integer settleType;
    /**
     * 付款主体id 银行账号id值
     */
    private Long payerAccountId;
    /**
     * 公付邮件发送状态 [0:无需发送 1:需要发送 2:发送成功 3:发送失败 4:首次失败 5:二次发送失败]
     */
    private Integer sendStatus;
    /**
     * 公付邮件发送时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date sendDt;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdDt;
    /**
     * 更新人
     */
    private String updatedBy;
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedDt;


    /**
     * 客户单号
     */
    private String channelOrderCode;

    /**
     * 供应商名称
     */
    private String supplierName;


    /**
     * 酒店名称
     */
    private String hotelName;

    /**
     * 房型名称
     */
    private String roomName;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 间夜数
     */
    private Integer roomQty;

    /**
     * 供货单金额
     */
    private BigDecimal supplyOrderAmt;

    /**
     * 供货单已付
     */
    private BigDecimal paidAmt;

    /**
     * 供货单未付金额
     */
    private BigDecimal unpaidAmt;


}