package com.tiangong.settle.remote;

import com.tiangong.common.Response;
import com.tiangong.settle.req.SettleOrderReq;
import com.tiangong.settle.resp.SettleOrderResp;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


/**
 * 自助结算内部fegin接口调用入口
 * <AUTHOR>
 */
@FeignClient(value = "tiangong-settle-server")
public interface SettleOrderRemote {

    /**
     * 结算单表详情
     */
    @PostMapping("/settle/settleOrder/settleOrderDetail")
    Response<SettleOrderResp> findOne(@RequestBody SettleOrderReq req);
}
