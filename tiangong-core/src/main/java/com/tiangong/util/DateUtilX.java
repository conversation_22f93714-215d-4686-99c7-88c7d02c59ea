package com.tiangong.util;

import com.tiangong.exception.ParameterException;
import lombok.extern.slf4j.Slf4j;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class DateUtilX {

    /**
     * 默认格式 yyyy-MM-dd
     */
    public static String defaultFormat = "yyyy-MM-dd";


    /**
     * 默认格式 yyyy-MM-dd hh:mm:dd
     */
    public static String hour_format = "yyyy-MM-dd HH:mm:ss";

    /**
     * 默认格式 yyyy-MM-dd hh:mm:dd
     */
    public static String hour_ms_format = "yyyy-MM-dd HH:mm:ss:SS";

    /**
     * 小程序支持苹果手机日期小时格式
     */
    public static String appleFormat = "yyyy/MM/dd HH:mm:ss";

    /**
     * 小程序支持苹果手机日期格式
     */
    public static String appleDefaultFormat = "yyyy/MM/dd";

    /**
     * 2021-08-24T17:13:20+08:00
     */
    public static String timeZoneFormat = "yyyy-MM-dd'T'HH:mm:ss";

    /**
     * 时间格式
     */
    public static String only_hour_format = "HH:mm:ss";

    /**
     * 20200319163541714
     */
    public static String LONG_STR_FORMAT = "yyyyMMddHHmmssXXX";

    /**
     * yyyy年MM月dd日
     */
    public static String SPECIFIC_DATE = "yyyy年MM月dd日";

    /**
     * 将字符串转换为日期 (使用默认格式yyyy-MM-dd)
     *
     * @param date
     * @return
     * @throws ParseException
     */
    public static Date stringToDate(String date) {
        return stringToDate(date, defaultFormat);

    }

    /**
     * 自定义格式将字符串转换为日期
     *
     * @param date   字符串的日期
     * @param format 格式(形如:yyyy-MM-dd)
     * @return
     * @throws ParseException
     */
    public static Date stringToDate(String date, String format) {
        try {
            if (StrUtilX.isEmpty(date))
                return null;
            if (StrUtilX.isEmpty(format))
                format = defaultFormat;

            SimpleDateFormat sdf = new SimpleDateFormat(format, Locale.CHINA);
            return sdf.parse(date);
        } catch (ParseException e) {
            throw new ParameterException(e);
        }
    }


    public static Date getYear(int year) {
        Calendar cal = Calendar.getInstance();
        //设置年份
        cal.set(Calendar.YEAR, year);
        //设置月份
        cal.set(Calendar.MONTH, 0);
        //获取某月最小天数
        int firstDay = cal.getMinimum(Calendar.DATE);
        //设置日历中月份的最小天数
        cal.set(Calendar.DAY_OF_MONTH, firstDay);
        return cal.getTime();
    }

    /**
     * 将日期转换为字符串 (使用默认格式yyyy-MM-dd)
     *
     * @param date
     * @return
     */
    public static String dateToString(Date date) {
        return dateToString(date, defaultFormat);
    }


    /**
     * 时间转换
     * 将日期转换为字符串 (使用格式yyyy-MM-dd HH:mm:ss)
     * @param date 日期
     * @return 返回日期字符串
     */
    public static String dateTimeToString(Date date) {
        return dateToString(date, hour_format);
    }

    /**
     * 自定义格式将日期转换为字符串
     *
     * @param date   日期
     * @param format 格式
     * @return
     */
    public static String dateToString(Date date, String format) {
        if (null == date)
            return null;
        if (StrUtilX.isEmpty(format))
            format = defaultFormat;

        SimpleDateFormat sdf = new SimpleDateFormat(format, Locale.CHINA);
        return sdf.format(date);
    }

    /**
     * 将时间戳日期转为 yyyy-MM-dd
     * @param longDate
     * @return
     */
    public static String longDateToString(Long longDate) {
        if (Objects.isNull(longDate)) {
            return null;
        }
        Date date = new Date(longDate);
        SimpleDateFormat sdf = new SimpleDateFormat(defaultFormat);
        return sdf.format(date);
    }

    /**
     * 获取当前时间
     *
     * @return
     */
    public static String dateToZoneString(Date date, String format) {
        if (null == date)
            return null;
        if (StrUtilX.isEmpty(format))
            format = timeZoneFormat;
        SimpleDateFormat dateFormat = new SimpleDateFormat(format);
        return dateFormat.format(date);
    }

    /**
     * 格式化日期
     *
     * @param date
     * @param format
     * @return
     */
    public static Date dateFormat(Date date, String format) {

        if (null == date)
            return null;

        if (StrUtilX.isEmpty(format))
            format = defaultFormat;

        String formatDate = dateToString(date, format);

        return stringToDate(formatDate, format);
    }

    /**
     * 对传入的时间进行加减天数/加减小时数
     *
     * @param date 原始时间
     * @param day  需要增加减少的天数
     * @param hour 需要增加减少的小时数
     * @return
     */
    public static Date getDate(Date date, int day, int hour) {
        if (null != date) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            calendar.add(Calendar.DATE, day);
            calendar.add(Calendar.HOUR, hour);
            date = calendar.getTime();
        }
        return date;
    }

    /**
     * 原始时间减少天  小时   分钟数
     *
     * @param date
     * @param day
     * @param hour
     * @return Date
     * @Title getDate
     * <AUTHOR> 2015-11-16 下午2:16:51
     */
    public static Date getDate(Date date, int day, int hour, int minute) {
        if (null != date) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            calendar.add(Calendar.DATE, day);
            calendar.add(Calendar.HOUR, hour);
            calendar.add(Calendar.MINUTE, minute);
            date = calendar.getTime();
        }
        return date;
    }

    public static Date getDate(Date date, int month) {
        if (null != date) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            calendar.add(Calendar.MONTH, month);
            date = calendar.getTime();
        }
        return date;
    }

    public static Date getAddDay(Date date, int day) {
        if (null != date) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            calendar.add(Calendar.DATE, day);
            date = calendar.getTime();
        }
        return date;
    }
    /**
     * 测试日期是否在某一段日期之间
     *
     * @param date
     * @param start
     * @param end
     * @return
     */
    public static boolean between(Date date, Date start, Date end) {
        return 0 <= getDay(start, date) && 0 >= getDay(end, date);
    }

    /**
     * 获取当前时间
     *
     * @return
     */
    public static Date getCurrentDate() {
        return new Date();
    }

    /**
     * 获取当前日期字符串
     * 2022-03-03
     *
     * @return
     */
    public static String getDateStr() {
        return DateUtilX.dateToString(new Date());
    }

    /**
     * 增加年数
     */
    public static String getDateToDayStr(String dateStr, int day) {
        Date date = DateUtilX.stringToDate(dateStr);
        if (null != date) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            calendar.add(Calendar.DATE, day);
            date = calendar.getTime();
        }
        return DateUtilX.dateToString(date);
    }

    /**
     * 获取指定时间时间 + 天数的日期字符串
     */
    public static String getDateTimeToDayStr(String dateStr, int day) {
        Date date = DateUtilX.stringToDate(dateStr);
        if (null != date) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            calendar.add(Calendar.DATE, day);
            date = calendar.getTime();
        }
        return DateUtilX.dateTimeToString(date);
    }

    /**
     * 获取当前时间字符串
     * <p>
     * 2022-03-03 12:00:00
     *
     * @return
     */
    public static String getDateTimeStr() {
        return DateUtilX.dateToString(new Date(), hour_format);
    }

    /**
     * 获取当前时间
     * 1精确到小时分钟
     * 2精确到日
     *
     * @return
     */
    public static String getCurrentDateStr(int formatType) {
        String format = null;
        if (formatType == 1) {
            format = hour_format;
        } else if (formatType == 2) {
            format = defaultFormat;
        } else if (formatType == 3) {
            format = only_hour_format;
        }

        return DateUtilX.dateToString(getCurrentDate(), format);
    }

    /**
     * 获取当前时间
     * 1精确到小时分钟
     * 2精确到日
     *
     * @return
     */
    public static String getCurrentDateStrWithTimeZone(int formatType) {
        String format = null;
        if (formatType == 1) {
            format = hour_format;
        } else if (formatType == 2) {
            format = defaultFormat;
        } else if (formatType == 3) {
            format = only_hour_format;
        }

        return DateUtilX.dateToString(getCurrentDate(), format);
    }

    /**
     * 得到两个时间点相差的天数(date2-date1)
     *
     * @param date1
     * @param date2
     * @return
     */
    public static long getDay(Date date1, Date date2) {
        if (null == date1 || null == date2)
            return 0;
        return Long.valueOf((date2.getTime() - date1.getTime()) / (1000 * 60 * 60 * 24)).longValue();
    }


    /**
     * 得到两个时间点相差的小时数(date2-date1)
     *
     * @param date1
     * @param date2
     * @return
     */
    public static long getHour(Date date1, Date date2) {
        if (null == date1 || null == date2)
            return 0;
        return Long.valueOf((date2.getTime() - date1.getTime()) / (1000 * 60 * 60)).longValue();
    }

    /**
     * 得到两个时间点相差的分钟数(date2-date1)
     *
     * @param date1
     * @param date2
     * @return
     */
    public static long getMinute(Date date1, Date date2) {
        if (null == date1 || null == date2)
            return 0;
        return Long.valueOf((date2.getTime() - date1.getTime()) / (1000 * 60)).longValue();
    }

    /**
     * 获取两个时间段的秒数
     *
     * @param beginDate
     * @param endDate
     * @return
     */
    public static long getSecond(Date beginDate, Date endDate) {
        long time = endDate.getTime() - beginDate.getTime();
        long second = (time / 1000);
        return second;
    }

    /**
     * 比较两个时间点大小(精确到日期) </br> date1 > date2 返回1 </br> date1 = date2 返回0 </br> date1 <
     * date2 返回-1
     *
     * @param date1
     * @param date2
     * @return
     */
    public static int compare(Date date1, Date date2) {
        int resultCode = 0;
        long days = getDay(date1, date2);
        if (days < 0) {
            resultCode = 1;
        } else if (days > 0) {
            resultCode = -1;
        }
        return resultCode;
    }

    /**
     * 比较两个时间点大小(精确到毫秒) </br> date1 > date2 返回1 </br> date1 = date2 返回0 </br> date1 <
     * date2 返回-1
     *
     * @param date1
     * @param date2
     * @return
     */
    public static int compareInMS(Date date1, Date date2) {
        int resultCode = 0;
        if (null == date1 || null == date2) {
            return resultCode;
        }
        long days = Long.valueOf((date2.getTime() - date1.getTime())).longValue();
        if (days < 0) {
            resultCode = 1;
        } else if (days > 0) {
            resultCode = -1;
        }
        return resultCode;
    }

    /**
     * 日期的星期
     *
     * @param date Date
     * @return int 1-7
     */
    public static int getWeekOfDate(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return 0 == (calendar.get(Calendar.DAY_OF_WEEK) - 1) ? 7 : calendar.get(Calendar.DAY_OF_WEEK) - 1;
    }

    /**
     * 返回日期星期
     *
     * @param date
     * @return
     */
    public static String getWeekStrOfDate(Date date) {
        int week = getWeekOfDate(date);
        String weekStr = null;
        switch (week) {
            case 1:
                weekStr = "周一";
                break;
            case 2:
                weekStr = "周二";
                break;
            case 3:
                weekStr = "周三";
                break;
            case 4:
                weekStr = "周四";
                break;
            case 5:
                weekStr = "周五";
                break;
            case 6:
                weekStr = "周六";
                break;
            case 7:
                weekStr = "周日";
                break;
        }
        return weekStr;
    }


    public static String getWeekEnStrOfDate(Date date) {
        int week = getWeekOfDate(date);
        String weekStr = null;
        switch (week) {
            case 1:
                weekStr = "Mon";
                break;
            case 2:
                weekStr = "Tue";
                break;
            case 3:
                weekStr = "Wed";
                break;
            case 4:
                weekStr = "Thu";
                break;
            case 5:
                weekStr = "Fri";
                break;
            case 6:
                weekStr = "Sat";
                break;
            case 7:
                weekStr = "Sun";
                break;
        }
        return weekStr;
    }

    /**
     * 返回两个时间段的List
     *
     * @param date1
     * @param date2
     * @return
     */
    public static List<Date> getDateList(Date date1, Date date2) {
        List<Date> dateList = null;
        if (null == date1 || null == date2)
            dateList = Collections.emptyList();
        int days = ((Long) getDay(date1, date2)).intValue();
        if (days >= 0) {
            dateList = new ArrayList<Date>(days);
            for (int i = 0; i <= days; i++) {
                dateList.add(getDate(date1, i, 0));
            }
        } else {
            dateList = Collections.emptyList();
        }

        return dateList;
    }

    /**
     * 返回两个时间段的List(闭区间)
     *
     * @param date1
     * @param date2
     * @return
     */
    public static Set<String> getDateListToString(Date date1, Date date2) {
        List<Date> dateList = getDateList(date1, date2);
        return dateList.stream().map(i -> DateUtilX.dateToString(i)).collect(Collectors.toSet());
    }

    /**
     * 返回两个时间段的List(闭区间)
     *
     * @param date1
     * @param date2
     * @return
     */
    public static Set<String> getDateListToStringOpenClose(Date date1, Date date2) {
        List<Date> dateList = getDateList(date1, date2);
        dateList = dateList.stream().filter(i -> !i.equals(date2)).collect(Collectors.toList());
        return dateList.stream().map(i -> DateUtilX.dateToString(i)).collect(Collectors.toSet());
    }


    /**
     * 返回两个时间段的List
     * 日期满足传入星期
     * 传入的星期0-6表示周日-周六
     *
     * @param date1
     * @param date2
     * @return
     */
    public static List<Date> getDateInWeekList(Date date1, Date date2, String weekStr) {
        List<Date> dateList = null;
        if (null == date1 || null == date2)
            dateList = Collections.emptyList();
        int days = ((Long) getDay(date1, date2)).intValue();
        if (days >= 0) {
            dateList = new ArrayList<Date>();
            for (int i = 0; i <= days; i++) {
                if (StrUtilX.isNotEmpty(weekStr)) {
                    //传入的星期0-6表示周日-周六，满足星期的日期星期要减去1
                    if (weekStr.contains(String.valueOf(getWeekOfDate(getDate(date1, i, 0)) == 7 ? 0 : getWeekOfDate(getDate(date1, i, 0))))) {
                        dateList.add(getDate(date1, i, 0));
                    }
                } else {
                    dateList.add(getDate(date1, i, 0));
                }
            }
        } else {
            dateList = Collections.emptyList();
        }

        return dateList;
    }

    /**
     * 返回两个时间段的Map
     * 升序返回
     *
     * @param date1
     * @param date2
     * @return
     */
    public static Map<Date, Date> getDateMap(Date date1, Date date2) {
        Map<Date, Date> map = new TreeMap<Date, Date>(
                new Comparator<Date>() {
                    public int compare(Date obj1, Date obj2) {
                        // 升序排序
                        return obj1.compareTo(obj2);
                    }
                });
        if (null == date1 || null == date2)
            map = Collections.emptyMap();
        int days = ((Long) getDay(date1, date2)).intValue();
        if (days >= 0) {
            for (int i = 0; i <= days; i++) {
                map.put(dateFormat(getDate(date1, i, 0), "yyyy-MM-dd"), dateFormat(getDate(date1, i, 0), "yyyy-MM-dd"));
            }
        } else {
            map = Collections.emptyMap();
        }

        return map;
    }

    /**
     * 获取两个时间段的分钟数
     *
     * @param beginDate
     * @param endDate
     * @return
     */
    public static long getMinsBetweenDate(Date beginDate, Date endDate) {
        long time = endDate.getTime() - beginDate.getTime();
        long min = (time / (60 * 1000));
        return min;
    }

    /**
     * 根据某个日期得到前一天日期
     *
     * @param d
     * @return
     */
    public static Date getBeforeDate(Date d) {
        Date date = null;
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(d);
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        date = calendar.getTime();
        return date;
    }

    /**
     * 得到某个日期的后一天日期
     *
     * @param d
     * @return
     */
    public static Date getAfterDate(Date d) {
        Date date = null;
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(d);
        calendar.add(Calendar.DAY_OF_MONTH, 1);
        date = calendar.getTime();
        return date;
    }

    /**
     * 日期是否符合某一星期
     *
     * @param date
     * @param week
     * @return
     */
    public static boolean isMatchWeek(Date date, int week) {
        return getWeekOfDate(date) == week;
    }

    /**
     * 获取日期的时间点
     * 转换前日期格式为：yyyy-MM-dd HH:mm
     *
     * @param date
     * @return
     */
    public static String getDateTime(String date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(stringToDate(date, "yyyy-MM-dd HH:mm"));
        int hour = calendar.get(Calendar.HOUR_OF_DAY);
        int minute = calendar.get(Calendar.MINUTE);
        return (hour < 10 ? ("0" + hour) : hour) + ":" + (minute < 10 ? ("0" + minute) : minute);
    }


    /**
     * 获取2个时间的日期差，小时差，分钟差
     * 结果：1天2时10分
     *
     * @param date1
     * @param date2
     * @return
     */
    public static String getDayHourMinuteFormat(Date date1, Date date2) {

        long allMinutes = DateUtilX.getMinute(date1, date2);
        long days = allMinutes / 60 / 24;
        long hours = allMinutes / 60 - days * 24;
        long minutes = allMinutes - hours * 60 - days * 60 * 24;
        String dateStr = String.valueOf(days) + "天" + String.valueOf(hours) + "时" + String.valueOf(minutes) + "分";
        return dateStr;
    }

    /**
     * 得到某个日期的后一天日期
     *
     * @param d
     * @return
     */
    public static Date addDate(Date d, int days) {
        Date date = null;
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(d);
        calendar.add(Calendar.DAY_OF_MONTH, days);
        date = calendar.getTime();
        return date;
    }

    /**
     * 得到某个日期的后一天日期
     *
     * @param d
     * @return
     */
    public static Date addDateToDay(Date d, int days) {
        Date date = null;
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(d);
        calendar.add(Calendar.DATE, days);
        date = calendar.getTime();
        return date;
    }

    /**
     * 获取下一天的日期
     *
     * @param DateStr
     * @return
     */
    public static String getNextDateStr(String DateStr) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Date checkInDate = sdf.parse(DateStr);
            Date checkOutDate = DateUtilX.addDate(checkInDate, 1);
            return sdf.format(checkOutDate);
        } catch (Exception e) {
            log.error("获取下一天的日期异常", e);
        }
        return DateStr;
    }


    /**
     * 得到某个日期的某几个小时后的日期
     *
     * @param d
     * @return
     */
    public static Date addHour(Date d, int hour) {
        Date date = null;
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(d);
        calendar.add(Calendar.HOUR, hour);
        date = calendar.getTime();
        return date;
    }


    /**
     * 获取当前月的第一天
     * 日期格式：yyyy-MM-dd
     *
     * @return
     */
    public static String getFirstDayOfThisMonth() {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        //获取前月的第一天
        Calendar cal_1 = Calendar.getInstance();//获取当前日期
        cal_1.add(Calendar.MONTH, -1);
        cal_1.set(Calendar.DAY_OF_MONTH, 1);//设置为1号,当前日期既为本月第一天
        return format.format(cal_1.getTime());
    }

    /**
     * 取当前月的最后一天
     * 日期格式：yyyy-MM-dd
     *
     * @return
     */
    public static String getLastDayOfThisMonth() {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        //获取前月的最后一天
        Calendar cale = Calendar.getInstance();
        cale.set(Calendar.DAY_OF_MONTH, 0);//设置为1号,当前日期既为本月第一天
        return format.format(cale.getTime());

    }

    /**
     * 取某个时间点，判断是否在这个时间段之间HH:mm 时:分
     * startTime<endTime，指的是当天几点到当天几点
     * startTime>endTime，指的是当天几点到次日几点
     *
     * @param startTime
     * @param endTime
     * @return
     */
    public static boolean betweenTime(String time, String startTime, String endTime) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("HH:mm");
        try {
            Date date = simpleDateFormat.parse(time);
            Date start = simpleDateFormat.parse(startTime);
            Date end = simpleDateFormat.parse(endTime);
            if (start.compareTo(end) < 0 && date.compareTo(start) >= 0 && date.compareTo(end) <= 0) {
                return true;
            } else if (start.compareTo(end) > 0) {
                Date endDayTime = DateUtilX.getDate(end, 1, 0);
                Date date1 = DateUtilX.getDate(date, 1, 0);
                if (date.compareTo(start) >= 0) {
                    return true;
                } else if (date1.compareTo(endDayTime) <= 0) {
                    return true;
                }
            } else if (start.compareTo(date) == 0 && end.compareTo(date) == 0 && start.compareTo(end) == 0) {
                return true;
            }
        } catch (ParseException e) {
            log.error("取某个时间点，判断是否在这个时间段之间异常", e);
        }
        return false;
    }

    /**
     * 拆分日期
     *
     * @param dateType 类型 M/D/H/N -->每月/每天/每小时/每分钟
     * @param dBegin   开始时间
     * @param dEnd     结束时间
     * @param time     指定小时(如：1、2、3、4)
     * @return
     */
    public static List<String> findDates(String dateType, Date dBegin, Date dEnd, int time) {
        List<String> listDate = new ArrayList<>();
        String dateFormat = "yyyy年MM月dd日 HH:mm";
        if ("M".equals(dateType)) {
            dateFormat = "yyyy年MM月";
        }
        if ("D".equals(dateType)) {
            dateFormat = "yyyy年MM月dd日";
        }
        listDate.add(new SimpleDateFormat(dateFormat).format(dBegin));
        Calendar calBegin = Calendar.getInstance();
        calBegin.setTime(dBegin);
        Calendar calEnd = Calendar.getInstance();
        calEnd.setTime(dEnd);
        while (calBegin.compareTo(calEnd) <= 0) {
            if ("H".equals(dateType)) {
                calBegin.add(Calendar.HOUR, time);
            }
            if ("M".equals(dateType)) {
                calBegin.add(Calendar.MONTH, time);
            }
            if ("D".equals(dateType)) {
                calBegin.add(Calendar.DATE, time);
            }
            if ("N".equals(dateType)) {
                calBegin.add(Calendar.MINUTE, time);
            }
            if (calBegin.compareTo(calEnd) <= 0) {
                listDate.add(new SimpleDateFormat(dateFormat).format(calBegin.getTime()));
            } else {
                // listDate.add(new SimpleDateFormat("yyyy-MM-dd HH:mm").format(calEnd.getTime()));
            }

        }
        return listDate;
    }

    /**
     * 取某时间段周几的日期列表
     *
     * @param dataBegin
     * @param dataEnd
     * @param weekDays
     * @return
     */
    public static List<String> getDayOfWeekWithinDateInterval(String dataBegin, String dataEnd, List<Integer> weekDays) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy年MM月dd日");
        List dateResult = new ArrayList<>();
        Calendar cal = Calendar.getInstance();
        String[] dateInterval = {dataBegin, dataEnd};
        Date[] dates = new Date[dateInterval.length];
        for (int i = 0; i < dateInterval.length; i++) {
            String[] ymd = dateInterval[i].split("[^\\d]+");
            cal.set(Integer.parseInt(ymd[0]), Integer.parseInt(ymd[1]) - 1, Integer.parseInt(ymd[2]));
            dates[i] = cal.getTime();
        }

        for (Date date = dates[0]; date.compareTo(dates[1]) <= 0; ) {
            cal.setTime(date);
            for (Integer weekDay : weekDays) {
                if (cal.get(Calendar.DAY_OF_WEEK) - 1 == weekDay) {
                    String format = sdf.format(date);
                    dateResult.add(format);
                }
            }

            cal.add(Calendar.DATE, 1);
            date = cal.getTime();
        }
        return dateResult;

    }

    public static String splitHoursAndSecond(String date) {
        Date stringDate = stringToDate(date, "yyyy-MM-dd HH:mm:ss");
        StringBuffer sb = new StringBuffer();
        if (stringDate.getHours() < 10) {
            sb.append(0).append(stringDate.getHours()).append(":");
        } else {
            sb.append(stringDate.getHours()).append(":");
        }

        if (stringDate.getMinutes() < 10) {
            sb.append(0).append(stringDate.getMinutes());
        } else {
            sb.append(stringDate.getMinutes());
        }
        return sb.toString();
    }


    /**
     * 获取当前天数的 + 或 — day 的天数
     *
     * @param startDate
     * @param day
     * @return
     */
    public static String dateConvert(String startDate, Integer day) {
        Date date = DateUtilX.stringToDate(startDate);
        Date date1 = DateUtilX.getDate(date, day, 0);
        String s = DateUtilX.dateToString(date1, DateUtilX.SPECIFIC_DATE);
        return s;
    }

    /**
     * 获取前几个月的当前时间
     *
     * @param month
     * @return
     */
    public static String getBeforeMonth(Integer month) {
        Date dNow = new Date();   //当前时间
        Date dBefore = new Date();
        Calendar calendar = Calendar.getInstance(); //得到日历
        calendar.setTime(dNow);//把当前时间赋给日历
        calendar.add(Calendar.MONTH, month);  //设置为前3月
        dBefore = calendar.getTime();   //得到前3月的时间
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd"); //设置时间格式
        String defaultStartDate = sdf.format(dBefore);    //格式化前3月的时间
        return defaultStartDate;
    }

    /**
     * 修改时间的时分秒
     */
    public static Date setTime(Date date, Integer hour,Integer minute,Integer second) {
        if (null != date) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            calendar.set(Calendar.HOUR_OF_DAY, hour);
            if (minute != null){
                calendar.set(Calendar.MINUTE, minute);
            }
            if (second != null){
                calendar.set(Calendar.SECOND, second);
            }
            date = calendar.getTime();
        }
        return date;
    }

    /**
     * 判断日期是否合法
     *
     * @param dateString
     * @return
     */
    public static boolean isValidDate(String dateString) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        sdf.setLenient(false); // 设置严格解析模式
        try {
            if (!StrUtilX.isEmpty(dateString) && dateString.trim().length() > 10){
                return false;
            }
            sdf.parse(dateString); // 尝试解析日期
            return true; // 解析成功，返回true
        } catch (ParseException e) {
            return false; // 解析失败，返回false
        }
    }

    /**
     * 判断日期是否合法
     *
     * @param dateString
     * @param format
     * @return
     */
    public static boolean isValidDate(String dateString, String format) {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        sdf.setLenient(false); // 设置严格解析模式
        try {
            sdf.parse(dateString); // 尝试解析日期
            return true; // 解析成功，返回true
        } catch (ParseException e) {
            return false; // 解析失败，返回false
        }
    }

    public static void main(String[] args) {
        System.out.println("======" + getDay(DateUtilX.stringToDate("2024-06-01"), DateUtilX.getCurrentDate()));
//        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
//        //获取前月的第一天
//        Calendar cal_1 = Calendar.getInstance();//获取当前日期
//        cal_1.add(Calendar.MONTH, -1);
//        cal_1.set(Calendar.DAY_OF_MONTH, 1);//设置为1号,当前日期既为本月第一天
//        String firstDay = format.format(cal_1.getTime());
//        System.out.println("-----1------firstDay:" + firstDay);
//
//        //获取前月的最后一天
//        Calendar cale = Calendar.getInstance();
//        cale.set(Calendar.DAY_OF_MONTH, 0);//设置为1号,当前日期既为本月第一天
//        String lastDay = format.format(cale.getTime());
//        System.out.println("-----2------lastDay:" + lastDay);
//
//        System.out.println(getFirstDayOfThisMonth());
//        System.out.println(getLastDayOfThisMonth());
//
//        System.out.println(betweenTime("00:20", "01:00", "24:00"));
//        System.out.println(splitHoursAndSecond("2020-07-03 00:00:00"));
//
//        System.out.println(getDay(getCurrentDate(), getDate(getCurrentDate(), 2, 0)));
//
//        System.out.println(compare(DateUtil.stringToDate("2022-04-07"), getCurrentDate()));
//
//        System.out.println(getHour(DateUtil.stringToDate("2022-05-08 13:25:01", hour_format), DateUtil.getCurrentDate()));
    }

}
