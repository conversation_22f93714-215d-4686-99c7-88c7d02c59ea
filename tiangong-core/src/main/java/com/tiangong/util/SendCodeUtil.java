package com.tiangong.util;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.aliyun.dysmsapi20170525.models.SendSmsRequest;
import com.aliyun.dysmsapi20170525.models.SendSmsResponse;
import com.aliyun.teaopenapi.models.Config;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;
import com.sendgrid.Method;
import com.sendgrid.Request;
import com.sendgrid.Response;
import com.sendgrid.SendGrid;
import com.sendgrid.helpers.mail.Mail;
import com.sendgrid.helpers.mail.objects.Attachments;
import com.sendgrid.helpers.mail.objects.Content;
import com.sendgrid.helpers.mail.objects.Email;
import com.sendgrid.helpers.mail.objects.Personalization;
import com.sun.mail.util.MailSSLSocketFactory;
import com.tiangong.config.AlyCodeConfigProperties;
import com.tiangong.config.EmailConfigProperties;
import com.tiangong.enums.LanguageTypeEnum;
import com.tiangong.exception.SysException;
import com.tiangong.req.MailAttachVO;
import com.tiangong.req.SendEmailReq;
import com.tiangong.req.SendPhoneReq;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.activation.DataHandler;
import javax.activation.DataSource;
import javax.activation.FileDataSource;
import javax.mail.*;
import javax.mail.internet.*;
import javax.mail.util.ByteArrayDataSource;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.nio.file.Files;
import java.security.GeneralSecurityException;
import java.util.Base64;
import java.util.Date;
import java.util.Properties;

/**
 * 发送邮件验证码
 */
@Component
@Slf4j
public class SendCodeUtil {

    @Autowired
    private EmailConfigProperties emailConfigProperties;

    @Autowired
    private AlyCodeConfigProperties alyCodeConfigProperties;

    public boolean sendEmail(SendEmailReq req, Integer isAttachment) {
        return sendEmail(req, isAttachment, null);
    }

    public boolean sendEmail(SendEmailReq req, Integer isAttachment, MailAttachVO mailAttachVO) {
        boolean sendResult = true;
        Properties prop = new Properties();
        // 协议
        prop.setProperty("mail.transport.protocol", emailConfigProperties.getProtocol());
        // 服务器
        prop.setProperty("mail.smtp.host", emailConfigProperties.getHost());
        // 端口
        prop.setProperty("mail.smtp.port", emailConfigProperties.getPort());
        // 使用smtp身份验证
        prop.setProperty("mail.smtp.auth", "true");

        MailSSLSocketFactory sf = null;
        try {
            sf = new MailSSLSocketFactory();
            sf.setTrustAllHosts(true);
        } catch (GeneralSecurityException e1) {
            e1.printStackTrace();
            sendResult = false;
            return sendResult;
        }

        prop.put("mail.smtp.ssl.enable", "true");
        prop.put("mail.smtp.ssl.socketFactory", sf);

        Session s = Session.getDefaultInstance(prop, new Authenticator() {
            @Override
            protected PasswordAuthentication getPasswordAuthentication() {
                return new PasswordAuthentication(emailConfigProperties.getUsername(), emailConfigProperties.getPassword());
            }
        });

        s.setDebug(true);
        Message mimeMessage = new MimeMessage(s);

        try {
            mimeMessage.setFrom(new InternetAddress(emailConfigProperties.getUsername(), MimeUtility.encodeText(emailConfigProperties.getSendUserName(), "UTF-8", "B")));

            if (req.getEmail().contains(",")) {
                String[] emails = req.getEmail().split(",");
                InternetAddress[] recipients = new InternetAddress[emails.length];
                for (int i = 0; i < emails.length; i++) {
                    recipients[i] = new InternetAddress(emails[i]);
                }
                mimeMessage.addRecipients(Message.RecipientType.TO, recipients);
            } else {
                mimeMessage.addRecipient(Message.RecipientType.TO, new InternetAddress(req.getEmail()));
            }
            // 设置抄送邮箱
            if (req.getCcEmail() != null && StrUtil.isNotBlank(req.getCcEmail())) {
                if (req.getCcEmail().contains(",")) {
                    String[] ccEmails = req.getCcEmail().split(",");
                    InternetAddress[] ccRecipients = new InternetAddress[ccEmails.length];
                    for (int i = 0; i < ccEmails.length; i++) {
                        ccRecipients[i] = new InternetAddress(ccEmails[i]);
                    }
                    mimeMessage.addRecipients(Message.RecipientType.CC, ccRecipients);
                } else {
                    mimeMessage.addRecipient(Message.RecipientType.CC, new InternetAddress(req.getCcEmail()));
                }
            }


            // 设置密送邮箱
            if (req.getBccEmail() != null && StrUtil.isNotBlank(req.getBccEmail())) {
                if (req.getBccEmail().contains(",")) {
                    String[] bccEmails = req.getBccEmail().split(",");
                    InternetAddress[] bccRecipients = new InternetAddress[bccEmails.length];
                    for (int i = 0; i < bccEmails.length; i++) {
                        bccRecipients[i] = new InternetAddress(bccEmails[i]);
                    }
                    mimeMessage.addRecipients(Message.RecipientType.BCC, bccRecipients);
                } else {
                    mimeMessage.addRecipient(Message.RecipientType.BCC, new InternetAddress(req.getBccEmail()));
                }
            }
            mimeMessage.setSubject(req.getTopic());

            // 创建 Multipart 容器
            Multipart mp = new MimeMultipart();

            // 设置 HTML 内容
            MimeBodyPart htmlPart = new MimeBodyPart();
            htmlPart.setContent(req.getContent(), "text/html; charset=UTF-8"); // 确保以 HTML 格式设置内容
            mp.addBodyPart(htmlPart);


            // 添加邮件正文（可选）
            BodyPart textPart = new MimeBodyPart();
            textPart.setText("请查收附件中的邮件内容。"); // 正文通知用户查收附件
            mp.addBodyPart(textPart);


            // 添加 HTML 附件
            //isAttachment = 0: 不添加附件
            //isAttachment = 1: 添加附件
            if (isAttachment == 1) {
                if (mailAttachVO != null) {
                    if (mailAttachVO.getName() == null || mailAttachVO.getFileUrl() == null) {
                        throw new SysException("附件的文件名称和附件流不可为空");
                    }
                    // 获取文件名和扩展名
                    int lastDotIndex = mailAttachVO.getName().lastIndexOf(".");
                    String fileName = lastDotIndex > 0 ? mailAttachVO.getName().substring(0, lastDotIndex) : mailAttachVO.getName();
                    String fileNameSuffix = lastDotIndex > 0 ? mailAttachVO.getName().substring(lastDotIndex) : "";
                    fileName = fileName + System.currentTimeMillis();

                    // 创建临时文件
                    File tempFile = File.createTempFile(fileName, fileNameSuffix);

                    // 下载文件内容到临时文件
                    // 如果是URL，从URL下载文件
                    try {
                        byte[] fileData = downloadFile(mailAttachVO.getFileUrl());
                        try (FileOutputStream fos = new FileOutputStream(tempFile)) {
                            fos.write(fileData);
                        }
                    } catch (Exception e) {
                        log.error("下载附件文件失败", e);
                        throw new SysException("下载附件文件失败: " + e.getMessage());
                    }
                    BodyPart bodyPart = new MimeBodyPart();
                    DataSource htmlSource = new FileDataSource(tempFile);
                    bodyPart.setDataHandler(new DataHandler(htmlSource));
                    bodyPart.setFileName(tempFile.getName());
                    mp.addBodyPart(bodyPart);
                    //设置附件
                    req.setType(3);
                    req.setFile(tempFile);
                    req.setFileName(fileName + fileNameSuffix);
                } else {
                    // 将 HTML 内容写入临时文件
                    File htmlFile = File.createTempFile("emailContent", ".html");
                    try (BufferedWriter writer = new BufferedWriter(new FileWriter(htmlFile))) {
                        writer.write(req.getContent());
                    }

                    BodyPart htmlAttachmentBodyPart = new MimeBodyPart();
                    DataSource htmlSource = new FileDataSource(htmlFile);
                    htmlAttachmentBodyPart.setDataHandler(new DataHandler(htmlSource));
                    htmlAttachmentBodyPart.setFileName(htmlFile.getName());
                    mp.addBodyPart(htmlAttachmentBodyPart);
                    //设置附件
                    req.setType(3);
                    req.setFile(htmlFile);
                    req.setFileName("emailContent" + System.currentTimeMillis() + ".html");
                }
            }

            //校验发送邮件方式
            if (emailConfigProperties.getEmailType().equals("grid")) {
                req.setContent(req.getContent());
                return sendGrid(req);
            }


            // 设置最终内容
            mimeMessage.setContent(mp);

            mimeMessage.setSentDate(new Date());
            mimeMessage.saveChanges();
            // 发送
            Transport.send(mimeMessage);
        } catch (Exception e) {
            log.error("发送邮件异常", e);
            sendResult = false;
        }
        return sendResult;
    }


    /**
     * 发送qq企业邮箱--验证码
     */
    public boolean sendQQEmail(SendEmailReq req) {
        //校验发送邮件方式
        if (emailConfigProperties.getEmailType().equals("grid")) {
            return sendGrid(req);
        }
        boolean sendResult = true;
        Properties prop = new Properties();
        //协议
        prop.setProperty("mail.transport.protocol", emailConfigProperties.getProtocol());
        //服务器
        prop.setProperty("mail.smtp.host", emailConfigProperties.getHost());
        //端口
        prop.setProperty("mail.smtp.port", emailConfigProperties.getPort());
        //使用smtp身份验证
        prop.setProperty("mail.smtp.auth", "true");
        //使用SSL，企业邮箱必需！
        //开启安全协议
        MailSSLSocketFactory sf = null;
        try {
            sf = new MailSSLSocketFactory();
            sf.setTrustAllHosts(true);
        } catch (GeneralSecurityException e1) {
            e1.printStackTrace();
            sendResult = false;
            return sendResult;
        }
        prop.put("mail.smtp.ssl.enable", "true");
        prop.put("mail.smtp.ssl.socketFactory", sf);
        //
        //获取Session对象
        Session s = Session.getDefaultInstance(prop, new Authenticator() {
            //此访求返回用户和密码的对象
            @Override
            protected PasswordAuthentication getPasswordAuthentication() {
                PasswordAuthentication pa = new PasswordAuthentication(emailConfigProperties.getUsername(), emailConfigProperties.getPassword());
                return pa;
            }
        });
        //设置session的调试模式，发布时取消
        s.setDebug(true);
        Message mimeMessage = new MimeMessage(s);
        try {
            mimeMessage.setFrom(new InternetAddress(emailConfigProperties.getUsername(), MimeUtility.encodeText(emailConfigProperties.getSendUserName(), "UTF-8", "B")));

            //判断是否为多个邮箱
            if (req.getEmail().contains(",")) {
                String[] emails = req.getEmail().split(",");
                //多个邮箱
                InternetAddress[] recipients = new InternetAddress[emails.length];
                for (int i = 0; i < emails.length; i++) {
                    recipients[i] = new InternetAddress(emails[i]);
                }
                mimeMessage.addRecipients(Message.RecipientType.TO, recipients);
            } else {
                //单个邮箱
                mimeMessage.addRecipient(Message.RecipientType.TO, new InternetAddress(req.getEmail()));
            }


            //设置主题
            mimeMessage.setSubject(req.getTopic());

            //容器类，可以包含多个MimeBodyPart对象
            Multipart mp = new MimeMultipart();

            //设置文本内容
            BodyPart textPart = new MimeBodyPart();
            textPart.setText(req.getContent());
            mp.addBodyPart(textPart);

            if (null == req.getType() || req.getType() == 1) {
                mimeMessage.setContent(mp, "text/html; charset=UTF-8");
            }

            //设置图片
            if (req.getType() == 2 || req.getType() == 4) {
                // 创建邮件内容部分
                MimeBodyPart textPart1 = new MimeBodyPart();
                textPart1.setContent(req.getContent(), "text/html; charset=UTF-8");

                // 创建图片部分
                MimeBodyPart imagePart = new MimeBodyPart();
                imagePart.setContent("<img src='" + req.getImageUrl() + "'>", "text/html");

                // 组合邮件内容和图片部分
                MimeMultipart multipart = new MimeMultipart();
                multipart.addBodyPart(textPart1);
                multipart.addBodyPart(imagePart);

                // 将组合后的内容设置为邮件的内容
                mimeMessage.setContent(multipart);
            }

            //设置附件
            if (req.getType() == 3 || req.getType() == 4) {
                BodyPart attachmentBodyPart = new MimeBodyPart();
                //处理附件
                URL url = new URL(req.getFileUrl());
                URLConnection connection = url.openConnection();
                InputStream inputStream = connection.getInputStream();
                BufferedInputStream bufferedInputStream = new BufferedInputStream(inputStream);
                DataSource source = new ByteArrayDataSource(bufferedInputStream, connection.getContentType());
                attachmentBodyPart.setDataHandler(new DataHandler(source));
                attachmentBodyPart.setFileName(req.getFileName());

                mp.addBodyPart(attachmentBodyPart);
                mimeMessage.setContent(mp, "text/html; charset=UTF-8");
            }

            mimeMessage.setSentDate(new Date());
            mimeMessage.saveChanges();
            //发送
            Transport.send(mimeMessage);
        } catch (Exception e) {
            log.error("发送qq企业邮箱--验证码异常", e);
            sendResult = false;
        }
        return sendResult;
    }

    /**
     * 发送qq企业邮箱--附件
     */
    public boolean sendQQEmailToFile(String email, String httpUrl, String orderCode, String language, String domain) {
        boolean sendResult = true;
        Properties prop = new Properties();
        //协议
        prop.setProperty("mail.transport.protocol", emailConfigProperties.getProtocol());
        //服务器
        prop.setProperty("mail.smtp.host", emailConfigProperties.getHost());
        //端口
        prop.setProperty("mail.smtp.port", emailConfigProperties.getPort());
        //使用smtp身份验证
        prop.setProperty("mail.smtp.auth", "true");
        //使用SSL，企业邮箱必需！
        //开启安全协议
        MailSSLSocketFactory sf = null;
        try {
            sf = new MailSSLSocketFactory();
            sf.setTrustAllHosts(true);
        } catch (GeneralSecurityException e1) {
            e1.printStackTrace();
            sendResult = false;
            return sendResult;
        }
        prop.put("mail.smtp.ssl.enable", "true");
        prop.put("mail.smtp.ssl.socketFactory", sf);
        //
        //获取Session对象
        Session s = Session.getDefaultInstance(prop, new Authenticator() {
            //此访求返回用户和密码的对象
            @Override
            protected PasswordAuthentication getPasswordAuthentication() {
                PasswordAuthentication pa = new PasswordAuthentication(emailConfigProperties.getUsername(), emailConfigProperties.getPassword());
                return pa;
            }
        });
        //设置session的调试模式，发布时取消
        s.setDebug(true);
        MimeMessage mimeMessage = new MimeMessage(s);
        try {
            mimeMessage.setFrom(new InternetAddress(emailConfigProperties.getUsername(), MimeUtility.encodeText(emailConfigProperties.getSendUserName(), "UTF-8", "B")));
            mimeMessage.addRecipient(Message.RecipientType.TO, new InternetAddress(email));
            //设置主题
            if (LanguageTypeEnum.zh_CN.getValue().equals(language)) {
                mimeMessage.setSubject(MimeUtility.encodeText("红色加力入住凭证", "UTF-8", "B"));
            } else {
                mimeMessage.setSubject("Red booster check-in voucher");
            }
            //容器类，可以包含多个MimeBodyPart对象
            Multipart mp = new MimeMultipart();

            //MimeBodyPart可以包装文本，图片，附件
            MimeBodyPart body = new MimeBodyPart();
            //HTML正文
            String text = null;
            if (LanguageTypeEnum.zh_CN.getValue().equals(language)) {
                text = "<p>尊敬的红色加力用户：</p>" +
                        "<p></p><p></p>" +
                        "<p>感谢您使用红色加力为您提供出行服务，本邮件由系统自动发出，请勿回复。</p>" +
                        "<p>红色加力祝您旅途愉快！</p>" +
                        "<p></p>" +
                        "<p>温馨提示：</p>" +
                        "<p>如附件下载失败，可登录" + domain + "进入\"订单详情页\"→\"入住凭证\"→\"重新发送\"。</p>";
            } else {
                text = "<p>Dear Red Booster User:</p>\n" +
                        "\n" +
                        "\"<p></p><p><p></p>\n" +
                        "\n" +
                        "\"Thank you for using the red booster to provide you with travel services. This email is automatically sent by the system, please do not reply.\n" +
                        "\n" +
                        "\"Red booster wishes you a pleasant journey!\n" +
                        "\n" +
                        "\"<p></p>\n" +
                        "\n" +
                        "\"Warm reminder:</p>\n" +
                        "\n" +
                        "If the attachment download fails, you can log in " + domain + " Go to the \"Order Details Page\" → \"check-in voucher\" → \"Resend";
            }
            body.setContent(text, "text/html; charset=UTF-8");
            mp.addBodyPart(body);

            BodyPart attachmentBodyPart = new MimeBodyPart();
            //处理附件
            URL url = new URL(httpUrl);
            URLConnection connection = url.openConnection();
            InputStream inputStream = connection.getInputStream();
            BufferedInputStream bufferedInputStream = new BufferedInputStream(inputStream);
            DataSource source = new ByteArrayDataSource(bufferedInputStream, connection.getContentType());
            attachmentBodyPart.setDataHandler(new DataHandler(source));
            if (LanguageTypeEnum.zh_CN.getValue().equals(language)) {
                attachmentBodyPart.setFileName(orderCode + MimeUtility.encodeText("入住凭证.pdf", "UTF-8", "B"));
            } else {
                attachmentBodyPart.setFileName(orderCode + MimeUtility.encodeText("Check-in voucher.pdf", "UTF-8", "B"));
            }
            //校验发送邮件方式
            if (emailConfigProperties.getEmailType().equals("grid")) {
                SendEmailReq req = new SendEmailReq();

                req.setType(3);
                req.setFileUrl(httpUrl);
                req.setFileName(attachmentBodyPart.getFileName());
                req.setTopic(mimeMessage.getSubject());
                req.setContent(text);
                req.setEmail(email);
                return sendGrid(req);
            }

            mp.addBodyPart(attachmentBodyPart);

            mimeMessage.setSentDate(new Date());
            //设置内容
            mimeMessage.setContent(mp);

            mimeMessage.saveChanges();
            //发送
            Transport.send(mimeMessage);
        } catch (Exception e) {
            log.error("发送qq企业邮箱--附件异常", e);
            sendResult = false;
        }
        return sendResult;
    }

    /**
     * 阿里云
     * 发国内短信
     */
    public Boolean sendSms(SendPhoneReq req) {
        boolean send = false;
        try {
            //可自助调整超时时间
            System.setProperty("sun.net.client.defaultConnectTimeout", "10000");
            System.setProperty("sun.net.client.defaultReadTimeout", "10000");

            //初始化acsClient,暂不支持region化
            IClientProfile profile = DefaultProfile.getProfile("cn-hangzhou", alyCodeConfigProperties.getAccessKeyId(), alyCodeConfigProperties.getAccessKeySecret());

            //开发者无需替换
            DefaultProfile.addEndpoint("cn-hangzhou", "cn-hangzhou", "Dysmsapi", alyCodeConfigProperties.getEndpoint());
            IAcsClient acsClient = new DefaultAcsClient(profile);

            //组装请求对象-具体描述见控制台-文档部分内容
            com.aliyuncs.dysmsapi.model.v20170525.SendSmsRequest request = new com.aliyuncs.dysmsapi.model.v20170525.SendSmsRequest();
            //必填:待发送手机号
            request.setPhoneNumbers(req.getPhone());
            //必填:短信签名-可在短信控制台中找到
            request.setSignName(alyCodeConfigProperties.getSignName());
            if (StringUtils.isEmpty(req.getTemplateCode())) {
                //必填:短信模板-可在短信控制台中找到
                request.setTemplateCode(alyCodeConfigProperties.getTemplateCode());
            } else {
                request.setTemplateCode(req.getTemplateCode());
            }
            //可选:模板中的变量替换JSON串,如模板内容为"亲爱的${name},您的验证码为${code}"时,此处的值为
            request.setTemplateParam(req.getJson().toJSONString());

            com.aliyuncs.dysmsapi.model.v20170525.SendSmsResponse acsResponse = acsClient.getAcsResponse(request);
            log.info("响应的结果是：{},{}", acsResponse.getMessage(), acsResponse.getCode());
            if (acsResponse.getCode().equalsIgnoreCase("ok")) {
                send = true;
            }
        } catch (ClientException e) {
            log.error("发国内短信异常", e);
        }
        return send;
    }

    /**
     * 阿里云
     * 发送海外短信
     */
    public Boolean sendOSea(SendPhoneReq req) {
        log.info("发送海外短信的请求数据是：{}", req);
        boolean send = false;
        try {
            Config config = new Config()
                    .setAccessKeyId(alyCodeConfigProperties.getAccessKeyId())
                    .setAccessKeySecret(alyCodeConfigProperties.getAccessKeySecret())
                    .setEndpoint(alyCodeConfigProperties.getSeaEndpoint());

            //Client client = new Client(config);
            com.aliyun.dysmsapi20170525.Client client = new com.aliyun.dysmsapi20170525.Client(config);
            SendSmsRequest request = new SendSmsRequest();
            request.setPhoneNumbers(req.getPhone());
            request.setSignName(alyCodeConfigProperties.getSeaSignName());

            if (StringUtils.isEmpty(req.getTemplateCode())) {
                //必填:短信模板-可在短信控制台中找到
                request.setTemplateCode(alyCodeConfigProperties.getSeaTemplateCode());
            } else {
                request.setTemplateCode(req.getTemplateCode());
            }
            //可选:模板中的变量替换JSON串,如模板内容为"亲爱的${name},您的验证码为${code}"时,此处的值为
            request.setTemplateParam(req.getJson().toJSONString());

            SendSmsResponse response = client.sendSms(request);
            if (response != null) {
                send = true;
                log.info("海外短信返回结果是：" + response.body.getMessage());
            }
        } catch (Exception e) {
            log.error("发送海外短信异常", e);
        }
        return send;
    }

    public static void main(String[] args) {
        //sendGrid();
        String url = "http://url.png";
        int lastDotIndex = url.lastIndexOf(".");
        String suffix = url.substring(lastDotIndex + 1);
        System.out.println(suffix);
        //return sendResult;
    }

    public boolean sendGrid(SendEmailReq req) {
        // 你的 SendGrid API 密钥
        try {
            // 创建一个邮件对象
            Mail mail = new Mail();

            // 设置发件人
            //Email from = new Email(emailConfigProperties.getGridAccount(), "红色加力");
            Email from = new Email(emailConfigProperties.getGridAccount(), emailConfigProperties.getSendUserName());
            mail.setFrom(from);

            // 设置收件人
            Personalization personalization = new Personalization();

            if (req.getEmail().contains(",")) {
                String[] emails = req.getEmail().split(",");
                for (String email : emails) {
                    Email to = new Email(email.trim());
                    personalization.addTo(to);
                }
            } else {
                Email to = new Email(req.getEmail());
                personalization.addTo(to);
            }


            // 设置抄送邮箱
            if (req.getCcEmail() != null && StrUtil.isNotBlank(req.getCcEmail())) {
                if (req.getCcEmail().contains(",")) {
                    String[] ccEmails = req.getCcEmail().split(",");
                    for (String ccEmail : ccEmails) {
                        Email cc = new Email(ccEmail.trim());
                        personalization.addCc(cc);
                    }
                } else {
                    Email cc = new Email(req.getCcEmail().trim());
                    personalization.addCc(cc);
                }
            }
            // 设置密送邮箱
            if (req.getBccEmail() != null && StrUtil.isNotBlank(req.getBccEmail())) {
                if (req.getBccEmail().contains(",")) {
                    String[] bccEmails = req.getBccEmail().split(",");
                    for (String bccEmail : bccEmails) {
                        Email bcc = new Email(bccEmail);
                        personalization.addBcc(bcc);
                    }
                } else {
                    Email bcc = new Email(req.getBccEmail());
                    personalization.addBcc(bcc);
                }
            }

            mail.addPersonalization(personalization);

            // 设置主题
            mail.setSubject(req.getTopic());

            // 设置邮件正文
            if (null == req.getType() || req.getType() == 1) {
                Content content = new Content("text/html", req.getContent());
                mail.addContent(content);
            }
            // 添加内嵌图片
            if (req.getType() == 2 || req.getType() == 4) {
                Content content = new Content("text/html", req.getContent() + ".<img src=\"cid:image1\">");
                mail.addContent(content);
                int lastDotIndex = req.getImageUrl().lastIndexOf(".");
                String suffix = req.getImageUrl().substring(lastDotIndex + 1);
                File imageFile = new File(req.getImageUrl());
                byte[] imageData = Files.readAllBytes(imageFile.toPath());
                Attachments image = new Attachments();
                image.setContent(Base64.getEncoder().encodeToString(imageData));
                image.setType("image/" + suffix);
                image.setFilename("image." + suffix);
                image.setDisposition("inline");
                image.setContentId("image1");
                mail.addAttachments(image);
            }

            // 添加附件
            if (req.getType() == 3 || req.getType() == 4) {
                Content content = new Content("text/html", req.getContent());
                mail.addContent(content);
                String suffix = null;
                File attachmentFile = null;
                byte[] attachmentData = null;
                Attachments attachment = new Attachments();
                //判断是否是文件连接
                if (StringUtils.isNotEmpty(req.getFileUrl())) {
                    int lastDotIndex = req.getFileUrl().lastIndexOf(".");
                    suffix = req.getFileUrl().substring(lastDotIndex + 1);
                    byte[] bytes = downloadFile(req.getFileUrl());
                    attachment.setContent(Base64.getEncoder().encodeToString(bytes));
                } else {
                    //不是文件连接就必须传递File类
                    int lastDotIndex = req.getFileName().lastIndexOf(".");
                    suffix = req.getFileName().substring(lastDotIndex + 1);
                    attachmentFile = req.getFile();
                    attachmentData = Files.readAllBytes(attachmentFile.toPath());
                    attachment.setContent(Base64.getEncoder().encodeToString(attachmentData));
                }

                attachment.setType("application/" + suffix);
                attachment.setFilename(req.getFileName());
                attachment.setDisposition("attachment");
                mail.addAttachments(attachment);
            }


            // 创建 SendGrid 客户端并发送邮件
            SendGrid sg = new SendGrid(emailConfigProperties.getGridApiKey());
            Request request = new Request();
            try {
                request.setMethod(Method.POST);
                request.setEndpoint("mail/send");
                request.setBody(mail.build());
                Response response = sg.api(request);
                log.info("sendGrid==========request={}=======response={}", JSONUtil.toJsonStr(request), JSONUtil.toJsonStr(response));
                return response.getStatusCode() == 202;
            } catch (IOException ex) {
                throw new RuntimeException(ex);
            }
        } catch (Exception e) {
            log.error("sendGrid异常", e);
            return false;
        }
    }

    private static byte[] downloadFile(String fileUrl) throws Exception {
        URL url = new URL(fileUrl); // 创建 URL 实例
        HttpURLConnection connection = (HttpURLConnection) url.openConnection(); // 打开连接
        connection.setRequestMethod("GET");

        // 检查响应代码
        if (connection.getResponseCode() != HttpURLConnection.HTTP_OK) {
            throw new RuntimeException("Failed to download file: HTTP error code : " + connection.getResponseCode());
        }

        try (InputStream inputStream = connection.getInputStream(); // 获取输入流
             ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) { // 用于存储下载的数据
            byte[] buffer = new byte[4096];
            int bytesRead;

            // 循环读取输入流中的数据
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }

            return outputStream.toByteArray(); // 返回文件字节数组
        }
    }
}


