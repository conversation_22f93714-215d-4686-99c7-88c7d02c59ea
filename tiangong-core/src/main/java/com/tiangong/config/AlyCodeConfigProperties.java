package com.tiangong.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @create 2023/11/27 19:48
 */
@Data
@Component
@ConfigurationProperties(prefix = "aly")
public class AlyCodeConfigProperties {

    /**
     * 阿里云短信accessKey
     */
    private String accessKeyId;

    /**
     * 阿里云短信Secret
     */
    private String accessKeySecret;

    /**
     * 签名-国内
     */
    private String signName;

    /**
     * 签名-海外
     */
    private String seaSignName;

    /**
     * 模板-国内
     */
    private String templateCode;

    /**
     * 模板-海外
     */
    private String seaTemplateCode;

    /**
     * 发送节点-国内
     */
    private String endpoint;

    /**
     * 发送节点-海外
     */
    private String seaEndpoint;
}
