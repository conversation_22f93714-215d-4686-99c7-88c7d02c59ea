package com.tiangong.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 系统错误编码枚举
 */
@Getter
@AllArgsConstructor
public enum ErrorCodeEnum {

    /***********公共业务异常类错误代码 以0开头***********/
    SUCCESS("000", "SUCCESS", "成功"),
    FAIL("99999", "FAIL", "失败"),
    INVALID_INPUTPARAM("001", "INVALID_PARAMETER", "无效的入参对象"),


    /***********系统异常类错误代码 以9开头***********/
    SYSTEM_EXCEPTION("900", "SYSTEM_EXCEPTION", "系统异常"),
    OUTER_IF_EXCEPTION("901", "CENTER_OUTER_IF_EXCEPTION", "外部接口调用异常"),
    UNKNOWN_EXCEPTION("902", "UNKNOWN_EXCEPTION", "未知异常"),
    CONNECT_TIME_OUT("903", "CONNECT_TIME_OUT", "连接超时"),
    READ_TIME_OUT("904", "CENTER_READ_TIME_OUT", "访问超时"),
    INSERT_DATA_EXCEPTION("905", "CENTER_INSERT_DATA_EXCEPTION", "插入数据异常"),
    NOT_LOGIN("906", "CENTER_NOT_LOGIN", "该用户未登陆"),
    PERMISSION_DENIED("907", "CENTER_PERMISSION_DENIED", "该用户没有权限"),
    FAIL_SIGNATURE("908", "CENTER_FAIL_SIGNATURE", "token解析失败"),
    INTERNAL_TIMEOUT("908", "HUB_INTERNAL_TIMEOUT", "内部连接超时"),
    EXPIRED_SIGNATURE("909", "CENTER_EXPIRED_SIGNATURE", "token过期"),
    EXCEPTION_SIGNATURE("910", "CENTER_EXCEPTION_SIGNATURE", "token解析异常"),
    FAIL_COUNT_OUT("911", "CENTER_FAIL_COUNT_OUT", "保存失败，配置信息超出设置范围"),
    ONLINE_COUNT_OUT("912", "CENTER_ONLINE_COUNT_OUT", "最大同时在线员工数不可小于1"),
    APPLY_ERROR("913", "CENTER_APPLY_ERROR", "审核失败"),


    /***********订单异常 以2开头***********/
    ORDER_NOT_EXISTS("201", "ORDER_NOT_EXISTS", "订单不存在"),
    CONFIRM_OTA_ORDER_ERROR("202", "CENTER_CONFIRM_OTA_ORDER_ERROR", "确认OTA订单系统异常"),
    NOT_HOTEL_MAPPING("203", "CENTER_NOT_HOTEL_MAPPING", "没有酒店映射"),
    NOT_ROOM_MAPPING("204", "CENTER_NOT_ROOM_MAPPING", "没有房型映射"),
    NOT_PRODUCT_MAPPING("205", "CENTER_NOT_PRODUCT_MAPPING", "没有产品映射"),
    PRODUCT_DISABLE("206", "CENTER_PRODUCT_DISABLE", "产品已经停售"),
    INSUFFICIENT_AMOUNT("207", "CENTER_INSUFFICIENT_AMOUNT", "信用额度不足，无法下单！"),
    NO_SALE_PRICE("208", "CENTER_NO_SALE_PRICE", "没有价格"),
    PRICE_NOT_ACCURATE("209", "CENTER_PRICE_NOT_ACCURATE", "价格不符合"),
    QUOTA_ENOUGH("210", "CENTER_QUOTA_ENOUGH", "产品库存不足"),
    ERROR_RESTRICT("211", "CENTER_ERROR_RESTRICT", "条款不符合"),
    ERROR_BREAKFAST("212", "CENTER_ERROR_BREAKFAST", "早餐数不对"),
    PRICE_AND_QUOTA_ERROR("213", "CENTER_PRICE_AND_QUOTA_ERROR", "价格和配额都不对"),
    CLOSE_ROOM("214", "CENTER_CLOSE_ROOM", "关房"),
    EXIST_ORDER("215", "CENTER_EXIST_ORDER", "存在重复的客户单号"),
    NOTIFY_ORDER_FAIL("216", "CENTER_NOTIFY_ORDER_FAIL", "订单状态变更失败"),
    SEND_SUPPLY_ORDER_TO_SUPPLIER_ERROR("217", "CENTER_SEND_SUPPLY_ORDER_TO_SUPPLIER_ERROR", "发单给供应商异常"),
    CHANNEL_ORDER_CODE_EXISTS("218", "CENTER_CHANNEL_ORDER_CODE_EXISTS", "客户单号在系统中已经存在，不可重复"),
    QUERY_ORDER_ERROR("219", "CENTER_QUERY_ORDER_ERROR", "订单查询异常"),
    PUSH_ORDER_STATUS_ERROR("220", "CENTER_PUSH_ORDER_STATUS_ERROR", "订单状态推送失败"),
    UPDATE_PAY_STATUS_ERROR("221", "CENTER_UPDATE_PAY_STATUS_ERROR", "修改订单状态失败"),
    ORDER_PAY_IS_ALREADY("222", "CENTER_ORDER_PAY_IS_ALREADY", "订单已支付"),

    ORDER_CODE_IS_NOT_EXIST("228", "ORDER_CODE_IS_NOT_EXIST", "订单不存在"),
    PAY_METHOD_NOT_SUPPORTED_PAY("223", "CENTER_PAY_METHOD_NOT_SUPPORTED_PAY", "到店付订单不支持支付"),
    ORDER_IS_ALREADY_CANCEL("224", "CENTER_ORDER_IS_ALREADY_CANCEL", "订单已取消"),
    SUPPLY_ORDER_NOT_EXIST("225", "CENTER_SUPPLY_ORDER_NOT_EXIST", "供货单信息不存在"),
    QUERY_SUPPLY_ORDER_FAIL("226", "CENTER_QUERY_SUPPLY_ORDER_FAIL", "查询供货单信息失败"),
    ORDER_TASK_PROCESSED("227", "ORDER_TASK_PROCESSED", "订单退款任务已处理，不可重复操作"),
    PAY_AMOUNT_NEQ_ORDER_AMT("228", "PAY_AMOUNT_NEQ_ORDER_AMT", "房费总价与订单金额不一致"),
    ORDER_NOT_REFUND("229", "ORDER_NOT_REFUND", "订单未支付或未确认，不支持退款"),

    CHECKOUT_APPLY_STATUS_PUSH_ERROR("230", "CHECKOUT_APPLY_STATUS_PUSH_ERROR", "订单退房申请状态通知推送失败，地址为空"),
    ORDER_GUARANTEETYPE_ERROR("231", "ORDER_GUARANTEETYPE_ERROR", "订单担保类型错误"),
    INVALID_SUPPLY_ORDER_TYPE("232", "INVALID_SUPPLY_ORDER_TYPE", "无效发单类型"),
    UNSUPPORTED_ORDER_OPERATION("233", "UNSUPPORTED_ORDER_OPERATION", "不支持的订单操作"),
    PUSH_ORDER_STATUS_URL_IS_EMPTY("234", "PUSH_ORDER_STATUS_URL_IS_EMPTY", "客户没配置推送地址"),
    PUSH_WARRANTIES_NOTIFY_URL_IS_EMPTY("235", "PUSH_WARRANTIES_NOTIFY_URL_IS_EMPTY", "客户未配置担保扣款通知推送地址"),

    /***用户异常以及权限 以1开头***/
    NOT_ADMIN("100", "CENTER_NOT_ADMIN", "该账号不为管理员账号，不能重置"),
    REPEAT_ACCOUNT("101", "CENTER_REPEAT_ACCOUNT", "该账号已存在，请换个账号重试"),
    NO_ADMIN_AUTH("102", "CENTER_NO_ADMIN_AUTH", "不能增加总管理员权限"),
    ADMIN_OR_NOT_EXIST_USER("103", "CENTER_UPDATE_ERROR", "修改失败"),
    THIS_USER_CANNOT_MODIFY("104", "CENTER_THIS_USER_CANNOT_MODIFY", "登录用户不能被修改"),
    NOT_EXIST_USER("105", "CENTER_NOT_EXIST_USER", "不存在该用户"),
    PASSWORD_ERROR("106", "CENTER_ACCOUNT_PASSWORD_IS_ERROR", "账号或密码错误"),
    USER_IS_DISABLED("107", "CENTER_USER_IS_DISABLED", "该账号已被禁用，无法登录"),
    ROLE_HAS_USER("108", "CENTER_ROLE_HAS_USER", "该角色下绑定了用户，不能删除"),
    REPEAT_TEL_OR_ID_NUMBER("109", "CENTER_REPEAT_TEL_OR_ID_NUMBER", "手机号或身份证已存在"),
    UPDATE_PASSWORD_ERROR("110", "CENTER_UPDATE_PASSWORD_ERROR", "密码错误，请重新输入"),
    LOGIN_ERROR("111", "CENTER_LOGIN_ERROR", "登录错误。"),
    AUDITING("112", "CENTER_AUDITING", "正在审核中"),
    IDCARDNOTCHANGED("113", "CENTER_IDCARDNOTCHANGED", "新身份证号码不能与旧的一致"),
    PHONENOTCHANGED("114", "CENTER_PHONENOTCHANGED", "新手机号不能与旧的一致"),
    ACCOUNT_IS_LOGOUT("115", "ACCOUNT_IS_LOGOUT", "该账号已注销"),
    EMPLOYEE_RESUBMIT_FAIL("116", "EMPLOYEE_RESUBMIT_FAIL", "重新提交失败"),


    /**** 酒店信息异常 以3开头****/
    EXIST_HOTEL("301", "CENTER_EXIST_HOTEL", "已存在该名称的酒店"),
    NOT_EXIST_HOTEL("302", "CENTER_NOT_EXIST_HOTEL", "不存在该酒店请重新操作"),
    NOT_EXIST_ROOM("303", "CENTER_NOT_EXIST_ROOM", "不存在该房型或者不存在该酒店请重新操作"),
    EXIST_ROOM("304", "CENTER_EXIST_ROOM", "已存在该名称的房型或者该酒店不存在"),
    NOT_EXIST_PHOTO("305", "CENTER_NOT_EXIST_PHOTO", "不存在该图片"),
    EXIST_PRODUCT("306", "CENTER_EXIST_PRODUCT", "存在产品，不能删除"),
    UPDATE_FAIL("307", "CENTER_UPDATE_FAIL", "供应商无此酒店"),
    HOTEL_NULL("308", "CENTER_HOTEL_NULL", "更新失败"),
    CHECK_DATE_ERROR("309", "CENTER_CHECK_DATE_ERROR", "入离时间异常"),
    PRICE_BETWEEN_ERROR("310", "CENTER_PRICE_BETWEEN_ERROR", "价格区间异常"),
    NOT_EXIST_COUNTRY_CODE("311", "NOT_EXIST_COUNTRY_CODE", "不存在国家编码"),

    /***********产品异常 以4开头***********/
    PRODUCTONSALEWITHNODELETE("401", "CENTER_PRODUCTONSALE", "产品处于销售状态，不能删除"),
    EXISTS_MEITUAN_PRODUCT_SALE_ON("402", "CENTER_EXISTS_MEITUAN_PRODUCT_SALE", "因为这些产品所在房型的早餐类型已有产品在上架。（美团同一个房型下每种早餐类型只支持上架一个产品）"),

    /***********OTA对接异常 以5开头***********/
    INTERFACENOTEXISTS("501", "CENTER_INTERFACENOTEXISTS", "接口不存在，调用失败"),


    /**************供应商相关**************************/
    FULL_ROOM("001", "CENTER_FULL_ROOM", "满房"),
    PRICE_NEED_QUERY("002", "PRICE_NEED_QUERY", "价格待查"),
    DISSATISFY_QUOTA("003", "DISSATISFY_QUOTA", "配额不满足"),
    INVALID_CHANNELCODE("004", "INVALID_CHANNELCODE", "渠道不存在"),
    INVALID_SALESTATE("006", "INVALID_SALESTATE", "无效售卖状态"),
    DISSATISFY_BOOKINGCLAUSE("007", "DISSATISFY_BOOKINGCLAUSE", "不满足预订条款"),
    DISSATISFY_OCCUPANCYCLAUSE("008", "DISSATISFY_OCCUPANCYCLAUSE", "不满足入住条款"),
    DISSATISFY_BOOKROOMSCLAUSE("009", "DISSATISFY_BOOKROOMSCLAUSE", "不满足预订间数条款"),
    MERCHANT_NOT_CONFIGURED("033", "MERCHANT_NOT_CONFIGURED", "该商家未配置"),
    SUPPLY_INTERFACE_EXCEPTION("081", "SUPPLY_INTERFACE_EXCEPTION", "供应商接口异常"),
    SUPPLY_RETURN_RESULT_EMPTY("082", "SUPPLY_RETURN_RESULT_EMPTY", "供应商返回结果为空"),
    SUPPLY_RETURN_RESULT_FORMAT_ERROR("083", "SUPPLY_RETURN_RESULT_FORMAT_ERROR", "供应商返回结果格式不正确"),
    SUPPLY_RETURN_RESULT_FAIL("084", "SUPPLY_RETURN_RESULT_FAIL", "供应商返回失败结果"),
    SUPPLY_NO_HAS_PRODUCT("085", "CENTER_SUPPLY_NO_HAS_PRODUCT", "供应商产品不存在"),
    CANCEL_SUCCESS("063", "CANCEL_ORDER_SUCCESS", "成功撤单"),
    CANCEL_FAILED("064", "CANCEL_ORDER_FAILED", "撤单失败"),
    DISSATISFY_CANCELCLAUSE("057", "DISSATISFY_CANCELCLAUSE", "不满足取消条款"),
    NOMATCH_SUPPLYANDMECHANT("049", "NOMATCH_SUPPLYANDMECHANT", "缺少商家与供应商配置"),
    PRICPLANMAPPING_ISEMPTY("031", "CENTER_PRICPLANMAPPING_ISEMPTY", "价格计划未映射"),
    HOTELMAPPING_ISEMPTY("029", "HOTEL_NOT_MAPPING", "酒店未映射"),
    ROOMTYPEMAPPING_ISEMPTY("030", "ROOM_NOT_MAPPING", "房型未映射"),
    SUPPLYORDER_NOT_EXIST("034", "SUPPLYORDER_NOT_EXIST", "供货单不存在"),
    SUPPLY_NOT_EXIST("035", "CENTER_SUPPLY_NOT_EXIST", "供应商不存在"),
    CACHE_DATA_IS_ERROR("036", "CENTER_CACHE_DATA_IS_ERROR", "当前数据必须为落地数据"),
    AGENT_SUPPLY_LABEL_IS_ALREADY("037", "CENTER_AGENT_SUPPLY_LABEL_IS_ALREADY", "该客户-供应商已存在,请勿重复添加"),
    SUPPLY_CHECK_NOT_EXIST("038", "CENTER_SUPPLY_CHECK_NOT_EXIST", "供应商缓存不存在"),
    SAME_SUPPLIER_SEND_ORDER_ERROR("039", "CENTER_SAME_SUPPLIER_SEND_ORDER_ERROR", "只有供应商相同才能发单!"),
    SUPPLY_RETURN_CANNOT_BOOK("086", "SUPPLY_RETURN_CANNOT_BOOK", "供应商返回不可订"),
    SUPPLIER_HOTEL_NON_VCC_AUTO_BILL_CONFIG_EXIST("087", "SUPPLIER_HOTEL_NON_VCC_AUTO_BILL_CONFIG_EXIST", "该供应商酒店非VCC自动出账配置已存在！"),

    /**** 分销商调用 以8开头****/
    LOSE_APPID_OR_APPSECRET("800", "CENTER_LOSE_APPID_OR_APPSECRET", "缺少appId或者appSecret"),
    AUTH_ERROR("801", "CENTER_AUTH_ERROR", "没有权限"),
    PARTNER_CODER_IS_ALREADY("802", "CENTER_PARTNER_CODER_IS_ALREADY", "该分销商编码已存在"),
    EXCHANGE_RATE_IS_NOT_ALREADT("803", "CENTER_EXCHANGE_RATE_IS_NOT_ALREADT", "兑换的汇率不存在"),
    AGENT_INFO_NOT_EXIST("804", "CENTER_AGENT_INFO_NOT_EXIST", "客户信息不存在"),
    PARTNER_CONFIG_NOT_EXIST("805", "PARTNER_CONFIG_NOT_EXIST", "合作商配置不存在"),
    AGENT_ORDER_CONFIRMATION_LETTER_CONFIG_NOT_EXIST("806", "AGENT_ORDER_CONFIRMATION_LETTER_CONFIG_NOT_EXIST", "客户订单确认函不存在"),

    /*** 发票相关***/
    INVOICE_COMPANY_NAME_IS_ALREADY("1001", "CENTER_INVOICE_COMPANY_NAME_IS_ALREADY", "公司名称已经存在"),


    /*** 银行卡相关***/
    BANK_ACCOUNT_NUMBER_IS_ALREADY("2001", "CENTER_BANK_ACCOUNT_NUMBER_IS_ALREADY", "银行卡账号已存在"),
    BANK_IS_NOT_ALREADY("2002", "CENTER_BANK_IS_NOT_ALREADY", "银行卡不存在"),
    BANK_IS_NOT_DISABLE("2003", "CENTER_BANK_IS_NOT_DISABLE", "银行卡非禁用"),
    BANK_IS_ANY_BANK_LOG("2004", "CENTER_BANK_IS_ANY_BANK_LOG", "银行卡存在流水"),


    /**** 其他异常用11000开头****/
    CHANNEL_IS_NOT_EXIST("11001", "CENTER_CHANNEL_IS_NOT_EXIST", "此渠道暂未开通！"),
    BALANCE_IS_NOT_ENOUGH("11002", "CENTER_BALANCE_IS_NOT_ENOUGH", "信用额度不足,无法下单！"),
    CREATE_ORDER_IS_ERROR("11003", "CENTER_CREATE_ORDER_IS_ERROR", "创建订单失败"),
    PRODUCT_IS_NOT_EXIST("11004", "CENTER_PRODUCT_IS_NOT_EXIST", "产品不存在,无法下单！"),
    SELF_SEND_ORDER_IS_ERROR("11005", "CENTER_SELF_SEND_ORDER_IS_ERROR", "自动发单功能异常"),
    AGENT_IS_INVALID("11006", "CENTER_AGENT_IS_INVALID", "客户无效"),
    SUPPLIER_USING_OFFICIAL_SEAL("11007", "CENTER_SUPPLIER_USING_OFFICIAL_SEAL", "有供应商使用该公章"),
    AGENT_NAME_IS_ALREADY("11008", "CENTER_AGENT_NAME_IS_ALREADY", "客户名称已存在"),
    ORDER_IS_CANCEL("11009", "CENTER_ORDER_IS_ALREADY_CANCEL", "订单已取消"),
    PARTNER_IS_ALREADY("11010", "CENTER_PARTNER_IS_ALREADY", "当前资源类型的合作商已存在"),
    PASSWORD_IS_NOT_EMPTY("11011", "EMPTY_PARAM_PASSWORD", "密码不能为空！"),
    PASSWORD_IS_ERROR("11012", "CENTER_PASSWORD_IS_ERROR", "密码错误！"),
    DIRECTION_IS_ALREADY("11013", "CENTER_DIRECTION_IS_ALREADY", "字段名称已存在！"),
    PARAMETER_IS_ALREADY("11014", "CENTER_PARAMETER_IS_ALREADY", "参数项名称不能重复！"),
    PLEASE_INPUT_CORRECT_PHONE("11015", "CENTER_PLEASE_INPUT_CORRECT_PHONE", "请输入正确的手机号！"),
    OLD_PHONE_IS_SAME("11016", "CENTER_OLD_PHONE_IS_SAME", "不能与旧手机号一致！"),
    RESET_PASSWORD_IS_ERROR("11017", "CENTER_RESET_PASSWORD_IS_ERROR", "重置密码失败！"),
    COIN_IS_ALREADY("11018", "CENTER_COIN_IS_ALREADY", "此币种已添加"),
    SUPPLIER_NAME_IS_ALREADY("11019", "CENTER_SUPPLIER_NAME_IS_ALREADY", "供应商名称已存在"),
    SUPPLIER_ID_IS_ERROR("11020", "CENTER_SUPPLIER_ID_IS_ERROR", "供应商id异常"),
    UPDATE_ERROR("11021", "CENTER_UPDATE_ERROR", "修改失败"),
    INIT_PRICE_ERROR("11022", "CENTER_INIT_PRICE_ERROR", "初始化价格失败，系统异常"),
    INIT_SALE_PRICE_ERROR("11022", "CENTER_INIT_SALE_PRICE_ERROR", "初始化售价信息失败，系统异常"),
    INIT_UP_DOWN_MSG_ERROR("11023", "CENTER_INIT_UP_DOWN_MSG_ERROR", "初始化上下架信息失败，系统异常"),
    INIT_TERM_ERROR("11024", "CENTER_INIT_TERM_ERROR", "初始化条款失败，系统异常"),
    INIT_PRODUCT_SUPPER_ERROR("11025", "CENTER_INIT_PRODUCT_SUPPER_ERROR", "初始化产品商家关系失败，系统异常"),
    INIT_PRODUCT_RATE_ERROR("11026", "CENTER_INIT_PRODUCT_RATE_ERROR", "初始化产品汇率失败，系统异常"),
    INIT_COIN_ERROR("11027", "CENTER_INIT_COIN_ERROR", "初始化币种失败，系统异常"),
    INIT_MAPPING_ERROR("11028", "CENTER_INIT_MAPPING_ERROR", "初始化映射失败，系统异常"),
    INIT_SUPPER_CHANNEL_MAPPING_ERROR("11029", "CENTER_INIT_SUPPER_CHANNEL_MAPPING_ERROR", "初始化商家渠道关系映射失败，系统异常"),
    SUPPER_BILL_CANCEL("11030", "CENTER_SUPPER_BILL_CANCEL", "该供应商有账单正在取消，请稍后再试"),
    SUPPER_BILL_OUTGOING("11031", "CENTER_SUPPER_BILL_OUTGOING", "该供应商有账单正在出账，请稍后再试"),
    SUPPER_BILL_OUTGOING_STATUS("11032", "CENTER_SUPPER_BILL_OUTGOING_STATUS", "该供应商有出账中的状态"),
    UPDATE_MANAGE_MESSAGE_ERROR("11033", "CENTER_UPDATE_MANAGE_MESSAGE_ERROR", "修改管理员信息失败"),
    ACCOUNT_OR_TEL_ALREADY("11034", "CENTER_ACCOUNT_OR_TEL_ALREADY", "账号或手机号已存在"),
    MANAGE_ALREADY("11035", "CENTER_MANAGE_ALREADY", "管理员账号已存在"),
    AGENT_AVAILABLE_IS_ALREADY("11036", "CENTER_AGENT_AVAILABLE_IS_ALREADY", "该客户下已存在该酒店的黑名单"),
    DUPLICATE_CLICK_ERROR("11037", "CENTER_DUPLICATE_CLICK_ERROR", "请勿重复点击"),
    FILE_NOT_EXISTS("11038", "CENTER_FILE_NOT_EXISTS", "文件不存在"),
    WITHDRAWAL_QUOTA_FAIL("11039", "CENTER_WITHDRAWAL_QUOTA_FAIL", "退额失败，请稍后重试"),
    HOTEL_DUPLICATION("11040", "CENTER_HOTEL_DUPLICATION", "酒店重复"),
    NOTIFY_DISTRIBUTOR_FAIL("11041", "CENTER_NOTIFY_DISTRIBUTOR_FAIL", "通知分销渠道客户失败"),
    SUPPLYORDER_CONFIRMED_NOT_UPDATE_SETTLEMENTTYPE("11042", "CENTER_SUPPLYORDER_CONFIRMED_NOT_UPDATE_SETTLEMENTTYPE", "供货单已确认不能修改结算方式"),
    CENTER_CONFIRMED_NO_MORE_ORDERS_CAN_BE_ISSUED_TO_SUPPLIERS("11043", "CENTER_CONFIRMED_NO_MORE_ORDERS_CAN_BE_ISSUED_TO_SUPPLIERS", "已经取消或者完成的订单，不能再发单给供应商"),
    CANCELLATION_MODIFICATION_REQUEST("11044", "CENTER_CANCELLATION_MODIFICATION_REQUEST", "有取消修改申请"),
    AUTOMATIC_ISSUE_ORDER_STATUS_AND_PAYMENT_STATUS_ARE_NOT_SATISFIED_ISSUE_FAILURE("11045", "CENTER_AUTOMATIC_ISSUE_ORDER_STATUS_AND_PAYMENT_STATUS_ARE_NOT_SATISFIED_ISSUE_FAILURE", "自动发单，订单状态和支付状态不满足，发单失败!"),
    ORDER_CANCELED("11045", "ORDER_CANCELED", "订单已经取消"),
    AUTOMATICALLY_CANCEL_A_SINGLE_FUNCTION_EXCEPTION("11046", "CENTER_AUTOMATICALLY_CANCEL_A_SINGLE_FUNCTION_EXCEPTION", "自动发取消单功能异常"),
    RESERVATIONS_CAN_ONLY_BE_SENT_ONCE("11047", "CENTER_RESERVATIONS_CAN_ONLY_BE_SENT_ONCE", "预订单只能发送一次"),
    RESERVATION_HAS_BEEN_ISSUED("11048", "CENTER_RESERVATION_HAS_BEEN_ISSUED", "已发过预订单，供应商可能已生成订单，请先联系供应商确认是否发单成功，当供应商已生成订单将供应商单号填写到我方【供应商单号】；当确认供应商未生成订单，可点击重新发送"),
    UNABLE_TO_INITIATE_A_NEW_APPLICATION("11049", "CENTER_UNABLE_TO_INITIATE_A_NEW_APPLICATION", "有尚未处理的取消（修改）申请，无法发起新申请"),
    SUPPLYORDER_NOT_CONFIRMED_NO_MODIFICATION_ORDERS_CAN_BE_ISSUED("11050", "CENTER_SUPPLYORDER_NOT_CONFIRMED_NO_MODIFICATION_ORDERS_CAN_BE_ISSUED", "供货单不是已确认，不能发修改单"),
    NO_RESERVATION_HAS_BEEN_ISSUED_CANNOT_OPERATE("11051", "CENTER_NO_RESERVATION_HAS_BEEN_ISSUED_CANNOT_OPERATE", "没发过预订单，不能操作"),
    SUPPLYORDER_CONFIRMED_RESERVATIONS_CANNOT_BE_REISSUED("11052", "CENTER_SUPPLYORDER_CONFIRMED_RESERVATIONS_CANNOT_BE_REISSUED", "供货单已确认，不能重发预订单"),
    SUPPLYORDER_CANCEL_CANNOT_OPERATE("11053", "CENTER_SUPPLYORDER_CANCEL_CANNOT_OPERATE", "供货单已取消，不能操作"),
    ORDER_CONFIRMED_NOT_UPDATE_SETTLEMENTTYPE("11054", "CENTER_ORDER_CONFIRMED_NOT_UPDATE_SETTLEMENTTYPE", "订单已确认不能修改结算方式"),
    CANNOT_OPERATE("11055", "CENTER_CANNOT_OPERATE", "不能操作"),
    CANCELABLE_TIME_HAS_EXPIRED("11056", "CENTER_CANCELABLE_TIME_HAS_EXPIRED", "已过可取消时间"),
    ORDER_IS_BEING_PROCESSED("11057", "CENTER_ORDER_IS_BEING_PROCESSED", "此订单正在处理"),
    NOT_CANCEL_STATUS_CANCELLATION_FEE_CANNOT_BE_MODIFIED("11058", "CENTER_NOT_CANCEL_STATUS_CANCELLATION_FEE_CANNOT_BE_MODIFIED", "非取消状态，无法修改退订费"),
    FAILED_TO_QUERY_THE_PRODUCT_UNABLE_TO_ISSUE_AN_ORDER("11059", "CENTER_FAILED_TO_QUERY_THE_PRODUCT_UNABLE_TO_ISSUE_AN_ORDER", "查询产品失败，无法发单!"),
    SUPPLIER_HAS_BEEN_WHITELISTED_AND_CANNOT_BE_BLACKLISTED("11060", "CENTER_SUPPLIER_HAS_BEEN_WHITELISTED_AND_CANNOT_BE_BLACKLISTED", "当前供应商已设置白名单，无法添加黑名单"),
    SUPPLIER_HAS_BEEN_BLACKLISTED_AND_THE_WHITELIST_CANNOT_BE_ADDED("11061", "CENTER_SUPPLIER_HAS_BEEN_BLACKLISTED_AND_THE_WHITELIST_CANNOT_BE_ADDED", "当前供应商已设置黑名单，无法添加白名单"),
    OPERATE_FAIL("11062", "CENTER_OPERATE_FAIL", "操作失败"),
    VENDOR_TYPE_NAME_ALREADY_EXISTS("11063", "CENTER_VENDOR_TYPE_NAME_ALREADY_EXISTS", "供应商类型名称已经存在"),
    CUSTOMER_HAS_A_BILL_IN_PROGRESS("11064", "CENTER_CUSTOMER_HAS_A_BILL_IN_PROGRESS", "该客户有账单正在出账，请稍后再试"),
    CUSTOMER_HAS_A_BILL_BEING_CANCELLED("11065", "CENTER_CUSTOMER_HAS_A_BILL_BEING_CANCELLED", "该客户有账单正在取消，请稍后再试"),
    BILL_HAS_BEEN_CANCELLED_NO_DETAILS_CAN_BE_ADDED("11066", "CENTER_BILL_HAS_BEEN_CANCELLED_NO_DETAILS_CAN_BE_ADDED", "账单已取消不能添加明细"),
    SUPPLIER_HAS_AN_OUTOFACCOUNT_STATUS("11067", "CENTER_SUPPLIER_HAS_AN_OUTOFACCOUNT_STATUS", "该供应商有出账中的状态"),
    SMS_TEMPLATE_IS_EMPTY("11068", "CENTER_SMS_TEMPLATE_IS_EMPTY", "短信模板为空"),
    SUPPLY_ORDER_IS_PENDING_CONFIRMATION_PLEASE_CANCEL_FIRST("11069", "CENTER_SUPPLY_ORDER_IS_PENDING_CONFIRMATION_PLEASE_CANCEL_FIRST", "供货单为待确认或已确认、已完成，请先取消！"),
    BILL_AMOUNT_HAS_CHANGED_PLEASE_UPDATE_THE_BILL_FIRST("11070", "CENTER_BILL_AMOUNT_HAS_CHANGED_PLEASE_UPDATE_THE_BILL_FIRST", "账单金额发生变化，请先更新账单"),
    BILLS_HAVE_BEEN_RECEIVED_OR_PAID_NO_CANCEL("11071", "CENTER_BILLS_HAVE_BEEN_RECEIVED_OR_PAID_NO_CANCEL", "该账单已产生实收或实付，或待确认的收款或付款，不能取消"),
    ENTRY_AND_DEPARTURE_TIME_CANNOT_BE_THE_SAME("11072", "CENTER_ENTRY_AND_DEPARTURE_TIME_CANNOT_BE_THE_SAME", "入离时间不能相同"),
    CONFIRMED_NON_STATEMENT_BILLS_CANNOT_BE_UNLOCKED("11073", "CENTER_CONFIRMED_NON_STATEMENT_BILLS_CANNOT_BE_UNLOCKED", "已确认的非单结账单不能解锁"),
    BILLING_WORK_ORDER_PROCESSING_TASK_IS_IN_PROGRESS_AND_CANNOT_BE_UNLOCKED("11074", "CENTER_BILLING_WORK_ORDER_PROCESSING_TASK_IS_IN_PROGRESS_AND_CANNOT_BE_UNLOCKED", "记账工单处理任务正在进行，不能解锁"),
    QUERY_FAIL("11075", "CENTER_QUERY_FAIL", "查询失败"),
    TASK_NOT_EXIST("11076", "TASK_NOT_EXIST", "任务不存在"),
    PUSH_FAIL("11078", "PUSH_FAIL", "推送失败"),
    CANCEL_FAIL("11079", "CANCEL_FAIL", "取消失败"),
    PRODUCT_TYPE_UNLIKE("11080", "PAY_METHOD_INCONFORMITY", "产品类型不一致"),
    SUPPLY_ORDER_CURRENCY_UNLIKE("11081", "CENTER_SUPPLY_ORDER_CURRENCY_UNLIKE", "需与原供货单币种一致"),
    THIS_LANGUAGE_NOT_EXIST_HOTEL_INFO("11082", "CENTER_THIS_LANGUAGE_NOT_EXIST_HOTEL_INFO", "当前语言酒店信息不存在"),
    HOTEL_LABEL_NAME_EXIST("11083", "CENTER_HOTEL_LABEL_NAME_EXIST", "酒店标签名称已存在"),
    THE_LABEL_IS_IN_USE("11084", "CENTER_THE_LABEL_IS_IN_USE", "该标签被使用中，不支持删除"),
    CUSTOMER_HOTEL_SORT_ALREADY_EXISTS("11085", "CENTER_CUSTOMER_HOTEL_SORT_ALREADY_EXISTS", "客户酒店排序已存在"),
    CUSTOMER_HAS_ADDED_A_COLLECTION_ACCOUNT("11086", "CENTER_CUSTOMER_HAS_ADDED_A_COLLECTION_ACCOUNT", "客户已添加收款账户，不能重复添加"),
    TAX_RULE_NAME_EXISTS("11087", "TAX_RULE_NAME_EXISTS", "税费名称已存在"),
    TAX_SCOPE_OF_APPLICATION_TYPE_ERROR("11088", "TAX_SCOPE_OF_APPLICATION_TYPE_ERROR", "税费适用范围不在指定范围内"),
    TAX_STANDARD_ERROR("11089", "TAX_STANDARD_ERROR", "税费标准不在指定范围内"),
    TAX_TYPE_ERROR("11090", "TAX_TYPE_ERROR", "税费类型不在指定范围内"),
    TAX_INCREASE_TYPE_ERROR("11091", "TAX_INCREASE_TYPE_ERROR", "税费/税率不在指定范围内"),
    TAX_RULE_IN_USE("11092", "TAX_RULE_IN_USE", "税费规则使用中,无法删除"),
    BILL_NOT_EXIST_ORDER("11093", "BILL_NOT_EXIST_ORDER", "此账单不存在该订单"),
    REFUSE_ORDERS_CAN_BE_ISSUED_TO_SUPPLIERS("11094", "REFUSE_ORDERS_CAN_BE_ISSUED_TO_SUPPLIERS", "已经完成的订单，不能再发单给供应商"),
    CENTER_CONFIRMED_NO_MORE_SUPPLY_ORDERS_CAN_BE_ISSUED_TO_SUPPLIERS("11095", "CENTER_CONFIRMED_NO_MORE_SUPPLY_ORDERS_CAN_BE_ISSUED_TO_SUPPLIERS", "已经取消或者完成的供货单，不能再发单给供应商"),
    THE_FINANCING_ALLOCATION_ALREADY_EXISTS("11096", "THE_FINANCING_ALLOCATION_ALREADY_EXISTS", "融资配置已经存在"),
    SUPPLIER_DUPLICATION("11097", "SUPPLIER_DUPLICATION", "供应商重复"),
    PUSH_TYPE_NOT_EXIST("11098", "PUSH_TYPE_NOT_EXIST", "推送类型不存在"),
    TASK_STATUS_CAN_NOT_PUSH("11099", "TASK_STATUS_CAN_NOT_PUSH", "推送中、已推送状态不能推送"),
    AGENT_FINANCING_CONFIG_IS_INVALID("11100", "AGENT_FINANCING_CONFIG_IS_INVALID", "客户融资配置无效"),
    ONLY_THE_PUSHED_DATA_CAN_BE_MODIFIED("11101", "ONLY_THE_PUSHED_DATA_CAN_BE_MODIFIED", "已推送的数据才能修改"),
    AGENT_FINANCING_CONFIG_NOT_EXIST("11102", "AGENT_FINANCING_CONFIG_NOT_EXIST", "客户融资配置不存在"),
    GENERATE_VIRTUAL_CARD_FAIL("11103", "GENERATE_VIRTUAL_CARD_FAIL", "生成VCC虚拟卡失败"),
    QUERY_PAY_SUBJECT_FAIL("11104", "QUERY_PAY_SUBJECT_FAIL", "获取付款主体失败"),
    THE_PAYER_DOES_NOT_EXIST("11105", "THE_PAYER_DOES_NOT_EXIST", "付款主体不存在"),
    QUERY_PAY_ACCOUNT_INFO_FAIL("11106", "QUERY_PAY_ACCOUNT_INFO_FAIL", "获取付款账号失败"),
    PAY_ACCOUNT_INFO_NOT_EXIST("11107", "PAY_ACCOUNT_INFO_NOT_EXIST", "付款账号配置不存在"),
    VCC_PAYMENT_ADVICE_FAILED("11108", "VCC_PAYMENT_ADVICE_FAILED", "VCC支付通知失败"),
    VCC_PAY_ACCOUNT_INFO_NOT_EXIST("11109", "VCC_PAY_ACCOUNT_INFO_NOT_EXIST", "VCC付款配置不存在"),
    CITY_ALREADY_EXISTS("11110", "CITY_ALREADY_EXISTS", "城市配置已存在"),
    IMPORT_MAX_200("11111", "IMPORT_MAX_200", "最多导入200条"),
    THE_ENTERED_NUMBER_OF_CITIES_IS_INVALID_PLEASE_INPUT_AGAIN("11112", "THE_ENTERED_NUMBER_OF_CITIES_IS_INVALID_PLEASE_INPUT_AGAIN", "输入的城市可跑数量城市有误，请重新输入"),
    BILL_NOT_EXIST_SUPPLY("11112", "BILL_NOT_EXIST_SUPPLY", "此账单不存在该供货单"),
    BILL_IS_SETTLEMENT_NOT_UPDATE_PAY_AMOUNT("11113", "BILL_IS_SETTLEMENT_NOT_UPDATE_PAY_AMOUNT", "账单已结算，不能修改供货单本次要付金额"),
    PAY_AT_HOTEL_CURRENCY_UNLIKE_ORDER_CURRENCY("11114", "PAY_AT_HOTEL_CURRENCY_UNLIKE_ORDER_CURRENCY", "到店付币种与订单币种不一致"),
    PAY_AT_HOTEL_CURRENCY_IS_NULL("11115", "PAY_AT_HOTEL_CURRENCY_IS_NULL", "到店付币种为空"),
    CREATE_STATEMENT_FAIL("11116", "CREATE_STATEMENT_FAIL", "创建账单失败"),

    //自助结算配置
    SETTLE_SUPPLIER_HOTEL_IS_EXISTS("12001", "CENTER_SETTLE_SUPPLIER_HOTEL_IS_EXISTS", "供应商和酒店已配置，请重新输入!"),
    SETTLE_CUSTOMER_HAS_BOUND("12002", "CENTER_SETTLE_CUSTOMER_HAS_BOUND", "该客户已被绑定，请重新输入!"),
    SETTLE_CUSTOMER_NOT_EXIST("12003", "CENTER_SETTLE_CUSTOMER_NOT_EXIST", "提交的酒店下不存在该客户，请重新输入!"),
    SETTLE_HOTEL_CONFIG_NOT_EXIST("12004", "CENTER_SETTLE_HOTEL_CONFIG_NOT_EXIST", "自助结算配置不存在!"),
    SETTLE_SUPPLY_ORDER_FINANCE_IS_NULL("12005", "CENTER_SETTLE_SUPPLY_ORDER_FINANCE_IS_NULL", "调用供应商财务接口为空!"),
    SETTLE_DDT_ORDER_DETAILS_IS_NULL("12006", "CENTER_SETTLE_DDT_ORDER_DETAILS_IS_NULL", "订单通订单明细为空!"),
    SETTLE_DDT_ORDER_DETAILS_IS_ERROR("12007", "CENTER_SETTLE_DDT_ORDER_DETAILS_IS_ERROR", "查询订单通订单明细异常!"),
    SETTLE_SUPPLIER_CODE_IS_NULL("12008", "EMPTY_PARAM_SUPPLYCODE", "供应商编码不能为空!"),
    SETTLE_WRITE_SETTLE_TASK_REDIS_ERROR("12009", "CENTER_SETTLE_WRITE_SETTLE_TASK_REDIS_ERROR", "写入结算单任务队列异常!"),
    SETTLE_UNABLE_TO_DELETE_WITH_PENDING_PAYMENTS("12010", "CENTER_SETTLE_UNABLE_TO_DELETE_WITH_PENDING_PAYMENTS", "该酒店目前还有未完成的付款任务，无法删除自助结算酒店配置!"),
    SETTLE_DELETE_HOTEL_CONFIG_ERROR("12011", "CENTER_SETTLE_DELETE_HOTEL_CONFIG_ERROR", "删除自助结算配置异常!"),
    SETTLE_ADD_HOTEL_CONFIG_ERROR("12012", "CENTER_SETTLE_ADD_HOTEL_CONFIG_ERROR", "新增保存自助结算配置异常!"),
    SETTLE_EDIT_HOTEL_CONFIG_ERROR("12013", "CENTER_SETTLE_EDIT_HOTEL_CONFIG_ERROR", "编辑保存自助结算配置异常!"),
    SETTLE_QUERY_DETAILS_HOTEL_CONFIG_ERROR("12014", "CENTER_SETTLE_QUERY_DETAILS_HOTEL_CONFIG_ERROR", "查询自助结算配置详情异常!"),

    SETTLE_QUERY_PAGE_HOTEL_CONFIG_ERROR("12015", "CENTER_SETTLE_QUERY_PAGE_HOTEL_CONFIG_ERROR", "查询自助结算配置列表异常!"),
    SETTLE_QUERY_AVAILABLE_BALANCE_ERROR("12016", "CENTER_SETTLE_QUERY_AVAILABLE_BALANCE_ERROR", "查询余额异常!"),
    SETTLE_SEND_REQUEST_IREVE_FAIL("12017", "CENTER_SETTLE_SEND_REQUEST_IREVE_FAIL", "调用ireve接口失败!"),
    SETTLE_TIME_TYPE_NOT_EXIST("12018", "SETTLE_TIME_TYPE_NOT_EXIST", "结算时间类型不存在"),
    IS_CHECK_DETAIL_NOT_EXIST("12019", "IS_CHECK_DETAIL_NOT_EXIST", "是否核对入住明细不存在"),
    IS_SYNC_IREVE_NOT_EXIST("12020", "IS_SYNC_IREVE_NOT_EXIST", "是否同步订单联系人信息到IREVE不存在"),
    IS_REAL_TIME_NOT_EXIST("12021", "IS_REAL_TIME_NOT_EXIST", "是否立即到账不存在"),
    PASSAGE_TYPE_NOT_EXIST("12022", "PASSAGE_TYPE_NOT_EXIST", "付款类型不存在"),
    INVOICE_SUB_TYPE_NOT_EXIST("12023", "INVOICE_SUB_TYPE_NOT_EXIST", "开票主体类型不存在"),
    INVOICE_TYPE_NOT_EXIST("12024", "INVOICE_TYPE_NOT_EXIST", "发票类型不存在"),
    MERCHANT_CODE_NOT_EXIST("12025", "MERCHANT_CODE_NOT_EXIST", "商家编码不存在"),
    SUPPLIER_CODE_NOT_EXIST("12026", "SUPPLIER_CODE_NOT_EXIST", "供应商编码不存在"),
    SUPPLIER_NAME_NOT_EXIST("12027", "SUPPLIER_NAME_NOT_EXIST", "供应商名称不存在"),
    PAYER_ACCOUNT_ID_NOT_EXIST("12028", "PAYER_ACCOUNT_ID_NOT_EXIST", "付款主体id不存在"),
    INVOICE_SUB_ID_NOT_EXIST("12029", "INVOICE_SUB_ID_NOT_EXIST", "开票主体id不存在"),
    HOTEL_ID_NOT_EXIST("12030", "HOTEL_ID_NOT_EXIST", "酒店ID不存在"),
    HOTEL_NAME_NOT_EXIST("12031", "HOTEL_NAME_NOT_EXIST", "酒店名称不存在"),
    CITY_NAME_NOT_EXIST("12027", "CITY_NAME_NOT_EXIST", "城市名称不存在"),

    /*** 财务相关***/
    SETTLE_WORK_ORDER_NOT_EXISTS("13001", "CENTER_SETTLE_WORK_ORDER_NOT_EXISTS", "重试失败，任务不存在!"),
    SUPPLY_REWARD_IS_LOCK("13002", "CENTER_SUPPLY_REWARD_IS_LOCK", "供货单奖励已锁定，请先解锁!"),
    SUPPLY_REBATE_IS_LOCK("13003", "CENTER_SUPPLY_REBATE_IS_LOCK", "供货单返佣已锁定，请先解锁!"),

    BANK_LOG_TYPE_ERROR("13004", "CENTER_BANK_LOG_TYPE_ERROR", "流水类型错误!"),
    BANK_ID_ERROR("13005", "CENTER_BANK_ID_ERROR", "银行卡id异常!"),
    BALANCE_CHANGE_ERROR("13006", "CENTER_BALANCE_CHANGE_ERROR", "余额变更失败!"),
    CANCEL_ORDER_AMOUNT_IS_ERROR("13007", "CENTER_CANCEL_GT_ORDER_AMOUNT_IS_ERROR", "退订费不能大于订单应收!"),
    CANCEL_SUPPLIER_ORDER_AMOUNT_IS_ERROR("13008", "CENTER_CANCEL_LT_ORDER_AMOUNT_IS_ERROR", "退订费不能大于供货单应付!"),
    EXCEL_CONTENT_FORMAT_ERROR("13009", "EXCEL_CONTENT_FORMAT_ERROR", "excel内容格式错误!"),
    FILE_FORMAT_ERROR("13010", "EXCEL_FILE_FORMAT_ERROR", "文件格式不正确!"),
    IMPORT_MAX_5000("13011", "IMPORT_MAX_5000", "最多导入5000条"),
    LABEL_TYPE_DOES_NOT_EXIST("13012", "LABEL_TYPE_DOES_NOT_EXIST", "标签类型不存在"),
    IMPORT_FILE_FORMAT_ERROR("13013", "IMPORT_FILE_FORMAT_ERROR", "导入文件，文件模版不正确"),
    DIFFERENT_RESOURCE_TYPES("13014", "DIFFERENT_RESOURCE_TYPES", "资源类型不同"),
    SUPPLY_IS_INVALID("13015", "SUPPLY_IS_INVALID", "供应商无效"),
    SUPPLY_FILE_CONTENT_IS_EMPTY("13016", "SUPPLY_FILE_CONTENT_IS_EMPTY", "文件内容为空"),

    /*** 发票相关***/
    INVOICE_STATE_BOB_ERROR("15001", "INVOICE_STATE_BOB_ERROR", "票单的状态为已平账或已坏账!"),
    CRSTARTDT_GT_ENDDT_ERROR("15002", "CRSTARTDT_GT_ENDDT_ERROR", "创建开始日期大于创建结束日期"),
    CONFIRMDT_GT_ENDDT_ERROR("15003", "CONFIRMDT_GT_ENDDT_ERROR", "确认开始日期大于确认结束日期"),
    IREVE_ORDER_EXISTS("15005", "IREVE_ORDER_EXISTS", "ireve订单重复"),
    ORDER_PAY_TYPE_NOT_SUPPORT("15006", "ORDER_PAY_TYPE_NOT_SUPPORT", "不支持该订单的支付类型"),
    ORDER_TONG_NOTFOUND_TIANGONG_ORDER("15007", "ORDER_TONG_NOTFOUND_TIANGONG_ORDER", "订单通找不到对应的天宫订单"),
    SYNC_DEL_DDT_SUPPLYORDER_FAILED("15007", "SYNC_DEL_DDT_SUPPLYORDER_FAILED", "同步删除订单通供货单失败"),
    SYNC_ADD_DDT_SUPPLYORDER_FAILED("15007", "SYNC_ADD_DDT_SUPPLYORDER_FAILED", "订单通同步添加/修改供货单失败"),
    SYNC_ADD_DDT_INVOICE_FAILED("15007", "SYNC_ADD_DDT_INVOICE_FAILED", "订单通同步发票失败"),
    INVOICEBILL_NOT_EXIST("15008", "INVOICEBILL_NOT_EXIST", "未查询到对应的票单"),
    CURRAMT_COMPARETO_UNBDINDAMT("15009", "CURRAMT_COMPARETO_UNBDINDAMT", "修改后增加金额不允许超过未绑定金额"),
    DUBBLE_CLICK_ERROR("15010", "DUBBLE_CLICK_ERROR", "请勿重复操作"),
    REMOVE_ORIGINAL_TICKET("15011", "REMOVE_ORIGINAL_TICKET", "建议先从原票单移出后,再手动操作加入票单"),
    INVOICEAMT_ZERO("15012", "INVOICEAMT_ZERO", "可开票金额为0"),
    AMT_ADJUSTMENT("15013", "AMT_ADJUSTMENT", "双方可开票金额不一致"),
    SUPPLY_ORDER_TO_BE_CONFIRMED("15014", "SUPPLY_ORDER_TO_BE_CONFIRMED", "供货单状态为待确认"),
    GET_BAIDUORC_TOKEN("15015", "GET_BAIDUORC_TOKEN", "获取百度token失败"),
    MAX_TWENTY_FILE_FAILED("15016", "MAX_TWENTY_FILE_FAILED", "一次最多识别20个文件"),
    ASYN_FILE_UPLOAD_FAILED("15017", "ASYN_FILE_UPLOAD_FAILED", "异步上传文件失败"),
    FILE_PATH_ISEMPTY("15018", "FILE_PATH_ISEMPTY", "文件路径为空"),
    FILE_TYPE_ISEMPTY("15019", "FILE_TYPE_ISEMPTY", "文件类型为空"),
    FILE_ID_ISEMPTY("15020", "FILE_ID_ISEMPTY", "文件id为空"),
    FILE_TYPE_MISMATCH("15019", "FILE_TYPE_MISMATCH", "文件类型不匹配"),
    BAIDUOCR_IMAGE_RECOGNITION_FAILED("15020", "BAIDUOCR_IMAGE_RECOGNITION_FAILED", "发票图片识别失败"),


    SYSTEM_ADD_FAILED("15021", "SYSTEM_ADD_FAILED", "新增失败"),
    SYSTEM_DEL_FAILED("15022", "SYSTEM_DEL_FAILED", "删除失败"),
    SYSTEM_EDIT_FAILED("15023", "SYSTEM_EDIT_FAILED", "编辑失败"),
    INVOICE_EMPTY_NOTBAD_CONFIRMED("15024", "INVOICE_EMPTY_NOTBAD_CONFIRMED", "发票明细为空,不可进行确认平账或确认坏账"),
    INVOICEDETAIL_EMPTY_NOTBAD_CONFIRMED("15025", "INVOICEDETAIL_EMPTY_NOTBAD_CONFIRMED", "票单明细为空,不可进行确认平账或确认坏账"),
    DIFFEXIST_NOTBAD_CONFIRMED("15026", "DIFFEXIST_NOTBAD_CONFIRMED", "当前票单存在差异单,不可进行确认平账或确认坏账"),
    BAIDUOCR_VERIFY_FAILED("15027", "BAIDUOCR_VERIFY_FAILED", "发票验真失败"),
    ITEMUPEXIST_NOTBAD_CONFIRMED("15030", "ITEMUPEXIST_NOTBAD_CONFIRMED", "当前票单明细单有更新,不可进行确认平账或确认坏账"),

    /**
     * 角色
     */
    CONTACT_ROLE_IS_ALREADY("15028", "CONTACT_ROLE_IS_ALREADY", "联系人角色已存在"),
    ROLE_NAME_IS_ALREADY("15029", "ROLE_NAME_IS_ALREADY", "角色名称已存在"),
    NO_ROLE_AUTH("15030", "NO_ROLE_AUTH", "员工无角色权限"),
    AUTH_LOGIN_BAD_ROLE("15031", "AUTH_LOGIN_BAD_ROLE", "账号未分配角色"),
    AUTH_LOGIN_BAD_MENU("15032", "AUTH_LOGIN_BAD_MENU", "角色未分配菜单"),
    AUTH_LOGIN_BAD_MENU2("15033", "AUTH_LOGIN_BAD_MENU2", "未配置菜单"),
    AUTH_MENU_IS_NOT_EMPTY("15034", "AUTH_MENU_IS_NOT_EMPTY", "菜单不能为空"),

    /**
     * 入住明细推送相关
     */
    ORDER_CHECK_EXCEPTION("16001", "ORDER_CHECK_EXCEPTION", "入住明细异常"),
    NO_ORDER_CHECK("16002", "NO_ORDER_CHECK", "未填写入住明细"),
    ORDER_CHECK_STATE_NOT_SURE("16003", "ORDER_CHECK_STATE_NOT_SURE", "入住状态为非已离店或NoShow"),

    /**
     * 提示说明
     */
    TYPE_SUPPLIER_CODE_IS_ALREADY("17001", "TYPE_SUPPLIER_CODE_IS_ALREADY", "供应商已有该类型说明"),

    /**
     * 0428新增异常编码
     */
    CITY_IS_NOT_ALREADY("18001", "CITY_IS_NOT_ALREADY", "城市不存在"),
    EXCHANGE_COIN_IS_NOT_ALREADY("18002", "EXCHANGE_COIN_IS_NOT_ALREADY", "币种不存在"),
    ORG_CURRENCY_IS_ALREADY("18003", "ORG_CURRENCY_IS_ALREADY", "商家币种不存在"),
    CURRENCY_EXCHANGE_ERROR("18004", "CURRENCY_EXCHANGE_ERROR", "原币种或者目标币种必须有一个为商家币种"),
    CITY_CODE_IS_NOT_EMPTY("18005", "CITY_CODE_IS_NOT_EMPTY", "城市编码不能为空"),
    TYPE_IS_NOT_EMPTY("18006", "TYPE_IS_NOT_EMPTY", "类型不能为空"),
    EMPTY_PARAM_SUPPLY_ORDER_CODE("18007", "EMPTY_PARAM_SUPPLY_ORDER_CODE", "供货单号不能为空"),
    ORDER_PAY_METHOD_IS_ERROR("18008", "CENTER_ORDER_PAY_METHOD_IS_ERROR", "订单支付类型错误"),
    ORDER_CODE_OR_FC_ORDER_CODE_NOT_EMPTY("18009", "CENTER_ORDER_CODE_OR_FC_ORDER_CODE_NOT_EMPTY", "客户订单号和天宫订单号二选一必填"),
    KEY_WORD_NOT_EMPTY("18010", "KEY_WORD_NOT_EMPTY", "关键字不能为空"),
    ADDRESS_BY_ID_NOT_EMPTY("18011", "ADDRESS_BY_ID_NOT_EMPTY", "目的地id不能为空"),
    ADDRESS_BY_ID_IS_ERROR("18012", "ADDRESS_BY_ID_IS_ERROR", "非法的目的地id"),
    AGENT_REFUND_CANNOT_BE_GREATER_THAN_CANCEL_AMOUNT("18013", "AGENT_REFUND_CANNOT_BE_GREATER_THAN_CANCEL_AMOUNT", "我司退客户金额不能大于取消日期的售价金额总和"),
    SUPPLY_REFUND_CANNOT_BE_GREATER_THAN_CANCEL_AMOUNT("18014", "SUPPLY_REFUND_CANNOT_BE_GREATER_THAN_CANCEL_AMOUNT", "供应商退我司金额不能大于取消日期的底价金额总和"),
    PRICE_END_IS_NOT_EMPTY("18015", "PRICE_END_IS_NOT_EMPTY", "最高价格不能为空"),
    PRICE_BEGIN_IS_NOT_EMPTY("18016", "PRICE_BEGIN_IS_NOT_EMPTY", "最低价格不能为空"),
    PRICE_BEGIN_LOWEST_PRICE_END("18017", "PRICE_BEGIN_LOWEST_PRICE_END", "最低价格不能大于等于最高价格"),
    PRICE_BEGIN_GREATER_ZERO("18018", "PRICE_BEGIN_GREATER_ZERO", "最低价格必须大于0"),
    COUNTRY_PROVINCE_NOT_SUPPORTED("18019", "COUNTRY_PROVINCE_NOT_SUPPORTED", "不支持该类型数据进行搜索"),
    SORT_BY_NOT_EXIST("18020", "SORT_BY_NOT_EXIST", "排序方式不存在"),
    //PRICE_END_GREATER_ZERO("18017", "PRICE_END_GREATER_ZERO", "最高价格必须大于0"),
    SUPPLIER_CODE_AND_NAME_IS_EMPTY("999", "SUPPLIER_CODE_AND_NAME_IS_EMPTY", "供应商编码和供应商名称都为空"),


    //邮件发单错误码
    EMAIL_SEND_ERROR("19001", "EMAIL_SEND_ERROR", "邮件发送失败"),
    //token失效
    TOKEN_INVALID("19002", "TOKEN_INVALID", "token失效"),
    //token缓存不一致
    TOKEN_INCONSISTENT("19003", "TOKEN_INCONSISTENT", "token不一致"),
    //当前供应商未配置邮件发单
    EMAIL_SEND_NOT_CONFIG_SUPPLIER("19004", "EMAIL_SEND_NOT_CONFIG_SUPPLIER", "当前供应商未配置邮件发单"),
    //保存供货单结果，状态一致不处理
    SAVE_SUPPLY_ORDER_RESULT_STATUS_CONSISTENT("19005", "SAVE_SUPPLY_ORDER_RESULT_STATUS_CONSISTENT", "保存供货单结果，状态一致不处理"),
    //供货单预览信息为空
    PREVIEW_SUPPLY_ORDER_INFO_IS_NULL("19006", "PREVIEW_SUPPLY_ORDER_INFO_IS_NULL", "预览供货单信息为空"),
    //订单必须为公付(预付)类型
    ORDER_TYPE_IS_NOT_PREPAY("19007", "ORDER_TYPE_IS_NOT_PREPAY", "订单必须为公付(预付)类型"),
    //邮件模版生成异常
    EMAIL_TEMPLATE_GENERATE_ERROR("19008", "EMAIL_TEMPLATE_GENERATE_ERROR", "邮件模版生成异常"),
    //邮件发单异常
    EMAIL_SEND_ERROR_CODE("19009", "EMAIL_SEND_ERROR_CODE", "邮件发单异常"),
    ORDER_CONFIRMATION_EMAIL_NOT_REQUIRED("19010", "EMAIL_SEND_ERROR_CODE", "到店付 + 确认订单不需要发送确认邮件"),
    //多个确认邮箱时
    EMAIL_IS_CONFIRM("001", "EMAIL_IS_CONFIRM", "This email has been processed. Please contact for verification offline"),


    TYPE_SCORE_MIN_ERROR("20001", "TYPE_SCORE_MIN_ERROR", "类型分数不能小于0"),
    TYPE_SCORE_MAX_ERROR("20002", "TYPE_SCORE_MAX_ERROR", "类型分数不能大于99"),
    ORDER_FILE_IS_NOT_EXIST("20003", "ORDER_FILE_IS_NOT_EXIST", "订单文件不存在"),
    FILE_NUM_ERROR("20004", "FILE_NUM_ERROR", "文件数量不能为空"),
    FILE_NUM_MAX_ERROR("20005", "FILE_NUM_MAX_ERROR", "文件数量最多只支持5个"),
    FILE_SUFFIX_ERROR("20006", "FILE_SUFFIX_ERROR", "上传的文件格式不支持"),
    EXCEPTION_DATETIME("20007", "EXCEPTION_DATETIME", "日期格式错误"),

    INVOICE_CONTACT_AREA_CODE_NOT_EXIST("10026", "INVOICE_CONTACT_AREA_CODE_NOT_EXIST", "联系人区号不存在"),
    INVOICE_COMPANY_AREA_CODE_NOT_EXIST("10052", "INVOICE_COMPANY_AREA_CODE_NOT_EXIST", "公司电话区号不存在"),

    // 发票自动开票相关错误码
    INVOICE_NOT_MEET_AUTO_CONDITION("20100", "INVOICE_NOT_MEET_AUTO_CONDITION", "该发票不符合自动开票条件"),
    INVOICE_AMOUNT_EXCEED_UNINVOICED_AMOUNT("20101", "INVOICE_AMOUNT_EXCEED_UNINVOICED_AMOUNT", "本次开票金额大于开票金额，不满足开票条件"),
    INVOICE_UPDATE_FAILED_NO_ORDER_INFO("20102", "INVOICE_UPDATE_FAILED_NO_ORDER_INFO", "发票更新失败，订单编号或订单ID必须存在一个"),
    INVOICE_NUMBER_EMPTY("20103", "INVOICE_NUMBER_EMPTY", "发票号不能为空"),
    INVOICE_BILL_ALREADY_EXISTS("20104", "INVOICE_BILL_ALREADY_EXISTS", "已存在票单"),
    INVOICE_AMOUNT_EXCEED_AVAILABLE_AMOUNT("20105", "INVOICE_AMOUNT_EXCEED_AVAILABLE_AMOUNT", "本次开票金额大于开票金额，不满足开票条件"),
    INVOICE_FILE_DOWNLOAD_FAIL("20106", "INVOICE_FILE_DOWNLOAD_FAIL", "下载失败"),
    INVOICE_CREATE_BILL_FAIL("20107", "INVOICE_CREATE_BILL_FAIL", "没有可开票的订单发票"),
    PUSH_STATUS_FAIL("20108", "PUSH_STATUS_FAIL", "推送失败"),
    INVOICE_UN_OPEN_INVOICE_DOWNLOAD_FAIL("20109", "INVOICE_UN_OPEN_INVOICE_DOWNLOAD_FAIL", "非第三方开票的发票,无法下载发票文件"),
    INVOICE_UN_SUPPORT_STATUS_TYPE("20110", "INVOICE_UN_SUPPORT_STATUS_TYPE", "该发票类型目前不支持开票"),
    INVOICE_UN_SUPPORT_ORDER_STATUS_TYPE("20111", "INVOICE_UN_SUPPORT_ORDER_STATUS_TYPE", "该订单状态不满足开票条件"),
    INVOICE_UN_SUPPORT_ORDER_ENT_TIME_TYPE("20112", "INVOICE_UN_SUPPORT_ORDER_ENT_TIME_TYPE", "该订单离店日期不满足开票条件"),
    INVOICE_ALREADY_OPENED_TYPE("20113", "INVOICE_ALREADY_OPENED_TYPE", "已发送第三方开票，不能重复开票"),
    INVOICE_UN_SUPPORT_ORDER_SALE_CURRENCY_TYPE("20114", "INVOICE_UN_SUPPORT_ORDER_SALE_CURRENCY_TYPE", "客户币种非CNY,不满足开票条件"),
    INVOICE_UN_OPEN_INVOICE_TYPE("20115", "INVOICE_UN_OPEN_INVOICE_TYPE", "发票非第三方开票,不支持推送开票状态"),
    INVOICE_UN_DOWNLOAD_INVOICE_TYPE("20116", "INVOICE_UN_DOWNLOAD_INVOICE_TYPE", "发票不存在发票文件,不支持推送开票状态"),
    PARTNER_CODE_NO_EXIST_TYPE("20117", "PARTNER_CODE_NO_EXIST_TYPE", "推送发票状态失败 客户合作编码不存在"),
    INVOICE_STATUS_SEND_FAIL("20118", "INVOICE_STATUS_SEND_FAIL", "推送发票状态失败"),
    CREATE_BILL_UN_SUPPORT_ORDER_SALE_CURRENCY_TYPE("20119", "CREATE_BILL_UN_SUPPORT_ORDER_SALE_CURRENCY_TYPE", "客户币种非CNY,不满足创建票单条件"),
    CREATE_BILL_ALREADY_OPENED_TYPE("20120", "CREATE_BILL_ALREADY_OPENED_TYPE", "已创建该订单的销项票单,请勿重新创建"),
    CREATE_BILL_UN_OPEN_INVOICE_FAIL("20121", "CREATE_BILL_UN_OPEN_INVOICE_FAIL", "非第三方开票的发票，无法创建票单"),
    CREATE_BILL_UN_OPEN_INVOICE_DOWNLOAD_FAIL("20122", "CREATE_BILL_UN_OPEN_INVOICE_DOWNLOAD_FAIL", "无发票文件，无法创建票单"),
    CREATE_BILL_UN_SUPPORT_ORDER_STATUS_TYPE("20123", "CREATE_BILL_UN_SUPPORT_ORDER_STATUS_TYPE", "该订单状态不满足创建票单条件"),
    CREATE_BILL_UN_SUPPORT_ORDER_ENT_TIME_TYPE("20124", "CREATE_BILL_UN_SUPPORT_ORDER_ENT_TIME_TYPE", "该订单离店日期不满足创建开票条件"),
    INVOICE_DOWNLOAD_FILE_UN_GEN("20125", "Invoice_DOWNLOAD_FILE_UN_GEN", "下载失败,还未生成pdf文件,请稍后重试"),
    PUSH_SEND_STATUS_UN_SUPPORT("20126", "PUSH_SEND_STATUS_UN_SUPPORT", "发送状态不符合推送条件"),
    PUSH_ORDER_STATUS_UN_SUPPORT("20127", "PUSH_ORDER_STATUS_UN_SUPPORT", "发票状态不符合推送条件"),
    PUSH_FAIL_BY_MANUAL("20128", "PUSH_FAIL_BY_MANUAL", "发票是人工手动新增的，不支持推送发票状态"),
    BIND_AMOUNT_NOT_EQUAL("20129", "BIND_AMOUNT_NOT_EQUAL", "发票可用金额不等于票单本次开票金额"),
    DOWNLOAD_FAIL_BY_EXIST_INVOICE("20130", "DOWNLOAD_FAIL_BY_EXIST_INVOICE", "发票号码已存在销项发票列表，下载发票文件失败"),
    INVOICE_ORDER_AMOUNT_ZERO_NOT_SUPPORT("20131", "INVOICE_ORDER_AMOUNT_ZERO_NOT_SUPPORT", "订单应收金额小于等于0，不满足开票条件"),

    // VIP名单错误码
    VIP_LIST_NOT_EXIST("20200", "VIP_LIST_NOT_EXIST", "VIP名单不存在"),
    VIP_LIST_NAME_ALREADY_EXISTS("20201", "VIP_LIST_NAME_ALREADY_EXISTS", "VIP名单名称已存在"),
    VIP_LIST_STATUS_INVALID("20202", "VIP_LIST_STATUS_INVALID", "VIP名单状态无效"),
    VIP_CUSTOMER_NOT_EXIST("20203", "VIP_CUSTOMER_NOT_EXIST", "VIP客户不存在"),
    VIP_CUSTOMER_INFO_DUPLICATE("20204", "VIP_CUSTOMER_INFO_DUPLICATE", "VIP客户信息重复"),
    VIP_CUSTOMER_INFO_FORMAT_ERROR("20205", "VIP_CUSTOMER_INFO_FORMAT_ERROR", "VIP客户信息格式错误"),
    VIP_IMPORT_FILE_FORMAT_ERROR("20206", "VIP_IMPORT_FILE_FORMAT_ERROR", "导入文件格式错误"),
    VIP_IMPORT_DATA_EMPTY("20207", "VIP_IMPORT_DATA_EMPTY", "导入数据为空"),
    VIP_IMPORT_DATA_EXCEED_LIMIT("20208", "VIP_IMPORT_DATA_EXCEED_LIMIT", "导入数据量超限"),
    VIP_CUSTOMER_BATCH_LIMIT_EXCEEDED("20209", "VIP_CUSTOMER_BATCH_LIMIT_EXCEEDED", "最大只支持20个客户编码"),
    VIP_CLIENT_DTO_NULL("20210", "VIP_CLIENT_DTO_NULL", "VIP客户信息不能为空"),
    VIP_AGENT_CODE_EMPTY("20211", "VIP_AGENT_CODE_EMPTY", "客户编码不能为空"),
    VIP_NAME_EMPTY("20212", "VIP_NAME_EMPTY", "VIP姓名不能为空"),
    VIP_AGENT_CODE_DUPLICATE("20213", "VIP_AGENT_CODE_DUPLICATE", "客户编码存在重复"),
    VIP_NAME_DUPLICATE("20214", "VIP_NAME_DUPLICATE", "VIP姓名存在重复"),
    VIP_AGENT_CODE_INVALID("20215", "VIP_AGENT_CODE_INVALID", "无效的客户编码"),
    VIP_AGENT_CODE_DISABLED("20216", "VIP_AGENT_CODE_DISABLED", "客户编码已禁用"),
    VIP_CUSTOMER_CLIENT_CODE_UPDATE_ERROR("20217", "VIP_CUSTOMER_CLIENT_CODE_UPDATE_ERROR", "客户编码不可修改"),
    VIP_CUSTOMER_VIP_NAME_UPDATE_ERROR("20218", "VIP_CUSTOMER_VIP_NAME_UPDATE_ERROR", "更新失败,VIP姓名仅能支持输入一个"),

    /*** 供应商酒店黑白名单相关 以20300开始 ***/
    PRO_ORG_HOTEL_AVAILABLE_QUERY_ERROR("20300", "PRO_ORG_HOTEL_AVAILABLE_QUERY_ERROR", "供应商酒店黑白名单查询失败"),
    PRO_ORG_HOTEL_AVAILABLE_HOTEL_INFO_QUERY_ERROR("20301", "PRO_ORG_HOTEL_AVAILABLE_HOTEL_INFO_QUERY_ERROR", "酒店信息查询失败"),
    PRO_ORG_HOTEL_AVAILABLE_HOTEL_ID_NOT_NULL("20302", "PRO_ORG_HOTEL_AVAILABLE_HOTEL_ID_NOT_NULL", "酒店id必填"),
    PRO_ORG_HOTEL_AVAILABLE_SUPPLIER_ID_NOT_NULL("20303", "PRO_ORG_HOTEL_AVAILABLE_SUPPLIER_ID_NOT_NULL", "供应商必填"),
    PRO_ORG_HOTEL_AVAILABLE_TYPE_NOT_NULL("20304", "PRO_ORG_HOTEL_AVAILABLE_TYPE_NOT_NULL", "类型必填"),
    PRO_ORG_HOTEL_AVAILABLE_TYPE_INVALID("20305", "PRO_ORG_HOTEL_AVAILABLE_TYPE_INVALID", "类型无效，只能为白名单或黑名单"),
    PRO_ORG_HOTEL_AVAILABLE_DUPLICATE("20306", "PRO_ORG_HOTEL_AVAILABLE_DUPLICATE", "新增失败，配置已存在"),
    PRO_ORG_HOTEL_AVAILABLE_ADD_ERROR("20307", "PRO_ORG_HOTEL_AVAILABLE_ADD_ERROR", "新增供应商酒店黑白名单失败"),
    PRO_ORG_HOTEL_AVAILABLE_FILE_SIZE_EXCEED("20308", "PRO_ORG_HOTEL_AVAILABLE_FILE_SIZE_EXCEED", "上传的模板数量不能大于5000条"),
    PRO_ORG_HOTEL_AVAILABLE_FILE_EMPTY_ERROR("20309", "PRO_ORG_HOTEL_AVAILABLE_FILE_EMPTY_ERROR", "模板为空，请检查"),
    PRO_ORG_HOTEL_AVAILABLE_HEADER_MISMATCH_ERROR("20310", "PRO_ORG_HOTEL_AVAILABLE_HEADER_MISMATCH_ERROR", "导入文件,文件模板不正确"),
    PRO_ORG_HOTEL_AVAILABLE_FILE_FORMAT_ERROR("20311", "PRO_ORG_HOTEL_AVAILABLE_FILE_FORMAT_ERROR", "文件格式不正确"),
    PRO_ORG_HOTEL_AVAILABLE_DUPLICATE_ROW("20312", "PRO_ORG_HOTEL_AVAILABLE_DUPLICATE_ROW", "该供应商酒店的黑白名单存在重复项"),
    PRO_ORG_HOTEL_AVAILABLE_HOTEL_ID_NOT_EXIST("20312","PRO_ORG_HOTEL_AVAILABLE_HOTEL_ID_NOT_EXIST" , "该酒店不存在"),
    PRO_ORG_HOTEL_AVAILABLE_SUPPLIER_CODE_NOT_EXIST("20313","PRO_ORG_HOTEL_AVAILABLE_SUPPLIER_CODE_NOT_EXIST" , "该供应商不存在"),
    PRO_ORG_HOTEL_AVAILABLE_SUPPLIER_CODE_NOT_ENABLE("20314","PRO_ORG_HOTEL_AVAILABLE_SUPPLIER_CODE_NOT_ENABLE" , "该供应商未启用"),
    PRO_ORG_HOTEL_AVAILABLE_ALREADY_EXIST_WHITE("20315","PRO_ORG_HOTEL_AVAILABLE_ALREADY_EXIST_WHITE" , "该供应商酒店已设置白名单，无法添加黑名单"),
    PRO_ORG_HOTEL_AVAILABLE_ALREADY_EXIST_BLACK("20316","PRO_ORG_HOTEL_AVAILABLE_ALREADY_EXIST_BLACK" , "该供应商酒店已设置黑名单，无法添加白名单"),
    PRO_ORG_HOTEL_AVAILABLE_HOTEL_ID_ERROR("20317","PRO_ORG_HOTEL_AVAILABLE_HOTEL_ID_ERROR" , "导入失败,请保证酒店ID为数字"),
    ;

    public final String errorNo;//业务场景编号
    public final String errorCode;//业务场景编码
    public final String errorDesc;//业务场景描述

    public static String getKeyByValue(String errorCode) {
        String key = "000";
        for (ErrorCodeEnum errorCodeEnum : ErrorCodeEnum.values()) {
            if (errorCodeEnum.errorCode.equals(errorCode)) {
                key = errorCodeEnum.errorNo;
                break;
            }
        }
        return key;
    }

    public static String getDescByValue(String errorCode) {
        String desc = "";
        for (ErrorCodeEnum errorCodeEnum : ErrorCodeEnum.values()) {
            if (errorCodeEnum.errorCode.equals(errorCode)) {
                desc = errorCodeEnum.errorDesc;
                break;
            }
        }
        return desc;
    }

    public static String getErrorCodeByKey(String key) {
        String errorCode = "";
        for (ErrorCodeEnum errorCodeEnum : ErrorCodeEnum.values()) {
            if (errorCodeEnum.errorNo.equals(key)) {
                errorCode = errorCodeEnum.errorCode;
                break;
            }
        }
        return errorCode;
    }
}
