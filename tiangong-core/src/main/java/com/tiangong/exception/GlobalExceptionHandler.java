package com.tiangong.exception;

import com.tiangong.common.Response;
import com.tiangong.enums.ErrorCodeEnum;
import com.tiangong.util.SlsLoggerUtil;
import com.tiangong.util.StrUtilX;
import feign.RetryableException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 全局异常处理
 */

@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    @Autowired
    private SlsLoggerUtil slsLoggerUtil;

    /**
     * 处理运行时异常
     */
    @ExceptionHandler(RuntimeException.class)
    public Response<Object> globalException(RuntimeException ex) {
        log.error("RuntimeException:", ex);
        Map<String, String> map = new HashMap<>();
        map.put("name", "异常信息");
        map.put("level", "error");
        map.put("message", StrUtilX.getStackTraceAsString(ex));
        map.put("response", ex.toString());
        slsLoggerUtil.saveLog(map, "tiangong", "tiangong");
//        try {
//            CustomException exception = (CustomException) ex;
//            return  Response.error(exception.getFailCode(), exception.getMsg()+"("+exception.getFailCode()+")");
//        }catch (Exception e){
//            return  Response.error(ex.toString(), StrUtilX.getStackTraceAsString(ex));
//        }
        return Response.error(ErrorCodeEnum.SYSTEM_EXCEPTION.errorCode, StrUtilX.getStackTraceAsString(ex));
    }

    /**
     * 处理自定义异常
     */
    @ExceptionHandler(CustomException.class)
    public Response<Object> customException(CustomException ex) {
        log.error("CustomException:", ex);
        Map<String, String> map = new HashMap<>();
        map.put("name", "异常信息");
        map.put("level", "error");
        map.put("message", StrUtilX.getStackTraceAsString(ex));
        map.put("response", ex.toString());
        slsLoggerUtil.saveLog(map, "tiangong", "tiangong");
        return Response.error(ex.getFailCode(), ex.getMsg(), StrUtilX.getStackTraceAsString(ex));
    }

    /**
     * 处理所有异常
     */
    @ExceptionHandler(Exception.class)
    public Response<Object> exception(Exception ex) {
        log.error("ExceptionHandler:", ex);
        Map<String, String> map = new HashMap<>();
        map.put("name", "异常信息");
        map.put("level", "error");
        map.put("message", StrUtilX.getStackTraceAsString(ex));
        map.put("response", ex.toString());
        slsLoggerUtil.saveLog(map, "tiangong", "tiangong");
        return Response.error(ErrorCodeEnum.SYSTEM_EXCEPTION.errorCode, ex.toString(), StrUtilX.getStackTraceAsString(ex));
    }

    /**
     * 处理参数异常
     */
    @ExceptionHandler(value = MethodArgumentNotValidException.class)
    public Response<Object> validException(MethodArgumentNotValidException ex) {
        log.error("MethodArgumentNotValidException:", ex);
        String msg = Objects.requireNonNull(ex.getBindingResult().getFieldError()).getDefaultMessage();
        if (StrUtilX.isNotEmpty(msg)) {
            return Response.error(msg, msg);
        } else {
            return Response.error(ErrorCodeEnum.INVALID_INPUTPARAM.errorCode, Objects.requireNonNull(ex.getBindingResult().getFieldError()).getDefaultMessage());
        }
    }

    /**
     * 处理约束违规错误
     */
    @ExceptionHandler(value = ConstraintViolationException.class)
    public Response<Object> validException(ConstraintViolationException ex) {
        log.error("ConstraintViolationException:", ex);
        return Response.error(ErrorCodeEnum.INVALID_INPUTPARAM.errorCode,
                ex.getConstraintViolations().stream()
                        .map(ConstraintViolation::getMessageTemplate)
                        .map(String::toString).distinct()
                        .collect(Collectors.joining(",")));
    }

    /**
     * 处理数组下标越界异常
     */
    @ExceptionHandler(value = IllegalArgumentException.class)
    public Response<Object> IllegalArgumentException(Exception ex) {
        log.error("IllegalArgumentException:", ex);
        Map<String, String> map = new HashMap<>();
        map.put("name", "异常信息");
        map.put("level", "error");
        map.put("message", StrUtilX.getStackTraceAsString(ex));
        map.put("response", ex.toString());
        slsLoggerUtil.saveLog(map, "tiangong", "tiangong");
        return Response.error(ErrorCodeEnum.INVALID_INPUTPARAM.errorCode, ex.getMessage());
    }

    @ExceptionHandler(value = SysException.class)
    public Response<Object> SysException(SysException ex) {
        log.error("SysException:", ex);
        Map<String, String> map = new HashMap<>();
        map.put("name", "异常信息");
        map.put("level", "error");
        map.put("message", StrUtilX.getStackTraceAsString(ex));
        map.put("response", ex.toString());
        slsLoggerUtil.saveLog(map, "tiangong", "tiangong");
        return Response.error(ex.getCode(), ex.getMessage());
    }

    @ExceptionHandler(value = RetryableException.class)
    public Response<Object> SocketTimeoutException(RetryableException re) {
        log.error("RetryableException:", re);
        Map<String, String> map = new HashMap<>();
        map.put("name", "连接超时");
        map.put("level", "error");
        map.put("message", StrUtilX.getStackTraceAsString(re));
        map.put("response", re.toString());
        slsLoggerUtil.saveLog(map, "tiangong", "tiangong");
        return Response.error(ErrorCodeEnum.INTERNAL_TIMEOUT.errorCode, ErrorCodeEnum.INTERNAL_TIMEOUT.errorDesc);
    }
}
