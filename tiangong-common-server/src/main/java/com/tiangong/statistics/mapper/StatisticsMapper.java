package com.tiangong.statistics.mapper;

import com.tiangong.statistics.domain.StatisticsPO;
import com.tiangong.statistics.dto.BusinessReturnDTO;
import com.tiangong.statistics.dto.QueryBusinessStatisticsDTO;
import tk.mybatis.mapper.common.MySqlMapper;

import java.util.List;

public interface StatisticsMapper extends MySqlMapper<StatisticsPO> {

    /**
     * 经营概况 2.0
     */
    List<BusinessReturnDTO> queryOperateStatistics(QueryBusinessStatisticsDTO request);
}
