package com.tiangong.task;

import com.alibaba.fastjson.JSONObject;
import com.tiangong.keys.RedisKey;
import com.tiangong.organization.domain.OrgSupplierPO;
import com.tiangong.organization.mapper.SupplierCompanyMapper;
import com.tiangong.redis.core.RedisTemplateX;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;

/**
 * 初始化供应商信息任务
 */
@Slf4j
@Component
public class InitSupplierConfigTask {

    @Autowired
    private SupplierCompanyMapper supplierCompanyMapper;

    @XxlJob("InitSupplierConfigTask")
    public void initSupplierConfigTask() {
        try {
            XxlJobHelper.log("执行初始化供应商信息任务开始");
            List<OrgSupplierPO> suppliers = supplierCompanyMapper.querySupplier();
            HashMap<String, String> map = new HashMap<>();
            suppliers.forEach(i -> map.put(i.getSupplierCode(), JSONObject.toJSONString(i)));
            RedisTemplateX.hPutAll(RedisKey.SUPPLY_ACCOUNT_CONFIG, map);
            XxlJobHelper.log("执行初始化供应商信息任务结束");
        } catch (Exception e) {
            log.error("执行初始化供应商信息任务异常", e);
            XxlJobHelper.log("执行初始化供应商信息任务异常", e);
        }
    }
}
