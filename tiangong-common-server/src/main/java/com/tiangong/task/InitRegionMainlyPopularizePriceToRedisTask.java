package com.tiangong.task;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.tiangong.hotel.domain.RegionMainlyPopularizePriceEntity;
import com.tiangong.hotel.domain.req.MainlyPopularizePriceReq;
import com.tiangong.hotel.mapper.RegionMainlyPopularizePriceMapper;
import com.tiangong.keys.RedisKey;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.util.CollUtilX;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 初始化区域主推价格信息到缓存任务
 */
@Slf4j
@Component
public class InitRegionMainlyPopularizePriceToRedisTask {

    @Autowired
    private RegionMainlyPopularizePriceMapper regionMainlyPopularizePriceMapper;

    @XxlJob("initRegionMainlyPopularizePriceToRedisTask")
    public void initRegionMainlyPopularizePriceToRedisTask() {
        try {
            XxlJobHelper.log("执行初始化区域主推价格信息到缓存任务开始");
            // 先删除缓存数据
            RedisTemplateX.delete(RedisKey.REGION_MAINLY_POPULARIZE_PRICE_COUNTRY_KEY);
            RedisTemplateX.delete(RedisKey.REGION_MAINLY_POPULARIZE_PRICE_CITY_KEY);
            // 查询区域主推价格信息
            List<RegionMainlyPopularizePriceEntity> entities = regionMainlyPopularizePriceMapper.selectList(new QueryWrapper<>());
            if (CollUtilX.isNotEmpty(entities)) {
                Map<String, String> countryMap = new HashMap<>();
                Map<String, String> cityMap = new HashMap<>();
                for (RegionMainlyPopularizePriceEntity entity : entities) {
                    MainlyPopularizePriceReq req = new MainlyPopularizePriceReq();
                    req.setStartPrice(entity.getStartPrice());
                    req.setEndPrice(entity.getEndPrice());
                    if (entity.getRegionType() == 1) {
                        countryMap.put(entity.getRegionCode(), JSONUtil.toJsonStr(req));
                    } else if (entity.getRegionType() == 3) {
                        cityMap.put(entity.getRegionCode(), JSONUtil.toJsonStr(req));
                    }
                }

                // 保存到缓存
                RedisTemplateX.hPutAll(RedisKey.REGION_MAINLY_POPULARIZE_PRICE_COUNTRY_KEY, countryMap);
                RedisTemplateX.hPutAll(RedisKey.REGION_MAINLY_POPULARIZE_PRICE_CITY_KEY, cityMap);
            }
            XxlJobHelper.log("执行初始化区域主推价格信息到缓存任务结束");
        } catch (Exception e) {
            log.error("执行初始化区域主推价格信息到缓存任务异常", e);
            XxlJobHelper.log("执行初始化区域主推价格信息到缓存任务异常", e);
        }
    }
}
