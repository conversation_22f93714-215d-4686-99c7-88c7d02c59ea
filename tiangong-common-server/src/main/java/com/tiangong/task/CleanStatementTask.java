package com.tiangong.task;

import com.tiangong.keys.RedisKey;
import com.tiangong.redis.core.RedisTemplateX;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 清除账单缓存状态任务
 */
@Slf4j
@Component
public class CleanStatementTask {

    @XxlJob("initCleanStatementTask")
    public void initCleanStatementTask() {
        try {
            XxlJobHelper.log("执行清除账单缓存状态任务开始");
            RedisTemplateX.delete(RedisKey.SUPPLY_CANCEL_STATE);
            RedisTemplateX.delete(RedisKey.AGENT_CANCEL_STATE);
            XxlJobHelper.log("执行清除账单缓存状态任务结束");
        } catch (Exception e) {
            log.error("执行清除账单缓存状态任务异常", e);
            XxlJobHelper.log("执行清除账单缓存状态任务异常", e);
        }
    }
}
