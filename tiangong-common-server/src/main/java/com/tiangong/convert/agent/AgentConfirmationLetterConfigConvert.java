package com.tiangong.convert.agent;

import com.tiangong.organization.domain.AgentConfirmationLetterConfigPO;
import com.tiangong.organization.domain.req.AgentConfirmationLetterConfigAditReq;
import com.tiangong.organization.remote.dto.AgentConfirmationLetterConfigResp;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * @program: glink_tiangong
 * @ClassName AgentConfirmationLetterConfigConvert
 * @description:
 * @author: 湫
 * @create: 2025/04/15/ 14:44
 * @Version 1.0
 **/

@Mapper
public interface AgentConfirmationLetterConfigConvert {

    AgentConfirmationLetterConfigConvert INSTANCE = Mappers.getMapper(AgentConfirmationLetterConfigConvert.class);

    AgentConfirmationLetterConfigPO convert(AgentConfirmationLetterConfigAditReq bean);

    AgentConfirmationLetterConfigResp convert(AgentConfirmationLetterConfigPO bean);
}
