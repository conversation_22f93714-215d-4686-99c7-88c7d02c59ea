package com.tiangong.convert;

import com.tiangong.agent.dto.AgentConfigDTO;
import com.tiangong.common.domain.entity.AuditLogEntity;
import com.tiangong.common.domain.req.AuditLogReq;
import com.tiangong.common.domain.resp.AuditLogResp;
import com.tiangong.dto.common.AuditLogAddReq;
import com.tiangong.dto.hotel.AddHotelAvailableReq;
import com.tiangong.dto.hotel.FacilityDto;
import com.tiangong.dto.hotel.HotCityClickDTO;
import com.tiangong.dto.hotel.HotelAvailableDTO;
import com.tiangong.file.domain.FileLogPO;
import com.tiangong.file.req.FileReq;
import com.tiangong.file.resp.FileResp;
import com.tiangong.finance.OrgDTO;
import com.tiangong.fuzzyquery.dto.FuzzyCityDTO;
import com.tiangong.hotel.domain.*;
import com.tiangong.hotel.domain.req.ExportHotelRecommendReq;
import com.tiangong.hotel.domain.req.ImportHotelRecommendReq;
import com.tiangong.hotel.domain.vo.AutoUpdateLowestPriceCityVo;
import com.tiangong.hotel.domain.vo.AutoUpdateLowestPriceConfigVo;
import com.tiangong.hotel.domain.vo.ImportHotelRecommendReqDTO;
import com.tiangong.hotel.dto.BaseinfoAreadataDTO;
import com.tiangong.hotel.dto.BaseinfoRegionDTO;
import com.tiangong.hotel.req.*;
import com.tiangong.operatelog.domain.OperateLogEntity;
import com.tiangong.operatelog.domain.OperateLogResp;
import com.tiangong.operatelog.dto.OperateLogReqDTO;
import com.tiangong.organization.domain.*;
import com.tiangong.organization.domain.req.ImportSupplierLabelConfigExportReq;
import com.tiangong.organization.domain.req.ImportSupplierLabelConfigReq;
import com.tiangong.organization.domain.req.SupplierLabelConfigReq;
import com.tiangong.organization.domain.req.SupplierUpdateReq;
import com.tiangong.organization.remote.dto.AddSupplierReq;
import com.tiangong.organization.remote.dto.AgentAccountConfig;
import com.tiangong.user.domain.ExamineUserDTO;
import com.tiangong.user.domain.MenuPO;
import com.tiangong.user.domain.RolePO;
import com.tiangong.user.domain.req.AddOrUpdateRoleReq;
import com.tiangong.user.domain.req.DelRoleReq;
import com.tiangong.user.dto.FirstMenuDTO;
import com.tiangong.user.dto.MenuDTO;
import com.tiangong.user.dto.MenuLevelDTO;
import com.tiangong.vip.domain.dto.AgentVipCacheDTO;
import com.tiangong.vip.domain.po.AgentVipClientPO;
import com.tiangong.vip.dto.AgentVipClientDTO;
import com.tiangong.vip.domain.vo.AgentVipClientVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @program: glink_tiangong
 * @ClassName CommonDtoConvert
 * @description:
 * @author: 湫
 * @create: 2025/01/16/ 12:27
 * @Version 1.0
 **/
@Mapper
public interface CommonDtoConvert {
    CommonDtoConvert INSTANCE = Mappers.getMapper(CommonDtoConvert.class);


    List<HotelAvgPriceEntity> HotelAvgPriceListConvert(List<HotelAvgPriceReq> reqList);

    RegionMainlyPopularizePriceEntity RegionMainlyPopularizePriceConvert(RegionMainlyPopularizePriceReq req);

    List<CityAvgPriceEntity> CityAvgPriceConvert(List<CityAvgPriceReq> reqList);

    FirstMenuDTO FirstMenuConvert(MenuDTO menuDTO);

    FileLogPO FileConvert(FileResp resp);

    FileResp fileRespConvert(FileLogPO fileLogPO);

    FileLogPO fileLogPOConvert(FileReq req);

    OperateLogEntity OperateLogConvert(OperateLogReqDTO newDO);

    ExamineUserDTO ExamineUserConvert(UserPO userPO);

    MenuLevelDTO MenuLevelConvert(MenuDTO menuDTO);

    UserApplyPO UserApplyPOConvert(UserPO existPO);

    OrgDTO orgDTOConvert(OrgPO orgPO);

    BaseinfoFacility facilityConvert(FacilityDto baseinfoFacility);

    FacilityDto facilityDtoConvert(BaseinfoFacility facility);

    RolePO RolePOConvert(AddOrUpdateRoleReq req);

    RolePO RoleConvert(DelRoleReq req);

    com.tiangong.user.domain.resp.MenuDTO MenuDTOConvert(MenuPO menu2);

    com.tiangong.user.domain.resp.MenuDTO MenuConvert(com.tiangong.user.domain.resp.MenuDTO menu2);

    List<BaseinfoAreadataDTO> baseinfoAreadataDTOConvert(List<BaseinfoAreadataEntity> list);

    List<BaseinfoRegionDTO> baseinfoRegionDTOConvert(List<BaseinfoRegionEntity> list);

    HotCityClickEntity hotCityClickEntityConvert(HotCityClickDTO req);

    OrgPO orgPOConvert(AddSupplierReq req);

    SupplierCompanyPO supplierCompanyPOConvert(AddSupplierReq req);

    UserPO adminPOConvert(AddSupplierReq req);

    com.tiangong.organization.remote.dto.OrgDTO orgDTOComConvert(OrgPO orgPO);

    AddSupplierReq supplierConvert(SupplierUpdateReq req);

    SupplierLabelConfigPO supplierLabelConfigPOConvert(SupplierLabelConfigReq req);

    ImportSupplierLabelConfigExportReq importSupplierLabelConfigExportReqConvert(ImportSupplierLabelConfigReq importReq);

    HotelAvailableDTO hotelAvailableDTOConvert(HotelAvailablePO hotelAvailablePO);

    HotelAvailablePO hotelAvailablePOConvert(AddHotelAvailableReq req);

    HotelLabelConfigEntity hotelLabelConfigEntityConvert(HotelLabelConfigReq req);

    com.tiangong.dto.common.TipsResp respConvert(TipsPO tipsPO);

    List<com.tiangong.dto.common.TipsResp> tipsRespConvert(List<TipsPO> poList);

    FirstMenuDTO firstMenuDTOConvert(MenuDTO menuDTO);

    MenuLevelDTO menuLevelDTOConvert(MenuDTO menuDTO);

    HotelRecommendEntity hgotelRecommendEntityConvert(HotelRecommendReq req);

    BaseinfoHotCityEntity baseinfoHotCityEntityConvert(BaseinfoAreadataEntity bean);

    AuditLogEntity convertAuditLogEntity(AuditLogReq req);

    AuditLogEntity convertAuditLogEntity(AuditLogAddReq req);

    AuditLogResp convertAuditLogResp(AuditLogEntity auditLogEntity);

    List<AuditLogResp> convertAuditLogResp(List<AuditLogEntity> auditLogEntityList);

    List<FuzzyCityDTO> convertFuzzyCityDTO(List<BaseinfoAreadataEntity> baseinfoAreadataEntityList);

    OperateLogResp convertOperateLogResp(OperateLogEntity operateLogEntity);

    OperateLogResp convertOperateLogResp(OperateLogReqDTO operateLogReqDTO);

    List<AutoUpdateLowestPriceCityEntity> lowestPriceCityEntityConvert(List<AutoUpdateLowestPriceCityVo> lowestPriceCity);

    AutoUpdateLowestPriceConfigEntity lowestPriceTaskConvert(AutoUpdateLowestPriceConfigVo lowestPriceTask);

    ExportHotelRecommendReq ExportHotelRecommendReqConvert(ImportHotelRecommendReqDTO req);

    ImportHotelRecommendReqDTO ImportHotelRecommendReqDTOConvert(ImportHotelRecommendReq req);
    AgentConfigDTO convert(AgentAccountConfig agentAccountConfig);


    AgentVipClientDTO convertVOToDTO(AgentVipClientVO vo);
    AgentVipClientVO convertPOToVO(AgentVipClientPO vo);
    AgentVipCacheDTO convert(AgentVipClientPO po);
    AgentVipCacheDTO convert(AgentVipClientDTO dto);
}
