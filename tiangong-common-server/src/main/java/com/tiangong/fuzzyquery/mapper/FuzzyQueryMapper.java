package com.tiangong.fuzzyquery.mapper;

import com.tiangong.dto.common.MyMapper;
import com.tiangong.dto.hotel.HotelBrandResp;
import com.tiangong.dto.hotel.HotelGroupResp;
import com.tiangong.fuzzyquery.dto.*;
import com.tiangong.hotel.domain.vo.HotelBrandDTO;
import com.tiangong.organization.domain.OrgPO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * @Auther: Owen
 * @Date: 2019/7/2 11:39
 * @Description:
 */
@Component(value = "fuzzyQueryMapper")
public interface FuzzyQueryMapper extends MyMapper<OrgPO> {

    /**
     * 查询供应商列表
     */
    List<FuzzySupplierDTO> fuzzyQuerySupplier(FuzzyQueryDTO fuzzyQueryDTO);

    /**
     * 模糊查询分销商列表
     */
    List<FuzzyAgentDTO> fuzzyQueryAgent(FuzzyQueryDTO fuzzyQueryDTO);

    /**
     * 查询渠道配置列表
     */
    List<FuzzyChannelConfigDTO> queryChannelConfig(@Param("companyCode") String companyCode);

    /**
     * 查询渠道配置列表
     */
    List<FuzzyChannelConfigDTO> queryAgentChannelCode(Map<String, String> requestMap);

    /**
     * 根据集团编码查询集团名称
     */
    List<HotelGroupResp> queryGroupByGroupCodes(com.tiangong.hotel.domain.vo.HotelGroupDTO groupDTO);

    /**
     * 更加品牌编码查询品牌名称
     */
    List<HotelBrandResp> queryBrandByBrandCodes(HotelBrandDTO hotelBrandDTO);

    List<FuzzySupplierDTO> getAvailableSupplierInfoByAgentCodeAndSupplierName(FuzzyQueryDTO fuzzyQueryDTO);

    List<FuzzySupplierDTO> getAvailableSupplierByAgentCodeAndSupplierName(FuzzyQueryDTO fuzzyQueryDTO);
}
