package com.tiangong.fuzzyquery.service;

import com.github.pagehelper.PageInfo;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.fuzzyquery.dto.*;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

/**
 * @Auther: Owen
 * @Date: 2019/7/2 11:35
 * @Description:模糊查询service
 */
public interface FuzzyQueryService {

    /**
     * 模糊查询供应商
     */
    PaginationSupportDTO<FuzzySupplierDTO> querySupplier(@RequestBody FuzzyQueryDTO fuzzyQueryDTO);

    /**
     * 模糊查询分销商
     */
    PaginationSupportDTO<FuzzyAgentDTO> queryAgent(@RequestBody FuzzyQueryDTO fuzzyQueryDTO);

    /**
     * 查询渠道配置
     */
    List<FuzzyChannelConfigDTO> queryChannelConfig(String companyCode);

    /**
     * 查询渠道配置
     */
    List<FuzzyChannelConfigDTO> queryAgentChannelCode(Map<String, String> requestMap);

    /**
     * 查询分销商列表
     */
    List<FuzzyAgentDTO> queryAgentList(FuzzyQueryDTO fuzzyQueryDTO);

    /**
     * 查询供应商列表
     */
    List<FuzzySupplierDTO> querySupplierList(FuzzyQueryDTO fuzzyQueryDTO);

    /**
     * 根据客户编码和供应商名称(模糊匹配)获取此客户下的所有有效供应商信息
     */
    PaginationSupportDTO<FuzzySupplierDTO> queryAvailableSupplierByAgentCodeAndSupplierName(FuzzyQueryDTO fuzzyQueryDTO);
}
