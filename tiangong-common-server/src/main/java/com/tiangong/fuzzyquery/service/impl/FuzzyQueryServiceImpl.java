package com.tiangong.fuzzyquery.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.enums.ParamErrorEnum;
import com.tiangong.exception.SysException;
import com.tiangong.fuzzyquery.dto.FuzzyAgentDTO;
import com.tiangong.fuzzyquery.dto.FuzzyChannelConfigDTO;
import com.tiangong.fuzzyquery.dto.FuzzyQueryDTO;
import com.tiangong.fuzzyquery.dto.FuzzySupplierDTO;
import com.tiangong.fuzzyquery.mapper.FuzzyQueryMapper;
import com.tiangong.fuzzyquery.service.FuzzyQueryService;
import com.tiangong.keys.RedisKey;
import com.tiangong.organization.domain.OrgPO;
import com.tiangong.organization.remote.dto.AgentAccountConfig;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.util.CollUtilX;
import com.tiangong.util.StrUtilX;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * @Auther: Owen
 * @Date: 2019/7/2 11:34
 * @Description: 模糊查询接口
 */
@Slf4j
@Service("fuzzyQueryService")
public class FuzzyQueryServiceImpl implements FuzzyQueryService {

    @Autowired
    private FuzzyQueryMapper fuzzyQueryMapper;

    @Override
    public PaginationSupportDTO<FuzzySupplierDTO> querySupplier(FuzzyQueryDTO fuzzyQueryDTO) {
        PaginationSupportDTO<FuzzySupplierDTO> paginationSupportDTO = new PaginationSupportDTO<>();

        PageHelper.startPage(fuzzyQueryDTO.getCurrentPage(), fuzzyQueryDTO.getPageSize());
        List<FuzzySupplierDTO> fuzzySupplierDTOList = fuzzyQueryMapper.fuzzyQuerySupplier(fuzzyQueryDTO);
        PageInfo<FuzzySupplierDTO> page = new PageInfo<>(fuzzySupplierDTOList);
        paginationSupportDTO.setItemList(fuzzySupplierDTOList);
        paginationSupportDTO.setCurrentPage(page.getPageNum());
        paginationSupportDTO.setPageSize(page.getPageSize());
        paginationSupportDTO.setTotalCount(page.getTotal());
        paginationSupportDTO.setTotalPage(page.getPages());

        return paginationSupportDTO;
    }

    @Override
    public PaginationSupportDTO<FuzzyAgentDTO> queryAgent(FuzzyQueryDTO fuzzyQueryDTO) {
        PaginationSupportDTO<FuzzyAgentDTO> paginationSupportDTO = new PaginationSupportDTO<>();

        PageHelper.startPage(fuzzyQueryDTO.getCurrentPage(), fuzzyQueryDTO.getPageSize());
        List<FuzzyAgentDTO> fuzzyAgentDTOList = fuzzyQueryMapper.fuzzyQueryAgent(fuzzyQueryDTO);
        PageInfo<FuzzyAgentDTO> page = new PageInfo<>(fuzzyAgentDTOList);
        paginationSupportDTO.setItemList(fuzzyAgentDTOList);
        paginationSupportDTO.setCurrentPage(page.getPageNum());
        paginationSupportDTO.setPageSize(page.getPageSize());
        paginationSupportDTO.setTotalCount(page.getTotal());
        paginationSupportDTO.setTotalPage(page.getPages());

        return paginationSupportDTO;
    }

    @Override
    public List<FuzzyChannelConfigDTO> queryChannelConfig(String companyCode) {
        return fuzzyQueryMapper.queryChannelConfig(companyCode);
    }

    @Override
    public List<FuzzyChannelConfigDTO> queryAgentChannelCode(Map<String, String> requestMap) {
        return fuzzyQueryMapper.queryAgentChannelCode(requestMap);
    }

    @Override
    public List<FuzzyAgentDTO> queryAgentList(FuzzyQueryDTO fuzzyQueryDTO) {
        return fuzzyQueryMapper.fuzzyQueryAgent(fuzzyQueryDTO);
    }

    @Override
    public List<FuzzySupplierDTO> querySupplierList(FuzzyQueryDTO fuzzyQueryDTO) {
        return fuzzyQueryMapper.fuzzyQuerySupplier(fuzzyQueryDTO);
    }

    public List<OrgPO> queryAllAgentList() {
        OrgPO orgPO = new OrgPO();
        orgPO.setAvailableStatus(1);
        return fuzzyQueryMapper.select(orgPO);
    }

    /**
     * 根据客户编码和供应商名称(模糊匹配)获取此客户下的所有有效供应商信息
     *
     * @param fuzzyQueryDTO
     * @return
     */
    @Override
    public PaginationSupportDTO<FuzzySupplierDTO> queryAvailableSupplierByAgentCodeAndSupplierName(FuzzyQueryDTO fuzzyQueryDTO) {
        String agentCode = fuzzyQueryDTO.getAgentCode();
        if(StrUtil.isBlank(agentCode)) {
            return null;
        }

        AgentAccountConfig agentAccountConfig = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.AGENT_ACCOUNT_CONFIG, agentCode), AgentAccountConfig.class);
        if (agentAccountConfig == null) {
            throw new SysException(ParamErrorEnum.INVALID_AGENT_CODE);
        }

        PaginationSupportDTO<FuzzySupplierDTO> paginationSupportDTO = new PaginationSupportDTO<>();

        PageHelper.startPage(fuzzyQueryDTO.getCurrentPage(), fuzzyQueryDTO.getPageSize());
        List<FuzzySupplierDTO> fuzzySupplierDTOList = fuzzyQueryMapper.getAvailableSupplierInfoByAgentCodeAndSupplierName(fuzzyQueryDTO);
        PageInfo<FuzzySupplierDTO> page = new PageInfo<>(fuzzySupplierDTOList);

        //有就直接返回
        if (CollUtilX.isEmpty(fuzzySupplierDTOList)) {
            //对当前客户的白名单 + （对当前客户的黑名单取反-对别的客户白名单 ）
            fuzzySupplierDTOList = fuzzyQueryMapper.getAvailableSupplierByAgentCodeAndSupplierName(fuzzyQueryDTO);
            page = new PageInfo<>(fuzzySupplierDTOList);
        }

        paginationSupportDTO.setItemList(fuzzySupplierDTOList);
        paginationSupportDTO.setCurrentPage(page.getPageNum());
        paginationSupportDTO.setPageSize(page.getPageSize());
        paginationSupportDTO.setTotalCount(page.getTotal());
        paginationSupportDTO.setTotalPage(page.getPages());

        return paginationSupportDTO;
    }
}