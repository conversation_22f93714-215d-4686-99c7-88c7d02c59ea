package com.tiangong.fuzzyquery.server;

import com.tiangong.cloud.common.anno.AnonymousAccess;
import com.tiangong.cloud.commonbean.common.BaseController;
import com.tiangong.common.Response;
import com.tiangong.dto.common.CompanyDTO;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.fuzzyquery.dto.FuzzyAgentDTO;
import com.tiangong.fuzzyquery.dto.FuzzyChannelConfigDTO;
import com.tiangong.fuzzyquery.dto.FuzzyQueryDTO;
import com.tiangong.fuzzyquery.dto.FuzzySupplierDTO;
import com.tiangong.fuzzyquery.service.FuzzyQueryService;
import com.tiangong.user.dto.PurchaseManagerDTO;
import com.tiangong.user.dto.SaleManagerDTO;
import com.tiangong.user.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Auther: Owen
 * @Date: 2019/7/2 12:11
 * @Description:模糊查询server
 */
@RestController
@Slf4j
@RequestMapping("/common/fuzzy")
public class FuzzyQueryServer extends BaseController {

    @Autowired
    private FuzzyQueryService fuzzyQueryService;

    @Autowired
    private UserService userService;

    /**
     * 模糊查询供应商
     */
    @PostMapping("/querySupplier")
    @PreAuthorize("@syyo.check('fuzzy')")
    public Response<PaginationSupportDTO<FuzzySupplierDTO>> querySupplier(@RequestBody FuzzyQueryDTO fuzzyQueryDTO) {
        fuzzyQueryDTO.setCompanyCode(getCompanyCode());
        fuzzyQueryDTO.setAvailableStatus(1);
        return Response.success(fuzzyQueryService.querySupplier(fuzzyQueryDTO));
    }

    /**
     * 模糊查询供应商
     */
    @AnonymousAccess
    @PostMapping("/querySupplierAll")
    public Response<PaginationSupportDTO<FuzzySupplierDTO>> querySupplierAll(@RequestBody FuzzyQueryDTO fuzzyQueryDTO) {
        fuzzyQueryDTO.setCompanyCode(CompanyDTO.COMPANY_CODE);
        return Response.success(fuzzyQueryService.querySupplier(fuzzyQueryDTO));
    }

    /**
     * 模糊查询分销商
     */
    @AnonymousAccess
    @PostMapping("/queryAgent")
    public Response<PaginationSupportDTO<FuzzyAgentDTO>> queryAgent(@RequestBody FuzzyQueryDTO fuzzyQueryDTO) {
        fuzzyQueryDTO.setCompanyCode(CompanyDTO.COMPANY_CODE);
        return Response.success(fuzzyQueryService.queryAgent(fuzzyQueryDTO));
    }

    /**
     * 获取采购管理员列表
     */
    @PostMapping("/queryPurchaseManagerList")
    @PreAuthorize("@syyo.check('fuzzy')")
    public Response<List<PurchaseManagerDTO>> queryPurchaseManager(@RequestBody Map<String, String> requestMap) {
        requestMap.put("orgCode", super.getCompanyCode());
        return Response.success(userService.getPurchaseManagerList(requestMap));
    }

    /**
     * 获取销售经理列表
     */
    @PostMapping("/querySaleManagerList")
    @PreAuthorize("@syyo.check('fuzzy')")
    public Response<List<SaleManagerDTO>> querySaleManager(@RequestBody Map<String, String> requestMap) {
        requestMap.put("orgCode", super.getCompanyCode());
        return Response.success(userService.getSaleManagerList(requestMap));
    }

    /**
     * 查询渠道配置
     */
    @PostMapping("/queryChannelConfig")
    @PreAuthorize("@syyo.check('fuzzy')")
    public Response<List<FuzzyChannelConfigDTO>> queryChannelConfig() {
        Map<String, String> requestMap = new HashMap<>();
        requestMap.put("companyCode", getCompanyCode());
        return Response.success(fuzzyQueryService.queryChannelConfig(requestMap.get("companyCode")));
    }

    /**
     * 查询渠道配置
     */
    @PostMapping("/queryAgentChannelCode")
    @PreAuthorize("@syyo.check('fuzzy')")
    public Response<List<FuzzyChannelConfigDTO>> queryAgentChannelCode() {
        Map<String, String> requestMap = new HashMap<>();
        requestMap.put("companyCode", getCompanyCode());
        return Response.success(fuzzyQueryService.queryAgentChannelCode(requestMap));
    }

    /**
     * 查询分销商列表
     */
    @PostMapping("/queryAgentList")
    @AnonymousAccess
    public Response<List<FuzzyAgentDTO>> queryAgentList(@RequestBody FuzzyQueryDTO fuzzyQueryDTO) {
        return Response.success(fuzzyQueryService.queryAgentList(fuzzyQueryDTO));
    }

    /**
     * 查询供应商列表
     */
    @PostMapping("/querySupplierList")
    @AnonymousAccess
    public Response<List<FuzzySupplierDTO>> querySupplierList(@RequestBody FuzzyQueryDTO fuzzyQueryDTO) {
        fuzzyQueryDTO.setCompanyCode(CompanyDTO.COMPANY_CODE);
        return Response.success(fuzzyQueryService.querySupplierList(fuzzyQueryDTO));
    }

    /**
     * 根据客户编码和供应商名称(模糊匹配)获取此客户下的所有有效供应商信息
     */
    @AnonymousAccess
    @PostMapping("/queryAvailableSupplierByAgentCodeAndSupplierName")
    public Response<PaginationSupportDTO<FuzzySupplierDTO>> queryAvailableSupplierByAgentCodeAndSupplierName(@RequestBody FuzzyQueryDTO fuzzyQueryDTO) {
        fuzzyQueryDTO.setCompanyCode(CompanyDTO.COMPANY_CODE);
        return Response.success(fuzzyQueryService.queryAvailableSupplierByAgentCodeAndSupplierName(fuzzyQueryDTO));
    }

}
