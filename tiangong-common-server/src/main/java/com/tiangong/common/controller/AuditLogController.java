package com.tiangong.common.controller;

import com.tiangong.cloud.common.anno.AnonymousAccess;
import com.tiangong.cloud.commonbean.utils.PageVo;
import com.tiangong.common.Response;
import com.tiangong.common.domain.req.AuditLogReq;
import com.tiangong.common.domain.req.AuditLogSearchReq;
import com.tiangong.common.domain.req.AuditLogStatisticsReq;
import com.tiangong.common.domain.resp.AuditLogResp;
import com.tiangong.common.service.AuditLogService;
import com.tiangong.dto.common.AuditLogAddReq;
import com.tiangong.operatelog.domain.OperateLogSearchReq;
import com.tiangong.operatelog.domain.OperateLogStatisticsReq;
import com.tiangong.operatelog.domain.OperateLogStatisticsResp;
import com.tiangong.operatelog.domain.SaveAuditSettingReq;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-03-14 14:20:18
 */
@RestController
@RequestMapping("/common/auditLog")
public class AuditLogController {

    @Autowired
    private AuditLogService auditLogService;

    /**
     * 新增审计日志
     */
    @PostMapping("/auditLogAdd")
    @AnonymousAccess
    public Response<Object> add(@RequestBody AuditLogAddReq req) {
        auditLogService.auditLogAdd(req);
        return Response.success();
    }

    /**
     * 删除审计日志
     */
    @PostMapping("/auditLogDel")
    @PreAuthorize("@syyo.check('common:auditLog')")
    public Response<Object> del(@RequestBody AuditLogReq req) {
        auditLogService.auditLogDel(req);
        return Response.success();
    }

    /**
     * 编辑审计日志
     */
    @PostMapping("/auditLogEdit")
    @PreAuthorize("@syyo.check('common:auditLog')")
    public Response<Object> edit(@RequestBody AuditLogReq req) {
        auditLogService.auditLogEdit(req);
        return Response.success();
    }

    /**
     * 审计日志详情
     */
    @PostMapping("/auditLogDetail")
    public Response<AuditLogResp> findOne(@RequestBody AuditLogReq req) {
        return Response.success(auditLogService.auditLogDetail(req));
    }

    /**
     * 审计日志列表（全部）
     */
    @PostMapping("/auditLogList")
    public Response<List<AuditLogResp>> findAll(@RequestBody AuditLogReq req) {
        return Response.success(auditLogService.auditLogList(req));
    }

    /**
     * 审计日志列表（分页）
     */
    @PostMapping("/auditLogPage")
    public Response<PageVo> findPage(@RequestBody AuditLogSearchReq req) {
        return Response.success(auditLogService.auditLogPage(req));
    }

    /**
     * 审计日志查询(分页)
     */
    @PostMapping("/findAuditLogSearchPage")
    public Response<PageVo> findAuditLogSearchPage(@RequestBody AuditLogSearchReq req) {
        return Response.success(auditLogService.findAuditLogSearchPage(req));
    }

    /**
     * 审计日志查询(分页)(新)
     */
    @PostMapping("/findAuditLogSearch")
    public Response<PageVo> findAuditLogSearch(@RequestBody OperateLogSearchReq req) {
        return Response.success(auditLogService.findAuditLogSearch(req));
    }

    /**
     * 审计统计查询(分页)
     */
    @PostMapping("/findAuditLogStatisticsPage")
    public Response<PageVo> findAuditLogStatisticsPage(@RequestBody AuditLogStatisticsReq req) {
        return Response.success(auditLogService.findAuditLogStatisticsPage(req));
    }

    /**
     * 审计统计查询(分页)
     */
    @PostMapping("/findAuditLogStatistics")
    public Response<List<OperateLogStatisticsResp>> findAuditLogStatistics(@RequestBody OperateLogStatisticsReq req) {
        return Response.success(auditLogService.findAuditLogStatistics(req));
    }

    @PostMapping("/saveAuditSetting")
    public Response<Object> saveAuditSetting(@RequestBody SaveAuditSettingReq req) {
        auditLogService.saveAuditSetting(req);
        return Response.success();
    }

    @PostMapping("/queryAuditSetting")
    public Response<SaveAuditSettingReq> queryAuditSetting() {
        return Response.success(auditLogService.queryAuditSetting());
    }
}
