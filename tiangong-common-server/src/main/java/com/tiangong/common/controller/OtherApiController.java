package com.tiangong.common.controller;

import com.tiangong.common.Response;
import com.tiangong.common.domain.req.AddMenuPermissionsReq;
import com.tiangong.common.domain.req.AddRoleAndMenuReq;
import com.tiangong.common.domain.req.EditLogSettingReq;
import com.tiangong.common.domain.resp.QueryLogSettingResp;
import com.tiangong.common.service.OtherApiService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Date 2023/7/25 14:53
 * @Description:
 */
@RestController
@RequestMapping("/common/otherApi")
public class OtherApiController {

    @Autowired
    private OtherApiService otherApiService;

    /**
     * 添加角色以及对应的菜单
     */
    @PostMapping("/addRoleAndMenu")
    public Response<Object> addRoleAndMenu(@RequestBody AddRoleAndMenuReq req) {
        otherApiService.addRoleAndMenu(req);
        return Response.success();
    }

    /**
     * 添加菜单权限
     */
    @PostMapping("/addMenuPermissions")
    public Response<Object> addMenuPermissions(@RequestBody AddMenuPermissionsReq req) {
        otherApiService.addMenuPermissions(req);
        return Response.success();
    }

    /**
     * 查询日志设置
     */
    @PostMapping("/queryLogSetting")
    public Response<QueryLogSettingResp> queryLogSetting() {
        return Response.success(otherApiService.queryLogSetting());
    }

    /**
     * 编辑日志设置
     */
    @PostMapping("/editLogSetting")
    public Response<Object> editLogSetting(@RequestBody EditLogSettingReq req) {
        otherApiService.editLogSetting(req);
        return Response.success();
    }

    /**
     * 强制更新产品筛选策略缓存
     */
    @PostMapping("/forceUpdateProductFilterStrategyCache")
    public Response<Object> forceUpdateProductFilterStrategyCache() {
        otherApiService.forceUpdateProductFilterStrategyCache();
        return Response.success();
    }

}
