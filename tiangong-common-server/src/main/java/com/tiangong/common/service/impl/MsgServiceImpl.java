package com.tiangong.common.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tiangong.common.Response;
import com.tiangong.common.domain.entity.MsgEntity;
import com.tiangong.common.domain.req.QueryMsgPage;
import com.tiangong.common.domain.req.ReadMsgReq;
import com.tiangong.common.mapper.MsgMapper;
import com.tiangong.common.service.MsgService;
import com.tiangong.dto.common.AddMsgDTO;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.enums.ResultCodeEnum;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2023/12/15 10:59
 */
@Service
public class MsgServiceImpl extends ServiceImpl<MsgMapper, MsgEntity> implements MsgService {
    @Override
    public PaginationSupportDTO<MsgEntity> queryMsgPage(QueryMsgPage queryMsgPage) {
        LambdaQueryWrapper<MsgEntity> wrapper = new LambdaQueryWrapper<>();
        if (queryMsgPage.getIsRead() != null){
            wrapper.eq(MsgEntity::getIsRead, queryMsgPage.getIsRead());
        }
        wrapper.orderByDesc(MsgEntity::getCreatedDt);
        IPage<MsgEntity> page = new Page<>(queryMsgPage.getCurrentPage(), queryMsgPage.getPageSize());
        IPage<MsgEntity> msgEntityIPage = this.getBaseMapper().selectPage(page, wrapper);
        PaginationSupportDTO<MsgEntity> paginationSupportDTO = new PaginationSupportDTO<>();
        paginationSupportDTO = paginationSupportDTO.getPaginationSupportDTO(msgEntityIPage);
        return paginationSupportDTO;
    }

    @Override
    public List<MsgEntity> queryMsgListToNoRead() {
        return this.list(new LambdaQueryWrapper<MsgEntity>().eq(MsgEntity::getIsRead, 0));
    }

    @Override
    public void addMsg(AddMsgDTO dto) {
        MsgEntity entity = new MsgEntity();
        entity.setIsRead(0);
        entity.setCreatedDt(new Date());
        entity.setMsgType(dto.getMsgType());
        entity.setMsgContent(dto.getMsgContent());
        this.save(entity);
    }

    @Override
    public void readMsg() {
        this.update(new LambdaUpdateWrapper<MsgEntity>().set(MsgEntity::getIsRead, 1).eq(MsgEntity::getIsRead, 0));
    }

    @Override
    public void readOneMsg(ReadMsgReq readMsgReq) {
        this.update(new LambdaUpdateWrapper<MsgEntity>().set(MsgEntity::getIsRead, 1).eq(MsgEntity::getId, readMsgReq.getId()));
    }
}
