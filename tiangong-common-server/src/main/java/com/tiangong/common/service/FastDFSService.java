package com.tiangong.common.service;


import com.tiangong.cloud.common.domain.FileUpload;
import org.springframework.web.multipart.MultipartFile;

/**
 * @Auther: wangzhong
 * @Date: 2020/8/18 10:17
 * @Description:
 */
public interface FastDFSService {

    /**
     * 上传文件
     *
     * @param file
     * @return
     */
    FileUpload uploadFast(MultipartFile file);


    void download(String url);

    int delete(String url);
}
