package com.tiangong.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tiangong.common.Response;
import com.tiangong.common.domain.entity.MsgEntity;
import com.tiangong.common.domain.req.QueryMsgPage;
import com.tiangong.common.domain.req.ReadMsgReq;
import com.tiangong.dto.common.AddMsgDTO;
import com.tiangong.dto.common.PaginationSupportDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023/12/15 10:59
 */
public interface MsgService extends IService<MsgEntity> {

    /**
     * 分页查询消息通知
     */
    PaginationSupportDTO<MsgEntity> queryMsgPage(QueryMsgPage queryMsgPage);

    /**
     * 查询未读消息列表
     */
    List<MsgEntity> queryMsgListToNoRead();

    /**
     * 新增消息通知
     */
    void addMsg(AddMsgDTO dto);

    /**
     * 读取消息
     */
    void readMsg();

    /**
     * 读取单条消息
     */
    void readOneMsg(ReadMsgReq readMsgReq);
}
