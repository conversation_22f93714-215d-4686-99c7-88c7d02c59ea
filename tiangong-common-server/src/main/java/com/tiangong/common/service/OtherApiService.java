package com.tiangong.common.service;

import com.tiangong.common.domain.req.AddMenuPermissionsReq;
import com.tiangong.common.domain.req.AddRoleAndMenuReq;
import com.tiangong.common.domain.req.EditLogSettingReq;
import com.tiangong.common.domain.resp.QueryLogSettingResp;

/**
 * <AUTHOR>
 * @Date 2023/7/25 14:54
 * @Description:
 */
public interface OtherApiService {

    /**
     * 添加角色以及对应的菜单
     */
    void addRoleAndMenu(AddRoleAndMenuReq req);

    /**
     * 添加菜单权限
     */
    void addMenuPermissions(AddMenuPermissionsReq req);

    /**
     * 查询日志设置
     */
    QueryLogSettingResp queryLogSetting();

    /**
     * 编辑日志设置
     */
    void editLogSetting(EditLogSettingReq req);

    /**
     * 强制更新产品筛选策略缓存
     */
    void forceUpdateProductFilterStrategyCache();

}
