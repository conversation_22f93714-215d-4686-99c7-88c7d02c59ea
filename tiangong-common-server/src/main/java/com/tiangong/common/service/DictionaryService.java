package com.tiangong.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tiangong.common.Response;
import com.tiangong.common.domain.entity.DictionaryEntity;
import com.tiangong.common.domain.resp.DictionaryResp;
import com.tiangong.common.req.QueryDictionaryReq;
import com.tiangong.dto.common.PaginationSupportDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023/11/18 11:28
 */
public interface DictionaryService extends IService<DictionaryEntity> {

    /**
     * 查询字典表分页
     */
    PaginationSupportDTO<DictionaryEntity> queryDictionaryPage(QueryDictionaryReq req);

    /**
     * 查询字典表列表
     */
    List<DictionaryResp> queryDictionaryList(QueryDictionaryReq req);

    /**
     * 查询字典表分类类型
     */
    List<DictionaryEntity> queryDictionaryTypeList();

    /**
     * 新增或修改字典表数据
     */
    void addOrUpdateDictionary(DictionaryEntity dictionaryEntity);
}
