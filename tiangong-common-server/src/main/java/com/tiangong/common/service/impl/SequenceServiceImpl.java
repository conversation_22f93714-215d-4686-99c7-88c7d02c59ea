package com.tiangong.common.service.impl;

import com.tiangong.common.domain.SequencePO;
import com.tiangong.common.mapper.SequenceMapper;
import com.tiangong.common.service.SequenceService;
import com.tiangong.enums.SystemCodeEnum;
import com.tiangong.finance.OrgDTO;
import com.tiangong.initializer.CommonInitializer;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.util.DateUtilX;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 用途：TODO
 *
 * @author：Owen
 */
@Slf4j
@Service("sequenceService")
public class SequenceServiceImpl implements SequenceService {

    @Autowired
    private SequenceMapper sequenceMapper;
    public static final String FLAG = "_flag";

    public static final String SEQNAME = "seqName";

    public static final String CREATECODE = "createcode_flag";

    @Override
    public void createCode(Map<String, String> requestMap) {
        try {
            if (null == RedisTemplateX.get(CREATECODE)) {
                RedisTemplateX.set(CREATECODE, "1");//流程加锁
                List<SequencePO> sequencePOList = sequenceMapper.selectAll();
                if (null != requestMap && StrUtilX.isNotEmpty(requestMap.get(SEQNAME))) {
                    for (SequencePO sequencePO : sequencePOList) {
                        if (sequencePO.getSeqName().equals(requestMap.get(SEQNAME))) {
                            createCode(sequencePO);
                        }
                    }
                } else {
                    for (SequencePO sequencePO : sequencePOList) {
                        createCode(sequencePO);
                    }
                }
                RedisTemplateX.delete(CREATECODE);//释放锁
            }
        } catch (Exception e) {
            log.error("批量生成编码失败", e);
        } finally {
            RedisTemplateX.delete(CREATECODE);//释放锁
        }
    }

    /**
     * 生成编码
     */
    private void createCode(SequencePO sequencePO) {
        try {
            if (null == RedisTemplateX.get(sequencePO.getSeqName() + FLAG)) {//获取锁
                Long listCount = RedisTemplateX.lLen(sequencePO.getSeqName());
                if (null == listCount) {//list查询错误或者格式不对，直接删除
                    RedisTemplateX.delete(sequencePO.getSeqName());
                }

                if (listCount.equals(0L)) {//列表为空时，创建编码
                    RedisTemplateX.set(sequencePO.getSeqName() + FLAG, "1");//加锁
                    List<String> codeList = new ArrayList<>();
                    String startLetter = SystemCodeEnum.getLetterByCode(sequencePO.getSeqName());
                    // 订单编码或供货单编码需要加商家简码
                    if (sequencePO.getSeqName().equals(SystemCodeEnum.ORDERCODE.code) || sequencePO.getSeqName().equals(SystemCodeEnum.SUPPLYORDERCODE.code)) {
                        OrgDTO orgDTO = CommonInitializer.getOrgInfo();
                        if (StrUtilX.isNotEmpty(orgDTO.getOrgBrevityCode())) {
                            startLetter += orgDTO.getOrgBrevityCode();
                        }
                    }
                    if (sequencePO.getSeqName().equals(SystemCodeEnum.ORDERCODE.code) || sequencePO.getSeqName().equals(SystemCodeEnum.SUPPLYORDERCODE.code)
                            || sequencePO.getSeqName().equals(SystemCodeEnum.SUPPLIERSETTLEWORKORDERCODE.code)
                            || sequencePO.getSeqName().equals(SystemCodeEnum.SEQ_SETTLE_JOB_CODE.code)
                            || sequencePO.getSeqName().equals(SystemCodeEnum.SEQ_SETTLE_PAY_CODE.code)
                            || sequencePO.getSeqName().equals(SystemCodeEnum.SEQ_ORDER_REFUND_TASK_CODE.code)
                            || sequencePO.getSeqName().equals(SystemCodeEnum.SEQ_ORDER_DEDUCTION_TASK_CODE.code)
                            || sequencePO.getSeqName().equals(SystemCodeEnum.SEQ_ORDER_STATEMENT_TASK_CODE.code)
                            || sequencePO.getSeqName().equals(SystemCodeEnum.CHANGE_WORD_ORDER_TASK_CODE.code)) {
                        String dayStr = DateUtilX.getCurrentDateStr(2).replace("-", "");
                        startLetter += dayStr.substring(2, dayStr.length());
                    }

                    for (int i = 1; i <= 100; i++) {
                        codeList.add(startLetter + (sequencePO.getCurrentVal() + i));
                    }

                    sequencePO.setCurrentVal(sequencePO.getCurrentVal() + 100L);
                    sequenceMapper.updateByPrimaryKey(sequencePO);//将最新值写入数据库
                    RedisTemplateX.lLeftPushAll(sequencePO.getSeqName(), codeList);//将编码写入redis
                    RedisTemplateX.delete(sequencePO.getSeqName() + FLAG);//释放锁
                }
            }
        } catch (Exception e) {
            log.error("生成编码失败！", e);
        } finally {
            RedisTemplateX.delete(sequencePO.getSeqName() + FLAG);//释放锁
        }
    }
}
