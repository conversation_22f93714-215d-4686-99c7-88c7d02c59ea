package com.tiangong.common.service;


import com.baomidou.mybatisplus.extension.service.IService;

import com.tiangong.cloud.commonbean.utils.PageVo;
import com.tiangong.dto.common.AuditLogAddReq;
import com.tiangong.common.domain.entity.AuditLogEntity;
import com.tiangong.common.domain.req.AuditLogReq;
import com.tiangong.common.domain.req.AuditLogSearchReq;
import com.tiangong.common.domain.req.AuditLogStatisticsReq;
import com.tiangong.common.domain.resp.AuditLogResp;
import com.tiangong.operatelog.domain.OperateLogSearchReq;
import com.tiangong.operatelog.domain.OperateLogStatisticsReq;
import com.tiangong.operatelog.domain.OperateLogStatisticsResp;
import com.tiangong.operatelog.domain.SaveAuditSettingReq;


import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-03-14 14:20:18
 */
public interface AuditLogService extends IService<AuditLogEntity> {

    /**
     * 新增审计日志
     */
    void auditLogAdd(AuditLogAddReq req);

    /**
     * 删除审计日志
     */
    void auditLogDel(AuditLogReq req);

    /**
     * 编辑审计日志
     */
    void auditLogEdit(AuditLogReq req);

    /**
     * 审计日志详情
     */
    AuditLogResp auditLogDetail(AuditLogReq req);

    /**
     * 审计日志列表（全部）
     */
    List<AuditLogResp> auditLogList(AuditLogReq req);

    /**
     * 审计日志列表（分页）
     */
    PageVo auditLogPage(AuditLogSearchReq req);

    /**
     * 审计日志查询(分页)
     */
    PageVo findAuditLogSearchPage(AuditLogSearchReq req);

    /**
     * 审计统计查询(分页)
     */
    PageVo findAuditLogStatisticsPage(AuditLogStatisticsReq req);

    /**
     * 审计日志查询(分页)(新)
     */
    PageVo findAuditLogSearch(OperateLogSearchReq req);

    /**
     * 审计统计查询(分页)(新)
     */
    List<OperateLogStatisticsResp> findAuditLogStatistics(OperateLogStatisticsReq req);

    /**
     * 保存审计设置
     */
    void saveAuditSetting(SaveAuditSettingReq req);

    /**
     * 查询审计日志设置
     */
    SaveAuditSettingReq queryAuditSetting();

}

