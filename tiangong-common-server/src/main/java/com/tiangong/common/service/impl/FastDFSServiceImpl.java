package com.tiangong.common.service.impl;


import com.luhuiguo.fastdfs.domain.StorePath;
import com.luhuiguo.fastdfs.service.FastFileStorageClient;
import com.tiangong.cloud.common.domain.FileUpload;
import com.tiangong.common.service.FastDFSService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * @Auther: wangzhong
 * @Date: 2020/8/18 10:17
 * @Description:
 */
@Slf4j
@Service
public class FastDFSServiceImpl implements FastDFSService {

    private final static String GROUP = "group1";

    @Autowired
    private FastFileStorageClient storageClient;

    //@Value("${fdfs.domain}" )
    private String domain = "***********:18888";

    @Autowired
    HttpServletResponse response;

    @Override
    public FileUpload uploadFast(MultipartFile myFile) {
        FileUpload fileUpdate = new FileUpload();
        // myFile.getOriginalFilename():取到文件的名字
        // FilenameUtils.getExtension(""):取到一个文件的后缀名
        String filename = myFile.getOriginalFilename();
        String suffix = filename.substring(filename.lastIndexOf(".") + 1);
        // 计算文件大小
        long fileS = myFile.getSize();
//        String fileSizeString = FileUtil.formetFileSize(fileS);
        // group1:指storage服务器的组名
        // myFile.getInputStream():指这个文件中的输入流
        // myFile.getSize():文件的大小
        // 这一行是通过storageClient将文件传到storage容器
        StorePath uploadFile = null;
        try {
            uploadFile = storageClient.uploadFile(GROUP, myFile.getInputStream(), fileS, suffix);
            String fullPath = uploadFile.getFullPath();
            fileUpdate.setFileName(filename);

            String fullUrl = "http://" + domain + "/" + fullPath;

            fileUpdate.setFileUrl(fullUrl);
            fileUpdate.setFileSize(fileS);
            fileUpdate.setFileFormat(suffix);
        } catch (IOException e) {
            log.error("上传文件异常", e);
        }
        // 返回它在storage容器的的路径
        return fileUpdate;
    }

    @Override
    public void download(String url) {
        String[] split = url.split(GROUP);
        String suffix = url.substring(url.lastIndexOf(".") + 1);
        if (split.length == 2) {
            String s = split[1];
            String key = s.substring(1, s.length());

            byte[] bytes = storageClient.downloadFile(GROUP, key);
            download(bytes, suffix);
        }
    }

    /**
     * 文件下载
     */
    public void download(byte[] bytes, String suffix) {
        response.setCharacterEncoding("utf-8");
        response.setContentType("application/octet-stream");
        try {
            response.setHeader("Content-disposition", "attachment;filename=" + System.currentTimeMillis() + "." + suffix);
            ServletOutputStream outputStream = response.getOutputStream();
            outputStream.write(bytes);
            response.flushBuffer();
        } catch (Exception var14) {
            log.error("文件下载异常", var14);
        }
    }

    @Override
    public int delete(String url) {
        String[] split = url.split(GROUP);
        if (split.length == 2) {
            String s = split[1];
            String key = s.substring(1, s.length());
            storageClient.deleteFile(GROUP, key);
        }
        return 0;
    }


}
