package com.tiangong.common.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tiangong.common.domain.entity.DictionaryEntity;
import com.tiangong.common.domain.resp.DictionaryResp;
import com.tiangong.common.mapper.DictionaryMapper;
import com.tiangong.common.req.QueryDictionaryReq;
import com.tiangong.common.service.DictionaryService;
import com.tiangong.dis.dto.CurrencyCoinDTO;
import com.tiangong.dis.remote.InitRemote;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.enums.ErrorCodeEnum;
import com.tiangong.exception.SysException;
import com.tiangong.util.StrUtilX;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2023/11/18 11:28
 */
@Service
public class DictionaryServiceImpl extends ServiceImpl<DictionaryMapper, DictionaryEntity> implements DictionaryService {

    @Autowired
    private InitRemote initRemote;

    @Override
    public PaginationSupportDTO<DictionaryEntity> queryDictionaryPage(QueryDictionaryReq req) {
        LambdaQueryWrapper<DictionaryEntity> wrapper = new LambdaQueryWrapper<>();
        if (!StringUtils.isEmpty(req.getType())) {
            wrapper.eq(DictionaryEntity::getType, req.getType());
        }
        if(req.getType().equals("coin") && !StringUtils.isEmpty(req.getKey())){
            wrapper.like(DictionaryEntity::getTValue, req.getKey()).or();
            wrapper.like(DictionaryEntity::getContent, req.getKey());
        }else if (!StringUtils.isEmpty(req.getKey())) {
            wrapper.eq(DictionaryEntity::getTKey, req.getKey());
        }

        wrapper.orderByDesc(DictionaryEntity::getId);
        IPage<DictionaryEntity> page = new Page<>(req.getCurrentPage(), req.getPageSize());
        IPage<DictionaryEntity> list = this.baseMapper.selectPage(page, wrapper);
        PaginationSupportDTO<DictionaryEntity> paginationSupportDTO = new PaginationSupportDTO<>();
        paginationSupportDTO = paginationSupportDTO.getPaginationSupportDTO(list);
        return paginationSupportDTO;
    }

    @Override
    public List<DictionaryResp> queryDictionaryList(QueryDictionaryReq req) {
        LambdaQueryWrapper<DictionaryEntity> wrapper = new LambdaQueryWrapper<>();
        if (!StringUtils.isEmpty(req.getType())) {
            wrapper.eq(DictionaryEntity::getType, req.getType());
        }
        if (!StringUtils.isEmpty(req.getKey())) {
            wrapper.eq(DictionaryEntity::getTKey, req.getKey());
        }
        // 查询币种 模糊匹配
        if (!StringUtils.isEmpty(req.getType()) && req.getType().equals("coin") && !StringUtils.isEmpty(req.getCoinName())) {
            wrapper.like(DictionaryEntity::getTValue, req.getCoinName()).or();
            wrapper.like(DictionaryEntity::getContent, req.getCoinName());
        }
        wrapper.orderByDesc(DictionaryEntity::getId);
        List<DictionaryEntity> list = this.list(wrapper);

        List<DictionaryResp> respList = new ArrayList<>();
        for (DictionaryEntity dictionaryEntity : list) {
            DictionaryResp resp = new DictionaryResp();
            BeanUtils.copyProperties(dictionaryEntity, resp);
            resp.setCoinName(dictionaryEntity.getTValue());
            if (StrUtilX.isNotEmpty(dictionaryEntity.getContent())) {
                resp.setCoinName(dictionaryEntity.getTValue() + "(" + dictionaryEntity.getContent() + ")");
            }
            respList.add(resp);
        }
        return respList;
    }

    @Override
    public List<DictionaryEntity> queryDictionaryTypeList() {
        LambdaQueryWrapper<DictionaryEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.groupBy(DictionaryEntity::getType);
        wrapper.select(DictionaryEntity::getType, DictionaryEntity::getTypeName);
        return this.list(wrapper);
    }


    @Override
    public void addOrUpdateDictionary(DictionaryEntity dictionaryEntity) {
        if (dictionaryEntity.getId() == null) {
            DictionaryEntity one = this.getOne(new LambdaQueryWrapper<DictionaryEntity>().eq(DictionaryEntity::getTValue, dictionaryEntity.getTValue()).last(" limit 1"));
            if (one != null) {
                throw new SysException(ErrorCodeEnum.DIRECTION_IS_ALREADY);
            }
            int count = this.count(new LambdaQueryWrapper<DictionaryEntity>().eq(DictionaryEntity::getType, dictionaryEntity.getType()));
            dictionaryEntity.setTKey(count);
            this.save(dictionaryEntity);
        } else {
            this.updateById(dictionaryEntity);
        }
        // 更新币种信息时需要更新缓存
        if (dictionaryEntity.getType().equals("coin")) {
            CurrencyCoinDTO currencyCoinDTO = new CurrencyCoinDTO();
            currencyCoinDTO.setKey(dictionaryEntity.getTKey());
            currencyCoinDTO.setValue(dictionaryEntity.getTValue());
            currencyCoinDTO.setContent(dictionaryEntity.getContent());
            initRemote.initExchangeCoin(currencyCoinDTO);
        }
    }
}
