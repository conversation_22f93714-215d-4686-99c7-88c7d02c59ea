package com.tiangong.common.domain.resp;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2023/3/14 19:16
 * @Description:
 */
@Data
public class AuditLogSearchResp implements Serializable {

    /**
     * 创建时间
     */
    private Date createdDt;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 事件类型
     * 0：系统级事件
     * <p>
     * 1：业务级事件
     */
    private Integer eventType;
    /**
     * 事件描述
     */
    private String eventDescription;
    /**
     * 事件结果
     * 0：异常
     * <p>
     * 1：正常
     */
    private Integer eventResult;


}
