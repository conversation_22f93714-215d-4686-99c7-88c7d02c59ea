package com.tiangong.common.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @create 2023/12/15 10:55
 */
@Data
@TableName("t_org_msg")
public class MsgEntity {

    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 是否已读 0否 1是
     */
    private Integer isRead;

    /**
     * 消息类型：1在线支付结果通知 2公付订单提示通知
     */
    private Integer msgType;

    /**
     * 消息内容
     */
    private String msgContent;

    /**
     * 消息时间
     */
    private Date createdDt;
}
