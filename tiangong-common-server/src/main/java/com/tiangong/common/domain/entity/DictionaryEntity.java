package com.tiangong.common.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @create 2023/11/18 11:49
 */
@Data
@TableName("t_dictionary")
public class DictionaryEntity {

    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 类型
     */
    @NotEmpty(message = "EMPTY_PARAM_TYPE")
    private String type;

    /**
     * 类型名
     */
    @NotEmpty(message = "EMPTY_PARAM_TYPENAME")
    private String typeName;

    //@NotEmpty(message = "key值不能为空")
    private Integer tKey;

    @NotEmpty(message = "EMPTY_PARAM_TYPEVALUE")
    private String tValue;

    //@NotEmpty(message = "描述名称不能为空")
    private String content;
}
