package com.tiangong.common.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 表的实体类，参数和表字段一一对应
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-03-14 14:20:18
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
@TableName("t_audit_log")
public class AuditLogEntity extends Model<AuditLogEntity> {

    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    /**
     * 创建时间
     */
    private Date createdDt;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 事件类型
     * 0：系统级事件
     * <p>
     * 1：业务级事件
     */
    private Integer eventType;
    /**
     * 事件描述
     */
    private String eventDescription;
    /**
     * 事件结果
     * 0：异常
     * <p>
     * 1：正常
     */
    private Integer eventResult;
    /**
     * 用户id
     */
    private Integer userId;
    /**
     * 登陆账号
     */
    private String userAccount;
    /**
     * ip地址
     */
    private String ipAddress;
    /**
     * 目标id
     */
    private String targetId;
    /**
     * 目标
     */
    private String target;

    /**
     * 目标类型0：用户1：订单
     */
    private Integer targetType;

}
