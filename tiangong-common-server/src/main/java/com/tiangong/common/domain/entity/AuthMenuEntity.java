package com.tiangong.common.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2023/7/26 16:32
 * @Description:
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@TableName("t_auth_menu")
public class AuthMenuEntity extends Model<AuthMenuEntity> {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 菜单名称
     */
    private String menuName;

    /**
     * 菜单编码
     */
    private String menuCode;

    /**
     * 目录等级
     */
    private Integer menuLevel;

    /**
     * 菜单在当前层级排序
     */
    private Integer menuRank;

    /**
     * 父节点code
     */
    private String parentCode;

    /**
     * 后台url
     */
    private String backEndUrl;


    /**
     * 哪个系统的角色(0.运营端，1.供应端， 2.平台端，3.营销端)
     */
    private Integer type;

    /**
     * 有效性
     */
    private Integer active;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdDt;

    /**
     * 修改人
     */
    private String updatedBy;

    /**
     * 修改时间
     */
    private Date updatedDt;

    /**
     * 前端url
     */
    private String frontEndUrl;

    /**
     * 打开方式1当前页签2新页签
     */
    private String openType;

}
