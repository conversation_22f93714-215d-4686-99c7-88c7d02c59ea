package com.tiangong.common.domain.resp;

import lombok.Data;

import java.util.Date;

import java.io.Serializable;

/**
 * 返回参数
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-03-14 14:20:18
 */
@Data
public class AuditLogResp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     *
     */
    private Integer id;
    /**
     * 创建时间
     */
    private Date createdDt;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 事件类型
     * 0：系统级事件
     * <p>
     * 1：业务级事件
     */
    private Integer eventType;
    /**
     * 事件描述
     */
    private String eventDescription;
    /**
     * 事件结果
     * 0：异常
     * <p>
     * 1：正常
     */
    private Integer eventResult;
    /**
     * 用户id
     */
    private Integer userId;
    /**
     * 登陆账号
     */
    private String userAccount;
    /**
     * ip地址
     */
    private String ipAddress;
    /**
     * 目标id
     */
    private String targetId;
    /**
     * 目标
     */
    private String target;
    /**
     * 目标类型
     * 0：用户
     * <p>
     * 1：订单
     */
    private Integer targetType;
}