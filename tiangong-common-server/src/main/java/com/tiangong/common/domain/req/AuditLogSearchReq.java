package com.tiangong.common.domain.req;

import com.tiangong.cloud.common.domain.BasePage;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2023/3/14 17:21
 * @Description:
 */
@Data
public class AuditLogSearchReq extends BasePage implements Serializable {

    private String operateAccount;


    /**
     * 事件类型
     * 0：系统级事件
     * <p>
     * 1：业务级事件
     */
    private Integer logType;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date operateStartDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date operateEndDate;

    /**
     * 事件结果
     * 0：异常
     * <p>
     * 1：正常
     */
    private Integer operateResult;


}
