package com.tiangong.common.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 表的实体类，参数和表字段一一对应
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-04-21 09:23:04
 */
@Data
@TableName("t_pro_product_filter_strategy")
public class ProProductFilterStrategyEntity extends Model<ProProductFilterStrategyEntity> {

    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    /**
     * 客户名称
     */
    private String agentName;
    /**
     * 客户编码
     */
    private String agentCode;
    /**
     * 供应商名称
     */
    private String supplierName;
    /**
     * 供应商编码
     */
    private String supplierCode;
    /**
     * 适用范围(0所有酒店 1指定酒店集团 2指定酒店)
     */
    private Integer scopeOfApplication;
    /**
     * 酒店Id或酒店集团Id
     */
    private String targetCode;
    /**
     * 酒店名称或酒店集团名称
     */
    private String targetName;
    /**
     * 过滤策略(0【取消规则】不优于其他供应商产品 1【确认时效】不优于其他供应商产品 2协议标签与其他供应商产品不一致)
     */
    private String filterStrategy;
    /**
     * 价格策略类型（0加数值 1减数值 2加百分比 3减百分比 4等于）
     */
    private Integer priceStrategyAdjustmentType;
    /**
     * 价格策略
     */
    private BigDecimal priceStrategy;
    /**
     * 数据创建人
     */
    private String createdBy;
    /**
     * 数据创建时间
     */
    private Date createdDt;
    /**
     * 修改人
     */
    private String updatedBy;
    /**
     * 修改时间
     */
    private Date updatedDt;

}
