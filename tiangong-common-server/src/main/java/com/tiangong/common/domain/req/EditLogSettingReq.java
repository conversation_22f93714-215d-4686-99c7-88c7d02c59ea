package com.tiangong.common.domain.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2023/7/26 14:30
 * @Description:
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class EditLogSettingReq implements Serializable {

    /**
     * 日志设置
     * 0:关闭记录
     * 1:开启记录
     */
    private Integer logSetting;

}
