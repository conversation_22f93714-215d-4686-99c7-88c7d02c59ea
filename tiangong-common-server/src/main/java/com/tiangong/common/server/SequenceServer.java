package com.tiangong.common.server;

import com.tiangong.annotations.SlsLog;
import com.tiangong.cloud.common.anno.AnonymousAccess;
import com.tiangong.common.Response;
import com.tiangong.common.service.SequenceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
@Slf4j
@RequestMapping("/common/sequence")
public class SequenceServer {

    @Autowired
    private SequenceService sequenceService;

    /**
     * 生成编码
     */
    @PostMapping("/createCode")
    @AnonymousAccess
    public Response<Object> creteCode(@RequestBody Map<String, String> requestMap) {
        sequenceService.createCode(requestMap);
        return Response.success();
    }

}
