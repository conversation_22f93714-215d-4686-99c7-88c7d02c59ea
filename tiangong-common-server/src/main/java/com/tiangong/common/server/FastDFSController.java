package com.tiangong.common.server;


import com.tiangong.cloud.common.anno.AnonymousAccess;
import com.tiangong.cloud.common.domain.FileUpload;
import com.tiangong.common.Response;
import com.tiangong.common.service.FastDFSService;
import com.tiangong.enums.ErrorCodeEnum;
import com.tiangong.util.StrUtilX;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.Arrays;
import java.util.List;


/**
 * @Auther: wangzhong
 * @Date: 2020/8/18 10:17
 * @Description: fast文件上传
 */

@RestController
@RequestMapping("/common/fastDfs/file")
public class FastDFSController {

    @Autowired
    private FastDFSService fileService;

    /**
     * 上传
     */
    @AnonymousAccess
    @PostMapping("/upload")
    public Response<FileUpload> upload(MultipartFile file) {
        String filename = file.getOriginalFilename();
        if (StrUtilX.isNotEmpty(filename)) {
            String suffix = filename.substring(filename.lastIndexOf(".") + 1);
            List<String> list = Arrays.asList("png", "jpeg", "gif", "pdf", "txt", "doc", "docx", "ppt", "xls", "xlsx");
            if (list.contains(suffix)) {
                return Response.success(fileService.uploadFast(file));
            } else {
                return Response.error(ErrorCodeEnum.FAIL.errorCode, "非法文件上传!");
            }
        } else {
            return Response.error(ErrorCodeEnum.FAIL.errorCode, "文件不能为空!");
        }

    }

    @AnonymousAccess
    @PostMapping("/download")
    public void download(@RequestBody FileUpload upload) {
        fileService.download(upload.getFileUrl());
    }

    @AnonymousAccess
    @PostMapping("/delete")
    public Response<Object> delete(String url) {
        return Response.success(fileService.delete(url));
    }


}
