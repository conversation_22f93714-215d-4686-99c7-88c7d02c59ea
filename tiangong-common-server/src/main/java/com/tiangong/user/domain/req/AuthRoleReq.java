package com.tiangong.user.domain.req;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 角色表
 * 请求参数
 */
@Data
public class AuthRoleReq implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 角色ID
     */
    private Integer roleId;
    /**
     * 角色名称
     */
    private String roleName;
    /**
     * 角色编码
     */
    private String roleCode;
    /**
     * 备注
     */
    private String remark;
    /**
     * 是否启用
     */
    private Integer enable;

    private List<Integer> menuIdList;

    /**
     * 操作人
     */
    private String operator;
}