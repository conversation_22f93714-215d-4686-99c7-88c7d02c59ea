package com.tiangong.user.server;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.tiangong.ValidatedFilter;
import com.tiangong.annotations.SlsLog;
import com.tiangong.cloud.common.anno.AnonymousAccess;
import com.tiangong.cloud.common.domain.LoginUser;
import com.tiangong.cloud.common.enums.result.AdminResultEnum;
import com.tiangong.cloud.commonbean.anno.OperateLog;
import com.tiangong.cloud.commonbean.common.BaseController;
import com.tiangong.common.Response;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.enums.ErrorCodeEnum;
import com.tiangong.exception.SysException;
import com.tiangong.operatelog.dto.OperateLogReqDTO;
import com.tiangong.organization.domain.UserPO;
import com.tiangong.sensitive.EncryptUtil;
import com.tiangong.user.domain.HrRequest;
import com.tiangong.user.domain.HrResponse;
import com.tiangong.user.domain.req.EmployeeApprovalStatusReq;
import com.tiangong.user.dto.*;
import com.tiangong.user.service.UserService;
import com.tiangong.util.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping(value = "/common/user")
public class UserServer extends BaseController {

    @Autowired
    private UserService userService;

    /**
     * 重置超级管理员密码
     */
    @PostMapping(value = "/resetAdminPwd", produces = {"application/json;charset=UTF-8"})
    @PreAuthorize("@syyo.check('user')")
    public Response<Object> resetAdminPwd(@RequestBody Map<String, String> requestMap) {
        if (requestMap != null && StrUtilX.isNotEmpty(requestMap.get("adminAccount"))) {
            UserPO userPO = new UserPO();
            userPO.setUserAccount(requestMap.get("adminAccount"));
            userPO.setUpdatedBy(getUserName());
            int i = userService.modifyAdminUserPwd(userPO);

            if (i > 0) {
                return Response.success();
            } else {
                return Response.error(ErrorCodeEnum.NOT_ADMIN.errorCode, ErrorCodeEnum.NOT_ADMIN.errorDesc);
            }
        }
        return Response.success();
    }

    /**
     * 获取角色和域名
     */
    @PostMapping(value = "/queryRoleAndDomain", produces = {"application/json;charset=UTF-8"})
    @PreAuthorize("@syyo.check('user')")
    public Response<RoleAndDomainDTO> getRoleAndDomain(@RequestBody Map<String, String> requestMap) {
        requestMap.put("orgCode", super.getCompanyCode());
        if (StrUtilX.isNotEmpty(requestMap.get("endType"))) {
            RoleAndDomainDTO roleAndDomain = userService.getRoleAndDomain(requestMap);
            return Response.success(roleAndDomain);
        } else {
            return Response.error(ErrorCodeEnum.INVALID_INPUTPARAM.errorCode, ErrorCodeEnum.INVALID_INPUTPARAM.errorDesc);
        }
    }

    @OperateLog(value = "查询用户，queryEmployeeList", bizType = 0, operateType = 0)
    @PostMapping(value = "/queryEmployeeList", produces = {"application/json;charset=UTF-8"})
    @PreAuthorize("@syyo.check('user')")
    public Response<PaginationSupportDTO<EmployeeUserDTO>> getEmployeeList(@RequestBody Map<String, String> requestMap) {
        if (StrUtilX.isEmpty(requestMap.get("currentPage"))) {
            requestMap.put("currentPage", "1");
        }

        if (StrUtilX.isEmpty(requestMap.get("pageSize"))) {
            requestMap.put("pageSize", "20");
        }
        requestMap.put("orgCode", getCompanyCode());
        PageInfo<EmployeeUserDTO> employeeUserDTOS = userService.getEmployeeList(requestMap);

        PaginationSupportDTO<EmployeeUserDTO> userDTO = null;
        if (employeeUserDTOS != null) {
            userDTO = new PaginationSupportDTO<>();
            userDTO.copyProperties(employeeUserDTOS, EmployeeUserDTO.class);
        }
        return Response.success(userDTO);
    }

    /**
     * 根据名称模糊查询用户信息列表
     */
    @PostMapping(value = "/queryEmployeeLikeName", produces = {"application/json;charset=UTF-8"})
    @PreAuthorize("@syyo.check('user')")
    public Response<List<UserInfoDTO>> queryEmployeeLikeName(@RequestBody Map<String, String> requestMap) {
        return Response.success(userService.queryEmployeeLikeName(requestMap.get("keyword")));
    }

    @PostMapping(value = "/addEmployee", produces = {"application/json;charset=UTF-8"})
    @PreAuthorize("@syyo.check('user')")
    public Response<Object> addEmployeeUser(@RequestBody EmployeeUserDTO employeeUserDTO) {
        if (employeeUserDTO != null && employeeUserDTO.getEmployeeAccountType() != null
                && StrUtilX.isNotEmpty(employeeUserDTO.getEmployeeAccount())
                && StrUtilX.isNotEmpty(employeeUserDTO.getEmployeeName())
                && StrUtilX.isNotEmpty(employeeUserDTO.getEmployeeTel())
                && StrUtilX.isNotEmpty(employeeUserDTO.getVisitStartTime())
                && StrUtilX.isNotEmpty(employeeUserDTO.getVisitEndTime())
                && StrUtilX.isNotEmpty(employeeUserDTO.getEmployeeAccountPwd())
                && CollUtilX.isNotEmpty(employeeUserDTO.getEmployeeRoleList())) {

            //IP设为ALL
            employeeUserDTO.setVisitIp("ALL");

            employeeUserDTO.setOrgCode(super.getCompanyCode());
            employeeUserDTO.setCreatedBy(super.getUserName());

            //判断参数长度
            if (employeeUserDTO.getEmployeeName().length() > 50 || employeeUserDTO.getEmployeeAccount().length() > 50
                    || employeeUserDTO.getEmployeeTel().length() > 50) {
                return Response.error(AdminResultEnum.E_90052.code, AdminResultEnum.E_90052.message);
            }

            String decrypt = employeeUserDTO.getEmployeeAccountPwd();
            String decrypt1 = employeeUserDTO.getEmployeeAccountPwd1();
            //判断密码和确认密码是否一致
            if (!decrypt.equals(decrypt1)) {
                return Response.error(AdminResultEnum.E_90050.code, AdminResultEnum.E_90050.message);
            }

            //判断密码包含账号和姓名
            if (decrypt.contains(employeeUserDTO.getEmployeeAccount()) || decrypt.contains(employeeUserDTO.getEmployeeName())) {
                return Response.error(AdminResultEnum.E_90051.code, AdminResultEnum.E_90051.message);
            }

            //临时账号不能超过90天有效期
            if (employeeUserDTO.getEmployeeAccountType() != null && employeeUserDTO.getEmployeeAccountType().equals(1)) {
                if (StrUtilX.isEmpty(employeeUserDTO.getAccountAvlStartTime())
                        || StrUtilX.isEmpty(employeeUserDTO.getAccountAvlEndTime())
                        || DateUtilX.getDay(DateUtilX.stringToDate(employeeUserDTO.getAccountAvlStartTime()), DateUtilX.stringToDate(employeeUserDTO.getAccountAvlEndTime())) > 90) {
                    return Response.error(AdminResultEnum.E_90049.code, AdminResultEnum.E_90049.message);
                }
                DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                LocalDate accountAvlStartTime = LocalDate.parse(employeeUserDTO.getAccountAvlStartTime(), dtf);
                LocalDate today = LocalDate.now();
                if (!(today.isEqual(accountAvlStartTime) || today.isBefore(accountAvlStartTime))) {//临时账户有效期开始日期为当前日期或往后
                    return Response.error(AdminResultEnum.E_90058.code, AdminResultEnum.E_90058.message);
                }
            }

            try {
                boolean validatedFlag = ValidatedFilter.passWordRuleValidated(decrypt, employeeUserDTO.getEmployeeAccount());
                if (validatedFlag) {
                    employeeUserDTO.setEmployeeAccountPwd(decrypt);
                } else {
                    return Response.error(AdminResultEnum.E_90040.code, AdminResultEnum.E_90040.message);
                }
            } catch (Exception e) {
                return Response.error(AdminResultEnum.E_90040.code, AdminResultEnum.E_90040.message);
            }
            int i = userService.addEmployeeUser(employeeUserDTO);

            if (i > 0) {
                return Response.success(i);
            } else if (i == -2) {
                return Response.error(ErrorCodeEnum.NO_ADMIN_AUTH.errorCode, ErrorCodeEnum.NO_ADMIN_AUTH.errorDesc);
            } else if (i == -1) {
                return Response.error(ErrorCodeEnum.REPEAT_ACCOUNT.errorCode, ErrorCodeEnum.REPEAT_ACCOUNT.errorDesc);
            } else {
                return Response.error(ErrorCodeEnum.REPEAT_TEL_OR_ID_NUMBER.errorCode, ErrorCodeEnum.REPEAT_TEL_OR_ID_NUMBER.errorDesc);
            }
        } else {
            return Response.error(ErrorCodeEnum.INVALID_INPUTPARAM.errorCode, ErrorCodeEnum.INVALID_INPUTPARAM.errorDesc);
        }
    }

    @PostMapping(value = "/modifyEmployee", produces = {"application/json;charset=UTF-8"})
    @PreAuthorize("@syyo.check('user')")
    public Response<Integer> modifyEmployeeUser(@RequestBody EmployeeUserDTO employeeUserDTO) {
        if (employeeUserDTO != null && employeeUserDTO.getEmployeeId() != null) {
            employeeUserDTO.setUpdatedBy(super.getUserName());
            employeeUserDTO.setModifiedAccount(super.getLoginName());
            int i = userService.modifyEmployeeUser(employeeUserDTO);

            if (i > 0) {
                return Response.success(i);
            } else if (i == -2) {
                return Response.error(ErrorCodeEnum.THIS_USER_CANNOT_MODIFY.errorCode, ErrorCodeEnum.THIS_USER_CANNOT_MODIFY.errorDesc);
            } else if (i == -3) {
                return Response.error(ErrorCodeEnum.REPEAT_TEL_OR_ID_NUMBER.errorCode, ErrorCodeEnum.REPEAT_TEL_OR_ID_NUMBER.errorDesc);
            } else if (i == -4) {
                return Response.error(ErrorCodeEnum.AUDITING.errorCode, ErrorCodeEnum.AUDITING.errorDesc);
            } else if (i == -5) {
                return Response.error(AdminResultEnum.E_90058.code, AdminResultEnum.E_90058.message);
            } else {
                return Response.error(ErrorCodeEnum.ADMIN_OR_NOT_EXIST_USER.errorCode, ErrorCodeEnum.ADMIN_OR_NOT_EXIST_USER.errorDesc);
            }
        } else {
            return Response.error(ErrorCodeEnum.INVALID_INPUTPARAM.errorCode, ErrorCodeEnum.INVALID_INPUTPARAM.errorDesc);
        }
    }

    /**
     * 修改员工有效性
     */
    @PostMapping(value = "/modifyEmployeeActive", produces = {"application/json;charset=UTF-8"})
    @PreAuthorize("@syyo.check('user')")
    public Response<Integer> modifyEmployeeUserActive(@RequestBody EmployeeUserDTO employeeUserDTO) {
        if (employeeUserDTO != null && employeeUserDTO.getEmployeeId() != null && employeeUserDTO.getActive() != null) {
            employeeUserDTO.setUpdatedBy(super.getUserName());
            employeeUserDTO.setModifiedAccount(super.getLoginName());
            employeeUserDTO.setIp(super.getIpAddress());
            int i;
            try {
                i = userService.modifyEmployeeUserActive(employeeUserDTO);
            } catch (SysException e) {
                return Response.error(ErrorCodeEnum.ADMIN_OR_NOT_EXIST_USER.errorCode, e.getMessage());
            }

            if (i > 0) {
                return Response.success(i);
            } else {
                return Response.error(ErrorCodeEnum.ACCOUNT_IS_LOGOUT.errorCode, ErrorCodeEnum.ACCOUNT_IS_LOGOUT.errorDesc);
            }
        } else {
            return Response.error(ErrorCodeEnum.INVALID_INPUTPARAM.errorCode, ErrorCodeEnum.INVALID_INPUTPARAM.errorDesc);
        }
    }

    /**
     * 员工重新提交审批
     */
    @PostMapping(value = "/employeeResubmit", produces = {"application/json;charset=UTF-8"})
    @PreAuthorize("@syyo.check('user')")
    public Response<Integer> employeeResubmit(@RequestBody EmployeeUserDTO employeeUserDTO) {
        if (employeeUserDTO != null && employeeUserDTO.getEmployeeId() != null) {
            employeeUserDTO.setUpdatedBy(super.getUserName());
            employeeUserDTO.setModifiedAccount(super.getLoginName());
            int i;
            try {
                i = userService.employeeResubmit(employeeUserDTO);
            } catch (SysException e) {
                return Response.error(ErrorCodeEnum.EMPLOYEE_RESUBMIT_FAIL.errorCode, e.getMessage());
            }
            if (i > 0) {
                return Response.success(i);
            } else {
                return Response.error(ErrorCodeEnum.ACCOUNT_IS_LOGOUT.errorCode, ErrorCodeEnum.ACCOUNT_IS_LOGOUT.errorDesc);
            }
        } else {
            return Response.error(ErrorCodeEnum.INVALID_INPUTPARAM.errorCode, ErrorCodeEnum.INVALID_INPUTPARAM.errorDesc);
        }
    }

    /**
     * 接收hr员工审批状态
     */
    @PostMapping(value = "/getEmployeeApprovalStatus", produces = {"application/json;charset=UTF-8"})
    @AnonymousAccess
    public HrResponse<Object> getEmployeeApprovalStatus(@RequestBody HrRequest<EmployeeApprovalStatusReq> request) {
        if (request != null && request.getData() != null) {
            int i = userService.getEmployeeApprovalStatus(request, super.getIpAddress());
            if (i > 0) {
                return HrResponse.success(i);
            } else {
                return HrResponse.error("用户不存在");
            }
        } else {
            return HrResponse.error(ErrorCodeEnum.INVALID_INPUTPARAM.errorDesc);
        }
    }

    /**
     * 重置密码或修改密码
     */
    @PostMapping(value = "/modifyEmployeePwd", produces = {"application/json;charset=UTF-8"})
    @PreAuthorize("@syyo.check('user')")
    public Response<Integer> modifyUserPwd(@RequestBody Map<String, String> requestMap) {
        LoginUser user = getUser();
        // 添加日志
        OperateLogReqDTO newDO = new OperateLogReqDTO();
        newDO.setCreatedDt(LocalDateTime.now());
        newDO.setLogName("修改企业员工密码");
        newDO.setLogType("error");
        newDO.setLogLevel(0);
        newDO.setOperationType(3);
        newDO.setOperationResult(0);
        newDO.setApplicationName("tiangong-common-server");
        newDO.setRequestMethod(request.getMethod());
        newDO.setRequestIp(IpUtil.getIpAddress(request));
        newDO.setRequestHeader(request.getHeader("User-Agent"));
        newDO.setRequestUrl(request.getRequestURI());
        newDO.setCreatedBy(user.getFullUserName());
        newDO.setUserAccount(user.getUserAccount());

        // 需要把密码参数 日志入库为*
        String employeePwdParam = requestMap.get("employeePwd");
        requestMap.put("employeePwd", "******");
        String currentEmployeePwdParam = null;
        String oldEmployeePwdParam = null;
        if (requestMap.containsKey("currentEmployeePwd")) {
            currentEmployeePwdParam = requestMap.get("currentEmployeePwd");
            requestMap.put("currentEmployeePwd", "******");
        }
        if (requestMap.containsKey("oldEmployeePwd")) {
            oldEmployeePwdParam = requestMap.get("oldEmployeePwd");
            requestMap.put("oldEmployeePwd", "******");
        }

        // 校验参数
        newDO.setRequestParam(JSONObject.toJSONString(requestMap));
        if (StrUtilX.isEmpty(requestMap.get("employeeId")) || StrUtilX.isEmpty(employeePwdParam)) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
        if (StrUtilX.isEmpty(currentEmployeePwdParam) && StrUtilX.isEmpty(oldEmployeePwdParam)) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }

        // 先根据员工ID 补充要求改的员工名称
        Map<String, String> queryMap = new HashMap<>();
        queryMap.put("userId", requestMap.get("employeeId"));
        UserDTO userDetail = userService.queryLoginUser(queryMap);//企业成员
        newDO.setLogName(newDO.getLogName() + userDetail.getLoginName());
        if (requestMap.get("employeeId").equals(user.getUserId().toString())) {
            newDO.setLogName("修改自身密码");
        }

        String oldEmployeePwd;
        boolean flag = false;
        if (StrUtilX.isNotEmpty(currentEmployeePwdParam)) {
            // 当前用户密码
            oldEmployeePwd = currentEmployeePwdParam;
            flag = true;
        } else {
            // 旧密码
            oldEmployeePwd = oldEmployeePwdParam;
        }
        Map<String, String> request = new HashMap<>();
        request.put("userAccount", user.getUserAccount());
        UserDTO userDTO = userService.queryLoginUser(request);

        String oldEPwdEn = SM4Utils.encrypt(oldEmployeePwd, Sm4O.defaultKey);

        // 判断密码是否一致
        if (!oldEPwdEn.equals(userDTO.getLoginPwd())) {
            throw new SysException(ErrorCodeEnum.UPDATE_PASSWORD_ERROR);
        }

        // 校验密码是否正常
        if (!SM3Utils.verify(userDTO.getLoginPwd(), userDTO.getSignaturePwd())) {
            throw new SysException(AdminResultEnum.E_90056.code, AdminResultEnum.E_90056.message);
        }

        UserPO userPO = new UserPO();
        userPO.setUserId(Integer.valueOf(requestMap.get("employeeId")));
        if (StrUtilX.isNotEmpty(employeePwdParam)) {
            if (flag) {
                // 用户密码
                oldEPwdEn = SM4Utils.decrypt(userDetail.getLoginPwd(), Sm4O.defaultKey);
            }
            if (oldEPwdEn.equals(employeePwdParam)) {
                throw new SysException(AdminResultEnum.E_90053.code, AdminResultEnum.E_90053.message);
            }

            try {
                ValidatedFilter.passWordRuleValidated(employeePwdParam, user.getUserAccount());
            } catch (Exception e) {
                return Response.error(AdminResultEnum.E_90040.code, AdminResultEnum.E_90040.message);
            }
            userPO.setUserPwd(employeePwdParam);
        }
        userPO.setUpdatedBy(getLoginName());

        // 修改密码
        int i = userService.modifyEmployeePwd(userPO, flag);

        if (i > 0) {
            newDO.setLogType("info");
            newDO.setOperationResult(1);
        } else if (i == -2) {
            throw new SysException(ErrorCodeEnum.THIS_USER_CANNOT_MODIFY);
        } else if (i == -3) {
            throw new SysException(ErrorCodeEnum.AUDITING);
        } else {
            throw new SysException(ErrorCodeEnum.ADMIN_OR_NOT_EXIST_USER);
        }

        return Response.success(i);
    }

    /**
     * 校验密码
     */
    @PostMapping(value = "/examinePwd", produces = {"application/json;charset=UTF-8"})
    @PreAuthorize("@syyo.check('user')")
    public Response<Integer> examinePwd(@RequestBody Map<String, String> requestMap) {
        if (requestMap != null && StrUtilX.isNotEmpty(requestMap.get("employeePwd"))
                && StrUtilX.isNotEmpty(requestMap.get("employeeId"))) {
            int i = userService.examinePwd(requestMap);

            return Response.success(i);
        } else {
            return Response.error(ErrorCodeEnum.INVALID_INPUTPARAM.errorCode, ErrorCodeEnum.INVALID_INPUTPARAM.errorDesc);
        }
    }

    /**
     * 是否手机号或身份证是否存在
     */
    @PostMapping(value = "/examinePhoneOrIdCard", produces = {"application/json;charset=UTF-8"})
    @PreAuthorize("@syyo.check('user')")
    public Response<Integer> examinePhoneOrIdCard(@RequestBody Map<String, String> requestMap) {
        if (requestMap != null && (StrUtilX.isNotEmpty(requestMap.get("employeeIDNumber"))
                || StrUtilX.isNotEmpty(requestMap.get("employeeTel")))) {
            int i = userService.examinePhoneOrIdCard(requestMap);
            return Response.success(i);
        } else {
            return Response.error(ErrorCodeEnum.INVALID_INPUTPARAM.errorCode, ErrorCodeEnum.INVALID_INPUTPARAM.errorDesc);
        }
    }

    /**
     * 删除员工
     */
    @PostMapping(value = "/deleteEmployee", produces = {"application/json;charset=UTF-8"})
    @PreAuthorize("@syyo.check('user')")
    public Response<Integer> deleteEmployeeUser(HttpServletRequest request, @RequestBody Map<String, String> requestMap) {
        LoginUser user = getUser();
        // 添加日志
        OperateLogReqDTO newDO = new OperateLogReqDTO();
        newDO.setCreatedDt(LocalDateTime.now());
        newDO.setLogName("删除企业员工");
        newDO.setLogType("error");
        newDO.setLogLevel(0);
        newDO.setOperationType(2);
        newDO.setOperationResult(0);
        newDO.setApplicationName("tiangong-common-server");
        newDO.setRequestMethod(request.getMethod());
        newDO.setRequestIp(IpUtil.getIpAddress(request));
        newDO.setRequestHeader(request.getHeader("User-Agent"));
        newDO.setRequestUrl(request.getRequestURI());
        newDO.setCreatedBy(user.getFullUserName());
        newDO.setUserAccount(user.getUserAccount());
        newDO.setRequestParam(JSONObject.toJSONString(requestMap));

        if (requestMap != null && StrUtilX.isNotEmpty(requestMap.get("employeeId"))) {
            //先根据员工ID 补充要求改的员工名称
            Map<String, String> queryMap = new HashMap<>();
            queryMap.put("userId", requestMap.get("employeeId"));
            UserDTO userDetail = userService.queryLoginUser(queryMap);//企业成员
            newDO.setLogName(newDO.getLogName() + userDetail.getLoginName());

            UserPO userPO = new UserPO();
            userPO.setUserId(Integer.valueOf(requestMap.get("employeeId")));
            userPO.setUpdatedBy(getUserName());
            String modifiedAccount = getLoginName();
            int i;
            try {
                i = userService.deleteEmployeeUser(userPO, modifiedAccount, request);
            } catch (SysException e) {
                return Response.error(ErrorCodeEnum.SYSTEM_DEL_FAILED.errorCode, e.getMessage());
            }

            if (i > 0) {
                newDO.setLogType("info");
                newDO.setOperationResult(1);
                return Response.success(i);
            } else if (i == -2) {
                return Response.error(ErrorCodeEnum.THIS_USER_CANNOT_MODIFY.errorCode, ErrorCodeEnum.THIS_USER_CANNOT_MODIFY.errorDesc);
            } else {
                return Response.error(ErrorCodeEnum.ADMIN_OR_NOT_EXIST_USER.errorCode, ErrorCodeEnum.ADMIN_OR_NOT_EXIST_USER.errorDesc);
            }

        } else {
            return Response.error(ErrorCodeEnum.INVALID_INPUTPARAM.errorCode, ErrorCodeEnum.INVALID_INPUTPARAM.errorDesc);
        }
    }

    /**
     * 配置系统设置
     */
    @PostMapping(value = "/configSystemSetting", produces = {"application/json;charset=UTF-8"})
    @PreAuthorize("@syyo.check('user')")
    public Response<Integer> configSystemSetting(@RequestBody SystemSettingDTO systemSettingDTO) {
        if (systemSettingDTO != null && systemSettingDTO.getMaxOnlineCount() != null
                && systemSettingDTO.getMaxFailCount() != null
                && systemSettingDTO.getFailLockTime() != null) {
            int i = userService.configSystemSetting(systemSettingDTO);

            if (i > 0) {
                return Response.success(i);
            } else {
                return Response.error(ErrorCodeEnum.FAIL_COUNT_OUT.errorCode, ErrorCodeEnum.FAIL_COUNT_OUT.errorDesc);
            }
        } else {
            return Response.error(ErrorCodeEnum.INVALID_INPUTPARAM.errorCode, ErrorCodeEnum.INVALID_INPUTPARAM.errorDesc);
        }
    }

    /**
     * 查询系统设置
     */
    @PostMapping(value = "/querySystemSetting", produces = {"application/json;charset=UTF-8"})
    @PreAuthorize("@syyo.check('user')")
    public Response<Map<String, String>> querySystemSetting() {
        return Response.success(userService.querySystemSetting());
    }

    /**
     * 修改员工状态
     */
    @PostMapping(value = "/updateAccountStatus", produces = {"application/json;charset=UTF-8"})
    @PreAuthorize("@syyo.check('user')")
    public Response<Integer> updateAccountStatus(@RequestBody Map<String, String> requestMap) {
        if (requestMap != null && StrUtilX.isNotEmpty(requestMap.get("employeeId"))
                && StrUtilX.isNotEmpty(requestMap.get("employeeAccount"))
                && StrUtilX.isNotEmpty(requestMap.get("accountStatus"))) {
            requestMap.put("modifiedAccount", getUserName());
            int result = userService.updateStatus(requestMap);

            if (result > 0) {
                return Response.success(result);
            } else {
                return Response.error(ErrorCodeEnum.THIS_USER_CANNOT_MODIFY.errorCode, ErrorCodeEnum.THIS_USER_CANNOT_MODIFY.errorDesc);
            }
        } else {
            return Response.error(ErrorCodeEnum.INVALID_INPUTPARAM.errorCode, ErrorCodeEnum.INVALID_INPUTPARAM.errorDesc);
        }
    }

    /**
     * 获取发送验证码类型
     */
    @PostMapping(value = "/querySendVerificationCodeType", produces = {"application/json;charset=UTF-8"})
    @PreAuthorize("@syyo.check('user')")
    public Response<String> querySendVerificationCodeType() {
        return Response.success(userService.querySendVerificationCodeType());
    }

    /**
     * 修改发送验证码类型
     */
    @PostMapping(value = "/updateSendVerificationCodeType", produces = {"application/json;charset=UTF-8"})
    @PreAuthorize("@syyo.check('user')")
    public Response<Object> updateSendVerificationCodeType(@RequestBody Map<String, String> requestMap) {
        userService.updateSendVerificationCodeType(requestMap.get("sendVerificationCodeType"));
        return Response.success();
    }

    /**
     * 获取企业员工详情
     */
    @PostMapping(value = "/queryEmployeeDetail", produces = {"application/json;charset=UTF-8"})
    @PreAuthorize("@syyo.check('user')")
    public Response<EmployeeUserDTO> getEmployeeUserDetail(@RequestBody Map<String, String> requestMap) {
        if (requestMap != null && StrUtilX.isNotEmpty(requestMap.get("employeeId"))) {
            requestMap.put("user", super.getLoginName());
            EmployeeUserDTO employeeUserDTO = userService.getEmployeeUserDetail(requestMap);
            return Response.success(employeeUserDTO);
        } else {
            return Response.error(ErrorCodeEnum.INVALID_INPUTPARAM.errorCode, ErrorCodeEnum.INVALID_INPUTPARAM.errorDesc);
        }
    }

    /**
     * 是否存在该用户名
     */
    @PostMapping(value = "/examineUserAccount", produces = {"application/json;charset=UTF-8"})
    @PreAuthorize("@syyo.check('user')")
    public Response<Integer> examineUserAccount(@RequestBody Map<String, String> requestMap) {
        if (requestMap != null && StrUtilX.isNotEmpty(requestMap.get("userAccount"))) {
            int i = userService.examineUserAccount(requestMap.get("userAccount"));
            return Response.success(i);
        } else {
            return Response.error(ErrorCodeEnum.INVALID_INPUTPARAM.errorCode, ErrorCodeEnum.INVALID_INPUTPARAM.errorDesc);
        }
    }

    /**
     * 用户登陆
     */
    @PostMapping(value = "/login", produces = {"application/json;charset=UTF-8"})
    public Response<UserDTO> login(@RequestBody UserDTO userDTO) throws Exception {
        UserDTO userDB = userService.login(userDTO);
        return Response.success(userDB);
    }

    /**
     * 通过登陆名查询菜单
     */
    @PostMapping(value = "/queryMenuListByLoginName", produces = {"application/json;charset=UTF-8"})
    @PreAuthorize("@syyo.check('user')")
    public Response<List<MenuDTO>> queryMenuListByLoginName(@RequestBody Map<String, String> requestMap) {
        List<MenuDTO> menuDTOS = userService.queryMenuListByLoginName(requestMap);
        return Response.success(menuDTOS);
    }

    /**
     * 查询角色列表
     */
    @PostMapping(value = "/queryRoleList", produces = {"application/json;charset=UTF-8"})
    @PreAuthorize("@syyo.check('user')")
    public Response<PageInfo<RoleDTO>> queryRoleList(@RequestBody Map<String, String> requestMap) {
        if (null == requestMap) {
            requestMap = new HashMap<>();
            requestMap.put("currentPage", "1");
            requestMap.put("pageSize", "20");
        }
        if (StrUtilX.isEmpty(requestMap.get("currentPage"))) {
            requestMap.put("currentPage", "1");
        }

        if (StrUtilX.isEmpty(requestMap.get("pageSize"))) {
            requestMap.put("pageSize", "20");
        }
        PageInfo<RoleDTO> roleListDTOPageInfo = userService.queryRoleList(requestMap);

        return Response.success(roleListDTOPageInfo);
    }

    /**
     * 查询菜单列表
     */
    @PostMapping(value = "/queryMenuList", produces = {"application/json;charset=UTF-8"})
    @PreAuthorize("@syyo.check('user')")
    public Response<List<FirstMenuDTO>> queryMenuList(@RequestBody Map<String, String> requestMap) {
        List<FirstMenuDTO> menuList = userService.queryMenuList(requestMap);
        return Response.success(menuList);
    }

    /**
     * 查询角色菜单列表
     */
    @PostMapping(value = "/queryRoleMenuList", produces = {"application/json;charset=UTF-8"})
    @PreAuthorize("@syyo.check('user')")
    public Response<RoleDTO> queryRoleMenuList(@RequestBody Map<String, String> requestMap) {
        RoleDTO roleMenuDTO = userService.queryRoleMenuList(requestMap);
        return Response.success(roleMenuDTO);
    }

    /**
     * 删除角色
     */
    @PostMapping(value = "/roleDel", produces = {"application/json;charset=UTF-8"})
    @PreAuthorize("@syyo.check('user')")
    public Response<Object> roleDel(@RequestBody Map<String, String> requestMap) {
        requestMap.put("updatedBy", super.getUserName());
        return userService.roleDel(requestMap);
    }

    /**
     * 编辑角色
     */
    @PostMapping(value = "/roleEdit", produces = {"application/json;charset=UTF-8"})
    @PreAuthorize("@syyo.check('user')")
    public Response<Object> roleEdit(@RequestBody RoleDTO roleDTO) {
        roleDTO.setUpdatedBy(super.getUserName());
        userService.editRole(roleDTO);
        return Response.success();
    }

    /**
     * 修改员工身份证
     */
    @PostMapping(value = "/modifyEmployeeUserIdCard", produces = {"application/json;charset=UTF-8"})
    @PreAuthorize("@syyo.check('user')")
    public Response<Integer> modifyEmployeeUserIdCard(@RequestBody EmployeeUserDTO employeeUserDTO) throws Exception {
        if (employeeUserDTO != null && employeeUserDTO.getEmployeeId() != null) {
            employeeUserDTO.setUpdatedBy(super.getUserName());
            employeeUserDTO.setModifiedAccount(super.getLoginName());
            int i = userService.modifyEmployeeUserPhone(employeeUserDTO);
            if (i > 0) {
                return Response.success(i);
            } else if (i == -2) {
                return Response.error(ErrorCodeEnum.THIS_USER_CANNOT_MODIFY.errorCode, ErrorCodeEnum.THIS_USER_CANNOT_MODIFY.errorDesc);
            } else if (i == -3) {
                return Response.error(ErrorCodeEnum.ACCOUNT_OR_TEL_ALREADY.errorCode, ErrorCodeEnum.ACCOUNT_OR_TEL_ALREADY.errorDesc);
            } else if (i == -4) {
                return Response.error(ErrorCodeEnum.AUDITING.errorCode, ErrorCodeEnum.AUDITING.errorDesc);
            } else if (i == -5) {
                return Response.error(ErrorCodeEnum.PHONENOTCHANGED.errorCode, ErrorCodeEnum.PHONENOTCHANGED.errorDesc);
            } else {
                return Response.error(ErrorCodeEnum.ADMIN_OR_NOT_EXIST_USER.errorCode, ErrorCodeEnum.ADMIN_OR_NOT_EXIST_USER.errorDesc);
            }
        }
        return Response.success(null);
    }

    /**
     * 修改员工手机号
     */
    @PostMapping(value = "/modifyEmployeeUserPhone", produces = {"application/json;charset=UTF-8"})
    @PreAuthorize("@syyo.check('user')")
    public Response<Integer> modifyEmployeeUserPhone(@RequestBody EmployeeUserDTO employeeUserDTO) throws Exception {
        if (employeeUserDTO != null && employeeUserDTO.getEmployeeId() != null) {
            employeeUserDTO.setUpdatedBy(super.getUserName());
            employeeUserDTO.setModifiedAccount(super.getLoginName());
            int i = userService.modifyEmployeeUserPhone(employeeUserDTO);
            if (i > 0) {
                return Response.success(i);
            } else if (i == -2) {
                return Response.error(ErrorCodeEnum.THIS_USER_CANNOT_MODIFY.errorCode, ErrorCodeEnum.THIS_USER_CANNOT_MODIFY.errorDesc);
            } else if (i == -3) {
                return Response.error(ErrorCodeEnum.ACCOUNT_OR_TEL_ALREADY.errorCode, ErrorCodeEnum.ACCOUNT_OR_TEL_ALREADY.errorDesc);
            } else if (i == -4) {
                return Response.error(ErrorCodeEnum.AUDITING.errorCode, ErrorCodeEnum.AUDITING.errorDesc);
            } else if (i == -5) {
                return Response.error(ErrorCodeEnum.PHONENOTCHANGED.errorCode, ErrorCodeEnum.PHONENOTCHANGED.errorDesc);
            } else {
                return Response.error(ErrorCodeEnum.ADMIN_OR_NOT_EXIST_USER.errorCode, ErrorCodeEnum.ADMIN_OR_NOT_EXIST_USER.errorDesc);
            }
        }
        return Response.success(null);
    }

    /**
     * 验证密码查看手机号
     */
    @PostMapping(value = "/verifyPasswordQueryPhone", produces = {"application/json;charset=UTF-8"})
    @PreAuthorize("@syyo.check('user')")
    public Response<String> verifyPasswordQueryPhone(@RequestBody EmployeeUserDTO employeeUserDTO) {
        if (employeeUserDTO != null && employeeUserDTO.getEmployeeId() != null) {
            employeeUserDTO.setUpdatedBy(super.getUserName());
            employeeUserDTO.setModifiedAccount(super.getLoginName());
            return userService.verifyPasswordQueryInfo(employeeUserDTO, 1);
        } else {
            return Response.error(ErrorCodeEnum.INVALID_INPUTPARAM.errorCode, ErrorCodeEnum.INVALID_INPUTPARAM.errorDesc);
        }
    }

    /**
     * 验证密码查看身份证
     */
    @PostMapping(value = "/verifyPasswordQueryIdCard", produces = {"application/json;charset=UTF-8"})
    @PreAuthorize("@syyo.check('user')")
    public Response<String> verifyPasswordQueryIdCard(@RequestBody EmployeeUserDTO employeeUserDTO) {
        if (employeeUserDTO != null && employeeUserDTO.getEmployeeId() != null) {
            employeeUserDTO.setUpdatedBy(super.getUserName());
            employeeUserDTO.setModifiedAccount(super.getLoginName());
            return userService.verifyPasswordQueryInfo(employeeUserDTO, 2);
        } else {
            return Response.error(ErrorCodeEnum.INVALID_INPUTPARAM.errorCode, ErrorCodeEnum.INVALID_INPUTPARAM.errorDesc);
        }
    }

    /**
     * 是否存在该账号或手机号
     */
    @PostMapping(value = "/examineUserAccountOrUserTel", produces = {"application/json;charset=UTF-8"})
    @PreAuthorize("@syyo.check('user')")
    public Response<Integer> examineUserAccountOrUserTel(@RequestBody Map<String, String> requestMap) throws Exception {
        if (requestMap != null && StrUtilX.isNotEmpty(requestMap.get("userAccount"))) {
            int i = userService.queryUserAccountAndUserTel(requestMap.get("userAccount"), (String) EncryptUtil.encrypt(requestMap.get("userAccount")), requestMap.get("agentCode"));
            return Response.success(i);
        } else {
            return Response.error(ErrorCodeEnum.INVALID_INPUTPARAM.errorCode, ErrorCodeEnum.INVALID_INPUTPARAM.errorDesc);
        }
    }
}
