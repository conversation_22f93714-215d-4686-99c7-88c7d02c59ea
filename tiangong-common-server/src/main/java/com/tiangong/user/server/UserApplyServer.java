package com.tiangong.user.server;

import com.github.pagehelper.PageInfo;
import com.tiangong.annotations.SlsLog;
import com.tiangong.cloud.commonbean.common.BaseController;
import com.tiangong.common.Response;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.enums.ErrorCodeEnum;
import com.tiangong.user.dto.EmployeeUserDTO;
import com.tiangong.user.dto.UserApplyDTO;
import com.tiangong.user.service.UserApplyService;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@Slf4j
@RestController
@RequestMapping(value = "/common/apply")
public class UserApplyServer extends BaseController {

    @Autowired
    private UserApplyService userApplyService;

    /**
     * 查询员工增改申请列表
     */
    @PostMapping(value = "/queryUserApplyList", produces = {"application/json;charset=UTF-8"})
    @PreAuthorize("@syyo.check('user')")
    public Response<PaginationSupportDTO<UserApplyDTO>> queryUserApplyList(@RequestBody Map<String, String> requestMap) {
        if (StrUtilX.isEmpty(requestMap.get("pageSize"))) {
            requestMap.put("pageSize", "20");
        }
        if (StrUtilX.isEmpty(requestMap.get("currentPage"))) {
            requestMap.put("currentPage", "1");
        }

        PageInfo<UserApplyDTO> result = userApplyService.queryUserApplyList(requestMap);
        PaginationSupportDTO<UserApplyDTO> userDTO = null;
        if (result != null) {
            userDTO = new PaginationSupportDTO<>();
            userDTO.copyProperties(result, UserApplyDTO.class);
        }
        return Response.success(userDTO);
    }

    /**
     * 审核
     */
    @PostMapping(value = "/userApply", produces = {"application/json;charset=UTF-8"})
    @PreAuthorize("@syyo.check('user')")
    public Response<Integer> userApply(@RequestBody Map<String, String> requestMap) {
        requestMap.put("modifiedUser", super.getUserName());
        int i = userApplyService.userApply(requestMap);
        if (i > 0) {
            return Response.success(i);
        } else {
            return Response.error(ErrorCodeEnum.APPLY_ERROR.errorCode, ErrorCodeEnum.APPLY_ERROR.errorDesc);
        }
    }

    /**
     * 查询员工增改申请详情
     */
    @PostMapping(value = "/queryUserApplyDetail", produces = {"application/json;charset=UTF-8"})
    @PreAuthorize("@syyo.check('user')")
    public Response<EmployeeUserDTO> queryUserApplyDetail(@RequestBody Map<String, String> requestMap) {
        return Response.success(userApplyService.queryUserApplyDetail(requestMap));
    }

}
