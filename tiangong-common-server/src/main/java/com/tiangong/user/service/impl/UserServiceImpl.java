package com.tiangong.user.service.impl;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.tiangong.ValidatedFilter;
import com.tiangong.cloud.common.enums.result.AdminResultEnum;
import com.tiangong.common.Response;
import com.tiangong.config.EmailConfigProperties;
import com.tiangong.config.SettingsConstant;
import com.tiangong.convert.CommonDtoConvert;
import com.tiangong.dto.common.CompanyDTO;
import com.tiangong.enums.EmployeeAccountStatusEnum;
import com.tiangong.enums.ErrorCodeEnum;
import com.tiangong.enums.ParamErrorEnum;
import com.tiangong.enums.PictureTypeEnum;
import com.tiangong.exception.SysException;
import com.tiangong.keys.RedisKey;
import com.tiangong.operatelog.domain.OperateLogEntity;
import com.tiangong.operatelog.dto.OperateLogReqDTO;
import com.tiangong.operatelog.mapper.OperateLogMapper;
import com.tiangong.organization.domain.OrgPO;
import com.tiangong.organization.domain.SysConfigPO;
import com.tiangong.organization.domain.UserApplyPO;
import com.tiangong.organization.domain.UserPO;
import com.tiangong.organization.mapper.OrgMapper;
import com.tiangong.organization.mapper.PictureMapper;
import com.tiangong.organization.mapper.SysConfigMapper;
import com.tiangong.organization.remote.dto.CompanyBusinessLicenseUrlDTO;
import com.tiangong.organization.service.SysConfigService;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.req.SendEmailReq;
import com.tiangong.req.SendPhoneReq;
import com.tiangong.sensitive.EncryptUtil;
import com.tiangong.user.domain.*;
import com.tiangong.user.domain.req.*;
import com.tiangong.user.dto.*;
import com.tiangong.user.manager.HrManager;
import com.tiangong.user.mapper.*;
import com.tiangong.user.service.UserService;
import com.tiangong.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.tiangong.dto.common.CompanyDTO.COMPANY_CODE;

@Service
@Slf4j
public class UserServiceImpl implements UserService {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private PictureMapper pictureMapper;

    @Autowired
    private RoleMapper roleMapper;

    @Autowired
    private UserRoleMapper userRoleMapper;

    @Autowired
    private MenuMapper menuMapper;

    @Autowired
    private RoleMenuMapper roleMenuMapper;

    @Autowired
    private UserApplyMapper userApplyMapper;

    @Autowired
    private OrgMapper orgMapper;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private OperateLogMapper operateLogMapper;


    @Autowired
    private SendCodeUtil sendCodeUtil;

    @Autowired
    private SettingsConstant settingsConstant;

    @Resource
    private SysConfigMapper sysConfigMapper;

    @Autowired
    private HrManager hrManager;

    @Autowired
    private EmailConfigProperties emailConfigProperties;


    public static final String VERIFICATION_CODE = "AUTH:VERIFICATION_CODE:";

    /**
     * 增加供应商、客户、运营商所需要的调用的增加超级管理员接口
     * 以下为所需要封装为AdminPO里面的参数
     * 姓名、账号、手机号、企业编号、创建人
     * type指哪个端的用户 type类型见com.tiangong.user.enums
     *
     * @return 返回为-1，则该企业编码下有超级管理员或有该账号名
     */
    @Override
    @Transactional
    public int addAdminUser(UserPO userPO, int type) {

        UserPO queryUserPO = new UserPO();
        queryUserPO.setOrgCode(userPO.getOrgCode());
        queryUserPO.setIsSuperAdmin(1);
        queryUserPO.setActive(1);
        queryUserPO.setUserAccount(userPO.getUserAccount());
        queryUserPO.setUserTel(userPO.getUserTel());
        List<UserPO> existPO = userMapper.queryExistAdminUser(queryUserPO);

        if (CollUtilX.isNotEmpty(existPO)) {
            return -1;
        }

        userPO.setIsSuperAdmin(1);
        userPO.setActive(1);
        userPO.setAvailableStatus(1);
        userPO.setAccountStatus(0);
        userPO.setCreatedDt(DateUtilX.dateToString(new Date(), "yyyy-MM-dd HH:mm:ss"));

        try {
            String password = CompanyDTO.PWD;
            if (!StringUtils.isEmpty(userPO.getUserPwd())) {
                password = userPO.getUserPwd();
            }
            String encode = SM4Utils.encrypt(password, Sm4O.defaultKey);
            userPO.setUserPwd(encode);
            userPO.setSignaturePwd(SM3Utils.encrypt(encode));
        } catch (Exception e) {
            log.error("新增用户密码加密失败");
        }
        userMapper.insert(userPO);

        // 增加总管理员的时候，把所有的权限都加上
        RolePO rolePO = new RolePO();
        rolePO.setActive(1);
        rolePO.setType(type);
        List<RolePO> rolePOS = roleMapper.select(rolePO);

        if (CollUtilX.isNotEmpty(rolePOS)) {
            List<UserRolePO> userRolePOs = new ArrayList<UserRolePO>();

            for (RolePO role : rolePOS) {
                UserRolePO userRolePO = new UserRolePO();
                userRolePO.setRoleId(role.getRoleId());
                userRolePO.setUserId(userPO.getUserId());
                userRolePO.setCreatedBy(userPO.getCreatedBy());
                userRolePO.setCreatedDt(DateUtilX.dateToString(new Date(), "yyyy-MM-dd HH:mm:ss"));

                userRolePOs.add(userRolePO);
            }

            userRoleMapper.insertList(userRolePOs);
        }
        return userPO.getUserId();
    }

    /**
     * 修改超级管理员信息
     * 必传userId ，修改人，以及修改的信息
     *
     * @param userPO 用户信息
     * @return 返回 == 1，则不为超级管理员,返回 > 0，则操作成功
     */
    @Override
    public int modifyAdminUser(UserPO userPO) {

        UserPO existUser = new UserPO();
        existUser.setUserId(userPO.getUserId());
        existUser.setActive(1);
        existUser.setIsSuperAdmin(1);
        existUser = userMapper.selectOne(existUser);

        if (existUser == null) {
            return -1;
        }

        if (userPO.getAvailableStatus() != null) {
            existUser.setAvailableStatus(userPO.getAvailableStatus());
        }

        if (StrUtilX.isNotEmpty(userPO.getUserName())) {
            existUser.setUserName(userPO.getUserName());
        }

        if (StrUtilX.isNotEmpty(userPO.getUserTel())) {
            existUser.setUserTel(userPO.getUserTel());
        }
        if (StrUtilX.isNotEmpty(userPO.getUpdatedBy())) {
            existUser.setUpdatedBy(userPO.getUpdatedBy());
        }

        existUser.setUpdatedDt(DateUtilX.dateToString(new Date(), "yyyy-MM-dd HH:mm:ss"));
        return userMapper.updateByPrimaryKeySelective(existUser);
    }

    /**
     * 重置超级管理员密码
     * 必传userId ，修改人
     *
     * @param userPO 用户信息
     * @return 返回 == 1，则不为超级管理员,返回 > 0，则操作成功
     */
    @Override
    public int modifyAdminUserPwd(UserPO userPO) {

        UserPO existUser = new UserPO();
        existUser.setUserAccount(userPO.getUserAccount());
        existUser.setActive(1);
        existUser.setIsSuperAdmin(1);
        existUser = userMapper.selectOne(existUser);


        if (existUser == null) {
            return -1;
        }

        try {
            String encode = SM4Utils.encrypt(CompanyDTO.PWD, Sm4O.defaultKey);
            existUser.setUserPwd(encode);
            existUser.setSignaturePwd(SM3Utils.encrypt(encode));
        } catch (Exception e) {
            log.error("新增用户密码加密失败");
        }
        existUser.setUpdatedDt(DateUtilX.dateToString(new Date(), "yyyy-MM-dd HH:mm:ss"));
        existUser.setUpdatedBy(userPO.getUpdatedBy());

        return userMapper.updateByPrimaryKeySelective(existUser);
    }

    /**
     * 获取采购经理列表
     */
    @Override
    public List<PurchaseManagerDTO> getPurchaseManagerList(Map<String, String> requestMap) {
        return userMapper.queryPurchaseManagerList(requestMap);
    }

    /**
     * 获取销售经理列表
     */
    @Override
    public List<SaleManagerDTO> getSaleManagerList(Map<String, String> requestMap) {
        return userMapper.getSaleManagerList(requestMap);
    }

    /**
     * 获取角色和域名
     */
    @Override
    public RoleAndDomainDTO getRoleAndDomain(Map<String, String> requestMap) {
        return userMapper.getRoleAndDomain(requestMap);
    }

    /**
     * 获取企业员工列表
     */
    @Override
    public PageInfo<EmployeeUserDTO> getEmployeeList(Map<String, String> requestMap) {
        PageHelper.startPage(Integer.parseInt(requestMap.get("currentPage")), Integer.parseInt(requestMap.get("pageSize")));
        List<EmployeeUserDTO> employeeUserDTOS = userMapper.getEmployeeList(requestMap);
        if (CollUtilX.isNotEmpty(employeeUserDTOS)) {
            List<EmployeeRoleDTO> roleList = userMapper.getRoleList(employeeUserDTOS);
            assemblyEmployee(employeeUserDTOS, roleList);
        }
        return new PageInfo<>(employeeUserDTOS);
    }

    @Override
    public List<UserInfoDTO> queryEmployeeLikeName(String keyword) {
        Example example = new Example(UserPO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("accountStatus", 0);
        criteria.andEqualTo("orgCode", COMPANY_CODE);
        if (StrUtilX.isNotEmpty(keyword)) {
            criteria.andLike("userName", "%" + keyword + "%");
        }
        List<UserPO> userPOList = userMapper.selectByExample(example);
        if (CollUtilX.isEmpty(userPOList)) {
            return null;
        }
        return userPOList.stream().map(item -> {
            UserInfoDTO dto = new UserInfoDTO();
            dto.setEmployeeId(item.getUserId());
            dto.setEmployeeName(item.getUserName());
            dto.setEmployeeTel(item.getUserTel());
            return dto;
        }).collect(Collectors.toList());
    }

    /**
     * 增加企业员工
     *
     * @param employeeUserDTO 员工信息
     * @return -1,已存在该账号；-2，不能增加总管理员身份
     */
    @Override
    @Transactional
    public int addEmployeeUser(EmployeeUserDTO employeeUserDTO) {
        int i = userMapper.queryUserAccount(employeeUserDTO.getEmployeeAccount());
        if (i > 0) {
            return -1;
        }

        String tel = employeeUserDTO.getEmployeeTel();
        String idNumber = employeeUserDTO.getEmployeeIDNumber();

        UserPO queryUserPO = new UserPO();
        queryUserPO.setOrgCode(employeeUserDTO.getOrgCode());
        queryUserPO.setUserTel(tel);
        queryUserPO.setUserIDNumber(idNumber);
        int count = userMapper.queryUserCount(queryUserPO);

        if (count > 0) {
            return -3;
        }

        // 查询公司配置是否需要同步员工到hr系统
        OrgPO orgPO = new OrgPO();
        orgPO.setOrgCode(employeeUserDTO.getOrgCode());
        OrgPO userCompany = orgMapper.selectOne(orgPO);

        UserPO userPO = new UserPO();
        userPO.setApprovalStatus(0);
        userPO.setActive(1);
        if (userCompany.getHrIsOpen() == 1) {
            userPO.setApprovalStatus(2);
            userPO.setActive(0);
        }
        userPO.setUserAccount(employeeUserDTO.getEmployeeAccount());
        userPO.setUserTel(tel);
        userPO.setUserIDNumber(idNumber);
        userPO.setVisitIp(employeeUserDTO.getVisitIp());
        userPO.setOrgCode(employeeUserDTO.getOrgCode());
        userPO.setUserName(employeeUserDTO.getEmployeeName());
        userPO.setUserAccountType(employeeUserDTO.getEmployeeAccountType());
        userPO.setVisitStartTime(employeeUserDTO.getVisitStartTime());
        userPO.setVisitEndTime(employeeUserDTO.getVisitEndTime());
        userPO.setAccountAvlStartTime(employeeUserDTO.getAccountAvlStartTime());
        userPO.setAccountAvlEndTime(employeeUserDTO.getAccountAvlEndTime());
        userPO.setCreatedDt(DateUtilX.dateToString(new Date(), "yyyy-MM-dd HH:mm:ss"));
        userPO.setAvailableStatus(1);
        userPO.setIsNotePermission(employeeUserDTO.getIsNotePermission());
        userPO.setAccountStatus(EmployeeAccountStatusEnum.VALID.no);
        String userPwd = SM4Utils.encrypt(employeeUserDTO.getEmployeeAccountPwd(), Sm4O.defaultKey);
        userPO.setUserPwd(userPwd);
        userPO.setSignaturePwd(SM3Utils.encrypt(userPwd));
        userMapper.insert(userPO);

        if (CollUtilX.isNotEmpty(employeeUserDTO.getEmployeeRoleList())) {
            List<UserRolePO> userRolePOList = new ArrayList<UserRolePO>();
            for (EmployeeRoleDTO employeeRoleDTO : employeeUserDTO.getEmployeeRoleList()) {
                UserRolePO userRolePO = new UserRolePO();
                userRolePO.setUserId(userPO.getUserId());
                userRolePO.setRoleId(employeeRoleDTO.getEmployeeRoleId());
                userRolePO.setCreatedBy(employeeUserDTO.getCreatedBy());
                userRolePO.setCreatedDt(DateUtilX.dateToString(new Date(), "yyyy-MM-dd HH:mm:ss"));
                userRolePOList.add(userRolePO);
            }

            userRoleMapper.insertList(userRolePOList);
        }

        // 新增成功，推送员工信息到hr
        if (userCompany.getHrIsOpen() == 1) {
            // 对接hr， 调用hr接口
            UserPO user = userMapper.selectByPrimaryKey(userPO.getUserId());
            String result = submitUserInfo(user, userCompany);
            log.info("result" + result);
            //错误内容为空，请求成功
            user.setApprovalStatus(1);
            if (StringUtils.isEmpty(result)) {
                user.setApprovalStatus(2);
            }
            userMapper.updateByPrimaryKey(user);
        }
        return 1;
    }

    /**
     * 修改员工信息
     * （只包括修改用户基本信息，启用性，以及权限）（不包括密码修改）
     */
    @Override
    @Transactional
    public int modifyEmployeeUser(EmployeeUserDTO employeeUserDTO) {
        //临时账户有效期最长为90天
        if (employeeUserDTO.getEmployeeAccountType() != null && employeeUserDTO.getEmployeeAccountType() == 1
                && StrUtilX.isNotEmpty(employeeUserDTO.getAccountAvlStartTime()) && StrUtilX.isNotEmpty(employeeUserDTO.getAccountAvlEndTime())) {
            DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDate accountAvlStartTime = LocalDate.parse(employeeUserDTO.getAccountAvlStartTime(), dtf);
            LocalDate accountAvlEndTime = LocalDate.parse(employeeUserDTO.getAccountAvlEndTime(), dtf);
            LocalDate today = LocalDate.now();
            if (!(today.isEqual(accountAvlStartTime) || today.isBefore(accountAvlStartTime))) {//临时账户有效期开始日期为当前日期或往后
                return -5;
            }

            long days = accountAvlEndTime.toEpochDay() - accountAvlStartTime.toEpochDay() + 1;//天数差
            if (days <= 0 || days > 90) {
                return -1;
            }
        }

        //查询非管理员的用户，因为管理员不能被修改
        UserPO existPO = userMapper.selectByPrimaryKey(employeeUserDTO.getEmployeeId());

        //existPO.getIsSuperAdmin() != null 前端控制超级用户不可修改，后端不控制
        if (existPO == null) {
            return -1;
        }

        //一个用户仅可一条正在申请
//        UserApplyPO existApplyPO = new UserApplyPO();
//        existApplyPO.setUserId(employeeUserDTO.getEmployeeId());
//        existApplyPO.setApprovalStatus(0);
//        UserApplyPO existApply = userApplyMapper.selectOne(existApplyPO);
//        if (existApply != null) {
//            return -4;
//        }

        // 若是当前用户的话，则不能修改
        if (existPO.getUserAccount().equals(employeeUserDTO.getModifiedAccount())) {
            return -2;
        }

        UserPO userPO = new UserPO();
        userPO.setUserId(existPO.getUserId());

        String tel = "";
        if (StrUtilX.isNotEmpty(employeeUserDTO.getEmployeeTel())) {
            tel = employeeUserDTO.getEmployeeTel();
        }
        String idNumber = "";
        if (StrUtilX.isNotEmpty(employeeUserDTO.getEmployeeIDNumber())) {
            idNumber = employeeUserDTO.getEmployeeIDNumber();
        }

        if (StrUtilX.isNotEmpty(employeeUserDTO.getEmployeeTel()) || StrUtilX.isNotEmpty(employeeUserDTO.getEmployeeIDNumber())) {
            UserPO queryUserPO = new UserPO();
            queryUserPO.setUserId(employeeUserDTO.getEmployeeId());
            queryUserPO.setUserTel(tel);
            queryUserPO.setUserIDNumber(idNumber);
            int count = userMapper.queryUserCount(queryUserPO);
            if (count > 0) {
                return -3;
            }
        }

        UserApplyPO userApplyPO = CommonDtoConvert.INSTANCE.UserApplyPOConvert(existPO);
        userApplyPO.setUserId(employeeUserDTO.getEmployeeId());

        StringBuilder content = new StringBuilder();
        if (StrUtilX.isNotEmpty(employeeUserDTO.getEmployeeTel())) {
            userApplyPO.setUserTel(tel);
            if (!existPO.getUserTel().equals(employeeUserDTO.getEmployeeTel())) {
                content.append("修改了手机号； ");
                userPO.setUserTel(tel);
            }
        } else {
            userApplyPO.setUserTel(existPO.getUserTel());
        }

        if (StrUtilX.isNotEmpty(employeeUserDTO.getEmployeeIDNumber())) {
            userApplyPO.setUserIDNumber(idNumber);
            if (!existPO.getUserIDNumber().equals(employeeUserDTO.getEmployeeIDNumber())) {
                content.append("修改了身份证；");
                userPO.setUserIDNumber(idNumber);
            }
        } else {
            userApplyPO.setUserIDNumber(existPO.getUserIDNumber());
        }

        if (StrUtilX.isNotEmpty(employeeUserDTO.getEmployeeName())) {
            userApplyPO.setUserName(employeeUserDTO.getEmployeeName());
            if (!existPO.getUserName().equals(employeeUserDTO.getEmployeeName())) {
                content.append(content("姓名", existPO.getUserName(), employeeUserDTO.getEmployeeName()));
                userPO.setUserName(employeeUserDTO.getEmployeeName());
            }
        }

        if (StrUtilX.isNotEmpty(employeeUserDTO.getVisitIp())) {
            userApplyPO.setVisitIp(employeeUserDTO.getVisitIp());
            if (!existPO.getVisitIp().equals(employeeUserDTO.getVisitIp())) {
                content.append(content("访问IP", existPO.getVisitIp(), employeeUserDTO.getVisitIp()));
                userPO.setVisitIp(employeeUserDTO.getVisitIp());
            }
        }

        if (StrUtilX.isNotEmpty(employeeUserDTO.getVisitStartTime())) {
            userApplyPO.setVisitStartTime(employeeUserDTO.getVisitStartTime());
            if (!existPO.getVisitStartTime().equals(employeeUserDTO.getVisitStartTime())) {
                content.append(content("账号访问开始时间", existPO.getVisitStartTime(), employeeUserDTO.getVisitStartTime()));
                userPO.setVisitStartTime(employeeUserDTO.getVisitStartTime());
            }
        }

        if (StrUtilX.isNotEmpty(employeeUserDTO.getVisitEndTime())) {
            userApplyPO.setVisitEndTime(employeeUserDTO.getVisitEndTime());
            if (!existPO.getVisitEndTime().equals(employeeUserDTO.getVisitEndTime())) {
                content.append(content("账号访问结束时间", existPO.getVisitEndTime(), employeeUserDTO.getVisitEndTime()));
                userPO.setVisitEndTime(employeeUserDTO.getVisitEndTime());
            }
        }

        if (employeeUserDTO.getIsNotePermission() != null) {
            userApplyPO.setIsNotePermission(employeeUserDTO.getIsNotePermission());
            if (!Objects.equals(employeeUserDTO.getIsNotePermission(), existPO.getIsNotePermission())) {
                if (employeeUserDTO.getIsNotePermission() == 0) {
                    content.append("关闭教程编辑权限；");
                    userPO.setIsNotePermission(0);
                } else {
                    content.append("打开教程编辑权限；");
                    userPO.setIsNotePermission(1);
                }
            }
        }

        if (!Objects.equals(employeeUserDTO.getEmployeeAccountType(), existPO.getUserAccountType())) {
            userApplyPO.setUserAccountType(employeeUserDTO.getEmployeeAccountType());
            userApplyPO.setAccountAvlStartTime(employeeUserDTO.getAccountAvlStartTime());
            userApplyPO.setAccountAvlEndTime(employeeUserDTO.getAccountAvlEndTime());
            userPO.setUserAccountType(employeeUserDTO.getEmployeeAccountType());
            userPO.setAccountAvlStartTime(employeeUserDTO.getAccountAvlStartTime());
            userPO.setAccountAvlEndTime(employeeUserDTO.getAccountAvlEndTime());
            if (employeeUserDTO.getEmployeeAccountType() == 1) {
                content.append(content("账号类型", "长期账号", "短期账号"));
                content.append(content("账号有效期开始时间", "无", employeeUserDTO.getAccountAvlStartTime()));
                content.append(content("账号有效期结束时间", "无", employeeUserDTO.getAccountAvlEndTime()));
            } else {
                content.append(content("账号类型", "短期账号", "长期账号"));
                content.append(content("账号有效期开始时间", existPO.getAccountAvlStartTime(), "无"));
                content.append(content("账号有效期结束时间", existPO.getAccountAvlEndTime(), "无"));
            }
        } else {
            if (StrUtilX.isNotEmpty(employeeUserDTO.getAccountAvlStartTime())) {
                userApplyPO.setAccountAvlStartTime(employeeUserDTO.getAccountAvlStartTime());
                userPO.setAccountAvlStartTime(employeeUserDTO.getAccountAvlStartTime());
                if (!existPO.getAccountAvlStartTime().equals(employeeUserDTO.getAccountAvlStartTime())) {
                    content.append(content("账号有效期开始时间", existPO.getAccountAvlStartTime(), employeeUserDTO.getAccountAvlStartTime()));
                }
            }

            if (StrUtilX.isNotEmpty(employeeUserDTO.getAccountAvlEndTime())) {
                userApplyPO.setAccountAvlEndTime(employeeUserDTO.getAccountAvlEndTime());
                userPO.setAccountAvlEndTime(employeeUserDTO.getAccountAvlEndTime());
                if (!existPO.getAccountAvlEndTime().equals(employeeUserDTO.getAccountAvlEndTime())) {
                    content.append(content("账号有效期结束时间", existPO.getAccountAvlEndTime(), employeeUserDTO.getAccountAvlEndTime()));
                }
            }
        }

        List<UserPO> userPOS = Collections.singletonList(new UserPO(userApplyPO.getUserId()));
        List<EmployeeRoleDTO> employeeRoleDTOS = userMapper.getRoleListByUserId(userPOS);
        String roles = employeeRoleDTOS.stream().map(EmployeeRoleDTO::getEmployeeRoleName).collect(Collectors.joining(","));
        boolean flag = false;
        for (EmployeeRoleDTO employeeRoleDTO : employeeUserDTO.getEmployeeRoleList()) {
            if (!roles.equals(employeeRoleDTO.getEmployeeRoleName())) {
                flag = true;
                break;
            }
        }
        String newRoles = null;
        if (flag) {
            newRoles = employeeUserDTO.getEmployeeRoleList().stream().map(EmployeeRoleDTO::getEmployeeRoleName).collect(Collectors.joining(","));
            content.append(content("角色", roles, newRoles));
        }

//        if (StrUtilX.isNotEmpty(content.toString())) {
//            content.insert(0, "修改用户" + existPO.getUserName() + ":");
//            userApplyPO.setUserRoles(JSON.toJSONString(employeeUserDTO.getEmployeeRoleList()));
//            userApplyPO.setOperationContent(content.toString());
//            userApplyPO.setApprovalStatus(0);
//            userApplyPO.setOperationType(1);
//            userApplyPO.setCreatedBy(employeeUserDTO.getUpdatedBy());
//            userApplyPO.setCreatedDt(DateUtil.dateToString(new Date(), "yyyy-MM-dd HH:mm:ss"));
//            userApplyMapper.insert(userApplyPO);
//        }

        // 修改员工 审核
        List<EmployeeRoleDTO> list = employeeUserDTO.getEmployeeRoleList();


        Map<Integer, EmployeeRoleDTO> roleMap = employeeRoleDTOS.stream().collect(Collectors.toMap(EmployeeRoleDTO::getEmployeeRoleId, Function.identity(), (key1, key2) -> key2));

        List<UserRolePO> addEmployeeUserRolePOS = new ArrayList<UserRolePO>();
        List<UserRolePO> delEmployeeUserRolePOS = new ArrayList<UserRolePO>();
        if (list != null && list.size() > 0) {
            for (EmployeeRoleDTO employeeRoleDTO : list) {
                if (roleMap.containsKey(employeeRoleDTO.getEmployeeRoleId())) {
                    roleMap.remove(employeeRoleDTO.getEmployeeRoleId());
                } else {
                    UserRolePO userRolePO = new UserRolePO();
                    userRolePO.setUserId(userPO.getUserId());
                    userRolePO.setRoleId(employeeRoleDTO.getEmployeeRoleId());
                    userRolePO.setCreatedBy(employeeUserDTO.getUpdatedBy());
                    userRolePO.setCreatedDt(DateUtilX.dateToString(new Date(), "yyyy-MM-dd HH:mm:ss"));
                    addEmployeeUserRolePOS.add(userRolePO);
                }
            }

            if (CollUtilX.isNotEmpty(addEmployeeUserRolePOS)) {
                userRoleMapper.insertList(addEmployeeUserRolePOS);
            }

            if (roleMap.size() > 0) {
                for (EmployeeRoleDTO employeeRoleDTO : roleMap.values()) {
                    UserRolePO deleteUserRole = new UserRolePO();
                    deleteUserRole.setId(employeeRoleDTO.getUserRoleId());

                    delEmployeeUserRolePOS.add(deleteUserRole);
                }
                userMapper.deleteUserRole(delEmployeeUserRolePOS);
            }
        }
        userMapper.updateByPrimaryKeySelective(userPO);
        return 1;
    }

    @Override
    public int modifyEmployeeUserActive(EmployeeUserDTO employeeUserDTO) {
        // 用户注销，不能修改有效状态
        UserPO userPO = userMapper.selectByPrimaryKey(employeeUserDTO.getEmployeeId());
        if (userPO == null || userPO.getAccountStatus() == 2) {
            return -1;
        }

        userPO.setUpdatedBy(employeeUserDTO.getUpdatedBy());
        userPO.setUpdatedDt(DateUtilX.dateToString(new Date(), "yyyy-MM-dd HH:mm:ss"));
        userPO.setActive(employeeUserDTO.getActive());

        // 查询公司配置是否需要同步员工到hr系统
        OrgPO orgPO = new OrgPO();
        orgPO.setOrgCode(userPO.getOrgCode());
        OrgPO userCompany = orgMapper.selectOne(orgPO);
        if (userCompany.getHrIsOpen() == 1) {
            // 账号状态推送给hr
            String result = pushUserStatusToHr(userPO, userCompany, employeeUserDTO.getIp(), employeeUserDTO.getModifiedAccount());
            if (StrUtilX.isNotEmpty(result)) {
                throw new SysException(ErrorCodeEnum.ADMIN_OR_NOT_EXIST_USER.errorCode, result);
            }
        }

        // 用户无效、token失效
        if (employeeUserDTO.getActive() == 0) {
            if (RedisTemplateX.hasKey(ValidatedFilter.MULTI_USER_LOGIN_ACCOUNT.concat(userPO.getUserAccount()))) {
                String token = RedisTemplateX.get(ValidatedFilter.MULTI_USER_LOGIN_ACCOUNT.concat(userPO.getUserAccount()));
                String ip = RedisTemplateX.get(token);
                RedisTemplateX.delete(RedisKey.LOGIN_TOKEN + token);
                if (StrUtilX.isNotEmpty(ip)) {
                    RedisTemplateX.delete(ValidatedFilter.MULTI_USER_LOGIN.concat(ip));
                }
                RedisTemplateX.delete(ValidatedFilter.MULTI_USER_LOGIN_ACCOUNT.concat(userPO.getUserAccount()));
            }
        }

        userMapper.updateByPrimaryKey(userPO);
        return 1;
    }

    /**
     * 推送用户状态给hr
     *
     * @param userPO
     * @param company
     * @return
     */
    private String pushUserStatusToHr(UserPO userPO, OrgPO company, String ip, String modifiedAccount) {
        String content = "账号状态改为无效";

        // 组装参数
        UpdateAccountStatusReq updateAccountStatusReq = new UpdateAccountStatusReq();
        updateAccountStatusReq.setUserId(userPO.getUserId().toString());
        updateAccountStatusReq.setAccountStatus(1);
        updateAccountStatusReq.setUpdatedBy(userPO.getUpdatedBy());
        updateAccountStatusReq.setUpdatedDt(userPO.getUpdatedDt());
        updateAccountStatusReq.setUpdatedAccount(modifiedAccount);
        if (userPO.getActive() == 1) {
            // 有效
            content = "账号状态改为有效";
            updateAccountStatusReq.setAccountStatus(0);
        } else if (userPO.getAccountStatus() == EmployeeAccountStatusEnum.LOGOUT.no) {
            // 注销 = 删除
            content = "账号状态改为已删除";
            updateAccountStatusReq.setAccountStatus(2);
        }

        // 通知hr员工账号状态
        HrResponse<String> hrResponse = hrManager.updateAccountStatus(updateAccountStatusReq, company);
        if (hrResponse.getResult() == 0) {
            return hrResponse.getMsg();
        }

        // 操作日志写入hr
        SystemLogAddReq systemLogAddReq = new SystemLogAddReq();
        systemLogAddReq.setAccount(userPO.getUserAccount());
        systemLogAddReq.setUpdatedBy(userPO.getUpdatedBy());
        systemLogAddReq.setUpdatedDt(userPO.getUpdatedDt());
        systemLogAddReq.setContent(content);
        systemLogAddReq.setIpAddress(ip);
        hrManager.systemLogAdd(systemLogAddReq, company);

        return null;
    }

    @Override
    public int employeeResubmit(EmployeeUserDTO employeeUserDTO) {
        // 用户注销，不能修改有效状态
        UserPO userPO = userMapper.selectByPrimaryKey(employeeUserDTO.getEmployeeId());
        if (userPO == null || userPO.getAccountStatus() == 2) {
            return -1;
        }

        // 查询公司配置是否需要同步员工到hr系统
        OrgPO orgPO = new OrgPO();
        orgPO.setOrgCode(userPO.getOrgCode());
        OrgPO userCompany = orgMapper.selectOne(orgPO);

        // 重新提交用户信息到hr
        String result = submitUserInfo(userPO, userCompany);
        if (StrUtilX.isNotEmpty(result)) {
            throw new SysException(ErrorCodeEnum.ADMIN_OR_NOT_EXIST_USER.errorCode, result);
        }

        userPO.setUpdatedBy(employeeUserDTO.getUpdatedBy());
        userPO.setUpdatedDt(employeeUserDTO.getUpdatedDt());
        userPO.setApprovalStatus(2);
        userMapper.updateByPrimaryKey(userPO);
        return 1;
    }

    @Override
    public int getEmployeeApprovalStatus(HrRequest<EmployeeApprovalStatusReq> request, String ip) {
        EmployeeApprovalStatusReq statusReq = request.getData();
        // 用户注销，不能修改有效状态
        UserPO userPO = userMapper.selectByPrimaryKey(Integer.valueOf(statusReq.getUserId()));
        if (userPO == null) {
            return -1;
        }

        userPO.setApprovalStatus(3);
        userPO.setUpdatedBy("HR");
        userPO.setActive(1);
        userPO.setUpdatedDt(DateUtilX.dateToString(new Date(), "yyyy-MM-dd HH:mm:ss"));
        for (Approval approval : statusReq.getApproverList()) {
            if (approval.getApprovalStatus() == 1) {
                userPO.setApprovalStatus(2);
                break;
            } else if (approval.getApprovalStatus() == 3) {
                // 不通过，账号无效
                userPO.setApprovalStatus(4);
                userPO.setActive(0);

                // hr通知，无需同步给hr
//                OrgPO orgPO = new OrgPO();
//                orgPO.setOrgCode(userPO.getOrgCode());
//                OrgPO userCompany = orgMapper.selectOne(orgPO);
//                pushUserStatusToHr(userPO, userCompany, ip, "HR");
                break;
            }
        }
        userMapper.updateByPrimaryKey(userPO);
        return 1;
    }

    /**
     * 提交员工信息
     *
     * @param userPO
     */
    private String submitUserInfo(UserPO userPO, OrgPO company) {

        UserSubmitApprovalReq userSubmitApprovalReq = new UserSubmitApprovalReq();
        // 查询员工角色
        String roleName = userMapper.getRoleByUserId(userPO.getUserId());

        // 员工信息转换
        Map<String, String> map = new HashMap<>();
        map.put("员工姓名", userPO.getUserName());
        map.put("账号", userPO.getUserAccount());
        map.put("手机号", userPO.getUserTel());
        map.put("账号角色", roleName);
        map.put("userId", userPO.getUserId().toString());

        List<TemplateData> dataList = new ArrayList<>();
        for (Map.Entry<String, String> entry : map.entrySet()) {
            TemplateData templateData = new TemplateData();
            templateData.setTitle(entry.getKey());
            templateData.setValue(entry.getValue());
            dataList.add(templateData);
        }
        userSubmitApprovalReq.setTemplateDataList(dataList);
        userSubmitApprovalReq.setCallbackUrl(company.getHrCallbackUrl());

        // 调用hr接口新增员工
        HrResponse<String> hrResponse = hrManager.wxSubmitApprovalTemplate(userSubmitApprovalReq, company);
        if (hrResponse.getResult() == 0) {
            return hrResponse.getMsg();
        }
        return null;
    }

    private String content(String type, Object oldInfo, Object newInfo) {
        return type + ",从" + oldInfo + "改为" + newInfo + "； ";
    }

    /**
     * 重置密码
     */
    @Override
    public int modifyEmployeePwd(UserPO userPO, boolean flag) {
        UserPO existPO = userMapper.selectByPrimaryKey(userPO.getUserId());
        if (existPO == null) {
            return -1;
        }

        String pwd = CompanyDTO.PWD;
        if (StrUtilX.isNotEmpty(userPO.getUserPwd())) {
            pwd = userPO.getUserPwd();
        }
        pwd = SM4Utils.encrypt(pwd, Sm4O.defaultKey);

        // 管理员修改员工密码需走审批
//        if (flag) {
        //一个用户仅可一条正在申请
//            UserApplyPO existApplyPO = new UserApplyPO();
//            existApplyPO.setUserId(userPO.getUserId());
//            existApplyPO.setApprovalStatus(0);
//            UserApplyPO existApply = userApplyMapper.selectOne(existApplyPO);
//            if (existApply != null) {
//                return -3;
//            }

//            List<UserPO> userPOS = Arrays.asList(new UserPO(userPO.getUserId()));
//            List<EmployeeRoleDTO> employeeRoleDTOS = userMapper.getRoleListByUserId(userPOS);
//            UserApplyPO userApplyPO = new UserApplyPO();
//            BeanUtils.copyProperties(existPO, userApplyPO);
//            userApplyPO.setUserRoles(JSON.toJSONString(employeeRoleDTOS));
//            userApplyPO.setOperationContent("修改" + existPO.getUserName() + "用户的密码；");
//            userApplyPO.setApprovalStatus(0);
//            userApplyPO.setOperationType(1);
//            userApplyPO.setCreatedBy(userPO.getUpdatedBy());
//            userApplyPO.setCreatedDt(DateUtil.dateToString(new Date(), "yyyy-MM-dd HH:mm:ss"));
//            userApplyPO.setUserPwd(pwd);
//            return userApplyMapper.insert(userApplyPO);
//        }

        UserPO existUser = new UserPO();
        existUser.setUserPwd(pwd);
        existUser.setUserId(existPO.getUserId());
        existUser.setUpdatePwdTime(new Date());
        // 密码加签
        existUser.setSignaturePwd(SM3Utils.encrypt(pwd));
        existUser.setUpdatedDt(DateUtilX.dateToString(new Date(), "yyyy-MM-dd HH:mm:ss"));

        // 修改用户密码成功需重新登录
        if (RedisTemplateX.hasKey(ValidatedFilter.MULTI_USER_LOGIN_ACCOUNT.concat(existPO.getUserAccount()))) {
            String token = RedisTemplateX.get(ValidatedFilter.MULTI_USER_LOGIN_ACCOUNT.concat(existPO.getUserAccount()));
            String ip = RedisTemplateX.get(token);
            RedisTemplateX.delete(RedisKey.LOGIN_TOKEN + token);
            if (StrUtilX.isNotEmpty(ip)) {
                RedisTemplateX.delete(ValidatedFilter.MULTI_USER_LOGIN.concat(ip));
            }
            RedisTemplateX.delete(ValidatedFilter.MULTI_USER_LOGIN_ACCOUNT.concat(existPO.getUserAccount()));
        }
        return userMapper.updateByPrimaryKeySelective(existUser);
    }

    /**
     * 删除员工
     */
    @Override
    public int deleteEmployeeUser(UserPO userPO, String modifiedAccount, HttpServletRequest request) {
        UserPO existUser = userMapper.getUserWithoutAdmin(userPO.getUserId());

        if (existUser == null) {
            return -1;
        }

        if (existUser.getUserAccount().equals(modifiedAccount)) {
            return -2;
        }

        userPO.setActive(0);
        userPO.setAccountStatus(2);
        userPO.setUserAccount(existUser.getUserAccount());
        userPO.setUpdatedDt(DateUtilX.dateToString(new Date(), "yyyy-MM-dd HH:mm:ss"));

        // 查询公司配置是否需要同步员工到hr系统
        OrgPO orgPO = new OrgPO();
        orgPO.setOrgCode(existUser.getOrgCode());
        OrgPO userCompany = orgMapper.selectOne(orgPO);
        if (userCompany.getHrIsOpen() == 1) {
            // 账号状态推送给hr
            String result = pushUserStatusToHr(userPO, userCompany, IpUtil.getIpAddress(request), modifiedAccount);
            if (StrUtilX.isNotEmpty(result)) {
                throw new SysException(ErrorCodeEnum.ADMIN_OR_NOT_EXIST_USER.errorCode, result);
            }
        }

        int i = userMapper.updateByPrimaryKeySelective(userPO);

        //添加日志
        OperateLogReqDTO newDO = new OperateLogReqDTO();
        newDO.setCreatedDt(LocalDateTime.now());
        newDO.setLogName("删除员工" + existUser.getUserAccount());
        newDO.setLogType("info");
        newDO.setLogLevel(0);
        newDO.setOperationType(2);
        newDO.setOperationResult(1);
        newDO.setApplicationName("base");
        newDO.setRequestMethod("POST");
        newDO.setRequestIp(IpUtil.getIpAddress(request));
        newDO.setRequestHeader(request.getHeader("User-Agent"));
        newDO.setRequestUrl("/common/user/deleteEmployee");
        newDO.setCreatedBy(modifiedAccount + "(" + userPO.getUpdatedBy() + ")");
        newDO.setUserAccount(modifiedAccount);
        OperateLogEntity entity = CommonDtoConvert.INSTANCE.OperateLogConvert(newDO);
        operateLogMapper.insert(entity);
        //日志结束

        // 删除员工，token失效
        if (RedisTemplateX.hasKey(ValidatedFilter.MULTI_USER_LOGIN_ACCOUNT.concat(existUser.getUserAccount()))) {
            String token = RedisTemplateX.get(ValidatedFilter.MULTI_USER_LOGIN_ACCOUNT.concat(existUser.getUserAccount()));
            String ip = RedisTemplateX.get(token);
            RedisTemplateX.delete(RedisKey.LOGIN_TOKEN + token);
            if (StrUtilX.isNotEmpty(ip)) {
                RedisTemplateX.delete(ValidatedFilter.MULTI_USER_LOGIN.concat(ip));
            }
            RedisTemplateX.delete(ValidatedFilter.MULTI_USER_LOGIN_ACCOUNT.concat(existUser.getUserAccount()));
        }

        return i;
    }

    @Override
    public EmployeeUserDTO getEmployeeUserDetail(Map<String, String> requestMap) {
        EmployeeUserDTO employeeUserDTO = userMapper.queryEmployeeDetail(requestMap);
        if (employeeUserDTO != null) {
            List<EmployeeRoleDTO> roleList = userMapper.getRoleList(Collections.singletonList(employeeUserDTO));
            assemblyEmployee(Collections.singletonList(employeeUserDTO), roleList);
        }
        return employeeUserDTO;
    }

    @Override
    public EmployeeUserDTO getCurrentUserDetail(Map<String, String> requestMap) {
        EmployeeUserDTO employeeUserDTO = userMapper.getCurrentUserDetail(requestMap);
        if (employeeUserDTO != null) {
            List<EmployeeRoleDTO> roleList = userMapper.getRoleList(Collections.singletonList(employeeUserDTO));
            assemblyEmployee(Collections.singletonList(employeeUserDTO), roleList);
        }
        return employeeUserDTO;
    }

    /**
     * 检查是否存在该用户账号
     *
     * @param userAccount 账号信息
     * @return 1为存在，0为不存在
     */
    @Override
    public int examineUserAccount(String userAccount) {
        int i = userMapper.queryUserAccount(userAccount);
        if (i > 0) {
            return 1;
        }
        return 0;
    }

    @Override
    public int examineUserAccountOrUserTel(UserPO userPO) {
        ExamineUserDTO dto = CommonDtoConvert.INSTANCE.ExamineUserConvert(userPO);
        if (!StringUtils.isEmpty(dto.getUserTel())) {
            try {
                dto.setUserTel((String) EncryptUtil.encrypt(dto.getUserTel()));
            } catch (Exception e) {
                log.error("转换用户手机号异常", e);
            }
        }
        //先判断手机号
       /* dto.setUserAccount(null);
        int i = userMapper.examineUserAccountOrUserTel(dto);
        if (i > 0) {
            return 1;
        }*/
        //在判断用户账号
        dto.setUserAccount(userPO.getUserAccount());
        dto.setUserTel(null);
        int i = userMapper.examineUserAccountOrUserTel(dto);
        if (i > 0) {
            return 1;
        }
        return 0;
    }

    /**
     * 通过登录名查询目录
     */
    @Override
    public List<MenuDTO> queryMenuListByLoginName(Map<String, String> requestMap) {
        return userMapper.queryMenuListByLoginName(requestMap);
    }

    /**
     * 用户登录
     */
    @Override
    public UserDTO login(UserDTO userDTO) throws Exception {

        Map<String, String> requestMap = new HashMap<>();
        requestMap.put("userAccount", userDTO.getLoginAccount());
        UserDTO user = userMapper.queryLoginUser(requestMap);

        if (user == null) {
            throw new Exception(ErrorCodeEnum.NOT_EXIST_USER.errorCode);
        }
        if (user.getAvailableStatus().equals(0)) {
            throw new Exception(ErrorCodeEnum.USER_IS_DISABLED.errorCode);
        }
        String pwd = null;
        try {
            pwd = passwordEncoder.encode(CompanyDTO.PWD);
        } catch (Exception e) {
            log.error("md5加密密码失败");
        }

        if (pwd == null || !pwd.equals(user.getLoginPwd())) {
            throw new Exception(ErrorCodeEnum.PASSWORD_ERROR.errorCode);
        }

        //角色
        EmployeeUserDTO employeeUserDTO = new EmployeeUserDTO();
        employeeUserDTO.setEmployeeId(user.getLoginId());
        List<EmployeeRoleDTO> roleList = userMapper.getRoleList(Collections.singletonList(employeeUserDTO));
        assemblyEmployee(Collections.singletonList(employeeUserDTO), roleList);

        List<CompanyBusinessLicenseUrlDTO> pictureLicenseDTOS = pictureMapper.PictureLicense(user.getCompanyCode(), PictureTypeEnum.TWO.key);
        if (CollUtilX.isNotEmpty(pictureLicenseDTOS)) {
            user.setCompanyLogoUrl(pictureLicenseDTOS.get(0).getUrl());
        }


        //菜单
        Map<String, String> request = new HashMap<String, String>();
        request.put("userAccount", userDTO.getLoginAccount());
        List<MenuDTO> menuDTOS = userMapper.queryMenuListByLoginName(request);
        List<FirstMenuDTO> firstMenuDTOS = combinationMenu(menuDTOS, null);
        userDTO = user;
        userDTO.setRoleList(employeeUserDTO.getEmployeeRoleList());
        userDTO.setLoginPwd(null);
        userDTO.setSalt(null);
        userDTO.setMenus(firstMenuDTOS);
        return userDTO;
    }


    /**
     * 组合用户以及对应的角色
     */
    private void assemblyEmployee(List<EmployeeUserDTO> employeeUserDTOS, List<EmployeeRoleDTO> employeeRoleDTOS) {
        Map<Integer, EmployeeUserDTO> employeeUserDTOMap = employeeUserDTOS.stream().collect(Collectors.toMap(EmployeeUserDTO::getEmployeeId, Function.identity(), (key1, key2) -> key2));
        for (EmployeeRoleDTO employeeRoleDTO : employeeRoleDTOS) {
            List<EmployeeRoleDTO> employeeRoleDTOList = new ArrayList<EmployeeRoleDTO>();
            EmployeeUserDTO employeeUserDTO = employeeUserDTOMap.get(employeeRoleDTO.getEmployeeId());
            if (CollUtilX.isEmpty(employeeUserDTO.getEmployeeRoleList())) {
                employeeRoleDTOList = Collections.singletonList(employeeRoleDTO);
            } else {
                employeeRoleDTOList = new ArrayList<EmployeeRoleDTO>(employeeUserDTO.getEmployeeRoleList());
                employeeRoleDTOList.add(employeeRoleDTO);
            }

            if (CollUtilX.isNotEmpty(employeeRoleDTOList)) {
                employeeUserDTO.setEmployeeRoleList(employeeRoleDTOList);
            }

            // 在线/离线
            if (RedisTemplateX.hasKey(StrUtilX.concat(ValidatedFilter.MULTI_USER_LOGIN_ACCOUNT, employeeUserDTO.getEmployeeAccount()))) {
                employeeUserDTO.setEmployeeStatus(1);
            } else {
                employeeUserDTO.setEmployeeStatus(0);
            }
        }
    }


    /**
     * 组装目录
     */
    private List<FirstMenuDTO> combinationMenu(List<MenuDTO> menuDTOS, Map<Integer, Integer> selectedMap) {
        List<FirstMenuDTO> firstMenuDTOS = new ArrayList<FirstMenuDTO>();

        for (MenuDTO menuDTO : menuDTOS) {
            if (menuDTO.getMenuLevel() == 1) {
                FirstMenuDTO firstMenuDTO = CommonDtoConvert.INSTANCE.FirstMenuConvert(menuDTO);
                firstMenuDTO.setPath(menuDTO.getFrontEndUrl() == null ? null : menuDTO.getFrontEndUrl());
                firstMenuDTO.setFirstMenu(menuDTO.getMenuName());
                if (null == selectedMap || null != selectedMap.get(menuDTO.getMenuId())) {
                    firstMenuDTO.setSelected(1);
                }
                firstMenuDTOS.add(firstMenuDTO);

            }
        }

        Collections.sort(firstMenuDTOS, new Comparator<FirstMenuDTO>() {
            @Override
            public int compare(FirstMenuDTO o1, FirstMenuDTO o2) {
                return o1.getMenuRank() - o2.getMenuRank();
            }
        });


        Map<String, FirstMenuDTO> menuMap = firstMenuDTOS.stream().collect(Collectors.toMap(FirstMenuDTO::getMenuCode, Function.identity(), (key1, key2) -> key2));

        for (MenuDTO menuDTO : menuDTOS) {
            if (menuDTO.getMenuLevel() == 2) {
                List<MenuLevelDTO> secondMenuDTOs = new ArrayList<MenuLevelDTO>();
                if (CollUtilX.isNotEmpty(menuMap.get(menuDTO.getParentCode()).getSecondMenuList())) {
                    secondMenuDTOs = menuMap.get(menuDTO.getParentCode()).getSecondMenuList();
                }
                MenuLevelDTO menuLevelDTO = CommonDtoConvert.INSTANCE.MenuLevelConvert(menuDTO);
                menuLevelDTO.setPath(menuDTO.getFrontEndUrl() == null ? null : menuDTO.getFrontEndUrl());
                menuLevelDTO.setSecondMenu(menuDTO.getMenuName());
                if (null == selectedMap || null != selectedMap.get(menuDTO.getMenuId())) {
                    menuLevelDTO.setSelected(1);
                }
                secondMenuDTOs.add(menuLevelDTO);
                menuMap.get(menuDTO.getParentCode()).setSecondMenuList(secondMenuDTOs);
            }
        }


        for (FirstMenuDTO firstMenuDTO : firstMenuDTOS) {
            if (CollUtilX.isNotEmpty(firstMenuDTO.getSecondMenuList())) {
                firstMenuDTO.getSecondMenuList().sort(Comparator.comparingInt(MenuLevelDTO::getMenuRank));
            }
        }

        firstMenuDTOS.removeIf(firstMenuDTO -> firstMenuDTO.getSecondMenuList() == null && StrUtilX.isEmpty(firstMenuDTO.getPath()));

        return firstMenuDTOS;
    }

    /**
     * 查询角色列表
     */
    @Override
    public PageInfo<RoleDTO> queryRoleList(Map<String, String> requestMap) {
        PageHelper.startPage(Integer.parseInt(requestMap.get("currentPage")), Integer.parseInt(requestMap.get("pageSize")));
        List<RoleDTO> roleListDTO = userMapper.queryRoleList(requestMap);
        return new PageInfo<>(roleListDTO);
    }

    /**
     * 查询角色及菜单信息
     */
    @Override
    public List<FirstMenuDTO> queryMenuList(Map<String, String> requestMap) {
        //查询所有菜单
        List<MenuPO> menuPOList = menuMapper.selectAll();
        List<MenuDTO> menuDTOList = new ArrayList<>();
        for (MenuPO menuPO : menuPOList) {
            MenuDTO menuDTO = new MenuDTO();
            menuDTO.setMenuId(menuPO.getId());
            menuDTO.setFrontEndUrl(menuPO.getPath());
            menuDTO.setMenuCode(menuPO.getMenuCode());
            menuDTO.setMenuName(menuPO.getMenuName());
            menuDTO.setMenuRank(menuPO.getMenuRank());
            menuDTO.setMenuLevel(menuPO.getMenuLevel());
            menuDTO.setParentCode(menuPO.getParentCode());
            menuDTOList.add(menuDTO);
        }

        return combinationMenu(menuDTOList, null);
    }

    /**
     * 查询角色菜单列表
     */
    @Override
    public RoleDTO queryRoleMenuList(Map<String, String> requestMap) {
        //查询角色对应菜单
        RoleMenuPO roleMenuPO = new RoleMenuPO();
        roleMenuPO.setRoleId(Integer.valueOf(requestMap.get("roleId")));
        List<RoleMenuPO> roleMenuPOList = roleMenuMapper.select(roleMenuPO);
        List<String> roleMenuList = null;
        if (null != roleMenuPOList) {
            roleMenuList = new ArrayList<>();
            for (RoleMenuPO roleMenuPO1 : roleMenuPOList) {
                roleMenuList.add(roleMenuPO1.getMenuId().toString());
            }
        }
        RoleDTO roleMenuDTO = new RoleDTO();
        roleMenuDTO.setRoleId(requestMap.get("roleId"));
        roleMenuDTO.setMenuList(roleMenuList);
        return roleMenuDTO;
    }


    /**
     * 角色编辑
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editRole(RoleDTO roleDTO) {
        if (null != roleDTO) {
            RolePO rolePO = new RolePO();
            if (null != roleDTO.getRoleId()) {//修改
                rolePO.setRoleId(Integer.valueOf(roleDTO.getRoleId()));
                rolePO.setRoleName(roleDTO.getRoleName());
                rolePO.setRoleDescription(roleDTO.getRoleDescription());
                rolePO.setUpdatedDt(DateUtilX.dateToString(new Date(), "yyyy-MM-dd HH:mm:ss"));
                rolePO.setUpdatedBy(roleDTO.getUpdatedBy());
                roleMapper.updateByPrimaryKeySelective(rolePO);

                //修改绑定菜单，先删除，后新增的方式
                editRoleMenu(Integer.valueOf(roleDTO.getRoleId()), roleDTO, 1);
            } else {//新增
                rolePO.setRoleName(roleDTO.getRoleName());
                rolePO.setActive(1);
                rolePO.setRoleCode("M1");
                rolePO.setRoleDescription(roleDTO.getRoleDescription());
                rolePO.setCreatedDt(DateUtilX.dateToString(new Date(), "yyyy-MM-dd HH:mm:ss"));
                rolePO.setCreatedBy(roleDTO.getCreatedBy());
                rolePO.setType(0);//哪个系统的角色(0.运营端，1.供应端， 2.平台端，3.营销端)
                roleMapper.insert(rolePO);

                rolePO.setRoleCode("M" + (10000 + rolePO.getRoleId()));//更新角色编码
                roleMapper.updateByPrimaryKeySelective(rolePO);
                //修改绑定菜单，先删除，后新增的方式
                editRoleMenu(rolePO.getRoleId(), roleDTO, 0);
            }
        }
    }

    /**
     * 操作角色对应菜单，先删除，后新增逻辑
     *
     * @param roleId  角色id
     * @param roleDTO 角色信息
     * @param type    <=0 新增 >0 更新
     */
    private void editRoleMenu(Integer roleId, RoleDTO roleDTO, Integer type) {

        List<RoleMenuPO> roleMenuPOList = new ArrayList<>();
        for (String menuId : roleDTO.getMenuList()) {

            RoleMenuPO roleMenuPO = new RoleMenuPO();
            roleMenuPO.setRoleId(roleId);
            roleMenuPO.setMenuId(Integer.valueOf(menuId));
            roleMenuPOList.add(roleMenuPO);
        }

        //删除角色对应菜单
        if (null != type && type > 0) {
            if (roleId == null) {
                throw new SysException(ParamErrorEnum.EMPTY_PARAM_ID);
            }
            RoleMenuPO roleMenuPO = new RoleMenuPO();
            roleMenuPO.setRoleId(roleId);
            roleMenuMapper.delete(roleMenuPO);
        }

        //新增角色对应菜单
        roleMenuMapper.insertList(roleMenuPOList);
    }

    /**
     * 删除角色
     */
    @Override
    public Response<Object> roleDel(Map<String, String> requestMap) {
        try {
            if (StrUtilX.isNotEmpty(requestMap.get("roleId"))) {
                //查询角色对应的用户
                Integer userCount = userMapper.queryRoleUserCount(requestMap);
                if (null != userCount && userCount > 0) {
                    return Response.error(ErrorCodeEnum.ROLE_HAS_USER.errorCode, ErrorCodeEnum.ROLE_HAS_USER.errorDesc);
                } else {
                    RolePO rolePO = new RolePO();
                    rolePO.setRoleId(Integer.valueOf(requestMap.get("roleId")));
                    rolePO.setActive(0);
                    rolePO.setUpdatedBy(requestMap.get("updatedBy"));
                    rolePO.setUpdatedDt(DateUtilX.dateToString(new Date(), "yyyy-MM-dd HH:mm:ss"));
                    roleMapper.updateByPrimaryKeySelective(rolePO);
                }
            } else {
                return Response.error(ErrorCodeEnum.INVALID_INPUTPARAM.errorCode, ErrorCodeEnum.INVALID_INPUTPARAM.errorDesc);
            }
        } catch (Exception e) {
            log.error("删除角色异常", e);
            throw e;
            //return Response.error(ErrorCodeEnum.SYSTEM_EXCEPTION.errorCode, ErrorCodeEnum.SYSTEM_EXCEPTION.errorDesc);
        }
        return Response.success();
    }

    @Autowired
    private SysConfigService sysConfigService;

    @Override
    public String querySendVerificationCodeType() {
        String sendVerificationCodeTypeReisValue = RedisTemplateX.get(RedisKey.SEND_VERIFICATIONCODE_TYPE_KEY);
        if (sendVerificationCodeTypeReisValue == null) {
            SysConfigPO sysConfigPO = sysConfigService.queryConfig(RedisKey.SEND_VERIFICATIONCODE_TYPE_KEY);
            if (sysConfigPO == null) {
                sendVerificationCodeTypeReisValue = "0";
                sysConfigPO = new SysConfigPO();
                sysConfigPO.setStrKey(RedisKey.SEND_VERIFICATIONCODE_TYPE_KEY);
                sysConfigPO.setStrValue(sendVerificationCodeTypeReisValue);
                sysConfigPO.setRemark("全局验证码发送类型缓存 0 短信 1 邮箱");
                sysConfigService.addOrUpdate(sysConfigPO);
            } else {
                sendVerificationCodeTypeReisValue = sysConfigPO.getStrValue();
            }
            RedisTemplateX.set(RedisKey.SEND_VERIFICATIONCODE_TYPE_KEY, sendVerificationCodeTypeReisValue);
        }
        return sendVerificationCodeTypeReisValue;
    }

    @Override
    public void updateSendVerificationCodeType(String sendVerificationCodeTypeValue) {
        SysConfigPO sysConfigPO = sysConfigService.queryConfig(RedisKey.SEND_VERIFICATIONCODE_TYPE_KEY);
        if (sysConfigPO == null) {//查询然后根据主键修改 没有就新增
            sysConfigPO = new SysConfigPO();
            sysConfigPO.setStrKey(RedisKey.SEND_VERIFICATIONCODE_TYPE_KEY);
            sysConfigPO.setRemark("全局验证码发送类型缓存 0 短信 1 邮箱");
        }
        sysConfigPO.setStrValue(sendVerificationCodeTypeValue);
        sysConfigService.addOrUpdate(sysConfigPO);
        RedisTemplateX.set(RedisKey.SEND_VERIFICATIONCODE_TYPE_KEY, sendVerificationCodeTypeValue);
    }

    /**
     * 发送登录验证码
     * 入参：userAccount，userPassword
     */
    @Override
    public Response<String> sendVerificationCode(AuthUser authUser) {
        String sendVerificationCodeTypeReisValue = querySendVerificationCodeType();
        UserPO userPO = new UserPO();
        userPO.setUserAccount(authUser.getUserAccount());
        userPO.setAvailableStatus(1);
        userPO = userMapper.selectOne(userPO);
        if (Objects.isNull(userPO)) {
//            response.setResult(ResultCodeEnum.FAILURE.code);
//            response.setFailCode(ErrorCodeEnum.SYSTEM_EXCEPTION.errorCode);
//            response.setFailReason("短信发送失败!");
            return Response.success(sendVerificationCodeTypeReisValue);
        }
        if (RedisTemplateX.hasKey(StrUtilX.concat(VERIFICATION_CODE, authUser.getUserAccount()))) {
            Long getTime = RedisTemplateX.getExpire(StrUtilX.concat(VERIFICATION_CODE, authUser.getUserAccount()));
            if (getTime != null && getTime > 240) {
//                response.setResult(ResultCodeEnum.FAILURE.code);
//                response.setFailCode(ErrorCodeEnum.SYSTEM_EXCEPTION.errorCode);
//                response.setFailReason("短信发送失败!");
                return Response.success(sendVerificationCodeTypeReisValue);
            }
        }

        // 测试环境和开发环境使用默认的验证码123456
        String verificationCode = "123456";
        SysConfigPO sysConfigPO = sysConfigMapper.selectOne(new LambdaQueryWrapper<SysConfigPO>().eq(SysConfigPO::getStrKey, "enableSendMsg"));
        System.out.println("查询发送短信数据是：" + sysConfigPO);
        int smsSend = 0;
        if (sysConfigPO != null) {
            smsSend = Integer.parseInt(sysConfigPO.getStrValue());
        }
        if (smsSend == 1) {
            //生产环境获取手机验证码
            verificationCode = generateRandom();
            if (sendVerificationCodeTypeReisValue.equals("0")) { //短信
                String[] userPhone = userPO.getUserTel().split("-");
                String phone = userPO.getUserTel();
                if (userPhone.length > 1) {
                    phone = userPhone[1];
                }
                //封装发送短信实体
                SendPhoneReq req = new SendPhoneReq();
                //req.setTemplateCode(TemplateCodeTypeEnum.CODE.type);
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("code", verificationCode);
                req.setType(1);
                req.setJson(jsonObject);
                req.setPhone(phone);
                if ("86".equals(userPhone[0]) || userPhone.length == 1) {
                    boolean send = sendCodeUtil.sendSms(req);
                    if (!send) {
                        return Response.error(ErrorCodeEnum.SYSTEM_EXCEPTION.errorCode, ErrorCodeEnum.SYSTEM_EXCEPTION.errorCode);
                    }
                } else {
                    try {
                        req.setType(2);
                        req.setPhone(userPO.getUserTel().replace("-", "").replace("+", ""));
                        Boolean send = sendCodeUtil.sendOSea(req);
                        if (!send) {
                            return Response.error(ErrorCodeEnum.SYSTEM_EXCEPTION.errorCode, ErrorCodeEnum.SYSTEM_EXCEPTION.errorCode);
                        }
                    } catch (Exception e) {
                        log.error("发送登录验证码异常", e);
                        return Response.error(ErrorCodeEnum.SYSTEM_EXCEPTION.errorCode, ErrorCodeEnum.SYSTEM_EXCEPTION.errorCode);
                    }
                }
            } else {//邮箱
                try {
                    SendEmailReq sendEmailReq = new SendEmailReq();
                    sendEmailReq.setEmail(userPO.getUserAccount());
                    sendEmailReq.setType(1);
                    sendEmailReq.setTopic("【" + emailConfigProperties.getTitle() + "】G-link天宫登陆验证码");
                    String content = "【" + emailConfigProperties.getTitle() + "】您的验证码是：" + verificationCode + "，该验证码5分钟内有效，请勿向他人泄露";
                    sendEmailReq.setContent(content);
                    boolean flag = sendCodeUtil.sendQQEmail(sendEmailReq);
                    if (!flag) {
                        return Response.error(ErrorCodeEnum.SYSTEM_EXCEPTION.errorCode, ErrorCodeEnum.SYSTEM_EXCEPTION.errorCode);
                    }
                } catch (Exception e) {
                    log.error("sendVerificationCode sendQQEmail userAccount:{} Exception e:{}", userPO.getUserAccount(), e);
                    return Response.error(ErrorCodeEnum.SYSTEM_EXCEPTION.errorCode, ErrorCodeEnum.SYSTEM_EXCEPTION.errorCode);
                }
            }
        }
        RedisTemplateX.setAndExpire(StrUtilX.concat(VERIFICATION_CODE, authUser.getUserAccount()), verificationCode, 60 * 5);
        return Response.success(sendVerificationCodeTypeReisValue);
    }

    @Override
    public int configSystemSetting(SystemSettingDTO systemSettingDTO) {
        if (systemSettingDTO.getMaxOnlineCount() < 1) {
            return -1;
        }
        if (systemSettingDTO.getMaxFailCount() < 1 || systemSettingDTO.getMaxFailCount() > 10) {
            return -2;
        }

        if (systemSettingDTO.getFailLockTime() < 20) {
            return -3;
        }

        RedisTemplateX.set(ValidatedFilter.USER_MAX_ONLINE_COUNT, systemSettingDTO.getMaxOnlineCount().toString());
        RedisTemplateX.set(ValidatedFilter.LOGIN_MAX_FAIL_COUNT, systemSettingDTO.getMaxFailCount().toString());
        RedisTemplateX.set(ValidatedFilter.FAIL_LOCK_TIME, systemSettingDTO.getFailLockTime().toString());
        return 1;
    }

    @Override
    public Map<String, String> querySystemSetting() {
        Map<String, String> resultMap = new HashMap<>();
        resultMap.put("maxOnlineCount", RedisTemplateX.get(ValidatedFilter.USER_MAX_ONLINE_COUNT));
        resultMap.put("maxFailCount", RedisTemplateX.get(ValidatedFilter.LOGIN_MAX_FAIL_COUNT));
        resultMap.put("failLockTime", RedisTemplateX.get(ValidatedFilter.FAIL_LOCK_TIME));
        return resultMap;
    }

    @Override
    public int updateStatus(Map<String, String> requestMap) {
        String employeeAccount = requestMap.get("employeeAccount");
        if (StrUtilX.isNotEmpty(requestMap.get("employeeId"))) {
            UserPO userPO = new UserPO();
            userPO.setUserId(Integer.valueOf(requestMap.get("employeeId")));
            UserPO user = userMapper.selectOne(userPO);
            if (user == null || user.getUserAccount().equals(requestMap.get("modifiedAccount"))) {
                return -2;
            }
        }

        //用户连续登录失败次数达到限制数量--记录对应日志以及发送短信
        if (requestMap.get("accountStatus").equals("3")) {
            UserPO userPO = new UserPO();
            userPO.setUserAccount(employeeAccount);
            userPO.setAccountStatus(0);
            UserPO user = userMapper.selectOne(userPO);
            if (user == null || user.getIsSuperAdmin() != null) {
                return 0;
            }
            // 锁定账号
            RedisTemplateX.set(ValidatedFilter.LOCK_STATUS_USER.concat(employeeAccount), "1");

            //解锁时间
            Date date = new Date();
            if (RedisTemplateX.hasKey(ValidatedFilter.FAIL_LOCK_TIME)) {
                date = DateUtilX.getDate(date, 0, 0, Integer.parseInt(RedisTemplateX.get(ValidatedFilter.FAIL_LOCK_TIME)));
                RedisTemplateX.expire(ValidatedFilter.LOCK_STATUS_USER.concat(employeeAccount), Integer.parseInt(RedisTemplateX.get(ValidatedFilter.FAIL_LOCK_TIME)) * 60);
            } else {
                date = DateUtilX.getDate(date, 0, 0, 20);
                RedisTemplateX.expire(ValidatedFilter.LOCK_STATUS_USER.concat(employeeAccount), 60 * 20);
            }

            userPO.setUserId(user.getUserId());
            userPO.setUnlockTime(date);
            userMapper.updateByPrimaryKeySelective(userPO);
        } else {
            RedisTemplateX.delete(ValidatedFilter.LOGIN_FAIL.concat(employeeAccount));
            RedisTemplateX.delete(ValidatedFilter.LOCK_STATUS_USER.concat(employeeAccount));
            requestMap.put("unlockTime", null);
            userMapper.updateAccountStatus(requestMap);
        }

        return 1;
    }

    /**
     * 生辰验证码随机数
     */
    public String generateRandom() {
        return String.valueOf((int) (Math.random() * 900000 + 100000));
    }


    @Override
    public UserDTO queryLoginUser(Map<String, String> requestMap) {
        return userMapper.queryLoginUser(requestMap);
    }

    @Override
    public void autoLogoutAccount(String param) {
        //自动注销账号需发送短信警告
        List<EmployeeUserDTO> userList = userMapper.queryLogoutAccount();
        if (null != param && param.equals("1")) {
            for (EmployeeUserDTO employeeUserDTO : userList) {
                Example example = new Example(UserPO.class);
                Example.Criteria criteria = example.createCriteria();
                criteria.andEqualTo("orgCode", employeeUserDTO.getOrgCode());
                criteria.andEqualTo("isSuperAdmin", 1);
                List<UserPO> userPOS = userMapper.selectByExample(example);
                //msgService.sendMsg(userPOS.get(0).getUserTel(), "自动扫描到即将被注销账号" + employeeUserDTO.getEmployeeAccount());
            }
        } else {
            userMapper.autoLogoutAccount();
        }
    }

    @Override
    public void autoDormantAccount() {
        userMapper.autoDormantAccount();
    }

    @Override
    public boolean checkUserAuth(HttpServletRequest request, JwtUser jwtUser) {
        Assert.isTrue(SM3Utils.verify(jwtUser.getPassword(), jwtUser.getSignaturePwd()), AdminResultEnum.E_90056.code);

        //总管理员无需校验
        if (jwtUser.getIsSuperAdmin() == null) {
            //锁定账号
            Assert.isTrue(jwtUser.getAccountStatus() != EmployeeAccountStatusEnum.LOCK.no, AdminResultEnum.E_90045.code);
            //休眠账号
            Assert.isTrue(jwtUser.getAccountStatus() != EmployeeAccountStatusEnum.DORMANT.no, AdminResultEnum.E_90059.code);
            //注销账号
            Assert.isTrue(jwtUser.getAccountStatus() != EmployeeAccountStatusEnum.LOGOUT.no, AdminResultEnum.E_90054.code);
            //未激活账号
            Assert.isTrue(jwtUser.getAccountStatus() != EmployeeAccountStatusEnum.NOT_ACTIVE.no, AdminResultEnum.E_90055.code);

            //超出访问时间账号
            LocalDateTime startTime = LocalDateTime.now().with(LocalTime.parse(jwtUser.getVisitStartTime(), DateTimeFormatter.ofPattern("HH:mm")));
            LocalDateTime endTime = LocalDateTime.now().with(LocalTime.parse(jwtUser.getVisitEndTime(), DateTimeFormatter.ofPattern("HH:mm")));
            //跨天
            if (startTime.isAfter(endTime) || startTime.isEqual(endTime)) {
                endTime = endTime.plusDays(1);
            }
            Assert.isTrue((LocalDateTime.now().isAfter(startTime) && LocalDateTime.now().isBefore(endTime)), AdminResultEnum.E_90046.code);


            //ip异常操作记录到日志里面去
            if (!(jwtUser.getVisitIp().equals("ALL") || jwtUser.getVisitIp().contains(IpUtil.getIpAddress(request)))) {
                String ip = IpUtil.getIpAddress(request);
                String ua = request.getHeader("User-Agent");
                OperateLogEntity operateLogEntity = new OperateLogEntity();
                operateLogEntity.setLogName("ip异常操作");
                operateLogEntity.setLogType("error");
                operateLogEntity.setLogLevel(0);
                operateLogEntity.setOperationType(19);
                operateLogEntity.setOperationResult(0);
                operateLogEntity.setCreatedDt(LocalDateTime.now());
                operateLogEntity.setApplicationName("tiangong-common-server");
                operateLogEntity.setRequestMethod(request.getMethod());
                operateLogEntity.setRequestIp(ip);
                operateLogEntity.setRequestUrl(request.getRequestURI());
                operateLogEntity.setRequestHeader(ua);
                operateLogEntity.setCreatedBy(jwtUser.getUserAccount());
                operateLogMapper.insert(operateLogEntity);

//                Example example = new Example(UserPO.class);
//                Example.Criteria criteria = example.createCriteria();
//                criteria.andEqualTo("orgCode", jwtUser.getCompanyCode());
//                criteria.andEqualTo("isSuperAdmin", 1);
//                List<UserPO> userPOS = userMapper.selectByExample(example);
                //msgService.sendMsg(userPOS.get(0).getUserTel(), "ip异常操作" + jwtUser.getUserAccount());
            }

            //账号可访问IP
            Assert.isTrue((jwtUser.getVisitIp().equals("ALL") || jwtUser.getVisitIp().contains(IpUtil.getIpAddress(request))), AdminResultEnum.E_90047.code);
        }

        return true;
    }

    @Override
    public void updateLastLoginTime(Integer userId) {
        UserPO userPO = new UserPO();
        userPO.setUserId(userId);
        userPO.setLastLoginTime(DateUtilX.getCurrentDate());
        userMapper.updateByPrimaryKeySelective(userPO);
    }

    @Override
    public List<String> getUserAllApiUrl(List<String> list) {
        list.add("common");
        Example example = new Example(MenuPO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("parentCode", list);
        List<MenuPO> menuPOS = menuMapper.selectByExample(example);
        return menuPOS.stream().map(MenuPO::getPath).collect(Collectors.toList());
    }

    @Override
    public Response<Long> getVerificationCodeTime(String userAccount) {
        long time = 0;
        if (RedisTemplateX.hasKey(StrUtilX.concat(VERIFICATION_CODE, userAccount))) {
            Long getTime = RedisTemplateX.getExpire(StrUtilX.concat(VERIFICATION_CODE, userAccount));
            if (getTime != null && getTime > 240) {
                time = getTime - 240;
            }
        }
        return Response.success(time);
    }

    @Override
    public int examinePhoneOrIdCard(Map<String, String> requestMap) {
        UserPO queryUserPO = new UserPO();
        if (StrUtilX.isNotEmpty(requestMap.get("employeeId"))) {
            queryUserPO.setUserId(Integer.valueOf(requestMap.get("employeeId")));
        }
        queryUserPO.setUserTel(requestMap.get("employeeTel"));
        queryUserPO.setUserIDNumber(requestMap.get("employeeIDNumber"));
        int count = userMapper.queryUserCount(queryUserPO);
        if (count > 0) {
            return 1;
        }
        return 0;
    }

    @Override
    public int examinePwd(Map<String, String> requestMap) {
        Map<String, String> queryMap = new HashMap<>();
        queryMap.put("userId", requestMap.get("employeeId"));
        UserDTO userDetail = queryLoginUser(queryMap);
        String oldEPwdEn = SM4Utils.decrypt(userDetail.getLoginPwd(), Sm4O.defaultKey);
        if (!oldEPwdEn.equals(requestMap.get("employeePwd"))) {
            return 0;
        }
        if (!SM3Utils.verify(userDetail.getLoginPwd(), userDetail.getSignaturePwd())) {
            return -1;
        }
        return 1;
    }

    @Override
    public int modifyEmployeeUserPhone(EmployeeUserDTO employeeUserDTO) throws Exception {

        //查询非管理员的用户，因为管理员不能被修改
        UserPO existPO = userMapper.selectByPrimaryKey(employeeUserDTO.getEmployeeId());

        //existPO.getIsSuperAdmin() != null 前端控制超级用户不可修改，后端不控制
        if (existPO == null) {
            return -1;
        }

        if (existPO.getUserTel().equals(employeeUserDTO.getEmployeeTel())) {
            return -5;
        }
        //一个用户仅可一条正在申请
//        UserApplyPO existApplyPO = new UserApplyPO();
//        existApplyPO.setUserId(employeeUserDTO.getEmployeeId());
//        existApplyPO.setApprovalStatus(0);
//        UserApplyPO existApply = userApplyMapper.selectOne(existApplyPO);
//        if (existApply != null) {
//            return -4;
//        }

        // 若是当前用户的话，则不能修改
        if (existPO.getUserAccount().equals(employeeUserDTO.getModifiedAccount())) {
            return -2;
        }

        //在员工信息表里验重手机号
        ExamineUserDTO userPO1 = new ExamineUserDTO();
        userPO1.setUserTel((String) EncryptUtil.encrypt(employeeUserDTO.getEmployeeTel()));
        //企业编码为SC10049表示员工信息，TF开头表示客户信息
        userPO1.setOrgCode(COMPANY_CODE);
        int i = userMapper.examineUserAccountOrUserTel(userPO1);
        if (i > 0) {
            return -3;
        }

        String tel = "";
        if (StrUtilX.isNotEmpty(employeeUserDTO.getEmployeeTel())) {
            tel = employeeUserDTO.getEmployeeTel();
        }

//        if (StrUtilX.isNotEmpty(employeeUserDTO.getEmployeeTel())){
//            UserPO queryUserPO = new UserPO();
//            queryUserPO.setUserId(employeeUserDTO.getEmployeeId());
//            queryUserPO.setUserTel(tel);
//            int count = userMapper.queryUserCount(queryUserPO);
//            if (count > 0) {
//                return -3;
//            }
//        }

        UserApplyPO userApplyPO = CommonDtoConvert.INSTANCE.UserApplyPOConvert(existPO);
        userApplyPO.setUserId(employeeUserDTO.getEmployeeId());

//        StringBuilder content = new StringBuilder();
        if (StrUtilX.isNotEmpty(employeeUserDTO.getEmployeeTel())) {
            userApplyPO.setUserTel(tel);
            if (!existPO.getUserTel().equals(employeeUserDTO.getEmployeeTel())) {
//                content.append("修改了手机号； ");
                UserPO userPO = new UserPO();
                userPO.setUserId(existPO.getUserId());
                userPO.setUserTel(employeeUserDTO.getEmployeeTel());
                userMapper.updateByPrimaryKeySelective(userPO);
            }
        } else {
            userApplyPO.setUserTel(existPO.getUserTel());
        }

//        if (StrUtilX.isNotEmpty(content.toString())) {
//            content.insert(0, "修改用户" + existPO.getUserName() + ":");
//            userApplyPO.setUserRoles(JSON.toJSONString(employeeUserDTO.getEmployeeRoleList()));
//            userApplyPO.setOperationContent(content.toString());
//            userApplyPO.setApprovalStatus(0);
//            userApplyPO.setOperationType(1);
//            userApplyPO.setCreatedBy(employeeUserDTO.getUpdatedBy());
//            userApplyPO.setCreatedDt(DateUtil.dateToString(new Date(), "yyyy-MM-dd HH:mm:ss"));
//            userApplyMapper.insert(userApplyPO);
//        }
        return 1;
    }

    @Override
    public int modifyEmployeeUserIdCard(EmployeeUserDTO employeeUserDTO) {

        //查询非管理员的用户，因为管理员不能被修改
        UserPO existPO = userMapper.selectByPrimaryKey(employeeUserDTO.getEmployeeId());

        if (existPO == null) {
            return -1;
        }
        if (existPO.getUserIDNumber().equals(employeeUserDTO.getEmployeeIDNumber())) {
            return -5;
        }
        //一个用户仅可一条正在申请
//        UserApplyPO existApplyPO = new UserApplyPO();
//        existApplyPO.setUserId(employeeUserDTO.getEmployeeId());
//        existApplyPO.setApprovalStatus(0);
//        UserApplyPO existApply = userApplyMapper.selectOne(existApplyPO);
//        if (existApply != null) {
//            return -4;
//        }

        // 若是当前用户的话，则不能修改
        if (existPO.getUserAccount().equals(employeeUserDTO.getModifiedAccount())) {
            return -2;
        }

        String idNumber = "";
        if (StrUtilX.isNotEmpty(employeeUserDTO.getEmployeeIDNumber())) {
            idNumber = employeeUserDTO.getEmployeeIDNumber();
        }

//        if (StrUtilX.isNotEmpty(employeeUserDTO.getEmployeeIDNumber())){
//            UserPO queryUserPO = new UserPO();
//            queryUserPO.setUserId(employeeUserDTO.getEmployeeId());
//            queryUserPO.setUserIDNumber(idNumber);
//            int count = userMapper.queryUserCount(queryUserPO);
//            if (count > 0) {
//                return -3;
//            }
//        }

        UserApplyPO userApplyPO = CommonDtoConvert.INSTANCE.UserApplyPOConvert(existPO);
        userApplyPO.setUserId(employeeUserDTO.getEmployeeId());

//        StringBuilder content = new StringBuilder();
        if (StrUtilX.isNotEmpty(employeeUserDTO.getEmployeeIDNumber())) {
            userApplyPO.setUserIDNumber(idNumber);
            if (!existPO.getUserIDNumber().equals(employeeUserDTO.getEmployeeIDNumber())) {
//                content.append("修改了身份证；");
                UserPO userPO = new UserPO();
                userPO.setUserId(existPO.getUserId());
                userPO.setUserIDNumber(employeeUserDTO.getEmployeeIDNumber());
                userMapper.updateByPrimaryKeySelective(userPO);
            }
        } else {
            userApplyPO.setUserIDNumber(existPO.getUserIDNumber());
        }

//        if (StrUtilX.isNotEmpty(content.toString())) {
//            content.insert(0, "修改用户" + existPO.getUserName() + ":");
//            userApplyPO.setUserRoles(JSON.toJSONString(employeeUserDTO.getEmployeeRoleList()));
//            userApplyPO.setOperationContent(content.toString());
//            userApplyPO.setApprovalStatus(0);
//            userApplyPO.setOperationType(1);
//            userApplyPO.setCreatedBy(employeeUserDTO.getUpdatedBy());
//            userApplyPO.setCreatedDt(DateUtil.dateToString(new Date(), "yyyy-MM-dd HH:mm:ss"));
//            userApplyMapper.insert(userApplyPO);
//        }
        return 1;
    }

    @Override
    public Response<String> verifyPasswordQueryInfo(EmployeeUserDTO employeeUserDTO, Integer infoType) {
        Assert.notNull(employeeUserDTO.getPassword(), "密码不能为空！");

        // 查询用户并验证密码
        UserPO user = userMapper.selectByPrimaryKey(employeeUserDTO.getUserId());
        String encryptUserPassword = SM4Utils.encrypt(employeeUserDTO.getPassword(), Sm4O.defaultKey);
        if (!encryptUserPassword.equals(user.getUserPwd())) {
            return Response.error(ErrorCodeEnum.PASSWORD_IS_ERROR.errorCode, ErrorCodeEnum.PASSWORD_IS_ERROR.errorDesc);
        }
        UserPO user2 = userMapper.selectByPrimaryKey(employeeUserDTO.getEmployeeId());

        String tel = null;
        if (infoType.equals(1)) {
            tel = user2.getUserTel();
        } else {
            tel = user2.getUserIDNumber();
        }
        return Response.success(tel);
    }

    @Override
    public String queryOrgCodeByAccount(String userAccount) {
        return userMapper.queryOrgCodeByAccount(userAccount);
    }

    @Override
    public int queryUserAccountAndUserTel(String userAccount, String userTel, String agentCode) {
        return userMapper.queryUserAccountAndUserTel(userAccount, userTel, agentCode);
    }

    @Override
    public List<String> getRoleIdByUserId(Integer userId) {
        //查询角色对应菜单
        UserRolePO userRolePO = new UserRolePO();
        userRolePO.setUserId(userId);
        List<UserRolePO> roleMenuPOList = userRoleMapper.select(userRolePO);
        List<String> roleMenuList = null;
        if (null != roleMenuPOList) {
            roleMenuList = new ArrayList<>();
            for (UserRolePO rolePO : roleMenuPOList) {
                roleMenuList.add(rolePO.getRoleId().toString());
            }
        }
        return roleMenuList;
    }

}
