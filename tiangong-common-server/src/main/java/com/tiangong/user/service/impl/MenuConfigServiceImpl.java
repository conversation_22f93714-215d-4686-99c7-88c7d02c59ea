package com.tiangong.user.service.impl;

import com.tiangong.organization.domain.MenuConfigPO;
import com.tiangong.user.domain.MenuConfig;
import com.tiangong.user.mapper.MenuConfigMapper;
import com.tiangong.user.service.MenuConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class MenuConfigServiceImpl implements MenuConfigService {

    @Autowired
    private MenuConfigMapper menuConfigMapper;

    @Override
    public String queryNote(String path) {
        MenuConfigPO config = new MenuConfigPO();
        config.setPath(path);
        MenuConfigPO menuConfigPO = menuConfigMapper.selectOne(config);
        if (menuConfigPO == null) {
            return null;
        }
        return menuConfigPO.getContent();
    }

    @Override
    public void editNote(MenuConfig menuConfig) {
        MenuConfigPO config = new MenuConfigPO();
        config.setPath(menuConfig.getPath());
        int num = menuConfigMapper.selectCount(config);

        //存在就更新，否则就新增
        MenuConfigPO menuConfigPO = new MenuConfigPO();
        menuConfigPO.setContent(menuConfig.getContent());
        menuConfigPO.setPath(menuConfig.getPath());
        if (num > 0) {
            menuConfigMapper.updateByPrimaryKey(menuConfigPO);
        } else {
            menuConfigMapper.insert(menuConfigPO);
        }
    }
}
