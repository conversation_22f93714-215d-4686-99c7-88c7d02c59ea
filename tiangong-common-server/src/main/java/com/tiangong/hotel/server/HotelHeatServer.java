package com.tiangong.hotel.server;

import com.alibaba.nacos.shaded.com.google.common.util.concurrent.RateLimiter;
import com.tiangong.cloud.common.anno.AnonymousAccess;
import com.tiangong.cloud.commonbean.common.BaseController;
import com.tiangong.common.Response;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.hotel.mapper.HotelHeatMapper;
import com.tiangong.hotel.req.CityAvgPriceReq;
import com.tiangong.hotel.req.HotelAvgPriceReq;
import com.tiangong.hotel.req.HotelHeatReq;
import com.tiangong.hotel.resp.HotelAvgPriceResp;
import com.tiangong.hotel.resp.HotelHeatResp;
import com.tiangong.hotel.service.CityAvgPriceService;
import com.tiangong.hotel.service.HotelAvgPriceService;
import com.tiangong.hotel.service.HotelHeatService;
import com.tiangong.keys.RedisKey;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.util.CollUtilX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

@RestController
@RequestMapping(value = "/common/hotelHeat")
@Slf4j
public class HotelHeatServer extends BaseController {

    @Autowired
    private HotelHeatService hotelHeatService;

    @Autowired
    private HotelAvgPriceService hotelAvgPriceService;

    @Autowired
    private CityAvgPriceService cityAvgPriceService;

    @Autowired
    private HotelHeatMapper hotelHeatMapper;

    /**
     * 查询酒店热度列表（分页）
     */
    @PostMapping(value = "/queryHotelHeatPage", produces = {"application/json;charset=UTF-8"})
    public Response<PaginationSupportDTO<HotelHeatResp>> queryHotelHeatPage(@RequestBody HotelHeatReq req) {
        req.setLanguage(getLanguage());
        return Response.success(hotelHeatService.queryHotelHeatPage(req));
    }

    /**
     * 查询酒店热度表中所有酒店id列表（分页）
     */
    @PostMapping(value = "/queryAllHotelHeatHotelIdPage", produces = {"application/json;charset=UTF-8"})
    @AnonymousAccess
    public Response<List<String>> queryAllHotelHeatHotelIdPage(@RequestBody HotelHeatReq req) {
        return Response.success(hotelHeatService.queryAllHotelHeatHotelIdPage(req));
    }

    /**
     * 修改热度分数
     */
    @PostMapping(value = "/updateHotelHeatScore", produces = {"application/json;charset=UTF-8"})
    @AnonymousAccess
    public Response<Object> updateHotelHeatScore(@RequestBody List<HotelHeatReq> reqList) {
        hotelHeatService.updateHotelHeatScore(reqList);
        return Response.success();
    }

    /**
     * 批量保存酒店平均价
     */
    @PostMapping(value = "/batchSaveHotelAvgPrice", produces = {"application/json;charset=UTF-8"})
    @AnonymousAccess
    public Response<Object> batchSaveHotelAvgPrice(@RequestBody List<HotelAvgPriceReq> reqList) {
        hotelAvgPriceService.batchSaveHotelAvgPrice(reqList);
        return Response.success();
    }

    /**
     * 删除酒店平均价
     */
    @PostMapping(value = "/delHotelAvgPrice", produces = {"application/json;charset=UTF-8"})
    @AnonymousAccess
    public Response<Object> delHotelAvgPrice(@RequestBody HotelAvgPriceReq req) {
        hotelAvgPriceService.delHotelAvgPrice(req);
        return Response.success();
    }

    /**
     * 批量保存城市平均价
     */
    @PostMapping(value = "/batchSaveCityAvgPrice", produces = {"application/json;charset=UTF-8"})
    @AnonymousAccess
    public Response<Object> batchSaveCityAvgPrice(@RequestBody List<CityAvgPriceReq> reqList) {
        cityAvgPriceService.batchSaveCityAvgPrice(reqList);
        return Response.success();
    }

    /**
     * 查询酒店平均价城市编码列表
     */
    @PostMapping(value = "/queryHotelAvgPriceCityCodes", produces = {"application/json;charset=UTF-8"})
    @AnonymousAccess
    public Response<List<String>> queryHotelAvgPriceCityCodes() {
        return Response.success(hotelAvgPriceService.queryHotelAvgPriceCityCodes());
    }

    /**
     * 查询酒店平均价列表
     */
    @PostMapping(value = "/queryHotelAvgPriceList", produces = {"application/json;charset=UTF-8"})
    @AnonymousAccess
    public Response<List<HotelAvgPriceResp>> queryHotelAvgPriceList(@RequestBody HotelAvgPriceReq req) {
        return Response.success(hotelAvgPriceService.queryHotelAvgPriceList(req));
    }

    /**
     * 初始化需要计算酒店热度评分和集团分数的酒店id到缓存任务
     */
    @PostMapping("initCalculateGroupScoreHotelIdToRedisTask")
    @AnonymousAccess
    public Response<Object> initCalculateGroupScoreHotelIdToRedisTask() {
        ExecutorService executor = null;
        try {
            // 配置参数
            final int pageSize = 2000;
            final int maxConcurrency = 8;
            final AtomicLong lastId = new AtomicLong(0);
            final RateLimiter rateLimiter = RateLimiter.create(5);
            executor = Executors.newFixedThreadPool(maxConcurrency);

            // 分页循环
            while (true) {
                rateLimiter.acquire();

                // 分页查询
                HotelHeatReq req = new HotelHeatReq();
                req.setLastId(lastId.get());
                req.setBatchSize(pageSize);

                List<String> hotelIds = hotelHeatMapper.selectAllHotelHeatHotelIdPage(req);

                if (CollUtilX.isEmpty(hotelIds)) break;

                // 提交处理
                executor.submit(() -> processWithRetry(hotelIds));

                // 更新游标
                updateLastIdAtomically(hotelIds, lastId);
            }

            // 等待完成
            executor.shutdown();
            while (!executor.awaitTermination(1, TimeUnit.MINUTES)) {
                log.info("等待线程池关闭...");
            }
        } catch (Exception e) {
            log.error("执行初始化需要计算酒店热度评分和集团分数的酒店id到缓存任务异常", e);
        } finally {
            if (executor != null) executor.shutdownNow();
        }
        return Response.success();
    }

    /**
     * 更新lastId
     */
    private void updateLastIdAtomically(List<String> hotelIds, AtomicLong lastId) {
        hotelIds.stream()
                .mapToLong(Long::parseLong)
                .max()
                .ifPresent(max -> {
                    long current;
                    do {
                        current = lastId.get();
                    } while (max > current && !lastId.compareAndSet(current, max));
                });
    }

    /**
     * 处理数据
     */
    private void processWithRetry(List<String> hotelIds) {
        try (RedisConnection conn = RedisTemplateX.getConnectionFactory()) {
            conn.openPipeline();
            byte[] key = RedisKey.CALCULATE_GROUP_SCORE_HOTEL_ID_KEY.getBytes();

            hotelIds.stream()
                    .map(String::getBytes)
                    .forEach(bytes -> conn.sAdd(key, bytes));

            conn.closePipeline();
        } catch (Exception e) {
            log.error("Redis批量写入失败", e);
        }
    }

    /**
     * 计算酒店热度城市平均房价任务
     */
    @PostMapping("calculateHotelHeatCityAvgPriceTask")
    @AnonymousAccess
    public Response<Object> calculateHotelHeatCityAvgPriceTask(@RequestBody Map<String, String> paramMap) {
        hotelAvgPriceService.calculateHotelHeatCityAvgPriceTask(paramMap.get("cityCodes"));
        return Response.success();
    }

    /**
     * 计算酒店热度酒店平均房价分数(有平均价)任务
     */
    @PostMapping("calculateHotelHeatHotelAvgPriceScoreHavePriceTask")
    @AnonymousAccess
    public Response<Object> calculateHotelHeatHotelAvgPriceScoreHavePriceTask(@RequestBody Map<String, String> paramMap) {
        cityAvgPriceService.calculateHotelHeatHotelAvgPriceScoreHavePriceTask(paramMap.get("cityCodes"));
        return Response.success();
    }

    /**
     * 计算酒店热度酒店平均房价分数(没有平均价)任务
     */
    @PostMapping("calculateHotelHeatHotelAvgPriceScoreNotPriceTask")
    @AnonymousAccess
    public Response<Object> calculateHotelHeatHotelAvgPriceScoreNotPriceTask(@RequestBody Map<String, String> paramMap) {
        cityAvgPriceService.calculateHotelHeatHotelAvgPriceScoreNotPriceTask(paramMap.get("hotelIds"));
        return Response.success();
    }

    /**
     * 初始化需要计算酒店热度城市平均房价分数的酒店id到缓存任务
     */
    @PostMapping("initCalculateCityAvgPriceScoreHotelIdToRedisTask")
    @AnonymousAccess
    public Response<Object> initCalculateCityAvgPriceScoreHotelIdToRedisTask(@RequestBody Map<String, String> paramMap) {
        hotelAvgPriceService.initCalculateCityAvgPriceScoreHotelIdToRedisTask(paramMap.get("hotelIds"));
        return Response.success();
    }
}
