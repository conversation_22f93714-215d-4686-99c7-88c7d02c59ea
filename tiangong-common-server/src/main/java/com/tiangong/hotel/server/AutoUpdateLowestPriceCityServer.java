package com.tiangong.hotel.server;

import com.tiangong.cloud.commonbean.common.BaseController;
import com.tiangong.common.Response;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.enums.LanguageTypeEnum;
import com.tiangong.hotel.domain.resp.AutoUpdateLowestPriceCityResp;
import com.tiangong.hotel.domain.vo.AutoUpdateLowestPriceCityVo;
import com.tiangong.hotel.service.AutoUpdateLowestPriceCityService;
import lombok.extern.slf4j.Slf4j;
import org.apache.tomcat.util.http.fileupload.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.Map;

/**
 * 主动更新起价城市
 */
@Slf4j
@RestController
@RequestMapping("/common/autoUpdateLowestPriceCity")
public class AutoUpdateLowestPriceCityServer extends BaseController {

    @Autowired
    private AutoUpdateLowestPriceCityService autoUpdateLowestPriceCityService;

    /**
     * 新增主动更新起价城市
     */
    @PostMapping("/autoUpdateLowestPriceCityAdd")
    public Response<Object> saveLowestPriceCity(@RequestBody AutoUpdateLowestPriceCityVo vo) {
        vo.setOperator(getUserName());
        vo.setLanguage(getLanguage());
        autoUpdateLowestPriceCityService.autoUpdateLowestPriceCityAdd(vo);
        return Response.success();
    }

    /**
     * 更新主动更新起价城市
     */
    @RequestMapping("/autoUpdateLowestPriceCityEdit")
    public Response<Object> autoUpdateLowestPriceCityEdit(@RequestBody AutoUpdateLowestPriceCityVo vo) {
        vo.setOperator(getUserName());
        autoUpdateLowestPriceCityService.autoUpdateLowestPriceCityEdit(vo);
        return Response.success();
    }

    /**
     * 删除主动更新起价城市
     */
    @RequestMapping("/autoUpdateLowestPriceCityDel")
    public Response<Object> autoUpdateLowestPriceCityDel(@RequestBody AutoUpdateLowestPriceCityVo vo) {
        autoUpdateLowestPriceCityService.autoUpdateLowestPriceCityDel(vo);
        return Response.success();
    }

    /**
     * 查询主动更新起价城市列表（分页）
     */
    @PostMapping("/autoUpdateLowestPriceCityPage")
    public Response<PaginationSupportDTO<AutoUpdateLowestPriceCityResp>> autoUpdateLowestPriceCityPage(@RequestBody AutoUpdateLowestPriceCityVo vo) {
        return Response.success(autoUpdateLowestPriceCityService.autoUpdateLowestPriceCityPage(vo));
    }

    /**
     * 导入主动更新起价城市
     */
    @PostMapping("/importAutoUpdateLowestPriceCity")
    public Response<Object> importAutoUpdateLowestPriceCity(MultipartFile file) {
        return autoUpdateLowestPriceCityService.importAutoUpdateLowestPriceCity(file, getLanguage(), getUserName());
    }

    /**
     * 下载导入主动更新起价城市模版
     */
    @PostMapping("/downLoadFile")
    public File downLoadFile(HttpServletResponse response) {
        InputStream in = null;
        try {
            String filename;
            if (LanguageTypeEnum.zh_CN.getValue().equals(getLanguage())) {
                filename = "主动更新起价范围配置导入模板";
            } else {
                filename = "AutoUpdateLowestPriceCityImportTemplate";
            }
            in = this.getClass().getClassLoader().getResourceAsStream("template/autoUpdateLowestPriceCityImportTemplate.xlsx");
            response.setHeader("Content-disposition", "attachment;filename=" + filename + ".xlsx");
            if (in != null) {
                IOUtils.copy(in, response.getOutputStream());
            }
            response.flushBuffer();
        } catch (Exception e) {
            log.error("下载主动更新起价范围配置导入模板异常", e);
        } finally {
            if (in != null) {
                try {
                    in.close();
                } catch (IOException e) {
                    log.error("下载主动更新起价范围配置导入模板异常", e);
                }
            }
        }
        return null;
    }

    /**
     * 初始化主动更新起价城市下酒店id到缓存任务
     */
    @RequestMapping("/initCalculateCityAvgPriceScoreHotelIdToRedisTask")
    public Response<Object> autoUpdateLowestPriceCityDel(@RequestBody Map<String, String> paramMap) {
        try {
            autoUpdateLowestPriceCityService.initCalculateCityAvgPriceScoreHotelIdToRedisTask(paramMap.get("hotelId"));
        } catch (Exception e) {
            log.error("初始化主动更新起价城市下酒店id到缓存任务异常", e);
        }
        return Response.success();
    }
}
