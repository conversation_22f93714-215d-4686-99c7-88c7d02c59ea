package com.tiangong.hotel.server;

import com.tiangong.annotations.SlsLog;
import com.tiangong.cloud.common.anno.AnonymousAccess;
import com.tiangong.cloud.commonbean.common.BaseController;
import com.tiangong.common.Response;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.hotel.req.HotelLabelConfigReq;
import com.tiangong.hotel.resp.HotelLabelConfigLogResp;
import com.tiangong.hotel.resp.HotelLabelConfigResp;
import com.tiangong.hotel.service.HotelLabelConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 酒店标签配置
 */
@RestController
@RequestMapping("/common/hotelLabelConfig")
public class HotelLabelConfigServer extends BaseController {

    @Autowired
    private HotelLabelConfigService hotelLabelConfigService;

    /**
     * 新增酒店标签配置
     */
    @PostMapping("/hotelLabelConfigAdd")
    @PreAuthorize("@syyo.check('hotel:hotelLabelConfig')")
    public Response<Object> hotelLabelConfigAdd(@RequestBody HotelLabelConfigReq req) {
        req.setCreatedBy(super.getUserName());
        hotelLabelConfigService.hotelLabelConfigAdd(req);
        return Response.success();
    }

    /**
     * 酒店标签配置列表（分页）
     */
    @PostMapping("/hotelLabelConfigPage")
    @PreAuthorize("@syyo.check('hotel:hotelLabelConfig')")
    public Response<PaginationSupportDTO<HotelLabelConfigResp>> hotelLabelConfigPage(@RequestBody HotelLabelConfigReq req) {
        return Response.success(hotelLabelConfigService.hotelLabelConfigPage(req));
    }

    /**
     * 酒店标签配置列表
     */
    @PostMapping("/hotelLabelConfigList")
    @AnonymousAccess
    public Response<List<HotelLabelConfigResp>> hotelLabelConfigList(@RequestBody HotelLabelConfigReq req) {
        return Response.success(hotelLabelConfigService.hotelLabelConfigList(req));
    }

    /**
     * 酒店标签配置编辑
     */
    @PostMapping("/hotelLabelConfigEdit")
    @PreAuthorize("@syyo.check('hotel:hotelLabelConfig')")
    public Response<Object> hotelLabelConfigEdit(@RequestBody HotelLabelConfigReq req) {
        req.setUpdatedBy(super.getUserName());
        hotelLabelConfigService.hotelLabelConfigEdit(req);
        return Response.success();
    }

    /**
     * 删除酒店标签配置
     */
    @PostMapping("/hotelLabelConfigDel")
    @PreAuthorize("@syyo.check('hotel:hotelLabelConfig')")
    public Response<Object> hotelLabelConfigDel(@RequestBody HotelLabelConfigReq req) {
        req.setUpdatedBy(super.getUserName());
        hotelLabelConfigService.hotelLabelConfigDel(req);
        return Response.success();
    }

    /**
     * 校验酒店标签配置
     */
    @PostMapping("/checkHotelLabelConfig")
    @PreAuthorize("@syyo.check('hotel:hotelLabelConfig')")
    public Response<Object> checkHotelLabelConfig(@RequestBody HotelLabelConfigReq req) {
        return Response.success(hotelLabelConfigService.checkHotelLabelConfig(req));
    }

    /**
     * 酒店标签配置日志列表（分页）
     */
    @PostMapping("/hotelLabelConfigLogPage")
    @PreAuthorize("@syyo.check('hotel:hotelLabelConfig')")
    public Response<PaginationSupportDTO<HotelLabelConfigLogResp>> hotelLabelConfigLogPage(@RequestBody HotelLabelConfigReq req) {
        return Response.success(hotelLabelConfigService.hotelLabelConfigLogPage(req));
    }
}
