package com.tiangong.hotel.server;

import com.tiangong.cloud.common.anno.AnonymousAccess;
import com.tiangong.cloud.commonbean.common.BaseController;
import com.tiangong.common.Response;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.dto.hotel.HotelPopularCityDTO;
import com.tiangong.dto.hotel.HotelPopularCityVO;
import com.tiangong.hotel.domain.req.HotelPopularCityReq;
import com.tiangong.hotel.domain.resp.HotelPopularCityResp;
import com.tiangong.hotel.service.HotelPopularCityService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 酒店热门城市
 */
@Slf4j
@RestController
@RequestMapping("/common/hotelPopularCity")
public class HotelPopularCityServer extends BaseController {

    @Autowired
    private HotelPopularCityService hotelPopularCityService;

    /**
     * 热门城市新增
     */
    @PostMapping("/hotelPopularCityAdd")
    public Response<Object> hotelPopularCityAdd(@RequestBody HotelPopularCityReq req) {
        req.setOperator(super.getUserName());
        hotelPopularCityService.hotelPopularCityAdd(req);
        return Response.success();
    }

    /**
     * 热门城市删除
     */
    @PostMapping("/hotelPopularCityDel")
    public Response<Object> hotelPopularCityDel(@RequestBody HotelPopularCityReq req) {
        hotelPopularCityService.hotelPopularCityDel(req);
        return Response.success();
    }

    /**
     * 热门城市列表
     */
    @PostMapping("/hotelPopularCityList")
    public Response<List<HotelPopularCityResp>> hotelPopularCityList(@RequestBody HotelPopularCityReq req) {
        req.setLanguage(getLanguage());
        return Response.success(hotelPopularCityService.hotelPopularCityList(req));
    }

    /**
     * 热门城市列表（分页）
     */
    @PostMapping("/hotelPopularCityPage")
    public Response<PaginationSupportDTO<HotelPopularCityResp>> hotelPopularCityPage(@RequestBody HotelPopularCityReq req) {
        req.setLanguage(getLanguage());
        return Response.success(hotelPopularCityService.hotelPopularCityPage(req));
    }

    /**
     * 热门城市列表
     */
    @PostMapping("/queryHotelPopularCityList")
    @AnonymousAccess
    public Response<List<HotelPopularCityDTO>> queryHotelPopularCityList(@RequestBody HotelPopularCityVO vo) {
        return Response.success(hotelPopularCityService.queryHotelPopularCityList(vo));
    }
}
