package com.tiangong.hotel.server;

import com.tiangong.cloud.commonbean.common.BaseController;
import com.tiangong.common.Response;
import com.tiangong.hotel.domain.AutoUpdateLowestPriceConfigEntity;
import com.tiangong.hotel.domain.vo.AutoUpdateLowestPriceConfigVo;
import com.tiangong.hotel.service.AutoUpdateLowestPriceConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 主动更新起价配置
 */
@RestController
@RequestMapping("/common/autoUpdateLowestPriceConfig")
public class AutoUpdateLowestPriceConfigServer extends BaseController {

    @Autowired
    private AutoUpdateLowestPriceConfigService autoUpdateLowestPriceConfigService;

    /**
     * 保存主动更新起价配置
     */
    @PostMapping("/saveAutoUpdateLowestPriceConfig")
    public Response<Object> saveAutoUpdateLowestPriceConfig(@RequestBody AutoUpdateLowestPriceConfigVo vo) {
        vo.setCreatedBy(getUserName());
        vo.setUpdatedBy(getUserName());
        autoUpdateLowestPriceConfigService.saveAutoUpdateLowestPriceConfig(vo);
        return Response.success();
    }

    /**
     * 查询主动更新起价配置
     */
    @PostMapping("/queryAutoUpdateLowestPriceConfig")
    public Response<AutoUpdateLowestPriceConfigEntity> queryAutoUpdateLowestPriceConfig() {
        return Response.success(autoUpdateLowestPriceConfigService.queryAutoUpdateLowestPriceConfig());
    }
}
