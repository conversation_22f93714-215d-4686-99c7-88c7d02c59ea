package com.tiangong.hotel.server;

import com.tiangong.cloud.common.anno.AnonymousAccess;
import com.tiangong.common.Response;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.dto.hotel.AddHotelAvailableReq;
import com.tiangong.dto.hotel.HotelAvailableDTO;
import com.tiangong.hotel.domain.req.DelHotelAvailableReq;
import com.tiangong.hotel.domain.req.QueryHotelAvailableReq;
import com.tiangong.hotel.domain.resp.HotelAvailableListResp;
import com.tiangong.hotel.service.HotelAvailableService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @create 2024/1/3 11:53
 */
@RestController
@RequestMapping("common/hotelAvailable")
public class HotelAvailableServer {

    @Autowired
    private HotelAvailableService hotelAvailableService;

    /**
     * 新增酒店可见性
     */
    @PostMapping("addHotelAvailable")
    @PreAuthorize("@syyo.check('common')")
    public Response<Object> addHotelAvailable(@Validated @RequestBody AddHotelAvailableReq req){
        hotelAvailableService.addHotelAvailable(req);
        return Response.success();
    }

    /**
     * 删除酒店可见性
     */
    @PostMapping("delHotelAvailable")
    @PreAuthorize("@syyo.check('common')")
    public Response<Object> delHotelAvailable(@Validated @RequestBody DelHotelAvailableReq req){
        hotelAvailableService.delHotelAvailable(req);
        return Response.success();
    }

    /**
     * 查询酒店可见性列表
     */
    @PostMapping("queryHotelListAvailable")
    @PreAuthorize("@syyo.check('common')")
    public Response<PaginationSupportDTO<HotelAvailableListResp>> queryHotelListAvailable(@Validated @RequestBody QueryHotelAvailableReq req){
        return Response.success(hotelAvailableService.queryHotelListAvailable(req));
    }

    /**
     * 查询酒店是否是黑名单
     */
    @PostMapping("queryHotelAvailable")
    @AnonymousAccess
    public Response<HotelAvailableDTO> queryHotelAvailable(@Validated @RequestBody AddHotelAvailableReq req){
        return Response.success(hotelAvailableService.queryHotelAvailable(req));
    }
}
