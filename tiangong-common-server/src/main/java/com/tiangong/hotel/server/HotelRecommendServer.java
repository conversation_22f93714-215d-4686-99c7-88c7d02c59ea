package com.tiangong.hotel.server;


import com.tiangong.cloud.commonbean.common.BaseController;
import com.tiangong.common.Response;
import com.tiangong.dis.dto.HotelInfoReq;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.enums.LanguageTypeEnum;
import com.tiangong.hotel.req.HotelRecommendReq;
import com.tiangong.hotel.resp.HotelRecommendResp;
import com.tiangong.hotel.service.HotelRecommendService;
import org.apache.tomcat.util.http.fileupload.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;

/**
 * 酒店推荐排序
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-10-21 11:19:43
 */
@Slf4j
@RestController
@RequestMapping("/common/hotelRecommend")
public class HotelRecommendServer extends BaseController {

    @Autowired
    private HotelRecommendService hotelRecommendService;

    /**
     * 酒店推荐排序新增
     */
    @PostMapping("/hotelRecommendAdd")
    @PreAuthorize("@syyo.check('hotel:hotelRecommend')")
    public Response<Object> add(@RequestBody HotelRecommendReq req) {
        req.setCreatedBy(super.getUserName());
        req.setUpdatedBy(super.getUserName());
        hotelRecommendService.hotelRecommendAdd(req);
        return Response.success();
    }

    /**
     * 酒店推荐排序删除
     */
    @PostMapping("/hotelRecommendDel")
    @PreAuthorize("@syyo.check('hotel:hotelRecommend')")
    public Response<Integer> del(@RequestBody HotelRecommendReq req) {
        req.setUpdatedBy(super.getUserName());
        return Response.success(hotelRecommendService.hotelRecommendDel(req));
    }

    /**
     * 酒店推荐排序编辑
     */
    @PostMapping("/hotelRecommendEdit")
    @PreAuthorize("@syyo.check('hotel:hotelRecommend')")
    public Response<Integer> edit(@RequestBody HotelRecommendReq req) {
        req.setUpdatedBy(super.getUserName());
        return Response.success(hotelRecommendService.hotelRecommendEdit(req));
    }

    /**
     * 酒店推荐排序列表（分页）
     * 前端使用
     */
    @PostMapping("/hotelRecommendList")
    public Response<PaginationSupportDTO<HotelRecommendResp>> findPage(@RequestBody HotelRecommendReq req) {
        req.setLanguage(getLanguage());
        return Response.success(hotelRecommendService.hotelRecommendPage(req));
    }

    /**
     * 获取酒店标签信息
     */
    @PostMapping("/queryHotelLabelInfo")
    public Response<Set<String>> queryHotelLabelInfo(@RequestBody HotelInfoReq req) {
        return Response.success(hotelRecommendService.queryHotelLabelInfo(req));
    }

    /**
     * 导入酒店排序
     */
    @PostMapping("/importHotelRecommend")
    public Response<Object> importHotelRecommend(MultipartFile file){
        return hotelRecommendService.importHotelRecommend(file, super.getUserName());
    }

    /**
     * 下载酒店排序导入模板
     */
    @PostMapping("/downRecommendScoreTemplateFile")
    public File downSupplierLabelTemplateFile(HttpServletResponse response) {
        InputStream in = null;
        try {
            String filename;
            if (LanguageTypeEnum.zh_CN.getValue().equals(getLanguage())) {
                filename = "酒店排序导入模板";
            } else {
                filename = "hotelRecommendScoreTemplate";
            }
            in = this.getClass().getClassLoader().getResourceAsStream("template/hotelRecommendScoreTemplate.xlsx");
            response.setHeader("Content-disposition", "attachment;filename=" + filename + ".xlsx");
            IOUtils.copy(in, response.getOutputStream());
            response.flushBuffer();
        } catch (Exception e) {
            log.error("下载酒店排序导入模板异常", e);
        } finally {
            if (in != null) {
                try {
                    in.close();
                } catch (IOException e) {
                    log.error("下载酒店排序导入模板异常");
                }
            }
        }
        return null;
    }
}
