package com.tiangong.hotel.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import javax.persistence.Column;

/**
 * <AUTHOR>
 * @create 2023/11/14 13:53
 */
@Data
@TableName("t_baseinfo_region")
public class BaseinfoRegionEntity {

    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 国际编号
     */
    private String regionCode;

    /**
     * 国际区号
     */
    private String countryCode;

    /**
     * 国际英文名
     */
    private String enName;

    /**
     * 国际中文名
     */
    @Column(name = "country_name")
    private String countryName;

    private String keyword;
}
