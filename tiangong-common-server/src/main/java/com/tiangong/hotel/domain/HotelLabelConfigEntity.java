package com.tiangong.hotel.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 酒店标签配置表
 */
@Data
@TableName("t_hotel_label_config")
public class HotelLabelConfigEntity {
    /**
     * 自增id
     */
    @TableId(value = "hotel_label_config_id", type = IdType.AUTO)
    private Integer hotelLabelConfigId;

    /**
     * 标签编码
     */
    private String labelCode;

    /**
     * 标签名称
     */
    private String labelName;

    /**
     * 标签推荐分值
     */
    private Long recommendScore;

    /**
     * 删除状态
     */
    private Integer deleted;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdDt;

    /**
     * 修改人
     */
    private String updatedBy;

    /**
     * 修改时间
     */
    private Date updatedDt;
}
