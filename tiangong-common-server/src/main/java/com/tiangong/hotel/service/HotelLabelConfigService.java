package com.tiangong.hotel.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.hotel.domain.HotelLabelConfigEntity;
import com.tiangong.hotel.req.HotelLabelConfigReq;
import com.tiangong.hotel.resp.HotelLabelConfigLogResp;
import com.tiangong.hotel.resp.HotelLabelConfigResp;

import java.util.List;

/**
 * 酒店标签配置
 */
public interface HotelLabelConfigService extends IService<HotelLabelConfigEntity>  {

    /**
     * 新增酒店标签配置
     */
    void hotelLabelConfigAdd(HotelLabelConfigReq req);

    /**
     * 酒店标签配置列表（分页）
     */
    PaginationSupportDTO<HotelLabelConfigResp> hotelLabelConfigPage(HotelLabelConfigReq req);

    /**
     * 酒店标签配置列表
     */
    List<HotelLabelConfigResp> hotelLabelConfigList(HotelLabelConfigReq req);

    /**
     * 编辑酒店标签配置
     */
    void hotelLabelConfigEdit(HotelLabelConfigReq req);

    /**
     * 删除酒店标签配置
     */
    void hotelLabelConfigDel(HotelLabelConfigReq req);

    /**
     * 校验酒店标签配置
     */
    int checkHotelLabelConfig(HotelLabelConfigReq req);

    /**
     * 酒店标签配置日志列表（分页）
     */
    PaginationSupportDTO<HotelLabelConfigLogResp> hotelLabelConfigLogPage(HotelLabelConfigReq req);

    /**
     * 查询全部的标签
     * @return
     */
    List<HotelLabelConfigEntity> selectAllHotelLabel();
}
