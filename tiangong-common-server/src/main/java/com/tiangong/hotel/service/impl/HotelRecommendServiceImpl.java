package com.tiangong.hotel.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tiangong.cloud.common.enums.result.ResultEnum;
import com.tiangong.common.Response;
import com.tiangong.convert.CommonDtoConvert;
import com.tiangong.dis.dto.HotelInfoReq;
import com.tiangong.dto.common.HotelAndAgentCodeLabelDTO;
import com.tiangong.dto.common.HotelPublicDTO;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.dto.hotel.*;
import com.tiangong.enums.ErrorCodeEnum;
import com.tiangong.enums.LanguageTypeEnum;
import com.tiangong.exception.SysException;
import com.tiangong.fuzzyquery.service.impl.FuzzyQueryServiceImpl;
import com.tiangong.hotel.domain.HotelLabelConfigEntity;
import com.tiangong.hotel.domain.HotelRecommendEntity;
import com.tiangong.hotel.domain.HotelRecommendLabelRelEntity;
import com.tiangong.hotel.domain.collection.HotelLanguageCollection;
import com.tiangong.hotel.domain.enums.ExcelTitleEnum;
import com.tiangong.hotel.domain.req.ExportHotelRecommendReq;
import com.tiangong.hotel.domain.req.ImportHotelRecommendReq;
import com.tiangong.hotel.domain.resp.ImportHotelRecommendResp;
import com.tiangong.hotel.domain.vo.ImportHotelRecommendReqDTO;
import com.tiangong.hotel.mapper.HotelRecommendMapper;
import com.tiangong.hotel.req.HotelRecommendReq;
import com.tiangong.hotel.resp.HotelLabelConfigResp;
import com.tiangong.hotel.resp.HotelRecommendResp;
import com.tiangong.hotel.service.HotelLabelConfigService;
import com.tiangong.hotel.service.HotelRecommendLabelRelService;
import com.tiangong.hotel.service.HotelRecommendService;
import com.tiangong.hotel.service.HotelService;
import com.tiangong.organization.domain.OrgPO;
import com.tiangong.util.CollUtilX;
import com.tiangong.util.DateUtilX;
import com.tiangong.util.FileUpUtil;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import static com.tiangong.enums.HotelRecommendEnum.*;
import static com.tiangong.hotel.service.impl.AutoUpdateLowestPriceCityServiceImpl.convert;

/**
 * 酒店推荐排序
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-10-21 11:19:43
 */
@Slf4j
@Service
public class HotelRecommendServiceImpl extends ServiceImpl<HotelRecommendMapper, HotelRecommendEntity> implements HotelRecommendService {

    @Autowired
    private HotelRecommendMapper hotelRecommendMapper;

    @Autowired
    private HotelRecommendLabelRelService hotelRecommendLabelRelService;

    @Autowired
    private HotelService hotelService;

    @Autowired
    private FuzzyQueryServiceImpl fuzzyQueryService;

    @Autowired
    private HotelLabelConfigService hotelLabelConfigService;

    @Autowired
    private FileUpUtil fileUpUtil;

    @Resource(name = "processingCommonExecutor")
    private ThreadPoolTaskExecutor processingCommonExecutor;

    /**
     * 酒店推荐排序新增
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void hotelRecommendAdd(HotelRecommendReq req) {
        if (StrUtilX.isEmpty(req.getAgentCode()) || req.getHotelId() == null) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }

        // 查询是否有设置
        QueryWrapper<HotelRecommendEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("agent_code", req.getAgentCode());
        wrapper.eq("hotel_id", req.getHotelId());
        wrapper.eq("deleted", 0);
        List<HotelRecommendEntity> hotelRecommendList = hotelRecommendMapper.selectList(wrapper);
        if (CollUtilX.isNotEmpty(hotelRecommendList)) {
            throw new SysException(ErrorCodeEnum.CUSTOMER_HOTEL_SORT_ALREADY_EXISTS);
        }
        Date currentDate = DateUtilX.getCurrentDate();

        HotelRecommendEntity hotelRecommendEntity = CommonDtoConvert.INSTANCE.hgotelRecommendEntityConvert(req);
        hotelRecommendEntity.setCreatedDt(currentDate);
        hotelRecommendEntity.setUpdatedDt(currentDate);
        hotelRecommendMapper.insert(hotelRecommendEntity);

        if (CollUtilX.isNotEmpty(req.getHotelLabelConfigIds())) {
            // 先删除再新增
            hotelRecommendLabelRelService.remove(new QueryWrapper<HotelRecommendLabelRelEntity>().eq("hotel_recommend_id", hotelRecommendEntity.getId()));
            // 新增酒店推荐标签关联
            List<HotelRecommendLabelRelEntity> collect = req.getHotelLabelConfigIds().stream().map(item -> {
                HotelRecommendLabelRelEntity entity = new HotelRecommendLabelRelEntity();
                entity.setHotelRecommendId(hotelRecommendEntity.getId());
                entity.setHotelLabelConfigId(item);
                entity.setCreatedBy(req.getCreatedBy());
                entity.setCreatedDt(currentDate);
                return entity;
            }).collect(Collectors.toList());
            hotelRecommendLabelRelService.saveBatch(collect);

            //异步处理，同步酒店标签到mongodb
            CompletableFuture.runAsync(()->{
                HotelPublicDTO dto = new HotelPublicDTO();
                dto.setHotelId(req.getHotelId());

                List<HotelAndAgentCodeLabelDTO> list = new ArrayList<>();
                HotelAndAgentCodeLabelDTO labelDTO = new HotelAndAgentCodeLabelDTO();
                labelDTO.setHotelLabelConfigIds(new HashSet<>(req.getHotelLabelConfigIds()));
                labelDTO.setAgentCode(req.getAgentCode());
                list.add(labelDTO);

                dto.setHotelLabelConfigIds(list);
                //dto.setUpdateLabel(1);
                hotelService.updateHotelMongodbByHotelId(dto);
            }, processingCommonExecutor);
        }

        // 注册事务同步器
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                // 事务提交后，异步执行，同步推荐分到mongodb
                CompletableFuture.runAsync(() -> syncDataToMongodb(Long.valueOf(hotelRecommendEntity.getId()), null), processingCommonExecutor);
            }
        });
    }

    /**
     * 同步推荐分到mongodb
     */
    private void syncDataToMongodb(Long id, Integer recommendScore) {
        try {
            HotelRecommendReq hotelRecommendReq = new HotelRecommendReq();
            List<Long> hotelRecommendIdList = new ArrayList<>();
            hotelRecommendIdList.add(id);
            hotelRecommendReq.setHotelRecommendIdList(hotelRecommendIdList);
            hotelRecommendReq.setRecommendScore(recommendScore);
            List<HotelRecommendResp> respList = hotelRecommendMapper.selectHotelRecommendScoreList(hotelRecommendReq);
            // 判断数据是否为空
            if (CollUtilX.isNotEmpty(respList)) {
                HotelRecommendResp hotelRecommendResp = respList.get(0);

                // 组装对象
                List<RecommendScoreReq> recommendScoreReqList = assembleRecommendScoreInfo(hotelRecommendResp.getHotelId(), hotelRecommendResp.getAgentCode(), hotelRecommendResp.getSumRecommendScore());

                // 更新mongodb公共表里面的数据
                UpdateHotelScoreReq updateHotelScoreReq = new UpdateHotelScoreReq();
                updateHotelScoreReq.setRecommendScoreReqList(recommendScoreReqList);
                hotelService.updateHotelScoreToMongodbBatch(updateHotelScoreReq);
            }
        } catch (Exception e) {
            log.error("异步执行，同步推荐分到mongodb异常", e);
        }
    }

    /**
     * 组装对象
     */
    private List<RecommendScoreReq> assembleRecommendScoreInfo(Long hotelId, String agentCode, Integer sumRecommendScore) {
        List<RecommendScoreReq> recommendScoreReqList = new ArrayList<>();
        RecommendScoreReq recommendScoreReq = new RecommendScoreReq();
        recommendScoreReq.setHotelId(hotelId);
        List<AgentRecommendScoreReq> reqList = new ArrayList<>();
        AgentRecommendScoreReq agentRecommendScoreReq = new AgentRecommendScoreReq();
        agentRecommendScoreReq.setAgentCode(agentCode);
        agentRecommendScoreReq.setRecommendScore(Double.valueOf(sumRecommendScore));
        reqList.add(agentRecommendScoreReq);
        recommendScoreReq.setAgentRecommendScoreReqList(reqList);
        recommendScoreReqList.add(recommendScoreReq);
        return recommendScoreReqList;
    }

    /**
     * 酒店推荐排序删除
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int hotelRecommendDel(HotelRecommendReq req) {
        if (req.getId() == null) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }

        HotelRecommendEntity entity = new HotelRecommendEntity();
        entity.setId(req.getId());
        entity.setUpdatedDt(new Date());
        entity.setDeleted(1);
        int insert = hotelRecommendMapper.updateById(entity);
        if (insert != 1) {
            throw new SysException(ResultEnum.E_1002.getCode(), ResultEnum.E_1002.getMessage());
        }

        // 删除酒店标签关联信息
        hotelRecommendLabelRelService.remove(new QueryWrapper<HotelRecommendLabelRelEntity>().eq("hotel_recommend_id", entity.getId()));

        // 注册事务同步器
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                // 事务提交后
                // 异步执行，同步酒店标签到mongodb
                CompletableFuture.runAsync(()->{
                    HotelPublicDTO dto = new HotelPublicDTO();
                    dto.setHotelId(req.getHotelId());
                    List<HotelAndAgentCodeLabelDTO> list = new ArrayList<>();
                    HotelAndAgentCodeLabelDTO labelDTO = new HotelAndAgentCodeLabelDTO();
                    labelDTO.setHotelLabelConfigIds(new HashSet<>());
                    if ("--".equals(req.getAgentCode())) {
                        labelDTO.setAgentCode("0");
                    }else {
                        labelDTO.setAgentCode(req.getAgentCode());
                    }
                    list.add(labelDTO);

                    dto.setHotelLabelConfigIds(list);
                    //dto.setUpdateLabel(2);
                    hotelService.updateHotelMongodbByHotelId(dto);
                }, processingCommonExecutor);

                // 异步执行，同步推荐分到mongodb
                CompletableFuture.runAsync(() -> {
                    try {
                        HotelRecommendEntity hotelRecommendEntity = hotelRecommendMapper.selectById(entity.getId());
                        // 组装对象
                        List<RecommendScoreReq> recommendScoreReqList = assembleRecommendScoreInfo(hotelRecommendEntity.getHotelId(), hotelRecommendEntity.getAgentCode(), 0);

                        // 更新mongodb公共表里面的数据
                        UpdateHotelScoreReq updateHotelScoreReq = new UpdateHotelScoreReq();
                        updateHotelScoreReq.setRecommendScoreReqList(recommendScoreReqList);
                        hotelService.updateHotelScoreToMongodbBatch(updateHotelScoreReq);
                    } catch (Exception e) {
                        log.error("异步执行，同步推荐分到mongodb异常", e);
                    }
                }, processingCommonExecutor);
            }
        });

        return insert;
    }

    /**
     * 酒店推荐排序编辑
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int hotelRecommendEdit(HotelRecommendReq req) {
        if (req.getId() == null) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }

        // 查询是否有设置
        QueryWrapper<HotelRecommendEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("agent_code", req.getAgentCode());
        wrapper.eq("hotel_id", req.getHotelId());
        wrapper.eq("deleted", 0);
        List<HotelRecommendEntity> hotelRecommendList = hotelRecommendMapper.selectList(wrapper);
        if (CollUtilX.isNotEmpty(hotelRecommendList)) {
            if (hotelRecommendList.size() > 1) {
                throw new SysException(ErrorCodeEnum.CUSTOMER_HOTEL_SORT_ALREADY_EXISTS);
            } else if (req.getId().compareTo(hotelRecommendList.get(0).getId()) != 0) {
                throw new SysException(ErrorCodeEnum.CUSTOMER_HOTEL_SORT_ALREADY_EXISTS);
            }
        }

        Date currentDate = DateUtilX.getCurrentDate();
        HotelRecommendEntity entity = CommonDtoConvert.INSTANCE.hgotelRecommendEntityConvert(req);
        entity.setUpdatedDt(currentDate);
        int insert = hotelRecommendMapper.updateById(entity);
        if (insert != 1) {
            throw new SysException(ResultEnum.E_1003.getCode(), ResultEnum.E_1003.getMessage());
        }

        // 先删除再新增
        hotelRecommendLabelRelService.remove(new QueryWrapper<HotelRecommendLabelRelEntity>().eq("hotel_recommend_id", entity.getId()));
        // 判断是否为空
        if (CollUtilX.isNotEmpty(req.getHotelLabelConfigIds())) {
            // 新增酒店推荐标签关联
            List<HotelRecommendLabelRelEntity> collect = req.getHotelLabelConfigIds().stream().map(item -> {
                HotelRecommendLabelRelEntity entityInfo = new HotelRecommendLabelRelEntity();
                entityInfo.setHotelRecommendId(req.getId());
                entityInfo.setHotelLabelConfigId(item);
                entityInfo.setCreatedBy(req.getCreatedBy());
                entityInfo.setCreatedDt(currentDate);
                return entityInfo;
            }).collect(Collectors.toList());
            hotelRecommendLabelRelService.saveBatch(collect);
        }

        // 注册事务同步器
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                // 事务提交后
                // 异步推送标签到mongodb
                CompletableFuture.runAsync(()->{
                    HotelPublicDTO dto = new HotelPublicDTO();
                    dto.setHotelId(req.getHotelId());

                    List<HotelAndAgentCodeLabelDTO> list = new ArrayList<>();
                    HotelAndAgentCodeLabelDTO labelDTO = new HotelAndAgentCodeLabelDTO();
                    labelDTO.setHotelLabelConfigIds(new HashSet<>(req.getHotelLabelConfigIds()));
                    if (CollectionUtils.isEmpty(labelDTO.getHotelLabelConfigIds())){
                        Set<Integer> set = new HashSet<>();
                        set.add(0);
                        labelDTO.setHotelLabelConfigIds(set);
                    }
                    labelDTO.setAgentCode(req.getAgentCode());
                    list.add(labelDTO);

                    dto.setHotelLabelConfigIds(list);
                    //dto.setUpdateLabel(1);
                    hotelService.updateHotelMongodbByHotelId(dto);
                }, processingCommonExecutor);

                // 异步执行，同步推荐分到mongodb
                CompletableFuture.runAsync(() -> syncDataToMongodb(Long.valueOf(entity.getId()), entity.getRecommendScore()), processingCommonExecutor);
            }
        });

        return insert;
    }

    /**
     * 酒店推荐排序列表（分页）
     */
    @Override
    public PaginationSupportDTO<HotelRecommendResp> hotelRecommendPage(HotelRecommendReq req) {
        IPage<HotelRecommendReq> page = new Page<>(req.getCurrentPage(), req.getPageSize());
        IPage<HotelRecommendResp> iPage = hotelRecommendMapper.selectHotelRecommendPage(page, req);

        List<HotelRecommendResp> collect = null;
        if (CollUtilX.isNotEmpty(iPage.getRecords())) {
            Set<Long> hotelIds = iPage.getRecords().stream().map(HotelRecommendResp::getHotelId).collect(Collectors.toSet());
            DestinationReq destinationReq = new DestinationReq();
            destinationReq.setHotelIds(hotelIds);
            destinationReq.setLanguage(req.getLanguage());
            List<EsHotelDto> esHotelDtos = hotelService.searchDestinationHotel(destinationReq);
            Map<Long, EsHotelDto> esHotelDtoMap = new HashMap<>();
            if (CollUtilX.isNotEmpty(esHotelDtos)) {
                for (EsHotelDto esHotelDto : esHotelDtos) {
                    esHotelDtoMap.put(esHotelDto.getHotelId(), esHotelDto);
                }
            }
            collect = iPage.getRecords().stream().peek(item -> {
                if (CollUtilX.isNotEmpty(item.getHotelLabelConfigList())) {
                    List<Integer> hotelLabelConfigIds = new ArrayList<>();
                    List<String> hotelLabelNameList = new ArrayList<>();
                    for (HotelLabelConfigResp hotelLabelConfigResp : item.getHotelLabelConfigList()) {
                        hotelLabelConfigIds.add(hotelLabelConfigResp.getHotelLabelConfigId());
                        hotelLabelNameList.add(hotelLabelConfigResp.getLabelName());
                    }
                    item.setHotelLabelConfigIds(hotelLabelConfigIds);
                    item.setHotelLabelNames(StrUtilX.listToString(hotelLabelNameList, "、"));
                }
                if ("0".equals(item.getAgentCode())) {
                    item.setAgentCode("--");
                    if (LanguageTypeEnum.zh_CN.getValue().equals(req.getLanguage())) {
                        item.setAgentName("全部客户");
                    } else {
                        item.setAgentName("All customers");
                    }
                }
                EsHotelDto esHotelDto = esHotelDtoMap.get(item.getHotelId());
                if (esHotelDto != null) {
                    item.setHotelName(esHotelDto.getHotelName());
                    item.setCityName(esHotelDto.getCityName());
                }
            }).collect(Collectors.toList());
        }

        PaginationSupportDTO<HotelRecommendResp> paginationSupportDTO = new PaginationSupportDTO<>();
        paginationSupportDTO.setItemList(collect);
        paginationSupportDTO.setCurrentPage((int) iPage.getCurrent());
        paginationSupportDTO.setPageSize((int) iPage.getSize());
        paginationSupportDTO.setTotalCount(iPage.getTotal());
        paginationSupportDTO.setTotalPage((int) iPage.getPages());

        return paginationSupportDTO;
    }

    @Override
    public Set<String> queryHotelLabelInfo(HotelInfoReq req) {
        List<HotelLabelConfigResp> hotelLabelConfigList = hotelRecommendMapper.queryHotelLabelInfo(req);
        if (CollUtilX.isEmpty(hotelLabelConfigList)) {
            return null;
        }
        return hotelLabelConfigList.stream().filter(Objects::nonNull).map(HotelLabelConfigResp::getLabelName).collect(Collectors.toSet());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response<Object> importHotelRecommend(MultipartFile file, String userName) {
        Response<Object> response = null;
        InputStream in = null;
        // 加载缓存标识
        boolean loadCheck = false;
        //批量插入到数据库的数组
        List<HotelRecommendEntity> poList = new ArrayList<>();
        //需要更新到mongodb的酒店标签
        Map<Integer,List<Integer>> recommendLabelIdMap = new HashMap<>();
        try{
            // 导入的Excel数据存储的数组
            List<ImportHotelRecommendReq> dataList;
            // 导入的Excel的处理情况
            ImportHotelRecommendResp uploadRespVO = new ImportHotelRecommendResp();
            if (StrUtilX.isEmpty(file.getOriginalFilename())) {
                throw new SysException(ErrorCodeEnum.FILE_FORMAT_ERROR);
            }
            // 获取文件名称后缀
            String suffix = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf(".") + 1);

            if (!"xlsx".contains(suffix.toLowerCase())) {
                throw new SysException(ErrorCodeEnum.FILE_FORMAT_ERROR);
            }

            in = file.getInputStream();
            Workbook wb = WorkbookFactory.create(in);
            Sheet sheet = wb.getSheetAt(0);

            // 校验文件格式
            boolean result = checkExcelHeaders(sheet);
            if (!result) {
                throw new SysException(ErrorCodeEnum.IMPORT_FILE_FORMAT_ERROR);
            }

            //解析Excel数据
            dataList = analyzeExcel(sheet);

            ConcurrentHashMap<String, ImportHotelRecommendReqDTO> dataListMap = new ConcurrentHashMap <>();

            if(CollectionUtil.isNotEmpty(dataList)){
                List<ExportHotelRecommendReq> failDataList = new ArrayList<>();
                Integer failedCount = 0;

                for(ImportHotelRecommendReq req:dataList){
                    String key = req.getClientName() + "=" + req.getHotelName();
                    ImportHotelRecommendReqDTO importHotelRecommendReqDTO = CommonDtoConvert.INSTANCE.ImportHotelRecommendReqDTOConvert(req);
                    if (!dataListMap.containsKey(key)) {
                        dataListMap.put(key, importHotelRecommendReqDTO);
                    } else {
                        ExportHotelRecommendReq exportHotelRecommendReq = CommonDtoConvert.INSTANCE.ExportHotelRecommendReqConvert(importHotelRecommendReqDTO);
                        exportHotelRecommendReq.setFailReason(DATA_REPEAT.errorDesc);
                        failDataList.add(exportHotelRecommendReq);
                        failedCount ++;
                    }
                }
                // 系统中已存在且状态为启用的客户
                Map<String,String> clientMap = new HashMap<>();
                // 系统中已存在的酒店标签
                Map<String,Integer> hotelLabelMap = new HashMap<>();
                // 初始化客户，酒店标签数据
                InitializeStatus(clientMap,hotelLabelMap);
                // 老酒店排序数据
                List<HotelRecommendResp> hotelRecommendRespList = hotelRecommendMapper.selectAllRecommend();
                for(String key : dataListMap.keySet()){
                    try{
                        String clientName = key.split("=")[0];
                        String hotelName = key.split("=",-1)[1];

                        // 客户名称
                        if(!clientMap.containsKey(clientName) || clientName.isEmpty()){
                           throw new SysException(CLIENT_NAME_MISS.errorCode,CLIENT_NAME_MISS.errorDesc);
                        }

                        // 酒店名称
                        List<HotelLanguageCollection> hotelLanguageCollectionList = hotelService.findMongoDBHotelList(hotelName);
                        if(CollUtilX.isEmpty(hotelLanguageCollectionList) || hotelName.isEmpty()){
                            throw new SysException(HOTEL_NAME_MISS.errorCode,HOTEL_NAME_MISS.errorDesc);
                        }

                        // 酒店标签
                        if(StringUtils.isNotEmpty(dataListMap.get(key).getHotelLabelConfigIds())){
                            String[] hotelLabels = dataListMap.get(key).getHotelLabelConfigIds().split("，");
                            for(String hotelLabel : hotelLabels){
                                if(!hotelLabelMap.containsKey(hotelLabel)){
                                    throw new SysException(HOTEL_LABEL_MISS.errorCode,HOTEL_LABEL_MISS.errorDesc);
                                }
                            }
                        }

                        // 酒店推荐值
                        int recommendScore = Integer.parseInt(dataListMap.get(key).getRecommendScore());
                        if (recommendScore < 0 || recommendScore > 9999) {
                            throw new SysException(RECOMMEND_SCORE_ERROR.errorCode,RECOMMEND_SCORE_ERROR.errorDesc);
                        }

                        // 设置酒店id 和 客户编码
                        dataListMap.get(key).setHotelId(hotelLanguageCollectionList.get(0).getHotelId());
                        dataListMap.get(key).setAgentCode(clientMap.get(clientName));

                    }catch (SysException e){
                        ExportHotelRecommendReq exportHotelRecommendReq = CommonDtoConvert.INSTANCE.ExportHotelRecommendReqConvert(dataListMap.get(key));
                        exportHotelRecommendReq.setFailReason(e.getMessage());
                        failDataList.add(exportHotelRecommendReq);
                        dataListMap.remove(key);
                        failedCount ++;
                    }catch (NumberFormatException e) {
                        ExportHotelRecommendReq exportHotelRecommendReq = CommonDtoConvert.INSTANCE.ExportHotelRecommendReqConvert(dataListMap.get(key));
                        exportHotelRecommendReq.setFailReason(RECOMMEND_SCORE_ERROR.errorDesc);
                        failDataList.add(exportHotelRecommendReq);
                        dataListMap.remove(key);
                        failedCount ++;
                    }
                }

                //酒店排序是否有重复行
                Iterator<Map.Entry<String, ImportHotelRecommendReqDTO>> iterator = dataListMap.entrySet().iterator();
                while (iterator.hasNext()) {
                    Map.Entry<String, ImportHotelRecommendReqDTO> entry = iterator.next();
                    a:for(HotelRecommendResp hotelRecommendResp : hotelRecommendRespList) {
                        if (entry.getValue().getAgentCode().equals(hotelRecommendResp.getAgentCode()) &&
                                entry.getValue().getHotelId().equals(hotelRecommendResp.getHotelId())
                        ) {
                            ExportHotelRecommendReq exportHotelRecommendReq = CommonDtoConvert.INSTANCE.ExportHotelRecommendReqConvert(entry.getValue());
                            exportHotelRecommendReq.setFailReason(DATA_REPEAT.errorDesc);
                            failDataList.add(exportHotelRecommendReq);
                            failedCount ++;
                            iterator.remove();
                            break a;
                        }
                    }
                }

                Date currentDate = DateUtilX.getCurrentDate();

                // 符合条件，可插入的数据 ,部分成功的数据
                if(CollectionUtil.isNotEmpty(dataListMap)) {
                    // 酒店排序推荐分
                    dataListMap.values().forEach(item -> {
                        HotelRecommendEntity hotelRecommendEntity = new HotelRecommendEntity();
                        hotelRecommendEntity.setHotelId(item.getHotelId());
                        hotelRecommendEntity.setAgentCode(clientMap.get(item.getClientName()));
                        hotelRecommendEntity.setRecommendScore(Integer.valueOf(item.getRecommendScore()));
                        hotelRecommendEntity.setCreatedBy(userName);
                        hotelRecommendEntity.setUpdatedBy(userName);
                        hotelRecommendEntity.setCreatedDt(currentDate);
                        hotelRecommendEntity.setUpdatedDt(currentDate);
                        poList.add(hotelRecommendEntity);
                    });
                    this.saveBatch(poList);

                    // 酒店标签
                    List<HotelRecommendLabelRelEntity> hotelRecommendLabelRelEntityList = new ArrayList<>();
                    for (HotelRecommendEntity hotelRecommendEntity : poList) {
                        ImportHotelRecommendReqDTO item = null;
                        b:for (ImportHotelRecommendReqDTO importHotelRecommendReqDTO : dataListMap.values()) {
                            if (clientMap.get(importHotelRecommendReqDTO.getClientName()).equals(hotelRecommendEntity.getAgentCode()) &&
                                    importHotelRecommendReqDTO.getHotelId().equals(hotelRecommendEntity.getHotelId())) {
                                item = importHotelRecommendReqDTO;
                                break b;
                            }
                        }
                        if(StrUtilX.isNotEmpty(item.getHotelLabelConfigIds())) {
                            String[] hotelLabels = item.getHotelLabelConfigIds().split("，");
                            List<Integer> hotelLabelIds = new ArrayList<>();
                            for (String hotelLabel : hotelLabels) {
                                HotelRecommendLabelRelEntity hotelRecommendLabelRelEntity = new HotelRecommendLabelRelEntity();
                                Integer hotelConfigId = hotelLabelMap.get(hotelLabel);
                                hotelRecommendLabelRelEntity.setHotelRecommendId(hotelRecommendEntity.getId());
                                if (null != hotelConfigId) {
                                    hotelRecommendLabelRelEntity.setHotelLabelConfigId(hotelConfigId);
                                }
                                hotelRecommendLabelRelEntity.setCreatedBy(userName);
                                hotelRecommendLabelRelEntity.setCreatedDt(currentDate);
                                hotelRecommendLabelRelEntityList.add(hotelRecommendLabelRelEntity);
                                hotelLabelIds.add(hotelConfigId);
                            }
                            recommendLabelIdMap.put(hotelRecommendEntity.getId(), hotelLabelIds);
                        }
                    }
                    hotelRecommendLabelRelService.saveBatch(hotelRecommendLabelRelEntityList);
                    loadCheck = true;
                }

                // 失败文件处理
                if(CollectionUtil.isNotEmpty(failDataList)){
                    // 1. list 转excel 文件
                    // 创建ExcelWriter
                    ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                    ExcelWriter excelWriter = EasyExcel.write(outputStream,ExportHotelRecommendReq.class).build();
                    WriteSheet writeSheet = EasyExcel.writerSheet("sheet1").build();

                    // 写入数据
                    excelWriter.write(failDataList,writeSheet);
                    excelWriter.finish(); // 完成写入操作
                    // 转化为MultipartFile
                    MultipartFile multipartFile = convert(outputStream,"导入酒店排序失败.xlsx");
                    String fileUrl = fileUpUtil.uploadFile2(multipartFile,"导入酒店排序失败.xlsx");
                    // 2.上传文件到阿里云
                    uploadRespVO.setFailedDataFileUrl(fileUrl);
                }
                uploadRespVO.setSuccessCount(String.valueOf(dataListMap.keySet().size()));
                uploadRespVO.setFailedCount(String.valueOf(failedCount));
                response = Response.success(uploadRespVO);
            }else{
                throw new SysException(ErrorCodeEnum.SUPPLY_FILE_CONTENT_IS_EMPTY);
            }

        }catch (SysException e) {
            log.error("导入酒店排序失败", e);
            response = Response.error(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("导入酒店排序异常", e);
            response = Response.error(ErrorCodeEnum.SYSTEM_EXCEPTION.errorCode, ErrorCodeEnum.SYSTEM_EXCEPTION.errorDesc);
        } finally {
            if (in != null) {
                try {
                    in.close();
                } catch (IOException e) {
                    log.error("导入酒店排序异常", e);
                }
            }
            // 判断是否加载缓存,更新到mongodb中
            if (loadCheck) {
                // 注册事务同步器
                TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                    @Override
                    public void afterCommit() {
                        // 异步加载缓存Mongodb中
                        CompletableFuture.runAsync(() -> cacheMongoAsync(poList,recommendLabelIdMap ), processingCommonExecutor);
                    }
                });
            }
        }
        return response;
    }

    private void InitializeStatus(Map<String, String> clientMap, Map<String, Integer> hotelLabelMap) {
        List<OrgPO> orgPOList = fuzzyQueryService.queryAllAgentList();
        for(OrgPO orgPO : orgPOList){
            clientMap.put(orgPO.getOrgName(),orgPO.getOrgCode());
        }

        clientMap.put("全部客户","0");

        List<HotelLabelConfigEntity> hotelLabelConfigEntityList = hotelLabelConfigService.selectAllHotelLabel();
        for(HotelLabelConfigEntity hotelLabelConfigEntity : hotelLabelConfigEntityList){
            hotelLabelMap.put(hotelLabelConfigEntity.getLabelName(),hotelLabelConfigEntity.getHotelLabelConfigId());
        }

    }

    private void cacheMongoAsync(List<HotelRecommendEntity> poList, Map<Integer,List<Integer>> recommendLabelIdMap) {
        // 更新酒店标签
        List<HotelPublicDTO> publicDTOS = new ArrayList<>();
        for(HotelRecommendEntity hotelRecommendEntity:poList){
            HotelPublicDTO dto = new HotelPublicDTO();
            dto.setHotelId(hotelRecommendEntity.getHotelId());

            List<HotelAndAgentCodeLabelDTO> list = new ArrayList<>();
            HotelAndAgentCodeLabelDTO labelDTO = new HotelAndAgentCodeLabelDTO();
            labelDTO.setHotelLabelConfigIds(new HashSet<>(recommendLabelIdMap.get(hotelRecommendEntity.getId())));
            labelDTO.setAgentCode(hotelRecommendEntity.getAgentCode());
            list.add(labelDTO);

            dto.setHotelLabelConfigIds(list);
            publicDTOS.add(dto);
        }
        hotelService.updateHotelMongodbByHotelIdBatch(publicDTOS);

        // 更新酒店的推荐分
        HotelRecommendReq hotelRecommendReq = new HotelRecommendReq();
        List<Long> hotelRecommendIdList = poList.stream().map(item->Long.valueOf(item.getId())).collect(Collectors.toList());
        hotelRecommendReq.setHotelRecommendIdList(hotelRecommendIdList);
        hotelRecommendReq.setRecommendScore(null);
        List<HotelRecommendResp> respList = hotelRecommendMapper.selectHotelRecommendScoreList(hotelRecommendReq);
        // 判断数据是否为空
        if(CollUtilX.isNotEmpty(respList)){
            // 组装对象
            List<RecommendScoreReq> recommendScoreReqList = assembleRecommendScoreList(respList);

            //更新mongodb 公共表里面的数据
            UpdateHotelScoreReq updateHotelScoreReq = new UpdateHotelScoreReq();
            updateHotelScoreReq.setRecommendScoreReqList(recommendScoreReqList);
            hotelService.updateHotelScoreToMongodbBatch(updateHotelScoreReq);
        }
    }

    private List<RecommendScoreReq> assembleRecommendScoreList(List<HotelRecommendResp> respList) {
        List<RecommendScoreReq> recommendScoreReqList = new ArrayList<>();

        for(HotelRecommendResp hotelRecommendResp : respList){
            RecommendScoreReq recommendScoreReq = new RecommendScoreReq();
            recommendScoreReq.setHotelId(hotelRecommendResp.getHotelId());
            List<AgentRecommendScoreReq> reqList = new ArrayList<>();
            AgentRecommendScoreReq agentRecommendScoreReq = new AgentRecommendScoreReq();
            agentRecommendScoreReq.setAgentCode(hotelRecommendResp.getAgentCode());
            agentRecommendScoreReq.setRecommendScore(Double.valueOf(hotelRecommendResp.getSumRecommendScore()));
            reqList.add(agentRecommendScoreReq);
            recommendScoreReq.setAgentRecommendScoreReqList(reqList);
            recommendScoreReqList.add(recommendScoreReq);
        }

        return recommendScoreReqList;
    }


    private List<ImportHotelRecommendReq> analyzeExcel(Sheet sheet) {
        List<ImportHotelRecommendReq> importHotelRecommendReqList = new ArrayList<>();
        int rowSize = sheet.getPhysicalNumberOfRows();
        for(int i = 2; i <= rowSize; i++){
            ImportHotelRecommendReq req = new ImportHotelRecommendReq();
            Row row = sheet.getRow(i);
            if(row == null){ // 修正此处条件判断
                continue;
            }
            for(int j = 0 ; j < 4; j++){
                Cell cell = null;
                try{
                    cell = row.getCell(j);
                    switch (j){
                        case 0:
                            if(cell !=null){
                                DataFormatter formatter = new DataFormatter();
                                String value  = formatter.formatCellValue(cell).trim();
                                req.setClientName(StringUtils.isEmpty(value)? "":value);
                            }
                            break;
                        case 1:
                            if (cell != null ) {
                                DataFormatter formatter = new DataFormatter();
                                String value = formatter.formatCellValue(cell).trim();
                                req.setHotelName(StringUtils.isEmpty(value)? "":value);
                            }
                            break;
                        case 2:
                            if (cell != null) {
                                DataFormatter formatter = new DataFormatter();
                                String value = formatter.formatCellValue(cell).trim();
                                req.setHotelLabelConfigIds(value);
                            }else{
                                req.setHotelLabelConfigIds("");
                            }
                            break;
                        case 3:
                            if (cell != null) {
                                DataFormatter formatter = new DataFormatter();
                                String value = formatter.formatCellValue(cell).trim();
                                req.setRecommendScore(value);
                            }
                            break;
                    }
                }catch (Exception e){
                    log.error("批量到酒店排序，解析excel第{" + i +"}行，【{" + j +"}】列出现异常，cell={}"+JSONUtil.toJsonStr(cell));
                }
            }
            importHotelRecommendReqList.add(req);
        }
        return importHotelRecommendReqList;
    }

    private boolean checkExcelHeaders(Sheet sheet) {
        try {
            // 获取 excel 第一行数据（表头）
            Row row = sheet.getRow(1);
            // 算下有多少列
            int colCount = sheet.getRow(0).getLastCellNum();
            for (int j = 0; j < colCount; j++) {
                Cell cell = row.getCell(j);
                String headName = cell.getStringCellValue().trim();
                if (ExcelTitleEnum.getKeyByValue(headName) != j) {
                    return false;
                }
            }
            return true;
        } catch (Exception e) {
            log.error("检查Excel文件表头信息异常", e);
            return false;
        }
    }


}