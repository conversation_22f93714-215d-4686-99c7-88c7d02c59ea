package com.tiangong.hotel.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.dto.hotel.HotelAvailableDTO;
import com.tiangong.dto.hotel.AddHotelAvailableReq;
import com.tiangong.hotel.domain.HotelAvailablePO;
import com.tiangong.hotel.domain.req.DelHotelAvailableReq;
import com.tiangong.hotel.domain.req.QueryHotelAvailableReq;
import com.tiangong.hotel.domain.resp.HotelAvailableListResp;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/1/3 11:00
 */
public interface HotelAvailableService extends IService<HotelAvailablePO> {

    /**
     * 新增酒店可见性
     */
    void addHotelAvailable(AddHotelAvailableReq req);

    /**
     * 删除酒店可见性
     */
    void delHotelAvailable(DelHotelAvailableReq req);

    /**
     * 查询酒店可见性列表
     */
    PaginationSupportDTO<HotelAvailableListResp> queryHotelListAvailable(QueryHotelAvailableReq req);

    /**
     * 查询酒店是否是黑名单
     */
    HotelAvailableDTO queryHotelAvailable(AddHotelAvailableReq req);

    /**
     * 查询客户酒店黑名单 酒店id列表
     */
    List<Long> queryAgentBlacklistHotelIds(AddHotelAvailableReq req);
}
