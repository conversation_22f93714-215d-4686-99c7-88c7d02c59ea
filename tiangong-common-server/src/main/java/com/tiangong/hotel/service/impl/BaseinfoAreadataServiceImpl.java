package com.tiangong.hotel.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tiangong.convert.CommonDtoConvert;
import com.tiangong.dto.hotel.HotCityResp;
import com.tiangong.enums.ErrorCodeEnum;
import com.tiangong.enums.LanguageTypeEnum;
import com.tiangong.exception.SysException;
import com.tiangong.hotel.domain.BaseinfoAreadataEntity;
import com.tiangong.hotel.domain.BaseinfoHotCityEntity;
import com.tiangong.hotel.domain.req.AddOrUpdateHotCityReq;
import com.tiangong.hotel.mapper.BaseinfoHotCityMapper;
import com.tiangong.hotel.req.QueryAreaDataReq;
import com.tiangong.hotel.resp.QueryCityResp;
import com.tiangong.hotel.service.BaseinfoAreadataService;
import com.tiangong.organization.mapper.AreaDataMapper;
import com.tiangong.table.TXTable;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class BaseinfoAreadataServiceImpl implements BaseinfoAreadataService {

    @Resource
    private AreaDataMapper areaDataMapper;

    @Resource
    private BaseinfoHotCityMapper baseinfoHotCityMapper;

    @Override
    public List<BaseinfoAreadataEntity> queryAreaDataListByType(QueryAreaDataReq req) {
        return areaDataMapper.queryAreaDataListByType(req, TXTable.getAreaTXTable(req.getLanguage()));
    }

    @Override
    public List<HotCityResp> queryHotCityList(String language) {
        List<HotCityResp> list = new ArrayList<>();
        List<BaseinfoHotCityEntity> baseinfoHotCityEntities = baseinfoHotCityMapper.selectList(new LambdaQueryWrapper<>());
        for (BaseinfoHotCityEntity city : baseinfoHotCityEntities) {
            HotCityResp resp = new HotCityResp();
            BeanUtils.copyProperties(city, resp);
            if (LanguageTypeEnum.en_US.getValue().equals(language)) {
                resp.setCityName(city.getCityEnName());
            }
            list.add(resp);
        }
        return list;
    }

    @Override
    public List<BaseinfoHotCityEntity> queryHotCityListManger() {
        return baseinfoHotCityMapper.selectList(new LambdaQueryWrapper<BaseinfoHotCityEntity>().orderByAsc(BaseinfoHotCityEntity::getSupplyType).orderByDesc(BaseinfoHotCityEntity::getUpdateTime));
    }

    @Override
    public void addOrUpdateHotCity(AddOrUpdateHotCityReq req) {
        // 判断是否是删除
        if (req.getTop() == 0) {
            baseinfoHotCityMapper.delete(new LambdaQueryWrapper<BaseinfoHotCityEntity>().eq(BaseinfoHotCityEntity::getCityCode, req.getCityCode()));
            return;
        }
        req.setTXTable(TXTable.getAreaTXTable(LanguageTypeEnum.zh_CN.getValue()));
        BaseinfoAreadataEntity baseinfoAreadataEntity = areaDataMapper.selectCity(TXTable.getAreaTXTable(LanguageTypeEnum.zh_CN.getValue()), req.getCityCode());
        BaseinfoAreadataEntity baseinfoAreadataEn = areaDataMapper.selectCity(TXTable.getAreaTXTable(LanguageTypeEnum.en_US.getValue()), req.getCityCode());

        if (baseinfoAreadataEn == null && baseinfoAreadataEntity == null) {
            throw new SysException(ErrorCodeEnum.CITY_IS_NOT_ALREADY);
        }

        BaseinfoHotCityEntity entity = new BaseinfoHotCityEntity();

        if (baseinfoAreadataEn == null) {
            entity = CommonDtoConvert.INSTANCE.baseinfoHotCityEntityConvert(baseinfoAreadataEntity);
            entity.setCityName(baseinfoAreadataEntity.getAreaName());
        } else if (baseinfoAreadataEntity == null) {
            entity = CommonDtoConvert.INSTANCE.baseinfoHotCityEntityConvert(baseinfoAreadataEn);
            entity.setCityEnName(baseinfoAreadataEn.getAreaName());
        } else {
            entity = CommonDtoConvert.INSTANCE.baseinfoHotCityEntityConvert(baseinfoAreadataEntity);
            entity.setCityName(baseinfoAreadataEntity.getAreaName());
            entity.setCityEnName(baseinfoAreadataEn.getAreaName());
        }
        entity.setCityCode(req.getCityCode());
        entity.setAreaType(3);
        entity.setUpdateTime(new Date());
        BaseinfoHotCityEntity entity1 = baseinfoHotCityMapper.selectOne(new LambdaQueryWrapper<BaseinfoHotCityEntity>().eq(BaseinfoHotCityEntity::getCityCode, req.getCityCode()).last(" limit 1"));

        if (entity1 == null) {
            baseinfoHotCityMapper.insert(entity);
        } else {
            entity.setId(entity1.getId());
            baseinfoHotCityMapper.updateById(entity);
        }
    }

    @Override
    public void delAreaDataByType(Integer areaType, String language) {
        areaDataMapper.delAreaDataByType(areaType, TXTable.getAreaTXTable(language));
    }

    @Override
    public void insertAreaData(List<BaseinfoAreadataEntity> list, String language) {
        areaDataMapper.insertAreaData(list, TXTable.getAreaTXTable(language));
    }

    @Override
    public List<QueryCityResp> queryCity(List<String> list, String language, String areaName) {
        return areaDataMapper.queryCity(list, TXTable.getAreaTXTable(language), areaName);
    }

}
