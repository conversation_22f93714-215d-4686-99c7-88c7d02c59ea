package com.tiangong.hotel.service.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tiangong.common.Constant;
import com.tiangong.config.SettingsConstant;
import com.tiangong.convert.CommonDtoConvert;
import com.tiangong.enums.SlsEnum;
import com.tiangong.enums.SlsLogLevelEnum;
import com.tiangong.hotel.domain.CityAvgPriceEntity;
import com.tiangong.hotel.domain.req.MainlyPopularizePriceReq;
import com.tiangong.hotel.mapper.CityAvgPriceMapper;
import com.tiangong.hotel.req.CityAvgPriceReq;
import com.tiangong.hotel.req.HotelAvgPriceReq;
import com.tiangong.hotel.req.HotelHeatReq;
import com.tiangong.hotel.resp.HotelAvgPriceResp;
import com.tiangong.hotel.service.CityAvgPriceService;
import com.tiangong.hotel.service.HotelAvgPriceService;
import com.tiangong.hotel.service.HotelHeatService;
import com.tiangong.keys.RedisKey;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.util.CollUtilX;
import com.tiangong.util.DateUtilX;
import com.tiangong.util.SlsLoggerUtil;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 城市平均价信息
 */
@Slf4j
@Service
public class CityAvgPriceServiceImpl extends ServiceImpl<CityAvgPriceMapper, CityAvgPriceEntity> implements CityAvgPriceService {

    @Autowired
    private SettingsConstant settingsConstant;

    @Autowired
    private HotelHeatService hotelHeatService;

    @Autowired
    private HotelAvgPriceService hotelAvgPriceService;

    @Autowired
    private SlsLoggerUtil slsLoggerUtil;

    @Override
    @Transactional
    public void batchSaveCityAvgPrice(List<CityAvgPriceReq> reqList) {
        if (CollUtilX.isEmpty(reqList)) {
            return;
        }
        List<CityAvgPriceEntity> entities = CommonDtoConvert.INSTANCE.CityAvgPriceConvert(reqList);
        this.saveBatch(entities);
    }

    @Override
    public void calculateHotelHeatHotelAvgPriceScoreHavePriceTask(String param) {
        List<String> cityCodes;
        if (StrUtilX.isNotEmpty(param)) {
            cityCodes = StrUtilX.stringToList(param, ",");
        } else {
            if (!RedisTemplateX.hasKey(RedisKey.HOTEL_HEAT_CITY_CODE_KEY)) {
                return;
            }
            cityCodes = RedisTemplateX.setPopCount(RedisKey.HOTEL_HEAT_CITY_CODE_KEY, settingsConstant.getConsumerCalculateCityAvgPriceScoreHavePriceCount());
        }
        if (CollUtilX.isEmpty(cityCodes)) {
            return;
        }
        for (String cityCode : cityCodes) {
            Set<String> hotelIds = null;
            // 传入指定参数
            if (StrUtilX.isNotEmpty(param)) {
                HotelAvgPriceReq hotelAvgPriceReq = new HotelAvgPriceReq();
                hotelAvgPriceReq.setCityCode(cityCode);
                List<HotelAvgPriceResp> respList = hotelAvgPriceService.queryHotelAvgPriceList(hotelAvgPriceReq);
                if (CollUtilX.isNotEmpty(respList)) {
                    hotelIds = respList.stream().map(item -> String.valueOf(item.getHotelId())).collect(Collectors.toSet());
                }
            } else {
                if (!RedisTemplateX.hasKey(RedisKey.HOTEL_HEAT_CITY_HOTEL_ID_KEY + cityCode)) {
                    continue;
                }
                hotelIds = RedisTemplateX.setMembers(RedisKey.HOTEL_HEAT_CITY_HOTEL_ID_KEY + cityCode);
                // 取出数据后删除数据
                RedisTemplateX.delete(RedisKey.HOTEL_HEAT_CITY_HOTEL_ID_KEY + cityCode);
            }
            if (CollUtilX.isEmpty(hotelIds)) {
                continue;
            }
            try {
                HotelAvgPriceReq hotelAvgPriceReq = new HotelAvgPriceReq();
                hotelAvgPriceReq.setHotelIds(hotelIds);
                List<HotelAvgPriceResp> respList = hotelAvgPriceService.queryHotelAvgPriceList(hotelAvgPriceReq);
                if (CollUtilX.isEmpty(respList)) {
                    continue;
                }
                Date currentDate = DateUtilX.getCurrentDate();
                List<HotelHeatReq> reqList = respList.stream().map(item -> {
                    HotelHeatReq hotelHeatReq = new HotelHeatReq();
                    hotelHeatReq.setHotelId(item.getHotelId());
                    // 判断酒店平均价是否低于城市平均价
                    Object cityAvgPrice = RedisTemplateX.hashGet(RedisKey.HOTEL_HEAT_CITY_AVG_PRICE_KEY, item.getCityCode());
                    if (cityAvgPrice != null) {
                        // 平均房价在城市级别推荐价格区间内40W、在国家级别推荐价格区间内30W、大于等于城市平均起价20W，否则10W
                        Object cityPrice = RedisTemplateX.hashGet(RedisKey.REGION_MAINLY_POPULARIZE_PRICE_CITY_KEY, item.getCityCode());
                        if (cityPrice != null) {
                            MainlyPopularizePriceReq priceReq = JSON.parseObject(JSONUtil.toJsonStr(cityPrice), new TypeReference<MainlyPopularizePriceReq>(){});
                            if (item.getAvgPrice().compareTo(priceReq.getStartPrice()) >= 0 && item.getAvgPrice().compareTo(priceReq.getEndPrice()) <= 0) {
                                hotelHeatReq.setCityAvgPriceScore(400000L);
                            }
                        }
                        if (hotelHeatReq.getCityAvgPriceScore() == null && StrUtilX.isNotEmpty(item.getCountryCode())) {
                            Object countryPrice = RedisTemplateX.hashGet(RedisKey.REGION_MAINLY_POPULARIZE_PRICE_COUNTRY_KEY, item.getCountryCode());
                            if (countryPrice != null) {
                                MainlyPopularizePriceReq priceReq = JSON.parseObject(JSONUtil.toJsonStr(countryPrice), new TypeReference<MainlyPopularizePriceReq>(){});
                                if (item.getAvgPrice().compareTo(priceReq.getStartPrice()) >= 0 && item.getAvgPrice().compareTo(priceReq.getEndPrice()) <= 0) {
                                    hotelHeatReq.setCityAvgPriceScore(300000L);
                                }
                            }
                        }
                        if (hotelHeatReq.getCityAvgPriceScore() == null) {
                            if (item.getAvgPrice().compareTo(new BigDecimal(String.valueOf(cityAvgPrice))) < 0) {
                                hotelHeatReq.setCityAvgPriceScore(100000L);
                            } else {
                                hotelHeatReq.setCityAvgPriceScore(200000L);
                            }
                        }
                    } else {
                        hotelHeatReq.setCityAvgPriceScore(100000L);
                    }
                    hotelHeatReq.setUpdatedBy(Constant.SYSTEM);
                    hotelHeatReq.setUpdatedDt(currentDate);
                    return hotelHeatReq;
                }).collect(Collectors.toList());
                // 修改热度分数
                hotelHeatService.updateHotelHeatScore(reqList);
            } catch (Exception e) {
                log.error("计算酒店热度酒店平均房价分数(有平均价)任务异常", e);
                Map<String, String> map = new HashMap<>();
                map.put(SlsEnum.LEVEL.getType(), SlsLogLevelEnum.ERROR.getLevel());
                map.put(SlsEnum.NAME.getType(), "calculateHotelHeatHotelAvgPriceScoreHavePriceTask");
                map.put(SlsEnum.MESSAGE.getType(), StrUtilX.getStackTraceAsString(e));
                slsLoggerUtil.saveLog(map, "calculateHotelHeatHotelAvgPriceScoreHavePriceTask", "tiangong-common-server");
                // 异常重新插入到缓存
                RedisTemplateX.setAdd(RedisKey.HOTEL_HEAT_CITY_CODE_KEY, cityCode);
                String[] ids = hotelIds.toArray(new String[0]);
                RedisTemplateX.setAdd(RedisKey.HOTEL_HEAT_CITY_HOTEL_ID_KEY + cityCode, ids);
            }
        }
    }

    @Override
    public void calculateHotelHeatHotelAvgPriceScoreNotPriceTask(String param) {
        List<String> hotelIds;
        if (StrUtilX.isNotEmpty(param)) {
            hotelIds = StrUtilX.stringToList(param, ",");
        } else {
            if (!RedisTemplateX.hasKey(RedisKey.CALCULATE_CITY_AVG_PRICE_SCORE_HOTEL_ID_KEY)) {
                return;
            }
            hotelIds = RedisTemplateX.setPopCount(RedisKey.CALCULATE_CITY_AVG_PRICE_SCORE_HOTEL_ID_KEY, settingsConstant.getConsumerCalculateCityAvgPriceScoreNotPriceCount());
        }
        if (CollUtilX.isEmpty(hotelIds)) {
            return;
        }

        try {
            Date currentDate = DateUtilX.getCurrentDate();
            List<HotelHeatReq> reqList = hotelIds.stream().map(item -> {
                HotelHeatReq hotelHeatReq = new HotelHeatReq();
                hotelHeatReq.setHotelId(Long.parseLong(item));
                // 没有酒店平均直接设置为2W
                hotelHeatReq.setCityAvgPriceScore(100000L);
                hotelHeatReq.setUpdatedBy(Constant.SYSTEM);
                hotelHeatReq.setUpdatedDt(currentDate);
                return hotelHeatReq;
            }).collect(Collectors.toList());
            // 修改热度分数
            hotelHeatService.updateHotelHeatScore(reqList);
        } catch (Exception e) {
            log.error("计算酒店热度酒店平均房价分数(没有平均价)任务异常", e);
            // 异常重新插入到缓存
            String[] ids = hotelIds.toArray(new String[0]);
            RedisTemplateX.setAdd(RedisKey.CALCULATE_CITY_AVG_PRICE_SCORE_HOTEL_ID_KEY, ids);
        }
    }
}