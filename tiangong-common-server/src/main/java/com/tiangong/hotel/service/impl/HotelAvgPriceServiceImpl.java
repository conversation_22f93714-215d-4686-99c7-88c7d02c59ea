package com.tiangong.hotel.service.impl;

import com.alibaba.nacos.shaded.com.google.common.util.concurrent.RateLimiter;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tiangong.cloud.commonbean.utils.IDUtilS;
import com.tiangong.common.Constant;
import com.tiangong.config.SettingsConstant;
import com.tiangong.convert.CommonDtoConvert;
import com.tiangong.hotel.domain.CityAvgPriceEntity;
import com.tiangong.hotel.domain.HotelAvgPriceEntity;
import com.tiangong.hotel.mapper.HotelAvgPriceMapper;
import com.tiangong.hotel.req.HotelAvgPriceReq;
import com.tiangong.hotel.req.HotelHeatReq;
import com.tiangong.hotel.resp.HotelAvgPriceResp;
import com.tiangong.hotel.service.CityAvgPriceService;
import com.tiangong.hotel.service.HotelAvgPriceService;
import com.tiangong.hotel.service.HotelHeatService;
import com.tiangong.keys.RedisKey;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.util.CollUtilX;
import com.tiangong.util.DateUtilX;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * 酒店平均价信息
 */
@Slf4j
@Service
public class HotelAvgPriceServiceImpl extends ServiceImpl<HotelAvgPriceMapper, HotelAvgPriceEntity> implements HotelAvgPriceService {

    @Autowired
    private HotelAvgPriceMapper hotelAvgPriceMapper;

    @Autowired
    private CityAvgPriceService cityAvgPriceService;

    @Autowired
    private HotelHeatService hotelHeatService;

    @Autowired
    private SettingsConstant settingsConstant;

    @Resource(name = "processingCommonExecutor")
    private ThreadPoolTaskExecutor processingCommonExecutor;

    @Override
    @Transactional
    public void batchSaveHotelAvgPrice(List<HotelAvgPriceReq> reqList) {
        if (CollUtilX.isEmpty(reqList)) {
            return;
        }
        // 分布式锁配置
        final int maxRetries = 3;
        final long baseSleep = 1000;
        int retryCount = 0;
        String redisValue = IDUtilS.getId_36();

        while (retryCount < maxRetries) {
            // 尝试获取分布式锁
            Boolean lock = RedisTemplateX.setIfAbsent(RedisKey.HOTEL_AVG_PRICE_LOCK, redisValue, 10L);
            if (Boolean.TRUE.equals(lock)) {
                try {
                    List<HotelAvgPriceEntity> entities = CommonDtoConvert.INSTANCE.HotelAvgPriceListConvert(reqList);
                    this.saveBatch(entities);
                    return; // 成功执行后退出
                } catch (Exception e) {
                    log.error("批量保存酒店平均价异常", e);
                    throw e; // 需要回滚事务，重新抛出异常
                } finally {
                    // 注册事务同步器
                    TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                        @Override
                        public void afterCommit() {
                            // 删除锁
                            RedisTemplateX.execute(RedisKey.HOTEL_AVG_PRICE_LOCK, redisValue);
                        }
                    });
                }
            }
            // 锁获取失败处理
            try {
                Thread.sleep(baseSleep * (1L << retryCount));
                retryCount++;
            } catch (InterruptedException e) {
                log.error("批量保存酒店平均价，锁获取异常", e);
                break;
            }
        }
    }

    @Override
    public void delHotelAvgPrice(HotelAvgPriceReq req) {
        // 分布式锁配置
        final int maxRetries = 3;
        final long baseSleep = 1000;
        int retryCount = 0;
        String redisValue = IDUtilS.getId_36();

        while (retryCount < maxRetries) {
            // 尝试获取分布式锁
            Boolean lock = RedisTemplateX.setIfAbsent(RedisKey.HOTEL_AVG_PRICE_LOCK, redisValue, 10L);
            if (Boolean.TRUE.equals(lock)) {
                try {
                    if (CollUtilX.isNotEmpty(req.getHotelIds())) {
                        LambdaQueryWrapper<HotelAvgPriceEntity> queryWrapper = new LambdaQueryWrapper<>();
                        queryWrapper.in(HotelAvgPriceEntity::getHotelId, req.getHotelIds());
                        hotelAvgPriceMapper.delete(queryWrapper);
                    } else {
                        hotelAvgPriceMapper.deleteAll();
                    }
                    return; // 成功执行后退出
                } catch (Exception e) {
                    log.error("删除酒店平均价异常", e);
                    throw e; // 需要回滚事务，重新抛出异常
                } finally {
                    // 注册事务同步器
                    TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                        @Override
                        public void afterCommit() {
                            // 删除锁
                            RedisTemplateX.execute(RedisKey.HOTEL_AVG_PRICE_LOCK, redisValue);
                        }
                    });
                }
            }
            // 锁获取失败处理
            try {
                Thread.sleep(baseSleep * (1L << retryCount));
                retryCount++;
            } catch (InterruptedException e) {
                log.error("删除酒店平均价，锁获取异常", e);
                break;
            }
        }
    }

    @Override
    public List<String> queryHotelAvgPriceCityCodes() {
        return hotelAvgPriceMapper.selectHotelAvgPriceCityCodes();
    }

    @Override
    public List<HotelAvgPriceResp> queryHotelAvgPriceList(HotelAvgPriceReq req) {
        return hotelAvgPriceMapper.selectHotelAvgPriceList(req);
    }

    @Override
    @Transactional
    public void calculateHotelHeatCityAvgPriceTask(String param) {
        List<String> cityCodes;
        if (StrUtilX.isNotEmpty(param)) {
            cityCodes = StrUtilX.stringToList(param, ",");
        } else {
            if (!RedisTemplateX.hasKey(RedisKey.CALCULATE_CITY_AVG_PRICE_CITY_CODE_KEY)) {
                return;
            }
            cityCodes = RedisTemplateX.setPopCount(RedisKey.CALCULATE_CITY_AVG_PRICE_CITY_CODE_KEY, settingsConstant.getConsumerCalculateCityAvgPriceCount());
        }
        if (CollUtilX.isEmpty(cityCodes)) {
            return;
        }

        try {
            // 当前时间
            Date currentDate = DateUtilX.getCurrentDate();
            // 需要新增的数据
            List<CityAvgPriceEntity> entities = new ArrayList<>();
            // 缓存临时对象
            Map<String, String> redisMap = new HashMap<>();
            for (String cityCode : cityCodes) {
                // 根据城市编码查询酒店平均价列表
                HotelAvgPriceReq hotelAvgPriceReq = new HotelAvgPriceReq();
                hotelAvgPriceReq.setCityCode(cityCode);
                List<HotelAvgPriceResp> respList = this.queryHotelAvgPriceList(hotelAvgPriceReq);
                // 不为空才计算
                if (CollUtilX.isNotEmpty(respList)) {
                    BigDecimal priceSum = BigDecimal.ZERO;
                    for (HotelAvgPriceResp hotelAvgPriceResp : respList) {
                        priceSum = priceSum.add(hotelAvgPriceResp.getAvgPrice());
                    }

                    // 计算平均价
                    BigDecimal avgPrice = priceSum.divide(new BigDecimal(respList.size()), 2, RoundingMode.UP);

                    CityAvgPriceEntity entity = new CityAvgPriceEntity();
                    entity.setCityCode(cityCode);
                    entity.setAvgPrice(avgPrice);
                    entity.setCalculateDate(currentDate);
                    entity.setCreatedBy(Constant.SYSTEM);
                    entity.setCreatedDt(currentDate);
                    entities.add(entity);

                    // 加入缓存
                    redisMap.put(cityCode, String.valueOf(avgPrice));
                }
            }
            // 保存数据
            if (CollUtilX.isNotEmpty(entities)) {
                cityAvgPriceService.saveBatch(entities);
            }
            // 存入缓存
            RedisTemplateX.hPutAll(RedisKey.HOTEL_HEAT_CITY_AVG_PRICE_KEY, redisMap);
        } catch (Exception e) {
            log.error("计算酒店热度城市平均房价任务异常", e);
            // 异常重新插入到缓存
            String[] codes = cityCodes.toArray(new String[0]);
            RedisTemplateX.setAdd(RedisKey.CALCULATE_CITY_AVG_PRICE_CITY_CODE_KEY, codes);
            throw e;// 需要回滚事务，重新抛出异常
        } finally {
            // 判断是否是最后一次执行
            if (StrUtilX.isEmpty(param) && !RedisTemplateX.hasKey(RedisKey.CALCULATE_CITY_AVG_PRICE_CITY_CODE_KEY)) {
                // 异步执行，初始化需要计算酒店热度城市平均房价分数的酒店id到缓存
                CompletableFuture.runAsync(() -> initCalculateCityAvgPriceScoreHotelIdToRedisTask(null), processingCommonExecutor);
            }
        }
    }

    @Override
    public void initCalculateCityAvgPriceScoreHotelIdToRedisTask(String param) {
        if (StrUtilX.isNotEmpty(param)) {
            List<String> hotelIds = StrUtilX.stringToList(param, ",");
            // 处理数据
            processData(hotelIds);
        } else {
            ExecutorService executor = null;
            try {
                // 配置参数
                final int pageSize = 2000;
                final int maxConcurrency = 8;
                final AtomicLong lastId = new AtomicLong(0);
                final RateLimiter rateLimiter = RateLimiter.create(5);
                executor = Executors.newFixedThreadPool(maxConcurrency);

                // 分页循环
                while (true) {
                    rateLimiter.acquire();

                    // 分页查询
                    HotelHeatReq req = new HotelHeatReq();
                    req.setLastId(lastId.get());
                    req.setBatchSize(pageSize);

                    List<String> hotelIds = hotelHeatService.queryAllHotelHeatHotelIdPage(req);

                    if (CollUtilX.isEmpty(hotelIds)) break;

                    // 提交处理
                    executor.submit(() -> processData(hotelIds));

                    // 更新游标
                    updateLastIdAtomically(hotelIds, lastId);
                }

                // 等待完成
                executor.shutdown();
                while (!executor.awaitTermination(1, TimeUnit.MINUTES)) {
                    log.info("等待线程池关闭...");
                }
            } catch (Exception e) {
                log.error("初始化需要计算酒店热度城市平均房价分数的酒店id到缓存任务，处理数据异常", e);
            } finally {
                if (executor != null) executor.shutdownNow();
            }
        }
    }

    /**
     * 更新lastId
     */
    private void updateLastIdAtomically(List<String> hotelIds, AtomicLong lastId) {
        hotelIds.stream()
                .mapToLong(Long::parseLong)
                .max()
                .ifPresent(max -> {
                    long current;
                    do {
                        current = lastId.get();
                    } while (max > current && !lastId.compareAndSet(current, max));
                });
    }

    /**
     * 处理数据
     */
    private void processData(List<String> hotelIdList) {
        try {
            // 1. 批量查询优化
            HotelAvgPriceReq hotelAvgPriceReq = new HotelAvgPriceReq();
            hotelAvgPriceReq.setHotelIds(hotelIdList);
            List<HotelAvgPriceResp> respList = this.queryHotelAvgPriceList(hotelAvgPriceReq);

            // 2. 使用更高效的数据结构
            Set<Long> existingHotelIds = new HashSet<>(respList.size() * 2);
            Map<String, Set<String>> cityHotelMap = new HashMap<>(respList.size() / 2); // 假设城市数约为结果集的1/2

            // 3. 并行流处理加速（需确保线程安全）
            if (CollUtilX.isNotEmpty(respList)) {
                respList.parallelStream().forEach(avgPriceResp -> {
                    String cityCode = avgPriceResp.getCityCode();
                    Long hotelId = avgPriceResp.getHotelId();

                    // 原子操作保证线程安全
                    synchronized (cityHotelMap) {
                        cityHotelMap.computeIfAbsent(cityCode, k -> new HashSet<>())
                                .add(String.valueOf(hotelId));
                    }
                    existingHotelIds.add(hotelId);
                });

                // 4. 批量Redis管道操作
                try (RedisConnection connection = RedisTemplateX.getConnectionFactory()) {
                    connection.openPipeline();

                    // 处理城市维度数据
                    cityHotelMap.forEach((cityCode, hotelIds) -> {
                        byte[] key = (RedisKey.HOTEL_HEAT_CITY_HOTEL_ID_KEY + cityCode).getBytes();
                        byte[][] members = hotelIds.stream()
                                .map(String::getBytes)
                                .toArray(byte[][]::new);
                        connection.sAdd(key, members);
                    });

                    // 处理城市码集合
                    byte[] cityCodeKey = RedisKey.HOTEL_HEAT_CITY_CODE_KEY.getBytes();
                    byte[][] cityCodes = cityHotelMap.keySet()
                            .stream()
                            .map(String::getBytes)
                            .toArray(byte[][]::new);
                    connection.sAdd(cityCodeKey, cityCodes);

                    connection.closePipeline();
                }
            }

            // 5. 使用集合运算优化差集计算
            Set<String> allHotelIds = new HashSet<>(hotelIdList);
            Set<String> missingIds = allHotelIds.stream()
                    .filter(id -> !existingHotelIds.contains(Long.parseLong(id)))
                    .collect(Collectors.toSet());

            // 6. 统一处理缺失数据
            if (!missingIds.isEmpty()) {
                RedisTemplateX.setAdd(
                        RedisKey.CALCULATE_CITY_AVG_PRICE_SCORE_HOTEL_ID_KEY,
                        missingIds.toArray(new String[0])
                );
            }
        } catch (Exception e) {
            log.error("初始化需要计算酒店热度城市平均房价分数的酒店id到缓存任务，处理数据异常 hotelIds={}",
                    hotelIdList.subList(0, Math.min(10, hotelIdList.size())), e);
        }
    }
}