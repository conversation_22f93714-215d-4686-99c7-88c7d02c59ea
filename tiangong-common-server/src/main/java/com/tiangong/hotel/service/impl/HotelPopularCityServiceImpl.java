package com.tiangong.hotel.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.dto.hotel.DestinationReq;
import com.tiangong.dto.hotel.EsCityDto;
import com.tiangong.dto.hotel.HotelPopularCityDTO;
import com.tiangong.dto.hotel.HotelPopularCityVO;
import com.tiangong.enums.ErrorCodeEnum;
import com.tiangong.enums.LanguageTypeEnum;
import com.tiangong.enums.ParamErrorEnum;
import com.tiangong.exception.SysException;
import com.tiangong.hotel.domain.HotelPopularCityEntity;
import com.tiangong.hotel.domain.req.HotelPopularCityReq;
import com.tiangong.hotel.domain.resp.HotelPopularCityResp;
import com.tiangong.hotel.mapper.HotelPopularCityMapper;
import com.tiangong.hotel.service.HotelPopularCityService;
import com.tiangong.hotel.service.HotelService;
import com.tiangong.util.CollUtilX;
import com.tiangong.util.DateUtilX;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 热门城市
 */
@Slf4j
@Service
public class HotelPopularCityServiceImpl extends ServiceImpl<HotelPopularCityMapper, HotelPopularCityEntity> implements HotelPopularCityService {

    @Autowired
    private HotelPopularCityMapper hotelPopularCityMapper;

    @Autowired
    private HotelService hotelService;

    @Override
    public void hotelPopularCityAdd(HotelPopularCityReq req) {
        if (StrUtilX.isEmpty(req.getDestinationId())) {
            throw new SysException(ParamErrorEnum.ADDRESS_BY_ID_NOT_EMPTY);
        }
        Integer i = hotelPopularCityMapper.selectCount(new LambdaQueryWrapper<HotelPopularCityEntity>()
                .eq(HotelPopularCityEntity::getDestinationId, req.getDestinationId()));
        if (i > 0) {
            throw new SysException(ErrorCodeEnum.CITY_ALREADY_EXISTS);
        }
        Date currentDate = DateUtilX.getCurrentDate();

        HotelPopularCityEntity entity = new HotelPopularCityEntity();
        entity.setDestinationId(req.getDestinationId());
        entity.setCreatedBy(req.getOperator());
        entity.setCreatedDt(currentDate);
        entity.setUpdatedBy(req.getOperator());
        entity.setUpdatedDt(currentDate);
        hotelPopularCityMapper.insert(entity);
    }

    @Override
    public void hotelPopularCityDel(HotelPopularCityReq req) {
        if (StrUtilX.isEmpty(req.getDestinationId())) {
            throw new SysException(ParamErrorEnum.ADDRESS_BY_ID_NOT_EMPTY);
        }
        hotelPopularCityMapper.deleteById(req.getDestinationId());
    }

    @Override
    public List<HotelPopularCityResp> hotelPopularCityList(HotelPopularCityReq req) {
        List<HotelPopularCityEntity> entityList = hotelPopularCityMapper.selectList(new LambdaQueryWrapper<>());
        if (CollUtilX.isEmpty(entityList)) {
            return Collections.emptyList();
        }
        List<EsCityDto> esCityDtoList = hotelService.searchDestinationCity(DestinationReq.builder()
                .destinationIds(entityList.stream().map(HotelPopularCityEntity::getDestinationId).collect(Collectors.toSet()))
                .language(req.getLanguage())
                .build());
        if (CollUtilX.isEmpty(esCityDtoList)) {
            return Collections.emptyList();
        }
        return esCityDtoList.stream().map(item -> {
            HotelPopularCityResp resp = new HotelPopularCityResp();
            resp.setDestinationId(item.getDestinationId());
            resp.setCityCode(item.getCityCode());
            resp.setCityName(item.getCityName());
            resp.setProvinceCode(item.getProvinceCode());
            resp.setProvinceName(item.getProvinceName());
            resp.setCountryCode(item.getCountryCode());
            resp.setCountryName(item.getCountryName());
            return resp;
        }).collect(Collectors.toList());
    }

    @Override
    public PaginationSupportDTO<HotelPopularCityResp> hotelPopularCityPage(HotelPopularCityReq req) {
        IPage<HotelPopularCityEntity> page = new Page<>(req.getCurrentPage(), req.getPageSize());
        IPage<HotelPopularCityEntity> iPage = hotelPopularCityMapper.selectPage(page, new LambdaQueryWrapper<>());

        List<EsCityDto> esCityDtoList = hotelService.searchDestinationCity(DestinationReq.builder()
                .destinationIds(iPage.getRecords().stream().map(HotelPopularCityEntity::getDestinationId).collect(Collectors.toSet()))
                .language(req.getLanguage())
                .build());
        List<HotelPopularCityResp> collect = null;
        if (CollUtilX.isNotEmpty(esCityDtoList)) {
            collect = esCityDtoList.stream().map(item -> {
                HotelPopularCityResp resp = new HotelPopularCityResp();
                resp.setDestinationId(item.getDestinationId());
                resp.setCityCode(item.getCityCode());
                resp.setCityName(item.getCityName());
                resp.setProvinceCode(item.getProvinceCode());
                resp.setProvinceName(item.getProvinceName());
                resp.setCountryCode(item.getCountryCode());
                resp.setCountryName(item.getCountryName());
                return resp;
            }).collect(Collectors.toList());
        }

        PaginationSupportDTO<HotelPopularCityResp> paginationSupportDTO = new PaginationSupportDTO<>();
        paginationSupportDTO.setItemList(collect);
        paginationSupportDTO.setCurrentPage((int) page.getCurrent());
        paginationSupportDTO.setPageSize((int) page.getSize());
        paginationSupportDTO.setTotalCount(page.getTotal());
        paginationSupportDTO.setTotalPage((int) page.getPages());
        return paginationSupportDTO;
    }

    @Override
    public List<HotelPopularCityDTO> queryHotelPopularCityList(HotelPopularCityVO vo) {
        // 默认中文
        if (StrUtilX.isEmpty(vo.getLanguage())) {
            vo.setLanguage(LanguageTypeEnum.zh_CN.getValue());
        }
        List<HotelPopularCityEntity> entityList = hotelPopularCityMapper.selectList(new LambdaQueryWrapper<>());
        if (CollUtilX.isEmpty(entityList)) {
            return Collections.emptyList();
        }
        List<EsCityDto> esCityDtoList = hotelService.searchDestinationCity(DestinationReq.builder()
                .destinationIds(entityList.stream().map(HotelPopularCityEntity::getDestinationId).collect(Collectors.toSet()))
                .language(vo.getLanguage())
                .build());
        if (CollUtilX.isEmpty(esCityDtoList)) {
            return Collections.emptyList();
        }
        return esCityDtoList.stream().map(item -> {
            HotelPopularCityDTO resp = new HotelPopularCityDTO();
            resp.setDestinationId(item.getDestinationId());
            resp.setCityCode(item.getCityCode());
            resp.setCityName(item.getCityName());
            resp.setProvinceCode(item.getProvinceCode());
            resp.setProvinceName(item.getProvinceName());
            resp.setCountryCode(item.getCountryCode());
            resp.setCountryName(item.getCountryName());
            return resp;
        }).collect(Collectors.toList());
    }
}