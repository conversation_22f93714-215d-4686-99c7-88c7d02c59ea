package com.tiangong.hotel.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tiangong.common.Response;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.dto.hotel.*;
import com.tiangong.enums.ErrorCodeEnum;
import com.tiangong.enums.ImportAutoUpdateLowestPriceCityExcelTitleEnum;
import com.tiangong.enums.ParamErrorEnum;
import com.tiangong.exception.SysException;
import com.tiangong.hotel.domain.AutoUpdateLowestPriceCityEntity;
import com.tiangong.hotel.domain.req.ImportAutoUpdateLowestPriceCityExportReq;
import com.tiangong.hotel.domain.req.ImportAutoUpdateLowestPriceCityReq;
import com.tiangong.hotel.domain.resp.AutoUpdateLowestPriceCityResp;
import com.tiangong.hotel.domain.resp.ImportAutoUpdateLowestPriceCityResp;
import com.tiangong.hotel.domain.vo.AutoUpdateLowestPriceCityVo;
import com.tiangong.hotel.mapper.AutoUpdateLowestPriceCityMapper;
import com.tiangong.hotel.service.AutoUpdateLowestPriceCityService;
import com.tiangong.hotel.service.HotelAvailableService;
import com.tiangong.hotel.service.HotelService;
import com.tiangong.keys.RedisKey;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.util.CollUtilX;
import com.tiangong.util.DateUtilX;
import com.tiangong.util.FileUpUtil;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 主动更新起价城市
 */
@Slf4j
@Service
public class AutoUpdateLowestPriceCityServiceImpl extends ServiceImpl<AutoUpdateLowestPriceCityMapper, AutoUpdateLowestPriceCityEntity> implements AutoUpdateLowestPriceCityService {

    @Autowired
    private AutoUpdateLowestPriceCityMapper autoUpdateLowestPriceCityMapper;

    @Autowired
    private HotelAvailableService hotelAvailableService;

    @Autowired
    private HotelService hotelService;

    @Autowired
    private FileUpUtil fileUpUtil;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void autoUpdateLowestPriceCityAdd(AutoUpdateLowestPriceCityVo vo) {
        if (CollUtilX.isEmpty(vo.getIds())) {
            throw new SysException(ParamErrorEnum.ADDRESS_BY_ID_NOT_EMPTY);
        }
        if (vo.getHotelCount() != null &&
                vo.getHotelCount().compareTo(10) != 0 &&
                vo.getHotelCount().compareTo(20) != 0 &&
                vo.getHotelCount().compareTo(30) != 0 &&
                vo.getHotelCount().compareTo(40) != 0 &&
                vo.getHotelCount().compareTo(50) != 0) {
            throw new SysException(ErrorCodeEnum.THE_ENTERED_NUMBER_OF_CITIES_IS_INVALID_PLEASE_INPUT_AGAIN);
        }
        // 查询是否有重复的城市
        List<AutoUpdateLowestPriceCityEntity> entities = autoUpdateLowestPriceCityMapper.selectList(new LambdaQueryWrapper<AutoUpdateLowestPriceCityEntity>()
                .in(AutoUpdateLowestPriceCityEntity::getDestinationId, vo.getIds())
                .eq(AutoUpdateLowestPriceCityEntity::getDeleted, 0));
        if (CollUtil.isNotEmpty(entities)) {
            throw new SysException(ErrorCodeEnum.CITY_ALREADY_EXISTS.errorCode, ErrorCodeEnum.CITY_ALREADY_EXISTS.errorDesc);
        }

        List<EsCityDto> dtoList = hotelService.searchDestinationCity(DestinationReq.builder().destinationIds(vo.getIds()).language(vo.getLanguage()).build());
        if (CollUtilX.isNotEmpty(dtoList)) {
            Date currentDate = DateUtilX.getCurrentDate();
            List<AutoUpdateLowestPriceCityEntity> entityList = dtoList.stream().map(item -> {
                AutoUpdateLowestPriceCityEntity entity = new AutoUpdateLowestPriceCityEntity();
                entity.setDestinationId(item.getDestinationId());
                entity.setCityName(item.getCityName());
                entity.setHotelCount(vo.getHotelCount());
                entity.setDeleted(0);
                entity.setCreatedDt(currentDate);
                entity.setCreatedBy(vo.getOperator());
                return entity;
            }).collect(Collectors.toList());
            this.saveBatch(entityList, entityList.size());
        }
    }

    @Override
    public void autoUpdateLowestPriceCityEdit(AutoUpdateLowestPriceCityVo vo) {
        if (vo.getId() == null) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_ID);
        }
        if (vo.getHotelCount() != null &&
                vo.getHotelCount().compareTo(10) != 0 &&
                vo.getHotelCount().compareTo(20) != 0 &&
                vo.getHotelCount().compareTo(30) != 0 &&
                vo.getHotelCount().compareTo(40) != 0 &&
                vo.getHotelCount().compareTo(50) != 0) {
            throw new SysException(ErrorCodeEnum.THE_ENTERED_NUMBER_OF_CITIES_IS_INVALID_PLEASE_INPUT_AGAIN);
        }
        AutoUpdateLowestPriceCityEntity entity = new AutoUpdateLowestPriceCityEntity();
        entity.setId(vo.getId());
        entity.setDestinationId(vo.getDestinationId());
        entity.setCityName(vo.getCityName());
        entity.setHotelCount(vo.getHotelCount());
        entity.setUpdatedBy(vo.getOperator());
        entity.setUpdatedDt(new Date());
        autoUpdateLowestPriceCityMapper.updateById(entity);
    }

    @Override
    public void autoUpdateLowestPriceCityDel(AutoUpdateLowestPriceCityVo vo) {
        if (vo.getId() == null) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_ID);
        }
        this.removeById(vo.getId());
    }

    @Override
    public PaginationSupportDTO<AutoUpdateLowestPriceCityResp> autoUpdateLowestPriceCityPage(AutoUpdateLowestPriceCityVo vo) {
        PaginationSupportDTO<AutoUpdateLowestPriceCityResp> paginationSupportDTO = new PaginationSupportDTO<>();
        Page<AutoUpdateLowestPriceCityEntity> page = new Page<>(vo.getCurrentPage(), vo.getPageSize());
        IPage<AutoUpdateLowestPriceCityResp> iPage = autoUpdateLowestPriceCityMapper.autoUpdateLowestPriceCityPage(page, vo);
        return paginationSupportDTO.getPaginationSupportDTO(iPage);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response<Object> importAutoUpdateLowestPriceCity(MultipartFile file, String language, String operator) {
        if (StrUtilX.isEmpty(language)) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_LANGUAGETYPE);
        }
        Response<Object> response;
        // 解析文件
        InputStream in = null;
        try {
            List<ImportAutoUpdateLowestPriceCityReq> dataList;
            ImportAutoUpdateLowestPriceCityResp uploadRespVO = new ImportAutoUpdateLowestPriceCityResp();
            if (StrUtilX.isEmpty(file.getOriginalFilename())) {
                throw new SysException(ErrorCodeEnum.FILE_FORMAT_ERROR);
            }
            // 获取文件名称后缀
            String suffix = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf(".") + 1);

            if (!"xlsx".contains(suffix.toLowerCase())) {
                throw new SysException(ErrorCodeEnum.FILE_FORMAT_ERROR);
            }

            in = file.getInputStream();
            Workbook wb = WorkbookFactory.create(in);
            Sheet sheet = wb.getSheetAt(0);

            int rowSize = sheet.getPhysicalNumberOfRows();
            if (rowSize > 201) {
                throw new SysException(ErrorCodeEnum.IMPORT_MAX_200);
            }

            // 校验文件格式
            boolean result = checkExcelHeaders(sheet);
            if (!result) {
                throw new SysException(ErrorCodeEnum.IMPORT_FILE_FORMAT_ERROR);
            }

            // 解析excel内容
            dataList = analyzeExcel(sheet);

            if (CollectionUtil.isNotEmpty(dataList)) {
                Integer successCount = 0;// 成功数量
                Integer repeatCount = 0;// 重复数量
                List<ImportAutoUpdateLowestPriceCityExportReq> failDataList = new ArrayList<>();
                // 旧数据
                Set<String> oldDestinationIdSet = new HashSet<>();
                Set<String> dbDestinationIdSet = dataList.stream().map(ImportAutoUpdateLowestPriceCityReq::getDestinationId).collect(Collectors.toSet());
                List<AutoUpdateLowestPriceCityEntity> entities = autoUpdateLowestPriceCityMapper.selectList(new LambdaQueryWrapper<AutoUpdateLowestPriceCityEntity>()
                        .in(AutoUpdateLowestPriceCityEntity::getDestinationId, dbDestinationIdSet)
                        .eq(AutoUpdateLowestPriceCityEntity::getDeleted, 0));
                if (CollUtilX.isNotEmpty(entities)) {
                    for (AutoUpdateLowestPriceCityEntity entity : entities) {
                        oldDestinationIdSet.add(entity.getDestinationId());
                    }
                }

                // 查询城市信息
                List<EsCityDto> dtoList = hotelService.searchDestinationCity(DestinationReq.builder().destinationIds(dbDestinationIdSet).language(language).build());
                Map<String, String> cityMap = new HashMap<>();
                if (CollUtilX.isNotEmpty(dtoList)) {
                    dtoList.forEach(item -> cityMap.put(item.getDestinationId(), item.getCityName()));
                }

                List<AutoUpdateLowestPriceCityEntity> poList = new ArrayList<>();
                Date currentDate = DateUtilX.getCurrentDate();
                for (ImportAutoUpdateLowestPriceCityReq importReq : dataList) {
                    try {
                        // 校验导入主动更新起价城市数据
                        checkImportAutoUpdateLowestPriceCityParam(importReq);
                        // 校验配置是否存在
                        if (oldDestinationIdSet.contains(importReq.getDestinationId())) {
                            throw new SysException(ErrorCodeEnum.CITY_ALREADY_EXISTS);
                        }
                        String cityName = cityMap.get(importReq.getDestinationId());
                        if (StrUtilX.isEmpty(cityName)) {
                            throw new SysException(ErrorCodeEnum.CITY_IS_NOT_ALREADY);
                        }
                        // 添加到旧数据
                        oldDestinationIdSet.add(importReq.getDestinationId());

                        AutoUpdateLowestPriceCityEntity entity = new AutoUpdateLowestPriceCityEntity();
                        entity.setDestinationId(importReq.getDestinationId());
                        entity.setHotelCount(importReq.getHotelCount());
                        entity.setCityName(cityName);
                        entity.setDeleted(0);
                        entity.setCreatedBy(operator);
                        entity.setCreatedDt(currentDate);
                        poList.add(entity);
                        successCount++;
                    } catch (DuplicateKeyException de) {
                        // 重复失败
                        repeatCount++;
                    } catch (SysException se) {
                        ImportAutoUpdateLowestPriceCityExportReq exportReq = new ImportAutoUpdateLowestPriceCityExportReq();
                        exportReq.setDestinationId(importReq.getDestinationId());
                        exportReq.setHotelCount(importReq.getHotelCount());
                        exportReq.setErrorMsg(se.getMessage());
                        failDataList.add(exportReq);
                    } catch (Exception e) {
                        ImportAutoUpdateLowestPriceCityExportReq exportReq = new ImportAutoUpdateLowestPriceCityExportReq();
                        exportReq.setDestinationId(importReq.getDestinationId());
                        exportReq.setHotelCount(importReq.getHotelCount());
                        if (e instanceof ConstraintViolationException) {
                            ConstraintViolationException constraintViolationException = (ConstraintViolationException) e;
                            ConstraintViolation<?> constraintViolation = constraintViolationException.getConstraintViolations().iterator().next();
                            exportReq.setErrorMsg(constraintViolation.getMessage());
                        } else {
                            exportReq.setErrorMsg(e.getMessage());
                        }
                        failDataList.add(exportReq);
                    }
                }
                // 新增数据
                if (CollUtilX.isNotEmpty(poList)) {
                    this.saveBatch(poList);
                }
                // 失败文件处理
                if (CollectionUtil.isNotEmpty(failDataList)) {
                    // 1. list转为excel文件
                    // 创建 ExcelWriter
                    ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                    ExcelWriter excelWriter = EasyExcel.write(outputStream, ImportAutoUpdateLowestPriceCityExportReq.class).build();
                    WriteSheet writeSheet = EasyExcel.writerSheet("Sheel1").build();

                    // 写入数据
                    excelWriter.write(failDataList, writeSheet);
                    excelWriter.finish(); // 完成写入操作
                    // 转换为 MultipartFile
                    MultipartFile multipartFile = convert(outputStream, "导入主动更新起价城市数据失败.xlsx");
                    String fileUrl = fileUpUtil.uploadFile2(multipartFile, "导入主动更新起价城市数据失败.xlsx");
                    // 2. 上传文件到阿里云
                    uploadRespVO.setFailedDataFileUrl(fileUrl);
                }
                uploadRespVO.setFailedCount(String.valueOf(failDataList.size()));
                uploadRespVO.setRepeatCount(String.valueOf(repeatCount));
                uploadRespVO.setSuccessCount(String.valueOf(successCount));
                // 部分成功部分失败
                response = Response.success(uploadRespVO);
            } else {
                throw new SysException(ParamErrorEnum.EMPTY_PARAM_CITYID);
            }
        } catch (SysException e) {
            log.error("导入主动更新起价城市数据失败", e);
            response = Response.error(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("导入主动更新起价城市数据异常", e);
            response = Response.error(ErrorCodeEnum.SYSTEM_EXCEPTION.errorCode, ErrorCodeEnum.SYSTEM_EXCEPTION.errorDesc);
        } finally {
            if (in != null) {
                try {
                    in.close();
                } catch (IOException e) {
                    log.error("导入主动更新起价城市数据异常", e);
                }
            }
        }
        return response;
    }

    /**
     * 解析excel内容
     */
    public List<ImportAutoUpdateLowestPriceCityReq> analyzeExcel(Sheet sheet) {
        List<ImportAutoUpdateLowestPriceCityReq> list = new ArrayList<>();
        int rowSize = sheet.getPhysicalNumberOfRows();
        for (int j = 2; j < rowSize; j++) {// 遍历每一行
            ImportAutoUpdateLowestPriceCityReq req = new ImportAutoUpdateLowestPriceCityReq();
            Row row = sheet.getRow(j);
            if (row == null) {
                continue;
            }
            for (int k = 0; k < 2; k++) {
                Cell cell = null;
                try {
                    cell = row.getCell(k);
                    switch (k) {
                        case 0:
                            if (cell != null) {
                                DataFormatter formatter = new DataFormatter();
                                String value = formatter.formatCellValue(cell).trim();
                                req.setDestinationId(value);
                            }
                            break;
                        case 1:
                            if (cell != null) {
                                DataFormatter formatter = new DataFormatter();
                                String value = formatter.formatCellValue(cell).trim();
                                if (StrUtilX.isNotEmpty(value)) {
                                    req.setHotelCount(Integer.parseInt(value));
                                }
                            }
                            break;
                    }
                } catch (Exception e) {
                    log.error("批量导单，解析excel第{}行，【{}】列出现异常，cell={}", j, k, JSONUtil.toJsonStr(cell), e);
                }
            }
            list.add(req);
        }
        return list;
    }

    /**
     * ByteArrayOutputStream 转换成 MultipartFile
     */
    public static MultipartFile convert(ByteArrayOutputStream byteArrayOutputStream, String fileName) {
        // 获取字节数组
        byte[] bytes = byteArrayOutputStream.toByteArray();

        // 创建 MockMultipartFile
        return new MockMultipartFile("file", fileName, "application/octet-stream", bytes);
    }

    /**
     * 检查Excel文件表头信息
     *
     * @param sheet 表头
     * @return 状态
     */
    private boolean checkExcelHeaders(Sheet sheet) {
        try {
            // 获取 excel 第一行数据（表头）
            Row row = sheet.getRow(1);
            // 算下有多少列
            int colCount = sheet.getRow(1).getLastCellNum();
            if (colCount != 2) {
                return false;
            }
            for (int j = 0; j < colCount; j++) {
                Cell cell = row.getCell(j);
                String headName = cell.getStringCellValue().trim();
                if (ImportAutoUpdateLowestPriceCityExcelTitleEnum.getKeyByValue(headName) != j) {
                    return false;
                }
            }
            return true;
        } catch (Exception e) {
            log.error("检查Excel文件表头信息异常", e);
            return false;
        }
    }

    /**
     * 校验导入参数
     */
    private void checkImportAutoUpdateLowestPriceCityParam(ImportAutoUpdateLowestPriceCityReq req) {
        if (StrUtilX.isEmpty(req.getDestinationId())) {
            throw new SysException(ParamErrorEnum.ADDRESS_BY_ID_NOT_EMPTY);
        }
        if (req.getHotelCount() == null) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_CITYRUNABLEHOTELCOUNT);
        }
        if (req.getHotelCount().compareTo(10) != 0 &&
                req.getHotelCount().compareTo(20) != 0 &&
                req.getHotelCount().compareTo(30) != 0 &&
                req.getHotelCount().compareTo(40) != 0 &&
                req.getHotelCount().compareTo(50) != 0) {
            throw new SysException(ErrorCodeEnum.THE_ENTERED_NUMBER_OF_CITIES_IS_INVALID_PLEASE_INPUT_AGAIN);
        }
    }

    @Override
    public void initCalculateCityAvgPriceScoreHotelIdToRedisTask(String param) {
        Map<Object, Object> configMap = RedisTemplateX.hGetAll(RedisKey.AUTO_UPDATE_LOWEST_PRICE_CITY_CONFIG_KEY);
        if (configMap.isEmpty()) {
            return;
        }
        Date currentDate = DateUtilX.getCurrentDate();
        // 凌晨0点到凌晨5点59之间不执行
        if (isWithinNightHours()) {
            return;
        }
        for (Map.Entry<Object, Object> entry : configMap.entrySet()) {
            try {
                String updateTime = RedisTemplateX.get(RedisKey.AUTO_UPDATE_LOWEST_PRICE_CITY_LAST_UPDATE_TIME_KEY + entry.getKey());
                if (StrUtilX.isEmpty(updateTime)) {
                    // 处理客户酒店
                    handleAgentHotel((String) entry.getKey());
                    // 更新执行时间
                    RedisTemplateX.set(RedisKey.AUTO_UPDATE_LOWEST_PRICE_CITY_LAST_UPDATE_TIME_KEY + entry.getKey(), DateUtilX.dateToString(currentDate, DateUtilX.hour_format));
                } else {
                    // 判断是否到执行时间
                    Date convetedDate = DateUtilX.getDate(DateUtilX.stringToDate(updateTime, DateUtilX.hour_format), 0, Integer.parseInt((String) entry.getValue()));
                    if (DateUtilX.compare(convetedDate, currentDate) <= 0) {
                        // 处理客户酒店
                        handleAgentHotel((String) entry.getKey());
                        // 更新执行时间
                        RedisTemplateX.set(RedisKey.AUTO_UPDATE_LOWEST_PRICE_CITY_LAST_UPDATE_TIME_KEY + entry.getKey(), DateUtilX.dateToString(currentDate, DateUtilX.hour_format));
                    }
                }
            } catch (Exception e) {
                log.error("初始化主动更新起价城市下酒店id到缓存任务异常，agentCode={}", entry.getKey(), e);
            }
        }
    }

    /**
     * 判断是否在晚上
     */
    public static boolean isWithinNightHours() {
        LocalTime now = LocalTime.now();
        LocalTime start = LocalTime.of(0, 0);   // 00:00
        LocalTime end = LocalTime.of(5, 59);     // 05:59
        return !now.isBefore(start) && now.isBefore(end);
    }

    /**
     * 处理客户酒店
     */
    private void handleAgentHotel(String agentCode) {
        List<AutoUpdateLowestPriceCityEntity> entityList = autoUpdateLowestPriceCityMapper.selectList(new LambdaQueryWrapper<AutoUpdateLowestPriceCityEntity>()
                .eq(AutoUpdateLowestPriceCityEntity::getDeleted, 0));
        if (CollUtil.isEmpty(entityList)) {
            return;
        }
        // 获取客户酒店黑名单
        AddHotelAvailableReq req = new AddHotelAvailableReq();
        req.setAgentCode(agentCode);
        List<Long> blacklistHotelIds = hotelAvailableService.queryAgentBlacklistHotelIds(req);

        for (AutoUpdateLowestPriceCityEntity entity : entityList) {
            try {
                HotelPageReq hotelPageReq = new HotelPageReq();
                hotelPageReq.setCurrentPage(1L);
                if (entity.getHotelCount() != null) {
                    hotelPageReq.setPageSize(Long.valueOf(entity.getHotelCount()));
                } else {
                    hotelPageReq.setPageSize(20L);
                }
                hotelPageReq.setAgentCode(agentCode);
                hotelPageReq.setDestinationId(entity.getDestinationId());
                hotelPageReq.setCheckInDate(DateUtilX.dateToString(DateUtilX.getCurrentDate()));
                hotelPageReq.setCheckOutDate(DateUtilX.dateToString(DateUtilX.getAddDay(DateUtilX.getCurrentDate(), 1)));
                PaginationSupportDTO<HotelListResp> hotelDto = hotelService.findHotelList(hotelPageReq);
                if (CollUtilX.isNotEmpty(hotelDto.getItemList())) {
                    String[] hotelIds = hotelDto.getItemList().stream()
                            .filter(item -> CollUtilX.isEmpty(blacklistHotelIds) || !blacklistHotelIds.contains(item.getHotelId()))
                            .map(item -> String.valueOf(item.getHotelId())).distinct().toArray(String[]::new);
                    RedisTemplateX.lLeftPushAll(RedisKey.AUTO_UPDATE_LOWEST_PRICE_CITY_HOTEL_ID_KEY + agentCode, hotelIds);
                }
                // 休眠1秒
                Thread.sleep(1000L);
            } catch (Exception e) {
                log.error("根据目的地获取酒店列表异常", e);
            }
        }
    }
}
