package com.tiangong.hotel.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.tiangong.convert.CommonDtoConvert;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.dto.hotel.AgentRecommendScoreReq;
import com.tiangong.dto.hotel.RecommendScoreReq;
import com.tiangong.dto.hotel.UpdateHotelScoreReq;
import com.tiangong.enums.ErrorCodeEnum;
import com.tiangong.enums.ParamErrorEnum;
import com.tiangong.exception.SysException;
import com.tiangong.hotel.domain.HotelLabelConfigEntity;
import com.tiangong.hotel.domain.HotelLabelConfigLogEntity;
import com.tiangong.hotel.domain.HotelRecommendLabelRelEntity;
import com.tiangong.hotel.mapper.HotelLabelConfigLogMapper;
import com.tiangong.hotel.mapper.HotelLabelConfigMapper;
import com.tiangong.hotel.mapper.HotelRecommendLabelRelMapper;
import com.tiangong.hotel.mapper.HotelRecommendMapper;
import com.tiangong.hotel.req.HotelLabelConfigReq;
import com.tiangong.hotel.req.HotelRecommendReq;
import com.tiangong.hotel.resp.HotelLabelConfigLogResp;
import com.tiangong.hotel.resp.HotelLabelConfigResp;
import com.tiangong.hotel.resp.HotelRecommendResp;
import com.tiangong.hotel.server.HotelServer;
import com.tiangong.hotel.service.HotelLabelConfigService;
import com.tiangong.util.CollUtilX;
import com.tiangong.util.DateUtilX;
import com.tiangong.util.StrUtilX;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Service
public class HotelLabelConfigServiceImpl extends ServiceImpl<HotelLabelConfigMapper, HotelLabelConfigEntity> implements HotelLabelConfigService {

    @Autowired
    private HotelLabelConfigMapper hotelLabelConfigMapper;

    @Autowired
    private HotelRecommendLabelRelMapper hotelRecommendLabelRelMapper;

    @Autowired
    private HotelLabelConfigLogMapper hotelLabelConfigLogMapper;

    @Autowired
    private HotelServer hotelServer;

    @Autowired
    private HotelRecommendMapper hotelRecommendMapper;

    @Resource(name = "processingCommonExecutor")
    private ThreadPoolTaskExecutor processingCommonExecutor;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void hotelLabelConfigAdd(HotelLabelConfigReq req) {
        if (StrUtilX.isEmpty(req.getLabelName())) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_LABELNAME);
        }
        if (req.getRecommendScore() == null) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_RECOMMENDSCORE);
        }
        // 校验名称是否已存在
        Integer count = hotelLabelConfigMapper.selectCount(new QueryWrapper<HotelLabelConfigEntity>()
                .eq("deleted", 0).eq("label_name", req.getLabelName()));
        if (count > 0) {
            throw new SysException(ErrorCodeEnum.HOTEL_LABEL_NAME_EXIST);
        }

        // 获取最大id
        String maxIdStr = null;
        Integer maxId = hotelLabelConfigMapper.selectHotelLabelConfigMaxId();
        if (maxId == null) {
            maxIdStr = "01";
        } else if (maxId < 9) {
            maxIdStr = "0" + (maxId + 1);
        } else {
            maxIdStr = String.valueOf(maxId + 1);
        }

        Date currentDate = DateUtilX.getCurrentDate();
        HotelLabelConfigEntity entity = CommonDtoConvert.INSTANCE.hotelLabelConfigEntityConvert(req);
        entity.setLabelCode(maxIdStr);
        entity.setDeleted(0);
        entity.setCreatedDt(currentDate);
        hotelLabelConfigMapper.insert(entity);

        // 新增日志
        HotelLabelConfigLogEntity logEntity = new HotelLabelConfigLogEntity();
        logEntity.setHotelLabelConfigId(entity.getHotelLabelConfigId());
        logEntity.setContent("新增标签名称：" + req.getLabelName() + "，标签分值：" + req.getRecommendScore());
        logEntity.setCreatedBy(req.getCreatedBy());
        logEntity.setCreatedDt(currentDate);
        hotelLabelConfigLogMapper.insert(logEntity);

        // 注册事务同步器
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                // 事务提交后，异步执行，同步推荐分到mongodb
                CompletableFuture.runAsync(() -> syncHotelRecommendToMongodb(entity.getHotelLabelConfigId()),processingCommonExecutor);
            }
        });
    }

    /**
     * 同步推荐分到mongodb
     */
    private void syncHotelRecommendToMongodb(Integer hotelLabelConfigId) {
        try {
            // 查询酒店推荐总分
            HotelRecommendReq hotelRecommendReq = new HotelRecommendReq();
            hotelRecommendReq.setHotelLabelConfigId(hotelLabelConfigId);
            List<HotelRecommendResp> respList = hotelRecommendMapper.selectHotelRecommendScoreList(hotelRecommendReq);
            // 判断数据是否为空
            if (CollUtilX.isNotEmpty(respList)) {
                List<RecommendScoreReq> recommendScoreReqList = new ArrayList<>();
                Map<Long, List<HotelRecommendResp>> hotelRecommendMap = respList.stream().collect(Collectors.groupingBy(HotelRecommendResp::getHotelId));
                for (Map.Entry<Long, List<HotelRecommendResp>> entry : hotelRecommendMap.entrySet()) {
                    RecommendScoreReq recommendScoreReq = new RecommendScoreReq();
                    recommendScoreReq.setHotelId(entry.getKey());
                    List<AgentRecommendScoreReq> reqList = entry.getValue().stream().map(item -> {
                        AgentRecommendScoreReq agentRecommendScoreReq = new AgentRecommendScoreReq();
                        agentRecommendScoreReq.setAgentCode(item.getAgentCode());
                        agentRecommendScoreReq.setRecommendScore(Double.valueOf(item.getSumRecommendScore()));
                        return agentRecommendScoreReq;
                    }).collect(Collectors.toList());
                    recommendScoreReq.setAgentRecommendScoreReqList(reqList);
                    recommendScoreReqList.add(recommendScoreReq);
                }

                // 更新mongodb公共表里面的数据
                UpdateHotelScoreReq updateHotelScoreReq = new UpdateHotelScoreReq();
                updateHotelScoreReq.setRecommendScoreReqList(recommendScoreReqList);
                hotelServer.updateHotelScoreToMongodbBatch(updateHotelScoreReq);
            }
        } catch (Exception e) {
            log.error("同步推荐分到mongodb", e);
        }
    }

    @Override
    public PaginationSupportDTO<HotelLabelConfigResp> hotelLabelConfigPage(HotelLabelConfigReq req) {
        PageHelper.startPage(req.getCurrentPage(), req.getPageSize());
        List<HotelLabelConfigResp> hotelLabelConfigList = hotelLabelConfigMapper.hotelLabelConfigList(req);

        PageInfo<HotelLabelConfigResp> pageInfo = new PageInfo<>(hotelLabelConfigList);
        PaginationSupportDTO<HotelLabelConfigResp> paginationSupportDTO = new PaginationSupportDTO<>();
        paginationSupportDTO.copyProperties(pageInfo, HotelLabelConfigResp.class);
        return paginationSupportDTO;
    }

    @Override
    public List<HotelLabelConfigResp> hotelLabelConfigList(HotelLabelConfigReq req) {
        return hotelLabelConfigMapper.hotelLabelConfigList(req);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void hotelLabelConfigEdit(HotelLabelConfigReq req) {
        if (req.getHotelLabelConfigId() == null) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_ID);
        }
        if (StrUtilX.isEmpty(req.getLabelName())) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_LABELNAME);
        }
        if (req.getRecommendScore() == null) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_RECOMMENDSCORE);
        }
        // 校验名称是否已存在
        Integer count = hotelLabelConfigMapper.selectCount(new QueryWrapper<HotelLabelConfigEntity>()
                .eq("deleted", 0).eq("label_name", req.getLabelName()).notIn("hotel_label_config_id", req.getHotelLabelConfigId()));
        if (count > 0) {
            throw new SysException(ErrorCodeEnum.HOTEL_LABEL_NAME_EXIST);
        }
        Date currentDate = DateUtilX.getCurrentDate();
        HotelLabelConfigEntity entity = new HotelLabelConfigEntity();
        entity.setHotelLabelConfigId(req.getHotelLabelConfigId());
        entity.setLabelName(req.getLabelName());
        entity.setRecommendScore(req.getRecommendScore());
        entity.setUpdatedDt(currentDate);
        entity.setUpdatedBy(req.getUpdatedBy());
        hotelLabelConfigMapper.updateById(entity);

        // 新增日志
        HotelLabelConfigLogEntity logEntity = new HotelLabelConfigLogEntity();
        logEntity.setHotelLabelConfigId(entity.getHotelLabelConfigId());
        logEntity.setContent("修改标签名称：" + req.getLabelName() + "，标签分值：" + req.getRecommendScore());
        logEntity.setCreatedBy(req.getUpdatedBy());
        logEntity.setCreatedDt(currentDate);
        hotelLabelConfigLogMapper.insert(logEntity);

        // 注册事务同步器
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                // 事务提交后，异步执行，同步推荐分到mongodb
                CompletableFuture.runAsync(() -> syncHotelRecommendToMongodb(entity.getHotelLabelConfigId()),processingCommonExecutor);
            }
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void hotelLabelConfigDel(HotelLabelConfigReq req) {
        if (req.getHotelLabelConfigId() == null) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_ID);
        }
        // 校验是否已经被使用
        Integer count = hotelRecommendLabelRelMapper.selectCount(new QueryWrapper<HotelRecommendLabelRelEntity>().eq("hotel_label_config_id", req.getHotelLabelConfigId()));
        if (count > 0) {
            throw new SysException(ErrorCodeEnum.THE_LABEL_IS_IN_USE);
        }
        HotelLabelConfigEntity entity = new HotelLabelConfigEntity();
        entity.setHotelLabelConfigId(req.getHotelLabelConfigId());
        entity.setDeleted(1);
        entity.setUpdatedDt(new Date());
        entity.setUpdatedBy(req.getUpdatedBy());
        hotelLabelConfigMapper.updateById(entity);

        // 注册事务同步器
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                // 事务提交后，异步执行，同步推荐分到mongodb
                CompletableFuture.runAsync(() -> syncHotelRecommendToMongodb(entity.getHotelLabelConfigId()),processingCommonExecutor);
            }
        });
    }

    @Override
    public int checkHotelLabelConfig(HotelLabelConfigReq req) {
        if (req.getHotelLabelConfigId() == null) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_ID);
        }
        return hotelRecommendLabelRelMapper.selectCount(new QueryWrapper<HotelRecommendLabelRelEntity>().eq("hotel_label_config_id", req.getHotelLabelConfigId()));
    }

    @Override
    public PaginationSupportDTO<HotelLabelConfigLogResp> hotelLabelConfigLogPage(HotelLabelConfigReq req) {
        if (req.getHotelLabelConfigId() == null) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_ID);
        }
        PageHelper.startPage(req.getCurrentPage(), req.getPageSize());
        List<HotelLabelConfigLogResp> hotelLabelConfigLogList = hotelLabelConfigLogMapper.hotelLabelConfigLogList(req.getHotelLabelConfigId());

        PageInfo<HotelLabelConfigLogResp> pageInfo = new PageInfo<>(hotelLabelConfigLogList);
        PaginationSupportDTO<HotelLabelConfigLogResp> paginationSupportDTO = new PaginationSupportDTO<>();
        paginationSupportDTO.copyProperties(pageInfo, HotelLabelConfigLogResp.class);
        return paginationSupportDTO;
    }

    @Override
    public List<HotelLabelConfigEntity> selectAllHotelLabel() {
        LambdaQueryWrapper<HotelLabelConfigEntity> queryWrapper = new LambdaQueryWrapper<>(new HotelLabelConfigEntity());
        queryWrapper.select(HotelLabelConfigEntity::getLabelName,HotelLabelConfigEntity::getHotelLabelConfigId);
        return hotelLabelConfigMapper.selectList(queryWrapper);
    }
}
