package com.tiangong.hotel.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tiangong.enums.ParamErrorEnum;
import com.tiangong.exception.SysException;
import com.tiangong.hotel.domain.AutoUpdateLowestPriceConfigEntity;
import com.tiangong.hotel.domain.vo.AutoUpdateLowestPriceConfigVo;
import com.tiangong.hotel.mapper.AutoUpdateLowestPriceConfigMapper;
import com.tiangong.hotel.service.AutoUpdateLowestPriceConfigService;
import com.tiangong.keys.RedisKey;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.util.CollUtilX;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 主动更新起价配置
 */
@Slf4j
@Service
public class AutoUpdateLowestPriceConfigServiceImpl extends ServiceImpl<AutoUpdateLowestPriceConfigMapper, AutoUpdateLowestPriceConfigEntity> implements AutoUpdateLowestPriceConfigService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveAutoUpdateLowestPriceConfig(AutoUpdateLowestPriceConfigVo vo) {
        if (StrUtilX.isEmpty(vo.getAgentCode())) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_AGENTCODE);
        }
        if (vo.getFrequency() == null) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_FREQUENCY);
        }
        AutoUpdateLowestPriceConfigEntity entity = new AutoUpdateLowestPriceConfigEntity();
        entity.setAgentCode(vo.getAgentCode());
        entity.setAgentName(vo.getAgentName());
        entity.setFrequency(vo.getFrequency());
        if (vo.getId() != null) {
            entity.setId(vo.getId());
            entity.setUpdatedDt(new Date());
            entity.setUpdatedBy(vo.getUpdatedBy());
            this.updateById(entity);
        } else {
            List<AutoUpdateLowestPriceConfigEntity> list = this.list(new LambdaQueryWrapper<>());
            if (CollUtilX.isNotEmpty(list)) {
                entity.setId(list.get(0).getId());
                entity.setUpdatedDt(new Date());
                entity.setUpdatedBy(vo.getUpdatedBy());
                this.updateById(entity);
            } else {
                entity.setDeleted(0);
                entity.setCreatedBy(vo.getCreatedBy());
                entity.setCreatedDt(new Date());
                this.save(entity);
            }
        }
        // 加载到缓存
        RedisTemplateX.delete(RedisKey.AUTO_UPDATE_LOWEST_PRICE_CITY_CONFIG_KEY);
        RedisTemplateX.hPut(RedisKey.AUTO_UPDATE_LOWEST_PRICE_CITY_CONFIG_KEY, vo.getAgentCode(), String.valueOf(vo.getFrequency()));
    }

    @Override
    public AutoUpdateLowestPriceConfigEntity queryAutoUpdateLowestPriceConfig() {
        List<AutoUpdateLowestPriceConfigEntity> list = this.list();
        if (CollUtilX.isEmpty(list)) {
            return null;
        }
        return this.list().get(0);
    }
}
