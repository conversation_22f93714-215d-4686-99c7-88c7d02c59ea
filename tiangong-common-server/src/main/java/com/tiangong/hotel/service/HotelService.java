package com.tiangong.hotel.service;

import com.tiangong.common.Response;
import com.tiangong.dis.dto.CountryReq;
import com.tiangong.dis.dto.DistrictOrBusinessReq;
import com.tiangong.dis.dto.HotelInfoReq;
import com.tiangong.dto.common.HotelPublicDTO;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.dto.hotel.*;
import com.tiangong.dto.hotel.base.HotelInfoCollectionDTO;
import com.tiangong.dto.hotel.base.HotelInfoCollectionReq;
import com.tiangong.fuzzyquery.dto.FuzzyQueryDTO;
import com.tiangong.fuzzyquery.dto.FuzzyRoomDTO;
import com.tiangong.hotel.domain.BaseinfoGroupPO;
import com.tiangong.hotel.domain.collection.HotelLanguageCollection;
import com.tiangong.hotel.domain.req.QueryGroupReq;
import com.tiangong.hotel.domain.resp.CityResp;
import com.tiangong.hotel.domain.resp.SelectCityListReq;
import com.tiangong.hotel.domain.vo.AddressDocVo;
import com.tiangong.hotel.dto.BaseinfoAreadataDTO;
import com.tiangong.hotel.dto.BaseinfoRegionDTO;

import java.util.List;
import java.util.Map;

public interface HotelService {

    /**
     * 酒店详情
     */
    HotelInfoCollectionDTO queryHotelInfo(HotelInfoCollectionReq req);

    /**
     * 查询酒店列表
     */
    List<HotelInfoCollectionDTO> queryHotelInfoList(HotelInfoCollectionReq req);

    /**
     * 酒店详情 返回string
     */
    String queryHotelInfoStrList(HotelInfoReq req);

    /**
     * 模糊查询房型
     */
    List<FuzzyRoomDTO> queryRoomLikeName(FuzzyQueryDTO req);

    /**
     * 根据城市编码获取行政区/商业区
     */
    DistrictOrBusinessResp queryBusinessAndDistrictList(DistrictOrBusinessReq req);

    /**
     * 国家基本信息列表（分页）
     */
    List<BaseinfoAreadataDTO> queryCountryList(CountryReq req);

    /**
     * 查询所有城市列表
     */
    List<CityResp> queryAllCityList(com.tiangong.hotel.domain.req.CityReq req);

    /**
     * 获取国际区号
     */
    List<BaseinfoRegionDTO> getRegion();

    /**
     * 通过关键词查询城市和酒店
     */
    List<HotCityResp> queryCityAndHotelByKeyword(SearchCityAndHotelReq req);

    /**
     * 修改时区
     */
    void updateTimeZone(UpdateTimeZoneDTO updateTimeZoneDTO);

    /**
     * 根据城市编码获取城市时区
     */
    QueryCityTimeZoneResp queryCityTimeZone(QueryCityTimeZoneReq req);

    /**
     * 跟进城市名查询城市，省份，国家数据结构
     */
    Object selectCityList(SelectCityListReq req);

    /**
     * 查询集团/品牌列表
     */
    List<BaseinfoGroupPO> queryGroupList(QueryGroupReq req);

    /**
     * 点击查询城市
     */
    void addHotCityClick(HotCityClickDTO hotCityClickDTO);

    /**
     * 查询酒店列表
     */
    PaginationSupportDTO<HotelListResp> findHotelList(HotelPageReq req);

    /**
     * 更新起价到酒店公共表（mongodb）
     */
    void updateHotelMongodbByHotelId(HotelPublicDTO hotelPublicDTO);

    /**
     * 初始化酒店热度数据任务
     */
    void initHotelHeatDateTask(String param);

    /**
     * 计算酒店热度评分和集团分数任务
     */
    void calculateHotelHeatGroupScoreTask(String param);

    /**
     * 目的地搜索接口
     * 0605版本，优化目的地接口需求 新增字段【热度值】、【目的地ID】
     * by. 雷燕军
     */
    Response<List<AddressRespDTO>> getAddress(Map<String, String> paramMap);

    /**
     * 更加目的地id获取目的地
     */
    AddressDocVo getAddressById(String addressId);

    /**
     * 获取酒店过滤器数据
     */
    Response<HotelSearchResp> getHotelSearch(HotelSearchReq hotelSearchReq);

    /**
     * 同步热度值和推荐值到mongodb
     */
    void updateHotelScoreToMongodbBatch(UpdateHotelScoreReq req);

    /**
     * 清除设施配置缓存
     */
    void clearFacilityConfigCache();

    /**
     * 查询目的地酒店
     */
    List<EsHotelDto> searchDestinationHotel(DestinationReq req);

    /**
     * 查询目的地城市
     */
    List<EsCityDto> searchDestinationCity(DestinationReq req);

    /**
     * 查询目的地
     */
    List<DestinationDTO> queryDestination(DestinationReq req);

    /**
     * 批量Mongodb里面的酒店排序
     * @param publicDTOS
     */
    void updateHotelMongodbByHotelIdBatch(List<HotelPublicDTO> publicDTOS);

    /**
     * 查询mongodb中有无存在的酒店名称
     * @param hotelName
     * @return
     */
    List<HotelLanguageCollection> findMongoDBHotelList(String hotelName);

    /**
     * 查询国，省，市
     * @param req
     * @return
     */
    List<CountryProvinceCityDto> searchCountryProvinceCity(CountryProvinceCityReq req);
}
