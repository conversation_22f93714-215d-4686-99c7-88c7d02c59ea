package com.tiangong.hotel.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tiangong.common.Response;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.hotel.domain.AutoUpdateLowestPriceCityEntity;
import com.tiangong.hotel.domain.resp.AutoUpdateLowestPriceCityResp;
import com.tiangong.hotel.domain.vo.AutoUpdateLowestPriceCityVo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 主动更新起价城市
 */
public interface AutoUpdateLowestPriceCityService extends IService<AutoUpdateLowestPriceCityEntity> {

    /**
     * 新增主动更新起价城市
     */
    void autoUpdateLowestPriceCityAdd(AutoUpdateLowestPriceCityVo vo);

    /**
     * 更新主动更新起价城市
     */
    void autoUpdateLowestPriceCityEdit(AutoUpdateLowestPriceCityVo vo);

    /**
     * 删除主动更新起价城市
     */
    void autoUpdateLowestPriceCityDel(AutoUpdateLowestPriceCityVo vo);

    /**
     * 查询主动更新起价城市列表（分页）
     */
    PaginationSupportDTO<AutoUpdateLowestPriceCityResp> autoUpdateLowestPriceCityPage(AutoUpdateLowestPriceCityVo vo);

    /**
     * 导入主动更新起价城市
     */
    Response<Object> importAutoUpdateLowestPriceCity(MultipartFile file, String language, String operator);

    /**
     * 初始化主动更新起价城市下酒店id到缓存任务
     */
    void initCalculateCityAvgPriceScoreHotelIdToRedisTask(String param);
}
