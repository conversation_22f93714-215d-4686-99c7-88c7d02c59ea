package com.tiangong.hotel.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tiangong.convert.CommonDtoConvert;
import com.tiangong.enums.ErrorCodeEnum;
import com.tiangong.exception.SysException;
import com.tiangong.hotel.domain.RegionMainlyPopularizePriceEntity;
import com.tiangong.hotel.mapper.RegionMainlyPopularizePriceMapper;
import com.tiangong.hotel.req.RegionMainlyPopularizePriceReq;
import com.tiangong.hotel.service.RegionMainlyPopularizePriceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
@Slf4j
public class RegionMainlyPopularizePriceServiceImpl extends ServiceImpl<RegionMainlyPopularizePriceMapper, RegionMainlyPopularizePriceEntity> implements RegionMainlyPopularizePriceService {

    @Autowired
    private RegionMainlyPopularizePriceMapper regionMainlyPopularizePriceMapper;

    @Override
    public void regionMainlyPopularizePriceAdd(RegionMainlyPopularizePriceReq req) {
        RegionMainlyPopularizePriceEntity entity = CommonDtoConvert.INSTANCE.RegionMainlyPopularizePriceConvert(req);
        entity.setCreatedDt(new Date());
        entity.setCreatedBy("system");
        regionMainlyPopularizePriceMapper.insert(entity);
    }

    @Override
    public void regionMainlyPopularizePriceEdit(RegionMainlyPopularizePriceReq req) {
        if (req.getId() == null) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
        RegionMainlyPopularizePriceEntity entity = CommonDtoConvert.INSTANCE.RegionMainlyPopularizePriceConvert(req);
        entity.setUpdatedDt(new Date());
        entity.setUpdatedBy("system");
        regionMainlyPopularizePriceMapper.updateById(entity);
    }
}