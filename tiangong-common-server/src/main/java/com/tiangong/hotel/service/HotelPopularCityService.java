package com.tiangong.hotel.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.dto.hotel.HotelPopularCityDTO;
import com.tiangong.dto.hotel.HotelPopularCityVO;
import com.tiangong.hotel.domain.HotelPopularCityEntity;
import com.tiangong.hotel.domain.req.HotelPopularCityReq;
import com.tiangong.hotel.domain.resp.HotelPopularCityResp;

import java.util.List;

/**
 * 热门城市
 */
public interface HotelPopularCityService extends IService<HotelPopularCityEntity> {

    /**
     * 添加热门城市
     */
    void hotelPopularCityAdd(HotelPopularCityReq req);

    /**
     * 删除热门城市
     */
    void hotelPopularCityDel(HotelPopularCityReq req);

    /**
     * 热门城市列表
     */
    List<HotelPopularCityResp> hotelPopularCityList(HotelPopularCityReq req);

    /**
     * 热门城市列表（分页）
     */
    PaginationSupportDTO<HotelPopularCityResp> hotelPopularCityPage(HotelPopularCityReq req);

    /**
     * 热门城市列表
     */
    List<HotelPopularCityDTO> queryHotelPopularCityList(HotelPopularCityVO vo);
}

