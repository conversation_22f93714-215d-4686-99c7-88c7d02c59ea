package com.tiangong.hotel.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tiangong.cloud.commonbean.utils.IDUtilS;
import com.tiangong.common.Constant;
import com.tiangong.config.SettingsConstant;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.dto.hotel.HeatValueReq;
import com.tiangong.dto.hotel.UpdateHotelScoreReq;
import com.tiangong.hotel.domain.HotelHeatEntity;
import com.tiangong.hotel.mapper.HotelHeatMapper;
import com.tiangong.hotel.req.HotelHeatReq;
import com.tiangong.hotel.req.HotelRecommendReq;
import com.tiangong.hotel.resp.HotelHeatResp;
import com.tiangong.hotel.server.HotelServer;
import com.tiangong.hotel.service.HotelHeatService;
import com.tiangong.keys.RedisKey;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.util.CollUtilX;
import com.tiangong.util.StrUtilX;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 酒店热度信息
 */
@Service
public class HotelHeatServiceImpl extends ServiceImpl<HotelHeatMapper, HotelHeatEntity> implements HotelHeatService {

    @Autowired
    private HotelHeatMapper hotelHeatMapper;

    @Autowired
    private SettingsConstant settingsConstant;

    @Autowired
    private HotelServer hotelServer;

    @Resource(name = "processingCommonExecutor")
    private ThreadPoolTaskExecutor processingCommonExecutor;

    @Override
    public PaginationSupportDTO<HotelHeatResp> queryHotelHeatPage(HotelHeatReq req) {
        IPage<HotelRecommendReq> page = new Page<>(req.getCurrentPage(), req.getPageSize());
        IPage<HotelHeatResp> iPage = hotelHeatMapper.selectHotelHeatPage(page, req);

        PaginationSupportDTO<HotelHeatResp> paginationSupportDTO = new PaginationSupportDTO<>();
        paginationSupportDTO.setItemList(iPage.getRecords());
        paginationSupportDTO.setCurrentPage((int) iPage.getCurrent());
        paginationSupportDTO.setPageSize((int) iPage.getSize());
        paginationSupportDTO.setTotalCount(iPage.getTotal());
        paginationSupportDTO.setTotalPage((int) iPage.getPages());

        return paginationSupportDTO;
    }

    @Override
    @Transactional
    public void updateHotelHeatScore(List<HotelHeatReq> reqList) {
        if (CollUtil.isEmpty(reqList)) return;

        final int maxRetries = 3;
        final long baseSleep = 1000;
        int retryCount = 0;
        String redisValue = IDUtilS.getId_36();

        while (retryCount < maxRetries) {
            Boolean lock = RedisTemplateX.setIfAbsent(RedisKey.HOTEL_HEAT_HEAT_SCORE_LOCK, redisValue, 10L);

            if (Boolean.TRUE.equals(lock)) {
                try {
                    // 分批次更新明细
                    int batchSize = 500;
                    CollUtil.split(reqList, batchSize).forEach(batch -> hotelHeatMapper.updateBatchFields(batch));

                    // 分批次更新总分
                    List<Long> hotelIds = reqList.stream()
                            .map(HotelHeatReq::getHotelId)
                            .distinct()
                            .collect(Collectors.toList());

                    CollUtil.split(hotelIds, 1000).forEach(batch -> {
                        HotelHeatReq batchReq = new HotelHeatReq();
                        batchReq.setHotelIds(batch);
                        batchReq.setUpdatedBy(Constant.SYSTEM);
                        batchReq.setUpdatedDt(reqList.get(0).getUpdatedDt());
                        hotelHeatMapper.updateHotelHeatScoreTotalValue(batchReq);
                    });

                    // 异步操作
                    CompletableFuture.runAsync(() -> {
                        String[] hotelIdList = hotelIds.stream().map(String::valueOf).toArray(String[]::new);
                        RedisTemplateX.setAdd(RedisKey.HOTEL_HEAT_SYNC_MONGODB_HOTEL_ID_KEY, hotelIdList);
                    }, processingCommonExecutor);
                    return; // 成功执行后退出
                } catch (Exception e) {
                    log.error("热度更新异常", e);
                } finally {
                    TransactionSynchronizationManager.registerSynchronization(
                            new TransactionSynchronization() {
                                @Override
                                public void afterCompletion(int status) {
                                    if (status == STATUS_COMMITTED) {
                                        // 删除锁
                                        RedisTemplateX.execute(RedisKey.HOTEL_HEAT_HEAT_SCORE_LOCK, redisValue);
                                    }
                                }
                            }
                    );
                }
            }

            // 锁获取失败处理
            try {
                Thread.sleep(baseSleep * (1L << retryCount));
                retryCount++;
            } catch (InterruptedException e) {
                log.error("修改热度分数，锁获取异常", e);
                break;
            }
        }
        log.warn("热度更新失败，已达最大重试次数");
    }

    @Override
    public void syncIncrementHotelHeatScoreToMongodbTask(String param) {
        List<String> hotelIds;
        if (StrUtilX.isNotEmpty(param)) {
            hotelIds = StrUtilX.stringToList(param, ",");
        } else {
            hotelIds = RedisTemplateX.setPopCount(RedisKey.HOTEL_HEAT_SYNC_MONGODB_HOTEL_ID_KEY, settingsConstant.getConsumerHotelScoreHotelIdCount());
        }
        if (CollUtilX.isEmpty(hotelIds)) {
            return;
        }
        HotelHeatReq hotelHeatReq = new HotelHeatReq();
        List<Long> collect = hotelIds.stream().map(Long::valueOf).collect(Collectors.toList());
        hotelHeatReq.setHotelIds(collect);
        List<HotelHeatResp> respList = hotelHeatMapper.selectHotelHeatScoreByHotelIds(hotelHeatReq);
        if (CollUtilX.isEmpty(respList)) {
            return;
        }

        // 更新mongodb公共表里面的数据
        List<HeatValueReq> heatValueReqList = respList.stream().map(item -> {
            HeatValueReq heatValueReq = new HeatValueReq();
            heatValueReq.setHotelId(item.getHotelId());
            heatValueReq.setHeatValue(item.getHeatScore());
            return heatValueReq;
        }).collect(Collectors.toList());
        UpdateHotelScoreReq req = new UpdateHotelScoreReq();
        req.setHeatValueReqList(heatValueReqList);
        hotelServer.updateHotelScoreToMongodbBatch(req);
    }

    @Override
    public List<String> queryAllHotelHeatHotelIdPage(HotelHeatReq req) {
        return hotelHeatMapper.selectAllHotelHeatHotelIdPage(req);
    }
}