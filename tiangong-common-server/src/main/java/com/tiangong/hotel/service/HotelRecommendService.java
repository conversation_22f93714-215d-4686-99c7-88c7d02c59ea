package com.tiangong.hotel.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.tiangong.common.Response;
import com.tiangong.dis.dto.HotelInfoReq;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.hotel.domain.HotelRecommendEntity;
import com.tiangong.hotel.req.HotelRecommendReq;
import com.tiangong.hotel.resp.HotelRecommendResp;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Set;

/**
 * 酒店推荐排序
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-10-21 11:19:43
 */
public interface HotelRecommendService extends IService<HotelRecommendEntity> {

    /**
     * 酒店推荐排序新增
     */
    void hotelRecommendAdd(HotelRecommendReq req);

    /**
     * 酒店推荐排序删除
     */
    int hotelRecommendDel(HotelRecommendReq req);

    /**
     * 酒店推荐排序编辑
     */
    int hotelRecommendEdit(HotelRecommendReq req);

    /**
     * 酒店推荐排序列表（分页）
     */
    PaginationSupportDTO<HotelRecommendResp> hotelRecommendPage(HotelRecommendReq req);

    /**
     * 获取酒店标签信息
     */
    Set<String> queryHotelLabelInfo(HotelInfoReq req);

    /**
     * 导入酒店排序
     * @param file
     * @param userName
     * @return
     */
    Response<Object> importHotelRecommend(MultipartFile file, String userName);
}

