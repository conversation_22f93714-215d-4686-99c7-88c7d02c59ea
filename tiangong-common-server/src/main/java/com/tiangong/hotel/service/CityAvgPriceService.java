package com.tiangong.hotel.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tiangong.hotel.domain.CityAvgPriceEntity;
import com.tiangong.hotel.req.CityAvgPriceReq;

import java.util.List;

/**
 * 城市平均价
 */
public interface CityAvgPriceService extends IService<CityAvgPriceEntity> {

    /**
     * 批量保存城市平均价
     */
    void batchSaveCityAvgPrice(List<CityAvgPriceReq> reqList);

    /**
     * 计算酒店热度酒店平均房价分数(有平均价)任务
     */
    void calculateHotelHeatHotelAvgPriceScoreHavePriceTask(String param);

    /**
     * 计算酒店热度酒店平均房价分数(没有平均价)任务
     */
    void calculateHotelHeatHotelAvgPriceScoreNotPriceTask(String param);
}

