package com.tiangong.hotel.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tiangong.common.Response;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.hotel.domain.BaseinfoFacility;
import com.tiangong.hotel.domain.req.FacilityAllReq;
import com.tiangong.hotel.domain.req.FacilityEditReq;
import com.tiangong.hotel.domain.req.FacilityListReq;
import com.tiangong.hotel.domain.req.FacilityScoreEditReq;
import com.tiangong.hotel.domain.resp.FacilityInfoAndConfigResp;
import com.tiangong.hotel.domain.resp.FacilityListResp;
import com.tiangong.hotel.domain.resp.FacilityNameResp;
import com.tiangong.hotel.domain.resp.FacilityResp;

import java.util.List;

public interface BaseinfoFacilityService extends IService<BaseinfoFacility> {

    /**
     * 新增或修改设施
     */
    void addOrUpdateFacility(BaseinfoFacility baseinfoFacility);

    /**
     * 查询设施信息
     */
    BaseinfoFacility queryFacility();

    /**
     * 查询酒店设施
     */
    FacilityInfoAndConfigResp queryFacilityManage();

    /**
     * 查询格式化设施
     */
    FacilityResp queryFacilityList(FacilityListReq req);

    /**
     * 编辑设施
     * @param req
     */
    void facilityEdit(FacilityEditReq req);

    /**
     * 分类查询设施
     * @param req
     * @return
     */
    PaginationSupportDTO<FacilityNameResp> facilityPage(FacilityAllReq req);

    /**
     * 修改设施分数
     * @param req
     */
    void facilityScoreEdit(FacilityScoreEditReq req);

}
