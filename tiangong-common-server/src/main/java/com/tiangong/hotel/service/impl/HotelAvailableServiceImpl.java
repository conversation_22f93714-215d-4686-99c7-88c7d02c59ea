package com.tiangong.hotel.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tiangong.convert.CommonDtoConvert;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.dto.hotel.AddHotelAvailableReq;
import com.tiangong.dto.hotel.DestinationReq;
import com.tiangong.dto.hotel.EsHotelDto;
import com.tiangong.dto.hotel.HotelAvailableDTO;
import com.tiangong.enums.ErrorCodeEnum;
import com.tiangong.exception.SysException;
import com.tiangong.hotel.domain.HotelAvailablePO;
import com.tiangong.hotel.domain.req.DelHotelAvailableReq;
import com.tiangong.hotel.domain.req.QueryHotelAvailableReq;
import com.tiangong.hotel.domain.resp.HotelAvailableListResp;
import com.tiangong.hotel.mapper.HotelAvailableMapper;
import com.tiangong.hotel.service.HotelAvailableService;
import com.tiangong.hotel.service.HotelService;
import com.tiangong.keys.RedisKey;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.util.CollUtilX;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2024/1/3 11:01
 */
@Service
public class HotelAvailableServiceImpl extends ServiceImpl<HotelAvailableMapper, HotelAvailablePO> implements HotelAvailableService {

    @Autowired
    private HotelService hotelService;

    @Override
    public void addHotelAvailable(AddHotelAvailableReq req) {
        HotelAvailablePO po = this.getOne(new LambdaQueryWrapper<HotelAvailablePO>()
                .eq(HotelAvailablePO::getHotelId, req.getHotelId())
                .eq(HotelAvailablePO::getAgentCode, req.getAgentCode())
                .last("limit 1")
        );
        if (po != null) {
            throw new SysException(ErrorCodeEnum.AGENT_AVAILABLE_IS_ALREADY);
        }
        po = CommonDtoConvert.INSTANCE.hotelAvailablePOConvert(req);
        List<HotelAvailablePO> result = this.lambdaQuery()
                .eq(HotelAvailablePO::getHotelId, req.getHotelId())
                .eq(HotelAvailablePO::getAgentCode, req.getAgentCode())
                .eq(HotelAvailablePO::getAvailableType, req.getAvailableType())
                .list();
        if (CollUtilX.isNotEmpty(result)) {
            throw new SysException(ErrorCodeEnum.HOTEL_DUPLICATION);
        }
        this.save(po);

        // 添加到redis中
        RedisTemplateX.hashSet(RedisKey.AGENT_HOTEL_BLACKLISTED, req.getAgentCode() + "_" + req.getHotelId(), String.valueOf(req.getHotelId()));
    }

    @Override
    public void delHotelAvailable(DelHotelAvailableReq req) {
        this.removeById(req.getId());

        // 清除缓存
        RedisTemplateX.hashDel(RedisKey.AGENT_HOTEL_BLACKLISTED, req.getAgentCode() + "_" + req.getHotelId());
    }

    @Override
    public PaginationSupportDTO<HotelAvailableListResp> queryHotelListAvailable(QueryHotelAvailableReq req) {
        IPage<HotelAvailableListResp> iPage = new Page<>(req.getCurrentPage(), req.getPageSize());
        IPage<HotelAvailableListResp> page = this.getBaseMapper().queryHotelListAvailable(iPage, req);
        if (CollUtilX.isNotEmpty(page.getRecords())) {
            Set<Long> hotelIds = iPage.getRecords().stream().map(HotelAvailableListResp::getHotelId).collect(Collectors.toSet());
            DestinationReq destinationReq = new DestinationReq();
            destinationReq.setHotelIds(hotelIds);
            destinationReq.setLanguage(req.getLanguage());
            List<EsHotelDto> esHotelDtos = hotelService.searchDestinationHotel(destinationReq);
            Map<Long, EsHotelDto> esHotelDtoMap = new HashMap<>();
            if (CollUtilX.isNotEmpty(esHotelDtos)) {
                for (EsHotelDto esHotelDto : esHotelDtos) {
                    esHotelDtoMap.put(esHotelDto.getHotelId(), esHotelDto);
                }
            }
            iPage.getRecords().forEach(item -> {
                if (esHotelDtoMap.containsKey(item.getHotelId())) {
                    EsHotelDto esHotelDto = esHotelDtoMap.get(item.getHotelId());
                    item.setHotelName(esHotelDto.getHotelName());
                    item.setCityName(esHotelDto.getCityName());
                    item.setCountryName(esHotelDto.getCountryName());
                    item.setProvinceName(esHotelDto.getProvinceName());
                }
            });
        }
        PaginationSupportDTO<HotelAvailableListResp> paginationSupportDTO = new PaginationSupportDTO<>();
        paginationSupportDTO = paginationSupportDTO.getPaginationSupportDTO(page);
        return paginationSupportDTO;
    }

    @Override
    public HotelAvailableDTO queryHotelAvailable(AddHotelAvailableReq req) {
        LambdaQueryWrapper<HotelAvailablePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HotelAvailablePO::getHotelId, req.getHotelId()).eq(HotelAvailablePO::getAgentCode, req.getAgentCode());
        if (req.getAvailableType() != null) {
            queryWrapper.eq(HotelAvailablePO::getAvailableType, req.getAvailableType());
        }
        queryWrapper.last("limit 1");
        HotelAvailablePO hotelAvailablePO = this.getOne(queryWrapper);
        if (hotelAvailablePO == null) {
            return null;
        }
        return CommonDtoConvert.INSTANCE.hotelAvailableDTOConvert(hotelAvailablePO);
    }

    @Override
    public List<Long> queryAgentBlacklistHotelIds(AddHotelAvailableReq req) {
        List<HotelAvailablePO> list = this.list(new LambdaQueryWrapper<HotelAvailablePO>()
                .eq(HotelAvailablePO::getAgentCode, req.getAgentCode())
                .eq(HotelAvailablePO::getAvailableType, 0));
        if (CollUtilX.isNotEmpty(list)) {
            return list.stream().map(HotelAvailablePO::getHotelId).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }
}
