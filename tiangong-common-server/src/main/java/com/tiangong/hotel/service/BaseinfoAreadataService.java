package com.tiangong.hotel.service;

import com.tiangong.hotel.domain.BaseinfoAreadataEntity;
import com.tiangong.hotel.domain.BaseinfoHotCityEntity;
import com.tiangong.hotel.domain.req.AddOrUpdateHotCityReq;
import com.tiangong.hotel.req.QueryAreaDataReq;
import com.tiangong.dto.hotel.HotCityResp;
import com.tiangong.hotel.resp.QueryCityResp;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023/10/30 14:02
 */
public interface BaseinfoAreadataService {

    /**
     * 根据区域类型查询区域集合
     */
    List<BaseinfoAreadataEntity> queryAreaDataListByType(QueryAreaDataReq req);

    /**
     * 查询热门城市
     */
    List<HotCityResp> queryHotCityList(String language);

    /**
     * 查询热门城市
     */
    List<BaseinfoHotCityEntity> queryHotCityListManger();

    /**
     * 修改或新增热门城市
     */
    void addOrUpdateHotCity(AddOrUpdateHotCityReq req);

    /**
     * 根据类型删除区域数据
     */
    void delAreaDataByType(Integer areaType, String language);

    /**
     * 批量新增区域数据
     */
    void insertAreaData(List<BaseinfoAreadataEntity> list, String language);

    /**
     * 查询国家下面所有城市数据
     */
    List<QueryCityResp> queryCity(List<String> list, String language, String areaName);
}
