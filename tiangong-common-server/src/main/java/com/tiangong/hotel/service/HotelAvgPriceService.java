package com.tiangong.hotel.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tiangong.hotel.domain.HotelAvgPriceEntity;
import com.tiangong.hotel.req.HotelAvgPriceReq;
import com.tiangong.hotel.resp.HotelAvgPriceResp;

import java.util.List;

/**
 * 酒店平均价
 */
public interface HotelAvgPriceService extends IService<HotelAvgPriceEntity> {

    /**
     * 批量保存酒店平均价
     */
    void batchSaveHotelAvgPrice(List<HotelAvgPriceReq> reqList);

    /**
     * 删除酒店平均价
     */
    void delHotelAvgPrice(HotelAvgPriceReq req);

    /**
     * 查询酒店平均价城市编码列表
     */
    List<String> queryHotelAvgPriceCityCodes();

    /**
     * 查询酒店平均价列表
     */
    List<HotelAvgPriceResp> queryHotelAvgPriceList(HotelAvgPriceReq req);

    /**
     * 计算酒店热度城市平均房价任务
     */
    void calculateHotelHeatCityAvgPriceTask(String param);

    /**
     * 初始化需要计算酒店热度城市平均房价分数的酒店id到缓存任务
     */
    void initCalculateCityAvgPriceScoreHotelIdToRedisTask(String param);
}

