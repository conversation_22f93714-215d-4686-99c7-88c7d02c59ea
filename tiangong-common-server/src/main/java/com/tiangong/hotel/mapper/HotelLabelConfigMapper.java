package com.tiangong.hotel.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tiangong.dto.hotel.HotelLabelResp;
import com.tiangong.hotel.domain.HotelLabelConfigEntity;
import com.tiangong.hotel.domain.vo.HotelLabelDTO;
import com.tiangong.hotel.req.HotelLabelConfigReq;
import com.tiangong.hotel.resp.HotelLabelConfigResp;

import java.util.List;

public interface HotelLabelConfigMapper extends BaseMapper<HotelLabelConfigEntity> {

    /**
     * 酒店标签配置列表
     */
    List<HotelLabelConfigResp> hotelLabelConfigList(HotelLabelConfigReq req);

    /**
     * 查询最大id
     */
    Integer selectHotelLabelConfigMaxId();

    /**
     * 根据酒店标签id查询酒店标签名称
     */
    List<HotelLabelResp> queryHotelLabelByIds(HotelLabelDTO hotelLabelDTO);
}
