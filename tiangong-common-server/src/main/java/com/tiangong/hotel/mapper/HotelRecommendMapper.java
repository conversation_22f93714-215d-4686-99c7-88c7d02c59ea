package com.tiangong.hotel.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tiangong.dis.dto.HotelInfoReq;
import com.tiangong.hotel.domain.HotelRecommendEntity;
import com.tiangong.hotel.domain.vo.HotelLabelAllDTO;
import com.tiangong.hotel.domain.vo.HotelLabelNameDTO;
import com.tiangong.hotel.req.HotelRecommendReq;
import com.tiangong.hotel.resp.HotelLabelConfigResp;
import com.tiangong.hotel.resp.HotelRecommendResp;
import com.tiangong.product.resp.HotelLabelDTO;

import java.util.List;

/**
 * 酒店推荐排序
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-10-21 11:19:43
 */
public interface HotelRecommendMapper extends BaseMapper<HotelRecommendEntity> {

    /**
     * 查询酒店推荐列表
     */
    IPage<HotelRecommendResp> selectHotelRecommendPage(IPage<?> page, HotelRecommendReq req);

    /**
     * 获取酒店标签信息
     */
    List<HotelLabelConfigResp> queryHotelLabelInfo(HotelInfoReq req);

    /**
     * 查询酒店标签列表
     * @return
     */
    List<HotelLabelAllDTO> queryHotelLabelAll();

    /**
     * 查询指定客户|所有客户酒店标签列表
     */
    List<HotelLabelDTO> selectAgentHotelLabel(HotelLabelNameDTO dto);

    /**
     * 查询酒店推荐总分
     */
    List<HotelRecommendResp> selectHotelRecommendScoreList(HotelRecommendReq req);

    /**
     * 查询酒店推荐总分(分页)
     */
    List<HotelRecommendResp> selectHotelRecommendScorePage(HotelRecommendReq req);

    /**
     * 查询deleted 为 0 的酒店排序
     * @return
     */
    List<HotelRecommendResp> selectAllRecommend();
}
