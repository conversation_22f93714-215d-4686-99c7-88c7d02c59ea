package com.tiangong.hotel.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tiangong.hotel.domain.AutoUpdateLowestPriceCityEntity;
import com.tiangong.hotel.domain.resp.AutoUpdateLowestPriceCityResp;
import com.tiangong.hotel.domain.vo.AutoUpdateLowestPriceCityVo;
import com.tiangong.hotel.req.HotelHeatReq;
import com.tiangong.hotel.resp.HotelHeatResp;

public interface AutoUpdateLowestPriceCityMapper extends BaseMapper<AutoUpdateLowestPriceCityEntity> {

     /**
      * 查询主动更新起价城市列表（分页）
      */
     IPage<AutoUpdateLowestPriceCityResp> autoUpdateLowestPriceCityPage(IPage<?> page, AutoUpdateLowestPriceCityVo vo);
}