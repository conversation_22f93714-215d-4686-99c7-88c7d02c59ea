package com.tiangong.hotel.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 *  酒店二级分类枚举
 */
@Getter
@AllArgsConstructor
public enum SubCategoryEnum {
    HOTEL("486",  "495","酒店", "Hotel","具有统一的管理和装修，通常为旅行者提供餐厅等客用设施及住宿服务"),
    MOTEL("486",  "496","汽车旅馆", "Motel","靠近交通道路，通常面向自驾客人，提供停车服务，其他设施较少"),
    RESORT("486", "497","度假村", "Resort","私人独立住宅，位于共用场地，共用内部餐厅和娱乐活动等设施，装潢陈设较为奢华"),
    INN("486",  "499","旅馆", "Inn","此类住宿提供基本住宿服务，常见于乡村或郊区，规模不大"),
    LOVE_HOTEL("486",  "500","情人酒店", "Love hotel","此类住宿仅限成人入住，按小时或天数出租"),
    RYOKAN("486",  "501","日式旅馆", "Ryokan","此类住宿享有日本传统风格，提供餐食"),
    RIAD("486", "512","摩洛哥传统庭院住宅", "Riad","此类住宿享有摩洛哥传统风格和奢华感，设有庭院"),
    LODGE("486",  "526","山林小屋", "Lodge","此类住宿为私人屋舍，自然环境优美，一般处于山间或林间"),

    APARTHOTEL("487", "505","酒店公寓", "Aparthotel","此类酒店式或服务式公寓具有统一的管理和装修，拥有家具陈设，可提供长租或短租"),
    TRADITIONAL("488",  "506","客栈", "Traditional Chinese Inn","此类住宿具有相对标准化的管理与设施，装修风格具有中国古风特色，多在旅游风景区，古镇名山等地"),
    VILLA("940",  "507","别墅", "Villa","此类多为独栋建筑，可整栋出租，周边建筑密度较低，享有奢华感和私密性"),
    COTTAGE("940", "508","乡村别墅", "Cottage","此类住宿为乡村的私人别墅，多为独栋建筑，可整栋出租，常见私人庭院，环境良好。(新规则将归入别墅)"),
    VILLA_HOTEL("940",  "796","酒店别墅", "Villa Hotel","此类住宿为标准酒店提供的别墅，独栋建筑，可整栋出租，提供标准化酒店服务"),
    FARM_STAY("493", "509","农家乐", "farm stay","此类住宿设在私家农场，陈设简约，提供当地美食"),

    HOMESTAY("489",  "510","住宅民居", "Homestay","民宿指的是供客人居住的私人住宅，房东也可能生活在其他房间内，房东和客人会共用一些设施"),
    GUESTHOUSE("489", "503","家庭旅馆", "Guesthouse","此类住宿一般为家庭经营，主人与住客分别使用不同的设施"),
    PENSION("489", "561","膳宿公寓", "pension","一种韩国特色民宿，一般是在景色较好的地区，有一定的主题的民宿"),
    BED("489",  "504","住宿加早餐旅馆", "Bed and Breakfast","此类住宿一般为家庭经营，提供过夜住宿和早餐"),
    APARTMENT("489", "513","公寓", "Apartment","此类住宿拥有家具陈设，可以短期或长期出租"),
    HOLIDAY_HOME("489",  "514","度假民宿", "Holiday home","此类住宿为独立屋舍，设有外部私人入口，一般为度假出租"),
    COUNTRY_HOUSE("489",  "517","乡村民宿", "Country house","此类住宿为乡村的私人屋舍，陈设简约"),
    LOFT("489",  "703","loft复式", "","上下双层、复式结构的特色公寓"),

    CAPSULE_HOTEL("491",  "520","胶囊旅馆", "Capsule hotel","此类住宿空间极小，提供价格便宜的基本过夜设施"),
    HOSTEL("491", "519","青年旅馆", "Hostel","此类住宿价格低廉，通常按床位售卖，多分男女宿舍，适合背包客出行"),

    BOAT_CRUISE("494",  "522","船屋/观光船", "Boat/Cruise","此类住宿设在船内，具有基本过夜设施"),
    LUXURY_TENT("494",  "523","豪华帐篷", "Luxury tent","此类住宿提供带有固定床铺的帐篷和其他服务，周围自然环境优美"),
    CHALET("494",  "524","木屋", "Chalet","此类住宿为独立屋舍，木质结构，常见用于度假出租"),
    CAMPGROUND("494",  "527","露营地", "Campground","此类住宿在露营地附近提供小屋或简易独栋房，或提供带有设施和娱乐活动的大篷车"),
    SIHEYUAN("494",  "539","四合院", "Siheyuan","中国北京传统合院式建筑，四面建有房屋，将庭院围在中间"),
    OLD_HOUSE("494",  "540","老洋房", "Old Western-style house","此类住宿多为解放前建造，装修精致，结构较好的独立式或复式、别墅式住宅"),
    GER("494",  "699","蒙古包", "","蒙古族特色建筑，多以圆形造型为顶"),
    STILTED_BUILDING("494",  "700","吊脚楼", "","云贵地区传统民居，多半悬空，依山傍水而建"),
    CONTAINER("494",  "701","集装箱", "e","由集装箱改造的特色房源"),
    STONE_BUILDING("494",  "702","石屋", "","由石头为主体材料建造的特色房源"),
    YUKA_LOK("494",  "784","渔家乐", "","带有渔家文化的民宅，多靠近江、河、湖、海"),
    OTHER_SPECIALITY("494",  "521","特色住宿", "Other speciality accommondation","此类住宿具有特色的房屋形态，多为体验式住宿"),
    ;

    private final String categoryId;
    private final String subCategoryId;
    private final String subCategoryChn;
    private final String subCategoryEng;
    private final String subCategoryDesc;

    public static SubCategoryEnum getEnumBySubCategoryId(String subCategoryId){
        for (SubCategoryEnum value : SubCategoryEnum.values()) {
            if (value.subCategoryId.equals(subCategoryId)){
                return value;
            }
        }
        return null;
    }
}
