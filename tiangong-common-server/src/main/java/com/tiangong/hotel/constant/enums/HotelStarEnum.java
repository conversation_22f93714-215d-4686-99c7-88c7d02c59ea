package com.tiangong.hotel.constant.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 酒店星级
 */
@Getter
@AllArgsConstructor
public enum HotelStarEnum {

    FIVESTAR("19", "五星级"),
    UPPERUPSCALE("29", "豪华型"),
    FOURSTAR("39", "四星级"),
    UPSCALE("49", "高档型"),
    THREESTAR("59", "三星级"),
    COMFORT("64", "舒适型"),
    TWOSTAR("69", "二星级"),
    ECONOMY("66", "经济型"),
    APARTMENT("79", "2星级以下/公寓");

    private final String starCode;
    private final String starName;

    public static HotelStarEnum getEnumByKey(String key) {
        for (HotelStarEnum hotelStarEnum : HotelStarEnum.values()) {
            if (hotelStarEnum.starCode.equals(key)) {
                return hotelStarEnum;
            }
        }
        return null;
    }
}
