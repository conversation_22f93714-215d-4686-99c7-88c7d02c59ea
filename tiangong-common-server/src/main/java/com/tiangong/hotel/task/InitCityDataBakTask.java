package com.tiangong.hotel.task;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.tiangong.config.SettingsConstant;
import com.tiangong.enums.LanguageTypeEnum;
import com.tiangong.hotel.BaseHotelInfoUrl;
import com.tiangong.keys.RedisKey;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.util.HttpUtilX;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 拉取城市信息任务
 *
 * <AUTHOR>
 * @create 2023/10/24 14:09
 */
@Slf4j
@Component
public class InitCityDataBakTask {

    @Autowired
    private SettingsConstant settingsConstant;

    @XxlJob("InitCityDataBakTask")
    public void initCityDataBakTask() {
        try {
            XxlJobHelper.log("执行拉取城市信息任务开始");
            JSONObject send = new JSONObject();
            String result = HttpUtilX.post(settingsConstant.getUrl() + BaseHotelInfoUrl.CITY_LIST_RUL_BAK, send.toJSONString());
            JSONObject areaData = JSONObject.parseObject(result);
            JSONArray data = areaData.getJSONArray("data");

            for (LanguageTypeEnum languageTypeEnum : LanguageTypeEnum.values()) {
                List<String> list = new ArrayList<>();
                for (Object datum : data) {
                    list.add(datum.toString());
                    if (list.size() == 1000) {
                        RedisTemplateX.lLeftPushAll(RedisKey.CITY_AND_PROVINCE_CODE + "_" + languageTypeEnum.getValue(), list);
                        list = new ArrayList<>();
                    }
                }
                if (list.size() > 0) {
                    RedisTemplateX.lLeftPushAll(RedisKey.CITY_AND_PROVINCE_CODE + "_" + languageTypeEnum.getValue(), list);
                }
            }
            XxlJobHelper.log("执行拉取城市信息任务结束");
        } catch (Exception e) {
            log.error("执行拉取城市信息任务异常", e);
            XxlJobHelper.log("执行拉取城市信息任务异常", e);
        }
    }
}
