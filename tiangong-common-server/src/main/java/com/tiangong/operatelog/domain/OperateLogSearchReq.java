package com.tiangong.operatelog.domain;

import com.tiangong.cloud.common.domain.BasePage;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2023/3/14 17:21
 * @Description:
 */
@Data
public class OperateLogSearchReq extends BasePage implements Serializable {

    private String operateAccount;


    /**
     * 事件类型
     * 0：系统级事件
     * <p>
     * 1：业务级事件
     */
    private Integer logType;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date operateStartDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date operateEndDate;

    /**
     * 事件结果
     * 0：异常
     * <p>
     * 1：正常
     */
    private Integer operateResult;

    /**
     * 操作类型
     * 0:查询
     * 1:增加
     * 2:删除
     * 3:修改
     * 4:导出
     * 5:加锁
     * 6:确认
     * 7:发短信
     * 8:登录
     * 9:处理
     * 10:取消
     * 11:发单
     * 12:重置
     * 13:推送
     * 14:刷新
     * 15:解锁
     * 16:退出登录
     * 17:移动
     * 18:越权操作
     * 19:ip异常操作
     * 20:连续登录失败
     * 21:获取
     * 22:请求错误
     * 23:初始化
     * 24:通知
     * 25:校验
     * 26:导入
     */
    private Integer operationType;

}
