package com.tiangong.operatelog.domain;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @Date 2023/3/14 19:16
 * @Description:
 */
@Data
public class ExportOperateLogSearchDTO implements Serializable {

    /**
     * 创建时间
     */
    private LocalDateTime createdDt;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 事件类型
     * 0：系统级事件
     * <p>
     * 1：业务级事件
     */
    private String eventTypeName;
    /**
     * 事件描述
     */
    private String eventDescription;
    /**
     * 事件结果
     * 0：异常
     * <p>
     * 1：正常
     */
    private String eventResult;

    /**
     * ip地址
     */
    private String ipAddress;

    /**
     * 操作类型
     */
    private String operateTypeName;


}
