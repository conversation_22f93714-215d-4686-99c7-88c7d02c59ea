package com.tiangong.operatelog.domain;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/3/29 10:29
 * @Description:
 */
@Data
public class SaveAuditSettingReq {

    private List<OperateTypeDTO> operateTypeList;

    private List<OperateRoleDTO> operateRoleList;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date ignoreStartTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date ignoreEndTime;

    private Double configureCapacity;


}
