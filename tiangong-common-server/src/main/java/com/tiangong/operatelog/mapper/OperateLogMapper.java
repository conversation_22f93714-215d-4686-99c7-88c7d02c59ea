package com.tiangong.operatelog.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tiangong.operatelog.domain.OperateLogEntity;
import com.tiangong.operatelog.domain.OperateLogSearchDTO;
import com.tiangong.operatelog.domain.QueryOperateLogDTO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-02-01 17:24:19
 */
@Mapper
public interface OperateLogMapper extends BaseMapper<OperateLogEntity> {

    /**
     * 查询操作日志列表（分页）
     */
    List<OperateLogSearchDTO> queryListPage(QueryOperateLogDTO queryOperateLogDTO);
}
