package com.tiangong.operatelog.controller;

import com.tiangong.common.Response;
import com.tiangong.operatelog.domain.OperateLogReq;
import com.tiangong.operatelog.dto.OperateLogReqDTO;
import com.tiangong.operatelog.service.OperateLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-03-14 14:20:18
 */
@RestController
@RequestMapping("/common/operateLog")
public class OperateLogController {

    @Autowired
    private OperateLogService operateLogService;

    /**
     * 新增
     */
    @PostMapping("/operateLogAdd")
    public void operateLogAdd(@RequestBody OperateLogReqDTO req) {
        operateLogService.operateLogAdd(req);
    }

    /**
     * 详情
     */
    @PostMapping("/operateLogDetail")
    public Response<Object> operateLogDetail(@RequestBody OperateLogReq req) {
        return Response.success(operateLogService.operateLogDetail(req.getLogId()));
    }

    /**
     * 列表（分页）
     */
    @PostMapping("/operateLogPage")
    public Response<Object> operateLogPage(@RequestBody OperateLogReq req) {
        return Response.success(operateLogService.operateLogPage(req));
    }

}
