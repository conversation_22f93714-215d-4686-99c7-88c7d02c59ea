package com.tiangong.operatelog.service;


import com.tiangong.operatelog.domain.OperateLogReq;
import com.tiangong.operatelog.dto.OperateLogReqDTO;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-03-14 14:20:18
 */
public interface OperateLogService {

    /**
     * 操作日志新增
     */
    void operateLogAdd(OperateLogReqDTO req);

    /**
     * 查询操作日志详情
     */
    Object operateLogDetail(Long id);

    /**
     * 查询操作日志列表
     */
    Object operateLogPage(OperateLogReq req);
}

