//package com.tiangong.secret.controller;
//
//import com.alibaba.fastjson.JSON;
//import com.tiangong.cloud.common.anno.AnonymousAccess;
//import com.tiangong.cloud.common.domain.LoginUser;
//import com.tiangong.cloud.common.domain.ResultVo;
//import com.tiangong.cloud.common.enums.result.ResultEnum;
//import com.tiangong.cloud.common.utils.R;
//import com.tiangong.cloud.commonbean.utils.WebFrameworkUtilX;
//import com.tiangong.keys.RedisKey;
//import com.tiangong.operatelog.domain.OperateLogReq;
//import com.tiangong.operatelog.dto.OperateLogReqDTO;
//import com.tiangong.operatelog.service.OperateLogService;
//import com.tiangong.user.dto.UserDTO;
//import com.tiangong.user.service.UserService;
//import com.tiangong.util.*;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.*;
//
//import javax.annotation.Resource;
//import javax.servlet.http.HttpServletRequest;
//import java.time.LocalDateTime;
//import java.util.HashMap;
//import java.util.Map;
//
//
///**
// *
// *
// * <AUTHOR>
// * @email <EMAIL>
// * @date 2023-03-27 14:20:18
// */
//@Slf4j
//@RestController
//@RequestMapping("/common/stateSecret")
//public class StateSecretController {
//
//    @Autowired
//    private UserService userService;
//
//    @Resource
//    private RedisUtil redisUtil;
//
//    @Autowired
//    private OperateLogService operateLogService;
//
//    /**
//     * 获取密钥
//     */
//    @AnonymousAccess
//    @PostMapping("/getSecretKey")
//    public ResultVo getSecretKey(@RequestBody Map<String, String> requestMap) {
//        String secretKey = NumberUtil.generateNumberAndLetters(32);
//        if (StrUtilX.isNotEmpty(requestMap.get("userAccount"))){
//            Map<String,String> request = new HashMap<>();
//            request.put("userAccount",requestMap.get("userAccount"));
//            UserDTO userDTO = userService.queryLoginUser(request);
//            if (userDTO == null){
//                return R.error("登录失败");
//            }
//            RedisTemplateX.set(StrUtilX.concat(RedisKey.USER_SECRET_KEY, userDTO.getLoginAccount()), secretKey);
//        } else {
//            LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
//            if (loginUser == null){
//                return R.error(ResultEnum.E_9001.getCode(),ResultEnum.E_9001.getMessage());
//            }
//            Map<String, String> keyMap = new HashMap<>();
//            keyMap.put(secretKey, "1");
//            RedisTemplateX.hPut(StrUtilX.concat(RedisKey.USER_SECRET_KEY, loginUser.getUserId().toString()), keyMap);
//        }
//
//        try {
//            Object obj = RedisTemplateX.get("loginTimeOut");
//            if (null != obj) {
//                if (null == RedisTemplateX.get("logout")) {
//                    OperateLogReqDTO operateLogReqDTO = JSON.parseObject(obj.toString(), OperateLogReqDTO.class);
//                    operateLogService.operateLogAdd(operateLogReqDTO);
//                }
//                RedisTemplateX.delete("loginTimeOut");
//            }
//            RedisTemplateX.delete("logout");
//        } catch (Exception e) {
//            log.error("超时登录日志记录失败,",e);
//        }
//        return R.ok(SM2Utils.encryptData(secretKey));
//    }
//
//    /**
//     * 获取密钥
//     */
//    @AnonymousAccess
//    @PostMapping("/removeSecretKey")
//    public void removeSecretKey(@RequestParam("secretKey") String secretKey) {
//        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
//        if (loginUser != null){
//            RedisTemplateX.hDelete(StrUtilX.concat(RedisKey.USER_SECRET_KEY, loginUser.getUserId().toString()),secretKey);
//        }
//    }
//
//    /**
//     * 验证密钥
//     */
//    @AnonymousAccess
//    @PostMapping(value = "/verifySecretKey", produces = {"application/json;charset=UTF-8"})
//    public String verifySecretKey(@RequestParam("secretKey") String secretKey) {
//        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
//        if (loginUser == null){
//            return "true";
//        }
//        Object object= RedisTemplateX.hGetAll(StrUtilX.concat(RedisKey.USER_SECRET_KEY, loginUser.getUserId().toString()),secretKey);
//        if(object !=null){
//            return "true";
//        }else{
//            return "false";
//        }
//    }
//}
