package com.tiangong;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tiangong.convert.CommonDtoConvert;
import com.tiangong.dto.common.CompanyDTO;
import com.tiangong.finance.OrgDTO;
import com.tiangong.keys.RedisKey;
import com.tiangong.organization.domain.ConfigLogCodePO;
import com.tiangong.organization.domain.OrgPO;
import com.tiangong.organization.mapper.ConfigLogCodeMapper;
import com.tiangong.organization.mapper.OrgMapper;
import com.tiangong.redis.core.RedisTemplateX;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class initCommonServer implements ApplicationRunner {

    @Autowired
    private OrgMapper orgMapper;

    @Autowired
    private ConfigLogCodeMapper configLogCodeMapper;

    @Override
    public void run(ApplicationArguments args) {
        OrgPO po = new OrgPO();
        po.setOrgCode(CompanyDTO.COMPANY_CODE);
        List<OrgPO> select = orgMapper.select(po);
        if (select != null && select.size() > 0) {
            OrgPO orgPO = select.get(0);
            OrgDTO dto = CommonDtoConvert.INSTANCE.orgDTOConvert(orgPO);
            RedisTemplateX.set(RedisKey.ORG_INFO, JSONObject.toJSONString(dto));
        }

        List<ConfigLogCodePO> configLogCodePOS = configLogCodeMapper.selectList(new LambdaQueryWrapper<ConfigLogCodePO>());
        for (ConfigLogCodePO po1 : configLogCodePOS) {
            RedisTemplateX.set(RedisKey.LOG_CODE + po1.logCode, po1.getLogContent());
        }
    }
}
