package com.tiangong.organization.mapper;

import com.tiangong.dto.common.MyMapper;
import com.tiangong.organization.domain.OrgSupplierPO;
import com.tiangong.organization.domain.SupplierCompanyPO;
import com.tiangong.organization.domain.dto.UpdateSupplierAdjustmentDTO;
import com.tiangong.organization.domain.req.SupplierAdjustmentUpdateReq;
import com.tiangong.organization.domain.req.SupplierCompanyReq;
import com.tiangong.organization.domain.req.SupplierInvoiceUpdateReq;
import com.tiangong.organization.domain.resp.SupplierCompanyResp;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/6/20 18:10
 **/
@Mapper
public interface SupplierCompanyMapper extends MyMapper<SupplierCompanyPO> {

    /**
     * 获取供应商企业
     */
    List<OrgSupplierPO> querySupplier();

    /**
     * 更加供应商id修改加幅
     */
    int updateByOrgIdToAdjustment(SupplierAdjustmentUpdateReq req);

    /**
     * 更加供应商id修改发票信息
     */
    int updateByOrgIdToInvoice(SupplierInvoiceUpdateReq req);

    /**
     * 查询供应商加幅和发票数据进行初始化到redis缓存
     */
    List<UpdateSupplierAdjustmentDTO> queryAdjustAndInvoice();

    /**
     * 查询供应商列表
     */
    List<SupplierCompanyResp> selectSupplierCompanyList(SupplierCompanyReq req);
}
