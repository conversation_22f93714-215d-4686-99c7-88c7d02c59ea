package com.tiangong.organization.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tiangong.organization.domain.entity.OrgAgentApiInvokeConfigEntity;
import com.tiangong.organization.remote.dto.OrgAgentApiInvokeConfigResp;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 分销api接口频次表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-18 12:44:15
 */
public interface OrgAgentApiInvokeConfigMapper extends BaseMapper<OrgAgentApiInvokeConfigEntity> {

    List<OrgAgentApiInvokeConfigResp> queryInvokeByAgentCode(@Param("partnerCode") String partnerCode);

    List<OrgAgentApiInvokeConfigResp> queryInitInvoke();

}
