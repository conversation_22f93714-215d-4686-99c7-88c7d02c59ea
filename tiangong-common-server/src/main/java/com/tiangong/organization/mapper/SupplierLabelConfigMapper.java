package com.tiangong.organization.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tiangong.organization.domain.SupplierLabelConfigPO;
import com.tiangong.organization.domain.dto.SupplierLabelDTO;
import com.tiangong.organization.domain.req.QuerySupplierLabelConfigReq;
import com.tiangong.organization.domain.resp.SupplierLabelConfigResp;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023/12/18 11:39
 */
@Mapper
public interface SupplierLabelConfigMapper extends BaseMapper<SupplierLabelConfigPO> {

    /**
     * 查询供应商标签配置列表（分页）
     */
    IPage<SupplierLabelConfigResp> querySupplierLabelConfigPage(IPage<SupplierLabelConfigResp> page, @Param("req")QuerySupplierLabelConfigReq req);

    /**
     * 查询供应商标签配置列表
     */
    List<SupplierLabelDTO> querySupplierLabelConfigList();
}
