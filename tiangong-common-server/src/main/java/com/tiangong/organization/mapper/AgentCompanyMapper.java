package com.tiangong.organization.mapper;

import com.tiangong.dto.common.MyMapper;
import com.tiangong.organization.domain.AgentCompanyPO;
import com.tiangong.organization.domain.req.AgentCompanyReq;
import com.tiangong.organization.domain.resp.AgentCompanyResp;
import com.tiangong.organization.remote.dto.AgentCreditWarningConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/6/26 21:36
 **/
@Mapper
public interface AgentCompanyMapper extends MyMapper<AgentCompanyPO> {

    /**
     * 查询需要推送结算成本客户编码
     */
    List<String> selectPushSettleCostSwitchAgentCodeList();

    /**
     * 查询客户列表
     */
    List<AgentCompanyResp> selectAgentCompanyList(AgentCompanyReq req);

    /**
     * 额度预警配置
     *
     * @param agentCodeList
     * @return
     */
    List<AgentCreditWarningConfig> selectWarningConfigList(@Param("agentCodeList") List<String> agentCodeList);
}
