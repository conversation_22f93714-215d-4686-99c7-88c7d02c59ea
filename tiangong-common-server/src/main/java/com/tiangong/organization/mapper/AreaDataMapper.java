package com.tiangong.organization.mapper;


import com.tiangong.dto.common.MyMapper;
import com.tiangong.dto.hotel.QueryCityTimeZoneReq;
import com.tiangong.dto.hotel.QueryCityTimeZoneResp;
import com.tiangong.dto.hotel.UpdateTimeZoneDTO;
import com.tiangong.hotel.domain.BaseinfoAreadataEntity;
import com.tiangong.hotel.domain.req.AddOrUpdateHotCityReq;
import com.tiangong.hotel.domain.resp.HotCityMangerResp;
import com.tiangong.hotel.req.QueryAreaDataReq;
import com.tiangong.dto.hotel.HotCityResp;
import com.tiangong.hotel.resp.QueryCityResp;
import com.tiangong.organization.domain.AreaDataPO;
import com.tiangong.organization.remote.dto.AreaDataDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;


public interface AreaDataMapper  extends MyMapper<AreaDataPO> {

    /**
     * 查询区域列表
     */
    List<AreaDataDTO> queryAreaData(Map<String, Object> map);

    /**
     * 根据区域类型查询区域集合
     */
    List<BaseinfoAreadataEntity> queryAreaDataListByType(@Param("req") QueryAreaDataReq req, @Param("tXTable") String tXTable);

    /**
     * 查询热门城市
     */
    List<HotCityResp> queryHotCityList(@Param("tXTable") String tXTable);

    /**
     * 查询热门城市
     */
    List<HotCityMangerResp> queryHotCityListManger();

    /**
     * 修改或新增热门城市
     */
    int addOrUpdateHotCity(AddOrUpdateHotCityReq req);

    /**
     * 根据类型删除区域数据
     */
    Integer delAreaDataByType(@Param("areaType") Integer areaType, @Param("tXTable") String tXTable);

    /**
     * 批量新增区域数据
     */
    Integer insertAreaData(@Param("list") List<BaseinfoAreadataEntity> list, @Param("tXTable") String tXTable);

    /**
     * 查询国家下面所有城市数据
     */
    List<QueryCityResp> queryCity(@Param("list") List<String> list, @Param("tXTable") String tXTable, @Param("areaName") String areaName);

    /**
     * 更新时区
     */
    int updateAreaTimeZeroByCityCode(UpdateTimeZoneDTO updateTimeZoneDTO);

    /**
     * 根据城市编码获取城市时区
     */
    QueryCityTimeZoneResp queryCityTimeZone(QueryCityTimeZoneReq req);

    /**
     * 查询城市
     */
    BaseinfoAreadataEntity selectCity(@Param("tXTable") String tXTable, @Param("cityCode") String cityCode);
}
