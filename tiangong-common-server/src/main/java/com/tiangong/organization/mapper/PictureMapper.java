package com.tiangong.organization.mapper;

import com.tiangong.dto.common.MyMapper;
import com.tiangong.organization.domain.PicturePO;
import com.tiangong.organization.remote.dto.CompanyBusinessLicenseUrlDTO;
import com.tiangong.organization.remote.dto.PictureLicenseDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/6/28 12:00
 **/
public interface PictureMapper extends MyMapper<PicturePO> {
    /**
     * 根据企业编码查询企业营业执照
     */
    List<PictureLicenseDTO> queryPictureLicense(@Param("companyCode") String companyCode, @Param("pictureType") Integer pictureType);

    /**
     * 查询企业营业执照
     */
    List<CompanyBusinessLicenseUrlDTO> PictureLicense(@Param("companyCode") String companyCode, @Param("pictureType") Integer pictureType);
}
