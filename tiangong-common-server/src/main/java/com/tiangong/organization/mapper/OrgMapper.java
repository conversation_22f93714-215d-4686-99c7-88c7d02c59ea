package com.tiangong.organization.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tiangong.dto.common.MyMapper;
import com.tiangong.organization.domain.AgentCompanyPO;
import com.tiangong.organization.domain.OrgPO;
import com.tiangong.organization.domain.SupplierCompanyPO;
import com.tiangong.organization.domain.UserPO;
import com.tiangong.organization.domain.dto.OrgBaseDTO;
import com.tiangong.organization.domain.dto.UserListDTO;
import com.tiangong.organization.domain.req.PartnerStatusReq;
import com.tiangong.organization.domain.req.SupplierListReq;
import com.tiangong.organization.domain.dto.BankListDTO;
import com.tiangong.organization.domain.req.*;
import com.tiangong.organization.domain.resp.QueryOrderAgentDetailResp;
import com.tiangong.organization.domain.resp.QueryOrderSupplierDetailResp;
import com.tiangong.organization.remote.dto.BankDetailResp;
import com.tiangong.organization.domain.resp.BankPageResp;
import com.tiangong.organization.remote.dto.BankAgentListDTO;
import com.tiangong.organization.domain.resp.BankLogDTO;
import com.tiangong.organization.remote.dto.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/6/19 21:14
 **/
@Mapper
public interface OrgMapper extends MyMapper<OrgPO> {
    /**
     * 根据供应商编号查出管理员对应的管理员Id
     */
    Integer getUserId(@Param("companyCode") String companyCode);

    /**
     * 根据客户编码获取客户下面的登录用户
     */
    List<UserListDTO> getUserListByOrgCode(@Param("companyCode") String companyCode);

    /**
     * 根据机构Id,查询主键供应商
     */
    Integer getSupplierCompany(@Param("supplierId") Integer supplierId, @Param("companyCode") String companyCode);

    /**
     * 根据机构Id,查询主键客户
     */
    Integer getAgentCompany(@Param("agentId") Integer agentId);

    /**
     * 根据供应商编码查询供应商详情
     */
    SupplierSelectDTO supplierList(@Param("orgCode") String orgCode, @Param("companyCode") String companyCode);

    /**
     * 根据供应商编码查询银行卡列表详情
     */
    List<ContactSupplierDTO> concactList(@Param("orgCode") String orgCode, @Param("active") Integer active, @Param("contactType") Integer contactType);

    /**
     * 根据机构编码查询银行卡信息
     */
    List<BankSupplierDTO> bankList(BankListDTO dto);

    /**
     * 分页查询银行卡列表
     */
    IPage<BankPageResp> bankPage(IPage<BankPageResp> iPage, @Param("req") BankListPageReq req);

    /**
     * 根据客户id查询客户个人付款银行卡
     */
    List<BankAgentListDTO> bankListByAgentId(@Param("agentId") Integer agentId);

    /**
     * 根据机构编码和银行卡id查询银行卡信息
     */
    BankSupplierDTO getOneBank(@Param("orgCode") String orgCode, @Param("bankId") Integer bankId);

    /**
     * 更新银行卡余额
     */
    boolean updateBankBalance(@Param("bankId") Integer bankId, @Param("amount") BigDecimal amount);

    /**
     * 修改银行卡状态
     */
    int updateBankStatus(ModifiedBankStatusReq req);

    /**
     * 查询银行卡详情
     */
    BankDetailResp bankDetail(@Param("bankId") Integer bankId);

    /**
     * 查询银行流水分页
     */
    IPage<BankLogDTO> bankLogList(IPage<BankLogDTO> page, @Param("req") BankLogReq req);

    /**
     * 根据客户编码查询客户详情
     */
    AgentSelectDTO agentList(@Param("orgCode") String orgCode);

    /**
     * 根据企业编码查询企业详情
     */
    CompanySelectDTO companyList(@Param("orgCode") String orgCode);

    /**
     * 根据企业编码修改总管理员启用状态
     */
    public void updateAdminStatus(@Param("userPO") UserPO userPO);

    /**
     * 供应商详情列表
     */
    List<QuerySupplierListDTO> querySupplierList(@Param("request") SupplierListReq request);

    /**
     * 客户详情列表
     */
    List<QueryAgentListDTO> queryAgentList(@Param("request") AgentListRequest request);

    /**
     * 企业详情列表
     */
    List<QueryCompanyListDTO> queryCompanyList(@Param("request") CompanyListRequest request);

    /**
     * 修改供应商编码的role都为0
     */
    void updateContactRole(@Param("orgCode") String orgCode, @Param("contactRole") String contactRole, @Param("role") String role, @Param("contactType") Integer contactType);

    /**
     * 修改供应商中间表启用状态
     */
    void updateSupplierStatus(@Param("supplierCompanyPO") SupplierCompanyPO supplierCompanyPO);

    /**
     * 修改客户中间表
     */
    void updateAgentStatus(@Param("agentCompanyPO") AgentCompanyPO agentCompanyPO);

    /**
     * 修改合作商启用状态
     */
    Integer updateAgentPartnerStatus(PartnerStatusReq req);

    /**
     * 判断域名是否重复
     */
    CompanySelectDTO getDomian(@Param("orgDomian") String orgDomian);

    /**
     * 查询该分销商是否存在该订单的扣退额度
     */
    List<AgentCreditLineDTO> getAgentCreditLine(@Param("agentCode") String agentCode);

    /**
     * 根据分销商编码插入剩余额度
     */
    boolean updateBalance(@Param("agentId") Integer agentId, @Param("Balance") BigDecimal balance);

    /**
     * 机构名称验重,机构为运营商
     */
    String getCompanyName(@Param("examineOrgNameDTO") ExamineOrgNameDTO examineOrgNameDTO);

    /**
     * 机构名称验重，机构为客户
     */
    String getAgentName(@Param("examineOrgNameDTO") ExamineOrgNameDTO examineOrgNameDTO);

    /**
     * 机构名称验重，机构为供应商
     */
    String getSupplierName(@Param("examineOrgNameDTO") ExamineOrgNameDTO examineOrgNameDTO);

    /**
     * 查询企业主体
     */
    List<SendEntityDTO> queryCompanyOfficial(@Param("companyCode") String companyCode);

    /**
     * 查询是否存在有公章的供应商
     */
    List<Integer> judgeExistSupplierOfficial(@Param("officialSealId") String officialSealId);

    /**
     * 查询订单客户信息
     */
    QueryOrderAgentDetailResp queryOrderAgentDetail(QueryOrderAgentDetailReq req);

    /**
     * 查询订单客户信息
     */
    QueryOrderSupplierDetailResp queryOrderSupplierDetail(QueryOrderSupplierDetailReq req);

    /**
     * 根据组织编码集合批量查询组织信息
     *
     * @param orgCodes 组织编码集合
     * @return 组织信息列表
     */
    List<OrgBaseDTO> selectByOrgCodes(@Param("orgCodes") List<String> orgCodes);

    /**
     * 根据供应商编码集合查询供应商信息
     * @param companyCodes 运营商编码
     * @return
     */
    List<SupplierBaseInfoResp> selectSupplierBaseInfo(@Param("companyCodes")List<String> companyCodes);
}
