package com.tiangong.organization.service.Impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.tiangong.convert.agent.AgentConfirmationLetterConfigConvert;
import com.tiangong.organization.domain.AgentConfirmationLetterConfigPO;
import com.tiangong.organization.domain.req.AgentConfirmationLetterConfigAditReq;
import com.tiangong.organization.mapper.AgentConfirmationLetterConfigMapper;
import com.tiangong.organization.remote.dto.AgentConfirmationLetterConfigResp;
import com.tiangong.organization.service.AgentConfirmationLetterConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 客户确认函配置服务实现类
 */
@Slf4j
@Service
public class AgentConfirmationLetterConfigServiceImpl implements AgentConfirmationLetterConfigService {

    @Autowired
    private AgentConfirmationLetterConfigMapper agentConfirmationLetterConfigMapper;

    @Override
    public Long confirmationLetterAdd(AgentConfirmationLetterConfigAditReq req) {
        AgentConfirmationLetterConfigPO po = AgentConfirmationLetterConfigConvert.INSTANCE.convert(req);
        agentConfirmationLetterConfigMapper.insert(po);
        return po.getId();
    }

    @Override
    public Long confirmationLetterEdit(AgentConfirmationLetterConfigAditReq req) {
        AgentConfirmationLetterConfigPO po = AgentConfirmationLetterConfigConvert.INSTANCE.convert(req);
        agentConfirmationLetterConfigMapper.updateById(po);
        return po.getId();
    }

    @Override
    public void delete(Long id) {
        agentConfirmationLetterConfigMapper.deleteById(id);
    }

    @Override
    public AgentConfirmationLetterConfigResp getByAgentCode(String agentCode) {
        QueryWrapper<AgentConfirmationLetterConfigPO> wrapper = new QueryWrapper<>();
        wrapper.eq("agent_code", agentCode);
        AgentConfirmationLetterConfigPO po = agentConfirmationLetterConfigMapper.selectOne(wrapper);
        return AgentConfirmationLetterConfigConvert.INSTANCE.convert(po);
    }

}