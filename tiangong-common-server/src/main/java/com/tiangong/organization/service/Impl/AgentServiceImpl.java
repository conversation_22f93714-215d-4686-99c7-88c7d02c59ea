package com.tiangong.organization.service.Impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Maps;
import com.tiangong.ValidatedFilter;
import com.tiangong.agent.dto.AgentConfigDTO;
import com.tiangong.cloud.common.enums.BaseEnum;
import com.tiangong.common.Response;
import com.tiangong.common.remote.SequenceRemote;
import com.tiangong.convert.CommonDtoConvert;
import com.tiangong.dto.common.CompanyDTO;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.enums.*;
import com.tiangong.exception.CustomException;
import com.tiangong.exception.SysException;
import com.tiangong.keys.RedisKey;
import com.tiangong.organization.domain.AgentCompanyPO;
import com.tiangong.organization.domain.AgentCreditLinePO;
import com.tiangong.organization.domain.OrgPO;
import com.tiangong.organization.domain.UserPO;
import com.tiangong.organization.domain.dto.BankListDTO;
import com.tiangong.organization.domain.dto.UpdateAccountPwdDTO;
import com.tiangong.organization.domain.dto.UserListDTO;
import com.tiangong.organization.domain.entity.OrgAgentApiConfigEntity;
import com.tiangong.organization.domain.entity.OrgAgentApiInvokeConfigEntity;
import com.tiangong.organization.domain.req.*;
import com.tiangong.organization.domain.resp.QueryOrderAgentDetailResp;
import com.tiangong.organization.mapper.*;
import com.tiangong.organization.remote.dto.*;
import com.tiangong.organization.service.AgentBankService;
import com.tiangong.organization.service.AgentConfirmationLetterConfigService;
import com.tiangong.organization.service.AgentCreditWarningService;
import com.tiangong.organization.service.AgentService;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.user.mapper.UserMapper;
import com.tiangong.user.service.UserService;
import com.tiangong.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.Assert;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.beans.Transient;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019/6/26 20:32
 **/
@Service
@Slf4j
public class AgentServiceImpl implements AgentService {

    @Autowired
    private OrgMapper orgMapper;

    @Autowired
    private AgentCompanyMapper agentCompanyMapper;

    @Autowired
    private AgentConfirmationLetterConfigService agentConfirmationLetterConfigService;

    @Autowired
    private UserService userService;

    @Autowired
    private AgentCreditLineMapper agentCreditLineMapper;

    @Autowired
    private OrgAgentApiConfigMapper orgAgentApiConfigMapper;

    @Autowired
    private OrgAgentApiInvokeConfigMapper orgAgentApiInvokeConfigMapper;

    @Autowired
    private SequenceRemote sequenceRemote;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private AgentBankService agentBankService;
    @Autowired
    private AgentCreditWarningService agentCreditWarningService;

    @Resource(name = "processingCommonExecutor")
    private ThreadPoolTaskExecutor processingCommonExecutor;
    /**
     * 新增客户
     */
    @Transient
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addAgent(AgentAddReq req) {
        // 校验客户名是否重复
        OrgPO orgPO1 = new OrgPO();
        orgPO1.setType(1);
        orgPO1.setOrgName(req.getAgentName());
        List<OrgPO> org = orgMapper.select(orgPO1);
        if (org != null && !org.isEmpty()) {
            throw new SysException(ErrorCodeEnum.AGENT_NAME_IS_ALREADY);
        }

        Date date = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        //获取String类型的时间
        String createdate = sdf.format(date);

        //if (req.getChannelCode().equals(ChannelEnum.B2B.key)) {
        OrgPO orgPO = new OrgPO();
        orgPO.setOrgType(req.getAgentType());
        orgPO.setOrgName(req.getAgentName());
        orgPO.setOrgDomian(req.getOrgDomain());
        orgPO.setCreatedDt(createdate);
        orgPO.setCreatedBy(req.getCreatedBy());
        orgPO.setAvailableStatus(AvailableEnum.Start.key);
        orgPO.setType(OrgEnum.Org_Agent.key);
        orgPO.setChannelCode(req.getChannelCode());
        orgPO.setIsShareSupplier(0);
        orgPO.setOrgUniqueCode(req.getOrgUniqueCode());
        orgPO.setCountryCode(req.getCountryCode());
        orgPO.setCountryName(req.getCountryName());
        orgPO.setProvinceCode(req.getProvinceCode());
        orgPO.setProvinceName(req.getProvinceName());
        orgPO.setCityCode(req.getCityCode());
        orgPO.setCityName(req.getCityName());
        orgPO.setOrgAddress(req.getOrgAddress());
        orgPO.setEstablishedDate(req.getEstablishedDate());

        //获取编码
        String code = getPartnerCode(SystemCodeEnum.CUSTOMERCODE.code);
        orgPO.setOrgCode(code);
        orgMapper.insert(orgPO);

        AgentCompanyPO agentCompanyPO = new AgentCompanyPO();

        UserPO userPO = new UserPO();
        userPO.setUserName(req.getAdminName());
        if (StrUtilX.isNotEmpty(req.getAdminTel())) {
            userPO.setUserTel(req.getAdminTel());
        }
        userPO.setUserAccount(req.getAdminAccount());
        userPO.setUserPwd(req.getAdminPassword());
        userPO.setCreatedBy(req.getCreatedBy());

        userPO.setOrgCode(code);
        int examine = userService.examineUserAccountOrUserTel(userPO);
        if (examine > 0) {
            throw new CustomException(ErrorCodeEnum.ACCOUNT_OR_TEL_ALREADY.errorNo, ErrorCodeEnum.ACCOUNT_OR_TEL_ALREADY.errorCode, ErrorCodeEnum.ACCOUNT_OR_TEL_ALREADY.errorDesc);
        }
        int userId = userService.addAdminUser(userPO, EndTypeEnum._3.no);
        if (userId == -1) {
            throw new CustomException(ErrorCodeEnum.MANAGE_ALREADY.errorNo, ErrorCodeEnum.MANAGE_ALREADY.errorCode, ErrorCodeEnum.MANAGE_ALREADY.errorDesc);
        }

        agentCompanyPO.setUserName(req.getAdminName());
        if (req.getSettlementType() != 3) {
            agentCompanyPO.setBalance(new BigDecimal(req.getCreditLine()));
            agentCompanyPO.setCreditLine(req.getCreditLine());
        }
        agentCompanyPO.setUserTel(req.getAdminTel());
        agentCompanyPO.setUserNumber(req.getAdminAccount());
        agentCompanyPO.setSaleManagerId(userId);
        agentCompanyPO.setSettlementType(req.getSettlementType());
        agentCompanyPO.setSettlementCurrency(req.getSettlementCurrency());
        agentCompanyPO.setCompanyCode(req.getCompanyCode());
        agentCompanyPO.setAvailableStatus(AvailableEnum.Start.key);
        agentCompanyPO.setCreatedDt(createdate);
        agentCompanyPO.setCreatedBy(req.getCreatedBy());
        agentCompanyPO.setOrgId(orgPO.getOrgId());
        agentCompanyPO.setDecimalPlaces(req.getDecimalPlaces());
        agentCompanyPO.setRoundingType(req.getRoundingType());
        agentCompanyPO.setInvoiceId(req.getInvoiceId());
        agentCompanyPO.setSettledType(req.getSettledType());
        agentCompanyPO.setRemark(req.getRemark());
        agentCompanyPO.setDomesticOrOverseas(req.getDomesticOrOverseas());
        agentCompanyPO.setSettlementStrategy(req.getSettlementStrategy());
        agentCompanyPO.setPushSettleCostSwitch(req.getPushSettleCostSwitch());
        agentCompanyPO.setPushSettleCostUrl(req.getPushSettleCostUrl());
        // 设置额度预警相关字段
        agentCompanyPO.setCreditWarningEnabled(req.getCreditWarningEnabled());
        agentCompanyPO.setWarningAmount(req.getWarningAmount());
        agentCompanyPO.setWarningEmails(req.getWarningEmails());
        // 设置额度账号类型
        agentCompanyPO.setLineAccountType(req.getLineAccountType());
        // 设置额度账号
        if (req.getLineAccountType() != null && req.getLineAccountType() == LineAccountTypeEnum.OTHER.key) {
            agentCompanyPO.setLineAccount(req.getLineAccount());
        } else {
            agentCompanyPO.setLineAccount(code);
        }
        agentCompanyMapper.insert(agentCompanyPO);

        //OrgPO supplierAddPO1 = orgMapper.selectByPrimaryKey(orgPO.getOrgId());


        // 加载分销商配置到redis中 AgentAccountConfig
        Map<String, String> accountMap = Maps.newHashMap();
        AgentAccountConfig agentAccountConfig = new AgentAccountConfig();
        agentAccountConfig.setId(agentCompanyPO.getOrgId());
        agentAccountConfig.setAgentName(req.getAgentName());
        agentAccountConfig.setAgentCode(code);
        //agentAccountConfig.setChannelCode(supplierAddPO1.getChannelCode());
        agentAccountConfig.setChannelName(ChannelEnum.B2B.value);
//            agentAccountConfig.setPartnerCode(supplierAddPO1.getOrgCode());
//            agentAccountConfig.setSecretkey(secretKey);
        agentAccountConfig.setCompanyCode(req.getCompanyCode());
        agentAccountConfig.setAvailableStatus(AvailableEnum.Start.key);
        agentAccountConfig.setLinkmanTel(req.getAgentTel());
        agentAccountConfig.setLinkman(req.getAgentName());
        agentAccountConfig.setSettlementType(req.getSettlementType());
        agentAccountConfig.setBalance(agentCompanyPO.getBalance());
        agentAccountConfig.setSaleManagerId(req.getSaleManagerId());
        agentAccountConfig.setDecimalPlaces(req.getDecimalPlaces());
        agentAccountConfig.setRoundingType(req.getRoundingType());
        agentAccountConfig.setSettlementCurrency(req.getSettlementCurrency());
        agentAccountConfig.setInvoiceId(req.getInvoiceId());
        agentAccountConfig.setSettledType(req.getSettledType());
        agentAccountConfig.setRemark(req.getRemark());
        agentAccountConfig.setDomesticOrOverseas(req.getDomesticOrOverseas());
        agentAccountConfig.setSettlementStrategy(req.getSettlementStrategy());
        agentAccountConfig.setPushSettleCostSwitch(req.getPushSettleCostSwitch());
        agentAccountConfig.setPushSettleCostUrl(req.getPushSettleCostUrl());
        // 设置额度账号类型
        agentAccountConfig.setLineAccountType(req.getLineAccountType());
        // 设置额度账号
        if (req.getLineAccountType() != null && req.getLineAccountType() == LineAccountTypeEnum.OTHER.key) {
            agentAccountConfig.setLineAccount(req.getLineAccount());
        } else {
            agentAccountConfig.setLineAccount(code);
        }
        accountMap.put(code, JSON.toJSONString(agentAccountConfig));
        RedisTemplateX.hPutAll(RedisKey.AGENT_ACCOUNT_CONFIG, accountMap);

        // 注册事务同步器
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                // 事务提交后，加载客户供应商可见性
                loadAgentSupplyAvailable(code);
            }
        });
        //消息预警
        agentCreditWarningService.sendWarningMsg(agentAccountConfig.getAgentCode());
    }

    /**
     * 加载客户供应商可见性
     */
    private void loadAgentSupplyAvailable(String agentCode) {
        CompletableFuture.runAsync(() -> RedisTemplateX.setAdd(RedisKey.RELOAD_AGENT_AVAILABLE_CACHE, agentCode),processingCommonExecutor);
    }

    @Override
    public void addPartner(AddPartnerReq req) {
        OrgAgentApiConfigEntity entity = new OrgAgentApiConfigEntity();
        if (req.getId() == null) {
            // 新增分销商api配置信息
            BeanUtils.copyProperties(req, entity);
            String zfcAll = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890!@#$%^&*";
            char[] zfc = zfcAll.toCharArray();
            String secretKey = RandomStringUtils.random(32, zfc);
            entity.setSecretKey(secretKey);
            entity.setCreatedDt(new Date());

            //获取编码
            String code = getPartnerCode(SystemCodeEnum.PARTNERCODE.code);
            String[] partnerCodeStr = code.split(SystemCodeEnum.PARTNERCODE.letter);
            //组装合作商编码 TP1--国内  TP2--海外
            String partnerCode = SystemCodeEnum.PARTNERCODE.letter + entity.getDomesticOrOverseas() + partnerCodeStr[1];
            entity.setPartnerCode(partnerCode);
            //判断该类型的合作商是否创建过了
            OrgAgentApiConfigEntity agentApiConfigEntity = orgAgentApiConfigMapper.selectOne(new LambdaQueryWrapper<OrgAgentApiConfigEntity>()
                    .eq(OrgAgentApiConfigEntity::getAgentCode, entity.getAgentCode())
                    .eq(OrgAgentApiConfigEntity::getDomesticOrOverseas, entity.getDomesticOrOverseas())
            );
            if (agentApiConfigEntity != null) {
                throw new SysException(ErrorCodeEnum.PARTNER_IS_ALREADY);
            }
            entity.setAvailableStatus(AvailableEnum.Start.key);
            orgAgentApiConfigMapper.insert(entity);

            // 加载初始化频率到redis中
            List<OrgAgentApiInvokeConfigResp> orgAgentApiInvokeConfigResps = orgAgentApiInvokeConfigMapper.queryInitInvoke();
            Map<String, String> map = Maps.newHashMap();
            orgAgentApiInvokeConfigResps.forEach(orgAgentApiInvokeConfigResp -> {
                String key = partnerCode.concat("_").concat(orgAgentApiInvokeConfigResp.getMethodCode());
                orgAgentApiInvokeConfigResp.setAgentCode(req.getAgentCode());
                map.put(key, JSON.toJSONString(orgAgentApiInvokeConfigResp));
            });
            RedisTemplateX.hPutAll(RedisKey.AGENT_API_CONFIG_REDIS_KEY, map);

        } else {
            entity = orgAgentApiConfigMapper.selectById(req.getId());
            String partnerCode = entity.getPartnerCode();
            BeanUtils.copyProperties(req, entity);
            entity.setPartnerCode(partnerCode);
            entity.setUpdatedDt(new Date());

            orgAgentApiConfigMapper.updateById(entity);
        }

//        //合作商配置加载到缓存
//        OrgAgentApiConfigEntity apiConfigEntity = new OrgAgentApiConfigEntity();
//
//        apiConfigEntity.setOrderStatusNotifyUrl(entity.getOrderStatusNotifyUrl());
//        apiConfigEntity.setOrderCheckDetailNotifyUrl(entity.getOrderCheckDetailNotifyUrl());
//        apiConfigEntity.setOrderRefundNotifyUrl(entity.getOrderRefundNotifyUrl());
//        apiConfigEntity.setInvoiceNotifyUrl(entity.getInvoiceNotifyUrl());
//        apiConfigEntity.setOrderPushNotifyUrl(entity.getOrderPushNotifyUrl());
//        apiConfigEntity.setWarrantiesNotifyUrl(entity.getWarrantiesNotifyUrl());
//        apiConfigEntity.setSecretKey(entity.getSecretKey());
//        apiConfigEntity.setAvailableStatus(AvailableEnum.Start.key);
//        apiConfigEntity.setDomesticOrOverseas(req.getDomesticOrOverseas());
//        apiConfigEntity.setPartnerCode(entity.getPartnerCode());
//        apiConfigEntity.setAgentCode(entity.getAgentCode());
        HashMap<String, String> agentMap = new HashMap<>();
        agentMap.put(entity.getPartnerCode(), JSON.toJSONString(entity));
        RedisTemplateX.hPutAll(RedisKey.PARTNER_ACCOUNT_CONFIG, agentMap);

        // 绑定合作商编码和客户编码
        RedisTemplateX.set(RedisKey.PARTNER_CODE_GET_AGENT_CODE.concat(entity.getPartnerCode()), entity.getAgentCode());
    }

    /**
     * 修改客户api配置信息
     */
    @Override
    public void modifyAgentApiConfig(AgentApiConfigUpdateReq request) {
        OrgAgentApiConfigEntity entity = new OrgAgentApiConfigEntity();
        BeanUtils.copyProperties(request, entity);
        entity.setUpdatedDt(new Date());

        orgAgentApiConfigMapper.updateById(entity);
        OrgAgentApiConfigEntity orgAgentApiConfigEntity = orgAgentApiConfigMapper.selectById(entity.getId());
        AgentAccountConfig agentAccountConfig = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.AGENT_ACCOUNT_CONFIG, orgAgentApiConfigEntity.getAgentCode()), AgentAccountConfig.class);
        Optional.ofNullable(agentAccountConfig).ifPresent(agentAccount ->
        {
            agentAccount.setOrderStatusPushUrl(entity.getOrderStatusNotifyUrl());
            agentAccount.setOrderCheckDetailNotifyUrl(entity.getOrderCheckDetailNotifyUrl());
            agentAccount.setOrderRefundNotifyUrl(entity.getOrderRefundNotifyUrl());
            agentAccount.setInvoiceNotifyUrl(entity.getInvoiceNotifyUrl());
            agentAccount.setOrderPushNotifyUrl(entity.getOrderPushNotifyUrl());
            agentAccount.setWarrantiesNotifyUrl(entity.getWarrantiesNotifyUrl());
            //agentAccount.setSecretkey(entity.getSecretKey());
            HashMap<String, String> map = new HashMap<>();
            map.put(orgAgentApiConfigEntity.getAgentCode(), JSON.toJSONString(agentAccount));
            RedisTemplateX.hPutAll(RedisKey.AGENT_ACCOUNT_CONFIG, map);
        });
    }

    /**
     * 生产唯一合作商编码
     */
    public String getPartnerCode(String codeName) {
        String code = RedisTemplateX.lRightPop(codeName);
        if (null == code) {
            Map<String, String> requestMap = new HashMap<>();
            requestMap.put("seqName", codeName);
            sequenceRemote.createCode(requestMap);
            code = RedisTemplateX.lRightPop(codeName);
        }
        return code;
    }

    /**
     * 修改客户启用状态
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modifyAgentStatus(AgentUpdateStatusReq req) {
        OrgPO orgPO = new OrgPO();
        UserPO userPO = new UserPO();
        AgentCompanyPO agentCompanyPO = new AgentCompanyPO();
        orgPO.setAvailableStatus(req.getAvailableStatus());
        orgPO.setOrgId(req.getAgentId());
        Date date = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        //获取String类型的时间
        String createdate = sdf.format(date);
        orgPO.setUpdatedBy(req.getUpdatedBy());
        orgPO.setUpdatedDt(createdate);
        orgPO.setOrgId(req.getAgentId());
        agentCompanyPO.setUpdatedBy(req.getUpdatedBy());
        agentCompanyPO.setUpdatedDt(createdate);
        agentCompanyPO.setOrgId(req.getAgentId());
        agentCompanyPO.setAvailableStatus(req.getAvailableStatus());
        OrgPO orgPO1 = orgMapper.selectByPrimaryKey(orgPO);
        Integer userId = orgMapper.getUserId(orgPO1.getOrgCode());
        if (userId != null) {
            userPO.setOrgCode(orgPO1.getOrgCode());
            userPO.setAvailableStatus(req.getAvailableStatus());
            userPO.setUpdatedBy(req.getUpdatedBy());
            userPO.setUpdatedDt(createdate);
            orgMapper.updateAdminStatus(userPO);

            //判断是否需要删除客户登录token
            if (req.getAvailableStatus().intValue() == AvailableEnum.End.key) {
                //查询改客户下的可登录用户
                List<UserListDTO> userListByOrgCode = orgMapper.getUserListByOrgCode(orgPO1.getOrgCode());
                for (UserListDTO dto : userListByOrgCode) {
                    if (dto == null) {
                        break;
                    }
                    String token = RedisTemplateX.get(StrUtilX.concat(ValidatedFilter.MULTI_USER_LOGIN_ACCOUNT, dto.getUserAccount()));
                    if (StrUtilX.isNotEmpty(token)) {
                        RedisTemplateX.delete(token);
                    }
                }
            }
        }
        orgMapper.updateAgentStatus(agentCompanyPO);
        orgMapper.updateByPrimaryKeySelective(orgPO);

        //客户禁用之后，合作商全部禁用
        if (agentCompanyPO.getAvailableStatus().intValue() == AvailableEnum.End.key.intValue()) {
            PartnerStatusReq statusReq = new PartnerStatusReq();
            statusReq.setAgentCode(req.getAgentCode());
            statusReq.setAvailableStatus(req.getAvailableStatus());
            orgMapper.updateAgentPartnerStatus(statusReq);

            //查询该客户下面的所有合作商，更新他们的缓存状态
            QueryWrapper<OrgAgentApiConfigEntity> wrapper = new QueryWrapper<>();
            wrapper.eq("agent_code", req.getAgentCode());
            wrapper.eq("deleted", BaseEnum.NOT_DELETE.getKey());
            List<OrgAgentApiConfigEntity> list = orgAgentApiConfigMapper.selectList(wrapper);
            for (OrgAgentApiConfigEntity entity : list) {
                OrgAgentApiConfigEntity orgAgentApiConfigEntity = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.PARTNER_ACCOUNT_CONFIG, entity.getPartnerCode()), OrgAgentApiConfigEntity.class);
                Optional.ofNullable(orgAgentApiConfigEntity).ifPresent(apiConfigEntity ->
                {
                    apiConfigEntity.setAvailableStatus(req.getAvailableStatus());
                    HashMap<String, String> agentMap = new HashMap<>();
                    agentMap.put(entity.getPartnerCode(), JSON.toJSONString(apiConfigEntity));
                    RedisTemplateX.hPutAll(RedisKey.PARTNER_ACCOUNT_CONFIG, agentMap);
                });
            }
        }

        // 禁用启用
        AgentAccountConfig agentAccountConfig = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.AGENT_ACCOUNT_CONFIG, orgPO1.getOrgCode()), AgentAccountConfig.class);
        Optional.ofNullable(agentAccountConfig).ifPresent(agentAccount ->
        {
            agentAccountConfig.setAvailableStatus(req.getAvailableStatus());
            RedisTemplateX.hPut(RedisKey.AGENT_ACCOUNT_CONFIG, orgPO1.getOrgCode(), JSON.toJSONString(agentAccountConfig));
        });

        // 注册事务同步器
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                // 事务提交后，加载客户供应商可见性
                loadAgentSupplyAvailable(req.getAgentCode());
            }
        });
    }

    @Override
    public void modifyPartnerStatus(PartnerStatusReq req) {
        // 修改合作商的启用状态
        orgMapper.updateAgentPartnerStatus(req);

        // 更新缓存状态
        OrgAgentApiConfigEntity orgAgentApiConfigEntity = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.PARTNER_ACCOUNT_CONFIG, req.getPartnerCode()), OrgAgentApiConfigEntity.class);
        Optional.ofNullable(orgAgentApiConfigEntity).ifPresent(apiConfigEntity ->
        {
            apiConfigEntity.setAvailableStatus(req.getAvailableStatus());
            HashMap<String, String> agentMap = new HashMap<>();
            agentMap.put(req.getPartnerCode(), JSON.toJSONString(apiConfigEntity));
            RedisTemplateX.hPutAll(RedisKey.PARTNER_ACCOUNT_CONFIG, agentMap);
        });
    }

    /**
     * 修改客户信息
     */
    @Override
    @Transient
    public void modifyAgent(AgentUpdateReq req) {
        // 校验客户名是否重复
        OrgPO orgPO1 = new OrgPO();
        orgPO1.setType(1);
        orgPO1.setOrgName(req.getAgentName());
        List<OrgPO> org = orgMapper.select(orgPO1);
        if (org != null) {
            for (OrgPO orgPO : org) {
                if (orgPO.getOrgId().equals(req.getAgentId())) {
                    continue;
                }
                throw new SysException(ErrorCodeEnum.AGENT_NAME_IS_ALREADY);
            }
        }

        Date date = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        //获取String类型的时间
        String createdate = sdf.format(date);

        //if (req.getChannelCode().equals(ChannelEnum.B2B.key) || req.getChannelCode().equals(ChannelEnum.B2C.key)) {
        OrgPO orgPO = new OrgPO();
        orgPO.setOrgName(req.getAgentName());
        orgPO.setOrgId(req.getAgentId());
        orgPO.setUpdatedDt(createdate);
        orgPO.setUpdatedBy(req.getUpdatedBy());
        orgPO.setAvailableStatus(AvailableEnum.Start.key);
        orgPO.setUpdatedDt(createdate);
        orgPO.setUpdatedBy(req.getUpdatedBy());
        orgPO.setChannelCode(req.getChannelCode());
        orgPO.setOrgDomian(req.getOrgDomain());
        orgPO.setOrgUniqueCode(req.getOrgUniqueCode());
        orgPO.setCountryName(req.getCountryName());
        orgPO.setCountryCode(req.getCountryCode());
        //修复#10575 【客户管理-基础信息】所在国家地区字段后端没有返回该字段
        orgPO.setProvinceName(req.getProvinceName());
        orgPO.setProvinceCode(req.getProvinceCode());
        //修复#10575 【客户管理-基础信息】所在国家地区字段后端没有返回该字段
        orgPO.setCityName(req.getCityName());
        orgPO.setCityCode(req.getCityCode());
        orgPO.setOrgAddress(req.getOrgAddress());
        orgPO.setEstablishedDate(DateUtilX.stringToDate(req.getEstablishedDate()));
        orgMapper.updateByPrimaryKeySelective(orgPO);

        if (StrUtilX.isNotEmpty(req.getAdminName()) && StrUtilX.isNotEmpty(req.getAdminAccount())) {
            UserPO userPO = new UserPO();
            userPO.setUserName(req.getAdminName());
            userPO.setUserAccount(req.getAdminAccount());
            userPO.setUserTel(req.getAdminTel());
            userPO.setCreatedBy(req.getCreatedBy());

            OrgPO agent = orgMapper.selectByPrimaryKey(req.getAgentId());
            Integer userId = orgMapper.getUserId(agent.getOrgCode());
            if (userId == null) {
                int examine = userService.examineUserAccountOrUserTel(userPO);
                if (examine > 0) {
                    throw new CustomException(ErrorCodeEnum.ACCOUNT_OR_TEL_ALREADY.errorNo, ErrorCodeEnum.ACCOUNT_OR_TEL_ALREADY.errorCode, ErrorCodeEnum.ACCOUNT_OR_TEL_ALREADY.errorDesc);
                }
                userPO.setOrgCode(agent.getOrgCode());
                userPO.setCreatedBy(req.getCreatedBy());
                userService.addAdminUser(userPO, EndTypeEnum._3.no);
            } else {
                userPO.setUpdatedBy(req.getUpdatedBy());
                userPO.setUserId(userId);
                int examine = userService.examineUserAccountOrUserTel(userPO);
                if (examine > 0) {
                    throw new CustomException(ErrorCodeEnum.ACCOUNT_OR_TEL_ALREADY.errorNo, ErrorCodeEnum.ACCOUNT_OR_TEL_ALREADY.errorCode, ErrorCodeEnum.ACCOUNT_OR_TEL_ALREADY.errorDesc);
                }
                int modifyAdminUser = userService.modifyAdminUser(userPO);
                if (modifyAdminUser == -1) {
                    throw new CustomException(ErrorCodeEnum.UPDATE_MANAGE_MESSAGE_ERROR.errorNo, ErrorCodeEnum.UPDATE_MANAGE_MESSAGE_ERROR.errorCode, ErrorCodeEnum.UPDATE_MANAGE_MESSAGE_ERROR.errorDesc);
                }
            }
        }

        AgentCompanyPO agentCompanyPO = new AgentCompanyPO();
        Integer agentCompany = orgMapper.getAgentCompany(req.getAgentId());
        AgentCompanyPO agentCompanyPO1 = agentCompanyMapper.selectByPrimaryKey(agentCompany);
        BeanUtils.copyProperties(agentCompanyPO1, agentCompanyPO);
        //修改
        agentCompanyPO.setUserTel(req.getAgentTel());
        agentCompanyPO.setUserName(req.getAdminName());
        agentCompanyPO.setUserNumber(req.getAdminAccount());
        //agentCompanyPO.setSaleManagerId(req.getSaleManagerId());
        agentCompanyPO.setSettlementType(req.getSettlementType());
        agentCompanyPO.setCompanyCode(CompanyDTO.COMPANY_CODE);
        agentCompanyPO.setAvailableStatus(AvailableEnum.Start.key);
        agentCompanyPO.setCreditLine(req.getCreditLine());
        BigDecimal sum = new BigDecimal(StrUtilX.isNotEmpty(req.getCreditLine()) ? req.getCreditLine() : "0");

        sum = sum.subtract(new BigDecimal(agentCompanyPO1.getCreditLine() == null ? "0" : agentCompanyPO1.getCreditLine())).add(agentCompanyPO1.getBalance() == null ? BigDecimal.ZERO : agentCompanyPO1.getBalance());
        agentCompanyPO.setBalance(sum);
        agentCompanyPO.setACompanyId(agentCompany);
        agentCompanyPO.setInvoiceId(req.getInvoiceId());
        agentCompanyPO.setSettledType(req.getSettledType());
        agentCompanyPO.setRemark(req.getRemark());
        //agentCompanyPO.setDomesticOrOverseas(req.getDomesticOrOverseas());
        agentCompanyPO.setSettlementStrategy(req.getSettlementStrategy());
        agentCompanyPO.setPushSettleCostSwitch(req.getPushSettleCostSwitch());
        agentCompanyPO.setPushSettleCostUrl(req.getPushSettleCostUrl());
        // 设置额度预警相关字段
        agentCompanyPO.setCreditWarningEnabled(req.getCreditWarningEnabled());
        agentCompanyPO.setWarningAmount(req.getWarningAmount());
        agentCompanyPO.setWarningEmails(req.getWarningEmails());
        // 设置额度账号类型
        agentCompanyPO.setLineAccountType(req.getLineAccountType());
        // 设置额度账号
        if (req.getLineAccountType() != null && req.getLineAccountType() == LineAccountTypeEnum.OTHER.key) {
            agentCompanyPO.setLineAccount(req.getLineAccount());
        } else {
            agentCompanyPO.setLineAccount(req.getAgentCode());
        }
        agentCompanyMapper.updateByPrimaryKeySelective(agentCompanyPO);

        // 修改缓存信息
        AgentAccountConfig agentAccountConfig = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.AGENT_ACCOUNT_CONFIG, req.getAgentCode()), AgentAccountConfig.class);
        if (agentAccountConfig == null) {
            agentAccountConfig = new AgentAccountConfig();
        }
        BeanUtils.copyProperties(agentCompanyPO, agentAccountConfig);
        agentAccountConfig.setLinkmanTel(req.getAgentTel());
        agentAccountConfig.setLinkman(req.getAgentName());
        agentAccountConfig.setAgentName(req.getAgentName());
        RedisTemplateX.hPut(RedisKey.AGENT_ACCOUNT_CONFIG, req.getAgentCode(), JSON.toJSONString(agentAccountConfig));
        //消息预警
        agentCreditWarningService.sendWarningMsg(agentAccountConfig.getAgentCode());
    }

    @Override
    public void modifyAgentMath(AgentUpdateMathReq req) {
        Integer agentCompany = orgMapper.getAgentCompany(req.getAgentId());
        AgentCompanyPO agentCompanyPO = new AgentCompanyPO();
        agentCompanyPO.setRoundingType(req.getRoundingType());
        agentCompanyPO.setDecimalPlaces(req.getDecimalPlaces());

        agentCompanyPO.setACompanyId(agentCompany);

        agentCompanyMapper.updateByPrimaryKeySelective(agentCompanyPO);

        //更新分销商缓存里面的数据
        AgentAccountConfig agentAccountConfig = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.AGENT_ACCOUNT_CONFIG, req.getAgentCode()), AgentAccountConfig.class);
        Optional.ofNullable(agentAccountConfig).ifPresent(agentAccount ->
        {
            agentAccount.setDecimalPlaces(req.getDecimalPlaces());
            agentAccount.setRoundingType(req.getRoundingType());
            HashMap<String, String> agentMap = new HashMap<>();
            agentMap.put(req.getAgentCode(), JSON.toJSONString(agentAccount));
            RedisTemplateX.hPutAll(RedisKey.AGENT_ACCOUNT_CONFIG, agentMap);
        });
    }


    /**
     * 修改客户api接口频次信息
     */
    @Override
    public void modifyAgentApiInvokeConfig(OrgAgentApiInvokeConfigReq request) {
        OrgAgentApiInvokeConfigEntity entity = new OrgAgentApiInvokeConfigEntity();
        BeanUtils.copyProperties(request, entity);
        entity.setUpdatedDt(new Date());
        orgAgentApiInvokeConfigMapper.updateById(entity);
        // 加载到缓存中
        cacheAgentApiConfig(entity);
    }

    /**
     * 添加客户api接口频次信息
     */
    @Override
    public void addAgentApiInvokeConfig(OrgAgentApiInvokeConfigReq request) {
        OrgAgentApiInvokeConfigEntity entity = new OrgAgentApiInvokeConfigEntity();
        BeanUtils.copyProperties(request, entity);
        entity.setCreatedDt(new Date());
        orgAgentApiInvokeConfigMapper.insert(entity);
        // 加载到缓存中
        cacheAgentApiConfig(entity);
    }

    @Override
    public void addOrEditAgentApiInvokeConfig(OrgAgentApiInvokeConfigReq request) {
        if (request.getId() != null) {
            modifyAgentApiInvokeConfig(request);
        } else {
            List<OrgAgentApiInvokeConfigEntity> list = orgAgentApiInvokeConfigMapper.selectList(new LambdaQueryWrapper<OrgAgentApiInvokeConfigEntity>()
                            .eq(OrgAgentApiInvokeConfigEntity::getPartnerCode, request.getPartnerCode())
                            .eq(OrgAgentApiInvokeConfigEntity::getMethodCode, request.getMethodCode())
                            .eq(OrgAgentApiInvokeConfigEntity::getDeleted, 0));
            if (CollUtilX.isNotEmpty(list)) {
                request.setId(list.get(0).getId());
                modifyAgentApiInvokeConfig(request);
            } else {
                addAgentApiInvokeConfig(request);
            }
        }
    }

    /**
     * 根据客户code,查询客户详细信息
     */
    @Override
    public AgentSelectDTO queryAgentDetail(AgentQueryDetailByCodeReq req) {
        AgentSelectDTO agentSelectDTO = orgMapper.agentList(req.getAgentCode());
        if (agentSelectDTO == null) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }

        // 查询确认函配置
        AgentConfirmationLetterConfigResp agentConfirmationLetterConfigResp = agentConfirmationLetterConfigService.getByAgentCode(req.getAgentCode());
        agentSelectDTO.setAgentConfirmationLetterConfig(agentConfirmationLetterConfigResp);


        BankListDTO bankListDTO = new BankListDTO();
        bankListDTO.setOrgCode(req.getAgentCode());
        bankListDTO.setActive(DeleteEnum.STATUS_EXIST.key);
        List<BankSupplierDTO> bankAddDTOS = orgMapper.bankList(bankListDTO);
        List<ContactSupplierDTO> contactAddDTOS = orgMapper.concactList(req.getAgentCode(), DeleteEnum.STATUS_EXIST.key, 0);
        List<ContactSupplierDTO> ownContactAddDTOS = orgMapper.concactList(req.getAgentCode(), DeleteEnum.STATUS_EXIST.key, 1);
        //查询分销商api配置信息
        QueryWrapper<OrgAgentApiConfigEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("agent_code", req.getAgentCode());
        wrapper.eq("deleted", BaseEnum.NOT_DELETE.getKey());
        List<OrgAgentApiConfigEntity> list = orgAgentApiConfigMapper.selectList(wrapper);
        if (CollUtilX.isNotEmpty(list)) {
            List<OrgAgentApiConfigListResp> listResp = new ArrayList<>();
            for (OrgAgentApiConfigEntity entity : list) {
                OrgAgentApiConfigListResp configListResp = new OrgAgentApiConfigListResp();
                OrgAgentApiConfigResp orgAgentApiConfigResp = new OrgAgentApiConfigResp();
                BeanUtils.copyProperties(entity, orgAgentApiConfigResp);
                List<OrgAgentApiInvokeConfigResp> orgAgentApiInvokeConfigRespList = orgAgentApiInvokeConfigMapper
                        .queryInvokeByAgentCode(entity.getPartnerCode());

                configListResp.setOrgAgentApiConfig(orgAgentApiConfigResp);
                configListResp.setInvokeList(orgAgentApiInvokeConfigRespList);
                configListResp.setDomesticOrOverseas(entity.getDomesticOrOverseas());
                listResp.add(configListResp);
            }

            //封装分销商配置信息
            agentSelectDTO.setOrgAgentApiConfigList(listResp);
        }

        if (!bankAddDTOS.isEmpty()) {
            agentSelectDTO.setBankCardList(bankAddDTOS);
        }
        if (!contactAddDTOS.isEmpty()) {
            agentSelectDTO.setContactList(contactAddDTOS);
        }
        if (!ownContactAddDTOS.isEmpty()) {
            agentSelectDTO.setOwnContactAddDTOS(ownContactAddDTOS);
        }

        //封装个人信息付款账户
        List<BankAgentListDTO> bankAgentListDTOS = orgMapper.bankListByAgentId(agentSelectDTO.getAgentId());
        agentSelectDTO.setBankAgentListDTOList(bankAgentListDTOS);

        //客户是否开启优选产品
        Object preferredProductsStatusObject = RedisTemplateX.hashGet(RedisKey.AGENT_PREFERRED_PRODUCTS_STATUS, agentSelectDTO.getAgentCode());
        if (Objects.isNull(preferredProductsStatusObject) || "1".equals(preferredProductsStatusObject.toString())) {
            //默认开启
            agentSelectDTO.setPreferredProductsStatus(1);
        } else {
            agentSelectDTO.setPreferredProductsStatus(0);
        }

        // 设置额度账户名称
        if (agentSelectDTO.getLineAccountType() != null && agentSelectDTO.getLineAccountType() == LineAccountTypeEnum.OTHER.key) {
            AgentAccountConfig agentAccountConfig = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.AGENT_ACCOUNT_CONFIG, agentSelectDTO.getLineAccount()), AgentAccountConfig.class);
            if (agentAccountConfig != null) {
                agentSelectDTO.setLineAccountName(agentAccountConfig.getAgentName());
                agentSelectDTO.setLineAccountBalance(agentAccountConfig.getBalance());
            }
        } else {
            agentSelectDTO.setLineAccountName(agentSelectDTO.getAgentName());
            if (StrUtilX.isNotEmpty(agentSelectDTO.getBalance())) {
                agentSelectDTO.setLineAccountBalance(new BigDecimal(agentSelectDTO.getBalance()));
            }
        }
        return agentSelectDTO;
    }

    @Override
    public AgentSelectDTO queryAgentBaseInfo(AgentQueryDetailByCodeReq request) {
        return orgMapper.agentList(request.getAgentCode());
    }

    @Override
    public PaginationSupportDTO<QueryAgentListDTO> queryAgentList(AgentListRequest request) {
        PageHelper.startPage(request.getCurrentPage(), request.getPageSize());
        List<QueryAgentListDTO> list = orgMapper.queryAgentList(request);
        PageInfo<QueryAgentListDTO> page = new PageInfo<>(list);
        PaginationSupportDTO<QueryAgentListDTO> paginationSupport = new PaginationSupportDTO<>();
        paginationSupport.setItemList(list);
        paginationSupport.setPageSize(page.getPageSize());
        paginationSupport.setTotalCount(page.getTotal());
        paginationSupport.setTotalPage(page.getPages());
        paginationSupport.setCurrentPage(page.getPageNum());
        return paginationSupport;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AgentCreditLineResultDTO deductRefundCreditLine(List<AgentCreditLineDTO> agentCreditLineList) {
        int successCount = 0;

        for (AgentCreditLineDTO agentCreditLineDTO : agentCreditLineList) {
            //记录日志
            AgentSelectDTO agentSelectDTO = orgMapper.agentList(agentCreditLineDTO.getAgentCode());
            AgentCreditLinePO agentCreditLinePO = new AgentCreditLinePO();
            agentCreditLinePO.setAgentCode(agentCreditLineDTO.getAgentCode());
            agentCreditLinePO.setOrderCode(agentCreditLineDTO.getOrderCode());
            agentCreditLinePO.setCreatedBy(agentCreditLineDTO.getCreatedBy());
            agentCreditLinePO.setAgentCreditLineId(agentCreditLineDTO.getAgentCreditLineId());
            agentCreditLinePO.setDeductRefundCreditLine(CommonTgUtils.formatBigDecimal(agentCreditLineDTO.getDeductRefundCreditLine()).toString());
            agentCreditLinePO.setCreatedDt(DateUtilX.dateToString(new Date(), "yyyy-MM-dd HH:mm:ss"));
            agentCreditLineMapper.insert(agentCreditLinePO);
            boolean balance = orgMapper.updateBalance(agentSelectDTO.getAgentId(), agentCreditLineDTO.getDeductRefundCreditLine());
            // 统计成功更新的记录数
            if (balance) {
                successCount++;
            }
            // 同时修改缓存配额
            if (balance && agentCreditLineDTO.getDeductRefundCreditLine() != null) {
                String agentCode = agentCreditLineList.get(0).getAgentCode();
                String agent = RedisTemplateX.hashGet(RedisKey.AGENT_ACCOUNT_CONFIG, agentCode).toString();
                AgentAccountConfig accountConfig = JSON.parseObject(agent, AgentAccountConfig.class);
                if (accountConfig.getBalance() != null) {
                    accountConfig.setBalance(accountConfig.getBalance().add(agentCreditLineDTO.getDeductRefundCreditLine()));
                    RedisTemplateX.hPut(RedisKey.AGENT_ACCOUNT_CONFIG, agentCode, JSONObject.toJSONString(accountConfig));
                }
            }
        }
        //发送额度预警检查消息
        if(CollUtilX.isNotEmpty(agentCreditLineList)){
            for (String agentCode : agentCreditLineList.stream().map(AgentCreditLineDTO::getAgentCode).filter(StrUtilX::isNotEmpty)
                    .distinct().collect(Collectors.toList())) {
                agentCreditWarningService.sendWarningMsg(agentCode);
            }
        }
        AgentCreditLineResultDTO agentCreditLineResultDTO = new AgentCreditLineResultDTO();
        agentCreditLineResultDTO.setUpdateCount(successCount);
        return agentCreditLineResultDTO;
    }


    /**
     * 缓存客户api频率限制
     */
    private void cacheAgentApiConfig(OrgAgentApiInvokeConfigEntity entity) {
        String key = entity.getPartnerCode().concat("_").concat(entity.getMethodCode());
        Map<String, String> map = Maps.newHashMap();
        map.put(key, JSON.toJSONString(entity));
        RedisTemplateX.hPutAll(RedisKey.AGENT_API_CONFIG_REDIS_KEY, map);
    }

    /**
     * 修改API密钥
     */
    @Override
    public void modifySecret(OrgAgentApiSecretReq req) {
        OrgAgentApiConfigEntity orgAgentApiConfig = new OrgAgentApiConfigEntity();
        orgAgentApiConfig.setId(req.getId());
        orgAgentApiConfig.setSecretKey(req.getSecretKey());
        orgAgentApiConfig.setUpdatedBy(req.getUpdatedBy());
        orgAgentApiConfig.setUpdatedDt(new Date());
        orgAgentApiConfigMapper.updateById(orgAgentApiConfig);
    }

    /**
     * 验证密码获取手机号
     */
    @Override
    public String getPhoneAsPassword(ContactAsPhoneDTO contact) {
        Assert.notNull(contact.getPassword(), ErrorCodeEnum.PASSWORD_IS_NOT_EMPTY.errorDesc);

        // 查询用户并验证密码
        UserPO user = userMapper.selectByPrimaryKey(contact.getUserId());
        String encryptUserPassword = SM4Utils.encrypt(contact.getPassword(), Sm4O.defaultKey);
        if (!encryptUserPassword.equals(user.getUserPwd())) {
            throw new SysException(ErrorCodeEnum.PASSWORD_IS_ERROR.errorCode, ErrorCodeEnum.PASSWORD_IS_ERROR.errorDesc);
        }
        AgentCompanyPO companyPO = new AgentCompanyPO();
        companyPO.setOrgId(contact.getOrgId());
        AgentCompanyPO org = agentCompanyMapper.selectOne(companyPO);
        String userTel = null;
        if (org != null && StrUtilX.isNotEmpty(org.getUserTel())) {
            userTel = org.getUserTel();
        }
        return userTel;
    }

    /**
     * 修改客户手机号
     */
    @Override
    public void modifyAgentPhone(ContactAsPhoneDTO contact) {
        // 如果手机号存在需要验证
        if (StrUtilX.isNotEmpty(contact.getTel()) && !Validator.isMobile(contact.getTel())) {
            throw new SysException(ErrorCodeEnum.PLEASE_INPUT_CORRECT_PHONE.errorCode, ErrorCodeEnum.PLEASE_INPUT_CORRECT_PHONE.errorDesc);
        }
        // 判断新旧手机号是否一致
        AgentCompanyPO companyPO = new AgentCompanyPO();
        companyPO.setOrgId(contact.getOrgId());
        AgentCompanyPO select = agentCompanyMapper.selectOne(companyPO);
        if (contact.getTel().equals(select.getUserTel())) {
            throw new SysException(ErrorCodeEnum.OLD_PHONE_IS_SAME.errorCode, ErrorCodeEnum.OLD_PHONE_IS_SAME.errorDesc);
        }

        companyPO.setUserTel(contact.getTel());
        Example example = new Example(AgentCompanyPO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("orgId", contact.getOrgId());
        int i = agentCompanyMapper.updateByExampleSelective(companyPO, example);
        if (i == 0) {
            throw new SysException(ErrorCodeEnum.OPERATE_FAIL);
        }
    }

    @Override
    public void addAgentBank(AgentBankReq req) {
        agentBankService.addAgentBank(req);
    }

    @Override
    public void delAgentBank(AgentBankIdReq req) {
        agentBankService.delAgentBank(req);
    }

    @Override
    public void resetAccountPassword(AgentQueryDetailByCodeReq agentAddDTO) {
        UpdateAccountPwdDTO dto = new UpdateAccountPwdDTO();
        String password = SM4Utils.encrypt(CompanyDTO.PWD, Sm4O.defaultKey);
        dto.setUserPwd(password);
        dto.setSignaturePwd(SM3Utils.encrypt(password));
        dto.setOrgCode(agentAddDTO.getAgentCode());
        boolean resetAccountPassword = userMapper.resetAccountPassword(dto);
        if (!resetAccountPassword) {
            throw new SysException(ErrorCodeEnum.RESET_PASSWORD_IS_ERROR.errorCode, ErrorCodeEnum.RESET_PASSWORD_IS_ERROR.errorDesc);
        }
    }

    @Override
    public Response<QueryOrderAgentDetailResp> queryOrderAgentDetail(QueryOrderAgentDetailReq agentDetailReq) {
        QueryOrderAgentDetailResp resp = orgMapper.queryOrderAgentDetail(agentDetailReq);
        if (resp != null) {
            // 设置额度账户名称
            if (resp.getLineAccountType() != null && resp.getLineAccountType() == LineAccountTypeEnum.OTHER.key) {
                agentDetailReq.setAgentCode(resp.getLineAccount());
                QueryOrderAgentDetailResp agentDetailResp = orgMapper.queryOrderAgentDetail(agentDetailReq);
                if (agentDetailResp != null) {
                    resp.setLineAccountName(agentDetailResp.getAgentName());
                    resp.setCreditLine(agentDetailResp.getCreditLine());
                    resp.setBalance(agentDetailResp.getBalance());
                }
            } else {
                resp.setLineAccountName(resp.getAgentName());
            }
        }
        return Response.success(resp);
    }

    @Override
    public void initPushProtocolOrderSettlementCostAgentCodeToRedisTask(String param) {
        List<String> agentCodeList;
        if (StrUtilX.isNotEmpty(param)) {
            agentCodeList = StrUtilX.stringToList(param, ",");
        } else {
            agentCodeList = agentCompanyMapper.selectPushSettleCostSwitchAgentCodeList();
        }
        if (CollUtilX.isNotEmpty(agentCodeList)) {
            String[] agentCodes = agentCodeList.toArray(new String[0]);
            RedisTemplateX.setAdd(RedisKey.PUSH_PROTOCOL_ORDER_SETTLEMENT_COST_AGENT_CODE, agentCodes);
        }
    }

    @Override
    public void modifyAgentAdditionInfo(AgentAdditionInfoReq req) {
        if (req.getAgentId() == null) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_ID);
        }
        if (StrUtilX.isEmpty(req.getAgentCode())) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_AGENTCODE);
        }
        Integer agentCompany = orgMapper.getAgentCompany(req.getAgentId());
        AgentCompanyPO agentCompanyPO = new AgentCompanyPO();
        agentCompanyPO.setACompanyId(agentCompany);
        agentCompanyPO.setPushSettleCostUrl(req.getPushSettleCostUrl());
        agentCompanyPO.setUpdatedBy(req.getUpdatedBy());
        agentCompanyPO.setUpdatedDt(DateUtilX.dateToString(DateUtilX.getCurrentDate(), DateUtilX.hour_format));
        agentCompanyMapper.updateByPrimaryKeySelective(agentCompanyPO);

        // 修改缓存信息
        AgentAccountConfig agentAccountConfig = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.AGENT_ACCOUNT_CONFIG, req.getAgentCode()), AgentAccountConfig.class);
        if (agentAccountConfig != null) {
            agentAccountConfig.setPushSettleCostUrl(req.getPushSettleCostUrl());
            RedisTemplateX.hPut(RedisKey.AGENT_ACCOUNT_CONFIG, req.getAgentCode(), JSON.toJSONString(agentAccountConfig));
        }
    }

    @Override
    public void loadAgentApiConfigToCache(String partnerCode,String agentCode) {
        if(StrUtil.isNotBlank(partnerCode) && StrUtil.isNotBlank(agentCode)){
            // 加载指定客户APi 配置信息到缓存
            syncAgentApiConfigToCache(partnerCode,agentCode);
        }else {
            // 分页加载所有客户信息到缓存
            int pageNum = 1;
            int pageSize = 100;
            while (true) {
                AgentListRequest agentListRequest = new AgentListRequest();
                agentListRequest.setPageSize(pageSize);
                agentListRequest.setCurrentPage(pageNum);
                agentListRequest.setAvailableStatus(1);
                agentListRequest.setCompanyCode(CompanyDTO.COMPANY_CODE);
                PaginationSupportDTO<QueryAgentListDTO> queryAgentListDTOPaginationSupportDTO = queryAgentList(agentListRequest);
                // 判断是否最后一页
                if (queryAgentListDTOPaginationSupportDTO == null || CollectionUtil.isEmpty(queryAgentListDTOPaginationSupportDTO.getItemList())) {
                    return;
                }
                // 循环加载数据到缓存
                for (QueryAgentListDTO queryAgentListDTO : queryAgentListDTOPaginationSupportDTO.getItemList()) {
                    syncAgentApiConfigToCache(queryAgentListDTO.getPartnerCode(), queryAgentListDTO.getAgentCode());
                }
                // 判断是否最后一页
                if (queryAgentListDTOPaginationSupportDTO.getItemList().size() < pageSize) {
                    return;
                }
                pageNum++;
            }
        }
    }

    @Override
    public void modifyPreferredProductsStatus(PreferredProductsStatusReq request) {
        if (Objects.isNull(request)
                || Objects.isNull(request.getPreferredProductsStatus())
                || (!request.getPreferredProductsStatus().equals(0) && !request.getPreferredProductsStatus().equals(1))) {
            throw new SysException(ParamErrorEnum.INVALID_INPUTPARAM);
        }
        //校验客户编码是否合法
        OrgPO orgPO = new OrgPO();
        orgPO.setType(1);
        orgPO.setOrgCode(request.getAgentCode());
        List<OrgPO> org = orgMapper.select(orgPO);
        if (CollectionUtil.isEmpty(org) || org.size() > 1) {
            throw new SysException(ErrorCodeEnum.AGENT_IS_INVALID.errorCode, ErrorCodeEnum.AGENT_IS_INVALID.errorDesc);
        }
        RedisTemplateX.hashSet(RedisKey.AGENT_PREFERRED_PRODUCTS_STATUS, request.getAgentCode(), String.valueOf(request.getPreferredProductsStatus()));
    }

    @Override
    public AgentConfigDTO getAgentConfig(String partnerCode) {
        AgentAccountConfig agentAccountConfig = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.PARTNER_ACCOUNT_CONFIG, partnerCode), AgentAccountConfig.class);
        if (null == agentAccountConfig){
            return null;
        }
        if (Objects.equals(agentAccountConfig.getAvailableStatus(), 1)) {
            return CommonDtoConvert.INSTANCE.convert(agentAccountConfig);
        }
        return null;
    }

    private void syncAgentApiConfigToCache(String partnerCode, String agentCode) {
        if(StrUtil.isBlank(partnerCode) || StrUtil.isBlank(agentCode)){
            return;
        }
        // 查询客户配置信息
        List<OrgAgentApiInvokeConfigResp> orgAgentApiInvokeConfigRespList = orgAgentApiInvokeConfigMapper.queryInvokeByAgentCode(partnerCode);
        Map<String, String> map = Maps.newHashMap();
        orgAgentApiInvokeConfigRespList.forEach(orgAgentApiInvokeConfigResp -> {
            String key = partnerCode.concat("_").concat(orgAgentApiInvokeConfigResp.getMethodCode());
            orgAgentApiInvokeConfigResp.setAgentCode(agentCode);
            map.put(key, JSON.toJSONString(orgAgentApiInvokeConfigResp));
        });
        // 更新缓存
        RedisTemplateX.hPutAll(RedisKey.AGENT_API_CONFIG_REDIS_KEY, map);
    }

    @Override
    public void setPaymentOvertimeCancelConfig(PaymentOvertimeCancelConfigReq request) {
        //校验客户编码是否合法
        Integer agentCompany = orgMapper.getAgentCompany(request.getAgentId());
        if (Objects.isNull(agentCompany)) {
            throw new SysException(ErrorCodeEnum.AGENT_IS_INVALID.errorCode, ErrorCodeEnum.AGENT_IS_INVALID.errorDesc);
        }

        // 处理等待支付时间
        Double waitTime = request.getPaymentOvertimeCancelTime();
        if (waitTime != null) {
            // 超过24小时设置为24小时
            if (waitTime > 24) {
                waitTime = 24.0;
            }

            // 处理时间精度：小数位小于5向下取整，大于5向上取整，等于5保留
            double decimal = waitTime % 1;
            if (decimal > 0 && decimal < 0.5) {
                waitTime = Math.floor(waitTime);
            } else if (decimal > 0.5 && decimal < 1) {
                waitTime = Math.ceil(waitTime);
            }
        }

        AgentCompanyPO agentCompanyPO = new AgentCompanyPO();
        agentCompanyPO.setACompanyId(agentCompany);
        agentCompanyPO.setPaymentOvertimeCancelSwitch(request.getPaymentOvertimeCancelSwitch());
        agentCompanyPO.setPaymentOvertimeCancelTime(waitTime);
        agentCompanyPO.setUpdatedBy(request.getUpdatedBy());
        agentCompanyPO.setUpdatedDt(DateUtilX.dateToString(DateUtilX.getCurrentDate(), DateUtilX.hour_format));
        agentCompanyMapper.updateByPrimaryKeySelective(agentCompanyPO);

        // 构建配置对象
        PaymentOvertimeCancelConfigDTO config = new PaymentOvertimeCancelConfigDTO();
        config.setPaymentOvertimeCancelSwitch(request.getPaymentOvertimeCancelSwitch());
        config.setPaymentOvertimeCancelTime(waitTime);

        // 存储到Redis
        RedisTemplateX.hashSet(RedisKey.AGENT_PAYMENT_OVERTIME_CONFIG, request.getAgentCode(), JSONUtil.toJsonStr(config));
    }
}
