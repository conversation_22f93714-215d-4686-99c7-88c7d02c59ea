package com.tiangong.organization.service;

import com.tiangong.organization.remote.dto.AgentCreditChangeMessageDTO;
import com.tiangong.organization.remote.dto.AgentCreditWarningConfig;

import java.util.List;

/**
 * 客户信用额度预警服务接口
 *
 * <AUTHOR>
 * @date 2024/12/19
 */
public interface AgentCreditWarningService {

    /**
     * 检查并发送额度预警
     *
     * @param creditChangeMessage 预警信息
     */
    void checkAndSendWarning(AgentCreditChangeMessageDTO creditChangeMessage);

    /**
     * 根据客户编码获取预警配置
     *
     * @param agentCode 客户编码
     * @return 预警配置信息
     */
    List<AgentCreditWarningConfig> getWarningConfig(List<String> agentCode);

    /**
     * 发送预警消息
     *
     * @param agentCode 客户编码
     */
    void sendWarningMsg(String agentCode);
}
