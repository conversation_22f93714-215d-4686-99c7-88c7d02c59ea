package com.tiangong.organization.service.Impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Validator;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.tiangong.common.Response;
import com.tiangong.common.service.SequenceService;
import com.tiangong.config.SettingsConstant;
import com.tiangong.convert.CommonDtoConvert;
import com.tiangong.dto.common.AddSupplierForeignReq;
import com.tiangong.dto.common.CompanyDTO;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.dto.hotel.AddSupplierDTO;
import com.tiangong.enums.*;
import com.tiangong.exception.CustomException;
import com.tiangong.exception.SysException;
import com.tiangong.keys.RedisKey;
import com.tiangong.order.enums.OrderMethodEnum;
import com.tiangong.order.remote.OrderRemote;
import com.tiangong.order.remote.request.AddSupplierAutoChannelDTO;
import com.tiangong.organization.domain.OrgPO;
import com.tiangong.organization.domain.SupplierCompanyPO;
import com.tiangong.organization.domain.SupplierLabelConfigPO;
import com.tiangong.organization.domain.UserPO;
import com.tiangong.organization.domain.dto.BankListDTO;
import com.tiangong.organization.domain.entity.OrgSupplierIncreaseLog;
import com.tiangong.organization.domain.req.*;
import com.tiangong.organization.domain.resp.*;
import com.tiangong.organization.mapper.*;
import com.tiangong.organization.remote.dto.*;
import com.tiangong.organization.service.SupplierLabelConfigService;
import com.tiangong.organization.service.SupplierService;
import com.tiangong.dto.common.AgentSupplierLabelDTO;
import com.tiangong.product.remote.ProductRemote;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.user.mapper.UserMapper;
import com.tiangong.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019/6/19 22:12
 **/
@Service
@Slf4j
public class SupplierServiceImpl implements SupplierService {

    @Autowired
    private OrgMapper orgMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private SupplierCompanyMapper supplierCompanyMapper;

    @Autowired
    private AgentCompanyMapper agentCompanyMapper;

    @Autowired
    private SequenceService sequenceService;

    @Resource
    private OrgSupplierIncreaseLogMapper orgSupplierIncreaseLogMapper;

    @Resource
    private SupplierLabelConfigMapper supplierLabelConfigMapper;

    @Autowired
    private SlsLoggerUtil slsLoggerUtil;

    @Autowired
    private OrderRemote orderRemote;

    @Autowired
    private FileUpUtil fileUpUtil;

    @Autowired
    private SupplierLabelConfigService supplierLabelConfigService;

    @Resource(name = "processingCommonExecutor")
    private ThreadPoolTaskExecutor processingCommonExecutor;

    @Autowired
    private SettingsConstant settingsConstant;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OrgDTO addSupplier(AddSupplierReq req) {
        // 校验供应商名称是否重复
        OrgPO orgPO1 = new OrgPO();
        orgPO1.setType(0);
        orgPO1.setOrgName(req.getSupplierName());
        List<OrgPO> org = orgMapper.select(orgPO1);
        if (org != null && org.size() > 0) {
            throw new SysException(ErrorCodeEnum.SUPPLIER_NAME_IS_ALREADY);
        }
        OrgPO orgPO = CommonDtoConvert.INSTANCE.orgPOConvert(req);
        SupplierCompanyPO supplierCompanyPO = CommonDtoConvert.INSTANCE.supplierCompanyPOConvert(req);
        UserPO adminPO = CommonDtoConvert.INSTANCE.adminPOConvert(req);
        orgPO.setOrgType(req.getSupplierType());
        orgPO.setOrgName(req.getSupplierName());
        orgPO.setOrgDomian(req.getOrgDomain());
        orgPO.setIsShareSupplier(0);
        adminPO.setUserName(req.getAdminName());
        if (StrUtilX.isNotEmpty(req.getAdminTel())) {
            adminPO.setUserTel(req.getAdminTel());
            supplierCompanyPO.setUserTel(req.getAdminTel());
        }

        adminPO.setUserAccount(req.getAdminAccount());
        supplierCompanyPO.setUserNumber(req.getAdminAccount());
//        supplierCompanyPO.setUserTel(supplierAddDTO.getAdminTel());

        supplierCompanyPO.setSettlementType(req.getSettlementType());
        //supplierCompanyPO.setSettlementCurrency(req.getSettlementCurrency());
        supplierCompanyPO.setPurchaseManagerId(req.getPurchaseManagerId());
        supplierCompanyPO.setOperatorCode(req.getSupplierCode());
        Date date = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        //获取String类型的时间
        String createdate = sdf.format(date);
        orgPO.setCreatedDt(createdate);
        adminPO.setCreatedDt(createdate);
        orgPO.setAvailableStatus(AvailableEnum.Start.key);
        orgPO.setType(OrgEnum.Org_Supplier.key);
        supplierCompanyPO.setAvailableStatus(AvailableEnum.Start.key);
        supplierCompanyPO.setCreatedDt(createdate);
        //获取编码
        String code = getSupplierCode();
        orgPO.setOrgCode(code);

        orgMapper.insert(orgPO);
        supplierCompanyPO.setOrgId(orgPO.getOrgId());
        supplierCompanyPO.setUserName(req.getAdminName());
        supplierCompanyPO.setUserNumber(req.getAdminAccount());
        supplierCompanyPO.setOneLevelChannelType(req.getOneLevelChannelType());
        supplierCompanyPO.setTwoLevelChannelType(req.getTwoLevelChannelType());
        supplierCompanyPO.setThreeLevelChannelType(req.getThreeLevelChannelType());
//        supplierCompanyPO.setUserTel(supplierAddDTO.getAdminTel());
        supplierCompanyMapper.insert(supplierCompanyPO);
        OrgPO supplierAddPO1 = orgMapper.selectByPrimaryKey(orgPO.getOrgId());
        adminPO.setOrgCode(supplierAddPO1.getOrgCode());
        adminPO.setCreatedBy(req.getCreatedBy());
        //userService.addAdminUser(adminPO, EndTypeEnum._1.no);

        // 加载初始化到redis中
        req.setSupplierCode(supplierAddPO1.getOrgCode());
        req.setAvailableStatus(AvailableEnum.Start.key);
        RedisTemplateX.hPut(RedisKey.SUPPLY_ACCOUNT_CONFIG, supplierAddPO1.getOrgCode(), JSONObject.toJSONString(req));

        //自动发单配置
        //设置供应商编码
        req.getAddSupplierAutoChannelDTO().setSupplierCode(req.getSupplierCode());
        orderRemote.addSupplierAutoChannel(req.getAddSupplierAutoChannelDTO());

        //新增加幅记录存入日志
        OrgSupplierIncreaseLog increaseLog = new OrgSupplierIncreaseLog();
        increaseLog.setSupplierCode(req.getSupplierCode());
        increaseLog.setAdjustmentType(req.getAdjustmentType());
        increaseLog.setModifiedAmt(req.getModifiedAmt());
        increaseLog.setContent("设置加幅为：" + getIncreaseType(req.getAdjustmentType(), String.valueOf(req.getModifiedAmt())) + "，最低加幅：" + req.getLowestIncreaseAmt());
        increaseLog.setCreatedBy(req.getCreatedBy());
        increaseLog.setCreatedDt(new Date());
        orgSupplierIncreaseLogMapper.insert(increaseLog);

        if (req.getLoadAgentSupplyAvailable() != null && req.getLoadAgentSupplyAvailable() == 1) {
            // 注册事务同步器
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    // 事务提交后，加载客户供应商可见性
                    loadAgentSupplyAvailable();
                }
            });
        }
        return CommonDtoConvert.INSTANCE.orgDTOComConvert(orgPO);
    }

    /**
     * 加载客户供应商可见性
     */
    private void loadAgentSupplyAvailable() {
        CompletableFuture.runAsync(() -> RedisTemplateX.setAdd(RedisKey.RELOAD_AGENT_AVAILABLE_CACHE, "code"), processingCommonExecutor);
    }

    //防重复供应商编码
    private String getSupplierCode() {
        String code = RedisTemplateX.lRightPop(SystemCodeEnum.SUPPLIERCODE.code);
        if (null == code) {
            Map<String, String> requestMap = new HashMap<>();
            requestMap.put("seqName", SystemCodeEnum.SUPPLIERCODE.code);
            sequenceService.createCode(requestMap);
            code = RedisTemplateX.lRightPop(SystemCodeEnum.SUPPLIERCODE.code);
        }
        OrgPO po = new OrgPO();
        po.setOrgCode(CompanyDTO.COMPANY_CODE);
        po.setOrgCode(code);
        po.setOrgType(2);
        List<OrgPO> select = orgMapper.select(po);
        if (CollectionUtil.isEmpty(select)){
            return code;
        }else {
            code = getSupplierCode();
        }
        return code;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> addSupplierForeign(List<AddSupplierForeignReq> addSupplierForeignReq) {
        Map<String, Object> map = new HashMap<>();
        List<AddSupplierDTO> list = new ArrayList<>();
        List<AddSupplierForeignReq> errorSupplierName = new ArrayList<>();
        List<AddSupplierForeignReq> emptySupplier = new ArrayList<>();
        for (AddSupplierForeignReq req : addSupplierForeignReq) {
            if (StringUtils.isEmpty(req.getSupplierName()) && StringUtils.isEmpty(req.getSupplierCode())) {
                throw new CustomException(ErrorCodeEnum.SUPPLIER_CODE_AND_NAME_IS_EMPTY.errorNo, ErrorCodeEnum.SUPPLIER_CODE_AND_NAME_IS_EMPTY.errorCode, ErrorCodeEnum.SUPPLIER_CODE_AND_NAME_IS_EMPTY.errorDesc + "{}" + JSONObject.toJSONString(req));
            }
            // 合作商编码和密钥必填过滤
            if (StringUtils.isEmpty(req.getSecretKey()) || StringUtils.isEmpty(req.getPartnerCode()) || req.getHotelId() == null) {
                emptySupplier.add(req);
                continue;
            }
            if (StringUtils.isEmpty(req.getSupplierCode())) {
                OrgPO orgPO1 = new OrgPO();
                orgPO1.setType(0);
                orgPO1.setOrgName(req.getSupplierName());
                List<OrgPO> org = orgMapper.select(orgPO1);
                if (org != null && org.size() > 0) {
                    OrgPO orgPO = org.get(0);
                    req.setSupplierCode(orgPO.getOrgCode());
                } else {
                    AddSupplierReq addSupplierReq = new AddSupplierReq();
                    addSupplierReq.setSupplierName(req.getSupplierName());
                    addSupplierReq.setSupplierType(SupplierTypeEnum.API.getKey());
                    addSupplierReq.setIsCached(1);
                    addSupplierReq.setDomesticOrOverseas(1);
                    addSupplierReq.setSettlementType(SettlementTypeEnum.MONTH.key);
                    addSupplierReq.setIsSupportEarlyMorningRoomOrder(0);
                    addSupplierReq.setQuickProcessingSwitch(0);
                    addSupplierReq.setIsSyncCheckDetail(0);
                    addSupplierReq.setAdjustmentType(2);
                    addSupplierReq.setModifiedAmt(BigDecimal.ZERO);
                    addSupplierReq.setSupplierCode(CompanyDTO.COMPANY_CODE);
                    addSupplierReq.setSupplierForm(1);
                    addSupplierReq.setHotelId(req.getHotelId());
                    addSupplierReq.setHotelName(req.getHotelName());

                    // 订单通配置自动发单配置，默认值
                    AddSupplierAutoChannelDTO addSupplierAutoChannelDTO = new AddSupplierAutoChannelDTO();
                    addSupplierAutoChannelDTO.setOrderMethod(OrderMethodEnum.ORDER_METHOD_ENUM_1.key);
                    addSupplierAutoChannelDTO.setAvailable(0);
                    addSupplierReq.setAddSupplierAutoChannelDTO(addSupplierAutoChannelDTO);


                    OrgDTO orgDTO = addSupplier(addSupplierReq);
                    if (orgDTO != null) {
                        req.setSupplierCode(orgDTO.getOrgCode());
                    } else {
                        errorSupplierName.add(req);
                        continue;
                    }
                }
            } else {
                OrgPO orgPO1 = new OrgPO();
                orgPO1.setType(0);
                orgPO1.setOrgCode(req.getSupplierCode());
                List<OrgPO> org = orgMapper.select(orgPO1);
                if (org.size() > 0) {
                    OrgPO orgPO = org.get(0);
                    req.setSupplierName(orgPO.getOrgName());
                } else {
                    emptySupplier.add(req);
                    continue;
                }
            }
            AddSupplierDTO dto = new AddSupplierDTO();
            dto.setMerchantCode(CompanyDTO.COMPANY_CODE);
            dto.setSupplyClass("IREVEBLD");
            dto.setSupplyName(req.getSupplierName());
            dto.setSupplyCode(req.getSupplierCode());
            dto.setPartnerCode(req.getPartnerCode());
            dto.setSignature(req.getSecretKey());
            dto.setHotelId(req.getHotelId());

            list.add(dto);
        }
        map.put("errorSupplierNameList", errorSupplierName);
        map.put("emptySupplierList", emptySupplier);

        // 开始时间
        Date start = new Date();
        String url = settingsConstant.getDomainUrl() + "/common/automaticRegisterSupply";
        String request = JSON.toJSONString(list);
        // 调用供应商新增配置
        String result = HttpUtilX.post(url, request);

        // 保存日志
        saveLog(url, request, result, start);

        JSONObject jsonObject = JSONObject.parseObject(result);
        if (jsonObject == null) {
            throw new SysException(ErrorCodeEnum.INTERNAL_TIMEOUT);
        }
        if (jsonObject.getInteger("result") != 1) {
            throw new SysException(jsonObject.getString("msg"));
        }

        // 注册事务同步器
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                // 事务提交后，加载客户供应商可见性
                loadAgentSupplyAvailable();
            }
        });

        return map;
    }

    /**
     * 保存日志
     */
    private void saveLog(String url, String request, String result, Date start) {
        Map<String, String> map = new HashMap<>();
        map.put(SlsEnum.LEVEL.getType(), SlsLogLevelEnum.INFO.getLevel());
        map.put(SlsEnum.NAME.getType(), "addSupplierForeign");
        map.put(SlsEnum.MESSAGE.getType(), "调用供应商新增配置");

        map.put("url", url);
        map.put("request", request);
        map.put("response", result);
        map.put("start", DateUtilX.dateToString(start, DateUtilX.hour_ms_format));

        slsLoggerUtil.saveLog(map, "addSupplierForeign", "tiangong-common-server");
    }

    /**
     * 修改供应商启用状态
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modifySupplierStatus(SupplierStatusUpdateReq supplierAddDTO) {
        OrgPO orgPO = orgMapper.selectByPrimaryKey(supplierAddDTO.getSupplierId());
        if (orgPO == null) {
            throw new SysException(ErrorCodeEnum.SUPPLY_NOT_EXIST);
        }

        SupplierCompanyPO querySupplierCompany = new SupplierCompanyPO();
        querySupplierCompany.setOrgId(supplierAddDTO.getSupplierId());
        querySupplierCompany.setOperatorCode(supplierAddDTO.getCompanyCode());
        List<SupplierCompanyPO> supplierCompanyPOS = supplierCompanyMapper.select(querySupplierCompany);

        if (CollUtilX.isEmpty(supplierCompanyPOS)) {
            throw new SysException(ErrorCodeEnum.NOMATCH_SUPPLYANDMECHANT);
        }

        // 若是为共享供应商，则判断只改变商家与供应商的关系
        SupplierCompanyPO supplierCompanyPO = new SupplierCompanyPO();
        supplierCompanyPO.setUpdatedBy(supplierAddDTO.getUpdatedBy());
        supplierCompanyPO.setUpdatedDt(DateUtilX.dateToString(new Date(), DateUtilX.hour_format));
        supplierCompanyPO.setOrgId(supplierAddDTO.getSupplierId());
        supplierCompanyPO.setAvailableStatus(supplierAddDTO.getAvailableStatus());
        supplierCompanyPO.setOperatorCode(supplierAddDTO.getCompanyCode());
        orgMapper.updateSupplierStatus(supplierCompanyPO);

        // 非共享供应商需要修改用户状态
        if (orgPO.getIsShareSupplier() != 1) {
            UserPO userPO = new UserPO();
            Date date = new Date();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            // 获取String类型的时间
            String createdate = sdf.format(date);
            Integer userId = orgMapper.getUserId(orgPO.getOrgCode());
            if (userId != null) {
                userPO.setOrgCode(orgPO.getOrgCode());
                userPO.setAvailableStatus(supplierAddDTO.getAvailableStatus());
                userPO.setUpdatedBy(supplierAddDTO.getUpdatedBy());
                userPO.setUpdatedDt(createdate);
                orgMapper.updateAdminStatus(userPO);
            }
        }

        // 禁用启用
        AddSupplierReq supplierAdd = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.SUPPLY_ACCOUNT_CONFIG, orgPO.getOrgCode()), AddSupplierReq.class);
        Optional.ofNullable(supplierAdd).ifPresent(agentAccount -> {
            if (supplierAddDTO.getAvailableStatus() != null) {
                agentAccount.setAvailableStatus(supplierAddDTO.getAvailableStatus());
            }
            RedisTemplateX.hPut(RedisKey.SUPPLY_ACCOUNT_CONFIG, orgPO.getOrgCode(), JSON.toJSONString(agentAccount));
        });

        // 注册事务同步器
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                // 事务提交后，加载客户供应商可见性
                loadAgentSupplyAvailable();
            }
        });
    }

    @Override
    public void modifySupplier(SupplierUpdateReq req) {
        // 校验供应商名称是否重复
        OrgPO orgPO1 = new OrgPO();
        orgPO1.setType(0);
        orgPO1.setOrgName(req.getSupplierName());
        List<OrgPO> org = orgMapper.select(orgPO1);
        if (org != null && org.size() > 0) {
            for (OrgPO orgPO : org) {
                if (orgPO.getOrgId().equals(req.getSupplierId())) {
                    continue;
                }
                throw new SysException(ErrorCodeEnum.SUPPLIER_NAME_IS_ALREADY);
            }
        }
        Integer supplierCompany = orgMapper.getSupplierCompany(req.getSupplierId(), req.getCompanyCode());
        if (supplierCompany == null) {
            throw new SysException(ErrorCodeEnum.NOMATCH_SUPPLYANDMECHANT);
        }

        OrgPO orgPO = new OrgPO();
        UserPO userPO = new UserPO();
        SupplierCompanyPO supplierCompanyPO = new SupplierCompanyPO();
        Date date = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        //获取String类型的时间
        String createdate = sdf.format(date);
        OrgPO supplierAddPO1 = orgMapper.selectByPrimaryKey(req.getSupplierId());
        Integer userId = orgMapper.getUserId(supplierAddPO1.getOrgCode());
        orgPO.setOfficialSealId(req.getOfficialSealId());
        orgPO.setOrgId(req.getSupplierId());
        orgPO.setOrgType(req.getSupplierType());
        orgPO.setOrgName(req.getSupplierName());
        orgPO.setHotelId(req.getHotelId());
        orgPO.setIsSupportEarlyMorningRoomOrder(req.getIsSupportEarlyMorningRoomOrder());
        userPO.setUserName(req.getAdminName());
        if (StrUtilX.isNotEmpty(req.getAdminTel())) {
            userPO.setUserTel(req.getAdminTel());
            supplierCompanyPO.setUserTel(req.getAdminTel());
        }

        userPO.setUserAccount(req.getAdminAccount());
        supplierCompanyPO.setUserName(req.getAdminName());
        supplierCompanyPO.setUserNumber(req.getAdminAccount());
        supplierCompanyPO.setSupplierForm(req.getSupplierForm());
        supplierCompanyPO.setQuickProcessingSwitch(req.getQuickProcessingSwitch());
        supplierCompanyPO.setHotelId(req.getHotelId());
        supplierCompanyPO.setHotelName(req.getHotelName());
        supplierCompanyPO.setGroupId(req.getGroupId());
        supplierCompanyPO.setGroupName(req.getGroupName());
        supplierCompanyPO.setInvoiceId(req.getInvoiceId());
        supplierCompanyPO.setEnableProtocol(req.getEnableProtocol());
        supplierCompanyPO.setEnableSettled(req.getEnableSettled());
        supplierCompanyPO.setRemark(req.getRemark());

        supplierCompanyPO.setSettlementType(req.getSettlementType());
        //supplierCompanyPO.setSettlementCurrency(req.getSettlementCurrency());
        supplierCompanyPO.setPurchaseManagerId(req.getPurchaseManagerId());

        supplierCompanyPO.setSCompanyId(supplierCompany);
        supplierCompanyPO.setUpdatedBy(req.getUpdatedBy());
        supplierCompanyPO.setUpdatedDt(createdate);

        supplierCompanyPO.setIsCached(req.getIsCached());
        supplierCompanyPO.setIsSyncCheckDetail(req.getIsSyncCheckDetail());
        supplierCompanyPO.setOneLevelChannelType(req.getOneLevelChannelType());
        supplierCompanyPO.setTwoLevelChannelType(req.getTwoLevelChannelType());
        supplierCompanyPO.setThreeLevelChannelType(req.getThreeLevelChannelType());

        if (userId == null) {
            orgPO.setUpdatedDt(createdate);
            orgPO.setUpdatedBy(req.getUpdatedBy());
            orgMapper.updateByPrimaryKeySelective(orgPO);
            supplierCompanyMapper.updateByPrimaryKeySelective(supplierCompanyPO);
        } else {
            orgPO.setUpdatedDt(createdate);
            orgPO.setUpdatedBy(req.getUpdatedBy());
            orgMapper.updateByPrimaryKeySelective(orgPO);
            supplierCompanyMapper.updateByPrimaryKeySelective(supplierCompanyPO);
        }

        // 修改后的供应商配置重新加载到缓存
        AddSupplierReq po = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.SUPPLY_ACCOUNT_CONFIG, supplierAddPO1.getOrgCode()), AddSupplierReq.class);
        Optional.ofNullable(po).ifPresent(supplier -> {
            Integer availableStatus = supplier.getAvailableStatus();
            Integer domesticOrOverseas = supplier.getDomesticOrOverseas();
            supplier = CommonDtoConvert.INSTANCE.supplierConvert(req);
            // 防止有效状态为空时，使用原来的有效状态
            if (supplier.getAvailableStatus() == null) {
                supplier.setAvailableStatus(availableStatus);
            }
            // 防止供应商资源类型为空时，使用原来的供应商资源类型
            if (supplier.getDomesticOrOverseas() == null) {
                supplier.setDomesticOrOverseas(domesticOrOverseas);
            }
            RedisTemplateX.hPut(RedisKey.SUPPLY_ACCOUNT_CONFIG, supplierAddPO1.getOrgCode(), JSONObject.toJSONString(supplier));
        });
    }

    @Override
    public SupplierSelectDTO querySupplierDetail(SupplierDetailByCode req) {
        SupplierSelectDTO supplierSelectDTO = orgMapper.supplierList(req.getSupplierCode(), req.getCompanyCode());
        BankListDTO dto = new BankListDTO();
        dto.setOrgCode(req.getSupplierCode());
        dto.setActive(DeleteEnum.STATUS_EXIST.key);
        List<BankSupplierDTO> bankAddDTOS = orgMapper.bankList(dto);
        List<ContactSupplierDTO> contactAddDTOS = orgMapper.concactList(req.getSupplierCode(), DeleteEnum.STATUS_EXIST.key, 0);
        List<ContactSupplierDTO> ownContactAddDTOS = orgMapper.concactList(req.getSupplierCode(), DeleteEnum.STATUS_EXIST.key, 1);
        // 查询供应商加幅和发票类型
        SupplierCompanyPO supplierCompanyPO = new SupplierCompanyPO();
        supplierCompanyPO.setOrgId(supplierSelectDTO.getSupplierId());
        SupplierCompanyPO companyPO = supplierCompanyMapper.selectOne(supplierCompanyPO);
        if (bankAddDTOS.size() != 0) {
            supplierSelectDTO.setBankCardList(bankAddDTOS);
        }
        if (contactAddDTOS.size() != 0) {
            supplierSelectDTO.setContactList(contactAddDTOS);
        }
        if (ownContactAddDTOS.size() != 0) {
            supplierSelectDTO.setOwncontactList(ownContactAddDTOS);
        }
        if (supplierSelectDTO.getIsSupportEarlyMorningRoomOrder() == null) {
            supplierSelectDTO.setIsSupportEarlyMorningRoomOrder(0);
        }

        if (companyPO != null) {
            if (companyPO.getAdjustmentType() != null) {
                supplierSelectDTO.setAdjustmentType(companyPO.getAdjustmentType());
                supplierSelectDTO.setModifiedAmt(companyPO.getModifiedAmt());
                supplierSelectDTO.setLowestIncreaseAmt(companyPO.getLowestIncreaseAmt());
            }
            if (companyPO.getInvoiceModel() != null) {
                supplierSelectDTO.setInvoiceModel(companyPO.getInvoiceModel());
                supplierSelectDTO.setInvoiceType(companyPO.getInvoiceType());
            }
            supplierSelectDTO.setIsCached(companyPO.getIsCached());
        }
        return supplierSelectDTO;
    }

    @Override
    public PaginationSupportDTO<QuerySupplierListDTO> querySupplierList(SupplierListReq request) {
        PageHelper.startPage(request.getCurrentPage(), request.getPageSize());
        List<QuerySupplierListDTO> list = orgMapper.querySupplierList(request);
        PageInfo<QuerySupplierListDTO> page = new PageInfo<>(list);
        PaginationSupportDTO<QuerySupplierListDTO> paginationSupport = new PaginationSupportDTO<>();
        paginationSupport.setItemList(list);
        paginationSupport.setPageSize(page.getPageSize());
        paginationSupport.setTotalCount(page.getTotal());
        paginationSupport.setTotalPage(page.getPages());
        paginationSupport.setCurrentPage(page.getPageNum());
        return paginationSupport;
    }

    @Override
    public List<QuerySupplierListDTO> querySupplierInfoList(SupplierListReq request) {
        return orgMapper.querySupplierList(request);
    }

    @Override
    public PaginationSupportDTO<SendEntityDTO> queryCompanySignature(QueryCompanySignatureDTO queryCompanySignatureDTO) {
        PageHelper.startPage(queryCompanySignatureDTO.getCurrentPage(), queryCompanySignatureDTO.getPageSize());
        List<SendEntityDTO> sendEntityList = orgMapper.queryCompanyOfficial(queryCompanySignatureDTO.getCompanyCode());
        PageInfo<SendEntityDTO> page = new PageInfo<>(sendEntityList);
        PaginationSupportDTO<SendEntityDTO> paginationSupportDTO = new PaginationSupportDTO<>();
        paginationSupportDTO.copyProperties(page, SendEntityDTO.class);
        return paginationSupportDTO;
    }

    @Override
    public String getPhoneAsPassword(ContactAsPhoneDTO contact) {
        Assert.notNull(contact.getPassword(), "密码不能为空！");

        // 查询用户并验证密码
        UserPO user = userMapper.selectByPrimaryKey(contact.getUserId());
        String encryptUserPassword = SM4Utils.encrypt(contact.getPassword(), Sm4O.defaultKey);
        if (!encryptUserPassword.equals(user.getUserPwd())) {
            throw new SysException(ErrorCodeEnum.PASSWORD_ERROR);
        }

        SupplierCompanyPO companyPO = new SupplierCompanyPO();
        companyPO.setOrgId(contact.getOrgId());
        SupplierCompanyPO supplierCompanyPO = supplierCompanyMapper.selectOne(companyPO);
        if (supplierCompanyPO != null && StrUtilX.isNotEmpty(supplierCompanyPO.getUserTel())) {
            return supplierCompanyPO.getUserTel();
        }
        return null;
    }

    @Override
    public void modifySupplierPhone(ContactAsPhoneDTO contact) {
        // 如果手机号存在需要验证
        if (StrUtilX.isNotEmpty(contact.getTel()) && !Validator.isMobile(contact.getTel())) {
            throw new SysException(ErrorCodeEnum.PLEASE_INPUT_CORRECT_PHONE.errorCode, ErrorCodeEnum.PLEASE_INPUT_CORRECT_PHONE.errorDesc);
        }
        // 判断新旧手机号是否一致
        SupplierCompanyPO companyPO = new SupplierCompanyPO();
        companyPO.setOrgId(contact.getOrgId());
        SupplierCompanyPO select = supplierCompanyMapper.selectOne(companyPO);
        if (contact.getTel().equals(select.getUserTel())) {
            throw new SysException(ErrorCodeEnum.OLD_PHONE_IS_SAME.errorCode, ErrorCodeEnum.OLD_PHONE_IS_SAME.errorDesc);
        }

        companyPO.setUserTel(contact.getTel());
        Example example = new Example(SupplierCompanyPO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("orgId", contact.getOrgId());
        supplierCompanyMapper.updateByExampleSelective(companyPO, example);
    }

    @Override
    public void modifySupplierAdjustment(SupplierAdjustmentUpdateReq req) {
        SupplierCompanyPO companyPO = new SupplierCompanyPO();
        companyPO.setOrgId(req.getSupplierId());
        SupplierCompanyPO select = supplierCompanyMapper.selectOne(companyPO);
        if (select == null) {
            throw new SysException(ErrorCodeEnum.SUPPLIER_ID_IS_ERROR.errorCode, ErrorCodeEnum.SUPPLIER_ID_IS_ERROR.errorDesc);
        }
        int update = supplierCompanyMapper.updateByOrgIdToAdjustment(req);
        if (update <= 0) {
            throw new SysException(ErrorCodeEnum.UPDATE_ERROR.errorCode, ErrorCodeEnum.UPDATE_ERROR.errorDesc);
        }
        //修改记录存入日志
        OrgSupplierIncreaseLog increaseLog = new OrgSupplierIncreaseLog();
        increaseLog.setSupplierCode(req.getSupplierCode());
        increaseLog.setAdjustmentType(req.getAdjustmentType());
        increaseLog.setModifiedAmt(req.getModifiedAmt());
        increaseLog.setLowestIncreaseAmt(req.getLowestIncreaseAmt());
        increaseLog.setContent("加幅改为：" + getIncreaseType(req.getAdjustmentType(), String.valueOf(req.getModifiedAmt())) + "，最低加幅：" + req.getLowestIncreaseAmt());
        increaseLog.setCreatedBy(req.getUpdatedBy());
        increaseLog.setCreatedDt(new Date());
        orgSupplierIncreaseLogMapper.insert(increaseLog);

        // 将修改过的数据存入缓存
        AddSupplierReq supplierReq = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.SUPPLY_ACCOUNT_CONFIG, req.getSupplierCode()), AddSupplierReq.class);
        Optional.ofNullable(supplierReq).ifPresent(agentAccount ->
        {
            supplierReq.setAdjustmentType(req.getAdjustmentType());
            supplierReq.setModifiedAmt(req.getModifiedAmt());
            supplierReq.setLowestIncreaseAmt(req.getLowestIncreaseAmt());
            RedisTemplateX.hPut(RedisKey.SUPPLY_ACCOUNT_CONFIG, req.getSupplierCode(), JSON.toJSONString(supplierReq));
        });
    }

    private String getIncreaseType(int type, String modifiedAmt) {
        String typeStr = "+";
        switch (type) {
            case 0:
                typeStr = "+" + modifiedAmt;
                break;
            case 1:
                typeStr = "-" + modifiedAmt;
                break;
            case 2:
                typeStr = "+" + modifiedAmt + "%";
                break;
            case 3:
                typeStr = "-" + modifiedAmt + "%";
                break;
            case 4:
                typeStr = "=" + modifiedAmt;
                break;
        }
        return typeStr;
    }

    @Override
    public void modifySupplierInvoice(SupplierInvoiceUpdateReq req) {
        SupplierCompanyPO companyPO = new SupplierCompanyPO();
        companyPO.setOrgId(req.getSupplierId());
        SupplierCompanyPO select = supplierCompanyMapper.selectOne(companyPO);
        if (select == null) {
            throw new SysException(ErrorCodeEnum.SUPPLIER_ID_IS_ERROR.errorCode, ErrorCodeEnum.SUPPLIER_ID_IS_ERROR.errorDesc);
        }
        int update = supplierCompanyMapper.updateByOrgIdToInvoice(req);
        if (update <= 0) {
            throw new SysException(ErrorCodeEnum.UPDATE_ERROR.errorCode, ErrorCodeEnum.UPDATE_ERROR.errorDesc);
        }

        // 将修改过的数据存入缓存
        AddSupplierReq supplierReq = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.SUPPLY_ACCOUNT_CONFIG, req.getSupplierCode()), AddSupplierReq.class);
        Optional.ofNullable(supplierReq).ifPresent(agentAccount -> {
            supplierReq.setInvoiceModel(req.getInvoiceModel());
            supplierReq.setInvoiceType(req.getInvoiceType());
            RedisTemplateX.hPut(RedisKey.SUPPLY_ACCOUNT_CONFIG, req.getSupplierCode(), JSON.toJSONString(supplierReq));
        });
    }

    @Override
    public PaginationSupportDTO<OrgSupplierIncreaseLog> supplierIncreaseLogList(IncreaseLogReq increaseLogReq) {
        List<OrgSupplierIncreaseLog> orgSupplierIncreaseLogs = orgSupplierIncreaseLogMapper
                .selectList(new LambdaQueryWrapper<OrgSupplierIncreaseLog>()
                        .eq(OrgSupplierIncreaseLog::getSupplierCode, increaseLogReq.getSupplierCode())
                        .orderByDesc(OrgSupplierIncreaseLog::getCreatedDt)
                );
        PageInfo<OrgSupplierIncreaseLog> pageInfo = new PageInfo<>(orgSupplierIncreaseLogs);

        PaginationSupportDTO<OrgSupplierIncreaseLog> supportDTO = new PaginationSupportDTO<>();
        supportDTO.copyProperties(pageInfo, OrgSupplierIncreaseLog.class);
        return supportDTO;
    }

    @Override
    public void addOrUpdateSupplierLabelConfig(SupplierLabelConfigReq req) {
        SupplierLabelConfigPO po = CommonDtoConvert.INSTANCE.supplierLabelConfigPOConvert(req);
        SupplierLabelConfigPO one = supplierLabelConfigMapper.selectOne(new LambdaQueryWrapper<SupplierLabelConfigPO>()
                .eq(SupplierLabelConfigPO::getAgentId, req.getAgentId())
                .eq(SupplierLabelConfigPO::getSupplierId, req.getSupplierId())
                .last("limit 1")
        );
        if (req.getId() == null) {
            if (one != null) {
                throw new SysException(ErrorCodeEnum.AGENT_SUPPLY_LABEL_IS_ALREADY.errorCode, ErrorCodeEnum.AGENT_SUPPLY_LABEL_IS_ALREADY.errorDesc);
            }
            supplierLabelConfigMapper.insert(po);
        } else {
            if (one != null && one.getId().intValue() != po.getId().intValue()) {
                throw new SysException(ErrorCodeEnum.AGENT_SUPPLY_LABEL_IS_ALREADY.errorCode, ErrorCodeEnum.AGENT_SUPPLY_LABEL_IS_ALREADY.errorDesc);
            }
            supplierLabelConfigMapper.updateById(po);
        }
        RedisTemplateX.hashSet(RedisKey.AGENT_SUPPLIER_LABEL, req.getAgentCode() + "_" + req.getSupplierCode(), req.getLabelType() + "");

        // 刷新客户供应商标签信息
        RedisTemplateX.convertAndSend(RedisKey.REFRESH_AGENT_SUPPLIER_LABEL_INFO, JSONUtil.toJsonStr(AgentSupplierLabelDTO.builder()
                .agentCode(req.getAgentCode())
                .supplierCode(req.getSupplierCode())
                .labelType(req.getLabelType())
                .build()));
    }

    @Override
    public void delSupplierLabelConfig(DelSupplierLabelConfigReq req) {
        int delete = supplierLabelConfigMapper.deleteById(req.getId());
        if (delete == 0) {
            throw new SysException(ErrorCodeEnum.OPERATE_FAIL);
        }
        RedisTemplateX.hashDel(RedisKey.AGENT_SUPPLIER_LABEL, req.getAgentCode() + "_" + req.getSupplierCode());

        // 刷新客户供应商标签信息
        RedisTemplateX.convertAndSend(RedisKey.REFRESH_AGENT_SUPPLIER_LABEL_INFO, JSONUtil.toJsonStr(AgentSupplierLabelDTO.builder()
                .agentCode(req.getAgentCode())
                .supplierCode(req.getSupplierCode())
                .labelType(req.getLabelType())
                .deleted(1)
                .build()));
    }

    @Override
    public PaginationSupportDTO<SupplierLabelConfigResp> querySupplierLabelConfigPage(QuerySupplierLabelConfigReq req) {
        IPage<SupplierLabelConfigResp> page = new Page<>(req.getCurrentPage(), req.getPageSize());
        IPage<SupplierLabelConfigResp> supplierLabelConfigPage = supplierLabelConfigMapper.querySupplierLabelConfigPage(page, req);
        PaginationSupportDTO<SupplierLabelConfigResp> paginationSupportDTO = new PaginationSupportDTO<>();
        paginationSupportDTO = paginationSupportDTO.getPaginationSupportDTO(supplierLabelConfigPage);
        return paginationSupportDTO;
    }

    @Override
    public QueryOrderSupplierDetailResp queryOrderSupplierDetail(QueryOrderSupplierDetailReq req) {
        return orgMapper.queryOrderSupplierDetail(req);
    }

    @Override
    public List<QuerySupplierListDTO> querySupplierNameList(SupplierListReq request) {
        if (CollUtilX.isEmpty(request.getSupplierCodes())) {
            return null;
        }
        Example orgExample = new Example(OrgPO.class);
        orgExample.createCriteria().andEqualTo("type", 0);
        orgExample.createCriteria().andIn("orgCode", request.getSupplierCodes());
        List<OrgPO> orgPOList = orgMapper.selectByExample(orgExample);
        if (CollUtilX.isEmpty(orgPOList)) {
            return null;
        }
        return orgPOList.stream().map(item -> {
            QuerySupplierListDTO dto = new QuerySupplierListDTO();
            dto.setSupplierCode(item.getOrgCode());
            dto.setSupplierName(item.getOrgName());
            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response<Object> importSupplierLabelConfig(MultipartFile file, String operator) {
        Response<Object> response;
        // 解析文件
        InputStream in = null;
        // 加载缓存数据
        List<SupplierLabelConfigReq> checkList = new ArrayList<>();
        try {
            List<ImportSupplierLabelConfigReq> dataList;
            ImportSupplierLabelConfigResp uploadRespVO = new ImportSupplierLabelConfigResp();
            if (StrUtilX.isEmpty(file.getOriginalFilename())) {
                throw new SysException(ErrorCodeEnum.FILE_FORMAT_ERROR);
            }
            // 获取文件名称后缀
            String suffix = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf(".") + 1);

            if (!"xlsx".contains(suffix.toLowerCase())) {
                throw new SysException(ErrorCodeEnum.FILE_FORMAT_ERROR);
            }

            in = file.getInputStream();
            Workbook wb = WorkbookFactory.create(in);
            Sheet sheet = wb.getSheetAt(0);

            // 校验文件格式
            boolean result = checkExcelHeaders(sheet);
            if (!result) {
                throw new SysException(ErrorCodeEnum.IMPORT_FILE_FORMAT_ERROR.errorCode, ErrorCodeEnum.IMPORT_FILE_FORMAT_ERROR.errorDesc);
            }

            // 解析excel内容
            dataList = analyzeExcel(sheet);

            if (CollectionUtil.isNotEmpty(dataList)) {
                Integer successCount = 0;
                List<ImportSupplierLabelConfigExportReq> failDataList = new ArrayList<>();
                Integer repeatCount = 0;
                Set<String> agentCodes = new HashSet<>();
                Set<String> supplierCodes = new HashSet<>();
                for (ImportSupplierLabelConfigReq importReq : dataList) {
                    if (StrUtilX.isNotEmpty(importReq.getAgentCode())) {
                        agentCodes.add(importReq.getAgentCode());
                    }
                    if (StrUtilX.isNotEmpty(importReq.getSupplyCode())) {
                        supplierCodes.add(importReq.getSupplyCode());
                    }
                }
                // 旧数据
                Set<String> oldLabels = new HashSet<>();
                // 客户信息
                Map<String, AgentCompanyResp> agentMap = new HashMap<>();
                // 供应商信息
                Map<String, SupplierCompanyResp> supplierMap = new HashMap<>();
                if (CollUtilX.isNotEmpty(agentCodes)) {
                    // 查询客户信息
                    AgentCompanyReq agentCompanyReq = new AgentCompanyReq();
                    agentCompanyReq.setAgentCodeList(agentCodes);
                    List<AgentCompanyResp> agentCompanyRespList = agentCompanyMapper.selectAgentCompanyList(agentCompanyReq);
                    if (CollUtilX.isNotEmpty(agentCompanyRespList)) {
                        Set<Integer> agentIds = new HashSet<>();
                        for (AgentCompanyResp agentCompanyResp : agentCompanyRespList) {
                            agentMap.put(agentCompanyResp.getOrgCode(), agentCompanyResp);
                            agentIds.add(agentCompanyResp.getACompanyId());
                        }
                        // 查询标签旧数据
                        List<SupplierLabelConfigPO> poList = supplierLabelConfigMapper.selectList(new LambdaQueryWrapper<SupplierLabelConfigPO>().in(SupplierLabelConfigPO::getAgentId, agentIds));
                        if (CollUtilX.isNotEmpty(poList)) {
                            for (SupplierLabelConfigPO supplierLabelConfigPO : poList) {
                                oldLabels.add(supplierLabelConfigPO.getAgentId() + "_" + supplierLabelConfigPO.getSupplierId());
                            }
                        }
                    }

                    // 查询供应商信息
                    if (CollUtilX.isNotEmpty(supplierCodes)) {
                        SupplierCompanyReq supplierCompanyReq = new SupplierCompanyReq();
                        supplierCompanyReq.setSupplierCodeList(supplierCodes);
                        List<SupplierCompanyResp> supplierCompanyRespList = supplierCompanyMapper.selectSupplierCompanyList(supplierCompanyReq);
                        if (CollUtilX.isNotEmpty(supplierCompanyRespList)) {
                            for (SupplierCompanyResp supplierCompanyResp : supplierCompanyRespList) {
                                supplierMap.put(supplierCompanyResp.getOrgCode(), supplierCompanyResp);
                            }
                        }
                    }
                }

                List<SupplierLabelConfigPO> poList = new ArrayList<>();
                String currentDate = DateUtilX.dateToString(DateUtilX.getCurrentDate(), DateUtilX.hour_format);
                Set<String> oldConfigSet = new HashSet<>();
                for (ImportSupplierLabelConfigReq importReq : dataList) {
                    try {
                        // 校验导入供应商标签配置数据
                        checkImportSupplierLabelConfigParam(importReq);
                        // 校验标签类型是否存在
                        Integer type = LabelTypeEnum.getNoByDesc(importReq.getLabelType());
                        if (type == null) {
                            throw new SysException(ErrorCodeEnum.LABEL_TYPE_DOES_NOT_EXIST.errorCode, ErrorCodeEnum.LABEL_TYPE_DOES_NOT_EXIST.errorDesc);
                        }
                        // 校验客户是否存在
                        if (!agentMap.containsKey(importReq.getAgentCode())) {
                            throw new SysException(ErrorCodeEnum.AGENT_INFO_NOT_EXIST.errorCode, ErrorCodeEnum.AGENT_INFO_NOT_EXIST.errorDesc);
                        }
                        // 校验供应商是否存在
                        if (!supplierMap.containsKey(importReq.getSupplyCode())) {
                            throw new SysException(ErrorCodeEnum.SUPPLY_NOT_EXIST);
                        }
                        // 校验配置是否存在
                        if (oldConfigSet.contains(importReq.getAgentCode() + "_" + importReq.getSupplyCode())) {
                            throw new SysException(ErrorCodeEnum.AGENT_SUPPLY_LABEL_IS_ALREADY.errorCode, ErrorCodeEnum.AGENT_SUPPLY_LABEL_IS_ALREADY.errorDesc);
                        }
                        // 校验客户是否有效
                        if (agentMap.get(importReq.getAgentCode()).getAvailableStatus() != null && agentMap.get(importReq.getAgentCode()).getAvailableStatus() == 0) {
                            throw new SysException(ErrorCodeEnum.AGENT_IS_INVALID.errorCode, ErrorCodeEnum.AGENT_IS_INVALID.errorDesc);
                        }
                        // 校验供应商是否有效
                        if (supplierMap.get(importReq.getSupplyCode()).getAvailableStatus() != null && supplierMap.get(importReq.getSupplyCode()).getAvailableStatus() == 0) {
                            throw new SysException(ErrorCodeEnum.SUPPLY_IS_INVALID.errorCode, ErrorCodeEnum.SUPPLY_IS_INVALID.errorDesc);
                        }
                        // 校验资源类型
                        if (agentMap.get(importReq.getAgentCode()).getDomesticOrOverseas() != null && supplierMap.get(importReq.getSupplyCode()).getDomesticOrOverseas() != null
                                && agentMap.get(importReq.getAgentCode()).getDomesticOrOverseas().compareTo(supplierMap.get(importReq.getSupplyCode()).getDomesticOrOverseas()) != 0) {
                            throw new SysException(ErrorCodeEnum.DIFFERENT_RESOURCE_TYPES.errorCode, ErrorCodeEnum.DIFFERENT_RESOURCE_TYPES.errorDesc);
                        }
                        Integer agentId = agentMap.get(importReq.getAgentCode()).getACompanyId();
                        Integer supplierId = supplierMap.get(importReq.getSupplyCode()).getSCompanyId();
                        // 校验配置是否存在
                        if (oldLabels.contains(agentId + "_" + supplierId)) {
                            throw new SysException(ErrorCodeEnum.AGENT_SUPPLY_LABEL_IS_ALREADY.errorCode, ErrorCodeEnum.AGENT_SUPPLY_LABEL_IS_ALREADY.errorDesc);
                        }
                        // 添加旧数据
                        oldConfigSet.add(importReq.getAgentCode() + "_" + importReq.getSupplyCode());

                        SupplierLabelConfigPO supplierLabelConfigPO = new SupplierLabelConfigPO();
                        supplierLabelConfigPO.setAgentId(agentId);
                        supplierLabelConfigPO.setSupplierId(supplierId);
                        supplierLabelConfigPO.setLabelType(type);
                        supplierLabelConfigPO.setCreatedBy(operator);
                        supplierLabelConfigPO.setCreatedDt(currentDate);
                        poList.add(supplierLabelConfigPO);

                        SupplierLabelConfigReq supplierLabelConfigReq = new SupplierLabelConfigReq();
                        supplierLabelConfigReq.setAgentCode(importReq.getAgentCode());
                        supplierLabelConfigReq.setSupplierCode(importReq.getSupplyCode());
                        supplierLabelConfigReq.setLabelType(type);
                        checkList.add(supplierLabelConfigReq);
                        successCount++;
                    } catch (DuplicateKeyException de) {
                        log.error("导入供应商标签配置重复", de);
                        // 重复失败
                        repeatCount++;
                    } catch (SysException se) {
                        log.error("导入供应商标签配置异常", se);
                        ImportSupplierLabelConfigExportReq convert = CommonDtoConvert.INSTANCE.importSupplierLabelConfigExportReqConvert(importReq);
                        convert.setErrorMsg(se.getMessage());
                        failDataList.add(convert);
                    } catch (Exception e) {
                        log.error("导入供应商标签配置异常", e);
                        ImportSupplierLabelConfigExportReq convert = CommonDtoConvert.INSTANCE.importSupplierLabelConfigExportReqConvert(importReq);
                        if (e instanceof ConstraintViolationException) {
                            ConstraintViolationException constraintViolationException = (ConstraintViolationException) e;
                            ConstraintViolation<?> constraintViolation = constraintViolationException.getConstraintViolations().iterator().next();
                            convert.setErrorMsg(constraintViolation.getMessage());
                        } else {
                            convert.setErrorMsg(e.getMessage());
                        }
                        failDataList.add(convert);
                    }
                }
                // 新增数据
                if (CollUtilX.isNotEmpty(poList)) {
                    supplierLabelConfigService.saveBatch(poList);
                }
                // 失败文件处理
                if (CollectionUtil.isNotEmpty(failDataList)) {
                    // 1. list转为excel文件
                    // 创建 ExcelWriter
                    ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                    ExcelWriter excelWriter = EasyExcel.write(outputStream, ImportSupplierLabelConfigExportReq.class).build();
                    WriteSheet writeSheet = EasyExcel.writerSheet("Sheel1").build();

                    // 写入数据
                    excelWriter.write(failDataList, writeSheet);
                    excelWriter.finish(); // 完成写入操作
                    // 转换为 MultipartFile
                    MultipartFile multipartFile = convert(outputStream, "导入供应商标签配置失败.xlsx");
                    String fileUrl = fileUpUtil.uploadFile2(multipartFile, "导入供应商标签配置失败.xlsx");
                    // 2. 上传文件到阿里云
                    uploadRespVO.setFailedDataFileUrl(fileUrl);
                }
                uploadRespVO.setFailedCount(String.valueOf(failDataList.size()));
                uploadRespVO.setRepeatCount(String.valueOf(repeatCount));
                uploadRespVO.setSuccessCount(String.valueOf(successCount));
                // 部分成功部分失败
                response = Response.success(uploadRespVO);
            } else {
                throw new SysException(ErrorCodeEnum.SUPPLY_FILE_CONTENT_IS_EMPTY.errorCode, ErrorCodeEnum.SUPPLY_FILE_CONTENT_IS_EMPTY.errorDesc);
            }
        } catch (SysException e) {
            log.error("导入供应商标签配置失败", e);
            response = Response.error(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("导入供应商标签配置异常", e);
            response = Response.error(ErrorCodeEnum.SYSTEM_EXCEPTION.errorCode, ErrorCodeEnum.SYSTEM_EXCEPTION.errorDesc);
        } finally {
            if (in != null) {
                try {
                    in.close();
                } catch (IOException e) {
                    log.error("导入供应商标签配置异常", e);
                }
            }
            // 异步加载缓存
            CompletableFuture.runAsync(() -> {
                if (CollUtilX.isNotEmpty(checkList)) {
                    Map<String, String> labelMap = new HashMap<>(checkList.size());
                    checkList.forEach(item -> labelMap.put(item.getAgentCode() + "_" + item.getSupplierCode(), item.getLabelType() + ""));
                    RedisTemplateX.hashSetAll(RedisKey.AGENT_SUPPLIER_LABEL, labelMap);

                    // 刷新客户供应商标签信息
                    RedisTemplateX.convertAndSend(RedisKey.REFRESH_AGENT_SUPPLIER_LABEL_INFO, "");
                }
            },processingCommonExecutor);
        }
        return response;
    }

    /**
     * 解析excel内容
     */
    public List<ImportSupplierLabelConfigReq> analyzeExcel(Sheet sheet) {
        List<ImportSupplierLabelConfigReq> list = new ArrayList<>();
        int rowSize = sheet.getPhysicalNumberOfRows();
        for (int j = 2; j < rowSize; j++) {// 遍历每一行
            ImportSupplierLabelConfigReq req = new ImportSupplierLabelConfigReq();
            Row row = sheet.getRow(j);
            if (row == null) {
                continue;
            }
            for (int k = 0; k < 3; k++) {
                Cell cell = null;
                try {
                    cell = row.getCell(k);
                    switch (k) {
                        case 0:
                            if (cell != null) {
                                DataFormatter formatter = new DataFormatter();
                                String value = formatter.formatCellValue(cell).trim();
                                req.setAgentCode(value);
                            }
                            break;
                        case 1:
                            if (cell != null) {
                                DataFormatter formatter = new DataFormatter();
                                String value = formatter.formatCellValue(cell).trim();
                                req.setLabelType(value);
                            }
                            break;
                        case 2:
                            if (cell != null) {
                                DataFormatter formatter = new DataFormatter();
                                String value = formatter.formatCellValue(cell).trim();
                                req.setSupplyCode(value);
                            }
                            break;
                    }
                } catch (Exception e) {
                    log.error("批量导单，解析excel第{}行，【{}】列出现异常，cell={}", j, k, JSONUtil.toJsonStr(cell), e);
                }
            }
            list.add(req);
        }
        return list;
    }

    /**
     * ByteArrayOutputStream 转换成 MultipartFile
     */
    public static MultipartFile convert(ByteArrayOutputStream byteArrayOutputStream, String fileName) throws IOException {
        // 获取字节数组
        byte[] bytes = byteArrayOutputStream.toByteArray();

        // 创建 MockMultipartFile
        return new MockMultipartFile("file", fileName, "application/octet-stream", bytes);
    }

    /**
     * 检查Excel文件表头信息
     *
     * @param sheet 表头
     * @return 状态
     */
    private boolean checkExcelHeaders(Sheet sheet) {
        try {
            // 获取 excel 第一行数据（表头）
            Row row = sheet.getRow(1);
            // 算下有多少列
            int colCount = sheet.getRow(1).getLastCellNum();
            for (int j = 0; j < colCount; j++) {
                Cell cell = row.getCell(j);
                String headName = cell.getStringCellValue().trim();
                if (ImportSupplierLabelConfigExcelTitleEnum.getKeyByValue(headName) != j) {
                    return false;
                }
            }
            return true;
        } catch (Exception e) {
            log.error("检查Excel文件表头信息异常", e);
            return false;
        }
    }

    /**
     * 校验导入公付白名单系统数据
     */
    private void checkImportSupplierLabelConfigParam(ImportSupplierLabelConfigReq req) {
        if (StrUtilX.isEmpty(req.getAgentCode())) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_AGENTCODE);
        }
        if (StrUtilX.isEmpty(req.getLabelType())) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_LABELTYPE);
        }
        if (StrUtilX.isEmpty(req.getSupplyCode())) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_SUPPLYCODE);
        }
    }

}
