package com.tiangong.organization.service.Impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.enums.*;
import com.tiangong.exception.SysException;
import com.tiangong.organization.domain.*;
import com.tiangong.organization.mapper.OrgMapper;
import com.tiangong.organization.mapper.OrgOfficialSealMapper;
import com.tiangong.organization.mapper.PictureMapper;
import com.tiangong.organization.remote.dto.*;
import com.tiangong.organization.service.CompanyService;
import com.tiangong.user.service.UserService;
import com.tiangong.util.CollUtilX;
import com.tiangong.util.DateUtilX;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/6/28 11:33
 **/
@Service
@Slf4j
public class CompanyServiceImpl implements CompanyService {

    @Autowired
    private PictureMapper pictureMapper;

    @Autowired
    private OrgMapper orgMapper;

    @Autowired
    private UserService userService;

    @Autowired
    private OrgOfficialSealMapper orgOfficialSealMapper;

    @Override
    public void addCompany(CompanyAddDTO companyAddDTO) {
        // 校验参数
        if (StrUtilX.isEmpty(companyAddDTO.getCompanyName()) || StrUtilX.isEmpty(companyAddDTO.getCompanyDomain())
                || StrUtilX.isEmpty(companyAddDTO.getAdminName()) || StrUtilX.isEmpty(companyAddDTO.getAdminAccount())
                || StrUtilX.isEmpty(companyAddDTO.getAdminTel())) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
        OrgPO orgPO = new OrgPO();
        PicturePO picturePO = new PicturePO();
        UserPO userPO = new UserPO();
        Date date = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        //获取String类型的时间
        String createdate = sdf.format(date);
        orgPO.setAvailableStatus(AvailableEnum.Start.key);
        orgPO.setCreatedDt(createdate);
        orgPO.setCreatedBy(companyAddDTO.getCreatedBy());
        orgPO.setOrgName(companyAddDTO.getCompanyName());
        orgPO.setOrgDomian(companyAddDTO.getCompanyDomain());
        orgPO.setHotelInfoPermissions(companyAddDTO.getHotelInfoPermissions());
        orgPO.setIsShareSupplier(0);
        userPO.setUserName(companyAddDTO.getAdminName());
        userPO.setUserAccount(companyAddDTO.getAdminAccount());
        userPO.setUserTel(companyAddDTO.getAdminTel());
        userPO.setCreatedBy(companyAddDTO.getCreatedBy());
        orgPO.setOrgTel(companyAddDTO.getCompanyTel());
        orgPO.setOrgAddress(companyAddDTO.getCompanyAddress());
        orgPO.setOrgSummary(companyAddDTO.getCompanySummary());
        orgPO.setType(OrgEnum.Org_Company.key);
        orgMapper.insert(orgPO);
        OrgPO supplierAddPO1 = orgMapper.selectByPrimaryKey(orgPO.getOrgId());
        userPO.setOrgCode(supplierAddPO1.getOrgCode());
        for (int i = 0; i < companyAddDTO.getPictureList().size(); i++) {
            BeanUtils.copyProperties(companyAddDTO.getPictureList().get(i), picturePO);
            picturePO.setCreatedBy(companyAddDTO.getCreatedBy());
            picturePO.setCreatedDt(createdate);
            picturePO.setCompanyCode(supplierAddPO1.getOrgCode());
            picturePO.setActive(1);
            pictureMapper.insert(picturePO);
        }
        userService.addAdminUser(userPO, EndTypeEnum._0.no);
    }

    @Override
    public void modifyCompany(CompanyAddDTO companyAddDTO) {
        if (companyAddDTO.getCompanyId() != null || StrUtilX.isEmpty(companyAddDTO.getCompanyName())
                || StrUtilX.isEmpty(companyAddDTO.getCompanyDomain()) || StrUtilX.isEmpty(companyAddDTO.getAdminName())
                || StrUtilX.isEmpty(companyAddDTO.getAdminAccount()) || StrUtilX.isEmpty(companyAddDTO.getAdminTel())) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
        int n = 0;
        OrgPO orgPO = new OrgPO();
        PicturePO picturePO = new PicturePO();
        UserPO userPO = new UserPO();
        Date date = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        //获取String类型的时间
        String createdate = sdf.format(date);
        orgPO.setUpdatedDt(createdate);
        orgPO.setUpdatedBy(companyAddDTO.getUpdatedBy());
        orgPO.setOrgName(companyAddDTO.getCompanyName());
        orgPO.setOrgDomian(companyAddDTO.getCompanyDomain());
        userPO.setUserName(companyAddDTO.getAdminName());
        userPO.setUserAccount(companyAddDTO.getAdminAccount());
        userPO.setUserTel(companyAddDTO.getAdminTel());
        userPO.setUpdatedBy(companyAddDTO.getUpdatedBy());
        orgPO.setOrgTel(companyAddDTO.getCompanyTel());
        orgPO.setOrgAddress(companyAddDTO.getCompanyAddress());
        orgPO.setOrgSummary(companyAddDTO.getCompanySummary());
        orgPO.setOrgId(companyAddDTO.getCompanyId());
        orgPO.setHotelInfoPermissions(companyAddDTO.getHotelInfoPermissions());
        OrgPO supplierAddPO1 = orgMapper.selectByPrimaryKey(orgPO.getOrgId());
        Integer userId = orgMapper.getUserId(supplierAddPO1.getOrgCode());
        if (userId == null) {
            userPO.setOrgCode(supplierAddPO1.getOrgCode());
            userPO.setCreatedBy(companyAddDTO.getCreatedBy());
            userService.addAdminUser(userPO, EndTypeEnum._0.no);
        } else {
            userPO.setUpdatedBy(companyAddDTO.getUpdatedBy());
            userPO.setUserId(userId);
            userService.modifyAdminUser(userPO);
        }
        orgMapper.updateByPrimaryKeySelective(orgPO);
        List<PictureLicenseDTO> pictureLicenseDTOS = pictureMapper.queryPictureLicense(supplierAddPO1.getOrgCode(), PictureTypeEnum.ONE.key);
        if (pictureLicenseDTOS.size() != 0) {
            for (int i = 0; i < companyAddDTO.getPictureList().size(); i++) {
                BeanUtils.copyProperties(companyAddDTO.getPictureList().get(i), picturePO);
                if (companyAddDTO.getPictureList().get(i).getPictureType() == 0) {
                    for (PictureLicenseDTO pictureLicenseDTO : pictureLicenseDTOS) {
                        if (companyAddDTO.getPictureList().get(i).getPictureUrl().equals(pictureLicenseDTO.getPictureUrl())) {
                            n++;
                        }
                    }
                    if (n == 0) {
                        picturePO.setCreatedDt(createdate);
                        picturePO.setCreatedBy(companyAddDTO.getUpdatedBy());
                        picturePO.setPictureUrl(companyAddDTO.getPictureList().get(i).getPictureUrl());
                        picturePO.setCompanyCode(supplierAddPO1.getOrgCode());
                        picturePO.setPictureName(companyAddDTO.getPictureList().get(i).getPictureName());
                        picturePO.setPictureType(PictureTypeEnum.ONE.key);
                        picturePO.setActive(1);
                        pictureMapper.insert(picturePO);
                    }
                } else {
                    List<PictureLicenseDTO> pictureLicenseDTOS1 = pictureMapper.queryPictureLicense(supplierAddPO1.getOrgCode(), PictureTypeEnum.TWO.key);
                    if (pictureLicenseDTOS1 != null) {
                        picturePO.setUpdatedBy(companyAddDTO.getUpdatedBy());
                        picturePO.setUpdatedDt(createdate);
                        picturePO.setPictureUrl(companyAddDTO.getPictureList().get(i).getPictureUrl());
                        picturePO.setCompanyCode(supplierAddPO1.getOrgCode());
                        picturePO.setPictureName(companyAddDTO.getPictureList().get(i).getPictureName());
                        picturePO.setPictureType(PictureTypeEnum.TWO.key);
                        picturePO.setId(pictureLicenseDTOS1.get(0).getPictureId());
                    }
                    pictureMapper.updateByPrimaryKeySelective(picturePO);
                }
            }
        }
    }

    @Override
    public void modifyCompanyStatus(CompanyAddDTO companyAddDTO) {
        if (companyAddDTO.getCompanyId() == null || companyAddDTO.getAvailableStatus() == null) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
        OrgPO orgPO = new OrgPO();
        orgPO.setAvailableStatus(companyAddDTO.getAvailableStatus());
        orgPO.setOrgId(companyAddDTO.getCompanyId());
        UserPO userPO = new UserPO();
        Date date = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        //获取String类型的时间
        String createdate = sdf.format(date);
        orgPO.setUpdatedDt(createdate);
        OrgPO orgPO1 = orgMapper.selectByPrimaryKey(orgPO);
        Integer userId = orgMapper.getUserId(orgPO1.getOrgCode());
        if (userId != null) {
            userPO.setOrgCode(orgPO1.getOrgCode());
            userPO.setAvailableStatus(companyAddDTO.getAvailableStatus());
            userPO.setUpdatedBy(companyAddDTO.getUpdatedBy());
            userPO.setUpdatedDt(createdate);
            orgMapper.updateAdminStatus(userPO);
        }
        orgMapper.updateByPrimaryKeySelective(orgPO);
    }

    @Override
    public CompanySelectDTO queryCompanyDetail(CompanyAddDTO companyAddDTO) {
        CompanySelectDTO companySelectDTO = orgMapper.companyList(companyAddDTO.getCompanyCode());
        List<CompanyBusinessLicenseUrlDTO> pictureLicenseDTOS = pictureMapper.PictureLicense(companyAddDTO.getCompanyCode(), PictureTypeEnum.ONE.key);
        List<CompanyBusinessLicenseUrlDTO> pictureLicenseDTOS1 = pictureMapper.PictureLicense(companyAddDTO.getCompanyCode(), PictureTypeEnum.TWO.key);
        List<SendEntityDTO> sendEntityList = orgMapper.queryCompanyOfficial(companyAddDTO.getCompanyCode());
        if (pictureLicenseDTOS.size() != 0) {
            companySelectDTO.setPictureList(pictureLicenseDTOS);

        }
        if (pictureLicenseDTOS1.size() != 0) {
            companySelectDTO.setCompanyLogoUrl(pictureLicenseDTOS1.get(0).getUrl());
            companySelectDTO.setCompanyLogoId(pictureLicenseDTOS1.get(0).getCompanyBusinessLicenseId());
        }

        if (CollUtilX.isNotEmpty(sendEntityList)) {
            companySelectDTO.setSendEntityList(sendEntityList);
        }
        return companySelectDTO;
    }

    @Override
    public PaginationSupportDTO<QueryCompanyListDTO> queryCompanyList(CompanyListRequest request) {
        PageHelper.startPage(request.getCurrentPage(), request.getPageSize());
        List<QueryCompanyListDTO> list = orgMapper.queryCompanyList(request);
        PageInfo<QueryCompanyListDTO> page = new PageInfo<>(list);
        PaginationSupportDTO<QueryCompanyListDTO> paginationSupport = new PaginationSupportDTO<>();
        paginationSupport.setItemList(list);
        paginationSupport.setPageSize(page.getPageSize());
        paginationSupport.setTotalCount(page.getTotal());
        paginationSupport.setTotalPage(page.getPages());
        paginationSupport.setCurrentPage(page.getPageNum());
        return paginationSupport;
    }

    @Override
    public isExitDTO isCompanyExit(CompanyAddDTO companyAddDTO) {
        if (StrUtilX.isEmpty(companyAddDTO.getCompanyDomain())) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
        CompanyDomainPO companyDomainPO = new CompanyDomainPO();
        BeanUtils.copyProperties(companyAddDTO, companyDomainPO);
        CompanySelectDTO domian = orgMapper.getDomian(companyAddDTO.getCompanyDomain());
        isExitDTO isExitDTO = new isExitDTO();
        int isExit = 0;
        if (domian != null) {
            isExit = 1;
        }
        isExitDTO.setIsExit(isExit);
        return isExitDTO;
    }

    @Override
    public Integer examineOrgName(@RequestBody ExamineOrgNameDTO examineOrgNameDTO) {
        if (StrUtilX.isEmpty(examineOrgNameDTO.getOrgName()) || examineOrgNameDTO.getType() == null) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
        if (examineOrgNameDTO.getType().equals(0)) {
            String supplierName = orgMapper.getSupplierName(examineOrgNameDTO);
            if (supplierName == null) {
                return 0;
            } else {
                return 1;
            }
        } else if (examineOrgNameDTO.getType().equals(1)) {
            String agentName = orgMapper.getAgentName(examineOrgNameDTO);
            if (agentName == null) {
                return 0;
            } else {
                return 1;
            }
        } else if (examineOrgNameDTO.getType().equals(2)) {
            String companyName = orgMapper.getCompanyName(examineOrgNameDTO);
            if (companyName == null) {
                return 0;
            } else {
                return 1;
            }
        }
        return null;
    }

    @Override
    public void modifyCompanyOfficialSeal(Map<String, String> request) {
        if (StrUtilX.isEmpty(request.get("companyCode")) || StrUtilX.isEmpty(request.get("officialSealUrl"))
                || StrUtilX.isEmpty(request.get("companySignature"))) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
        OrgOfficialSealPO orgPO = new OrgOfficialSealPO();
        orgPO.setCompanyCode(request.get("companyCode"));
        orgPO.setOfficialSealUrl(request.get("officialSealUrl"));
        orgPO.setCompanySignature(request.get("companySignature"));

        if (StrUtilX.isNotEmpty(request.get("officialSealId"))) {
            orgPO.setId(Integer.valueOf(request.get("officialSealId")));
            orgPO.setUpdatedBy(request.get("updatedBy"));
            orgPO.setUpdatedDt(DateUtilX.dateToString(new Date(), DateUtilX.hour_format));
            orgOfficialSealMapper.updateByPrimaryKeySelective(orgPO);
        } else {
            orgPO.setCreatedBy(request.get("updatedBy"));
            orgPO.setCreatedDt(DateUtilX.dateToString(new Date(), DateUtilX.hour_format));
            orgPO.setActive(1);
            orgOfficialSealMapper.insert(orgPO);
        }
    }

    @Override
    public void deleteCompanyOfficialSeal(Map<String, String> request) {
        if (StrUtilX.isEmpty(request.get("officialSealId"))) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
        List<Integer> ids = orgMapper.judgeExistSupplierOfficial(request.get("officialSealId"));
        if (CollUtilX.isNotEmpty(ids)) {
            throw new SysException(ErrorCodeEnum.SUPPLIER_USING_OFFICIAL_SEAL);
        }

        OrgOfficialSealPO orgPO = new OrgOfficialSealPO();
        orgPO.setId(Integer.valueOf(request.get("officialSealId")));
        orgPO.setUpdatedBy(request.get("updatedBy"));
        orgPO.setUpdatedDt(DateUtilX.dateToString(new Date(), DateUtilX.hour_format));
        orgPO.setActive(0);
        orgOfficialSealMapper.updateByPrimaryKeySelective(orgPO);
    }

    @Override
    public void deleteOrgPicture(Map<String, String> requestMap) {
        if (StrUtilX.isEmpty(requestMap.get("pictureId")) || StrUtilX.isEmpty(requestMap.get("pictureUrl"))) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
        PicturePO picturePO = new PicturePO();
        picturePO.setId(Integer.valueOf(requestMap.get("pictureId")));
        picturePO.setActive(0);
        picturePO.setUpdatedBy(requestMap.get("updatedBy"));
        picturePO.setUpdatedDt(DateUtilX.dateToString(new Date(), DateUtilX.hour_format));
        pictureMapper.updateByPrimaryKeySelective(picturePO);
    }

    @Override
    public void uploadCompanyBusinessLicenseUrl(List<CompanyBusinessLicenseUrlDTO> companyBusinessLicenseUrls, String updatedBy) {
        if (CollUtilX.isEmpty(companyBusinessLicenseUrls)) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
        List<PicturePO> picturePOS = new ArrayList<>();
        for (CompanyBusinessLicenseUrlDTO companyBusinessLicenseUrlDTO : companyBusinessLicenseUrls) {
            PicturePO picturePO = new PicturePO();
            picturePO.setActive(1);
            picturePO.setCompanyCode(companyBusinessLicenseUrlDTO.getCompanyCode());
            picturePO.setPictureUrl(companyBusinessLicenseUrlDTO.getUrl());
            picturePO.setPictureType(PictureTypeEnum.ONE.key);
            picturePO.setCreatedBy(updatedBy);
            picturePO.setCreatedDt(DateUtilX.dateToString(new Date(), DateUtilX.hour_format));

            picturePOS.add(picturePO);
        }
        pictureMapper.insertList(picturePOS);
    }

    @Override
    public void uploadCompanyLogoUrl(CompanyLogoUrlDTO companyLogoUrlDTO) {
        if (StrUtilX.isEmpty(companyLogoUrlDTO.getCompanyLogoUrl()) || StrUtilX.isEmpty(companyLogoUrlDTO.getCompanyCode())) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
        PicturePO picturePO = new PicturePO();
        picturePO.setActive(1);
        picturePO.setCompanyCode(companyLogoUrlDTO.getCompanyCode());
        picturePO.setPictureUrl(companyLogoUrlDTO.getCompanyLogoUrl());
        picturePO.setPictureType(PictureTypeEnum.TWO.key);
        picturePO.setCreatedBy(companyLogoUrlDTO.getUpdatedBy());
        picturePO.setCreatedDt(DateUtilX.dateToString(new Date(), DateUtilX.hour_format));

        pictureMapper.insert(picturePO);
    }

    @Override
    public void modifyCompanyCustomerTel(CompanyAddDTO companyAddDTO) {
        if (null == companyAddDTO.getCompanyId() || StrUtilX.isEmpty(companyAddDTO.getCustomerTel())){
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
        OrgPO orgPO = new OrgPO();
        orgPO.setCustomerTel(companyAddDTO.getCustomerTel());
        orgPO.setOrgId(companyAddDTO.getCompanyId());
        orgMapper.updateByPrimaryKeySelective(orgPO);
    }

    @Override
    public void modifyCompanyHrConfig(CompanyAddDTO companyAddDTO) {
        if (null == companyAddDTO.getCompanyId() || companyAddDTO.getHrIsOpen() == null){
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
        OrgPO orgPO = new OrgPO();
        orgPO.setHrIsOpen(companyAddDTO.getHrIsOpen());
        orgPO.setHrUrl(companyAddDTO.getHrUrl());
        orgPO.setHrSystemCode(companyAddDTO.getHrSystemCode());
        orgPO.setHrPublicKey(companyAddDTO.getHrPublicKey());
        orgPO.setHrPrivateKey(companyAddDTO.getHrPrivateKey());
        orgPO.setHrCallbackUrl(companyAddDTO.getHrCallbackUrl());
        orgPO.setOrgId(companyAddDTO.getCompanyId());
        orgMapper.updateByPrimaryKeySelective(orgPO);
    }

}




