package com.tiangong.organization.service.Impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.tiangong.config.EmailConfigProperties;
import com.tiangong.constanct.MqConstants;
import com.tiangong.organization.remote.dto.AgentCreditChangeMessageDTO;
import com.tiangong.organization.domain.entity.AgentCreditWarningLogPO;
import com.tiangong.organization.mapper.AgentCompanyMapper;
import com.tiangong.organization.mapper.AgentCreditWarningLogMapper;
import com.tiangong.organization.remote.dto.AgentCreditWarningConfig;
import com.tiangong.organization.service.AgentCreditWarningService;
import com.tiangong.req.SendEmailReq;
import com.tiangong.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * 客户信用额度预警服务实现类
 *
 * <AUTHOR>
 * @date 2024/12/19
 */
@Slf4j
@Service
public class AgentCreditWarningServiceImpl implements AgentCreditWarningService {

    @Autowired
    private AgentCompanyMapper agentCompanyMapper;

    @Autowired
    private AgentCreditWarningLogMapper creditWarningLogMapper;

    @Autowired
    private SendCodeUtil sendCodeUtil;
    @Autowired
    private RocketMQTemplate rocketMQTemplate;
    @Autowired
    private EmailConfigProperties emailConfigProperties;
    @Override
    public void checkAndSendWarning(AgentCreditChangeMessageDTO creditChangeMessage) {

            log.info("开始检查客户额度预警，客户编码：{}", creditChangeMessage.getAgentCode());
            // 获取客户预警配置
            List<AgentCreditWarningConfig> warningConfigList = getWarningConfig(Collections.singletonList(creditChangeMessage.getAgentCode()));
            if (CollUtilX.isEmpty(warningConfigList)) {
                log.info("客户编码：{}未配置预警信息，跳过预警检查", creditChangeMessage.getAgentCode());
                return;
            }
            AgentCreditWarningConfig warningConfig = warningConfigList.get(0);

            // 检查预警发送
            if (!CommonConstants.YES.equals(warningConfig.getCreditWarningEnabled()) || StrUtilX.isEmpty(warningConfig.getWarningEmails())) {
                log.info("客户{}不需要额度预警，跳过预警检查,是否开启额度预警:{},邮箱地址:{}", creditChangeMessage.getAgentCode(), warningConfig.getCreditWarningEnabled(), warningConfig.getWarningEmails());
                return;
            }
        try {
            // 判断是否需要发送预警
            BigDecimal currentBalance = warningConfig.getBalance();
            BigDecimal warningAmount = warningConfig.getWarningAmount();

            if (currentBalance.compareTo(warningAmount) <= 0) {
                log.info("客户{}当前余额{}小于等于预警额度{}，需要发送预警",
                        creditChangeMessage.getAgentCode(), currentBalance, warningAmount);
                // 发送预警邮件
                boolean sendResult = sendWarningEmail(warningConfig);
                // 记录预警日志
                saveWarningLog(warningConfig, sendResult, null);
                log.info("客户{}额度预警处理完成，发送结果：{}", creditChangeMessage.getAgentCode(), sendResult);
            } else {
                log.info("客户{}当前余额{}大于预警额度{}，无需发送预警",
                        creditChangeMessage.getAgentCode(), currentBalance, warningAmount);
            }
        } catch (Exception e) {
            log.error("处理客户额度预警异常，客户编码：{}", creditChangeMessage.getAgentCode(), e);
            saveWarningLog(warningConfig, false, e.getMessage());
        }
    }

    @Override
    public List<AgentCreditWarningConfig> getWarningConfig(List<String> agentCode) {
        return agentCompanyMapper.selectWarningConfigList(agentCode);
    }

    @Override
    public void sendWarningMsg(String agentCode) {
        try {
            // 构建MQ消息
            AgentCreditChangeMessageDTO agentCreditChangeMessageDTO=new AgentCreditChangeMessageDTO();
            agentCreditChangeMessageDTO.setAgentCode(agentCode);
            Message<String> message = MessageBuilder.withPayload(JSON.toJSONString(agentCreditChangeMessageDTO))
                    .setHeader("messageId", IdWorker.getId())
                    .build();
            // 发送MQ消息
            rocketMQTemplate.syncSend(MqConstants.CREDIT_CHANGE_TOPIC, message);
        }catch (Exception e){
            log.error("发送额度预警检查消息失败",e);
        }
    }

    public boolean sendWarningEmail(AgentCreditWarningConfig warningConfig) {
        try {
            // 构建邮件内容
            String emailContent = buildEmailContent(warningConfig);

            // 解析邮箱列表
            String[] emailArray = warningConfig.getWarningEmails().split(",");

            boolean allSuccess = true;

            // 向每个邮箱发送预警邮件
            for (String email : emailArray) {
                email = email.trim();
                if (StrUtilX.isEmpty(email)) {
                    continue;
                }

                try {
                    SendEmailReq sendEmailReq = new SendEmailReq();
                    sendEmailReq.setEmail(email);
                    sendEmailReq.setType(1);
                    sendEmailReq.setTopic(StrUtil.format("【{}】{}（{}）信用额度预警提醒",emailConfigProperties.getSendUserName(), warningConfig.getAgentName(), warningConfig.getAgentCode()));
                    sendEmailReq.setContent(emailContent);

                    boolean sendResult = sendCodeUtil.sendQQEmail(sendEmailReq);
                    if (!sendResult) {
                        log.error("发送预警邮件失败，客户：{}，邮箱：{}", warningConfig.getAgentCode(), email);
                        allSuccess = false;
                    } else {
                        log.info("发送预警邮件成功，客户：{}，邮箱：{}", warningConfig.getAgentCode(), email);
                    }

                } catch (Exception e) {
                    log.error("发送预警邮件异常，客户：{}，邮箱：{}", warningConfig.getAgentCode(), email, e);
                    allSuccess = false;
                }
            }

            return allSuccess;

        } catch (Exception e) {
            log.error("发送预警邮件异常，客户：{}", warningConfig.getAgentCode(), e);
            return false;
        }
    }

    /**
     * 构建邮件内容
     */
    private String buildEmailContent(AgentCreditWarningConfig warningDTO) {
        return StrUtil.format("{}（{}）的信用额度已经降到设置的预警额度值，请及时更改客户的信用额度，确保客户能够正常下单。", warningDTO.getAgentName(), warningDTO.getAgentCode());
    }

    /**
     * 保存预警日志
     */
    private void saveWarningLog(AgentCreditWarningConfig warningDTO, boolean sendResult, String errorMessage) {
        AgentCreditWarningLogPO logPO = new AgentCreditWarningLogPO();
        logPO.setAgentCode(warningDTO.getAgentCode());
        logPO.setWarningAmount(warningDTO.getWarningAmount());
        logPO.setCurrentBalance(warningDTO.getBalance());
        logPO.setWarningEmails(warningDTO.getWarningEmails());
        logPO.setSendStatus(sendResult ? 1 : 0);
        logPO.setErrorMessage(errorMessage);
        logPO.setCreatedBy("system");
        logPO.setCreatedDt(DateUtilX.dateToString(new Date(), "yyyy-MM-dd HH:mm:ss"));
        creditWarningLogMapper.insert(logPO);
    }
}
