package com.tiangong.organization.service.Impl;

import com.alibaba.fastjson.JSON;
import com.tiangong.enums.DeleteEnum;
import com.tiangong.enums.ErrorCodeEnum;
import com.tiangong.exception.SysException;
import com.tiangong.keys.RedisKey;
import com.tiangong.organization.domain.ContactPO;
import com.tiangong.organization.mapper.ContactMapper;
import com.tiangong.organization.mapper.OrgMapper;
import com.tiangong.organization.remote.dto.AgentAccountConfig;
import com.tiangong.organization.remote.dto.ContactAddDTO;
import com.tiangong.organization.service.ContactService;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.util.CollUtilX;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2019/6/22 11:46
 **/
@Service
@Slf4j
public class ContactServiceImpl implements ContactService {

    @Autowired
    private ContactMapper contactMapper;

    @Autowired
    private OrgMapper orgMapper;

    @Override
    public void addContact(ContactAddDTO contactAddDTO) {
        if (StrUtilX.isEmpty(contactAddDTO.getOrgCode()) || contactAddDTO.getOrgType() == null ||
                StrUtilX.isEmpty(contactAddDTO.getContactName()) || StrUtilX.isEmpty(contactAddDTO.getContactTel())) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
        ContactPO contactPO = new ContactPO();
        BeanUtils.copyProperties(contactAddDTO, contactPO);
        contactPO.setActive(DeleteEnum.STATUS_EXIST.key);
        Date date = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        //获取String类型的时间
        String createdate = sdf.format(date);
        contactPO.setCreatedDt(createdate);
        if (contactAddDTO.getContactType() == 0) {
            if (contactAddDTO.getContactRole().equals("0")) {
                orgMapper.updateContactRole(contactAddDTO.getOrgCode(), "", contactAddDTO.getContactRole(), 0);
                orgMapper.updateContactRole(contactAddDTO.getOrgCode(), "1", "0,1", contactAddDTO.getContactType());
            }
            if (contactAddDTO.getContactRole().equals("1")) {
                orgMapper.updateContactRole(contactAddDTO.getOrgCode(), "", contactAddDTO.getContactRole(), contactAddDTO.getContactType());
                orgMapper.updateContactRole(contactAddDTO.getOrgCode(), "0", "0,1", contactAddDTO.getContactType());
            } else if (contactAddDTO.getContactRole().equals("0,1")) {
                orgMapper.updateContactRole(contactAddDTO.getOrgCode(), "", "0", contactAddDTO.getContactType());
                orgMapper.updateContactRole(contactAddDTO.getOrgCode(), "", "1", contactAddDTO.getContactType());
                orgMapper.updateContactRole(contactAddDTO.getOrgCode(), "", "0,1", contactAddDTO.getContactType());
            }
        } else {
            ContactPO po = new ContactPO();
            po.setContactType(contactAddDTO.getContactType());
            po.setOrgType(contactAddDTO.getOrgType());
            po.setContactRole(contactAddDTO.getContactRole());
            po.setOrgCode(contactAddDTO.getOrgCode());
            po.setActive(DeleteEnum.STATUS_EXIST.key);
            List<ContactPO> select = contactMapper.select(po);
            if (select != null && select.size() > 0) {
                throw new SysException(ErrorCodeEnum.CONTACT_ROLE_IS_ALREADY);
            }
        }
        contactMapper.insert(contactPO);
        cacheContract(contactPO);
    }

    /**
     * 缓存业务联系人信息
     */
    private void cacheContract(ContactPO contactPO) {
        Example example = new Example(ContactPO.class);
        example.createCriteria().andLike("contactRole", "%1%")
                .andEqualTo("active", 1).andEqualTo("orgCode", contactPO.getOrgCode());
        List<ContactPO> contactPOS = contactMapper.selectByExample(example);
        if (CollUtilX.isNotEmpty(contactPOS)) {
            ContactPO contact = contactPOS.stream().findFirst().get();
            AgentAccountConfig agentAccountConfig = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.AGENT_ACCOUNT_CONFIG, contact.getOrgCode()), AgentAccountConfig.class);
            Optional.ofNullable(agentAccountConfig).ifPresent(agentAccount ->
            {
                agentAccountConfig.setLinkman(contact.getContactName());
                agentAccountConfig.setLinkmanEmail(contact.getContactEmail());
                agentAccountConfig.setLinkmanTel(contact.getContactTel());
                HashMap<String, String> map = new HashMap<>();
                map.put(contact.getOrgCode(), JSON.toJSONString(agentAccountConfig));
                RedisTemplateX.hPutAll(RedisKey.AGENT_ACCOUNT_CONFIG, map);
            });
        } else {
            AgentAccountConfig agentAccountConfig = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.AGENT_ACCOUNT_CONFIG, contactPO.getOrgCode()), AgentAccountConfig.class);
            Optional.ofNullable(agentAccountConfig).ifPresent(agentAccount ->
            {
                agentAccountConfig.setLinkman(null);
                agentAccountConfig.setLinkmanEmail(null);
                agentAccountConfig.setLinkmanTel(null);
                HashMap<String, String> map = new HashMap<>();
                map.put(contactPO.getOrgCode(), JSON.toJSONString(agentAccountConfig));
                RedisTemplateX.hPutAll(RedisKey.AGENT_ACCOUNT_CONFIG, map);
            });
        }
    }


    @Override
    public void modifyContact(ContactAddDTO contactAddDTO) {
        if (StrUtilX.isEmpty(contactAddDTO.getOrgCode()) || contactAddDTO.getContactId() == null ||
                StrUtilX.isEmpty(contactAddDTO.getContactName()) || StrUtilX.isEmpty(contactAddDTO.getContactTel())) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
        ContactPO contactPO = new ContactPO();
        BeanUtils.copyProperties(contactAddDTO, contactPO);
        Date date = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        //获取String类型的时间
        String createdate = sdf.format(date);
        contactPO.setUpdatedDt(createdate);
        if (contactAddDTO.getContactType() == 0) {
            switch (contactAddDTO.getContactRole()) {
                case "":
                    contactMapper.updateByPrimaryKeySelective(contactPO);
                    break;
                case "0":
                    orgMapper.updateContactRole(contactAddDTO.getOrgCode(), "", contactAddDTO.getContactRole(), 0);
                    orgMapper.updateContactRole(contactAddDTO.getOrgCode(), "1", "0,1", contactAddDTO.getContactType());
                    contactMapper.updateByPrimaryKeySelective(contactPO);
                    break;
                case "1":
                    orgMapper.updateContactRole(contactAddDTO.getOrgCode(), "", contactAddDTO.getContactRole(), 0);
                    orgMapper.updateContactRole(contactAddDTO.getOrgCode(), "0", "0,1", contactAddDTO.getContactType());
                    contactMapper.updateByPrimaryKeySelective(contactPO);
                    break;
                case "0,1":
                    orgMapper.updateContactRole(contactAddDTO.getOrgCode(), "", "0", 0);
                    orgMapper.updateContactRole(contactAddDTO.getOrgCode(), "", "1", contactAddDTO.getContactType());
                    orgMapper.updateContactRole(contactAddDTO.getOrgCode(), "", "0,1", contactAddDTO.getContactType());
                    contactMapper.updateByPrimaryKeySelective(contactPO);
                    break;
            }
        } else {
            ContactPO po = new ContactPO();
            po.setContactType(contactAddDTO.getContactType());
            po.setOrgType(contactAddDTO.getOrgType());
            po.setContactRole(contactAddDTO.getContactRole());
            po.setOrgCode(contactAddDTO.getOrgCode());
            po.setActive(DeleteEnum.STATUS_EXIST.key);
            List<ContactPO> select = contactMapper.select(po);
            if (select != null && select.size() > 0) {
                ContactPO po1 = select.get(0);
                if (!Objects.equals(po1.getContactId(), contactPO.getContactId())) {
                    throw new SysException(ErrorCodeEnum.CONTACT_ROLE_IS_ALREADY);
                }
            }
            contactMapper.updateByPrimaryKeySelective(contactPO);
        }
        cacheContract(contactPO);
    }

    @Override
    public void deleteContact(ContactAddDTO contactAddDTO) {
        if (contactAddDTO.getContactId() == null) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
        ContactPO contactPO = new ContactPO();
        BeanUtils.copyProperties(contactAddDTO, contactPO);
        Date date = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        //获取String类型的时间
        String createdate = sdf.format(date);
        contactPO.setCreatedDt(createdate);
        contactPO.setActive(DeleteEnum.STATUS_DELECT.key);
        contactMapper.updateByPrimaryKeySelective(contactPO);

        if (contactAddDTO.getContactType() == 0) {
            ContactPO contact = contactMapper.selectByPrimaryKey(contactPO);
            AgentAccountConfig agentAccountConfig = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.AGENT_ACCOUNT_CONFIG, contact.getOrgCode()), AgentAccountConfig.class);
            Optional.ofNullable(agentAccountConfig).ifPresent(agentAccount ->
            {
                if (Objects.equals(contactPO.getContactName(), agentAccount.getLinkman())) {
                    agentAccountConfig.setLinkman(null);
                    agentAccountConfig.setLinkmanEmail(null);
                    agentAccountConfig.setLinkmanTel(null);
                }
                HashMap<String, String> map = new HashMap<>();
                map.put(contact.getOrgCode(), JSON.toJSONString(agentAccountConfig));
                RedisTemplateX.hPutAll(RedisKey.AGENT_ACCOUNT_CONFIG, map);
            });
        }
    }
}
