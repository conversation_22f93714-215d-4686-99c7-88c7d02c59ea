package com.tiangong.organization.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tiangong.common.Response;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.organization.domain.InvoicePO;
import com.tiangong.organization.domain.req.DelInvoiceReq;
import com.tiangong.organization.domain.req.InvoiceReq;
import com.tiangong.organization.domain.req.QueryInvoiceReq;
import com.tiangong.organization.remote.dto.InvoiceDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023/12/12 10:59
 */
public interface InvoiceService extends IService<InvoicePO> {

    /**
     * 新增或修改发票主体
     */
    void saveOrUpdateInvoice(InvoiceReq req);

    /**
     * 查询发票主体分页
     */
    PaginationSupportDTO<InvoicePO> invoiceListPage(QueryInvoiceReq req);

    /**
     * 查询发票主体列表
     */
    List<InvoicePO> invoiceList(QueryInvoiceReq req);

    /**
     * 删除发票主体
     */
    void delInvoice(DelInvoiceReq req);

    /**
     * 查询公司发票主体列表（不分页）
     */
    List<InvoicePO> companyInvoiceList(QueryInvoiceReq req);

    /**
     * 查询发票
     * @param invoiceId 开票主体id
     * @return 返回开票信息
     */
    InvoiceDTO queryInvoice(Integer invoiceId);
}
