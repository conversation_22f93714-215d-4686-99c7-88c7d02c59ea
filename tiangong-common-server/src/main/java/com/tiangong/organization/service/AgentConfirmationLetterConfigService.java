package com.tiangong.organization.service;

import com.tiangong.organization.domain.req.AgentConfirmationLetterConfigAditReq;
import com.tiangong.organization.remote.dto.AgentConfirmationLetterConfigResp;


/**
 * 客户确认函配置服务接口
 */
public interface AgentConfirmationLetterConfigService {

    /**
     * 新增配置
     */
    Long confirmationLetterAdd(AgentConfirmationLetterConfigAditReq req);

    /**
     * 修改配置
     */
    Long confirmationLetterEdit(AgentConfirmationLetterConfigAditReq req);

    /**
     * 删除配置
     */
    void delete(Long id);

    /**
     * 根据客户编码获取配置
     */
    AgentConfirmationLetterConfigResp getByAgentCode(String agentCode);


}