package com.tiangong.organization.service;

import com.tiangong.common.Response;
import com.tiangong.organization.remote.dto.ContactAddDTO;

/**
 * <AUTHOR>
 * @date 2019/6/22 11:43
 **/
public interface ContactService {
    /**
     * 新增联系人
     */
    void addContact(ContactAddDTO ContactAddDTO);

    /**
     * 修改联系人信息
     */
    void modifyContact(ContactAddDTO ContactAddDTO);

    /**
     * 删除联系人信息
     */
    void deleteContact(ContactAddDTO contactAddDTO);
}
