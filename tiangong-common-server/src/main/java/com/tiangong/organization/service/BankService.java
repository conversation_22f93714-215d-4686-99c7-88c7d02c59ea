package com.tiangong.organization.service;

import com.tiangong.common.Response;
import com.tiangong.dto.common.BankBalanceChangeReq;
import com.tiangong.dto.common.BankListReq;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.organization.domain.req.BankListPageReq;
import com.tiangong.organization.domain.req.BankLogReq;
import com.tiangong.organization.domain.req.ModifiedBankStatusReq;
import com.tiangong.organization.domain.resp.BankLogDTO;
import com.tiangong.organization.domain.resp.BankPageResp;
import com.tiangong.organization.remote.dto.BankAddDTO;
import com.tiangong.organization.remote.dto.BankDetailResp;
import com.tiangong.organization.remote.dto.BankSupplierDTO;
import com.tiangong.organization.remote.dto.QueryBankDetailReq;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/6/21 21:58
 **/
public interface BankService {
    /**
     * 新增银行
     */
    void addBank(BankAddDTO BankAddDTO);

    /**
     * 修改银行卡信息
     */
    void modifyBank(BankAddDTO BankAddDTO);

    /**
     * 删除银行卡信息
     */
    void deleteBank(BankAddDTO BankAddDTO);

    /**
     * 银行卡列表信息
     */
    Response<List<BankSupplierDTO>> queryBankList(BankListReq req);

    /**
     * 银行卡分页
     */
    Response<PaginationSupportDTO<BankPageResp>> queryBankPage(BankListPageReq req);

    /**
     * 查询银行流水分页
     */
    Response<PaginationSupportDTO<BankLogDTO>> bankLogPage(BankLogReq req);

    /**
     * 银行卡金额变动
     */
    void bankBalanceChange(BankBalanceChangeReq req);

    /**
     * 修改银行卡状态
     */
    Response<Object> modifiedBankStatus(ModifiedBankStatusReq req);

    /**
     * 查询银行卡详情
     */
    Response<BankDetailResp> bankDetail(QueryBankDetailReq req);
}
