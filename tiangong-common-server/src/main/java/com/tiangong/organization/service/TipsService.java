package com.tiangong.organization.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tiangong.common.Response;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.organization.domain.SysConfigPO;
import com.tiangong.organization.domain.TipsPO;
import com.tiangong.organization.domain.resp.TipsReq;
import com.tiangong.organization.domain.resp.TipsResp;

import java.util.List;

public interface TipsService extends IService<TipsPO> {

    /**
     * 新增或修改提示信息
     */
    void addOrUpdate(TipsPO po);

    /**
     * 查询提示信息列表（分页）
     */
    PaginationSupportDTO<TipsResp> queryConfigList(TipsReq req);

    /**
     * 删除提示信息
     */
    void del(int id);
}
