package com.tiangong.organization.service;

import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.common.Response;
import com.tiangong.organization.remote.dto.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/6/28 11:26
 **/
public interface CompanyService {
    /**
     * 新增企业信息
     */
    void addCompany(CompanyAddDTO companyAddDTO);

    /**
     * 修改企业信息
     */
    void modifyCompany(CompanyAddDTO companyAddDTO);

    /**
     * 修改企业启动状态
     */
    void modifyCompanyStatus(CompanyAddDTO companyAddDTO);

    /**
     * 根据企业编码查询企业详细信息
     */
    CompanySelectDTO queryCompanyDetail(CompanyAddDTO companyAddDTO);

    /**
     * 运营商列表
     */
    PaginationSupportDTO<QueryCompanyListDTO> queryCompanyList(CompanyListRequest request);

    /**
     * 查询域名是否存在
     */
    isExitDTO isCompanyExit(CompanyAddDTO companyAddDTO);

    /**
     * 查询机构名称是否已存在
     */
    Integer examineOrgName(ExamineOrgNameDTO examineOrgNameDTO);

    /**
     * 修改企业公章
     */
    void modifyCompanyOfficialSeal(Map<String, String> request);

    /**
     * 修改企业公章
     */
    void deleteCompanyOfficialSeal(Map<String, String> request);

    /**
     * 删除企业logo和营业执照
     */
    void deleteOrgPicture(Map<String, String> requestMap);

    /**
     * 上传营业执照
     */
    void uploadCompanyBusinessLicenseUrl(List<CompanyBusinessLicenseUrlDTO> companyBusinessLicenseUrls, String updatedBy);

    /**
     * 上传公司log
     */
    void uploadCompanyLogoUrl(CompanyLogoUrlDTO companyLogoUrlDTO);

    /**
     * 修改客服电话
     */
    void modifyCompanyCustomerTel(CompanyAddDTO companyAddDTO);

    /**
     * 修改企业hr配置信息
     */
    void modifyCompanyHrConfig(CompanyAddDTO companyAddDTO);
}
