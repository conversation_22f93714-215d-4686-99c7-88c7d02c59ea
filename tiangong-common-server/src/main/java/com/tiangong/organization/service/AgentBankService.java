package com.tiangong.organization.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tiangong.common.Response;
import com.tiangong.organization.domain.AgentBankPO;
import com.tiangong.organization.domain.req.AgentBankIdReq;
import com.tiangong.organization.domain.req.AgentBankReq;

/**
 * <AUTHOR>
 * @create 2023/12/16 16:41
 */
public interface AgentBankService extends IService<AgentBankPO> {

    /**
     * 新增客户个人收款账户
     */
    void addAgentBank(AgentBankReq req);

    /**
     * 移除客户个人收款账户
     */
    void delAgentBank(AgentBankIdReq req);
}
