package com.tiangong.organization.service;

import com.tiangong.agent.dto.AgentConfigDTO;
import com.tiangong.common.Response;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.organization.domain.req.*;
import com.tiangong.organization.domain.resp.QueryOrderAgentDetailResp;
import com.tiangong.organization.remote.dto.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/6/26 20:19
 **/
public interface AgentService {
    /**
     * 新增客户
     */
    void addAgent(AgentAddReq agentAddDTO);

    /**
     * 新增客户合作商
     */
    void addPartner(AddPartnerReq req);

    /**
     * 修改客户启用状态
     */
    void modifyAgentStatus(AgentUpdateStatusReq agentAddDTO);

    /**
     * 修改客户启用状态
     */
    void modifyPartnerStatus(PartnerStatusReq req);

    /**
     * 修改客户信息
     */
    void modifyAgent(AgentUpdateReq agentAddDTO);

    /**
     * 修改客户小数位和取整方式
     */
    void modifyAgentMath(AgentUpdateMathReq agentAddDTO);

    /**
     * 修改客户api配置信息
     */
    void modifyAgentApiConfig(AgentApiConfigUpdateReq request);

    /**
     * 修改客户api接口频次信息
     */
    void modifyAgentApiInvokeConfig(OrgAgentApiInvokeConfigReq request);

    /**
     * 添加客户api接口频次信息
     */
    void addAgentApiInvokeConfig(OrgAgentApiInvokeConfigReq request);

    /**
     * 新增或更新客户api接口频次信息
     */
    void addOrEditAgentApiInvokeConfig(OrgAgentApiInvokeConfigReq request);

    /**
     * 根据客户Code,查询客户详情
     */
    AgentSelectDTO queryAgentDetail(AgentQueryDetailByCodeReq agentAddDTO);

    /**
     * 根据客户Code,查询客户基本信息
     */
    AgentSelectDTO queryAgentBaseInfo(AgentQueryDetailByCodeReq request);

    /**
     * 查询客户列表
     */
    PaginationSupportDTO<QueryAgentListDTO> queryAgentList(AgentListRequest request);

    /**
     * 扣退额度
     * @param agentCreditLineList 额度变更列表
     * @return 操作结果，包含成功更新的记录条数
     */
    AgentCreditLineResultDTO deductRefundCreditLine(List<AgentCreditLineDTO> agentCreditLineList);

    /**
     * 修改API密钥
     */
    void modifySecret(OrgAgentApiSecretReq orgAgentApiConfigReq);

    /**
     * 验证密码获取手机号
     */
    String getPhoneAsPassword(ContactAsPhoneDTO contact);

    /**
     * 修改客户手机号
     */
    void modifyAgentPhone(ContactAsPhoneDTO contact);

    /**
     * 绑定个人付款银行卡
     */
    void addAgentBank(AgentBankReq req);

    /**
     * 移除个人账户付款银行卡
     */
    void delAgentBank(AgentBankIdReq req);

    /**
     * 重置管理员密码
     */
    void resetAccountPassword(AgentQueryDetailByCodeReq agentAddDTO);

    /**
     * 查询订单客户信息
     */
    Response<QueryOrderAgentDetailResp> queryOrderAgentDetail(QueryOrderAgentDetailReq agentDetailReq);

    /**
     * 初始化推送协议订单结算成本客户编码到缓存任务
     */
    void initPushProtocolOrderSettlementCostAgentCodeToRedisTask(String param);

    /**
     * 修改客户附加信息
     */
    void modifyAgentAdditionInfo(AgentAdditionInfoReq agentAddDTO);

    /**
     * 加载合作商配置信息到缓存
     */
    void loadAgentApiConfigToCache(String partnerCode, String agentCod);

    /**
     * 修改客户优选产品状态
     */
    void modifyPreferredProductsStatus(PreferredProductsStatusReq request);

    /**
     * 获取客户配置
     *
     * @param partnerCode 合作编码
     * @return
     */
    AgentConfigDTO getAgentConfig(String partnerCode);

    /**
     * 设置支付超时自动取消配置
     *
     * @param request
     */
    void setPaymentOvertimeCancelConfig(PaymentOvertimeCancelConfigReq request);
}
