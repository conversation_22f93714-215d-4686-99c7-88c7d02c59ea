package com.tiangong.organization.domain.req;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data

public class ImportSupplierLabelConfigExportReq {

    /**
     * 客户编码
     */
    @ExcelProperty("客户编码")
    private String agentCode;

    /**
     * 标签类型
     * @see com.tiangong.enums.LabelTypeEnum
     */
    @ExcelProperty("标签类型")
    private String labelType;

    /**
     * 供应商编码
     */
    @ExcelProperty("供应商编码")
    private String supplyCode;

    /**
     * 失败原因
     */
    @ExcelProperty("失败原因")
    private String errorMsg;
}
