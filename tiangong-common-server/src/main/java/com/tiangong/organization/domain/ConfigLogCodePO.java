package com.tiangong.organization.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("sys_config_log_code")
public class ConfigLogCodePO {

    @TableId(type = IdType.AUTO)
    public Integer id;

    public String logCode;

    public String logContent;
}
