package com.tiangong.organization.domain.req;

import com.tiangong.dto.common.BasePO;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @create 2023/10/25 15:07
 */
@Data
public class AgentUpdateStatusReq extends BasePO {

    /**
     * 客户Id
     */
    @NotNull(message = "EMPTY_PARAM_AGENTID")
    private Integer agentId;

    @NotEmpty(message = "EMPTY_PARAM_AGENTCODE")
    private String agentCode;


    /**
     * 客户启用状态
     */
    @NotNull(message = "EMPTY_PARAM_AVAILABLESTATUS")
    private Integer availableStatus;

    /**
     * companyCode
     */
    private String companyCode;
}
