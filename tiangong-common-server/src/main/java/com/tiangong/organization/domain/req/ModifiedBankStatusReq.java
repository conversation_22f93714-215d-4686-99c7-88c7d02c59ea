package com.tiangong.organization.domain.req;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @create 2023/12/19 14:56
 */
@Data
public class ModifiedBankStatusReq {

    /**
     * 银行卡id
     */
    @NotNull(message = "EMPTY_PARAM_ID")
    private Integer bankId;

    /**
     * 1启用 2禁用
     */
    @NotNull(message = "EMPTY_PARAM_STATUS")
    private Integer active;
}
