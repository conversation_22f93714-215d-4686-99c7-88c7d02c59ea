package com.tiangong.organization.domain.req;

import com.tiangong.dto.common.BasePO;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2023/10/25 15:07
 */
@Data
public class AgentUpdateReq extends BasePO {

    /**
     * 渠道编码
     */
    //@NotEmpty(message = "渠道编码不能为空")
    private String channelCode;

    /**
     * 客户名称
     */
    @NotEmpty(message = "EMPTY_PARAM_AGENTNAME")
    private String agentName;

    /**
     * 结算方式
     */
    @NotNull(message = "EMPTY_PARAM_SETTLEMENTTYPE")
    private Integer settlementType;

    /**
     * 客户手机号
     */
    private String agentTel;

    /**
     * 总管理员手机号
     */
    private String adminTel;

    /**
     * 客户Id
     */
    private Integer agentId;

    /**
     * 总管理员姓名
     */
    private String adminName;
    /**
     * 总管理员账号
     */
    private String adminAccount;

    /**
     * 信用额度
     */
    @NotEmpty(message = "EMPTY_PARAM_CREDITLINE")
    private String creditLine;
    /**
     * 我司销售经理ID
     */
    private Integer saleManagerId;

    /**
     * 客户编码
     */
    @NotEmpty(message = "EMPTY_PARAM_AGENTCODE")
    private String agentCode;

    /**
     * 客户类型
     */
    private Integer agentType;

    /**
     * companyCode
     */
    private String companyCode;

    /**
     * 域名
     */
    private String orgDomain;

    /**
     * 企业唯一码
     */
    private String orgUniqueCode;

    /**
     * 国家编码
     */
    private String countryCode;

    /**
     * 国家名称
     */
    private String countryName;

    /**
     * 省份编码
     */
    private String provinceCode;

    /**
     * 省份名称
     */
    private String provinceName;

    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 企业地址/客户地址
     */
    private String orgAddress;

    /**
     * 企业成立日期
     */
    private String establishedDate;

    /**
     * 我方合作主题
     */
    @NotNull(message = "EMPTY_PARAM_INVOICEID")
    private Integer invoiceId;

    /**
     * 结算模式：0挂账 1预存
     */
    @NotNull(message = "SETTLED_TYPE")
    private Integer settledType;

    /**
     * 描述
     */
    private String remark;

    /**
     * 结算策略(0按下单 1按结算成本)
     */
    private Integer settlementStrategy;

    /**
     * 推送结算成本(0关 1开)
     */
    private Integer pushSettleCostSwitch;

    /**
     * 推送结算成本地址
     */
    private String pushSettleCostUrl;

    /**
     * 是否开启额度预警：0否 1是
     */
    private Integer creditWarningEnabled;

    /**
     * 预警额度
     */
    private BigDecimal warningAmount;

    /**
     * 额度预警邮箱，多个邮箱用逗号分隔
     */
    private String warningEmails;

    /**
     * 额度账户类型 0：自己 1：其他客户 默认自己
     */
    private Integer lineAccountType;

    /**
     * 额度账户
     */
    private String lineAccount;
}
