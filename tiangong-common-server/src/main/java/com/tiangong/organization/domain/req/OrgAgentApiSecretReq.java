package com.tiangong.organization.domain.req;

import com.tiangong.dto.common.BaseRequest;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 分销api配置表
 * 请求参数
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-18 12:44:15
 */
@Data
public class OrgAgentApiSecretReq extends BaseRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * Id
     */
    @NotNull(message = "EMPTY_PARAM_ID")
    private Integer id;

    /**
     * 密钥
     */
    @NotEmpty(message = "EMPTY_PARAM_SECRETKEY")
    private String secretKey;

    /**
     * 修改人
     */
    private String updatedBy;

}