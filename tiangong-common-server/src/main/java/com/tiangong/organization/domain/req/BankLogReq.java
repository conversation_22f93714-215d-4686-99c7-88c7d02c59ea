package com.tiangong.organization.domain.req;

import com.tiangong.cloud.common.domain.BasePage;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @create 2023/12/14 19:29
 */
@Data
public class BankLogReq extends BasePage {

    /**
     * 银行流水类型(收付类型)
     */
    private Integer bankLogType;

    /**
     * 交易类型
     */
    private Integer changeType;

    /**
     * 交易开始时间
     */
    private String changeBeginTime;

    /**
     * 交易结束时间
     */
    private String changeEndTime;

    /**
     * 机构编码
     */
    @NotEmpty(message = "EMPTY_PARAM_ORGCODE")
    private String orgCode;

    private Integer bankId;
}
