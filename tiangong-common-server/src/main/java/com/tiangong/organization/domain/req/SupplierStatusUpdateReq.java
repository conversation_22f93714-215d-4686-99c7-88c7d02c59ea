package com.tiangong.organization.domain.req;

import com.tiangong.dto.common.BasePO;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @create 2023/10/25 16:18
 */
@Data
public class SupplierStatusUpdateReq extends BasePO {
    /**
     * 供应商企业名称
     */
    private String supplierName;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 供应商Id
     */
    @NotNull(message = "EMPTY_PARAM_SUPPLIERID")
    private Integer supplierId;

    /**
     * 启用状态
     */
    @NotNull(message = "EMPTY_PARAM_AVAILABLESTATUS")
    private Integer AvailableStatus;

    /**
     * 商家编码
     */
    private String companyCode;
}
