package com.tiangong.organization.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tiangong.dto.common.BasePO;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2023/12/16 16:36
 */
@Data
@TableName("t_org_agent_bank")
public class AgentBankPO extends BasePO {

    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 客户id
     */
    private Integer agentId;

    /**
     * 银行卡id
     */
    private Integer bankId;
}
