package com.tiangong.organization.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tiangong.dto.common.BasePO;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2023/12/18 11:21
 */
@Data
@TableName("t_org_supplier_label_config")
public class SupplierLabelConfigPO extends BasePO {

    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 客户id
     */
    private Integer agentId;

    //业务类型 1国内业务 2海外以为
    //private Integer serviceType;

    /**
     * 标签类型
     * @see com.tiangong.enums.LabelTypeEnum
     */
    private Integer labelType;

    /**
     * 供应商id
     */
    private Integer supplierId;
}
