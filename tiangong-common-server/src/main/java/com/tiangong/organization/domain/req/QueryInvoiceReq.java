package com.tiangong.organization.domain.req;

import com.tiangong.cloud.common.domain.BasePage;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @create 2023/12/12 11:04
 */
@Data
public class QueryInvoiceReq extends BasePage {

    @NotNull(message = "EMPTY_PARAM_TYPE")
    private Integer type;

    //@NotEmpty(message = "企业编码不能为空")
    private String orgCode;

    //公司名称
    private String companyName;
}
