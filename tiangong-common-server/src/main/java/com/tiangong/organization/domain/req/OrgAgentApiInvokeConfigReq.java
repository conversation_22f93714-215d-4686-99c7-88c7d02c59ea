package com.tiangong.organization.domain.req;

import com.tiangong.dto.common.BaseRequest;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 分销api接口频次表
 * 请求参数
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-18 12:44:15
 */
@Data
public class OrgAgentApiInvokeConfigReq extends BaseRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * Id
     */
    private Integer id;
    /**
     * 客户编码
     */
    @NotEmpty(message = "EMPTY_PARAM_AGENTCODE")
    private String agentCode;

    /**
     * 合作商编码
     */
    @NotEmpty(message = "EMPTY_PARAM_PARTNERCODE")
    private String partnerCode;
    /**
     * 接口名称
     */
    @NotEmpty(message = "EMPTY_PARAM_METHODNAME")
    private String methodName;
    /**
     * 接口编码
     */
    @NotEmpty(message = "EMPTY_PARAM_METHODCODE")
    private String methodCode;
    /**
     * 调用频次
     */
    @NotNull(message = "EMPTY_PARAM_INVOKETIMES")
    private Integer invokeTimes;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private Date createdDt;
    /**
     * 修改人
     */
    private String updatedBy;
    /**
     * 修改时间
     */
    private Date updatedDt;
    /**
     * 删除状态
     */
    private Integer deleted;
}