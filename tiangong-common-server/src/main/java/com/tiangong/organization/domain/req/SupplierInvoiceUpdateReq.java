package com.tiangong.organization.domain.req;

import com.tiangong.dto.common.BasePO;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @create 2023/10/19 15:58
 */
@Data
public class SupplierInvoiceUpdateReq extends BasePO {

    /**
     * 供应商Id
     */
    @NotNull(message = "EMPTY_PARAM_SUPPLIERID")
    private Integer supplierId;

    /**
     * 供应商编码
     */
    @NotEmpty(message = "EMPTY_PARAM_SUPPLYCODE")
    private String supplierCode;

    /**
     * 发票类型：1普票 2专票
     */
    @NotNull(message = "EMPTY_PARAM_INVOICETYPE")
    private Integer invoiceType;

    /**
     * 发票模式: 1商家开票 2酒店前台开票
     */
    @NotNull(message = "EMPTY_PARAM_INVOICEMODEL")
    private Integer invoiceModel;
}
