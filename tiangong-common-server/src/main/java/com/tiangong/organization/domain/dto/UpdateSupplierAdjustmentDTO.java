package com.tiangong.organization.domain.dto;

import com.tiangong.dto.common.BasePO;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2023/10/19 15:58
 */
@Data
public class UpdateSupplierAdjustmentDTO extends BasePO {

    /**
     * 供应商Id
     */
    private Integer supplierId;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 增量类型0加数值 1减数值 2加百分比 3减百分比 4等于
     */
    private Integer adjustmentType;

    /**
     * 调整金额
     */
    private BigDecimal modifiedAmt;

    /**
     * 发票类型：1普票 2专票
     */
    private Integer invoiceType;

    /**
     * 发票模式: 1商家开票 2酒店前台开票
     */
    private Integer invoiceModel;

}
