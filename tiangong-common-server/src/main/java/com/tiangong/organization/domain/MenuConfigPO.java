package com.tiangong.organization.domain;

import com.tiangong.sensitive.SensitiveClass;
import lombok.Data;

import javax.persistence.*;

@Data
@SensitiveClass
@Table(name = "s_menu_config")
public class MenuConfigPO {

    /**
     * 地址
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private String path;

    /**
     * 内容
     */
    @Column(name = "content")
    private String content;

}
