package com.tiangong.organization.domain.req;

import com.tiangong.dto.common.BaseDTO;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @create 2023/12/18 11:27
 */
@Data
public class SupplierLabelConfigReq extends BaseDTO {

    private Integer id;

    /**
     * 客户id
     */
    @NotNull(message = "EMPTY_PARAM_AGENTID")
    private Integer agentId;

    /**
     * 业务类型 1国内业务 2全国
     */
    private Integer serviceType;

    /**
     * 标签类型
     * @see com.tiangong.enums.LabelTypeEnum
     */
    @NotNull(message = "EMPTY_PARAM_LABELTYPE")
    private Integer labelType;

    /**
     * 供应商id
     */
    @NotNull(message = "EMPTY_PARAM_SUPPLIERID")
    private Integer supplierId;

    /**
     * 客户编码
     */
    @NotEmpty(message = "EMPTY_PARAM_AGENTCODE")
    private String agentCode;

    /**
     * 供应商编码
     */
    @NotEmpty(message = "EMPTY_PARAM_SUPPLYCODE")
    private String supplierCode;
}
