package com.tiangong.organization.domain.req;

import com.tiangong.dto.common.BasePO;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 支付超时自动取消配置请求类
 */

@Data
public class PaymentOvertimeCancelConfigReq extends BasePO {

    /**
     * 客户Id
     */
    @NotNull(message = "客户Id不能为空")
    private Integer agentId;

    /**
     * 客户编码
     */
    @NotBlank(message = "客户编码不能为空")
    private String agentCode;

    /**
     * 支付超时自动取消开关 0-关闭 1-开启
     */
    @NotNull(message = "开关状态不能为空")
    private Integer paymentOvertimeCancelSwitch;

    /**
     * 支付超时等待时间
     */
    private Double paymentOvertimeCancelTime;
}