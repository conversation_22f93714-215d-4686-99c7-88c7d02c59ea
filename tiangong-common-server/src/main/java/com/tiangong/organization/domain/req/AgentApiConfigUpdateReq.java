package com.tiangong.organization.domain.req;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @create 2023/10/25 15:36
 */
@Data
public class AgentApiConfigUpdateReq {
    /**
     * Id
     */
    @NotNull(message = "EMPTY_PARAM_ID")
    private Integer id;
    /**
     * 客户编码
     */
    @NotEmpty(message = "EMPTY_PARAM_AGENTCODE")
    private String agentCode;
    /**
     * 密钥
     */
    @NotEmpty(message = "EMPTY_PARAM_SECRETKEY")
    private String secretKey;

    /**
     * 合作商编码
     */
    private String partnerCode;

    /**
     * 订单推送地址
     */
    private String orderStatusPushUrl;

    /**
     * 发票通知接口
     */
    private String invoiceNotifyUrl;

    /**
     * 入住明细推送接口
     */
    private String orderCheckDetailNotifyUrl;


    /**
     * 订单退款接口
     */
    private String orderRefundNotifyUrl;

    /**
     * 订单推送接口
     */
    private String orderPushNotifyUrl;

    /**
     * 担保条款通知接口
     */
    private String warrantiesNotifyUrl;

    /**
     * 修改人
     */
    private String updatedBy;
    /**
     * 修改时间
     */
    private Date updatedDt;

    /**
     * 原始协议价输出开关
     */
    private Integer originalProtocolPriceSwitch;

    /**
     * 30天报价输出开关
     */
    private Integer quoteSwitch;

    /**
     * 评分
     */
    private Double rating;

    /**
     * 数据加密开关
     */
    private Integer dataEncryptionSwitch;

    /**
     * 私钥
     */
    private String opsSecretKey;

    /**
     * 客户公钥
     */
    private String customerPublicKey;

    /**
     * 天宫公钥
     */
    private String tiangongPublicKey;
}
