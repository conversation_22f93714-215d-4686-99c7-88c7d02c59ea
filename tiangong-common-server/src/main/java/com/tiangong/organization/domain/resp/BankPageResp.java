package com.tiangong.organization.domain.resp;

import com.tiangong.dto.common.BaseDTO;
import com.tiangong.sensitive.EncryptField;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2023/12/19 13:59
 */
@Data
public class BankPageResp extends BaseDTO {

    /**
     * 银行卡Id
     */
    private Integer bankId;


    /**
     * 账号类型 1个人账户2公司账户
     */
    private String accountType;

    /**
     * 银行卡名称
     */
    private String bankName;

    /**
     * 行号
     */
    @EncryptField
    private String bankCode;
    /**
     * 开户名
     */
    private String accountName;


    /**
     * 账号
     */
    @EncryptField
    private String accountNumber;

    /**
     * 余额
     */
    private BigDecimal balance;

    /**
     * 币种
     */
    private Integer bankCurrency;

    /**
     * 开票主体公司名称
     */
    private String companyName;

    /**
     * 状态
     */
    private Integer active;

    /**
     * 开户行类型：0网商银行 1其他
     */
    private Integer bankType;
}
