package com.tiangong.organization.domain.req;

import com.tiangong.dto.common.BaseDTO;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2023/12/12 11:02
 */
@Data
public class InvoiceReq extends BaseDTO {

    private Integer id;

    /**
     * 类型 1客户 2供应商 3系统设置（公司）
     */
    @NotNull(message = "EMPTY_PARAM_TYPE")
    private Integer type;

    /**
     * 客户或供应商编码
     */
    @NotEmpty(message = "EMPTY_PARAM_CODE")
    private String orgCode;

    /**
     * 公司名称
     */
    @NotEmpty(message = "EMPTY_PARAM_COMPANYNAME")
    private String companyName;

    /**
     * 纳税人识别号
     */
    @NotEmpty(message = "EMPTY_PARAM_TAXPAYERIDENTIFICATIONNUMBER")
    private String taxpayerIdentificationNumber;

    /**
     * 地址
     */
    @NotEmpty(message = "EMPTY_PARAM_ADDRESS")
    private String address;

    /**
     * 手机号
     */
    @NotEmpty(message = "EMPTY_PARAM_TEL")
    private String phone;

    /**
     * 开户行
     */
    private String accountBank;

    /**
     * 开户账号
     */
    private String accountNo;

    /**
     * 普票开票比例
     */
    private BigDecimal generalInvoiceRatio;

    /**
     * 专票开票比例
     */
    private BigDecimal specialInvoiceRatio;
}
