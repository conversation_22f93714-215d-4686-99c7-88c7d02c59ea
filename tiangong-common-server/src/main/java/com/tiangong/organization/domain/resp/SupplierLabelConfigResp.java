package com.tiangong.organization.domain.resp;

import lombok.Data;

/**
 * <AUTHOR>
 * @create 2023/12/18 11:29
 */
@Data
public class SupplierLabelConfigResp {

    private Integer id;

    /**
     * 标签类型
     * @see com.tiangong.enums.LabelTypeEnum
     */
    private Integer labelType;

    /**
     * 供应商id
     */
    private Integer supplierId;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 供应商类型：0自签 2api
     */
    private Integer supplierType;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 供应商资源类型：1国内 2海外
     */
    private Integer domesticOrOverseas;

    /**
     * 客户id
     */
    private Integer agentId;
}
