package com.tiangong.organization.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.tiangong.dto.common.BasePO;
import lombok.Data;

import javax.persistence.*;
import java.math.BigDecimal;

/**
 * 客户信用额度预警日志实体类
 * 
 * <AUTHOR>
 * @date 2024/12/19
 */
@Data
@Table(name = "t_agent_credit_warning_log")
public class AgentCreditWarningLogPO extends BasePO {

    /**
     * Id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    
    /**
     * 客户编码
     */
    private String agentCode;
    
    /**
     * 预警额度
     */
    private BigDecimal warningAmount;
    
    /**
     * 当前剩余额度
     */
    private BigDecimal currentBalance;
    
    /**
     * 发送的预警邮箱
     */
    private String warningEmails;
    
    /**
     * 发送状态：0失败 1成功
     */
    private Integer sendStatus;
    
    /**
     * 错误信息
     */
    private String errorMessage;
}
