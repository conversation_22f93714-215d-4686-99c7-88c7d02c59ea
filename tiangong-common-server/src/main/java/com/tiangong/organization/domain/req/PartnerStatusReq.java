package com.tiangong.organization.domain.req;

import com.tiangong.dto.common.BasePO;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @create 2023/12/4 17:04
 */
@Data
public class PartnerStatusReq extends BasePO {

    /**
     * 启用禁用状态
     */
    @NotNull(message = "EMPTY_PARAM_AVAILABLESTATUS")
    private Integer availableStatus;

    /**
     * 合作编码
     */
    @NotEmpty(message = "EMPTY_PARAM_PARTNERCODE")
    private String partnerCode;

    /**
     * 客户编码
     */
    @NotEmpty(message = "EMPTY_PARAM_AGENTCODE")
    private String agentCode;
}
