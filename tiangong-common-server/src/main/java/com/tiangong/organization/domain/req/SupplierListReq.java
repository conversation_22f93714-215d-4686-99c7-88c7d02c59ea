package com.tiangong.organization.domain.req;

import com.tiangong.dto.common.BaseRequest;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023/10/25 16:56
 */
@Data
public class SupplierListReq extends BaseRequest {

    /**
     * 供应商企业名称
     */
    private String supplierName;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 供应商编码
     */
    private List<String> supplierCodes;

    /**
     * 供应商启用状态
     */
    private Integer availableStatus;

    /**
     * 运营商编码
     */
    private String companyCode;

    /**
     * 我司采购经理Id
     */
    private Integer purchaseManagerId;

    /**
     * 空代表全部 1国内 2海外
     */
    private Integer domesticOrOverseas;

    /**
     * 业务联系人
     */
    private String businessContactName;

    /**
     * 商务经理
     */
    private String businessManager;

    /**
     * 运营经理
     */
    private String operationManager;
}
