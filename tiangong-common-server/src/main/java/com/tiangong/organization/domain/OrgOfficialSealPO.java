package com.tiangong.organization.domain;

import com.tiangong.dto.common.BasePO;
import lombok.Data;

import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Data
@Table(name = "t_org_official_seal")
public class OrgOfficialSealPO extends BasePO {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;


    private String companyCode;


    private String officialSealUrl;


    private String companySignature;


    private Integer active;

}
