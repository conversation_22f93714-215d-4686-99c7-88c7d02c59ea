package com.tiangong.organization.domain.req;

import com.tiangong.dto.common.BasePO;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @create 2023/10/25 14:58
 */
@Data
public class AgentAddReq extends BasePO {

    /**
     * 客户类型
     */
    private Integer agentType;
    /**
     * 客户名称
     */
    @NotEmpty(message = "EMPTY_PARAM_AGENTNAME")
    private String agentName;
    /**
     * 客户手机号
     */
    private String agentTel;
    /**
     * 信用额度
     */
    @NotEmpty(message = "EMPTY_PARAM_CREDITLINE")
    private String creditLine;
    /**
     * 我司销售经理ID
     */
    private Integer saleManagerId;
    /**
     * 总管理员姓名
     */
    @NotEmpty(message = "EMPTY_PARAM_ADMINNAME")
    private String adminName;
    /**
     * 总管理员账号
     */
    @NotEmpty(message = "EMPTY_PARAM_ADMINACCOUNT")
    private String adminAccount;
    /**
     * 总管理员手机号
     */
    @NotEmpty(message = "EMPTY_PARAM_ADMINTEL")
    private String adminTel;

    /**
     * 管理员密码
     */
    @NotEmpty(message = "EMPTY_PARAM_ADMINPASSWORD")
    private String adminPassword;
    /**
     * 结算方式
     */
    @NotNull(message = "EMPTY_PARAM_SETTLEMENTTYPE")
    private Integer settlementType;

    /**
     * 结算币种类型
     */
    @NotNull(message = "EMPTY_PARAM_SETTLEMENTCURRENCY")
    private Integer settlementCurrency;

    /**
     * 保留的小数位
     */
    @NotNull(message = "EMPTY_PARAM_DECIMALPLACES")
    private Integer decimalPlaces;

    /**
     * 渠道编码
     */
    private String channelCode;

    /**
     * companyCode
     */
    private String companyCode;
    /**
     * 域名
     */
    private String orgDomain;

    /**
     * 企业唯一码
     */
    private String orgUniqueCode;

    /**
     * 国家编码
     */
    private String countryCode;

    /**
     * 国家名称
     */
    private String countryName;

    /**
     * 省份名称
     */
    private String provinceName;

    /**
     * 省份编码
     */
    private String provinceCode;

    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 企业地址/客户地址
     */
    private String orgAddress;

    /**
     * 企业成立日期
     */
    private Date establishedDate;

    /**
     * 取整方式 1向上取整 2向下取整 3四舍五入
     */
    @NotNull(message = "EMPTY_PARAM_ROUNDINGTYPE")
    private Integer roundingType;

    /**
     * 我方合作主题
     */
    @NotNull(message = "EMPTY_PARAM_INVOICEID")
    private Integer invoiceId;

    /**
     * 结算模式：0挂账 1预存
     */
    @NotNull(message = "SETTLED_TYPE")
    private Integer settledType;

    /**
     * 描述
     */
    private String remark;

    /**
     * 1国内 2海外
     */
    private Integer domesticOrOverseas;

    /**
     * 结算策略(0按下单 1按结算成本)
     */
    @NotNull(message = "SETTLEMENT_STRATEGY")
    private Integer settlementStrategy;

    /**
     * 推送结算成本(0关 1开)
     */
    private Integer pushSettleCostSwitch;

    /**
     * 推送结算成本地址
     */
    private String pushSettleCostUrl;

    /**
     * 是否开启额度预警：0否 1是
     */
    private Integer creditWarningEnabled;

    /**
     * 预警额度
     */
    private BigDecimal warningAmount;

    /**
     * 额度预警邮箱，多个邮箱用逗号分隔
     */
    private String warningEmails;

    /**
     * 额度账户类型 0：自己 1：其他客户 默认自己
     */
    private Integer lineAccountType;

    /**
     * 额度账户
     */
    private String lineAccount;
}
