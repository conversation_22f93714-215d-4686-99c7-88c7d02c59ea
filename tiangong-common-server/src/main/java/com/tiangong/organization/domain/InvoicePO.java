package com.tiangong.organization.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tiangong.dto.common.BasePO;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2023/12/12 10:42
 */
@Data
@TableName("t_invoice")
public class InvoicePO extends BasePO {

    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 类型 1客户 2供应商 3系统设置（公司）
     */
    private Integer type;

    /**
     * 客户或供应商编码
     */
    private String orgCode;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 纳税人识别号
     */
    private String taxpayerIdentificationNumber;

    /**
     * 地址
     */
    private String address;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 开户行
     */
    private String accountBank;

    /**
     * 开户账号
     */
    private String accountNo;

    /**
     * 普票开票比例
     */
    private BigDecimal generalInvoiceRatio;

    /**
     * 专票开票比例
     */
    private BigDecimal specialInvoiceRatio;
}
