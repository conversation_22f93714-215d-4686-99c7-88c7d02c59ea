package com.tiangong.organization.domain;

import com.tiangong.dto.common.BasePO;
import com.tiangong.sensitive.EncryptField;
import com.tiangong.sensitive.SensitiveClass;
import lombok.Data;

import javax.persistence.*;

/**
 * <AUTHOR>
 * @date 2019/6/19 16:32
 **/
@Data
@SensitiveClass
@Table(name = "t_auth_user_apply")
public class UserApplyPO extends BasePO {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 账号信息Id
     */
    @Column(name = "user_id")
    private Integer userId;

    /**
     * 操作类型 0-新增员工 1-修改员工
     */
    @Column(name = "operation_type")
    private Integer operationType;

    /**
     * 操作内容
     */
    @Column(name = "operation_content")
    private String operationContent;

    /**
     * 审核状态 0-待审核 1-已通过 2-已驳回
     */
    @Column(name = "approval_status")
    private Integer approvalStatus;

    /**
     * 拒绝原因
     */
    @Column(name = "refused_reason")
    private String refusedReason;

    /**
     * 用户名
     */
    @Column(name = "user_name")
    private String userName;
    /**
     * 登录账号
     */
    @Column(name = "user_account")
    private String userAccount;

    /**
     * 手机号
     */
    @EncryptField
    @Column(name = "user_tel")
    private String userTel;

    /**
     * 身份证
     */
    @EncryptField
    @Column(name = "user_id_number")
    private String userIDNumber;

    /**
     * 账号类型 0-长期 1-短期
     */
    @Column(name = "user_account_type")
    private Integer userAccountType;

    /**
     * 账号有效期开始时间
     */
    @Column(name = "account_avl_start_time")
    private String accountAvlStartTime;

    /**
     * 账号有效期结束时间
     */
    @Column(name = "account_avl_end_time")
    private String accountAvlEndTime;

    /**
     * 账号有效期开始时间
     */
    @Column(name = "visit_start_time")
    private String visitStartTime;

    /**
     * 账号有效期结束时间
     */
    @Column(name = "visit_end_time")
    private String visitEndTime;

    /**
     * 可访问IP(白名单)
     */
    @Column(name = "visit_ip")
    private String visitIp;

    /**
     * 登录密码
     */
    @Column(name = "user_pwd")
    private String userPwd;

    /**
     * 企业编码
     */
    @Column(name = "org_code")
    private String orgCode;

    /**
     * 员工角色
     */
    @Column(name = "user_roles")
    private String userRoles;

    /**
     * 是否有教程编辑权限 0-否 1-是
     */
    @Column(name = "is_note_permission")
    private Integer isNotePermission;

}
