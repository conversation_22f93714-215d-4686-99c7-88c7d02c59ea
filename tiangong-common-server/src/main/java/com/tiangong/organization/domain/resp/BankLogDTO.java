package com.tiangong.organization.domain.resp;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2023/12/14 19:52
 */
@Data
public class BankLogDTO {
    private Integer id;

    /**
     * 银行卡号
     */
    private String bankName;

    /**
     * 交易类型1余额调整 2线上转账 3线下转账
     */
    private String changeType;

    /**
     * 机构编码
     */
    private String changeName;

    /**
     * 交易内容
     */
    private String changeContent;

    /**
     * 收入
     */
    private BigDecimal income;

    /**
     * 支出
     */
    private BigDecimal expenditure;

    /**
     * 余额
     */
    private BigDecimal balance;

    /**
     * 交易币种
     */
    private Integer changeCurrency;

    /**
     * 交易时间
     */
    private String changeDate;

    private String createdBy;

    private String createdDt;
}
