package com.tiangong.organization.domain.req;

import com.tiangong.dto.common.BasePO;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2023/10/26 20:06
 */
@Data
public class AddPartnerReq extends BasePO {

    private Integer id;

    /**
     * 客户编码
     */
    @NotEmpty(message = "EMPTY_PARAM_AGENTCODE")
    private String agentCode;
    /**
     * 订单通知接口
     */
    private String orderStatusNotifyUrl;
    /**
     * 发票通知接口
     */
    private String invoiceNotifyUrl;
    /**
     * 订单退款接口
     */
    private String orderRefundNotifyUrl;

    /**
     * 入住明细推送接口
     */
    private String orderCheckDetailNotifyUrl;

    /**
     * 订单推送接口
     */
    private String orderPushNotifyUrl;

    /**
     * 担保条款通知接口
     */
    private String warrantiesNotifyUrl;

    /**
     * 订单推送通知推送接口
     */
    private String orderCheckoutNotifyUrl;

    /**
     * 1国内 2海外
     */
    @NotNull(message = "EMPTY_PARAM_DOMESTICOROVERSEAS")
    private Integer domesticOrOverseas;

    /**
     * 运营商编码
     */
    private String companyCode;

    /**
     * 合作商编码
     */
    private String partnerCode;

    /**
     * 原始协议价输出开关
     */
    private Integer originalProtocolPriceSwitch;

    /**
     * 30天报价输出开关
     */
    private Integer quoteSwitch;

    /**
     * 评分
     */
    private Double rating;

    /**
     * 数据加密开关
     */
    private Integer dataEncryptionSwitch=0;

    /**
     * 私钥
     */
    private String opsSecretKey;

    /**
     * 客户公钥
     */
    private String customerPublicKey;

    /**
     * 天宫公钥
     */
    private String tiangongPublicKey;

    /**
     * 是否开启额度预警：0否 1是
     */
    private Integer creditWarningEnabled;

    /**
     * 预警额度
     */
    private BigDecimal warningAmount;

    /**
     * 额度预警邮箱，多个邮箱用逗号分隔
     */
    private String warningEmails;
}
