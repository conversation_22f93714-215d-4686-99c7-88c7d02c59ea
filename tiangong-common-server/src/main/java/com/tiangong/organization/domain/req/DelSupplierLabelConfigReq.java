package com.tiangong.organization.domain.req;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @create 2023/12/18 11:28
 */
@Data
public class DelSupplierLabelConfigReq {

    @NotNull(message = "EMPTY_PARAM_ID")
    private Integer id;

    /**
     * 客户编码
     */
    @NotEmpty(message = "EMPTY_PARAM_AGENTCODE")
    private String agentCode;

    /**
     * 供应商编码
     */
    @NotEmpty(message = "EMPTY_PARAM_SUPPLYCODE")
    private String supplierCode;

    /**
     * 标签类型
     * @see com.tiangong.enums.LabelTypeEnum
     */
    @NotNull(message = "EMPTY_PARAM_LABELTYPE")
    private Integer labelType;
}
