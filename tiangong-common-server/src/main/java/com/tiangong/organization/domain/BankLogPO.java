package com.tiangong.organization.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2023/12/14 19:33
 */
@Data
@TableName("t_org_bank_log")
public class BankLogPO{

    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 银行卡号
     */
    private Integer bankId;

    /**
     * 交易类型1余额调整 2线上转账 3线下转账
     */
    private Integer changeType;

    /**
     * 机构编码
     */
    private String orgCode;

    /**
     * 交易内容
     */
    private String changeContent;

    /**
     * 收入
     */
    private BigDecimal income;

    /**
     * 支出
     */
    private BigDecimal expenditure;

    /**
     * 余额
     */
    private BigDecimal balance;

    /**
     * 交易币种
     */
    private Integer changeCurrency;

    /**
     * 交易时间
     */
    private String changeDate;

    private String createdBy;

    private String createdDt;
}
