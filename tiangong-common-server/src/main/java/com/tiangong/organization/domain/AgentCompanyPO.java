package com.tiangong.organization.domain;

import com.tiangong.dto.common.BasePO;
import com.tiangong.sensitive.SensitiveClass;
import lombok.Data;

import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2019/6/26 21:02
 **/
@Data
@SensitiveClass
@Table(name = "t_org_company_agent")
public class AgentCompanyPO extends BasePO {
    /**
     * 客户-运营商Id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer aCompanyId;
    /**
     * 机构Id
     */
    private  Integer orgId;
    /**
     * 总管理员名称
     */
    private  String userName;
    /**
     * 总管理员账号
     */
    private  String userNumber;
    /**
     * 总管理员手机号
     */
    //@EncryptField
    private  String userTel;
    /**
     * 结算方式
     */
    private  Integer settlementType;

    /**
     * 结算币种类型
     */
    private Integer settlementCurrency;
    /**
     * 信用额度
     */
    private  String creditLine;

    /**
     * 剩余额度
     */
    private BigDecimal balance;

    /**
     * 销售经理Id
     */
    private Integer saleManagerId;

    /**
     * 运营商编码
     */
    private String companyCode;

    /**
     * 启用状态
     */
    private  Integer  availableStatus;

    /**
     * 保留的小数位
     */
    private Integer decimalPlaces;

    /**
     * 取整方式 1向上取整 2向下取整 3四舍五入
     */
    private Integer roundingType;

    /**
     * 我方合作主题
     */
    private Integer invoiceId;

    /**
     * 结算模式：0挂账 1预存
     */
    private Integer settledType;

    /**
     * 描述
     */
    private String remark;

    /**
     * 1国内 2海外
     */
    private Integer domesticOrOverseas;

    /**
     * 结算策略(0按下单 1按结算成本)
     */
    private Integer settlementStrategy;

    /**
     * 推送结算成本(0关 1开)
     */
    private Integer pushSettleCostSwitch;

    /**
     * 推送结算成本地址
     */
    private String pushSettleCostUrl;

    /**
     * 开关状态 0-关闭 1-开启
     */
    private Integer paymentOvertimeCancelSwitch;

    /**
     * 支付超时等待时间
     */
    private Double paymentOvertimeCancelTime;

    /**
     * 是否开启额度预警：0否 1是
     */
    private Integer creditWarningEnabled;

    /**
     * 预警额度
     */
    private BigDecimal warningAmount;

    /**
     * 额度预警邮箱，多个邮箱用逗号分隔
     */
    private String warningEmails;

    /**
     * 额度账户类型 0：自己 1：其他客户 默认自己
     */
    private Integer lineAccountType;

    /**
     * 额度账户
     */
    private String lineAccount;
}
