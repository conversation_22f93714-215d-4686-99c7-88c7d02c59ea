package com.tiangong.organization.domain.req;

import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * @program: glink_tiangong
 * @ClassName AgentConfirmationLetterConfigQueryReq
 * @description:
 * @author: 湫
 * @create: 2025/04/15/ 15:32
 * @Version 1.0
 **/

@Data
public class AgentConfirmationLetterConfigQueryReq {

    @NotEmpty(message = "EMPTY_PARAM_AGENTCODE")
    private String agentCode;
}
