package com.tiangong.organization.domain.req;

import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * @Description:
 * @ClassName: PreferredProductsStatusReq
 * @Author: xubohao
 * @CreateDate: 2025/4/12 15:03
 * @Version: 1.0
 */

@Data
public class PreferredProductsStatusReq {

    @NotEmpty(message = "EMPTY_PARAM_AGENTCODE")
    private String agentCode;

    /**
     * 客户优选产品状态
     * 0关闭
     * 1开启
     */
    private Integer preferredProductsStatus;

}
