package com.tiangong.organization.domain.req;

import com.tiangong.cloud.common.domain.BasePage;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @create 2023/12/19 14:11
 */
@Data
public class BankListPageReq extends BasePage {


    /**
     * 机构编码
     */
    @NotEmpty(message = "EMPTY_PARAM_ORGCODE")
    private String orgCode;

    /**
     * 是否可用
     */
    private Integer active;

    /**
     * 账户类型
     */
    private Integer accountType;

    /**
     * 开户名
     */
    private String accountName;
}
