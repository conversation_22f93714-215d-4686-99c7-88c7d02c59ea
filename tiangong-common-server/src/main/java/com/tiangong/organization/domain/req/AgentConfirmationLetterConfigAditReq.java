package com.tiangong.organization.domain.req;

import com.tiangong.dto.common.BasePO;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * @program: glink_tiangong
 * @ClassName AgentConfirmationLetterConfigAditReq
 * @description:
 * @author: 湫
 * @create: 2025/04/15/ 14:41
 * @Version 1.0
 **/

@Data
public class AgentConfirmationLetterConfigAditReq extends BasePO {

    private Long id;

    /**
     * 自动发送
     * 0 不自动 1 自动
     */
    @NotNull(message = "EMPTY_PARAM_AUOTOFLAG")
    private Integer auotoFlag;

    /**
     * 客户编码
     */
    @NotEmpty(message = "EMPTY_PARAM_AGENTCODE")
    private String agentCode;

    /**
     * 密送邮箱
     */
    private String blindCarbonCopy;

    /**
     * 抄送邮箱
     */
    private String carbonCopy;

    /**
     * 价格显示
     * 0 展示 1不展示
     */
    @NotNull(message = "EMPTY_PARAM_PRICESHOW")
    private Integer priceShow;

    /**
     * 确认函语言
     *  zh-CN 中文
     *  en-US 英文
     *  zh-CN_en-US 中英双语
     */
    @NotEmpty(message = "EMPTY_PARAM_LANGUAGE")
    private String language;

    /**
     * 附件携带
     * 0 不携带 1携带
     */
    @NotNull(message = "EMPTY_PARAM_ANNEXCARRY")
    private Integer annexCarry;

    /**
     * 客户专属客服电话
     */
    private String agentPhone;

    /**
     * logo 地址
     */
    private String logoUrl;

}
