package com.tiangong.organization.domain.req;

import com.tiangong.dto.common.BasePO;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2023/10/19 15:58
 */
@Data
public class SupplierAdjustmentUpdateReq extends BasePO {

    /**
     * 供应商Id
     */
    @NotNull(message = "EMPTY_PARAM_SUPPLIERID")
    private Integer supplierId;

    /**
     * 供应商编码
     */
    @NotEmpty(message = "EMPTY_PARAM_SUPPLYCODE")
    private String supplierCode;

    /**
     * 增量类型0加数值 1减数值 2加百分比 3减百分比 4等于
     */
    @NotNull(message = "EMPTY_PARAM_ADJUSTMENTTYPE")
    private Integer adjustmentType;

    /**
     * 调整金额
     */
    @NotNull(message = "EMPTY_PARAM_MODIFIEDAMT")
    private BigDecimal modifiedAmt;

    /**
     * 最低加幅金额
     */
    private BigDecimal lowestIncreaseAmt;
}
