package com.tiangong.organization.domain;

import com.tiangong.dto.common.BasePO;
import com.tiangong.enums.SupplyOneLevelChannelTypeEnum;
import com.tiangong.enums.SupplyTwoLevelChannelTypeEnum;
import com.tiangong.sensitive.EncryptField;
import com.tiangong.sensitive.SensitiveClass;
import lombok.Data;

import javax.persistence.*;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2019/6/19 19:46
 **/
@Data
@SensitiveClass
@Table(name = "t_org_company_supplier")
public class SupplierCompanyPO extends BasePO {
    /**
     * 供应商-运营商Id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer sCompanyId;

    /**
     * 机构Id,在这里特指运营商
     */
    @Column(name = "org_id")
    private Integer orgId;

    /**
     * 总管理员姓名
     */
    @Column(name = "user_name")
    private String userName;
    /**
     * 总管理员账号
     */
    @Column(name = "user_number")
    private String userNumber;
    /**
     * 总管理员手机号
     */
    @Column(name = "user_tel")
    @EncryptField
    private String userTel;
    /**
     * 结算方式
     */
    @Column(name = "settlement_type")
    private Integer settlementType;
//
//    /**
//     * 结算币种类型
//     */
//    @Column(name = "settlement_currency")
//    private Integer settlementCurrency;

    /**
     * 我司采购经理Id
     */
    @Column(name = "purchase_manager_id")
    private Integer purchaseManagerId;


    /**
     * 启用状态
     */
    @Column(name = "available_status")
    private Integer availableStatus;

    /**
     * 运营商Id
     */
    @Column(name = "company_code")
    private String operatorCode;

    /**
     * 增量类型0加数值 1减数值 2加百分比 3减百分比 4等于
     */
    @Column(name = "adjustment_type")
    private Integer adjustmentType;

    /**
     * 调整金额
     */
    @Column(name = "modified_amt")
    private BigDecimal modifiedAmt;

    /**
     * 最低加幅金额
     */
    @Column(name = "lowest_increase_amt")
    private BigDecimal lowestIncreaseAmt;

    /**
     * 发票类型：1普票 2专票
     */
    @Column(name = "invoice_type")
    private Integer invoiceType;

    /**
     * 发票模式: 1商家开票 2酒店前台开票
     */
    @Column(name = "invoice_model")
    private Integer invoiceModel;

    /**
     * 是否不落地 1不落地 2落地
     */
    @Column(name = "is_cached")
    private Integer isCached;

    /**
     * 1国内 2海外
     */
    @Column(name = "domestic_or_overseas")
    private Integer domesticOrOverseas;

    /**
     * 供应商类型
     * @see com.tiangong.enums.SupplierFormEnum
     */
    @Column(name = "supplier_form")
    private Integer supplierForm;

    /**
     * 快速处理标签开关
     */
    @Column(name = "quick_processing_switch")
    private Integer quickProcessingSwitch;

    /**
     * 集团id
     */
    @Column(name = "group_id")
    private Integer groupId;

    /**
     * 集团名称
     */
    @Column(name = "group_name")
    private String groupName;

    /**
     * 酒店id
     */
    @Column(name = "hotel_id")
    private Long hotelId;

    /**
     * 酒店名称
     */
    @Column(name = "hotel_name")
    private String hotelName;

    /**
     * 是否同步入住明细：0否 1是
     */
    @Column(name = "is_sync_check_detail")
    private Integer isSyncCheckDetail;


    /**
     * 我方合作主题
     */
    @Column(name = "invoice_id")
    private Integer invoiceId;

    /**
     * 是否协议托管：0否 1是
     */
    @Column(name = "enable_protocol")
    private Integer enableProtocol;

    /**
     * 是否待结算
     */
    @Column(name = "enable_settled")
    private Integer enableSettled;

    /**
     * 描述
     */
    @Column(name = "remark")
    private String remark;

    /**
     * 一级渠道类型
     * @see SupplyOneLevelChannelTypeEnum
     */
    @Column(name = "one_level_channel_type")
    private Integer oneLevelChannelType;

    /**
     * 二级渠道类型
     * @see SupplyTwoLevelChannelTypeEnum
     */
    @Column(name = "two_level_channel_type")
    private Integer twoLevelChannelType;

    /**
     * 三级渠道类型
     */
    @Column(name = "three_level_channel_type")
    private String threeLevelChannelType;
}
