package com.tiangong.organization.domain.req;

import com.tiangong.dto.common.BaseRequest;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 分销api配置表
 * 请求参数
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-18 12:44:15
 */
@Data
public class OrgAgentApiConfigReq extends BaseRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * Id
     */
    @NotNull(message = "EMPTY_PARAM_ID")
    private Integer id;
    /**
     * 客户编码
     */
    @NotEmpty(message = "EMPTY_PARAM_AGENTCODE")
    private String agentCode;
    /**
     * 密钥
     */
    @NotEmpty(message = "EMPTY_PARAM_SECRETKEY")
    private String secretKey;
    /**
     * 订单通知接口
     */
    private String orderStatusNotifyUrl;
    /**
     * 发票通知接口
     */
    private String invoiceNotifyUrl;
    /**
     * 订单退款接口
     */
    private String orderRefundNotifyUrl;
    /**
     * 入住明细推送接口
     */
    private String orderCheckDetailNotifyUrl;


    /**
     * 合作状态（1-合作中 0-暂停合作）
     */
    private Integer cooperatingState;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private Date createdDt;
    /**
     * 修改人
     */
    private String updatedBy;
    /**
     * 修改时间
     */
    private Date updatedDt;
    /**
     * 删除状态
     */
    private Integer deleted;
}