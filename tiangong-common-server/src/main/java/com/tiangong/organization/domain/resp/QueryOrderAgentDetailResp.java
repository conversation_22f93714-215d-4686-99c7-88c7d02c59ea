package com.tiangong.organization.domain.resp;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class QueryOrderAgentDetailResp {

    /**
     * 客户编码
     */
    private String agentCode;

    /**
     * 客户名称
     */
    private String agentName;

    /**
     * 业务联系人姓名
     */
    private String businessContactName;

    /**
     * 业务联系人电话
     */
    private String businessContactTel;

    /**
     * 客户经理
     */
    private String agentManagerName;

    /**
     * 运营经理
     */
    private String operationManager;

    /**
     * 总额度
     */
    private BigDecimal creditLine;

    /**
     * 剩余额度
     */
    private BigDecimal balance;

//    /**
//     * 剩余额度
//     */
//    private BigDecimal remainingBalance;

    /**
     * 备注
     */
    private String remark;

    /**
     * 额度账户类型 0：自己 1：其他客户 默认自己
     */
    private Integer lineAccountType;

    /**
     * 额度账户
     */
    private String lineAccount;

    /**
     * 额度账户名称
     */
    private String lineAccountName;
}
