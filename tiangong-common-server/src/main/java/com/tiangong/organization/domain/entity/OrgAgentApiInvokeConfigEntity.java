package com.tiangong.organization.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.util.Date;

/**
 * 分销api接口频次表
 * 表的实体类，参数和表字段一一对应
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-18 12:44:15
 */
@Data
@TableName("t_org_agent_api_invoke_config")
public class OrgAgentApiInvokeConfigEntity extends Model<OrgAgentApiInvokeConfigEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * Id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    /**
     * 客户编码
     */
    private String partnerCode;
    /**
     * 接口名称
     */
    private String methodName;
    /**
     * 接口编码
     */
    private String methodCode;
    /**
     * 调用频次
     */
    private Integer invokeTimes;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private Date createdDt;
    /**
     * 修改人
     */
    private String updatedBy;
    /**
     * 修改时间
     */
    private Date updatedDt;
    /**
     * 删除状态
     */
    private Integer deleted;
    /**
     * 1国内 2海外
     */
    private Integer domesticOrOverseas;

}
