package com.tiangong.organization.server;

import com.tiangong.annotations.SlsLog;
import com.tiangong.cloud.common.anno.AnonymousAccess;
import com.tiangong.cloud.commonbean.common.BaseController;
import com.tiangong.common.Response;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.organization.domain.InvoicePO;
import com.tiangong.organization.domain.req.DelInvoiceReq;
import com.tiangong.organization.domain.req.InvoiceReq;
import com.tiangong.organization.domain.req.QueryInvoiceReq;
import com.tiangong.organization.remote.dto.InvoiceDTO;
import com.tiangong.organization.service.InvoiceService;
import com.tiangong.util.DateUtilX;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023/12/12 11:01
 */
@RestController
@RequestMapping("/common/invoice")
public class InvoiceServer extends BaseController {

    @Autowired
    private InvoiceService invoiceService;

    /**
     * 新增或修改发票主体
     */
    @PostMapping("saveOrUpdateInvoice")
    public Response<Object> saveOrUpdateInvoice(@Validated @RequestBody InvoiceReq req) {
        if (req.getId() == null) {
            req.setCreatedBy(getUserName());
            req.setCreatedDt(DateUtilX.getDateTimeStr());
        } else {
            req.setUpdatedBy(getUserName());
            req.setUpdatedDt(DateUtilX.getDateTimeStr());
        }
        invoiceService.saveOrUpdateInvoice(req);
        return Response.success();
    }

    /**
     * 查询发票主体列表--分页
     */
    @PostMapping("queryInvoicePage")
    public Response<PaginationSupportDTO<InvoicePO>> queryInvoicePage(@Validated @RequestBody QueryInvoiceReq req) {
        return Response.success(invoiceService.invoiceListPage(req));
    }

    /**
     * 查询发票主体列表
     */
    @PostMapping("queryInvoiceList")
    public Response<List<InvoicePO>> queryInvoiceList(@Validated @RequestBody QueryInvoiceReq req) {
        return Response.success(invoiceService.invoiceList(req));
    }

    /**
     * 删除发票主体
     */
    @PostMapping("delInvoice")
    public Response<Object> delInvoice(@Validated @RequestBody DelInvoiceReq req) {
        invoiceService.delInvoice(req);
        return Response.success();
    }

    /**
     * 查询公司发票主体列表（不分页）
     */
    @PostMapping("queryCompanyInvoiceList")
    public Response<List<InvoicePO>> queryCompanyInvoiceList(@RequestBody QueryInvoiceReq req) {
        return Response.success(invoiceService.companyInvoiceList(req));
    }

    /**
     * 查询发票
     *
     * @param invoiceId 开票主体id
     * @return 返回开票信息
     */
    @PostMapping("queryInvoiceInfoById")
    @AnonymousAccess
    public Response<InvoiceDTO> queryInvoiceInfoById(@RequestBody Integer invoiceId) {
        return Response.success(invoiceService.queryInvoice(invoiceId));
    }
}
