package com.tiangong.organization.server;

import com.tiangong.annotations.SlsLog;
import com.tiangong.cloud.common.anno.AnonymousAccess;
import com.tiangong.common.Response;
import com.tiangong.dto.common.SysConfigDTO;
import com.tiangong.dto.common.SysConfigReq;
import com.tiangong.enums.ResultCodeEnum;
import com.tiangong.organization.domain.SysConfigPO;
import com.tiangong.organization.service.SysConfigService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/common/sysConfig")
public class SysConfigServer {

    @Autowired
    private SysConfigService sysConfigService;

    /**
     * 新增或修改系统配置
     */
    @PostMapping("addOrUpdate")
    public Response<Object> addOrUpdate(@RequestBody SysConfigPO sysConfigPO){
        sysConfigService.addOrUpdate(sysConfigPO);
        return Response.success();
    }

    /**
     * 查询系统配置列表
     */
    @PostMapping("list")
    public Response<List<SysConfigPO>> list(){
        return Response.success(sysConfigService.queryConfigList());
    }

    /**
     * 查询系统配置
     */
    @PostMapping("querySysConfigByKey")
    @AnonymousAccess
    public Response<SysConfigDTO> querySysConfigByKey(@RequestBody SysConfigReq req){
        SysConfigDTO dto = new SysConfigDTO();
        SysConfigPO sysConfigPO = sysConfigService.queryConfig(req.getStrKey());
        BeanUtils.copyProperties(sysConfigPO, dto);
        return Response.success(dto);
    }
}
