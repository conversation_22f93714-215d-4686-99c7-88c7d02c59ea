package com.tiangong.organization.server;

import com.tiangong.annotations.SlsLog;
import com.tiangong.cloud.common.anno.AnonymousAccess;
import com.tiangong.cloud.commonbean.common.BaseController;
import com.tiangong.common.Response;
import com.tiangong.dto.common.AddSupplierForeignReq;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.enums.LanguageTypeEnum;
import com.tiangong.organization.domain.entity.OrgSupplierIncreaseLog;
import com.tiangong.organization.domain.req.*;
import com.tiangong.organization.domain.resp.QueryOrderSupplierDetailResp;
import com.tiangong.organization.domain.resp.SupplierLabelConfigResp;
import com.tiangong.organization.remote.dto.*;
import com.tiangong.organization.service.SupplierService;
import com.tiangong.util.DateUtilX;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.apache.tomcat.util.http.fileupload.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/6/19 22:13
 **/
@RestController
@Slf4j
@RequestMapping("/common/supplier")
public class SupplierServer extends BaseController {
    @Autowired
    private SupplierService supplierService;

    /**
     * 新增供应商
     */
    @PostMapping("/addSupplier")
    @PreAuthorize("@syyo.check('supplier')")
    public Response<OrgDTO> addSupplier(@Valid @RequestBody AddSupplierReq request) {
        if (StrUtilX.isEmpty(request.getSupplierName())) {
            request.setSupplierName(request.getHotelName());
        }
        if (StrUtilX.isEmpty(request.getOrgDomain())) {
            request.setOrgDomain("tiangong");
        }
        if (request.getIsSupportEarlyMorningRoomOrder() == null) {
            request.setIsSupportEarlyMorningRoomOrder(0);
        }
        request.setLoadAgentSupplyAvailable(1);// 加载客户可见供应商
        request.setSupplierCode(super.getCompanyCode());
        request.setCreatedBy(super.getUserName());
        return Response.success(supplierService.addSupplier(request));
    }

    /**
     * 新增供应商
     */
    @AnonymousAccess
    @PostMapping("/addSupplierForeign")
    @SlsLog(level = "info", name = "查询", message = "addSupplierForeign", topic = "/common/supplier/addSupplierForeign", source = "tiangong-common-server")
    public Response<Object> addSupplierForeign(@RequestBody List<AddSupplierForeignReq> request) {
        return Response.success(supplierService.addSupplierForeign(request));
    }

    /**
     * 修改供应商启用状态
     */
    @PostMapping("/modifySupplierStatus")
    @PreAuthorize("@syyo.check('supplier')")
    public Response<Object> modifySupplierStatus(@RequestBody SupplierStatusUpdateReq request) {
        request.setCompanyCode(getCompanyCode());
        request.setUpdatedBy(getUserName());
        supplierService.modifySupplierStatus(request);
        return Response.success();
    }

    /**
     * 修改供应商信息
     */
    @PostMapping("/modifySupplier")
    @PreAuthorize("@syyo.check('supplier')")
    public Response<Object> modifySupplier(@RequestBody SupplierUpdateReq request) {
        if (StrUtilX.isNotEmpty(request.getAdminTel()) && request.getAdminTel().contains("*")) {
            request.setAdminTel(null);
        }
        if (request.getIsSupportEarlyMorningRoomOrder() == null) {
            request.setIsSupportEarlyMorningRoomOrder(0);
        }
        request.setUpdatedBy(getUserName());
        request.setCreatedBy(getUserName());
        request.setCompanyCode(getCompanyCode());
        supplierService.modifySupplier(request);
        return Response.success();
    }

    /**
     * 根据供应商Code,查询供应商详情
     */
    @PostMapping("/querySupplierDetail")
    @AnonymousAccess
    public Response<SupplierSelectDTO> querySupplierDetail(@RequestBody SupplierDetailByCode request) {
        if (StrUtilX.isEmpty(request.getCompanyCode())) {
            request.setCompanyCode(getCompanyCode());
        }
        return Response.success(supplierService.querySupplierDetail(request));
    }

    /**
     * 查询供应商列表（分页）
     */
    @PostMapping("/querySupplierList")
    @PreAuthorize("@syyo.check('supplier')")
    public Response<PaginationSupportDTO<QuerySupplierListDTO>> querySupplierList(@RequestBody SupplierListReq request) {
        request.setCompanyCode(getCompanyCode());
        if (request.getAvailableStatus() != null) {
            if (request.getAvailableStatus() == -1) {
                request.setAvailableStatus(null);
            }
        }
        return Response.success(supplierService.querySupplierList(request));
    }

    /**
     * 查询供应商列表（不分页）
     */
    @PostMapping("/querySupplierInfoList")
    @PreAuthorize("@syyo.check('supplier')")
    public Response<List<QuerySupplierListDTO>> querySupplierInfoList(@RequestBody SupplierListReq request) {
        request.setCompanyCode(getCompanyCode());
        if (request.getAvailableStatus() != null) {
            if (request.getAvailableStatus() == -1) {
                request.setAvailableStatus(null);
            }
        }
        return Response.success(supplierService.querySupplierInfoList(request));
    }

    /**
     * 查询企业落款
     */
    @PostMapping(value = "/queryCompanySignature", produces = {"application/json;charset=UTF-8"})
    @ResponseBody
    @PreAuthorize("@syyo.check('supplier')")
    public Response<PaginationSupportDTO<SendEntityDTO>> queryCompanySignature(@RequestBody QueryCompanySignatureDTO queryCompanySignatureDTO) {
        return Response.success(supplierService.queryCompanySignature(queryCompanySignatureDTO));
    }

    /**
     * 修改供应商加幅信息
     */
    @PostMapping("/modifySupplierAdjustment")
    @PreAuthorize("@syyo.check('supplier')")
    public Response<Object> modifySupplierAdjustment(@Validated @RequestBody SupplierAdjustmentUpdateReq request) {
        request.setUpdatedBy(getUserName());
        request.setUpdatedDt(DateUtilX.getDateStr());
        supplierService.modifySupplierAdjustment(request);
        return Response.success();
    }

    /**
     * 修改供应商发票信息
     */
    @PostMapping("/modifySupplierInvoice")
    @PreAuthorize("@syyo.check('supplier')")
    public Response<Object> modifySupplierInvoice(@Validated @RequestBody SupplierInvoiceUpdateReq request) {
        request.setUpdatedBy(getUserName());
        request.setUpdatedDt(DateUtilX.getDateStr());
        supplierService.modifySupplierInvoice(request);
        return Response.success();
    }

    /**
     * 根据供应商编码查询供应商加幅修改记录
     */
    @PostMapping("/supplierIncreaseLogList")
    @PreAuthorize("@syyo.check('supplier')")
    public Response<PaginationSupportDTO<OrgSupplierIncreaseLog>> supplierIncreaseLogList(@RequestBody IncreaseLogReq req) {
        return Response.success(supplierService.supplierIncreaseLogList(req));
    }

    /**
     * 验证密码获取手机号
     */
    @PostMapping(value = "/getPhoneAsPassword", produces = {"application/json;charset=UTF-8"})
    @PreAuthorize("@syyo.check('contact')")
    public Response<Object> getPhoneAsPassword(@RequestBody ContactAsPhoneDTO contact) {
        return Response.success(supplierService.getPhoneAsPassword(contact));
    }

    /**
     * 修改供应商手机号
     */
    @PostMapping(value = "/modifySupplierPhone", produces = {"application/json;charset=UTF-8"})
    @PreAuthorize("@syyo.check('contact')")
    public Response<Object> modifySupplierPassword(@RequestBody ContactAsPhoneDTO contact) {
        supplierService.modifySupplierPhone(contact);
        return Response.success();
    }

    /**
     * 新增/修改供应商配置
     */
    @PostMapping("addOrUpdateSupplierLabelConfig")
    public Response<Object> addOrUpdateSupplierLabelConfig(@Validated @RequestBody SupplierLabelConfigReq req) {
        if (req.getId() == null) {
            req.setCreatedBy(getUserName());
            req.setCreatedDt(DateUtilX.getDateTimeStr());
        } else {
            req.setUpdatedBy(getUserName());
            req.setUpdatedDt(DateUtilX.getDateTimeStr());
        }
        supplierService.addOrUpdateSupplierLabelConfig(req);
        return Response.success();
    }

    /**
     * 移除供应商配置
     */
    @PostMapping("delSupplierLabelConfig")
    public Response<Object> delSupplierLabelConfig(@Validated @RequestBody DelSupplierLabelConfigReq req) {
        supplierService.delSupplierLabelConfig(req);
        return Response.success();
    }

    /**
     * 查询供应商配置
     */
    @PostMapping("querySupplierLabelConfigPage")
    public Response<PaginationSupportDTO<SupplierLabelConfigResp>> querySupplierLabelConfigPage(@Validated @RequestBody QuerySupplierLabelConfigReq req) {
        return Response.success(supplierService.querySupplierLabelConfigPage(req));
    }

    /**
     * 查询订单客户信息
     */
    @PostMapping(value = "/queryOrderSupplierDetail", produces = {"application/json;charset=UTF-8"})
    @PreAuthorize("@syyo.check('supplier')")
    public Response<QueryOrderSupplierDetailResp> queryOrderSupplierDetail(@Validated @RequestBody QueryOrderSupplierDetailReq supplierDetailReq) {
        return Response.success(supplierService.queryOrderSupplierDetail(supplierDetailReq));
    }

    /**
     * 查询供应商名称列表
     */
    @PostMapping("/querySupplierNameList")
    @PreAuthorize("@syyo.check('supplier')")
    public Response<List<QuerySupplierListDTO>> querySupplierNameList(@RequestBody SupplierListReq request) {
        return Response.success(supplierService.querySupplierNameList(request));
    }

    /**
     * 下载供应商标签配置导入模板
     */
    @PostMapping("/downSupplierLabelConfigTemplateFile")
    public File downSupplierLabelTemplateFile(HttpServletResponse response) {
        InputStream in = null;
        try {
            String filename;
            if (LanguageTypeEnum.zh_CN.getValue().equals(getLanguage())) {
                filename = "供应商标签配置导入模板";
            } else {
                filename = "supplierLabelConfigTemplate";
            }
            in = this.getClass().getClassLoader().getResourceAsStream("template/supplierLabelConfigTemplate.xlsx");
            response.setHeader("Content-disposition", "attachment;filename=" + filename + ".xlsx");
            IOUtils.copy(in, response.getOutputStream());
            response.flushBuffer();
        } catch (Exception e) {
            log.error("下载供应商标签配置导入模板异常", e);
        } finally {
            if (in != null) {
                try {
                    in.close();
                } catch (IOException e) {
                    log.error("下载供应商标签配置导入模板异常", e);
                }
            }
        }
        return null;
    }

    /**
     * 导入供应商标签配置
     */
    @PostMapping("/importSupplierLabelConfig")
    @PreAuthorize("@syyo.check('finance')")
    public Response<Object> importSupplierLabelConfig(MultipartFile file) {
        return supplierService.importSupplierLabelConfig(file, super.getUserName());
    }
}
