package com.tiangong.organization.server;

import com.tiangong.cloud.common.anno.AnonymousAccess;
import com.tiangong.cloud.commonbean.common.BaseController;
import com.tiangong.common.Response;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.enums.ErrorCodeEnum;
import com.tiangong.exception.SysException;
import com.tiangong.finance.OrgDTO;
import com.tiangong.initializer.CommonInitializer;
import com.tiangong.keys.RedisKey;
import com.tiangong.organization.remote.dto.*;
import com.tiangong.organization.service.CompanyService;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/6/28 15:25
 **/
@RestController
@Slf4j
@RequestMapping("/common/company")
public class CompanyServer extends BaseController {

    @Autowired
    private CompanyService companyService;

    /**
     * 新增运营商
     */
    @PostMapping("/addCompany")
    @PreAuthorize("@syyo.check('company')")
    public Response<Object> addCompany(@RequestBody CompanyAddDTO request) {
        request.setCreatedBy(getUserName());
        companyService.addCompany(request);
        return Response.success();
    }

    /**
     * 修改运营商信息
     */
    @PostMapping("/modifyCompany")
    @PreAuthorize("@syyo.check('company')")
    public Response<Object> modifyCompany(@RequestBody CompanyAddDTO request) {
        request.setUpdatedBy(getUserName());
        if (request.getAdminTel().contains("*")) {
            request.setAdminTel(null);
        }
        companyService.modifyCompany(request);
        return Response.success();
    }

    /**
     * 修改客服电话
     */
    @PostMapping("/modifyCompanyCustomerTel")
    @PreAuthorize("@syyo.check('company')")
    public Response<Object> modifyCompanyCustomerTel(@RequestBody CompanyAddDTO request) {
        companyService.modifyCompanyCustomerTel(request);
        return Response.success();
    }

    /**
     * 修改hr对接信息
     */
    @PostMapping("/modifyCompanyHrConfig")
    @PreAuthorize("@syyo.check('company')")
    public Response<Object> modifyCompanyHrConfig(@RequestBody CompanyAddDTO request) {
        companyService.modifyCompanyHrConfig(request);
        return Response.success();
    }

    /**
     * 修改运营商启用状态
     */
    @PostMapping("/modifyCompanyStatus")
    @PreAuthorize("@syyo.check('company')")
    public Response<Object> modifyCompanyStatus(@RequestBody CompanyAddDTO request) {
        request.setUpdatedBy(getUserName());
        companyService.modifyCompanyStatus(request);
        return Response.success();
    }

    /**
     * 查询运营商详情
     */
    @PostMapping("/queryCompanyDetail")
    @AnonymousAccess
    public Response<CompanySelectDTO> queryCompanyDetail(@RequestBody CompanyAddDTO request) {
        if (StrUtilX.isEmpty(request.getCompanyCode())) {
            request.setCompanyCode(getCompanyCode());
        }
        return Response.success(companyService.queryCompanyDetail(request));
    }

    /**
     * 运营商列表
     */
    @PostMapping("/queryCompanyList")
    @PreAuthorize("@syyo.check('company')")
    public Response<PaginationSupportDTO<QueryCompanyListDTO>> queryCompanyList(@RequestBody CompanyListRequest request) {
        return Response.success(companyService.queryCompanyList(request));
    }

    /**
     * 判断域名是否为空
     */
    @PostMapping("/isCompanyExit")
    @PreAuthorize("@syyo.check('company')")
    public Response<isExitDTO> isExit(@RequestBody CompanyAddDTO request) {
        return Response.success(companyService.isCompanyExit(request));
    }

    /**
     * 判断机构名称验重
     */
    @PostMapping("/examineOrgName")
    @PreAuthorize("@syyo.check('company')")
    public Response<Integer> examineOrgName(@RequestBody ExamineOrgNameDTO request) {
        request.setCompanyCode(getCompanyCode());
        return Response.success(companyService.examineOrgName(request));
    }

    /**
     * 修改企业公章
     */
    @PostMapping(value = "/modifyCompanyOfficialSeal", produces = {"application/json;charset=UTF-8"})
    @ResponseBody
    @PreAuthorize("@syyo.check('company')")
    public Response<Object> modifyCompanyOfficialSeal(@RequestBody Map<String, String> request) {
        request.put("updatedBy", getUserName());
        companyService.modifyCompanyOfficialSeal(request);
        return Response.success();
    }

    /**
     * 修改企业公章
     */
    @PostMapping(value = "/deleteCompanyOfficialSeal", produces = {"application/json;charset=UTF-8"})
    @ResponseBody
    @PreAuthorize("@syyo.check('company')")
    public Response<Object> deleteCompanyOfficialSeal(@RequestBody Map<String, String> request) {
        request.put("updatedBy", getUserName());
        companyService.deleteCompanyOfficialSeal(request);
        return Response.success();
    }

    /**
     * 删除企业logo和营业执照
     */
    @PostMapping("/deleteOrgPicture")
    @PreAuthorize("@syyo.check('company')")
    public Response<Object> deleteOrgPicture(@RequestBody Map<String, String> requestMap) {
        requestMap.put("updatedBy", getUserName());
        DeleteFileRequestDTO deleteFileRequestDTO = new DeleteFileRequestDTO();
        deleteFileRequestDTO.setFileUrl(requestMap.get("pictureUrl"));
        companyService.deleteOrgPicture(requestMap);
        return Response.success();
    }

    /**
     * 上传营业执照
     */
    @PostMapping("/uploadCompanyBusinessLicenseUrl")
    @PreAuthorize("@syyo.check('company')")
    public Response<Object> uploadCompanyBusinessLicenseUrl(@RequestBody List<CompanyBusinessLicenseUrlDTO> companyBusinessLicenseUrls, @RequestParam("updatedBy") String updatedBy) {
        companyService.uploadCompanyBusinessLicenseUrl(companyBusinessLicenseUrls, updatedBy);
        return Response.success();
    }

    /**
     * 上传公司log
     */
    @PostMapping("/uploadCompanyLogoUrl")
    @PreAuthorize("@syyo.check('company')")
    public Response<Object> uploadCompanyLogoUrl(@RequestBody CompanyLogoUrlDTO companyLogoUrlDTO) {
        companyLogoUrlDTO.setCreatedBy(getUserName());
        companyService.uploadCompanyLogoUrl(companyLogoUrlDTO);
        return Response.success();
    }

    /**
     * 获取商家币种
     */
    @PostMapping("/getOrgCurrency")
    @PreAuthorize("@syyo.check('company')")
    public Response<String> getOrgCurrency() {
        OrgDTO orgDTO = CommonInitializer.getOrgInfo();
        return Response.success(orgDTO.getOrgCurrency());
    }

    /**
     * 刷新机构信息
     */
    @PostMapping("/refreshOrgInfo")
    @AnonymousAccess
    public Response<Object> refreshOrgInfo() {
        // 发布刷新消息到Redis频道
        RedisTemplateX.convertAndSend(RedisKey.REFRESH_ORG_INFO, "refresh");
        return Response.success();
    }
}
