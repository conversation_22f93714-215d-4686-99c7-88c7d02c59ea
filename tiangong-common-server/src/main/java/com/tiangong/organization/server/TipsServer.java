package com.tiangong.organization.server;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tiangong.cloud.common.anno.AnonymousAccess;
import com.tiangong.common.Response;
import com.tiangong.convert.CommonDtoConvert;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.dto.common.TipsDTO;
import com.tiangong.organization.domain.TipsPO;
import com.tiangong.organization.domain.resp.TipsReq;
import com.tiangong.organization.domain.resp.TipsResp;
import com.tiangong.organization.service.TipsService;
import com.tiangong.util.CollUtilX;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/common/tips")
public class TipsServer {

    @Autowired
    private TipsService tipsService;

    /**
     * 新增或修改提示
     */
    @PostMapping("addOrUpdate")
    public Response<Object> addOrUpdate(@RequestBody TipsPO po) {
        tipsService.addOrUpdate(po);
        return Response.success();
    }

    /**
     * 查询提示信息列表（分页）
     */
    @PostMapping("list")
    public Response<PaginationSupportDTO<TipsResp>> list(@RequestBody TipsReq req) {
        return Response.success(tipsService.queryConfigList(req));
    }

    /**
     * 删除提示列表
     */
    @PostMapping("del")
    public Response<Object> querySysConfigByKey(@RequestBody TipsPO po) {
        tipsService.del(po.getId());
        return Response.success();
    }

    /**
     * 查询提示列表
     */
    @PostMapping("findTips")
    @AnonymousAccess
    public Response<List<com.tiangong.dto.common.TipsResp>> findTips(@RequestBody TipsDTO po) {
        LambdaQueryWrapper<TipsPO> queryWrapper = new LambdaQueryWrapper<>();
        if (StrUtilX.isNotEmpty(po.getLanguage())) {
            queryWrapper.eq(TipsPO::getLanguage, po.getLanguage());
        }
        queryWrapper.eq(TipsPO::getSupplierCode, po.supplierCode).eq(TipsPO::getActive, 1);
        List<TipsPO> one = tipsService.list(queryWrapper);
        List<com.tiangong.dto.common.TipsResp> list = new ArrayList<>();
        if (one != null && !one.isEmpty()) {
            for (TipsPO tipsPO : one) {
                com.tiangong.dto.common.TipsResp resp = CommonDtoConvert.INSTANCE.respConvert(tipsPO);
                list.add(resp);
            }
        }
        return Response.success(list);
    }

    /**
     * 查询提示列表(有效)
     */
    @PostMapping("findTipList")
    public Response<List<com.tiangong.dto.common.TipsResp>> findTipList(@RequestBody TipsDTO po) {
        LambdaQueryWrapper<TipsPO> queryWrapper = new LambdaQueryWrapper<>();
        if (StrUtilX.isNotEmpty(po.getLanguage())) {
            queryWrapper.eq(TipsPO::getLanguage, po.getLanguage());
        }
        queryWrapper.eq(TipsPO::getActive, 1);
        List<TipsPO> poList = tipsService.list(queryWrapper);
        List<com.tiangong.dto.common.TipsResp> list = new ArrayList<>();
        if (CollUtilX.isNotEmpty(poList)) {
            list = CommonDtoConvert.INSTANCE.tipsRespConvert(poList);
        }
        return Response.success(list);
    }
}
