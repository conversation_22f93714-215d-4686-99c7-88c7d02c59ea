package com.tiangong.organization.server;


import com.tiangong.annotations.SlsLog;
import com.tiangong.common.Response;
import com.tiangong.organization.remote.dto.AreaDataDTO;
import com.tiangong.organization.service.AreaDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@Slf4j
@RequestMapping(value = "/common")
public class AreaDataServer {

    @Autowired
    private AreaDataService areaDataService;

    /**
     * 查询城市
     */
    @PostMapping("/areaData/queryAreaData")
    @PreAuthorize("@syyo.check('areadata')")
    public Response<List<AreaDataDTO>> queryAreaData(@RequestBody AreaDataDTO request) {
        List<AreaDataDTO> areaDataDTOList = areaDataService.queryAreaData(request);
        return Response.success(areaDataDTOList);
    }
}
