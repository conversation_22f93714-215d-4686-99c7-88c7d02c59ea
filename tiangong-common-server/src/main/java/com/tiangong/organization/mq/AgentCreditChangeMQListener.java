package com.tiangong.organization.mq;

import com.alibaba.fastjson.JSON;
import com.tiangong.constanct.MqConstants;
import com.tiangong.organization.remote.dto.AgentCreditChangeMessageDTO;
import com.tiangong.organization.service.AgentCreditWarningService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.common.consumer.ConsumeFromWhere;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.MessageModel;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.apache.rocketmq.spring.core.RocketMQPushConsumerLifecycleListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 客户信用额度变更MQ监听器
 * 
 * <AUTHOR>
 * @date 2024/12/19
 */
@Slf4j
@Service
@RocketMQMessageListener(
        topic = MqConstants.CREDIT_CHANGE_TOPIC,
        consumerGroup = "agent_credit-change-consumer-group",
        consumeMode = ConsumeMode.CONCURRENTLY, // 普通
        messageModel = MessageModel.CLUSTERING // 集群模式
)
public class AgentCreditChangeMQListener implements RocketMQListener<MessageExt>, RocketMQPushConsumerLifecycleListener {
    
    @Autowired
    private AgentCreditWarningService creditWarningService;
    
    @Override
    public void onMessage(MessageExt messageExt) {
        try {
            String messageBody = new String(messageExt.getBody());
            log.info("接收到客户信用额度变更消息：{}", messageBody);
            
            // 解析消息
            AgentCreditChangeMessageDTO creditChangeMessage = JSON.parseObject(messageBody, AgentCreditChangeMessageDTO.class);
            
            if (creditChangeMessage == null || creditChangeMessage.getAgentCode() == null) {
                log.warn("客户信用额度变更消息格式错误，跳过处理：{}", messageBody);
                return;
            }
            
            // 处理额度预警检查
            creditWarningService.checkAndSendWarning(creditChangeMessage);
            
            log.info("客户信用额度变更消息处理完成，客户编码：{}", creditChangeMessage.getAgentCode());
            
        } catch (Exception e) {
            log.error("处理客户信用额度变更消息异常", e);
            // 抛出异常会触发RocketMQ的重试机制
            throw new RuntimeException("消息处理失败", e);
        }
    }
    
    @Override
    public void prepareStart(DefaultMQPushConsumer consumer) {
        // 设置最大重试次数
        consumer.setMaxReconsumeTimes(3);
        // 设置消费线程数
        consumer.setConsumeThreadMin(5);
        consumer.setConsumeThreadMax(10);
        // 设置从最早的消息开始消费
        consumer.setConsumeFromWhere(ConsumeFromWhere.CONSUME_FROM_FIRST_OFFSET);
        
        log.info("客户信用额度变更MQ监听器启动完成");
    }
}
