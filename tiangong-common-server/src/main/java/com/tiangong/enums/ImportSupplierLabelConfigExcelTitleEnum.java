package com.tiangong.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ImportSupplierLabelConfigExcelTitleEnum {
    AGENT_CODE(0,"客户编码"),
    LABEL_TYPE(1,"标签类型"),
    SUPPLIER_CODE(2,"供应商编码"),
    ;

    public final int key;
    public final String value;

    public static int getKeyByValue(String value) {
        int key = 0;
        for(ImportSupplierLabelConfigExcelTitleEnum titleEnum : ImportSupplierLabelConfigExcelTitleEnum.values()) {
            if(titleEnum.value.equals(value.trim())) {
                key = titleEnum.key;
                break;
            }
        }
        return key;
    }

}
