<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tiangong.hotel.mapper.FacilityMapper">

    <resultMap id="queryFacilityMap" type="com.tiangong.hotel.domain.resp.FacilityListResp">
        <result property="id" column="id"/>
        <result property="facilityCode" column="facility_code"/>
        <result property="facilityName" column="facility_name"/>
        <result property="facilityType" column="facility_type"/>
        <result property="typeScore" column="type_score"/>
        <result property="facilityIdList" column="facility_id_list"/>
        <collection property="facilityNameRespList" column="facility_id_list" ofType="com.tiangong.hotel.domain.resp.FacilityNameResp" select="selectFacilityName">
            <result property="facilityName" column="facility_name"/>
            <result property="facilityId" column="facility_id"/>
            <result property="language" column="language"/>
        </collection>
    </resultMap>
    
    <resultMap id="facilityNameMap" type="com.tiangong.hotel.domain.resp.FacilityNameResp">
        <result property="facilityName" column="facility_name"></result>
    </resultMap>

    <select id="queryFacility" resultMap="queryFacilityMap" parameterType="java.lang.Object">
        select id,
               facility_code,
               facility_name,
               facility_type,
               type_score,
               facility_id_list
        from t_facility_config where facility_type = #{facilityType}
        order by type_score desc
    </select>

    <select id="selectFacilityName" resultType="com.tiangong.hotel.domain.resp.FacilityNameResp" parameterType="java.lang.String">
        select facility_id,facility_name,language from t_facility where FIND_IN_SET (facility_id, #{facilityIdList})
    </select>

    <select id="queryFacilityPage" resultType="com.tiangong.hotel.domain.resp.FacilityNameResp" parameterType="java.lang.Object">
        select
            facility_id,
            facility_name
        from t_facility
        where facility_type = #{facilityType}
        and `language` = #{language}
        <if test="facilityName != null and facilityName != '' ">
            AND facility_name like concat('%',#{facilityName}, '%')
        </if>
        <if test="facilityIdList != null and facilityIdList != '' ">
            AND  NOT FIND_IN_SET (facility_id, #{facilityIdList})
        </if>
    </select>

    <select id="selectFacilityNameByFacilityCode" resultType="com.tiangong.hotel.domain.resp.FacilityNameResp" parameterType="java.lang.String">
        select facility_id,facility_name
        from t_facility
        where language = #{language}
        and FIND_IN_SET (facility_id,
        (select facility_id_list from t_facility_config where  facility_code = #{facilityCode})
            )
        <if test="facilityName != null and facilityName != '' ">
            AND facility_name like concat('%',#{facilityName}, '%')
        </if>
    </select>

    <select id="queryHotFacilityCode" resultType="com.tiangong.dto.hotel.HotFacilityDTO">
    select
        facility_code,
        facility_name,
        facility_type
    from t_facility_config
    where facility_code in
    <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
        #{item}
    </foreach>
    order by type_score desc
    </select>
</mapper>