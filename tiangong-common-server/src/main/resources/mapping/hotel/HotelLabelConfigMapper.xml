<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.tiangong.hotel.mapper.HotelLabelConfigMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap id="BaseResultMap" type="com.tiangong.hotel.domain.HotelLabelConfigEntity">
        <result property="hotelLabelConfigId" column="hotel_label_config_id"/>
        <result property="labelCode" column="label_code"/>
        <result property="labelName" column="label_name"/>
        <result property="recommendScore" column="recommend_score"/>
        <result property="deleted" column="deleted"/>
        <result property="createdBy" column="created_by"/>
        <result property="createdDt" column="created_dt"/>
        <result property="updatedBy" column="updated_by"/>
        <result property="updatedDt" column="updated_dt"/>
    </resultMap>

    <select id="hotelLabelConfigList" resultType="com.tiangong.hotel.resp.HotelLabelConfigResp">
        select
            hotel_label_config_id,
            label_code,
            label_name,
            recommend_score,
            created_dt,
            created_by,
            updated_dt,
            updated_by
        from t_hotel_label_config
        where deleted = 0
        <if test="labelName != null and labelName != ''">
            and label_name like concat('%', #{labelName}, '%')
        </if>
        order by created_dt desc
    </select>

    <select id="selectHotelLabelConfigMaxId" resultType="java.lang.Integer">
        select max(hotel_label_config_id) from t_hotel_label_config
    </select>

    <select id="queryHotelLabelByIds" resultType="com.tiangong.dto.hotel.HotelLabelResp"
            parameterType="com.tiangong.hotel.domain.vo.HotelLabelDTO">
        select
        hotel_label_config_id hotelLabelId,
        label_name hotelLabelName
        from t_hotel_label_config
        where hotel_label_config_id in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>