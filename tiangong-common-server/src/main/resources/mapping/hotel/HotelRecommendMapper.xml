<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.tiangong.hotel.mapper.HotelRecommendMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap id="BaseResultMap" type="com.tiangong.hotel.domain.HotelRecommendEntity">
        <result property="id" column="id"/>
        <result property="agentCode" column="agent_code"/>
        <result property="hotelId" column="hotel_id"/>
        <result property="recommendType" column="recommend_type"/>
        <result property="recommendScore" column="recommend_score"/>
        <result property="createdBy" column="created_by"/>
        <result property="createdDt" column="created_dt"/>
        <result property="updatedBy" column="updated_by"/>
        <result property="updatedDt" column="updated_dt"/>
        <result property="deleted" column="deleted"/>
    </resultMap>

    <resultMap id="HotelListMap" type="com.tiangong.hotel.resp.HotelRecommendResp">
        <result property="id" column="id"/>
        <result property="agentCode" column="agentCode"/>
        <result property="agentName" column="agentName"/>
        <result property="hotelId" column="hotelId"/>
        <result property="hotelName" column="hotelName"/>
        <result property="cityName" column="cityName"/>
        <result property="recommendType" column="recommendType"/>
        <result property="recommendScore" column="recommendScore"/>
        <result property="sumRecommendScore" column="sumRecommendScore"/>
        <result property="updatedBy" column="updatedBy"/>
        <result property="updatedDt" column="updatedDt"/>

        <collection property="hotelLabelConfigList" select="com.tiangong.hotel.mapper.HotelRecommendMapper.selectHotelLabelConfigList"
                    column="id" javaType="ArrayList" ofType="com.tiangong.hotel.resp.HotelLabelConfigResp">
            <result property="hotelLabelConfigId" column="hotelLabelConfigId"/>
            <result property="labelName" column="labelName"/>
        </collection>
    </resultMap>

    <resultMap id="HotelLabelMap" type="com.tiangong.product.resp.HotelLabelDTO">
        <result property="hotelId" column="hotel_id"/>

        <collection property="hotelLabelNameList" ofType="java.lang.String">
            <result column="label_name"/>
        </collection>
    </resultMap>

    <select id="selectHotelRecommendPage" resultMap="HotelListMap" parameterType="com.tiangong.hotel.req.HotelRecommendReq">
        select
            t1.id                        id,
            t1.agent_code                agentCode,
            t2.org_name                  agentName,
            t1.hotel_id                  hotelId,
            t1.recommend_type            recommendType,
            t1.recommend_score           recommendScore,
            IFNULL(t4.recommend_score, 0) * 10000 + IFNULL(t1.recommend_score, 0) sumRecommendScore,
            t1.updated_by                updatedBy,
            t1.updated_dt                updatedDt
        from t_hotel_recommend t1
        left join t_org_organization t2 on t1.agent_code = t2.org_code
        left join (
            select
                t4.hotel_recommend_id,
                max(t5.recommend_score) recommend_score
            from t_hotel_recommend_label_rel t4
            left join t_hotel_label_config t5 on t5.hotel_label_config_id = t4.hotel_label_config_id
            group by t4.hotel_recommend_id
        ) t4 on t4.hotel_recommend_id = t1.id
        where t1.deleted = 0
        <if test="req.agentName != null and req.agentName != ''">
            and t2.org_name like concat(concat('%',#{req.agentName}),'%')
        </if>
        <if test="req.agentCode != null and req.agentCode != ''">
            and t1.agent_code = #{req.agentCode}
        </if>
        <if test="req.hotelId != null">
            and t1.hotel_id = #{req.hotelId}
        </if>
        <if test="req.recommendType != null and req.recommendType != ''">
            and t1.recommend_type = #{req.recommendType}
        </if>
        order by t1.updated_dt desc
    </select>

    <select id="selectHotelLabelConfigList" resultType="com.tiangong.hotel.resp.HotelLabelConfigResp">
        select
            hlc.hotel_label_config_id   hotelLabelConfigId,
            hlc.label_name              labelName
        from t_hotel_recommend_label_rel hrlr
        left join t_hotel_label_config hlc on hlc.hotel_label_config_id = hrlr.hotel_label_config_id
        where hrlr.hotel_recommend_id = #{id}
    </select>

    <select id="queryHotelLabelInfo" resultType="com.tiangong.hotel.resp.HotelLabelConfigResp">
        SELECT
            t2.hotel_label_config_id    hotelLabelConfigId,
            t3.label_name               labelName
        FROM t_hotel_recommend t1
        LEFT JOIN t_hotel_recommend_label_rel t2 ON t2.hotel_recommend_id = t1.id
        LEFT JOIN t_hotel_label_config t3 ON t3.hotel_label_config_id = t2.hotel_label_config_id
        WHERE t1.deleted = 0 AND t1.hotel_id = #{hotelId} AND (t1.agent_code = #{agentCode} OR t1.agent_code = "0") AND t3.label_name is not null
    </select>
    
    <select id="queryHotelLabelAll" resultType="com.tiangong.hotel.domain.vo.HotelLabelAllDTO">
        SELECT
            rel.hotel_label_config_id,
            hre.agent_code,
            hre.hotel_id
        FROM
            `t_hotel_recommend_label_rel` rel
                LEFT JOIN t_hotel_recommend hre ON rel.hotel_recommend_id = hre.id
        WHERE hre.deleted = 0
    </select>

    <select id="selectAgentHotelLabel" resultMap="HotelLabelMap">
        SELECT
        t1.hotel_id,
        t3.label_name
        FROM t_hotel_recommend t1
        LEFT JOIN t_hotel_recommend_label_rel t2 ON t2.hotel_recommend_id = t1.id
        LEFT JOIN t_hotel_label_config t3 ON t3.hotel_label_config_id = t2.hotel_label_config_id
        WHERE
        t1.deleted = 0
        AND (t1.agent_code = #{agentCode} or t1.agent_code = '0')
        <if test="hotelIdList!=null">
            AND t1.hotel_id IN
            <foreach collection="hotelIdList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectHotelRecommendScoreList" resultType="com.tiangong.hotel.resp.HotelRecommendResp">
        select
            t1.agent_code                agentCode,
            t1.hotel_id                  hotelId,
            IFNULL(t4.recommend_score, 0) * 10000 + IFNULL(
            <if test="recommendScore != null">
                #{recommendScore}
            </if>
            <if test="recommendScore == null">
                t1.recommend_score
            </if>
            , 0) sumRecommendScore
        from t_hotel_recommend t1
        left join (
            select
                t2.hotel_recommend_id,
                max(t3.recommend_score) recommend_score
            from t_hotel_recommend_label_rel t2
            left join t_hotel_label_config t3 on t3.hotel_label_config_id = t2.hotel_label_config_id
            group by t2.hotel_recommend_id
        ) t4 on t4.hotel_recommend_id = t1.id
        where t1.deleted = 0
        <if test="hotelRecommendIdList != null">
            and t1.id in
            <foreach collection="hotelRecommendIdList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="hotelLabelConfigId != null">
            and t1.id in (
                select
                    hotel_recommend_id
                from t_hotel_recommend_label_rel t5
                left join t_hotel_recommend t6 on t6.id = t5.hotel_recommend_id
                where t6.deleted = 0 and t5.hotel_label_config_id = #{hotelLabelConfigId}
            )
        </if>
    </select>

    <select id="selectHotelRecommendScorePage" resultType="com.tiangong.hotel.resp.HotelRecommendResp">
        select
            t1.agent_code                agentCode,
            t1.hotel_id                  hotelId,
            IFNULL(t4.recommend_score, 0) * 10000 + IFNULL(t1.recommend_score, 0) sumRecommendScore
        from t_hotel_recommend t1
        left join (
            select
                t2.hotel_recommend_id,
                max(t3.recommend_score) recommend_score
            from t_hotel_recommend_label_rel t2
            left join t_hotel_label_config t3 on t3.hotel_label_config_id = t2.hotel_label_config_id
            group by t2.hotel_recommend_id
        ) t4 on t4.hotel_recommend_id = t1.id
        where t1.deleted = 0
        order by t1.id
        LIMIT #{batchSize} offset #{offset}
    </select>

    <select id="selectAllRecommend" resultType="com.tiangong.hotel.resp.HotelRecommendResp">
        SELECT
            agent_code   agentCode,
            hotel_id     hotelId
        FROM t_hotel_recommend
        WHERE deleted = 0
    </select>
</mapper>