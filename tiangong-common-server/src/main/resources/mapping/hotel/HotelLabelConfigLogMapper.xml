<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.tiangong.hotel.mapper.HotelLabelConfigLogMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap id="BaseResultMap" type="com.tiangong.hotel.domain.HotelLabelConfigLogEntity">
        <result property="id" column="id"/>
        <result property="hotelLabelConfigId" column="hotel_label_config_id"/>
        <result property="createdBy" column="created_by"/>
        <result property="createdDt" column="created_dt"/>
        <result property="updatedBy" column="updated_by"/>
        <result property="updatedDt" column="updated_dt"/>
    </resultMap>

    <select id="hotelLabelConfigLogList" resultType="com.tiangong.hotel.resp.HotelLabelConfigLogResp">
        select
            id,
            hotel_label_config_id,
            content,
            created_dt,
            created_by
        from t_hotel_label_config_log
        where hotel_label_config_id = #{hotelLabelConfigId}
        order by created_dt desc
    </select>

</mapper>