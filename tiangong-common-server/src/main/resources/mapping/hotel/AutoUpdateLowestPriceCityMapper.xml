<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tiangong.hotel.mapper.AutoUpdateLowestPriceCityMapper">
    <resultMap id="BaseResultMap" type="com.tiangong.hotel.domain.AutoUpdateLowestPriceCityEntity">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="destination_id" jdbcType="VARCHAR" property="destinationId" />
        <result column="city_name" jdbcType="VARCHAR" property="cityName" />
        <result column="hotel_count" jdbcType="INTEGER" property="hotelCount" />
        <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
        <result column="created_dt" jdbcType="TIMESTAMP" property="createdDt" />
        <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
        <result column="updated_dt" jdbcType="TIMESTAMP" property="updatedDt" />
        <result column="deleted" jdbcType="TINYINT" property="deleted" />
    </resultMap>

    <select id="autoUpdateLowestPriceCityPage" resultType="com.tiangong.hotel.domain.resp.AutoUpdateLowestPriceCityResp">
        select
            id,
            destination_id,
            city_name,
            ifnull(hotel_count, 20) hotelCount,
            created_by,
            created_dt,
            updated_by,
            updated_dt,
            deleted
        from t_auto_update_lowest_price_city
        order by created_dt desc
    </select>

</mapper>