<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tiangong.hotel.mapper.AutoUpdateLowestPriceConfigMapper">
    <resultMap id="BaseResultMap" type="com.tiangong.hotel.domain.AutoUpdateLowestPriceConfigEntity">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="agent_code" jdbcType="VARCHAR" property="agentCode" />
        <result column="agent_name" jdbcType="VARCHAR" property="agentName" />
        <result column="frequency" jdbcType="INTEGER" property="frequency" />
        <result column="execute_date" jdbcType="DATE" property="executeDate" />
        <result column="is_succeed" jdbcType="TINYINT" property="isSucceed" />
        <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
        <result column="created_dt" jdbcType="TIMESTAMP" property="createdDt" />
        <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
        <result column="updated_dt" jdbcType="TIMESTAMP" property="updatedDt" />
        <result column="deleted" jdbcType="TINYINT" property="deleted" />
    </resultMap>

</mapper>