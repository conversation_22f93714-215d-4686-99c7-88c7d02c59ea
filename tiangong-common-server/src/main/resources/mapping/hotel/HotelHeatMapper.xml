<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.tiangong.hotel.mapper.HotelHeatMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap id="BaseResultMap" type="com.tiangong.hotel.domain.HotelHeatEntity">
        <result property="hotelId" column="hotel_id"/>
        <result property="heatScore" column="heat_score"/>
        <result property="bookableScore" column="bookable_score"/>
        <result property="gradeScore" column="grade_score"/>
        <result property="groupScore" column="group_score"/>
        <result property="cityAvgPriceScore" column="city_avg_price_score"/>
        <result property="roomNightScore" column="room_night_score"/>
        <result property="createdBy" column="created_by"/>
        <result property="createdDt" column="created_dt"/>
        <result property="updatedBy" column="updated_by"/>
        <result property="updatedDt" column="updated_dt"/>
    </resultMap>

    <select id="selectHotelHeatPage" resultType="com.tiangong.hotel.resp.HotelHeatResp">
        SELECT
            t1.hotel_id,
            t1.heat_score,
            t1.updated_by,
            t1.updated_dt
        FROM t_hotel_heat t1
        WHERE t1.heat_score != 0
        <if test="req.hotelId != null">
            AND t1.hotel_id = #{req.hotelId}
        </if>
    </select>

    <update id="updateHotelHeatScore">
        UPDATE t_hotel_heat
        SET updated_by = #{updatedBy},
            updated_dt = #{updatedDt},
            <if test="bookableScore != null">
                bookable_score = #{bookableScore},
            </if>
            <if test="gradeScore != null">
                grade_score = #{gradeScore},
            </if>
            <if test="groupScore != null">
                group_score = #{groupScore},
            </if>
            <if test="cityAvgPriceScore != null">
                city_avg_price_score = #{cityAvgPriceScore},
            </if>
            <if test="roomNightScore != null">
                room_night_score = #{roomNightScore},
            </if>
            heat_score = bookable_score + grade_score + group_score + city_avg_price_score + room_night_score
        WHERE hotel_id = #{hotelId}
    </update>

    <update id="updateHotelHeatScoreTotalValue">
        UPDATE t_hotel_heat
        SET updated_by = #{updatedBy},
        updated_dt = #{updatedDt},
        heat_score = bookable_score + grade_score + group_score + city_avg_price_score + room_night_score
        WHERE hotel_id IN
        <foreach collection="hotelIds" open="(" close=")" index="index" item="item" separator=",">
            #{item}
        </foreach>
    </update>

    <update id="updateBatchFields">
        <foreach collection="list" item="item" separator=";">
            UPDATE t_hotel_heat
            SET
            <if test="item.bookableScore != null">
                bookable_score = #{item.bookableScore},
            </if>
            <if test="item.gradeScore != null">
                bookable_score = #{item.gradeScore},
            </if>
            <if test="item.groupScore != null">
                group_score = #{item.groupScore},
            </if>
            <if test="item.cityAvgPriceScore != null">
                city_avg_price_score = #{item.cityAvgPriceScore},
            </if>
            <if test="item.roomNightScore != null">
                room_night_score = #{item.roomNightScore},
            </if>
            updated_by = #{item.updatedBy},
            updated_dt = #{item.updatedDt}
            WHERE hotel_id = #{item.hotelId}
        </foreach>
    </update>

    <select id="selectHotelHeatScorePage" resultType="com.tiangong.hotel.resp.HotelHeatResp">
        select
            t1.hotel_id,
            t1.heat_score
        from t_hotel_heat t1
        order by t1.hotel_id
        LIMIT #{batchSize} offset #{offset}
    </select>

    <select id="selectHotelHeatScoreByHotelIds" resultType="com.tiangong.hotel.resp.HotelHeatResp">
        select
            t1.hotel_id,
            t1.heat_score
        from t_hotel_heat t1
        where t1.hotel_id in
        <foreach collection="hotelIds" open="(" close=")" index="index" item="item" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="selectAllHotelHeatHotelIdPage" resultType="java.lang.String">
        SELECT
            t1.hotel_id
        FROM t_hotel_heat t1
        WHERE t1.hotel_id > #{lastId}
        ORDER BY t1.hotel_id
        LIMIT #{batchSize}
    </select>

    <select id="selectHotelIdByHotelIds" resultType="java.lang.String">
        select
            t1.hotel_id
        from t_hotel_heat t1
        where t1.hotel_id in
        <foreach collection="hotelIds" open="(" close=")" index="index" item="item" separator=",">
            #{item}
        </foreach>
    </select>

</mapper>