<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.tiangong.hotel.mapper.HotelAvgPriceMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap id="BaseResultMap" type="com.tiangong.hotel.domain.HotelAvgPriceEntity">
        <result property="hotelId" column="hotel_id"/>
        <result property="avgPrice" column="avg_price"/>
        <result property="cityCode" column="city_code"/>
        <result property="createdBy" column="created_by"/>
        <result property="createdDt" column="created_dt"/>
        <result property="updatedBy" column="updated_by"/>
        <result property="updatedDt" column="updated_dt"/>
    </resultMap>

    <delete id="deleteAll">
        DELETE FROM t_hotel_avg_price
    </delete>

    <select id="selectHotelAvgPriceCityCodes" resultType="java.lang.String">
        SELECT
            city_code
        FROM t_hotel_avg_price
        GROUP BY city_code
    </select>

    <select id="selectHotelAvgPriceList" resultType="com.tiangong.hotel.resp.HotelAvgPriceResp">
        SELECT
            hotel_id,
            avg_price,
            city_code,
            country_code,
            created_by,
            created_dt
        FROM t_hotel_avg_price
        WHERE 1=1
        <if test="hotelId != null">
            AND hotel_id = #{hotelId}
        </if>
        <if test="cityCode != null and cityCode != ''">
            AND city_code = #{cityCode}
        </if>
        <if test="hotelIds != null and hotelIds.size > 0">
            AND hotel_id IN
            <foreach collection="hotelIds" separator="," open="(" close=")" index="index" item="item">
                #{item}
            </foreach>
        </if>
    </select>

</mapper>