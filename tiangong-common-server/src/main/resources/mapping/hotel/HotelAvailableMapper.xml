<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tiangong.hotel.mapper.HotelAvailableMapper">

    <select id="queryHotelListAvailable" parameterType="com.tiangong.hotel.domain.req.QueryHotelAvailableReq"
            resultType="com.tiangong.hotel.domain.resp.HotelAvailableListResp">
        SELECT
            ha.id,
            ha.agent_code,
            ha.available_type,
            o.org_name as agentName,
            ha.hotel_id
        FROM t_hotel_available ha
        LEFT JOIN t_org_organization o ON ha.agent_code = o.org_code
        where o.type = 1
        <if test="req.agentCode != null and req.agentCode != '' ">
            AND o.org_code = #{req.agentCode}
        </if>
        <if test="req.agentName != null and req.agentName != '' ">
            AND o.org_name like concat('%', #{req.agentName}, '%')
        </if>
        <if test="req.hotelId != null">
            AND ha.hotel_id = #{req.hotelId}
        </if>
        order by ha.id desc
    </select>

</mapper>