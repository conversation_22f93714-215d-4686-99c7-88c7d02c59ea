<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.tiangong.vip.mapper.AgentVipClientMapper">

    <!-- VIP客户名单结果映射 -->
    <resultMap id="VipClientDTOMap" type="com.tiangong.vip.dto.AgentVipClientDTO">
        <id column="id" property="id"/>
        <result column="agent_name" property="agentName"/>
        <result column="agent_code" property="agentCode"/>
        <result column="scope_type" property="scopeType"/>
        <result column="vip_name" property="vipName"/>
        <result column="vip_level" property="vipLevel"/>
        <result column="remark" property="remark"/>
        <result column="partner_code" property="partnerCode"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_dt" property="createdDt"/>
        <result column="updated_by" property="updatedBy"/>
        <result column="updated_dt" property="updatedDt"/>
    </resultMap>

    <!-- 分页查询VIP客户名单列表 -->
    <select id="selectVipClientPageList" resultMap="VipClientDTOMap">
        SELECT
        id,
        agent_name,
        agent_code,
        scope_type,
        vip_name,
        vip_level,
        remark,
        partner_code,
        created_by,
        created_dt,
        updated_by,
        updated_dt
        FROM t_agent_vip_client
        <where>
            <!-- 客户名称：精确匹配查询 -->
            <if test="vipClientVO.agentName != null and vipClientVO.agentName != ''">
                AND agent_name = #{vipClientVO.agentName}
            </if>
            <!-- 客户编码：模糊查询 -->
            <if test="vipClientVO.agentCode != null and vipClientVO.agentCode != ''">
                AND agent_code =#{vipClientVO.agentCode}
            </if>
            <!-- VIP范围：精确匹配查询 -->
            <if test="vipClientVO.scopeType != null">
                AND scope_type = #{vipClientVO.scopeType}
            </if>
            <!-- VIP姓名：模糊查询 -->
            <if test="vipClientVO.vipName != null and vipClientVO.vipName != ''">
                AND vip_name LIKE CONCAT('%', #{vipClientVO.vipName}, '%')
            </if>
            <!-- VIP等级：精确匹配查询 -->
            <if test="vipClientVO.vipLevel != null">
                AND vip_level = #{vipClientVO.vipLevel}
            </if>
            <!-- 合作商编码：模糊查询 -->
            <if test="vipClientVO.partnerCode != null and vipClientVO.partnerCode != ''">
                AND partner_code LIKE CONCAT('%', #{vipClientVO.partnerCode}, '%')
            </if>
        </where>
        ORDER BY created_dt DESC
    </select>

    <select id="selectVipClientList" resultMap="VipClientDTOMap">
        SELECT
        id,
        agent_name,
        agent_code,
        scope_type,
        vip_name,
        vip_level,
        remark,
        partner_code,
        created_by,
        created_dt,
        updated_by,
        updated_dt
        FROM t_agent_vip_client
        <where>
            <!-- 客户名称：精确匹配查询 -->
            <if test="vipClientVO.agentName != null and vipClientVO.agentName != ''">
                AND agent_name = LIKE CONCAT('%', #{vipClientVO.agentName}, '%')
            </if>
            <!-- 客户编码：模糊查询 -->
            <if test="vipClientVO.agentCode != null and vipClientVO.agentCode != ''">
                AND agent_code LIKE CONCAT('%', #{vipClientVO.agentCode}, '%')
            </if>
            <!-- VIP范围：精确匹配查询 -->
            <if test="vipClientVO.scopeType != null">
                AND scope_type = #{vipClientVO.scopeType}
            </if>
            <!-- VIP姓名：模糊查询 -->
            <if test="vipClientVO.vipName != null and vipClientVO.vipName != ''">
                AND vip_name LIKE CONCAT('%', #{vipClientVO.vipName}, '%')
            </if>
            <!-- VIP等级：精确匹配查询 -->
            <if test="vipClientVO.vipLevel != null">
                AND vip_level = #{vipClientVO.vipLevel}
            </if>
            <!-- 合作商编码：模糊查询 -->
            <if test="vipClientVO.partnerCode != null and vipClientVO.partnerCode != ''">
                AND partner_code LIKE CONCAT('%', #{vipClientVO.partnerCode}, '%')
            </if>
        </where>
        ORDER BY created_dt DESC
    </select>
    <select id="selectVipClientByAgentCodes" resultMap="VipClientDTOMap">
        SELECT
            id,
            agent_name,
            agent_code,
            scope_type,
            vip_name,
            vip_level,
            remark,
            partner_code,
            created_by,
            created_dt,
            updated_by,
            updated_dt
        FROM t_agent_vip_client
        WHERE agent_code IN
        <foreach item="agentCode" collection="agentCodes" open="(" separator="," close=")">
            #{agentCode}
        </foreach>
    </select>
</mapper>
