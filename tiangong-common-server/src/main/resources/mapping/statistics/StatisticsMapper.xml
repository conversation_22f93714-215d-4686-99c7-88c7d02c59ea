<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tiangong.statistics.mapper.StatisticsMapper">

    <resultMap id="operateStatisticsMap" type="com.tiangong.statistics.dto.BusinessReturnDTO">
        <result column="period" property="period"/>
        <collection property="agentStatisticsList" ofType="com.tiangong.statistics.dto.AgentInfoStatisticsDTO">
            <result column="agentCode" property="agentCode"/>
            <result column="agentName" property="agentName"/>
            <result column="orderQty" property="orderQty"/>
            <result column="nightQty" property="nightQty"/>
            <result column="orderTotalAmt" property="sales"/>
            <result column="supplyOrderAmt" property="supplyOrderAmt"/>
            <result column="profit" property="profit"/>
            <result column="profitRate" property="profitRate"/>
            <result column="saleRate" property="saleRate"/>
            <result column="saleCurrency" property="saleCurrency"/>
            <result column="baseCurrency" property="baseCurrency"/>
            <result column="profitRate" property="baseRate"/>
        </collection>
    </resultMap>

    <select id="queryOperateStatistics" parameterType="com.tiangong.statistics.dto.QueryBusinessStatisticsDTO"
            resultMap="operateStatisticsMap">
        SELECT
        c.agent_code agentCode,
        c.agent_name agentName,
        c.saleRate,
        c.saleCurrency,
        c.baseCurrency,
        c.baseRate,
        SUM(c.orderNum) orderQty,
        <choose>
            <when test="statisticsType == 0">
                DATE_FORMAT( c.created_dt, '%Y年%m月%d日 %H:00' ) period,
            </when>
            <when test="statisticsType == 1">
                DATE_FORMAT( c.created_dt, '%Y年%m月%d日' ) period,
            </when>
            <otherwise>
                DATE_FORMAT( c.created_dt, '%Y年%m月' ) period,
            </otherwise>
        </choose>
        SUM( c.nightQty ) nightQty,
        SUM( c.orderTotalAmt ) orderTotalAmt,
        SUM( c.supply_order_amt ) supplyOrderAmt,
        SUM( c.orderTotalAmt - c.supply_order_amt ) profit,
        SUM( c.orderTotalAmt - c.supply_order_amt ) / SUM( c.orderTotalAmt ) profitRate
        -- SUM( c.profit ) profit,
        -- SUM( c.profit ) / SUM( c.orderTotalAmt ) profitRate
        FROM (
        SELECT
        <choose>
            <when test="dateQueryType==1">
                o.start_date created_dt,
            </when>
            <when test="dateQueryType==2">
                o.end_date created_dt,
            </when>
            <otherwise>
                o.created_dt,
            </otherwise>
        </choose>
        o.agent_code,
        o.agent_name,
        IFNULL(o.sale_rate, 1) saleRate,
        IFNULL(o.sale_currency, 0) saleCurrency,
        IFNULL(s.base_currency, 0) baseCurrency,
        IFNULL(s.rate, 1) baseRate,
        CASE WHEN o.order_confirmation_status IN ( 1,3 ) THEN 1 ELSE 0 END orderNum,
        CASE WHEN o.order_confirmation_status IN ( 1,2,3 ) THEN IF(o.order_confirmation_status = 2
        ,o.refund_fee*o.sale_rate,o.order_amt*o.sale_rate) ELSE 0 END orderTotalAmt,
        SUM(if(
        o.hourly=1,
        CASE WHEN o.order_confirmation_status IN ( 1,3 ) THEN 0.5 *
        o.room_qty ELSE 0 END,
        CASE WHEN o.order_confirmation_status IN ( 1,3 ) THEN 0.0+timestampdiff( DAY, o.start_date, o.end_date ) *
        o.room_qty ELSE 0 END
        ) )nightQty,
        SUM( CASE WHEN s.confirmation_status IN ( 1,2,3) THEN IF(s.confirmation_status = 2
        ,s.refund_fee*s.rate,s.supply_order_amt*s.rate)ELSE 0 END ) supply_order_amt
        FROM o_order o
        LEFT JOIN o_supply_order s ON o.id = s.order_id
        LEFT JOIN t_org_organization too ON o.agent_code = too.org_code
        WHERE o.company_code = #{companyCode}
        <choose>
            <when test="dateQueryType==1">
                <if test="startDate != null">
                    AND o.start_date &gt;= #{startDate}
                </if>
                <if test="endDate != null">
                    AND o.start_date &lt;= #{endDate}
                </if>
            </when>
            <when test="dateQueryType==2">
                <if test="startDate != null">
                    AND o.end_date &gt;= #{startDate}
                </if>
                <if test="endDate != null">
                    AND o.end_date &lt;= #{endDate}
                </if>
            </when>
            <otherwise>
                <if test="startDate != null">
                    AND o.created_dt &gt;= #{startDate}
                </if>
                <if test="endDate != null">
                    AND o.created_dt &lt;= #{endDate}
                </if>
            </otherwise>
        </choose>
        <if test="agentCode != null and agentCode != ''">
            AND o.agent_code = #{agentCode}
        </if>
<!--        <if test="starList != null and starList.size > 0 and starList.get(0) != null">-->
<!--            AND b.hotel_rank IN-->
<!--            <foreach collection="starList" item="item" index="index" separator="," open="(" close=")">-->
<!--                #{item}-->
<!--            </foreach>-->
<!--        </if>-->
        <if test="productLabel != null">
            AND o.product_label = #{productLabel}
        </if>
        <if test="productLabelType != null">
            AND o.product_label_type = #{productLabelType}
        </if>
<!--        <if test="groupName != null and groupName != ''">-->
<!--            AND b.group_name = #{groupName}-->
<!--        </if>-->
<!--        <if test="groupIdList != null and groupIdList.size > 0">-->
<!--            AND b.group_code IN-->
<!--            <foreach collection="groupIdList" item="item" index="index" separator="," open="(" close=")">-->
<!--                #{item}-->
<!--            </foreach>-->
<!--        </if>-->
<!--        <if test="brandName != null and brandName != ''">-->
<!--            AND b.brand_name = #{brandName}-->
<!--        </if>-->
<!--        <if test="brandIdList != null and brandIdList.size > 0 and brandIdList.get(0) != null and brandIdList.get(0) != ''">-->
<!--            AND b.brand_code IN-->
<!--            <foreach collection="brandIdList" item="item" index="index" separator="," open="(" close=")">-->
<!--                #{item}-->
<!--            </foreach>-->
<!--        </if>-->
        <if test="cityName != null and cityName != ''">
            AND o.city_name = #{cityName}
        </if>
        <if test="cityCode != null and cityCode != ''">
            AND o.city_code = #{cityCode}
        </if>
<!--        <if test="provinceCode != null and provinceCode != ''">-->
<!--            AND b.province_code = #{provinceCode}-->
<!--        </if>-->
        GROUP BY o.order_code
        )c
        GROUP BY
        <choose>
            <when test="statisticsType == 0">
                DATE_FORMAT( c.created_dt, '%Y-%m-%d %H' ),c.agent_code
            </when>
            <when test="statisticsType == 1">
                DATE_FORMAT( c.created_dt, '%Y-%m-%d' ),c.agent_code
            </when>
            <otherwise>
                DATE_FORMAT( c.created_dt, '%Y-%m' ),c.agent_code
            </otherwise>
        </choose>
    </select>

</mapper>