<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tiangong.organization.mapper.OrgMapper">
    <!--根据机构编码查询对应的总管理员用户Id-->
    <select id="getUserId" resultType="java.lang.Integer">
        select user_id
        from t_auth_user
        where org_code = #{companyCode}
        AND t_auth_user.is_super_admin = 1
    </select>

    <select id="getUserListByOrgCode" resultType="com.tiangong.organization.domain.dto.UserListDTO">
        select user_id,user_account
        from t_auth_user
        where org_code = #{companyCode}
    </select>
    <!--根据机构Id查询对应的供应商运营商Id-->
    <select id="getSupplierCompany" resultType="java.lang.Integer">
        select s_company_id
        from t_org_company_supplier
        where org_id = #{supplierId}
        and company_code = #{companyCode}
    </select>
    <!--根据机构Id查询对应的客户运营商Id
    -->
    <select id="getAgentCompany" resultType="java.lang.Integer">
        select a_company_id
        from t_org_company_agent
        where org_id = #{agentId}
    </select>
    <!--根据供应商code,查询供应商详情-->
    <select id="supplierList" resultMap="supplierListResultMap">
        SELECT too.org_id,
        too.org_type,
        too.org_name,
        too.org_code,
        too.is_support_early_morning_room_order,
        tocs.user_name,
        tocs.user_number,
        tocs.user_tel,
        tocs.settlement_type,
        tocs.is_cached,
        tocs.domestic_or_overseas,
        tocs.supplier_form,
        tocs.quick_processing_switch,
        tocs.hotel_id,
        tocs.hotel_name,
        tocs.group_id,
        tocs.group_name,
        tocs.is_sync_check_detail,
        tocs.invoice_id,
        ti.company_name,
        tocs.enable_protocol,
        tocs.enable_settled,
        tocs.remark,
        tocs.one_level_channel_type,
        tocs.two_level_channel_type,
        tocs.three_level_channel_type,
        tau.user_id AS purchase_manager_id,
        tau.user_name AS purchase_manager_name,
        toos.company_signature,
        toos.official_seal_url,
        toos.id official_seal_id,
        tc.contact_name businessContactName,
        tc.contact_tel businessContactTel,
        tc2.contact_name businessManager,
        tc3.contact_name operationManager,
        too.hotel_id
        FROM t_org_organization too
        INNER JOIN t_org_company_supplier tocs ON tocs.org_id = too.org_id
        LEFT JOIN t_auth_user tau
        ON tau.user_id = tocs.purchase_manager_id AND tau.org_code = tocs.company_code
        LEFT JOIN t_org_official_seal toos ON toos.id = too.official_seal_id
        left join t_invoice ti on ti.id = tocs.invoice_id
        left join t_org_contact tc on tc.org_code = too.org_code and tc.contact_type = 0 and tc.contact_role = 1 and tc.active = 1
        left join t_org_contact tc2 on tc2.org_code = too.org_code and tc2.contact_type = 1 and tc2.contact_role = 0 and tc2.active = 1
        left join t_org_contact tc3 on tc3.org_code = too.org_code and tc3.contact_type = 1 and tc3.contact_role = 1 and tc3.active = 1
        where too.org_code = #{orgCode}
        AND tocs.company_code = #{companyCode}
    </select>
    <resultMap id="supplierListResultMap" type="com.tiangong.organization.remote.dto.SupplierSelectDTO">
        <result column="org_id" property="supplierId"/>
        <result column="org_type" property="supplierType"/>
        <result column="org_name" property="supplierName"/>
        <result column="org_code" property="supplierCode"/>
        <result column="is_support_early_morning_room_order" property="isSupportEarlyMorningRoomOrder"/>
        <result column="user_name" property="adminName"/>
        <result column="user_number" property="adminAccount"/>
        <result column="user_tel" property="adminTel"/>
        <result column="settlement_type" property="settlementType"/>
        <result column="purchase_manager_id" property="purchaseManagerId"/>
        <result column="purchase_manager_name" property="purchaseManagerName"/>
        <result column="company_signature" property="companySignature"/>
        <result column="official_seal_url" property="officialSealUrl"/>
        <result column="official_seal_id" property="officialSealId"/>
        <result column="hotel_id" property="hotelId"/>
        <result column="hotel_name" property="hotelName"/>
        <result column="group_id" property="groupId"/>
        <result column="group_name" property="groupName"/>
        <result column="quick_processing_switch" property="quickProcessingSwitch"/>
        <result column="supplier_form" property="supplierForm"/>
        <result column="is_sync_check_detail" property="isSyncCheckDetail"/>
        <result column="invoice_id" property="invoiceId"/>
        <result column="channel" property="channel"/>
        <result column="enable_protocol" property="enableProtocol"/>
        <result column="enable_settled" property="enableSettled"/>
        <result column="remark" property="remark"/>
        <result column="one_level_channel_type" property="oneLevelChannelType"/>
        <result column="two_level_channel_type" property="twoLevelChannelType"/>
        <result column="three_level_channel_type" property="threeLevelChannelType"/>
        <result column="company_name" property="invoiceName"/>
        <result column="businessContactName" property="businessContactName"/>
        <result column="businessContactTel" property="businessContactTel"/>
        <result column="businessManager" property="businessManager"/>
        <result column="operationManager" property="operationManager"/>
    </resultMap>
    <select id="concactList" resultMap="concactListResultMap">
        SELECT contact_id,
        contact_name,
        contact_tel,
        contact_role,
        contact_email,
        contact_remark
        FROM t_org_contact toc
        WHERE toc.org_code = #{orgCode}
        AND active = #{active}
        AND contact_type = #{contactType}
        ORDER BY contact_id DESC
    </select>
    <resultMap id="concactListResultMap" type="com.tiangong.organization.remote.dto.ContactSupplierDTO">
        <result column="contact_id" property="contactId"/>
        <result column="contact_name" property="contactName"/>
        <result column="contact_tel" property="contactTel"/>
        <result column="contact_role" property="contactRole"/>
        <result column="contact_email" property="contactEmail"/>
        <result column="contact_remark" property="contactRemark"/>
    </resultMap>

    <select id="bankList" resultMap="bankListResultMap" parameterType="com.tiangong.organization.domain.dto.BankListDTO">
        SELECT
        tob.bank_id,
        tob.bank_name,
        tob.account_name,
        tob.account_number,
        tob.account_type,
        tob.bank_currency,
        tob.active,
        tob.bank_code
        FROM t_org_bank tob
        WHERE tob.org_code = #{orgCode}
        <if test="active != null">
            AND active = #{active}
        </if>
        <if test="accountType != null">
            AND tob.account_type = #{accountType}
        </if>
        <if test="bankName != null and bankName != '' ">
            AND tob.account_name like concat('%',#{bankName}, '%')
        </if>
        <if test="bankType != null">
            AND tob.bank_type = #{bankType}
        </if>
        ORDER BY bank_id DESC
    </select>

    <select id="bankPage" resultType="com.tiangong.organization.domain.resp.BankPageResp" parameterType="com.tiangong.organization.domain.req.BankListPageReq">
        SELECT
        tob.bank_id,
        tob.bank_name,
        tob.account_name,
        tob.account_number,
        tob.account_type,
        tob.bank_currency,
        tob.active,
        tob.created_by,
        tob.updated_by,
        IFNULL(tob.updated_dt, tob.created_dt) updatedDt,
        tob.bank_code,
        tob.bank_type,
        ti.company_name
        FROM t_org_bank tob left join t_invoice ti on tob.company_invoice_id = ti.id
        WHERE tob.org_code = #{req.orgCode}
        <if test="req.active != null">
            AND active = #{req.active}
        </if>
        <if test="req.accountType != null">
            AND tob.account_type = #{req.accountType}
        </if>
        <if test="req.accountName != null and req.accountName != '' ">
            AND tob.account_name like concat('%',#{req.accountName}, '%')
        </if>
        ORDER BY tob.bank_id DESC
    </select>

    <select id="bankListByAgentId" resultType="com.tiangong.organization.remote.dto.BankAgentListDTO" parameterType="java.lang.Integer">
        SELECT
        oab.bank_id,
        oab.agent_id,
        tob.bank_name,
        tob.account_name,
        tob.account_number,
        tob.account_type,
        tob.bank_currency,
        tob.bank_code,
        ti.company_name
        FROM t_org_agent_bank oab left join t_org_bank tob on oab.bank_id = tob.bank_id
        left join t_invoice ti on tob.company_invoice_id = ti.id
        WHERE oab.agent_id = #{agentId}
        AND tob.active = 1
        ORDER BY oab.bank_id DESC
    </select>

    <select id="getOneBank" resultType="com.tiangong.organization.remote.dto.BankSupplierDTO">
        SELECT bank_id,
        bank_name,
        account_name,
        account_number,
        balance,
        bank_code
        FROM t_org_bank tob
        WHERE tob.org_code = #{orgCode}
--         AND active = 1
        AND bank_id = #{bankId}
    </select>

    <update id="updateBankBalance" parameterType="java.lang.Object">
        update t_org_bank set balance = balance + #{amount} where bank_id = #{bankId}
    </update>

    <update id="updateBankStatus" parameterType="com.tiangong.organization.domain.req.ModifiedBankStatusReq">
        update t_org_bank set active = #{active} where bank_id = #{bankId}
    </update>

    <select id="bankDetail" resultType="com.tiangong.organization.remote.dto.BankDetailResp" parameterType="java.lang.Object">
        SELECT
            b.bank_id,
            b.bank_name,
            b.account_name,
            b.account_number,
            b.bank_code,
            b.org_code,
            b.bank_currency,
            b.invoice_type,
            b.balance,
            b.account_type,
            b.invoice_ratio,
            b.invoice_type,
            b.bank_type,
            i.id companyInvoiceId,
            i.company_name,
            i.taxpayer_identification_number,
            i.address,
            i.phone,
            i.account_bank,
            i.account_no,
            b.active
        FROM
            t_org_bank b
            LEFT JOIN t_invoice i ON b.company_invoice_id = i.id
        WHERE b.bank_id = #{bankId}
    </select>

    <select id="bankLogList" resultType="com.tiangong.organization.domain.resp.BankLogDTO" parameterType="com.tiangong.organization.domain.req.BankLogReq">
        SELECT
            obl.id,
            obl.change_content,
            obl.income,
            obl.expenditure,
            obl.balance,
            obl.change_type,
            obl.created_by,
            obl.created_dt,
            obl.change_date,
            obl.change_currency,
            ob.account_name as bankName,
            oo.org_name as changeName
        FROM
            t_org_bank_log obl
            LEFT JOIN t_org_bank ob ON obl.bank_id = ob.bank_id
            LEFT JOIN t_org_organization oo ON obl.org_code = oo.org_code
        WHERE obl.org_code = #{req.orgCode} and obl.bank_id = #{req.bankId}
        <if test="req.bankLogType != null">
            <if test="req.bankLogType == 1">
                and obl.income > 0
            </if>
            <if test="req.bankLogType == 2">
                and obl.expenditure > 0
            </if>
        </if>
        <if test="req.changeType">
            and obl.change_type = #{req.changeType}
        </if>
        <if test="req.changeBeginTime != null and req.changeBeginTime != '' and req.changeEndTime != null and req.changeEndTime != '' ">
            and obl.change_date between #{req.changeBeginTime} and #{req.changeEndTime}
        </if>
        order by obl.created_dt desc
    </select>

    <resultMap id="bankListResultMap" type="com.tiangong.organization.remote.dto.BankSupplierDTO">
        <result column="bank_id" property="bankId"/>
        <result column="bank_name" property="bankName"/>
        <result column="bank_code" property="bankCode"/>
        <result column="account_name" property="accountName"/>
        <result column="account_number" property="accountNumber"/>
        <result column="account_type" property="accountType"/>
        <result column="bank_currency" property="bankCurrency"/>
        <result column="bank_type" property="bankType" />
        <result column="active" property="active"/>
    </resultMap>
    <select id="agentList" resultType="com.tiangong.organization.remote.dto.AgentSelectDTO">
        SELECT too.org_id as agentId,
        too.org_type as agentType,
        too.org_name as agentName,
        too.org_code as agentCode,
        too.org_tel,
        too.org_unique_code,
        too.country_name,
        too.country_code,
        too.province_name,
        too.province_code,
        too.city_name,
        too.city_code,
        too.org_domian as orgDomain,
        too.org_address,
        too.established_date,
        toca.user_name,
        toca.user_number,
        toca.user_tel as agentTel,
        toca.settlement_type,
        toca.credit_line,
        toca.balance,
        toca.decimal_places,
        toca.settlement_currency,
        toca.rounding_type,
        toca.settled_type,
        toca.invoice_id,
        toca.domestic_or_overseas,
        toca.settlement_strategy,
        toca.push_settle_cost_switch,
        toca.push_settle_cost_url,
        toca.payment_overtime_cancel_switch,
        toca.payment_overtime_cancel_time,
        ti.company_name as invoiceName,
        toca.remark,
        tau.user_id AS sale_manager_id,
        tau.user_name AS adminName,
        tau.user_account AS adminAccount,
        tau.user_tel AS adminTel,
        tc.contact_name businessContactName,
        tc.contact_tel businessContactTel,
        tc2.contact_name agentManagerName,
        tc3.contact_name operationManager,
        toca.credit_warning_enabled creditWarningEnabled,
        toca.warning_amount warningAmount,
        toca.warning_emails warningEmails,
        toca.line_account_type,
        toca.line_account
        FROM t_org_company_agent toca
        INNER JOIN t_org_organization too
        ON too.org_id = toca.org_id
        LEFT JOIN t_auth_user tau
        ON tau.user_id = toca.sale_manager_id
        left join t_invoice ti on ti.id = toca.invoice_id
        left join t_org_contact tc on tc.org_code = too.org_code and tc.contact_type = 0 and tc.contact_role = 1 and tc.active = 1
        left join t_org_contact tc2 on tc2.org_code = too.org_code and tc2.contact_type = 1 and tc2.contact_role = 0 and tc2.active = 1
        left join t_org_contact tc3 on tc3.org_code = too.org_code and tc3.contact_type = 1 and tc3.contact_role = 1 and tc3.active = 1
        WHERE too.org_code = #{orgCode}
    </select>
    <resultMap id="agentListResultMap" type="com.tiangong.organization.remote.dto.AgentSelectDTO">
        <result column="org_id" property="agentId"/>
        <result column="org_type" property="agentType"/>
        <result column="org_name" property="agentName"/>
        <result column="org_code" property="agentCode"/>
        <result column="org_tel" property="adminTel"/>
        <result column="user_name" property="adminName"/>
        <result column="user_number" property="adminAccount"/>
        <result column="user_tel" property="agentTel"/>
        <result column="settlement_type" property="settlementType"/>
        <result column="credit_line" property="creditLine"/>
        <result column="sale_manager_id" property="saleManagerId"/>
        <result column="sale_manager_name" property="saleManagerName"/>
        <result column="balance" property="balance"/>
        <result column="channel_name" property="channelName"/>
        <result column="channel_code" property="channelCode"/>
    </resultMap>
    <select id="companyList" resultMap="companyList">
        SELECT too.org_id,
               too.org_name,
               too.org_code,
               too.org_domian,
               tau.user_name,
               tau.user_account,
               tau.user_tel,
               too.org_tel,
               too.org_address,
               too.org_summary,
               too.created_dt,
               too.customer_tel,
               too.org_currency,
               too.hr_is_open,
               too.hr_url,
               too.hr_system_code,
               too.hr_private_key,
               too.hr_public_key,
               too.hr_callback_url
        FROM t_org_organization too
                 INNER JOIN t_auth_user tau ON too.org_code = tau.org_code
        WHERE too.org_code = #{orgCode}
          and tau.is_super_admin = 1 limit 1
    </select>
    <resultMap id="companyList" type="com.tiangong.organization.remote.dto.CompanySelectDTO">
        <result column="org_id" property="companyId"/>
        <result column="org_name" property="companyName"/>
        <result column="org_code" property="companyCode"/>
        <result column="org_domian" property="companyDomain"/>
        <result column="user_name" property="adminName"/>
        <result column="user_account" property="adminAccount"/>
        <result column="user_tel" property="adminTel"/>
        <result column="org_tel" property="companyTel"/>
        <result column="org_currency" property="orgCurrency"/>
        <result column="org_address" property="companyAddress"/>
        <result column="org_summary" property="companySummary"/>
        <result column="hr_is_open" property="hrIsOpen"/>
        <result column="hr_url" property="hrUrl"/>
        <result column="hr_system_code" property="hrSystemCode"/>
        <result column="hr_private_key" property="hrPrivateKey"/>
        <result column="hr_public_key" property="hrPublicKey"/>
        <result column="hr_callback_url" property="hrCallbackUrl"/>
        <result column="created_dt" property="createdDt"/>
    </resultMap>
    <update id="updateAdminStatus" parameterType="com.tiangong.organization.domain.UserPO">
        UPDATE
        t_auth_user
        SET t_auth_user.available_status = #{userPO.availableStatus},
        t_auth_user.updated_by = #{userPO.updatedBy},
        t_auth_user.updated_dt = NOW()
        WHERE t_auth_user.org_code = #{userPO.orgCode}
    </update>
    <!--按条件查询供应商列表-->
    <select id="querySupplierList" resultMap="querySupplierList">
        SELECT
        too.org_id,
        too.org_type,
        too.org_name,
        too.org_code,
        tocs.available_status,
        tocs.user_tel,
        tocs.purchase_manager_id,
        tocs.domestic_or_overseas,
        tau.user_name AS purchase_manager_name,
        tc.contact_name businessContactName,
        tc.contact_tel businessContactTel,
        tc2.contact_name businessManager,
        tc3.contact_name operationManager,
        too.hotel_id,
        tocs.created_dt
        FROM t_org_organization too
        LEFT JOIN t_org_company_supplier tocs ON too.org_id = tocs.org_id
        LEFT JOIN t_auth_user tau ON tau.user_id = tocs.purchase_manager_id AND tau.org_code = tocs.company_code
        left join t_org_contact tc on tc.org_code = too.org_code and tc.contact_type = 0 and tc.contact_role like concat('%',1, '%') and tc.active = 1
        left join t_org_contact tc2 on tc2.org_code = too.org_code and tc2.contact_type = 1 and tc2.contact_role = 0 and tc2.active = 1
        left join t_org_contact tc3 on tc3.org_code = too.org_code and tc3.contact_type = 1 and tc3.contact_role = 1 and tc3.active = 1
        where type = 0 and tocs.org_id = too.org_id
        and tocs.company_code = #{request.companyCode}

        <if test="request.availableStatus != null and request.availableStatus != '' or request.availableStatus==0 ">
            AND tocs.available_status = #{request.availableStatus}
        </if>
        <if test="request.supplierCode != null and request.supplierCode != ''">
            AND too.org_code =#{request.supplierCode}
        </if>
        <if test="request.purchaseManagerId != null and request.purchaseManagerId != ''">
            AND tocs.purchase_manager_id = #{request.purchaseManagerId}
        </if>
        <if test="request.domesticOrOverseas != null and request.domesticOrOverseas != ''">
            AND tocs.domestic_or_overseas = #{request.domesticOrOverseas}
        </if>
        <if test="request.supplierCodes != null">
            AND too.org_code IN
            <foreach collection="request.supplierCodes" item="item" open="(" separator="," close=")">#{request.supplierCodes}</foreach>
        </if>
        <if test="request.businessContactName != null and request.businessContactName != ''">
            AND tc.contact_name like concat('%',#{request.businessContactName}, '%' )
        </if>
        <if test="request.businessManager != null and request.businessManager != ''">
            AND tc2.contact_name like concat('%', #{request.businessManager}, '%')
        </if>
        <if test="request.operationManager != null and request.operationManager != ''">
            AND tc3.contact_name like concat('%',  #{request.operationManager}, '%')
        </if>
        ORDER BY too.org_id DESC
    </select>
    <resultMap id="querySupplierList" type="com.tiangong.organization.remote.dto.QuerySupplierListDTO">
        <result column="org_id" property="supplierId"/>
        <result column="org_type" property="supplierType"/>
        <result column="org_name" property="supplierName"/>
        <result column="org_code" property="supplierCode"/>
        <result column="available_status" property="availableStatus"/>
        <result column="user_tel" property="adminTel"/>
        <result column="purchase_manager_id" property="purchaseManagerId"/>
        <result column="purchase_manager_name" property="purchaseManagerName"/>
        <result column="businessContactName" property="businessContactName"/>
        <result column="businessContactTel" property="businessContactTel"/>
        <result column="businessManager" property="businessManager"/>
        <result column="operationManager" property="operationManager"/>
        <result column="hotel_id" property="hotelId"/>
        <result column="created_dt" property="createdDt"/>
    </resultMap>

    <select id="queryAgentList" resultMap="queryAgentList">
        SELECT
        too.org_id,
        too.org_id id,
        too.org_type,
        too.org_name,
        too.org_code,
        too.org_tel,
        tau.user_name AS sale_manager_name,
        toca.user_tel,
        toca.balance,
        toca.credit_line,
        toca.company_code,
        toca.settlement_currency,
        toca.settlement_type,
        toca.decimal_places,
        toca.rounding_type,
        toca.domestic_or_overseas,
        too.available_status,
        too.channel_code,
        m.channel_name,
        tc.contact_name businessContactName,
        tc.contact_tel businessContactTel,
        tc2.contact_name agentManagerName,
        tc3.contact_name operationManager,
        toca.created_dt,
        toca.settlement_strategy,
        toca.push_settle_cost_switch,
        toca.push_settle_cost_url,
        ac.partner_code
        FROM t_org_company_agent toca
        INNER JOIN t_org_organization too
        ON too.org_id = toca.org_id
        LEFT JOIN t_auth_user tau
        ON tau.user_id = toca.sale_manager_id AND tau.org_code = toca.company_code
        LEFT JOIN t_pro_m_channel m ON m.channel_code = too.channel_code AND m.company_code = toca.company_code
        left join t_org_contact tc on tc.org_code = too.org_code and tc.contact_type = 0 and tc.contact_role like concat('%',1, '%') and tc.active = 1
        left join t_org_contact tc2 on tc2.org_code = too.org_code and tc2.contact_type = 1 and tc2.contact_role = 0 and tc2.active = 1
        left join t_org_contact tc3 on tc3.org_code = too.org_code and tc3.contact_type = 1 and tc3.contact_role = 1 and tc3.active = 1
        left join t_org_agent_api_config ac on ac.agent_code = too.org_code
        WHERE type = 1 and toca.company_code =#{request.companyCode}
        <if test="request.availableStatus != null and request.availableStatus != '' or request.availableStatus==0 ">
            AND too.available_status = #{request.availableStatus}
        </if>
        <if test="request.agentType != null and request.agentType != '' or request.agentType==0 ">
            AND too.org_type = #{request.agentType}
        </if>
        <if test="request.agentName != null and request.agentName != ''">
            AND too.org_name LIKE concat(concat('%',#{request.agentName}),'%')
        </if>
        <if test="request.agentCode != null and request.agentCode != ''">
            AND too.org_code LIKE concat(concat('%',#{request.agentCode}),'%')
        </if>
        <if test="request.saleManagerId != null">
            AND toca.sale_manager_id = #{request.saleManagerId}
        </if>
        <if test="request.channelCode != null and request.channelCode != ''">
            AND too.channel_code = #{request.channelCode}
        </if>
        <if test="request.businessContactName != null and request.businessContactName != ''">
            AND tc.contact_name like concat('%',#{request.businessContactName}, '%' )
        </if>
        <if test="request.agentManagerName != null and request.agentManagerName != ''">
            AND tc2.contact_name like concat('%', #{request.agentManagerName}, '%')
        </if>
        <if test="request.operationManager != null and request.operationManager != ''">
            AND tc3.contact_name like concat('%',  #{request.operationManager}, '%')
        </if>
        <if test="request.partnerCode != null and request.partnerCode != ''">
            AND ac.partner_code like concat('%',  #{request.partnerCode}, '%')
        </if>
        ORDER BY too.org_id DESC
    </select>
    <resultMap id="queryAgentList" type="com.tiangong.organization.remote.dto.QueryAgentListDTO">
        <result column="id" property="id"/>
        <result column="org_id" property="agentId"/>
        <result column="org_type" property="agentType"/>
        <result column="org_name" property="agentName"/>
        <result column="org_code" property="agentCode"/>
        <result column="org_tel" property="adminTel"/>
        <result column="user_tel" property="agentTel"/>
        <result column="sale_manager_name" property="saleManagerName"/>
        <result column="available_status" property="availableStatus"/>
        <result column="balance" property="balance"/>
        <result column="channel_code" property="channelCode"/>
        <result column="channel_name" property="channelName"/>
        <result column="settlement_currency" property="settlementCurrency"/>
        <result column="credit_line" property="creditLine"/>
        <result column="settlement_type" property="settlementType"/>
        <result column="decimal_places" property="decimalPlaces"/>
        <result column="rounding_type" property="roundingType"/>
        <result column="company_code" property="companyCode"/>
        <result column="businessContactName" property="businessContactName"/>
        <result column="businessContactTel" property="businessContactTel"/>
        <result column="agentManagerName" property="agentManagerName"/>
        <result column="operationManager" property="operationManager"/>
        <result column="domestic_or_overseas" property="domesticOrOverseas"/>
        <result column="created_dt" property="createdDt"/>
        <result column="settlement_strategy" property="settlementStrategy"/>
        <result column="push_settle_cost_switch" property="pushSettleCostSwitch"/>
        <result column="push_settle_cost_url" property="pushSettleCostUrl"/>
        <result column="partner_code" property="partnerCode"/>
    </resultMap>
    <select id="queryCompanyList" resultMap="queryCompanyList">

        SELECT
        too.org_id,
        too.org_name,
        too.org_code,
        tau.user_name,
        tau.user_tel,
        too.available_status
        FROM t_org_organization too
        INNER JOIN t_auth_user tau
        ON too.org_code = tau.org_code
        WHERE too.type = 2 and tau.is_super_admin = 1
        <if test="request.companyCode != null and request.companyCode != ''">
            AND too.org_code = #{request.companyCode}
        </if>
        ORDER BY too.org_id DESC
    </select>
    <resultMap id="queryCompanyList" type="com.tiangong.organization.remote.dto.QueryCompanyListDTO">
        <result column="org_id" property="companyId"/>
        <result column="org_name" property="companyName"/>
        <result column="org_code" property="companyCode"/>
        <result column="user_name" property="adminName"/>
        <result column="user_tel" property="adminTel"/>
        <result column="available_status" property="availableStatus"/>
    </resultMap>

    <update id="updateContactRole">
        UPDATE
        t_org_contact
        SET contact_role = #{contactRole}
        WHERE org_code = #{orgCode}
        AND contact_role = #{role}
        AND contact_type = #{contactType}
    </update>

    <update id="updateSupplierStatus" parameterType="com.tiangong.organization.domain.SupplierCompanyPO">
        UPDATE
        t_org_company_supplier
        SET available_status=#{supplierCompanyPO.availableStatus},
        updated_by=#{supplierCompanyPO.updatedBy},
        updated_dt=#{supplierCompanyPO.updatedDt}
        WHERE org_id = #{supplierCompanyPO.orgId}
        AND company_code = #{supplierCompanyPO.operatorCode}
    </update>

    <update id="updateAgentStatus" parameterType="com.tiangong.organization.domain.AgentCompanyPO">
        UPDATE
        t_org_company_agent
        SET available_status=#{agentCompanyPO.availableStatus},
        updated_by=#{agentCompanyPO.updatedBy},
        updated_dt=#{agentCompanyPO.updatedDt}
        WHERE org_id = #{agentCompanyPO.orgId}
    </update>

    <update id="updateAgentPartnerStatus" parameterType="com.tiangong.organization.domain.req.PartnerStatusReq">
        UPDATE
        t_org_agent_api_config
        SET available_status= #{availableStatus},
        updated_by= #{updatedBy},
        updated_dt= #{updatedDt}
        WHERE agent_code = #{agentCode}
        <if test="partnerCode != null and partnerCode != '' ">
           and partner_code = #{partnerCode}
        </if>
    </update>


    <select id="getDomian" resultMap="companyList">
        SELECT *
        FROM t_org_organization
        WHERE org_domian = #{orgDomian}
    </select>
    <select id="getCompanyName" resultType="java.lang.String"
            parameterType="com.tiangong.organization.remote.dto.ExamineOrgNameDTO">
        SELECT too.org_name
        FROM t_org_organization too
        WHERE org_name = #{examineOrgNameDTO.orgName}
        AND too.type = #{examineOrgNameDTO.type}
    </select>
    <select id="getSupplierName" resultType="java.lang.String"
            parameterType="com.tiangong.organization.remote.dto.ExamineOrgNameDTO">
        SELECT DISTINCT
        too.org_name
        FROM
        t_org_organization too,
        t_org_company_supplier tos
        WHERE too.org_id = tos.org_id
        AND org_name =#{examineOrgNameDTO.orgName}
        AND too.type = #{examineOrgNameDTO.type}
        AND tos.company_code =#{examineOrgNameDTO.companyCode}
        <if test="examineOrgNameDTO.orgCode != null and examineOrgNameDTO.orgCode != ''">
            AND too.org_code != #{examineOrgNameDTO.orgCode}
        </if>
    </select>
    <select id="getAgentName" resultType="java.lang.String">
        SELECT
        max(too.org_name)
        FROM
        t_org_organization too,
        t_org_company_agent toa
        WHERE org_name =#{examineOrgNameDTO.orgName}
        AND too.org_id = toa.org_id
        AND too.type = #{examineOrgNameDTO.type}
        AND toa.company_code =#{examineOrgNameDTO.companyCode}
        <if test="examineOrgNameDTO.orgCode != null and examineOrgNameDTO.orgCode != ''">
            AND too.org_code != #{examineOrgNameDTO.orgCode}
        </if>
    </select>
    <select id="getAgentCreditLine" resultType="com.tiangong.organization.remote.dto.AgentCreditLineDTO">
        SELECT agent_code agentCode,
        order_code orderCode,
        agent_credit_line_id agentCreditLineId,
        d_r_credit_line deductRefundCreditLine
        FROM t_org_agent_credit_line
        where agent_code = #{agentCode}
    </select>
    <resultMap id="getAgentCreditLine" type="com.tiangong.organization.remote.dto.AgentCreditLineDTO">
        <result column="agent_credit_line_id" property="agentCreditLineId"/>
        <result column="agent_code" property="agentCode"/>
        <result column="order_code" property="orderCode"/>
        <result column="d_r_credit_line" property="deductRefundCreditLine"/>
    </resultMap>
    <update id="updateBalance">
        UPDATE
        t_org_company_agent
        SET
        t_org_company_agent.balance = t_org_company_agent.balance + #{Balance}
        WHERE t_org_company_agent.org_id = #{agentId} and t_org_company_agent.balance + #{Balance} > 0
    </update>


    <select id="queryCompanyOfficial" resultType="com.tiangong.organization.remote.dto.SendEntityDTO">
        SELECT o.id officialSealId,
        o.official_seal_url officialSealUrl,
        o.company_signature companySignature
        FROM t_org_official_seal o
        WHERE o.active = 1
        AND o.company_code = #{companyCode}
    </select>

    <select id="judgeExistSupplierOfficial" resultType="java.lang.Integer">
        SELECT o.org_id
        FROM t_org_organization o
        WHERE o.official_seal_id = #{officialSealId}
        AND o.type = 1
    </select>

    <select id="queryOrderAgentDetail" resultType="com.tiangong.organization.domain.resp.QueryOrderAgentDetailResp" parameterType="com.tiangong.organization.domain.req.QueryOrderAgentDetailReq">
        SELECT
            too.org_code agentCode,
            too.org_name agentName,
            tc.contact_name businessContactName,
            tc.contact_tel businessContactTel,
            tc2.contact_name agentManagerName,
            tc3.contact_name operationManager,
            tca.balance,
            tca.credit_line creditLine,
            tca.remark,
            tca.line_account_type,
            tca.line_account
        FROM
            t_org_organization too
                LEFT JOIN t_org_company_agent tca ON too.org_id = tca.org_id
                LEFT JOIN t_org_contact tc ON tc.org_code = too.org_code
                AND tc.contact_type = 0
                AND tc.contact_role = 1
                and tc.active = 1
                LEFT JOIN t_org_contact tc2 ON tc2.org_code = too.org_code
                AND tc2.contact_type = 1
                AND tc2.contact_role = 0
                and tc2.active = 1
                LEFT JOIN t_org_contact tc3 ON tc3.org_code = too.org_code
                AND tc3.contact_type = 1
                AND tc3.contact_role = 1
                and tc3.active = 1
        WHERE
            too.org_code = #{agentCode}
    </select>

    <select id="queryOrderSupplierDetail" resultType="com.tiangong.organization.domain.resp.QueryOrderSupplierDetailResp" parameterType="com.tiangong.organization.domain.req.QueryOrderSupplierDetailReq">
        SELECT
            too.org_code supplierCode,
            too.org_name supplierName,
            tc.contact_name businessContactName,
            tc.contact_tel businessContactTel,
            tc2.contact_name businessManager,
            tc3.contact_name operationManager,
            tca.remark
        FROM
            t_org_organization too
                LEFT JOIN t_org_company_supplier tca ON too.org_id = tca.org_id
                LEFT JOIN t_org_contact tc ON tc.org_code = too.org_code
                AND tc.contact_type = 0
                AND tc.contact_role = 1
                and tc.active = 1
                LEFT JOIN t_org_contact tc2 ON tc2.org_code = too.org_code
                AND tc2.contact_type = 1
                AND tc2.contact_role = 0
                and tc2.active = 1
                LEFT JOIN t_org_contact tc3 ON tc3.org_code = too.org_code
                AND tc3.contact_type = 1
                AND tc3.contact_role = 1
                and tc3.active = 1
        WHERE
            too.org_code = #{supplierCode}
    </select>

    <!-- 根据组织编码集合批量查询组织信息 -->
    <select id="selectByOrgCodes" resultType="com.tiangong.organization.domain.dto.OrgBaseDTO">
        SELECT
            too.org_id,
            too.org_name,
            too.org_code,
            too.available_status,
            ac.partner_code
        FROM t_org_organization too
        left join t_org_agent_api_config ac on ac.agent_code = too.org_code
        WHERE too.type=1 and too.org_code IN
        <foreach collection="orgCodes" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>
    <select id="selectSupplierBaseInfo" resultType="com.tiangong.organization.remote.dto.SupplierBaseInfoResp">
        SELECT
        too.org_code,
        too.org_name,
        tocs.available_status
        FROM t_org_organization too
        LEFT JOIN t_org_company_supplier tocs ON too.org_id = tocs.org_id
        WHERE type=0 and too.org_code IN
        <foreach collection="companyCodes" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>
</mapper>