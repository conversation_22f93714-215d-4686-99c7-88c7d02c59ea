<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tiangong.organization.mapper.SupplierLabelConfigMapper">

    <select id="querySupplierLabelConfigPage" resultType="com.tiangong.organization.domain.resp.SupplierLabelConfigResp"
            parameterType="com.tiangong.organization.domain.req.QuerySupplierLabelConfigReq">
        SELECT
            oslc.id,
            oslc.label_type,
            oslc.supplier_id,
            oslc.agent_id,
            ocs.domestic_or_overseas,
            too.org_name supplierName,
            too.org_code supplierCode,
            too.org_type as supplierType
        FROM
            t_org_supplier_label_config oslc
            LEFT JOIN t_org_company_supplier ocs ON oslc.supplier_id = ocs.s_company_id
            LEFT JOIN t_org_organization too ON ocs.org_id = too.org_id
        WHERE
            oslc.agent_id = #{req.agentId}
        <if test="req.serviceType != null">
            AND oslc.service_type = #{req.serviceType}
        </if>
        <if test="req.labelType != null">
            AND oslc.label_type = #{req.labelType}
        </if>
        <if test="req.supplierCode != null and req.supplierCode != '' ">
            AND too.org_code = #{req.supplierCode}
        </if>
        <if test="req.supplierName != null and req.supplierName != '' ">
            AND too.org_name like concat('%' #{req.supplierName}, '%')
        </if>
        ORDER BY oslc.created_dt desc
    </select>

    <select id="querySupplierLabelConfigList" resultType="com.tiangong.organization.domain.dto.SupplierLabelDTO">
        SELECT
            oslc.label_type labelType,
            too.org_code supplierCode,
            toa.org_code agentCode

        FROM
            t_org_supplier_label_config oslc
                LEFT JOIN t_org_company_supplier ocs ON oslc.supplier_id = ocs.s_company_id
                LEFT JOIN t_org_organization too ON ocs.org_id = too.org_id
                LEFT JOIN t_org_company_agent oca ON oslc.agent_id = oca.a_company_id
                LEFT JOIN t_org_organization toa ON oca.org_id = toa.org_id
    </select>

</mapper>