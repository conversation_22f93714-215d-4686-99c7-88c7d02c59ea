<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tiangong.organization.mapper.PictureMapper">

    <!--根据企业编码查询企业营业执照 -->
    <select id="queryPictureLicense" resultMap="queryPictureLicense">
        SELECT
        picture_url,picture_id
        from t_org_picture
        WHERE t_org_picture.company_code = #{companyCode}
        AND t_org_picture.picture_type = #{pictureType}
        AND t_org_picture.active = 1
        ORDER BY picture_id desc
    </select>
    <resultMap id="queryPictureLicense" type="com.tiangong.organization.remote.dto.PictureLicenseDTO">
        <result column="picture_id" property="pictureId"/>
        <result column="picture_url" property="pictureUrl"/>
    </resultMap>

    <select id="PictureLicense" resultMap="PictureLicense">
        SELECT
        picture_id,
        picture_url
        from t_org_picture
        WHERE t_org_picture.company_code = #{companyCode}
        AND t_org_picture.picture_type = #{pictureType}
        AND t_org_picture.active = 1
        ORDER BY picture_id desc
    </select>

    <resultMap id="PictureLicense" type="com.tiangong.organization.remote.dto.CompanyBusinessLicenseUrlDTO">
        <id column="picture_id" property="companyBusinessLicenseId"/>
        <result column="picture_url" property="url"/>
    </resultMap>
</mapper>