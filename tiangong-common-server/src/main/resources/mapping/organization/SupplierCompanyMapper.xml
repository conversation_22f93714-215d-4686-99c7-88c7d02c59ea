<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tiangong.organization.mapper.SupplierCompanyMapper">

    <select id="querySupplier" resultType="com.tiangong.organization.domain.OrgSupplierPO">
        select
        o.org_id orgId,
        o.org_code supplierCode,
        o.org_name supplierName,
        IFNULL(o.is_support_early_morning_room_order, 0) isSupportEarlyMorningRoomOrder,
        o.org_type as supplierType,
        t.purchase_manager_id purchaseManagerId,
        t.settlement_type settlementType,
        t.domestic_or_overseas,
        t.invoice_model,
        t.invoice_type,
        t.decimal_places,
        t.modified_amt,
        t.adjustment_type,
        t.company_code,
        t.settlement_currency,
        t.available_status availableStatus,
        t.is_sync_check_detail,
        t.quick_processing_switch,
        t.supplier_form,
        t.hotel_id,
        t.is_cached,
        t.one_level_channel_type,
        t.two_level_channel_type,
        t.three_level_channel_type
        from t_org_company_supplier t
        left join t_org_organization o ON t.org_id = o.org_id
        where o.available_status = 1
    </select>

    <update id="updateByOrgIdToAdjustment"
            parameterType="com.tiangong.organization.domain.req.SupplierAdjustmentUpdateReq">
        update t_org_company_supplier
        <set>
            <if test="adjustmentType != null">
                adjustment_type = #{adjustmentType},
            </if>
            <if test="modifiedAmt != null">
                modified_amt = #{modifiedAmt},
            </if>
            <if test="modifiedAmt != null">
                lowest_increase_amt = #{lowestIncreaseAmt},
            </if>
        </set>
        where org_id = #{supplierId}
    </update>

    <update id="updateByOrgIdToInvoice"
            parameterType="com.tiangong.organization.domain.req.SupplierAdjustmentUpdateReq">
        update t_org_company_supplier
        <set>
            <if test="invoiceType != null">
                invoice_type = #{invoiceType},
            </if>
            <if test="invoiceModel != null">
                invoice_model = #{invoiceModel},
            </if>
        </set>
        where org_id = #{supplierId}
    </update>


    <select id="queryAdjustAndInvoice" parameterType="java.lang.String"
            resultType="com.tiangong.organization.domain.dto.UpdateSupplierAdjustmentDTO">
        SELECT
        cs.org_id as supplierId,
        oo.org_code as supplierCode,
        IFNULL(cs.adjustment_type, 2) as adjustmentType,
        IFNULL(cs.modified_amt, 0) as modifiedAmt,
        IFNULL(cs.decimal_places, 2) as decimalPlaces,
        IFNULL(cs.invoice_type, 1) as invoiceType,
        IFNULL(cs.invoice_model, 1) as invoiceModel
        FROM
        t_org_company_supplier cs
        LEFT JOIN t_org_organization oo ON oo.org_id = cs.org_id
        WHERE
        oo.type = 0 AND cs.available_status = 1
    </select>

    <select id="selectSupplierCompanyList" resultType="com.tiangong.organization.domain.resp.SupplierCompanyResp">
        select
            t.org_code,
            t1.s_company_id,
            t1.domestic_or_overseas,
            t1.available_status
        from t_org_organization t
        left join t_org_company_supplier t1 on t1.org_id = t.org_id
        where t.org_code in
        <foreach collection="supplierCodeList" open="(" close=")" separator="," item="item" index="index">
            #{item}
        </foreach>
    </select>
</mapper>