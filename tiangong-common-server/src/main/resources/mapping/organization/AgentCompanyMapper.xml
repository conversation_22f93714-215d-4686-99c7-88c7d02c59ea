<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.tiangong.organization.mapper.AgentCompanyMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap id="BaseResultMap" type="com.tiangong.organization.domain.AgentCompanyPO">
        <result property="aCompanyId" column="a_company_id"/>
        <result property="orgId" column="org_id"/>
        <result property="userName" column="user_name"/>
        <result property="userNumber" column="user_number"/>
        <result property="userTel" column="user_tel"/>
        <result property="settlementType" column="settlement_type"/>
        <result property="settlementCurrency" column="settlement_currency"/>
        <result property="creditLine" column="credit_line"/>
        <result property="balance" column="balance"/>
        <result property="saleManagerId" column="sale_manager_id"/>
        <result property="companyCode" column="company_code"/>
        <result property="availableStatus" column="available_status"/>
        <result property="decimalPlaces" column="decimal_places"/>
        <result property="roundingType" column="rounding_type"/>
        <result property="invoiceId" column="invoice_id"/>
        <result property="settledType" column="settled_type"/>
        <result property="remark" column="remark"/>
        <result property="domesticOrOverseas" column="domestic_or_overseas"/>
        <result property="settlementStrategy" column="settlement_strategy"/>
        <result property="pushSettleCostSwitch" column="push_settle_cost_switch"/>
        <result property="pushSettleCostUrl" column="push_settle_cost_url"/>
        <result property="creditWarningEnabled" column="credit_warning_enabled"/>
        <result property="warningAmount" column="warning_amount"/>
        <result property="warningEmails" column="warning_emails"/>
    </resultMap>

    <select id="selectPushSettleCostSwitchAgentCodeList" resultType="java.lang.String">
        select
            t2.org_code
        from t_org_company_agent t1
        left join t_org_organization t2 on t1.org_id = t2.org_id
        where t1.available_status = 1 and t1.settlement_strategy = 1 and t1.push_settle_cost_url is not null
    </select>

    <select id="selectAgentCompanyList" resultType="com.tiangong.organization.domain.resp.AgentCompanyResp">
        select
            t.org_code,
            t1.a_company_id,
            t1.domestic_or_overseas,
            t1.available_status
        from t_org_organization t
        left join t_org_company_agent t1 on t1.org_id = t.org_id
        where t.org_code in
        <foreach collection="agentCodeList" open="(" close=")" separator="," item="item" index="index">
            #{item}
        </foreach>
    </select>
    <select id="selectWarningConfigList" resultType="com.tiangong.organization.remote.dto.AgentCreditWarningConfig">
        select
        t.org_code agentCode,
        t.org_name agentName,
        t1.credit_warning_enabled,
        t1.warning_amount,
        t1.warning_emails,
        t1.balance
        from t_org_organization t
        left join t_org_company_agent t1 on t1.org_id = t.org_id
        where t.org_code in
        <foreach collection="agentCodeList" open="(" close=")" separator="," item="item" index="index">
            #{item}
        </foreach>
    </select>
</mapper>