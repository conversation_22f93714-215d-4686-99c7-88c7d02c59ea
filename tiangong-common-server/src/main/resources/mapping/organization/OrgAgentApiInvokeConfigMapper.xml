<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.tiangong.organization.mapper.OrgAgentApiInvokeConfigMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap id="BaseResultMap" type="com.tiangong.organization.domain.entity.OrgAgentApiInvokeConfigEntity">
        <result property="id" column="id"/>
        <result property="partnerCode" column="agent_code"/>
        <result property="methodName" column="method_name"/>
        <result property="methodCode" column="method_code"/>
        <result property="invokeTimes" column="invoke_times"/>
        <result property="createdBy" column="created_by"/>
        <result property="createdDt" column="created_dt"/>
        <result property="updatedBy" column="updated_by"/>
        <result property="updatedDt" column="updated_dt"/>
        <result property="deleted" column="deleted"/>
    </resultMap>

    <select id="queryInvokeByAgentCode" resultType="com.tiangong.organization.remote.dto.OrgAgentApiInvokeConfigResp">
        SELECT b.id id,
        #{partnerCode} partnerCode,
        a.method_code methodCode,
        a.method_name methodName,
        ifnull(b.invoke_times, a.invoke_times) invokeTimes
        FROM t_org_agent_api_invoke_config a
        LEFT JOIN t_org_agent_api_invoke_config b ON a.method_code = b.method_code
        AND b.partner_code = #{partnerCode}
        WHERE a.partner_code IS NULL
    </select>

    <select id="queryInitInvoke" resultType="com.tiangong.organization.remote.dto.OrgAgentApiInvokeConfigResp">
        SELECT a.id id,
        a.partner_code agentCode,
        a.method_code methodCode,
        a.method_name methodName,
        a.invoke_times invokeTimes
        FROM t_org_agent_api_invoke_config a
        where a.partner_code is null
        and a.deleted = 0;
    </select>


</mapper>