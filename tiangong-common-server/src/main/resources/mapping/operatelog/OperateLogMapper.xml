<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.tiangong.operatelog.mapper.OperateLogMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap id="BaseResultMap" type="com.tiangong.operatelog.domain.OperateLogEntity">
        <result property="logId" column="log_id"/>
        <result property="logName" column="log_name"/>
        <result property="logType" column="log_type"/>
        <result property="applicationName" column="application_name"/>
        <result property="requestBrowser" column="request_browser"/>
        <result property="requestMethod" column="request_method"/>
        <result property="requestIp" column="request_ip"/>
        <result property="requestUrl" column="request_url"/>
        <result property="requestHeader" column="request_header"/>
        <result property="requestParam" column="request_param"/>
        <result property="errorMsg" column="error_msg"/>
        <result property="elapsedTime" column="elapsed_time"/>
        <result property="createdDt" column="created_dt"/>
        <result property="createdBy" column="created_by"/>
        <result property="logLevel" column="log_level"/>
        <result property="operationType" column="operation_type"/>
        <result property="operationResult" column="operation_result"/>
        <result property="userAccount" column="user_account"/>
    </resultMap>

    <select id="queryListPage" parameterType="com.tiangong.operatelog.domain.QueryOperateLogDTO"
            resultType="com.tiangong.operatelog.domain.OperateLogSearchDTO">
        SELECT created_dt createdDt,
        created_by createdBy,
        log_level logLevel,
        log_name logName,
        request_param requestParam,
        operation_result operationResult,
        request_ip requestIp,
        operation_type operationType
        FROM s_log_operate_log
        WHERE
        <choose>
            <when test="operateResult != null and operateResult != ''">
                operation_result = #{operateResult}
            </when>
            <otherwise>
                operation_result in(0,1)
            </otherwise>
        </choose>
        <if test="operateStartDate != null and operateStartDate != '' and operateEndDate != null and operateEndDate != ''">
            AND created_dt &gt;= #{operateStartDate}
            AND created_dt &lt;= #{operateEndDate}
        </if>
        <if test="logType != null and logType != ''">
            AND log_level = #{logType}
        </if>
        <if test="operationType != null and operationType != ''">
            AND operation_type = #{operationType}
        </if>
        <if test="operateAccount != null and operateAccount != ''">
            AND created_by like concat(#{operateAccount}, '%')
        </if>
        ORDER BY created_dt DESC
        LIMIT #{currentPage}, #{pageSize};
    </select>

</mapper>