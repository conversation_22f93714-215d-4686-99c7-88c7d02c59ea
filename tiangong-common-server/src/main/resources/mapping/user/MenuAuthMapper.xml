<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
        
<mapper namespace="com.tiangong.user.mapper.MenuAuthMapper">

    <select id="max" resultType="long">
        SELECT max(menu_seq)
        FROM t_auth_menu2
    </select>

    <select id="getMenuByRoleId" resultType="com.tiangong.user.domain.AuthMenu2">
        SELECT
            t1.menu_id,
            t1.menu_name,
            t1.parent_item_id,
            t1.parent_item_ids,
            t1.permissions,
            t1.path,
            t1.type,
            t1.path,
            t1.hidden,
            t1.icon,
            t1.sort
        FROM t_auth_menu2 t1
        LEFT JOIN t_auth_role_menu t2 ON t1.menu_id = t2.menu_id
        WHERE t2.role_id IN
        <foreach collection="roleIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        ORDER BY t1.sort ASC
    </select>

    <select id="selectList" resultType="com.tiangong.user.domain.AuthMenu2">
        SELECT
            menu_id,
            menu_name,
            parent_item_id,
            parent_item_ids,
            permissions,
            path,
            type,
            path,
            hidden,
            icon,
            sort
        FROM t_auth_menu2
        WHERE deleted = 0 AND type IN
        <foreach collection="typeList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        ORDER BY sort ASC
    </select>

    <select id="selectChildrenMenuList" resultType="string">
        SELECT menu_id
        FROM t_auth_menu2
        WHERE type in (1,2) AND concat(parent_item_ids, ',') like concat('%', #{menuId}, ',%')
    </select>

    <select id="selectListInId" resultType="com.tiangong.user.domain.AuthMenu2">
        SELECT t1.menu_id,
               t1.menu_name,
               t1.parent_item_id,
               t1.parent_item_ids,
               t1.permissions,
               t1.path,
               t1.type,
               t1.path,
               t1.hidden,
               t1.icon,
               t1.sort
        FROM t_auth_menu2 t1
        WHERE t1.menu_id in
        <foreach collection="pidSet" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectListByButton" resultType="java.lang.String">
        SELECT permissions
        FROM t_auth_menu2
        WHERE type = #{type}
    </select>

</mapper>