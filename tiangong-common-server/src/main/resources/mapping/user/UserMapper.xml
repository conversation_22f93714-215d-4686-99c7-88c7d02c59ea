<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tiangong.user.mapper.UserMapper">

    <resultMap id="ExistAdminUser" type="com.tiangong.organization.domain.UserPO">
        <id column="user_id" property="userId"/>
    </resultMap>

    <resultMap id="PurchaseManager" type="com.tiangong.user.dto.PurchaseManagerDTO">
        <id column="user_id" property="purchaseManagerId"/>
        <result column="user_name" property="purchaseManagerName"/>
    </resultMap>

    <resultMap id="SaleManager" type="com.tiangong.user.dto.SaleManagerDTO">
        <id column="user_id" property="saleManagerId"/>
        <result column="user_name" property="saleManagerName"/>
    </resultMap>

    <resultMap id="RoleAndDomain" type="com.tiangong.user.dto.RoleAndDomainDTO">
        <result column="org_domian" property="domainName"/>
        <collection property="employeeRoleList" ofType="com.tiangong.user.dto.EmployeeRoleDTO">
            <id column="role_id" property="employeeRoleId"/>
            <result column="role_name" property="employeeRoleName"/>
            <result column="role_description" property="employeeRoleDescription"/>
        </collection>
    </resultMap>

    <select id="queryExistAdminUser" resultMap="ExistAdminUser"
            parameterType="com.tiangong.organization.domain.UserPO">
        SELECT
        t.user_id
        FROM t_auth_user t
        WHERE
        t.active = 1
        AND ((t.is_super_admin = #{isSuperAdmin} AND t.org_code = #{orgCode})
        OR t.user_account = #{userAccount})
    </select>

    <select id="queryPurchaseManagerList" resultMap="PurchaseManager"
            parameterType="java.util.Map">
        SELECT
        u.user_id,
        u.user_name
        FROM t_auth_role t
        LEFT JOIN t_auth_user_role ur ON t.role_id = ur.role_id
        LEFT JOIN t_auth_user u ON ur.user_id = u.user_id
        WHERE t.role_name ='采购经理' AND u.active = 1
        AND u.org_code = #{orgCode}
        <if test="purchaseManagerName != null and purchaseManagerName != ''">
            AND u.user_name LIKE "%"#{purchaseManagerName}"%"
        </if>
    </select>

    <select id="getSaleManagerList" resultMap="SaleManager"
            parameterType="java.util.Map">
        SELECT
        u.user_id,
        u.user_name
        FROM t_auth_role t
        LEFT JOIN t_auth_user_role ur ON t.role_id = ur.role_id
        LEFT JOIN t_auth_user u ON ur.user_id = u.user_id
        WHERE t.role_name ='销售经理' AND u.active = 1
        AND u.org_code = #{orgCode}
        <if test="saleManagerName != null and saleManagerName != ''">
            AND u.user_name LIKE "%"#{saleManagerName}"%"
        </if>
    </select>

    <select id="getRoleAndDomain" resultMap="RoleAndDomain"
            parameterType="java.util.Map">
        SELECT
        t.role_id,
        t.role_name,
        t.role_description,
        too.org_domian
        FROM t_auth_role t, t_org_organization too
        WHERE t.active = 1 AND t.type = #{endType}
        AND too.org_code = #{orgCode}
    </select>

    <select id="getEmployeeList" resultType="com.tiangong.user.dto.EmployeeUserDTO"
            parameterType="java.util.Map">
        SELECT
        u.user_id employeeId,
        u.user_name employeeName,
        u.user_account employeeAccount,
        u.user_tel employeeTel,
        u.user_account_type employeeAccountType,
        u.account_avl_start_time accountAvlStartTime,
        u.account_avl_end_time accountAvlEndTime,
        u.visit_start_time visitStartTime,
        u.visit_end_time visitEndTime,
        u.visit_ip visitIp,
        IF(u.unlock_time > now() and u.account_status = 0, 3, u.account_status) accountStatus,
        u.unlock_time unlockTime,
        if(u.user_account = #{user} OR u.is_super_admin = 1 OR u.available_status = 0,0,1) availableStatus,
        if(u.user_account = #{user} OR u.is_super_admin = 1,0,1) deletableStatus,
        if(ISNULL(u.is_super_admin),0 ,u.is_super_admin) isSuperAdmin,
        u.active,
        u.approval_status approvalStatus
        FROM t_auth_user u
        WHERE u.org_code = #{orgCode}
        <if test="employeeName != null and employeeName != ''">
            AND u.user_name LIKE CONCAT('%',#{employeeName},'%')
        </if>
        <if test="employeeAccountType != null and employeeAccountType != ''">
            AND u.user_account_type = #{employeeAccountType}
        </if>
        <if test="approvalStatus != null and approvalStatus != ''">
            AND u.approval_status = #{approvalStatus}
        </if>
        <if test="active != null and active != ''">
            AND u.active = #{active}
        </if>
        <choose>
            <when test="accountStatus == 3">
                AND u.unlock_time > now()
            </when>
            <otherwise>
                <if test="accountStatus != null and accountStatus != ''">
                    AND u.account_status = #{accountStatus}
                </if>
            </otherwise>
        </choose>
        ORDER BY isSuperAdmin DESC, u.user_id DESC
    </select>

    <select id="getRoleList" resultType="com.tiangong.user.dto.EmployeeRoleDTO">
        SELECT
        ur.id userRoleId,
        r.role_id employeeRoleId,
        r.role_name employeeRoleName,
        ur.user_id employeeId
        FROM t_auth_user_role ur
        LEFT JOIN t_auth_role r ON ur.role_id = r.role_id
        WHERE ur.user_id IN
        <foreach collection="list" item="item" index="index" separator="," open="(" close=")">
            #{item.employeeId}
        </foreach>
    </select>

    <select id="getRoleListByUserId" resultType="com.tiangong.user.dto.EmployeeRoleDTO">
        SELECT
        ur.id userRoleId,
        ur.role_id employeeRoleId,
        r.role_name employeeRoleName,
        ur.user_id employeeId
        FROM t_auth_user_role ur
        LEFT JOIN t_auth_role r ON r.role_id = ur.role_id
        WHERE ur.user_id in
        <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
            #{item.userId}
        </foreach>
    </select>

    <select id="getRoleByUserId" resultType="java.lang.String">
        SELECT
            r.role_name
        FROM t_auth_user_role ur
        LEFT JOIN t_auth_role r ON r.role_id = ur.role_id
        WHERE ur.user_id = #{userId}
        LIMIT 1
    </select>

    <select id="deleteUserRole">
        DELETE
        FROM t_auth_user_role
        WHERE id IN
        <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
            #{item.id}
        </foreach>
    </select>

    <select id="getUserWithoutAdmin" resultType="com.tiangong.organization.domain.UserPO">
        SELECT
        t.user_id userId,
        t.user_name userName,
        t.user_account userAccount,
        t.org_code orgCode
        FROM t_auth_user t
        WHERE ISNULL(t.is_super_admin)
        AND t.user_id = #{userId}
    </select>

    <select id="queryEmployeeDetail" resultType="com.tiangong.user.dto.EmployeeUserDTO">
        SELECT
        u.user_id employeeId,
        u.user_name employeeName,
        u.user_account employeeAccount,
        u.user_tel employeeTel,
        u.user_id_number employeeIDNumber,
        u.user_account_type employeeAccountType,
        u.account_avl_start_time accountAvlStartTime,
        u.account_avl_end_time accountAvlEndTime,
        u.visit_start_time visitStartTime,
        u.visit_end_time visitEndTime,
        u.visit_ip visitIp,
        u.is_note_permission isNotePermission,
        if(u.user_account = #{user} OR u.is_super_admin = 1,0,1) resetable,
        if(u.user_account = #{user} OR u.is_super_admin = 1,0,1) modifiable,
        u.active,
        u.approval_status approvalStatus
        FROM t_auth_user u
        WHERE u.user_id = #{employeeId}
    </select>

    <select id="queryMenuListByLoginName" resultType="com.tiangong.user.dto.MenuDTO" parameterType="java.util.Map">
        SELECT DISTINCT
        m.id menuId,
        m.menu_code menuCode,
        m.menu_name menuName,
        m.menu_level menuLevel,
        m.menu_rank menuRank,
        m.parent_code parentCode,
        m.front_end_url frontEndUrl,
        m.back_end_url backEndUrl,
        m.open_type openType
        FROM
        t_auth_user u,
        t_auth_user_role ur,
        t_auth_role r,
        t_auth_role_menu rm,
        t_auth_menu m
        WHERE
        m.id = rm.menu_id
        AND rm.role_id = r.role_id
        AND u.user_id = ur.user_id
        AND ur.role_id = r.role_id
        AND u.active = 1
        AND m.active = 1
        AND r.active = 1
        <if test="userAccount != null and userAccount != ''">
            AND u.user_account = #{userAccount}
        </if>
    </select>

    <select id="queryLoginUser" parameterType="java.util.Map" resultType="com.tiangong.user.dto.UserDTO">
        SELECT
        u.user_id loginId,
        u.user_account loginAccount,
        u.user_name loginName,
        u.user_pwd loginPwd,
        u.signature_pwd signaturePwd,
        u.salt salt,
        o.org_name companyName,
        o.org_code companyCode,
        o.org_domian companyDomain,
        o.hotel_info_permissions hotelPermissions,
        u.available_status availableStatus,
        u.account_status accountStatus,
        u.update_pwd_time updatePwdTime,
        u.visit_start_time visitStartTime,
        u.visit_end_time visitEndTime,
        u.unlock_time unlockTime,
        u.visit_ip visitIp,
        u.is_super_admin isSuperAdmin,
        u.is_note_permission isNotePermission,
        o.org_type orgType,
        u.approval_status approvalStatus,
        u.active active
        FROM
        t_auth_user u
        LEFT JOIN t_org_organization o ON o.org_code = u.org_code
        WHERE o.type = 2
        AND o.is_share_supplier = 0
        <if test="userAccount != null and userAccount != ''">
            AND u.user_account = #{userAccount}
        </if>
        <if test="userId != null and userId != ''">
            AND u.user_id = #{userId}
        </if>

    </select>

    <select id="queryRoleList" resultType="com.tiangong.user.dto.RoleDTO">
        select
        h.role_id roleId,
        h.role_code roleCode,
        h.role_name roleName,
        h.role_description roleDescription,
        count(u.user_id) roleAccountNum
        from t_auth_role h
        left join t_auth_user_role ho on h.role_id = ho.role_id
        left join t_auth_user u on u.user_id = ho.user_id and u.active = 1
        where h.active = 1
        group by h.role_id,h.role_code,h.role_name,h.role_description
        order by h.role_id
    </select>

    <select id="queryMenuListByRoleId" resultType="com.tiangong.user.dto.MenuDTO" parameterType="java.util.Map">
        SELECT DISTINCT
        m.id menuId,
        m.menu_code menuCode,
        m.menu_name menuName,
        m.menu_level menuLevel,
        m.menu_rank menuRank,
        m.parent_code parentCode,
        m.front_end_url frontEndUrl,
        m.back_end_url backEndUrl,
        m.open_type openType
        FROM
        t_auth_role_menu rm,
        t_auth_menu m
        WHERE
        m.id = rm.menu_id
        AND rm.role_id = #{roleId}
        AND m.active = 1
    </select>

    <select id="queryRoleUserCount" resultType="java.lang.Integer" parameterType="java.util.Map">
        select count(*) from t_auth_user h ,t_auth_user_role hh
        where h.user_id = hh.user_id
        and h.active = 1
        and hh.role_id = #{roleId}
    </select>

    <select id="queryUserCount" resultType="int" parameterType="com.tiangong.organization.domain.UserPO">
        SELECT COUNT( 1 )
        FROM t_auth_user
        WHERE account_status <![CDATA[ <> ]]> 5
        AND (
        <choose>
            <when test="userTel != null and userTel != '' and userIDNumber != null and userIDNumber != ''">
                user_tel = #{userTel} OR user_id_number = #{userIDNumber}
            </when>
            <when test="userTel != null and userTel != ''">
                user_tel = #{userTel}
            </when>
            <otherwise>
                <if test="userIDNumber != null and userIDNumber != ''">
                    user_id_number = #{userIDNumber}
                </if>
            </otherwise>
        </choose>
        )
        <if test="userId != null">
            AND user_id <![CDATA[ <> ]]>  #{userId}
        </if>
        <if test="orgCode != null">
            AND org_code =  #{orgCode}
        </if>
    </select>

    <update id="updateAccountStatus" parameterType="java.util.Map">
        UPDATE t_auth_user
        SET last_login_time = ( CASE WHEN account_status = 1 THEN NOW() ELSE last_login_time END ),
        account_status = #{accountStatus},
        unlock_time = #{unlockTime}
        WHERE user_account = #{employeeAccount}
        AND active = 1
    </update>

    <select id="queryUserPwd" resultType="com.tiangong.user.dto.UserDTO" parameterType="java.lang.String">
        SELECT
        u.user_id loginId,
        u.user_account loginAccount,
        u.user_name loginName,
        u.user_pwd loginPwd
        FROM
        t_auth_user
        WHERE u.user_account = #{userAccount} AND u.active = 1
    </select>

    <select id="queryLogoutAccount" resultType="com.tiangong.user.dto.EmployeeUserDTO">
        SELECT user_account employeeAccount,
        org_code orgCode
        FROM t_auth_user
        WHERE user_account_type = 1 AND DATE_ADD(last_login_time, INTERVAL 1 YEAR) &lt;= NOW()
        AND active = 1
        AND ISNULL(is_super_admin)
    </select>

    <update id="autoLogoutAccount">
        UPDATE t_auth_user
        SET active = 0, account_status = 2
        WHERE user_account_type = 1 AND DATE_ADD(last_login_time, INTERVAL 1 YEAR) &lt;= NOW()
        AND active = 1
        AND ISNULL(is_super_admin)
    </update>

    <update id="autoDormantAccount">
        UPDATE t_auth_user
        SET account_status = 1
        WHERE ((user_account_type = 1 AND account_avl_end_time &lt; DATE(NOW()))
        OR (DATE_ADD(last_login_time, INTERVAL 3 MONTH) &lt; NOW()))
        AND active = 1
        AND account_status = 0
        AND ISNULL(is_super_admin)
    </update>

    <update id="autoSleepAccount">
        UPDATE t_auth_user
        SET account_status = 1
        WHERE user_account_type = 1 AND account_avl_end_time &lt;= DATE(NOW())
        AND ISNULL(is_super_admin)
    </update>

    <select id="queryUserAccount" resultType="int">
        SELECT COUNT(1)
        FROM t_auth_user
        WHERE account_status <![CDATA[ <> ]]> 5
        AND user_account = #{userAccount}
    </select>

    <select id="examineUserAccountOrUserTel" resultType="int" parameterType="com.tiangong.user.domain.ExamineUserDTO">
        SELECT COUNT(1)
        FROM t_auth_user
        WHERE account_status <![CDATA[ <> ]]> 5
        <if test="userAccount != null and userAccount != '' ">
            AND user_account = #{userAccount}
        </if>
        <if test="userTel != null and userTel != '' ">
            AND user_tel = #{userTel}
        </if>
        <if test="orgCode != null and orgCode != '' ">
            AND org_code = #{orgCode}
        </if>
        <if test="userId != null">
            AND user_id != #{userId}
        </if>
    </select>

    <select id="queryUserAccountAndUserTel" resultType="int">
        SELECT COUNT(1)
        FROM t_auth_user
        WHERE account_status <![CDATA[ <> ]]> 5
        AND user_account = #{userAccount} or user_tel = #{userTel}
        <if test="agentCode != null and agentCode != '' ">
            AND org_code != #{agentCode}
        </if>
    </select>

    <select id="queryOrgCodeByAccount" resultType="String">
        SELECT org_code
        FROM t_auth_user
        WHERE account_status = 0
        and available_status = 1
        and active = 1
        AND user_account = #{userAccount}
    </select>

    <update id="resetAccountPassword" parameterType="com.tiangong.organization.domain.dto.UpdateAccountPwdDTO">
        update t_auth_user set user_pwd = #{userPwd}, signature_pwd = #{signaturePwd} where org_code = #{orgCode} and is_super_admin = 1
    </update>

    <select id="queryUserMenuList" parameterType="java.lang.Integer" resultType="com.tiangong.user.domain.resp.MenuDTO">
        SELECT
            am.id,
            am.menu_name,
            am.menu_level,
            am.menu_code,
            am.parent_code,
            am.front_end_url as path
        FROM
            t_auth_user_role atr
                LEFT JOIN t_auth_role_menu arm ON atr.role_id = arm.role_id
                LEFT JOIN t_auth_menu am ON arm.menu_id = am.id
        WHERE
            atr.user_id = #{userId} and am.active = 1
        ORDER BY
         am.menu_level asc , am.menu_rank asc
    </select>

    <select id="queryAllMenuList" resultType="com.tiangong.user.domain.MenuPO" >
        select * from t_auth_menu where active = 1 order by menu_level asc , menu_rank asc
    </select>
</mapper>