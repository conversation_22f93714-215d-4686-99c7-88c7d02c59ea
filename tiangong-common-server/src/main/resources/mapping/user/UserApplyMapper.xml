<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tiangong.user.mapper.UserApplyMapper">

    <select id="queryUserApplyList" resultType="com.tiangong.user.dto.UserApplyDTO">
        SELECT
        id applyId,
        user_id userId,
        operation_type operationType,
        created_by operationUser,
        created_dt operationTime,
        operation_content operationContent,
        refused_reason refusedReason,
        approval_status approvalStatus
        FROM t_auth_user_apply
        WHERE 1=1
        <if test="approvalStatus != null and approvalStatus != ''">
            AND approval_status = #{approvalStatus}
        </if>
        <if test="userId != null and userId != ''">
            AND user_id = #{userId}
        </if>
        <if test="applyId != null and applyId != ''">
            AND id = #{applyId}
        </if>
        ORDER BY IF(approval_status>0,1,0),created_dt desc
    </select>

    <select id="queryUserApplyDetail" resultType="com.tiangong.user.dto.EmployeeUserDTO">
        SELECT
        user_id employeeId,
        user_name employeeName,
        user_account employeeAccount,
        user_tel employeeTel,
        user_id_number employeeIDNumber,
        user_account_type employeeAccountType,
        account_avl_start_time accountAvlStartTime,
        account_avl_end_time accountAvlEndTime,
        visit_start_time visitStartTime,
        visit_end_time visitEndTime,
        visit_ip visitIp,
        is_note_permission isNotePermission,
        user_roles userRoles
        FROM t_auth_user_apply
        WHERE id = #{applyId}
    </select>

</mapper>