<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tiangong.organization.mapper.AreaDataMapper">
    <resultMap id="AreaDataMap" type="com.tiangong.organization.domain.AreaDataPO">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="date_id" jdbcType="INTEGER" property="dateId"/>
        <result column="date_name" jdbcType="VARCHAR" property="dateName"/>
        <result column="date_code" jdbcType="INTEGER" property="dateCode"/>
        <result column="super_id" jdbcType="INTEGER" property="superId"/>
        <result column="frist_name" jdbcType="VARCHAR" property="fristName"/>
        <result column="level" jdbcType="INTEGER" property="level"/>
        <result column="pinyin" jdbcType="VARCHAR" property="pinyin"/>
        <result column="active" jdbcType="INTEGER" property="active"/>

    </resultMap>


    <select id="queryAreaData" parameterType="java.util.Map"
            resultType="com.tiangong.organization.remote.dto.AreaDataDTO">
        SELECT
        id dataId,
        area_name cityName,
        area_code cityCode,
        parent_area_id superId,
        upper(substr(acronym,1,1)) firstLetter,
        area_type level,
        pinyin pinyin,
        1 active,

        if(top>0,1,0) top

        FROM
        t_baseinfo_areadata_zh_CN b
        where
        b.deleted = 0
        <if test="superId!=null">
            AND b.parent_area_id = #{superId}
        </if>
        <if test="firstLetter!=null">
            AND b.acronym IN
            <foreach collection="firstLetter" item="item" open="(" separator="," close=")">#{item}</foreach>
        </if>
        <if test="level!=null">
            AND b.area_type = #{level}
        </if>
        <if test="cityName !=null and cityName!=''">
            AND (b.area_name = #{cityName} or b.pinyin=#{cityName})
        </if>
        ORDER BY b.top desc,b.id DESC
  </select>

    <select id="queryAreaDataListByType" resultType="com.tiangong.hotel.domain.BaseinfoAreadataEntity" parameterType="com.tiangong.hotel.req.QueryAreaDataReq">
        select area_name, area_code, parent_area_id, area_type, time_zero from ${tXTable} where deleted = 0

        <if test="req.areaName != null and req.areaName != '' ">
            and area_name like concat('%', #{req.areaName}, '%')
        </if>
        <if test="req.areaType != null and req.areaType != '' ">
            and area_type = #{req.areaType}
        </if>
        <if test="req.areaCode != null and req.areaCode != '' ">
            <if test="req.codeLike != null and req.codeLike == 1">
                and area_code like concat('%', #{req.areaCode}, '%')
            </if>
            <if test="req.codeLike == null or req.codeLike == 0">
                and area_code = #{req.areaCode}
            </if>
        </if>
        <if test="req.parentAreaId != null and req.parentAreaId != '' ">
            and parent_area_id = #{req.parentAreaId}
        </if>

    </select>

    <select id="queryHotCityList" resultType="com.tiangong.dto.hotel.HotCityResp">
        select
        area_name as cityName,
        area_code as cityCode,
        parent_area_id,
        latitude,
        longitude,
        supply_type,
        area_type
        from ${tXTable}
        where area_type = 3  and top = 1 and deleted = 0
        order by supply_type asc
    </select>

    <select id="queryHotCityListManger" resultType="com.tiangong.hotel.domain.resp.HotCityMangerResp">
        select
            zh.area_name as cityName,
            en.area_name as cityEnName,
            zh.area_code as cityCode,
            zh.supply_type
        from t_baseinfo_areadata_en_us en left join t_baseinfo_areadata_zh_cn zh on en.area_code = zh.area_code
        where en.area_type = 3  and en.top = 1 and en.deleted = 0 and zh.area_type = 3 and zh.top = 1 and zh.deleted = 0
        order by supply_type asc
    </select>

    <update id="addOrUpdateHotCity" parameterType="com.tiangong.hotel.domain.req.AddOrUpdateHotCityReq">
        update ${tXTable} set top = #{top} where area_code = #{cityCode} and area_type = 3;
    </update>

    <delete id="delAreaDataByType" parameterType="java.lang.Object">
        delete from ${tXTable} where area_type = #{areaType}
    </delete>

    <insert id="insertAreaData" parameterType="java.lang.Object">
        insert into ${tXTable} (area_code,area_name,pinyin,acronym,parent_area_id,city_id,area_type,latitude,longitude,supply_type)
        values
        <foreach collection="list" item="areaData" separator=",">
            (
                #{areaData.areaCode},
                #{areaData.areaName},
                #{areaData.pinyin},
                #{areaData.acronym},
                #{areaData.parentAreaId},
                #{areaData.cityId},
                #{areaData.areaType},
                #{areaData.latitude},
                #{areaData.longitude},
                #{areaData.supplyType}
            )
        </foreach>
    </insert>

    <select id="queryCity" resultType="com.tiangong.hotel.resp.QueryCityResp" parameterType="java.lang.Object">
        SELECT
        area_code,
        area_name
        FROM
        ${tXTable}
        WHERE
        area_type = 3
        <if test="areaName != null and areaName != '' ">
            AND area_name like concat('%', #{areaName}, '%')
        </if>
        AND parent_area_id IN
        <foreach collection="list" item="item" index="index" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <delete id="updateAreaTimeZeroByCityCode" parameterType="com.tiangong.dto.hotel.UpdateTimeZoneDTO">
        update ${tXTable} set time_zero = #{timeZone} where area_code = #{cityCode}
    </delete>

    <select id="queryCityTimeZone" resultType="com.tiangong.dto.hotel.QueryCityTimeZoneResp" parameterType="com.tiangong.dto.hotel.QueryCityTimeZoneReq">
        SELECT
        area_code cityCode,
        area_name cityName,
        time_zero timeZero
        FROM
        ${tXTable}
        WHERE
        area_type = 3
        and area_code = #{cityCode}
        limit 1
    </select>

    <select id="selectCity" parameterType="java.lang.Object" resultType="com.tiangong.hotel.domain.BaseinfoAreadataEntity">
        select * from ${tXTable} where area_code = #{cityCode} and area_type = 3 limit 1
    </select>
</mapper>