# VIP client related internationalization messages
vip.client.duplicate.message=exists in list,Customer code:{0}, VIP Scope:{1}, VIP Name:{2} already exists
vip.client.duplicate.message2=exists in list,Customer code:{0}, VIP Scope:{1} already exists
vip.client.agentCode.invalid.message=Invalid Customer code:{0}
vip.client.agentCode.unable.message=Customer code:{0} is disabled

# VIP scope types
vip.scope.type.all.personnel=All personnel
vip.scope.type.specified.personnel=Designated personnel

