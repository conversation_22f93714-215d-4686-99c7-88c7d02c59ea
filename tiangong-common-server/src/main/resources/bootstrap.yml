spring:
  application:
    name: tiangong-common-server
  jackson:
    time-zone: GMT+8
  main:
    allow-bean-definition-overriding: true
  profiles:
    active: pro
  servlet:
    multipart:
      enabled: true
      file-size-threshold: 0
      max-file-size: 20MB  # 单个文件的最大上限
      max-request-size: 20MB #单个请求的文件总大小上限


server:
  port: 14001
  tomcat:
    connection-timeout: 10000

#mybatis:
#  mapper-locations: classpath*:/mapping/*/*Mapper.xml
#  type-aliases-package: com.tiangong.*.domain,com.tiangong.*.*.domain.*

mybatis-plus:
  mapper-locations: classpath*:/mapping/*/*Mapper.xml
  type-aliases-package: com.tiangong.*.domain,com.tiangong.*.*.domain.*
  global-config:
    db-config:
      id-type: auto
      field-strategy: 2
      refresh-mapper: true
      db-type: mysql

tiangong:
  security:
    switch: true # 是否开启security，true：开启，false：关闭
    secret: ZmQ0ZGI5NjQ0MDQwY2I4MjMxY2Y3ZmI3MjdhN2ZmMjNhODViOTg1ZGE0NTBjMGM4NDA5NzYxMjdjOWMwYWRmZGE= # Base64编码
    tokentime: 20 # 令牌过期时间 此处单位/小时 ，默认2小时，可在此网站生成 https://www.convertworld.com/zh-hans/time/milliseconds.html


mapper:
  mappers: com.tiangong.dto.common.MyMapper
  not-empty: false
  identity: MYSQL

pagehelper:
  helperDialect: mysql
  reasonable: true
  supportMethodsArguments: true
  params: count=countSql


#调用feign超时时间
ribbon:
  ReadTimeout: 6000000
  ConnectTimeout: 6000000
  ServerListRefreshInterval: 1000

#xxljob配置
xxl:
  job:
    admin:
      addresses: http://127.0.0.1:18880/xxl-job-admin/
    accessToken: default_token
    executor:
      appname: tiangong-common-server
      address:
      logpath: ./applogs/tiangong-common-server
      logretentiondays: 3
      port: 0

logstash:
  url: ************:5044


