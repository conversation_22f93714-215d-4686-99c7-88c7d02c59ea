package com.tiangong.supply.shub.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.tiangong.common.Constant;
import com.tiangong.common.Response;
import com.tiangong.dto.product.HourlyRoomDetail;
import com.tiangong.enums.*;
import com.tiangong.exception.SysException;
import com.tiangong.keys.RedisKey;
import com.tiangong.order.remote.SupplyOrderRemote;
import com.tiangong.order.remote.dto.InBlankCheckInfoDTO;
import com.tiangong.order.remote.dto.OrderCheckDetailInfoDTO;
import com.tiangong.order.remote.dto.OrderCheckDetailPriceItem;
import com.tiangong.order.remote.dto.OrderCheckDetails;
import com.tiangong.order.remote.request.SaveSupplyResultDTO;
import com.tiangong.order.remote.response.SupplyOrderDTO;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.supply.direct.remote.dto.*;
import com.tiangong.supply.direct.remote.enums.ReturnNoEnum;
import com.tiangong.supply.direct.remote.request.*;
import com.tiangong.supply.direct.remote.response.CancelOrderResponse;
import com.tiangong.supply.direct.remote.response.CreateSupplyOrderResponse;
import com.tiangong.supply.direct.remote.response.PreBookingResponse;
import com.tiangong.supply.direct.remote.response.QuerySupplyOrderResponse;
import com.tiangong.supply.shub.config.SettingsConstant;
import com.tiangong.supply.shub.constant.ShubConstant;
import com.tiangong.supply.shub.convert.ShubConvert;
import com.tiangong.supply.shub.dto.PriceItem;
import com.tiangong.supply.shub.dto.*;
import com.tiangong.supply.shub.enues.ShubCancelRestrictEnum;
import com.tiangong.supply.shub.enues.ShubOrderStatusEnum;
import com.tiangong.supply.shub.enues.ShubPayMethodEnum;
import com.tiangong.supply.shub.enues.ShubRoomStateEnum;
import com.tiangong.supply.shub.exception.BusinessException;
import com.tiangong.supply.shub.exception.ErrorCode;
import com.tiangong.supply.shub.exception.GlobalErrorCodeConstants;
import com.tiangong.supply.shub.manager.ShubManager;
import com.tiangong.supply.shub.service.ShubOrderService;
import com.tiangong.supply.shub.service.ShubProductService;
import com.tiangong.supply.shub.utils.CheckInStateConverter;
import com.tiangong.supply.shub.utils.EnumsConvertToCommonUtils;
import com.tiangong.supply.shub.utils.ParamValidator;
import com.tiangong.supply.shub.utils.ShubUtils;
import com.tiangong.util.CollUtilX;
import com.tiangong.util.DateUtilX;
import com.tiangong.util.SlsLoggerUtil;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.tiangong.supply.shub.utils.ShubUtils.buildProductCacheKey;

@Slf4j
@Service
public class ShubOrderServiceImpl implements ShubOrderService {

    @Autowired
    private ShubManager shubManager;

    @Autowired
    private ShubProductService shubProductService;

    @Autowired
    private SettingsConstant settingsConstant;

    @Autowired
    private SlsLoggerUtil slsLoggerUtil;

    @Autowired
    private SupplyOrderRemote supplyOrderRemote;

    /**
     * 试预订接口
     */
    @Override
    public Response<PreBookingResponse> proBooking(PreBookingRequest request) {
        Date start = new Date();
        Response<PreBookingResponse> initialResponse = Response.success(new PreBookingResponse());
        PreBookingResponse responseBody = initialResponse.getModel();
        responseBody.setHourlyRoom(request.getHourly());

        String errorMsg = null;
        try {
            // 参数预处理
            processRequestParams(request);

            // 构建并执行Shub请求
            ResultX<PreBookingShubResp> shubResult = executeShubPreBooking(request);
            if (shubResult == null) {
                initialResponse = Response.error(ReturnNoEnum._010701904.returnCode, ReturnNoEnum._010701904.returnDesc);
                return initialResponse;
            }
            if (shubResult.isError()) {
                initialResponse = Response.error(shubResult.getCode(), shubResult.getMsg());
                return initialResponse;
            }
            if (shubResult.getData() == null) {
                initialResponse = Response.error(ReturnNoEnum._010701083.returnCode, ReturnNoEnum._010701083.returnDesc);
                return initialResponse;
            }

            // 处理响应数据
            processResponseData(request, responseBody, shubResult.getData());

            // 缓存试预定产品信息
            cachePreBookingInfo(responseBody, request);
        } catch (BusinessException e) {
            errorMsg = e.getMessage();
            log.error("预订单异常 request={}", JSONUtil.toJsonStr(request), e);
            initialResponse = Response.error(e.getErrorCode(), e.getMessage());
        } catch (Exception e) {
            errorMsg = e.getMessage();
            log.error("系统异常 request={}", JSONUtil.toJsonStr(request), e);
            initialResponse = Response.error(ReturnNoEnum._010701900.returnCode, ReturnNoEnum._010701900.returnDesc);
        } finally {
            // 记录日志
            logSlsRecord(start, ShubConstant.PRO_BOOKING, JSONUtil.toJsonStr(request), JSONUtil.toJsonStr(initialResponse), initialResponse, errorMsg);
        }

        return initialResponse;
    }

    /**
     * 参数预处理
     */
    private void processRequestParams(PreBookingRequest request) {
        String[] productParts = request.getSpProductId().split("#");
        request.setSpProductId(productParts[0]);
        request.setSpProductName(productParts[1]);
    }

    /**
     * 执行Shub请求
     */
    private ResultX<PreBookingShubResp> executeShubPreBooking(PreBookingRequest request) {
        try {
            PreBookingShubReq shubReq = buildShubRequest(request);
            return shubManager.preBooking(shubReq);
        } catch (Exception e) {
            log.error("调用shub接口异常", e);
            throw new BusinessException(ReturnNoEnum._010701900);
        }
    }

    /**
     * 构建Shub请求
     */
    private PreBookingShubReq buildShubRequest(PreBookingRequest request) {
        PreBookingShubReq req = PreBookingShubReq.builder()
                .hotelId(request.getHotelId())
                .roomId(request.getRoomId())
                .supplyRateId(request.getSpProductId())
                .checkInDate(request.getCheckInDate())
                .checkOutDate(request.getCheckOutDate())
                .roomNum(request.getRoomNum())
                .supplyCode(request.getSupplyCode())
                .currency(request.getCurrency())
                .nationality(request.getNationality())
                .build();
        req.setRequestId(request.getRequestId());
        if (CollUtil.isNotEmpty(request.getRoomGuestNumbers())) {
            req.setRoomGuestNumbers(ShubConvert.INSTANCE.roomGuestNumberListConvert(request.getRoomGuestNumbers()));
        }

        if (StrUtil.isNotBlank(request.getUserAccount()) && settingsConstant.getExpedia()) {
            req.setToken(RedisTemplateX.get(RedisKey.TOKEN_RANDOM_ID + request.getUserAccount()));
            req.setCustomerIp(RedisTemplateX.get(RedisKey.USER_IPADDRESS + request.getUserAccount()));
        }
        return req;
    }

    /**
     * 处理响应数据
     */
    private void processResponseData(PreBookingRequest request, PreBookingResponse response, PreBookingShubResp shubResp) {
        // 基础信息设置
        setBasicInfo(request, response);

        // 处理产品缓存
        ProductRedisDTO productDTO = processProductCache(request);
        if (productDTO == null) {
            throw new BusinessException(ReturnNoEnum._010702085);
        }

        // 取消政策处理
        response.setCancelRestrictions(processCancelPolicy(productDTO.getSaleItemDetail()));
        // 设置支付方式
        response.setPayMethod(productDTO.getPayMethod());
        // 当试预订没有返回房型名称时取查询产品的房型名称
        if (StrUtilX.isEmpty(response.getRoomName())) {
            response.setRoomName(productDTO.getSpRoomName());
        }

        // 价格明细处理
        processPriceDetails(request, response, shubResp);
    }

    /**
     * 基础信息设置
     */
    private void setBasicInfo(PreBookingRequest request, PreBookingResponse response) {
        response.setHotelId(request.getHotelId());
        response.setRoomId(request.getRoomId());
        response.setPricePlanId(request.getSpProductId());
        response.setSupplyCode(request.getSupplyCode());
        response.setCheckInDate(request.getCheckInDate());
        response.setCheckOutDate(request.getCheckInDate());
    }

    /**
     * 处理产品缓存
     */
    private ProductRedisDTO processProductCache(PreBookingRequest request) {
        String cacheKey = buildProductCacheKey(request.getSupplyCode(),
                request.getHotelId(),
                request.getSpProductId(),
                request.getCheckInDate(),
                request.getCheckOutDate(),
                request.getRoomId(),
                request.getRoomGuestNumbers());
        String productJson = RedisTemplateX.get(cacheKey);

        // 为空加载一次缓存，还是不存在说明产品不存在
        if (StrUtilX.isEmpty(productJson)) {
            productJson = reloadProductToCache(request);
        }
        if (StrUtilX.isEmpty(productJson)) {
            throw new BusinessException(ReturnNoEnum._010702085);
        }

        return JSONUtil.toBean(productJson, ProductRedisDTO.class);
    }

    /**
     * 重新加载产品缓存
     */
    private String reloadProductToCache(PreBookingRequest request) {
        QueryProductInfoRequest queryRequest = QueryProductInfoRequest.builder()
                .spHotelId(request.getHotelId())
                .supplyType(request.getSupplyType())
                .checkInDate(request.getCheckInDate())
                .checkOutDate(request.getCheckOutDate())
                .roomQty(request.getRoomNum())
                .roomGuestNumbers(request.getRoomGuestNumbers())
                .supplyCodes(Collections.singleton(request.getSupplyCode()))
                .build();
        queryRequest.setRequestId(request.getRequestId());

        shubProductService.queryProductList(queryRequest);
        String cacheKey = buildProductCacheKey(request.getSupplyCode(),
                request.getHotelId(),
                request.getSpProductId(),
                request.getCheckInDate(),
                request.getCheckOutDate(),
                request.getRoomId(),
                request.getRoomGuestNumbers());
        return RedisTemplateX.get(cacheKey);
    }

    /**
     * 处理取消政策逻辑
     */
    private List<CancelRestriction> processCancelPolicy(SaleItemDetail saleItemDetail) {
        List<CancelRestriction> restrictions = new ArrayList<>();
        CancelRestriction restriction = new CancelRestriction();

        // 处理未定义取消政策的情况
        if (saleItemDetail.getCancelRestrictType() == null) {
            restriction.setCancelRestrictionType(CancelRestrictionTypeEnum.FULL_CANCEL_LATE.getCode());
            restriction.setCancelRestrictionDay(0);
            restriction.setCancelRestrictionTime("2359");
            restrictions.add(restriction);
            return restrictions;
        }

        // 根据取消政策类型处理
        ShubCancelRestrictEnum restrictType = ShubCancelRestrictEnum.getEnumByKey(saleItemDetail.getCancelRestrictType());
        switch (restrictType) {
            case NO_CANCEL:
                handleNoCancelPolicy(restriction, saleItemDetail);
                break;
            case PARTIAL_CANCEL:
                handlePartialCancelPolicy(restriction, saleItemDetail);
                break;
            case FULL_CANCEL:
                handleFullCancelPolicy(restriction, saleItemDetail);
                break;
            default:
                log.warn("未知的取消政策类型: {}", saleItemDetail.getCancelRestrictType());
                return Collections.emptyList();
        }

        // 验证必要字段
        if (restriction.getCancelRestrictionType() != null) {
            restrictions.add(restriction);
        }
        return restrictions;
    }

    /**
     * 处理不可取消政策
     */
    private void handleNoCancelPolicy(CancelRestriction restriction, SaleItemDetail detail) {
        restriction.setCancelRestrictionType(CancelRestrictionTypeEnum.NO_CANCEL.getCode());
        restriction.setCancelRestrictionRemark(detail.getCancelRestrictRemark());
    }

    /**
     * 处理部分取消政策
     */
    private void handlePartialCancelPolicy(CancelRestriction restriction, SaleItemDetail detail) {
        if (isValidCancelCondition(detail)) {
            restriction.setCancelRestrictionType(CancelRestrictionTypeEnum.PARTIAL_CANCEL.getCode());
            // 转换取消政策
            convertCancelPolicy(restriction, detail);
        }
    }

    /**
     * 处理完全取消政策
     */
    private void handleFullCancelPolicy(CancelRestriction restriction, SaleItemDetail detail) {
        if (isValidCancelCondition(detail)) {
            restriction.setCancelRestrictionType(CancelRestrictionTypeEnum.FULL_CANCEL_LATE.getCode());
            // 转换取消政策
            convertCancelPolicy(restriction, detail);
        }
    }

    /**
     * 转换取消政策
     */
    private void convertCancelPolicy(CancelRestriction restriction, SaleItemDetail detail) {
        restriction.setCancelRestrictionDay(detail.getCancelRestrictDays());
        restriction.setCancelRestrictionTime(detail.getCancelRestrictTime());
        restriction.setCancelRestrictionRemark(detail.getCancelRestrictRemark());
        if (CollUtil.isNotEmpty(detail.getCancelPenalties())) {
            List<CancelPenalties> cancelPenalties = ShubConvert.INSTANCE.cancelPenaltiesConvert(detail.getCancelPenalties());
            restriction.setCancelPenalties(cancelPenalties);
        }
    }

    /**
     * 验证取消条件是否完整
     */
    private boolean isValidCancelCondition(SaleItemDetail detail) {
        return detail.getCancelRestrictDays() != null
                && StrUtil.isNotBlank(detail.getCancelRestrictTime());
    }

    /**
     * 处理价格明细
     */
    private void processPriceDetails(PreBookingRequest request,
                                     PreBookingResponse response,
                                     PreBookingShubResp shubResp) {
        // 是否是国内
        boolean isDomestic = SupplyTypeEnum.DOMESTIC.getCode().equals(request.getSupplyType());

        // 处理价格明细
        List<OrderRoomDetailDto> roomDetails = isDomestic ?
                processDomesticPrice(request, shubResp) :
                processOverseasPrice(shubResp, request);
        response.setOrderRoomDetails(roomDetails);
        // 设置可否预订
        response.setCanBook(shubResp.getCanBook() ? CanBookEnum.CAN_BOOK.value : CanBookEnum.CAN_NOT_BOOK.value);
    }

    /**
     * 处理国内价格明细
     */
    private List<OrderRoomDetailDto> processDomesticPrice(PreBookingRequest request,
                                                          PreBookingShubResp shubResp) {
        return request.getRoomGuestNumbers().stream()
                .map(guestNumber -> buildRoomDetail(guestNumber, shubResp, request))
                .collect(Collectors.toList());
    }

    /**
     * 处理海外价格明细
     */
    private List<OrderRoomDetailDto> processOverseasPrice(PreBookingShubResp shubResp,
                                                          PreBookingRequest request) {
        return shubResp.getRoomDetails().stream()
                .map(roomDetail -> convertOverseasRoomDetail(shubResp, roomDetail, request))
                .collect(Collectors.toList());
    }

    /**
     * 构建房间详情（国内）
     */
    private OrderRoomDetailDto buildRoomDetail(RoomGuestNumber guestNumber,
                                               PreBookingShubResp shubResp,
                                               PreBookingRequest request) {
        OrderRoomDetailDto dto = OrderRoomDetailDto.builder()
                .roomIndex(guestNumber.getRoomIndex())
                .adultNum(guestNumber.getAdultNum())
                .childAges(convertChildrenAges(guestNumber.getChildrenInfos()))
                .build();

        // 转换价格项
        convertPriceItems(shubResp.getPriceItems(), request, dto,
                shubResp.getBaseCurrency(), shubResp.getCanBook());
        return dto;
    }

    /**
     * 转换海外房间详情
     */
    private OrderRoomDetailDto convertOverseasRoomDetail(PreBookingShubResp shubResp,
                                                         OrderRoomDetail roomDetail,
                                                         PreBookingRequest request) {
        OrderRoomDetailDto dto = ShubConvert.INSTANCE.convertOrderRoomDetailDto(roomDetail);
        // 转换价格项
        convertPriceItems(roomDetail.getPriceItems(), request, dto,
                shubResp.getBaseCurrency(), shubResp.getCanBook());
        return dto;
    }

    /**
     * 转换子节点年龄信息
     */
    private String convertChildrenAges(List<ChildrenInfo> childrenInfos) {
        return Optional.ofNullable(childrenInfos)
                .map(list -> list.stream()
                        .map(ChildrenInfo::getChildrenAge)
                        .map(String::valueOf)
                        .collect(Collectors.joining(",")))
                .orElse(null);
    }

    /**
     * 价格项转换通用逻辑
     */
    private void convertPriceItems(List<PriceItem> priceItems,
                                   PreBookingRequest request,
                                   OrderRoomDetailDto dto,
                                   Integer baseCurrency,
                                   boolean canBook) {
        if (CollUtil.isEmpty(priceItems)) return;

        List<PriceInfoDetail> priceDetails = priceItems.stream()
                .map(item -> convertPriceItem(item, request, baseCurrency, canBook))
                .collect(Collectors.toList());

        dto.setPriceInfoDetails(priceDetails);
    }

    /**
     * 单个价格项转换
     */
    private PriceInfoDetail convertPriceItem(PriceItem item,
                                             PreBookingRequest request,
                                             Integer baseCurrency,
                                             boolean canBook) {
        PriceInfoDetail detail = PriceInfoDetail.builder()
                .date(DateUtilX.dateToString(item.getSaleDate()))
                .price(item.getBasePrice())
                .currency(baseCurrency)
                .breakfastType(item.getBreakfastType())
                .breakfastNum(item.getBreakfastNum())
                .quotaNum(item.getQuotaNum())
                .canOverDraft(item.getCanOverDraft())
                .roomStatus(canBook ? ShubRoomStateEnum.HAVA_ROOM.key : item.getRoomStatus())
                .build();

        // 处理税费明细
        processTaxDetail(request.getSupplyType(), detail, item);
        return detail;
    }

    /**
     * 处理税费明细
     */
    private void processTaxDetail(String supplyType, PriceInfoDetail detail, PriceItem item) {
        if (SupplyTypeEnum.DOMESTIC.getCode().equals(supplyType)) {
            detail.setTaxDetail(TaxDetailDto.builder().roomPrice(item.getBasePrice().doubleValue()).build());
        } else if (item.getTaxDetail() != null) {
            detail.setTaxDetail(ShubConvert.INSTANCE.taxDetailConvert(item.getTaxDetail()));
        }
    }

    /**
     * 缓存试预定产品信息
     */
    private void cachePreBookingInfo(PreBookingResponse preBookingResponse, PreBookingRequest request) {
        if (preBookingResponse == null) {
            return;
        }

        ProductMiddleDto productMiddleDto = new ProductMiddleDto();
        // 设置基础信息
        copyBasicInfoToProductDto(preBookingResponse, request, productMiddleDto);
        // 设置是否到店付标志
        setPayAtHotelFlag(preBookingResponse, productMiddleDto);
        // 处理订单房间详情
        List<CancelRestriction> cancelRestrictions = convertCancelRestrictions(preBookingResponse);
        processOrderRoomDetails(preBookingResponse, request, productMiddleDto, cancelRestrictions);
    }

    /**
     * 设置基础信息
     */
    private void copyBasicInfoToProductDto(PreBookingResponse preBookingResponse, PreBookingRequest request, ProductMiddleDto productMiddleDto) {
        productMiddleDto.setBedTypeDetails(preBookingResponse.getBedTypeDetails());
        productMiddleDto.setHotelId(Long.parseLong(preBookingResponse.getHotelId()));
        productMiddleDto.setHotelName(preBookingResponse.getHotelName());
        productMiddleDto.setRoomId(Integer.valueOf(preBookingResponse.getRoomId()));
        productMiddleDto.setRoomName(preBookingResponse.getRoomName());
        productMiddleDto.setSpPricePlanId(request.getSpProductId());
        productMiddleDto.setSpPricePlanName(request.getSpProductName());
        productMiddleDto.setSupplyCode(preBookingResponse.getSupplyCode());
        productMiddleDto.setInvoiceModel(preBookingResponse.getInvoiceModel());
        productMiddleDto.setInvoiceType(preBookingResponse.getInvoiceType());
    }

    /**
     * 设置是否到店付标志
     */
    private void setPayAtHotelFlag(PreBookingResponse preBookingResponse, ProductMiddleDto productMiddleDto) {
        boolean isPayAtHotel = PayMethodEnum.PAY.key.equals(preBookingResponse.getPayMethod());
        productMiddleDto.setPayAtHotelFlag(isPayAtHotel ? 1 : 0);
    }

    /**
     * 处理订单房间详情
     */
    private List<CancelRestriction> convertCancelRestrictions(PreBookingResponse preBookingResponse) {
        if (CollUtilX.isEmpty(preBookingResponse.getCancelRestrictions())) {
            return Collections.emptyList();
        }
        return preBookingResponse.getCancelRestrictions().stream()
                .map(this::buildCancelRestriction)
                .collect(Collectors.toList());
    }

    /**
     * 构造取消条款
     */
    private CancelRestriction buildCancelRestriction(CancelRestriction source) {
        CancelRestriction restriction = new CancelRestriction();
        restriction.setCancelRestrictionType(source.getCancelRestrictionType());
        restriction.setCancelRestrictionDay(source.getCancelRestrictionDay());
        restriction.setCancelRestrictionTime(source.getCancelRestrictionTime());
        restriction.setCancellationPrice(source.getCancellationPrice());
        restriction.setCancelRestrictionRemark(source.getCancelRestrictionRemark());
        restriction.setCancelPenalties(source.getCancelPenalties());
        return restriction;
    }

    /**
     * 处理订单房间详情
     */
    private void processOrderRoomDetails(PreBookingResponse preBookingResponse,
                                         PreBookingRequest request,
                                         ProductMiddleDto productMiddleDto,
                                         List<CancelRestriction> cancelRestrictions) {
        List<OrderRoomDetailDto> orderRoomDetails = preBookingResponse.getOrderRoomDetails();
        if (CollUtilX.isEmpty(orderRoomDetails)) {
            return;
        }

        List<RoomItemDetailDto> roomItemDetails = orderRoomDetails.stream()
                .map(detail -> convertToRoomItemDetail(detail, cancelRestrictions, preBookingResponse))
                .collect(Collectors.toList());

        productMiddleDto.setRoomItemDetails(roomItemDetails);
        productMiddleDto.setHourlyRoom(preBookingResponse.getHourlyRoom());
        productMiddleDto.setTips(preBookingResponse.getTips());
        // 设置币种
        if (CollUtilX.isNotEmpty(roomItemDetails) && CollUtilX.isNotEmpty(roomItemDetails.get(0).getProductDetails())) {
            productMiddleDto.setCurrency(roomItemDetails.get(0).getProductDetails().get(0).getCurrency());
        }

        // 缓存产品信息
        cacheProductDto(productMiddleDto, request);
    }

    /**
     * 转换订单房间详情
     */
    private RoomItemDetailDto convertToRoomItemDetail(OrderRoomDetailDto orderRoomDetail, List<CancelRestriction> cancelRestrictions, PreBookingResponse preBookingResponse) {
        RoomItemDetailDto roomItemDetail = new RoomItemDetailDto();
        roomItemDetail.setRoomIndex(orderRoomDetail.getRoomIndex());
        roomItemDetail.setAdultNum(orderRoomDetail.getAdultNum());
        roomItemDetail.setChildAges(orderRoomDetail.getChildAges());
        // 设置税费详情
        copyTaxDetail(orderRoomDetail, roomItemDetail);
        // 设置是否到店付标志
        setPayInStoreInfo(orderRoomDetail, roomItemDetail);
        // 处理价格详情
        processPriceInfoDetails(orderRoomDetail, roomItemDetail, cancelRestrictions, preBookingResponse);
        return roomItemDetail;
    }

    /**
     * 拷贝税费详情
     */
    private void copyTaxDetail(OrderRoomDetailDto source, RoomItemDetailDto target) {
        if (source.getTaxDetail() != null) {
            target.setTaxDetail(ShubConvert.INSTANCE.taxDetailConvert(source.getTaxDetail()));
        }
    }

    /**
     * 设置是否到店付标志
     */
    private void setPayInStoreInfo(OrderRoomDetailDto source, RoomItemDetailDto target) {
        target.setPayInStorePrice(source.getPayInStorePrice());
        target.setPayInStoreCurrency(source.getPayInStoreCurrency());
        target.setSupplyPayInStorePrice(source.getSupplyPayInStorePrice());
        target.setSupplyPayInStoreCurrency(source.getSupplyPayInStoreCurrency());
    }

    /**
     * 处理价格详情
     */
    private void processPriceInfoDetails(OrderRoomDetailDto orderRoomDetail, RoomItemDetailDto roomItemDetail,
                                         List<CancelRestriction> cancelRestrictions, PreBookingResponse preBookingResponse) {
        List<PriceInfoDetail> priceInfoDetails = orderRoomDetail.getPriceInfoDetails();
        if (CollUtilX.isEmpty(priceInfoDetails)) {
            return;
        }

        List<ProductDetailMiddleDto> productDetails = priceInfoDetails.stream()
                .map(detail -> buildProductDetailMiddleDto(detail, cancelRestrictions, preBookingResponse))
                .collect(Collectors.toList());
        roomItemDetail.setProductDetails(productDetails);
    }

    /**
     * 构建产品详情
     */
    private ProductDetailMiddleDto buildProductDetailMiddleDto(PriceInfoDetail priceInfoDetail,
                                                               List<CancelRestriction> cancelRestrictions,
                                                               PreBookingResponse preBookingResponse) {
        ProductDetailMiddleDto dto = new ProductDetailMiddleDto();
        dto.setSaleDate(priceInfoDetail.getDate());
        dto.setBasePrice(priceInfoDetail.getPrice());
        dto.setCurrency(priceInfoDetail.getCurrency());
        dto.setBreakfastType(priceInfoDetail.getBreakfastType());
        dto.setBreakfastNum(priceInfoDetail.getBreakfastNum());
        dto.setCancelRestrictions(cancelRestrictions);
        dto.setTaxDetail(priceInfoDetail.getTaxDetail());
        dto.setRoomStatus(priceInfoDetail.getRoomStatus() == 3 ? 0 : priceInfoDetail.getRoomStatus());
        // 设置担保信息
        setGuaranteeInfo(preBookingResponse, dto);
        return dto;
    }

    /**
     * 设置担保信息
     */
    private void setGuaranteeInfo(PreBookingResponse preBookingResponse, ProductDetailMiddleDto dto) {
        dto.setGuaranteeType(preBookingResponse.getGuaranteeType());
        dto.setGuaranteeCondition(preBookingResponse.getGuaranteeCondition());
        dto.setGuaranteeFeeType(preBookingResponse.getGuaranteeFeeType());
    }

    /**
     * 缓存产品信息
     */
    private void cacheProductDto(ProductMiddleDto productMiddleDto, PreBookingRequest request) {
        String redisKey = ShubUtils.buildPrebookCacheKey(
                request.getSupplyCode(),
                request.getHotelId(),
                request.getSpProductId(),
                request.getRoomGuestNumbers(),
                request.getCheckInDate(),
                request.getCheckOutDate()
        );
        RedisTemplateX.setAndExpire(redisKey, JSON.toJSONString(productMiddleDto), 60 * 60);
    }

    /**
     * 创建订单
     */
    @Override
    public Response<CreateSupplyOrderResponse> createSupplyOrder(CreateSupplyOrderRequest request) {
        Date startTime = new Date();
        Response<CreateSupplyOrderResponse> response = initializeResponse();
        CreateSupplyOrderResponse responseBody = response.getModel();
        String errorMsg = null;

        try {
            responseBody.setSupplyOrderCode(request.getSupplyOrderCode());

            CreateOrderShubReq createOrderReq = buildCreateOrderRequest(request);
            ResultX<CreateOrderShubResp> shubResult = createOrderViaShub(createOrderReq, request);

            if (shubResult == null) {
                response = Response.error(ReturnNoEnum._010702904.returnCode, ReturnNoEnum._010702904.returnDesc);
                return response;
            }
            if (shubResult.isError()) {
                response = Response.error(shubResult.getCode(), shubResult.getMsg(), responseBody);
                return response;
            }
            if (shubResult.getData() == null) {
                response = Response.error(ReturnNoEnum._010702904.returnCode, ReturnNoEnum._010702904.returnDesc);
                return response;
            }

            // 处理shub响应
            processShubResponse(responseBody, shubResult.getData(), createOrderReq);
        } catch (Exception e) {
            errorMsg = e.getMessage();
            log.error("供应商 shub 创建订单异常，request={}", JSONUtil.toJsonStr(request), e);
            response = Response.error(ReturnNoEnum._010702900.returnCode, ReturnNoEnum._010702900.returnDesc, responseBody);
        } finally {
            // 记录日志
            logSlsRecord(startTime, ShubConstant.CREATE_SUPPLY_ORDER, JSONUtil.toJsonStr(request), JSONUtil.toJsonStr(responseBody), response, errorMsg);
        }

        return response;
    }

    /**
     * 初始化返回对象
     */
    private Response<CreateSupplyOrderResponse> initializeResponse() {
        CreateSupplyOrderResponse body = new CreateSupplyOrderResponse();
        return Response.success(body);
    }

    /**
     * 构建创建订单请求
     */
    private CreateOrderShubReq buildCreateOrderRequest(CreateSupplyOrderRequest request) {
        CreateOrderShubReq req = new CreateOrderShubReq();

        // 处理价格明细
        List<CreateOrderPriceItem> orderDetails = request.getSupplyDetails().stream()
                .map(this::buildPriceItem)
                .sorted(Comparator.comparing(CreateOrderPriceItem::getSaleDate))
                .collect(Collectors.toList());
        req.setCreateOrderDetails(orderDetails);

        // 设置基础信息
        req.setHotelId(request.getHotelId());
        req.setRoomId(request.getRoomId());
        req.setSupplyRateId(request.getSpProductId());
        req.setSupplyCode(request.getSupplyCode());
        req.setCheckInDate(DateUtilX.dateToString(request.getCheckInDate()));
        req.setCheckOutDate(DateUtilX.dateToString(request.getCheckOutDate()));
        req.setRoomNum(request.getRoomNum());
        req.setPayInStorePrice(request.getPayInStorePrice());// 到店另付费用
        req.setPayInStoreCurrency(request.getPayInStoreCurrency());
        req.setTotalAmount(request.getOrderSum());
        req.setMerchantOrderCode(request.getSupplyOrderCode());
        req.setBaseCurrency(request.getSupplyDetails().get(0).getCurrency());
        req.setRoomGuestNumbers(request.getRoomGuestNumbers());
        req.setGuaranteeFlag(request.getGuaranteeFlag());
        req.setIsVipOrder(request.getIsVipOrder());
        req.setTravelType(request.getTravelType());
        req.setRequestId(request.getRequestId());
        req.setAgentCode(request.getAgentCode());
        // 处理支付方式
        Optional.ofNullable(request.getPayMethod())
                .map(this::getShubMethod)
                .ifPresent(req::setPayMethod);

        // 处理联系人信息
        processContactInfo(req, request);

        // 处理入住人信息
        req.setGuests(buildGuestList(request.getSupplyGuests()));

        // 其他设置
        processSpecialRequests(req, request);
        return req;
    }

    /**
     * 处理价格明细
     */
    private CreateOrderPriceItem buildPriceItem(SupplyDetailsDTO dto) {
        CreateOrderPriceItem item = new CreateOrderPriceItem();
        item.setSaleDate(dto.getRoomDate());
        item.setBasePrice(dto.getRoomPrice());
        item.setBreakfastNum(dto.getBreakFastNum());
        item.setBreakfastType(dto.getBreakFastType());
        return item;
    }

    /**
     * 系统支付方式转供应商代码（逆向转换）
     */
    public Integer getShubMethod(Integer method) {
        if (PayMethodEnum.PREPAY.key.equals(method)) {
            return ShubPayMethodEnum.PRE_PAY.key;
        } else if (PayMethodEnum.PAY.key.equals(method)) {
            return ShubPayMethodEnum.PAY_NOCOMISSION.key;
        }
        return null;
    }

    /**
     * 处理联系人信息
     */
    private void processContactInfo(CreateOrderShubReq req, CreateSupplyOrderRequest request) {
        // 判断是否使用订单入住人信息
        if ("1".equals(settingsConstant.getUseOrderContacts())) {
            req.setLinkMan(request.getContactName());
            req.setLinkPhone(request.getContactPhone());
            req.setLinkCountryCode(request.getContactCountryCode());
            req.setLinkEmail(request.getContactEmail());
        } else {
            req.setLinkMan(settingsConstant.getOrderLinkMan());
            req.setLinkPhone(settingsConstant.getOrderLinkPhone());
            req.setLinkCountryCode(settingsConstant.getOrderLinkCountryCode());
            req.setLinkEmail(settingsConstant.getOrderLinkEmail());
        }
    }

    /**
     * 处理入住人信息
     */
    private List<Guest> buildGuestList(List<SupplyGuestDTO> guests) {
        return guests.stream()
                .map(this::convertToGuest)
                .collect(Collectors.toList());
    }

    /**
     * 转换入住人信息
     */
    private Guest convertToGuest(SupplyGuestDTO dto) {
        Guest guest = new Guest();
        // 房间号
        guest.setRoomIndex(dto.getRoomIndex());
        // 姓
        guest.setLastName(dto.getLastName());
        // 名字
        guest.setFirstName(dto.getFirstName());
        // 客人全称
        guest.setGuestName(dto.getGuestName());
        // 会员卡号
//        guest.setMembershipCardNumber();
        // 国籍
        guest.setNationality(dto.getNationality());
        // 电话
        guest.setPhone(dto.getPhone());
        //区号
        guest.setCountryCode(dto.getCountryCode());
        // 证件类型
        guest.setIdCardType(dto.getIdType());
        // 证件号码
        guest.setIdCardNo(dto.getIdnumber());
        // 其他特殊字段处理
        return guest;
    }

    private void processSpecialRequests(CreateOrderShubReq req, CreateSupplyOrderRequest request) {
        // 钟点房时间处理
        if (request.getHourlyRoom() == 1) {
            HourlyRoomDetail detail = request.getHourlyRoomDetail();
            req.setArriveTime(extractTime(detail.getCheckInTime()));  // 提取HH:mm格式
            req.setLatestArriveTime(extractTime(detail.getCheckOutTime()));
        } else {
            // 常规订单时间处理
            req.setArriveTime(request.getArrivalEarlyTime());
            req.setLatestArriveTime(request.getArrivalLateTime());
        }

        // 备注处理（确保非空）
        req.setRemark(Optional.ofNullable(request.getRemark())
                .filter(StrUtilX::isNotEmpty)
                .orElse("无"));

        // 床型JSON解析
        Optional.ofNullable(request.getBedType())
                .ifPresent(bedTypeJson -> {
                    try {
                        BedTypesDetails bedType = JSONUtil.toBean(bedTypeJson, BedTypesDetails.class);
                        req.setBedTypeDetails(bedType);
                    } catch (Exception e) {
                        log.error("床型信息解析失败，supplyOrderCode={}", request.getSupplyOrderCode(), e);
                    }
                });
    }

    /**
     * 提取时间部分
     */
    private String extractTime(String time) {
        return time.substring(11, 16);
    }

    /**
     * 创建订单
     */
    private ResultX<CreateOrderShubResp> createOrderViaShub(CreateOrderShubReq req, CreateSupplyOrderRequest request) {
        try {
            enrichRequestWithAuthInfo(req, request);
            return shubManager.createOrder(req);
        } catch (Exception e) {
            log.error("shub创建订单失败，supplyOrderCode={}", request.getSupplyOrderCode(), e);
            throw new BusinessException(ReturnNoEnum._010702900);
        }
    }

    /**
     * 添加认证信息
     */
    private void enrichRequestWithAuthInfo(CreateOrderShubReq req, CreateSupplyOrderRequest request) {
        if (shouldAddAuthInfo(request)) {
            req.setToken(RedisTemplateX.get(RedisKey.TOKEN_RANDOM_ID + request.getUserAccount()));
            req.setCustomerIp(RedisTemplateX.get(RedisKey.USER_IPADDRESS + request.getUserAccount()));
        }
    }

    /**
     * 是否添加认证信息
     */
    private boolean shouldAddAuthInfo(CreateSupplyOrderRequest request) {
        return StrUtilX.isNotEmpty(request.getUserAccount()) && settingsConstant.getExpedia();
    }

    /**
     * 处理shub响应
     */
    private void processShubResponse(CreateSupplyOrderResponse target, CreateOrderShubResp source, CreateOrderShubReq req) {
        target.setSupplierOrderId(source.getSupplyOrderCode());
        target.setSupplierCode(req.getSupplyCode());
        target.setSupplyOrderCode(source.getMerchantOrderCode());
        target.setConfirmNos(source.getSupplyConfirmNo());
        target.setSupplyReward(source.getSupplyReward());
        target.setSupplyShouldRackBack(source.getSupplyShouldRackBack());

        Integer status = EnumsConvertToCommonUtils.convertOrderStatus(source.getOrderStatus());
        if (status == null) {
            log.error("订单状态转换失败，请求参数：{}，响应结果：{}", JSONUtil.toJsonStr(req), JSONUtil.toJsonStr(source));
        }
        target.setSupplyResult(status);
    }

    /**
     * 查询订单状态  订单状态已经转换成内部状态
     */
    @Override
    public Response<QuerySupplyOrderResponse> querySupplyOrder(QuerySupplyOrderRequest request) {
        // 查询订单返回对象
        Response<QuerySupplyOrderResponse> response = Response.success(new QuerySupplyOrderResponse());
        QuerySupplyOrderResponse responseBody = response.getModel();
        String errorMsg = null;
        // 开始时间
        Date start = new Date();
        try {
            responseBody.setSupplyOrderCode(request.getSupplyOrderCode());
            responseBody.setSupplierOrderCode(request.getSupplierOrderCode());
            // 请求 shub 接口参数
            QueryOrderShubReq queryOrderShubReq;
            // 请求 shub 接口返回对象
            ResultX<QueryOrderShubResp> queryOrderDetailResultX;
            try {
                queryOrderShubReq = new QueryOrderShubReq();
                queryOrderShubReq.setMerchantOrderCode(request.getSupplyOrderCode());
                queryOrderShubReq.setSupplyOrderCode(request.getSupplierOrderCode());
                queryOrderShubReq.setQueryCache(false);// 不查询缓存，直接查询供应商
                // 设置请求id
                queryOrderShubReq.setRequestId(request.getRequestId());
                if (StrUtilX.isNotEmpty(request.getUserAccount()) && settingsConstant.getExpedia()) {
                    queryOrderShubReq.setToken(RedisTemplateX.get(RedisKey.TOKEN_RANDOM_ID + request.getUserAccount()));
                    queryOrderShubReq.setCustomerIp(RedisTemplateX.get(RedisKey.USER_IPADDRESS + request.getUserAccount()));
                }
                queryOrderDetailResultX = shubManager.queryOrderDetail(queryOrderShubReq);
            } catch (Exception e) {
                log.error("shub查询订单状态失败 {}，异常", JSONUtil.toJsonStr(request), e);
                response = Response.error(ReturnNoEnum._010704900.returnCode, ReturnNoEnum._010704900.returnDesc, responseBody);
                return response;
            }
            // 判断返回结果是否为空
            if (queryOrderDetailResultX == null) {
                response = Response.error(ReturnNoEnum._010704083.returnCode, ReturnNoEnum._010704083.returnDesc, responseBody);
                return response;
            }
            // 判断返回结果是否失败
            if (queryOrderDetailResultX.isError()) {
                response = Response.error(queryOrderDetailResultX.getCode(), queryOrderDetailResultX.getMsg(), responseBody);
                return response;
            }
            QueryOrderShubResp queryOrderShubResp = queryOrderDetailResultX.getData();
            if (queryOrderShubResp == null) {
                response = Response.error(ReturnNoEnum._010704082.returnCode, ReturnNoEnum._010704082.returnDesc, responseBody);
                return response;
            }
            // orderStatus 订单状态 Integer 0-未确认，1-已确认，2表示-不确认，3-已取消 4-取消待审;
            Integer orderStatus = queryOrderShubResp.getOrderStatus();
            if (orderStatus != null) {
                if (ShubOrderStatusEnum.CONFIRM.result == orderStatus) {// 确认
                    responseBody.setSupplyResult(OrderStatusEnum.CONFIRMED.no);
                    responseBody.setReason(OrderStatusEnum.CONFIRMED.desc);
                    responseBody.setConfirmationNumber(queryOrderShubResp.getSupplyConfirmNo());
                } else if (ShubOrderStatusEnum.NO_CONFIRM.result == orderStatus || ShubOrderStatusEnum.CANCEL.result == orderStatus) {// 不确认/已取消
                    responseBody.setSupplyResult(OrderStatusEnum.CANCELED.no);
                    responseBody.setReason(OrderStatusEnum.CANCELED.desc);
                } else if (ShubOrderStatusEnum.NEED_CONFIRM.result == orderStatus) {// 未确认
                    responseBody.setSupplyResult(OrderStatusEnum.CONFIRMING.no);
                    responseBody.setReason(OrderStatusEnum.CONFIRMING.desc);
                } else {
                    log.error("查询供应商 shub 订单状态返回状态未定义,request=" + JSONUtil.toJsonStr(queryOrderShubReq)
                            + ",result=" + JSONUtil.toJsonStr(queryOrderDetailResultX));
                }
            } else {
                log.error("查询供应商 shub 订单状态返回为空,request=" + JSONUtil.toJsonStr(queryOrderShubReq)
                        + ",result=" + JSONUtil.toJsonStr(queryOrderDetailResultX));
            }
            responseBody.setRefundFee(queryOrderShubResp.getRefundPrice());// 退订费
            responseBody.setRefundFeeCurrency(queryOrderShubResp.getRefundCurrency());// 退订费币种
        } catch (Exception e) {
            response = Response.error(ReturnNoEnum._010704900.returnCode, ReturnNoEnum._010704900.returnDesc, responseBody);
            errorMsg = e.getMessage();
            log.error("供应商 shub 查询订单状态异常，request=" + JSONUtil.toJsonStr(request), e);
        } finally {
            // 记录日志
            logSlsRecord(start, ShubConstant.QUERY_SUPPLY_ORDER, JSONUtil.toJsonStr(request), JSONUtil.toJsonStr(responseBody), response, errorMsg);
        }
        return response;
    }

    /**
     * 取消订单
     */
    @Override
    public Response<CancelOrderResponse> cancelSupplyOrder(CancelOrderRequest request) {
        // 取消订单返回对象
        Response<CancelOrderResponse> response = Response.success(new CancelOrderResponse());
        CancelOrderResponse responseBody = response.getModel();
        String errorMsg = null;
        // 开始时间
        Date start = new Date();
        try {
            responseBody.setSupplyOrderCode(request.getSupplyOrderCode());
            ResultX<CancelOrderShubResp> cancelOrderResultX;
            try {
                CancelOrderShubReq cancelOrderShubReq = new CancelOrderShubReq();
                cancelOrderShubReq.setMerchantOrderCode(request.getSupplyOrderCode());
                cancelOrderShubReq.setSupplyOrderCode(request.getSupplierOrderCode());
                cancelOrderShubReq.setCancelReason(request.getCancelReason());
                // 设置请求id
                cancelOrderShubReq.setRequestId(request.getRequestId());
                if (StrUtilX.isNotEmpty(request.getUserAccount()) && settingsConstant.getExpedia()) {
                    cancelOrderShubReq.setToken(RedisTemplateX.get(RedisKey.TOKEN_RANDOM_ID + request.getUserAccount()));
                    cancelOrderShubReq.setCustomerIp(RedisTemplateX.get(RedisKey.USER_IPADDRESS + request.getUserAccount()));
                }
                cancelOrderResultX = shubManager.cancelOrder(cancelOrderShubReq);
            } catch (Exception e) {
                log.error("shub取消订单失败 {}，系统异常", JSONUtil.toJsonStr(request), e);
                response = Response.error(ReturnNoEnum._010703900.returnCode, ReturnNoEnum._010703900.returnDesc);
                return response;
            }
            if (cancelOrderResultX == null) {
                response = Response.error(ReturnNoEnum._010703903.returnCode, ReturnNoEnum._010703903.returnDesc);
                return response;
            }
            if (cancelOrderResultX.isError()) {
                response = Response.error(cancelOrderResultX.getCode(), cancelOrderResultX.getMsg());
                return response;
            }
            if (cancelOrderResultX.getData() == null) {
                response = Response.error(ReturnNoEnum._010703082.returnCode, ReturnNoEnum._010703082.returnDesc);
                return response;
            }

            Integer orderStatus = EnumsConvertToCommonUtils.convertOrderStatus(cancelOrderResultX.getData().getOrderStatus());
            if (orderStatus == null) {
                response = Response.error(ReturnNoEnum._010703083.returnCode, ReturnNoEnum._010703083.returnDesc);
                return response;
            }
            if (orderStatus == OrderStatusEnum.CANCELED.no) {
                responseBody.setReason("取消成功");
            }
            responseBody.setSupplierOrderId(cancelOrderResultX.getData().getSupplyOrderCode());
            responseBody.setOrderConfirmationStatus(orderStatus);
            responseBody.setRefundFee(cancelOrderResultX.getData().getRefundPrice());
            responseBody.setRefundFeeCurrency(cancelOrderResultX.getData().getRefundCurrency());
        } catch (Exception e) {
            response = Response.error(ReturnNoEnum._010703900.returnCode, ReturnNoEnum._010703900.returnDesc, responseBody);
            errorMsg = e.getMessage();
            log.error("供应商 shub 取消订单异常，request=" + JSONUtil.toJsonStr(request), e);
        } finally {
            // 记录日志
            logSlsRecord(start, ShubConstant.CANCEL_SUPPLY_ORDER, JSONUtil.toJsonStr(request), JSONUtil.toJsonStr(responseBody), response, errorMsg);
        }
        return response;
    }

    /**
     * 接收订单状态推送
     */
    public ResultX<Object> orderStatusPush(OrderStatusPushShubReq request) {
        ResultX<Object> resultX = null;
        String errorMsg = null;
        // 开始时间
        Date start = new Date();
        try {
            // 校验参数
            resultX = checkOrderStatusPushParam(request);
            if (resultX != null) {
                return resultX;
            }
            // 订单状态 Integer 是 3-确认;4-拒绝;
            Integer orderStatus = request.getOrderStatus();

            SaveSupplyResultDTO dto = new SaveSupplyResultDTO();
            dto.setSupplyOrderCode(request.getMerchantOrderCode());
            dto.setSupplierOrderCode(request.getSupplyOrderCode());
            if (orderStatus == ShubOrderStatusEnum.CONFIRM.result) {// 确认
                dto.setConfirmationStatus(OrderStatusEnum.CONFIRMED.no);
            } else if (orderStatus == ShubOrderStatusEnum.NO_CONFIRM.result || orderStatus == ShubOrderStatusEnum.CANCEL.result) {// 不确认/取消
                dto.setConfirmationStatus(OrderStatusEnum.CANCELED.no);
            }
            dto.setRefundFee(request.getRefundPrice());
            dto.setRefundFeeCurrency(request.getRefundCurrency());
            dto.setConfirmationCode(request.getSupplyConfirmNo());
            dto.setOperator(Constant.SYSTEM);

            // 确认状态不为空
            if (dto.getConfirmationStatus() != null) {
                RedisTemplateX.lLeftPushAll(RedisKey.ORDER_RESULT_NOTIFY, Collections.singleton(JSON.toJSONString(dto)));
            }

            // 默认返回成功
            resultX = ResultX.success();
        } catch (Exception e) {
            resultX = ResultX.error(GlobalErrorCodeConstants.SYSTEM_ERROR);
            errorMsg = e.getMessage();
            log.error("shub订单状态通知异常，request={}", JSONUtil.toJsonStr(request), e);
        } finally {
            // 记录日志
            logSlsRecord(start, ShubConstant.ORDER_STATUS_PUSH, JSONUtil.toJsonStr(request), JSONUtil.toJsonStr(resultX), null, errorMsg);
        }
        return resultX;
    }

    /**
     * 自动同步入住明细任务
     */
    public void autoSyncCheckInfoTask(String param) {
        String errorMsg = null;
        String request = null;
        String result = null;
        // 开始时间
        Date start = new Date();
        try {
            // 当前时间
            String thisDate;
            // 判断参数是否为空，不为空取参数时间
            if (StrUtilX.isNotEmpty(param)) {
                thisDate = param;
            } else {
                thisDate = DateUtilX.dateToString(DateUtilX.getCurrentDate());
            }
            // 获取需要同步入住明细的供货单
            InBlankCheckInfoDTO inBlankCheckInfoDTO = new InBlankCheckInfoDTO();
            inBlankCheckInfoDTO.setThisDate(thisDate);
            request = JSONUtil.toJsonStr(inBlankCheckInfoDTO);
            Response<List<SupplyOrderDTO>> response = supplyOrderRemote.queryInBlankCheckInfoSupplyOrderCodeList(inBlankCheckInfoDTO);
            result = JSONUtil.toJsonStr(response);
            if (response.isSuccess() && CollUtilX.isNotEmpty(response.getModel())) {
                for (SupplyOrderDTO supplyOrderDTO : response.getModel()) {
                    String errorMsg2 = null;
                    String request2 = null;
                    String result2 = null;
                    String request3 = null;
                    String result3 = null;
                    // 开始时间
                    Date start2 = new Date();
                    try {
                        OrderCheckDetailsShubReq checkDetailsShubReq = new OrderCheckDetailsShubReq();
                        checkDetailsShubReq.setMerchantOrderCode(supplyOrderDTO.getSupplyOrderCode());
                        checkDetailsShubReq.setSupplyOrderCode(supplyOrderDTO.getSupplierOrderCode());
                        // 设置请求id
                        checkDetailsShubReq.setRequestId(UUID.randomUUID().toString());
                        request2 = JSONUtil.toJsonStr(checkDetailsShubReq);
                        ResultX<OrderCheckDetailsShubResp> resultX = shubManager.querySupplyOrderCodeCheckDetail(checkDetailsShubReq);
                        result2 = JSONUtil.toJsonStr(resultX);
                        if (resultX.isSuccess()) {
                            if (resultX.getData() != null) {
                                OrderCheckDetailInfoDTO detailInfoDTO = convertOrderCheckDetailInfoParam(resultX.getData());
                                request3 = JSONUtil.toJsonStr(detailInfoDTO);
                                // 新增入住明细
                                Response<Object> orderCheckDetailResponse = supplyOrderRemote.pushOrderCheckDetail(detailInfoDTO);
                                result3 = JSONUtil.toJsonStr(orderCheckDetailResponse);
                            }
                        } else {
                            log.error("自动同步入住明细任务，调用供应商接口查询供货单入住明细失败，request={}，resultX={}", JSONUtil.toJsonStr(checkDetailsShubReq), JSONUtil.toJsonStr(resultX));
                        }
                    } catch (Exception e) {
                        // 记录其他异常
                        errorMsg2 = e.getMessage();
                        log.error("自动同步入住明细任务，request={}", request2, e);
                    } finally {
                        // 记录SLS（Structured Logging Service）日志信息
                        Map<String, String> map = new HashMap<>();
                        map.put(SlsEnum.LEVEL.getType(), SlsLogLevelEnum.INFO.getLevel());
                        map.put(SlsEnum.NAME.getType(), "autoSyncCheckInfoTask-ToSupply");
                        map.put(SlsEnum.MESSAGE.getType(), errorMsg2 == null ? "none" : "异常信息：" + errorMsg2);

                        map.put("request", request2);
                        map.put("response", result2);
                        map.put("request1", request3);
                        map.put("response1", result3);
                        map.put("start", DateUtilX.dateToString(start2, DateUtilX.hour_ms_format));

                        slsLoggerUtil.saveLog(map, "自动同步入住明细任务", ShubConstant.SLS_SOURCE);
                    }
                }
            }
        } catch (Exception e){
            // 记录其他异常
            errorMsg = e.getMessage();
            log.error("自动同步入住明细任务异常，request={}", request, e);
        } finally {
            // 记录日志
            logSlsRecord(start, ShubConstant.AUTO_SYNC_CHECK_INFO, JSONUtil.toJsonStr(request), result, null, errorMsg);
        }
    }

    /**
     * 转换入住明细参数
     */
    private OrderCheckDetailInfoDTO convertOrderCheckDetailInfoParam(OrderCheckDetailsShubResp resp) {
        // 参数校验前置
        validateRequiredParams(resp);

        OrderCheckDetailInfoDTO detailInfoDTO = buildBaseInfo(resp);
        detailInfoDTO.setCheckDetails(processRoomCheckDetails(resp));

        return detailInfoDTO;
    }

    /**
     * 参数校验
     */
    private void validateRequiredParams(OrderCheckDetailsShubResp resp) {
        ParamValidator.validateNotEmpty(resp.getMerchantOrderCode(), ParamErrorEnum.EMPTY_PARAM_MERCHANTORDERCODE);
        ParamValidator.validateNotEmpty(resp.getSupplyOrderCode(), ParamErrorEnum.EMPTY_PARAM_SUPPLYORDERCODE);
        ParamValidator.validateNotEmpty(resp.getRoomCheckDetails(), ParamErrorEnum.EMPTY_PARAM_ROOMCHECKDETAILS);
    }

    /**
     * 构建基本信息
     */
    private OrderCheckDetailInfoDTO buildBaseInfo(OrderCheckDetailsShubResp resp) {
        OrderCheckDetailInfoDTO dto = new OrderCheckDetailInfoDTO();
        dto.setSupplyOrderCode(resp.getMerchantOrderCode());
        dto.setSupplierOrderCode(resp.getSupplyOrderCode());
        dto.setOperator("system(供应商同步)");
        dto.setIsSync(true);
        return dto;
    }

    /**
     * 处理房间入住明细
     */
    private List<OrderCheckDetails> processRoomCheckDetails(OrderCheckDetailsShubResp resp) {
        return resp.getRoomCheckDetails().stream()
                .map(item -> convertToOrderCheckDetails(item, resp.getCheckInState()))
                .collect(Collectors.toList());
    }

    /**
     * 转换房间入住明细
     */
    private OrderCheckDetails convertToOrderCheckDetails(RoomCheckDetail item, String orderLevelState) {
        OrderCheckDetails details = new OrderCheckDetails();
        details.setRoomNumber(item.getRoomNumber());
        details.setCheckInDate(DateUtilX.dateToString(item.getCheckInDate()));
        details.setCheckOutDate(DateUtilX.dateToString(item.getCheckOutDate()));

        details.setCheckInState(resolveCheckInState(item.getCheckInState(), orderLevelState));
        details.setGuestInfos(extractGuestNames(item.getGuestInfos()));
        details.setPriceItems(convertPriceItems(item.getMonthlyCheckInDetails()));

        return details;
    }

    /**
     * 解析入住状态
     */
    private String resolveCheckInState(String itemState, String orderLevelState) {
        String state = Optional.ofNullable(itemState)
                .orElse(orderLevelState);

        return CheckInStateConverter.convert(state)
                .orElseThrow(() -> new SysException(ParamErrorEnum.EMPTY_PARAM_CHECKINSTATE.getCode(), ParamErrorEnum.EMPTY_PARAM_CHECKINSTATE.getDesc()));
    }

    /**
     * 提取入住人姓名
     */
    private List<String> extractGuestNames(List<CheckInDetailGuestInfo> guests) {
        return Optional.ofNullable(guests)
                .orElseGet(Collections::emptyList)
                .stream()
                .map(CheckInDetailGuestInfo::getGuestName)
                .collect(Collectors.toList());
    }

    /**
     * 转换价格项
     */
    private List<OrderCheckDetailPriceItem> convertPriceItems(List<MonthlyCheckInDetail> monthlyDetails) {
        ParamValidator.validateNotEmpty(monthlyDetails, ParamErrorEnum.EMPTY_PARAM_ROOMCHECKDETAILS);

        return monthlyDetails.stream()
                .map(this::buildPriceItem)
                .collect(Collectors.toList());
    }

    /**
     * 构建价格项
     */
    private OrderCheckDetailPriceItem buildPriceItem(MonthlyCheckInDetail priceItem) {
        OrderCheckDetailPriceItem item = new OrderCheckDetailPriceItem();
        item.setSaleDate(DateUtilX.dateToString(priceItem.getSaleDate()));
        item.setSalePrice(priceItem.getSalePrice());
        item.setRefundPrice(priceItem.getRefundPrice());
        return item;
    }

    /**
     * 接收入住明细
     */
    public ResultX<Object> acceptCheckInfo(OrderCheckDetailInfoDTO request) {
        String errorMsg = null;
        String result = null;
        // 开始时间
        Date start = new Date();
        ResultX<Object> resultX = null;
        try {
            if (StrUtilX.isEmpty(request.getSupplyOrderCode())) {
                throw new SysException(ParamErrorEnum.EMPTY_PARAM_SUPPLYORDERCODE);
            }
            if (StrUtilX.isEmpty(request.getSupplierOrderCode())) {
                throw new SysException(ParamErrorEnum.EMPTY_PARAM_SUPPLIERORDERCODE);
            }
            if (CollUtilX.isEmpty(request.getCheckDetails())) {
                throw new SysException(ParamErrorEnum.EMPTY_PARAM_CHECKDETAILS);
            }
            request.setIsSync(true);// 同步操作
            request.setOperator("system(订单通同步)");
            Response<Object> response = supplyOrderRemote.pushOrderCheckDetail(request);
            result = JSONUtil.toJsonStr(response);
            if (response.isSuccess()) {
                resultX = ResultX.success();
            } else {
                resultX = ResultX.error(response.getFailCode(), response.getFailReason());
            }
        } catch (Exception e) {
            // 记录其他异常
            errorMsg = e.getMessage();
            resultX = ResultX.error("01", e.getMessage());
            log.error("接收入住明细异常，request={}", JSONUtil.toJsonStr(request), e);
        } finally {
            // 记录日志
            logSlsRecord(start, ShubConstant.ACCEPT_CHECK_INFO, JSONUtil.toJsonStr(request), result, null, errorMsg);
        }
        return resultX;
    }

    /**
     * 校验接收订单状态推送参数
     */
    private ResultX<Object> checkOrderStatusPushParam(OrderStatusPushShubReq req) {
        ErrorCode paramNullError = GlobalErrorCodeConstants.PARAM_NULL_ERROR;
        if (StrUtilX.isEmpty(req.getMerchantOrderCode())) {
            return ResultX.error(paramNullError.getCode(), paramNullError.getMsg() + "订单号");
        } else if (req.getOrderStatus() == null) {
            return ResultX.error(paramNullError.getCode(), paramNullError.getMsg() + "订单状态");
        }
        return null;
    }

    /**
     * 记录日志
     */
    private void logSlsRecord(Date start, String name, String request, String resp, Response<?> response, String errorMsg) {
        Map<String, String> logData = new LinkedHashMap<>();
        logData.put(SlsEnum.LEVEL.getType(), SlsLogLevelEnum.INFO.getLevel());
        logData.put(SlsEnum.NAME.getType(), name);
        logData.put(SlsEnum.MESSAGE.getType(), errorMsg != null ? "异常信息：" + errorMsg : "none");
        logData.put("request", request);
        logData.put("response", resp);
        logData.put("start", DateUtilX.dateToString(start, DateUtilX.hour_ms_format));
        if (Objects.nonNull(response)) {
            logData.put("status", response.isSuccess() ? "1" : "0");
        }

        slsLoggerUtil.saveLog(logData, name, ShubConstant.SLS_SOURCE);
    }
}
