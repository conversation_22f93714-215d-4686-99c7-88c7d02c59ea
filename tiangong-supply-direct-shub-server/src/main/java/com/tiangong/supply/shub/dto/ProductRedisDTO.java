package com.tiangong.supply.shub.dto;

import lombok.Data;

import java.util.List;

/**
 * 需要存入缓存的产品信息
 */
@Data
public class ProductRedisDTO {
    /**
     * 售卖信息
     */
    private SaleItemDetail saleItemDetail;

    /**
     * 床型详细信息
     */
    private List<BedTypesDetails> bedTypeDetails;

    /**
     * 支付类型
     * @see com.tiangong.enums.PayMethodEnum
     */
    private Integer payMethod;

    /**
     * 供应商房型id
     */
    private String spRoomId;

    /**
     * 供应商房型名称
     */
    private String spRoomName;

    /**
     * 提示信息
     */
    private List<ShubTipsDTO> tips;
}
