package com.tiangong.supply.shub.controller;

import cn.hutool.json.JSONUtil;
import com.tiangong.cloud.common.anno.AnonymousAccess;
import com.tiangong.common.Response;
import com.tiangong.enums.SlsEnum;
import com.tiangong.enums.SlsLogLevelEnum;
import com.tiangong.supply.direct.remote.dto.ProductMiddleDto;
import com.tiangong.supply.direct.remote.request.QueryProductInfoRequest;
import com.tiangong.supply.shub.constant.ShubConstant;
import com.tiangong.supply.shub.service.ShubProductService;
import com.tiangong.util.CollUtilX;
import com.tiangong.util.DateUtilX;
import com.tiangong.util.SlsLoggerUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 产品
 * <AUTHOR>
 */
@RestController
@Slf4j
@RequestMapping("/shub/product")
public class ShubProductController {

    @Autowired
    private ShubProductService shubProductService;

    @Autowired
    private SlsLoggerUtil slsLoggerUtil;

    /**
     * 实时查询产品列表
     */
    @AnonymousAccess
    @PostMapping("/queryProductList")
    public Response<List<ProductMiddleDto>> queryProductList(@RequestBody QueryProductInfoRequest request) {
        Response<List<ProductMiddleDto>> response = null;
        String errorMsg = null;
        // 开始时间
        Date start = new Date();
        try {
            response = Response.success(shubProductService.queryProductList(request));
            // 过滤空数据
            if (CollUtilX.isNotEmpty(response.getModel())) {
                List<ProductMiddleDto> collect = response.getModel().stream().filter(Objects::nonNull).collect(Collectors.toList());
                response.setModel(collect);
            }
        } catch (Exception e) {
            errorMsg = e.getMessage();
            log.error("查询产品异常，request={}，e:", JSONUtil.toJsonStr(request), e);
        } finally {
            // 记录 sls 日志
            Map<String, String> map = new HashMap<>();
            map.put(SlsEnum.LEVEL.getType(), SlsLogLevelEnum.INFO.getLevel());
            map.put(SlsEnum.NAME.getType(), ShubConstant.QUERY_PRODUCT_LIST);
            map.put(SlsEnum.MESSAGE.getType(), errorMsg == null ? "none" : "异常信息：" + errorMsg);

            map.put("request", JSONUtil.toJsonStr(request));
            map.put("response", JSONUtil.toJsonStr(response));
            map.put("start", DateUtilX.dateToString(start, DateUtilX.hour_ms_format));
            if (response != null && response.isSuccess()) {
                map.put("status", "1");
            } else {
                map.put("status", "0");
            }
            slsLoggerUtil.saveLog(map, ShubConstant.QUERY_PRODUCT_LIST, ShubConstant.SLS_SOURCE);
        }
        return response;
    }

    /**
     * 查询产品信息（根据产品id查询）
     */
    @AnonymousAccess
    @PostMapping("/queryProductInfo")
    public Response<List<ProductMiddleDto>> queryProductInfo(@RequestBody QueryProductInfoRequest request) {
        Response<List<ProductMiddleDto>> response = null;
        String errorMsg = null;
        // 开始时间
        Date start = new Date();
        try {
            response = shubProductService.queryProductInfo(request);
        } catch (Exception e) {
            errorMsg = e.getMessage();
            log.error("查询产品异常，request={}，e:", JSONUtil.toJsonStr(request), e);
        } finally {
            // 记录 sls 日志
            Map<String, String> map = new HashMap<>();
            map.put(SlsEnum.LEVEL.getType(), SlsLogLevelEnum.INFO.getLevel());
            map.put(SlsEnum.NAME.getType(), "queryProductInfo");
            map.put(SlsEnum.MESSAGE.getType(), errorMsg == null ? "none" : "异常信息：" + errorMsg);

            map.put("request", JSONUtil.toJsonStr(request));
            map.put("response", JSONUtil.toJsonStr(response));
            map.put("start", DateUtilX.dateToString(start, DateUtilX.hour_ms_format));
            if (response != null && response.isSuccess()) {
                map.put("status", "1");
            } else {
                map.put("status", "0");
            }
            slsLoggerUtil.saveLog(map, "queryProductInfo", ShubConstant.SLS_SOURCE);
        }
        return response;
    }
}
