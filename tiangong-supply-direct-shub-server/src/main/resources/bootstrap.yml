# 加载顺序高于 application.yml
spring:
  profiles:
    active: pro
  application:
    name: tiangong-supply-direct-shub-server
  jackson:
    time-zone: GMT+8
  main:
    allow-bean-definition-overriding: true

server:
  port: 14007

mybatis:
  mapper-locations: classpath*:/mappers/*Mapper.xml
  type-aliases-package: com.tiangong.supply.shub.domain.*

mapper:
  mappers: com.tiangong.dto.common.MyMapper
  not-empty: false
  identity: MYSQL

pagehelper:
  helperDialect: mysql
  reasonable: true
  supportMethodsArguments: true
  params: count=countSql

ribbon:
  ReadTimeout: 60000
  ConnectTimeout: 60000
  ServerListRefreshInterval: 1000


logging:
  level:
    com:
      alibaba:
        nacos: info

logstash:
  url: ************:5044

#xxljob配置
xxl:
  job:
    admin:
      addresses: http://127.0.0.1:18880/xxl-job-admin/
    accessToken: default_token
    executor:
      appname: tiangong-supply-direct-shub-server
      address:
      logpath: ./applogs/tiangong-supply-direct-shub-server
      logretentiondays: 3
      port: 0