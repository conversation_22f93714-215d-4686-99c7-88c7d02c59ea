package com.tiangong.hotel.req;


import com.tiangong.dto.common.BaseRequest;
import lombok.Data;

import java.util.Date;

import java.io.Serializable;
import java.util.Set;

/**
 * 数据字典类别表
 * 请求参数
 *
 * <AUTHOR>
 * @email
 * @date 2022-09-15 02:00:31
 */
@Data
public class TypeInfoReq extends BaseRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     *
     */
    private Integer id;
    /**
     * 类别编码 必填
     */
    private String typeCode;
    /**
     * 类别名称
     */
    private String typeName;
    /**
     * 类型 详情见 TypeEnum
     */
    private Integer type;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private Date createdDt;
    /**
     * 修改人
     */
    private String updatedBy;
    /**
     * 修改时间
     */
    private Date updatedDt;
    /**
     * 是否删除(0 否，1 是)
     */
    private Integer deleted;

    /**
     * 类型code
     */
    private Set<String> typeCodeList;

    /**
     * 类别名称
     */
    private Set<String> typeNameList;
}