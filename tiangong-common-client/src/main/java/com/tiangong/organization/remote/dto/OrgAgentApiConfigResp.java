package com.tiangong.organization.remote.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 分销api配置表
 * 返回参数
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-18 12:44:15
 */
@Data
public class OrgAgentApiConfigResp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * Id
     */
    private Integer id;
    /**
     * 客户编码
     */
    private String agentCode;
    /**
     * 密钥
     */
    private String secretKey;
    /**
     * 订单通知接口
     */
    private String orderStatusNotifyUrl;
    /**
     * 发票通知接口
     */
    private String invoiceNotifyUrl;
    /**
     * 订单退款接口
     */
    private String orderRefundNotifyUrl;

    /**
     * 入住明细推送接口
     */
    private String orderCheckDetailNotifyUrl;

    /**
     * 订单推送接口
     */
    private String orderPushNotifyUrl;

    /**
     * 担保条款通知接口
     */
    private String warrantiesNotifyUrl;

    /**
     * 订单推送通知推送接口
     */
    private String orderCheckoutNotifyUrl;

    /**
     * 合作状态（1-合作中 0-暂停合作）
     */
    private Integer cooperatingState;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private Date createdDt;
    /**
     * 修改人
     */
    private String updatedBy;
    /**
     * 修改时间
     */
    private Date updatedDt;
    /**
     * 删除状态
     */
    private Integer deleted;

    /**
     * 合作商编码
     */
    private String partnerCode;

    /**
     * 启用状态 0禁用 1启用
     */
    private  Integer  availableStatus;

    /**
     * 原始协议价输出开关
     */
    private Integer originalProtocolPriceSwitch;

    /**
     * 30天报价输出开关
     */
    private Integer quoteSwitch;

    /**
     * 评分
     */
    private Double rating;

    /**
     * 数据加密开关
     */
    private Integer dataEncryptionSwitch;

    /**
     * 私钥
     */
    private String opsSecretKey;

    /**
     * 客户公钥
     */
    private String customerPublicKey;

    /**
     * 天宫公钥
     */
    private String tiangongPublicKey;
}