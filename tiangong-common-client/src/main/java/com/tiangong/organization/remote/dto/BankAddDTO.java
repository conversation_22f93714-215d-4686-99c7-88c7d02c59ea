package com.tiangong.organization.remote.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019/6/21 21:27
 **/
@Data
public class BankAddDTO implements Serializable {
    /**
     * 银行卡Id
     */
    private Integer bankId;
    /**
     * 银行卡名称
     */
    private String bankName;
    /**
     * 开户名
     */
    private String accountName;
    /**
     * 账号
     */
    private String accountNumber;
    /**
     * 行号
     */
    private String bankCode;

    /**
     * 银行卡状态
     */
    private Integer bankStatus;

    /**
     * 机构编码
     */
    private String orgCode;
    /**
     * 机构类型0-供应商，1-客户，2-运营商
     */
    private Integer orgType;

    /**
     * 数据创建时间
     */
    private Date createdDt;

    /**
     * 数据创建人
     */
    private String createdBy;
    /**
     * 数据修改人
     */
    private String updatedBy;
    /**
     * 数据修改时间
     */
    private Date updatedDt;

    /**
     * 账号类型：1企业账号 2个人账号
     */
    private Integer accountType;

    /**
     * 银行币种
     */
    private Integer bankCurrency;

    /**
     * 公司发票主体id（t_invoice表）
     */
    private Integer companyInvoiceId;

    /**
     * 发票类型：1普票2专票
     */
    private Integer invoiceType;

    /**
     * 发票税点
     */
    private BigDecimal invoiceRatio;

    /**
     * 余额
     */
    private BigDecimal balance;

    /**
     * 开户行类型：0网商银行 1其他
     */
    private Integer bankType;
}
