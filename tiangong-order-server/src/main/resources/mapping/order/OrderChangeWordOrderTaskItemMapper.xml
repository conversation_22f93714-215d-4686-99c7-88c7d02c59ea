<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.tiangong.order.mapper.OrderChangeWordOrderTaskItemMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap id="BaseResultMap" type="com.tiangong.order.domain.OrderChangeWordOrderTaskItemPO">
        <result property="itemId" column="item_id"/>
        <result property="taskOrderCode" column="task_order_code"/>
        <result property="roomIndex" column="room_index"/>
        <result property="changeDate" column="change_date"/>
        <result property="guestName" column="guest_name"/>
        <result property="agentGuestName" column="agent_guest_name"/>
        <result property="createdBy" column="created_by"/>
        <result property="createdDt" column="created_dt"/>
        <result property="updatedBy" column="updated_by"/>
        <result property="updatedDt" column="updated_dt"/>
    </resultMap>

    <resultMap id="OrderChangeWordOrderTaskItemMap" type="com.tiangong.order.domain.resp.OrderChangeWordOrderTaskItemResp">
        <result property="itemId" column="item_id"/>
        <result property="taskOrderCode" column="task_order_code"/>
        <result property="roomIndex" column="room_index"/>
        <result property="changeDate" column="change_date"/>
        <result property="guestName" column="guest_name"/>

        <collection property="priceItemList" select="com.tiangong.order.mapper.OrderChangeWordOrderTaskPriceItemMapper.selectOrderChangeWordOrderTaskPriceItemList"
                    column="item_id" ofType="com.tiangong.order.domain.resp.OrderChangeWordOrderTaskPriceItemResp">
            <result property="itemId" column="item_id"/>
            <result property="saleDate" column="sale_date"/>
            <result property="roomPrice" column="room_price"/>
            <result property="refundPrice" column="refund_price"/>
        </collection>
    </resultMap>

    <select id="selectOrderChangeWordOrderTaskItemList" resultMap="OrderChangeWordOrderTaskItemMap">
        select
            item_id,
            task_order_code,
            room_index,
            change_date,
            guest_name,
            agent_guest_name
        from t_order_change_word_order_task_item
        where task_order_code = #{task_order_code}
    </select>

</mapper>