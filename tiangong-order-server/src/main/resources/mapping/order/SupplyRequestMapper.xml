<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tiangong.order.mapper.SupplyRequestMapper">
    <resultMap id="BaseResultMap" type="com.tiangong.order.domain.SupplyRequestPO">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="supply_order_id" jdbcType="INTEGER" property="supplyOrderId"/>
        <result column="supply_order_type" jdbcType="INTEGER" property="supplyOrderType"/>
        <result column="sending_type" jdbcType="INTEGER" property="sendingType"/>
        <result column="sending_result" jdbcType="INTEGER" property="sendingResult"/>
        <result column="merchant_remark" jdbcType="VARCHAR" property="merchantRemark"/>
        <result column="this_confirmation_status" jdbcType="INTEGER" property="thisConfirmationStatus"/>
        <result column="this_confirmation_code" jdbcType="VARCHAR" property="thisConfirmationCode"/>
        <result column="this_supplier_confirmer" jdbcType="VARCHAR" property="thisSupplierConfirmer"/>
        <result column="this_refund_fee" jdbcType="DECIMAL" property="thisRefundFee"/>
        <result column="this_refused_reason" jdbcType="VARCHAR" property="thisRefusedReason"/>
        <result column="this_confirmation_remark" jdbcType="VARCHAR" property="thisConfirmationRemark"/>
        <result column="created_by" jdbcType="VARCHAR" property="createdBy"/>
        <result column="created_dt" jdbcType="TIMESTAMP" property="createdDt"/>
        <result column="updated_by" jdbcType="VARCHAR" property="updatedBy"/>
        <result column="updated_dt" jdbcType="TIMESTAMP" property="updatedDt"/>
    </resultMap>
</mapper>