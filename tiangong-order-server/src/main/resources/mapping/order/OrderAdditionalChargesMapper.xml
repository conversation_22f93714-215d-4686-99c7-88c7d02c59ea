<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.tiangong.order.mapper.OrderAdditionalChargesMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap id="BaseResultMap" type="com.tiangong.order.domain.OrderAdditionalChargesPO">
        <result property="id" column="id"/>
        <result property="additionalChargesType" column="additional_charges_type"/>
        <result property="additionalChargesName" column="additional_charges_name"/>
        <result property="orderId" column="order_id"/>
        <result property="orderCode" column="order_code"/>
        <result property="additionalChargesDate" column="additional_charges_date"/>
        <result property="quantity" column="quantity"/>
        <result property="additionalCharges" column="additional_charges"/>
        <result property="createdBy" column="created_by"/>
        <result property="createdDt" column="created_dt"/>
        <result property="updatedBy" column="updated_by"/>
        <result property="updatedDt" column="updated_dt"/>
    </resultMap>


</mapper>