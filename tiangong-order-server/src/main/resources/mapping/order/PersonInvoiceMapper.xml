<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tiangong.order.mapper.PersonInvoiceMapper">

    <resultMap id="PersonInvoiceQueryRespMap" type="com.tiangong.order.dto.PersonInvoiceQueryRespDTO">
        <id column="id" property="id"/>
        <result column="orderCode" property="orderCode"/>
        <result column="agentName" property="agentName"/>
        <result column="hotelName" property="hotelName"/>
        <result column="guest" property="guest"/>
        <result column="startTime" property="startTime"/>
        <result column="endTime" property="endTime"/>
        <result column="orderAmt" property="orderAmt"/>
        <result column="receivedAmt" property="receivedAmt"/>
        <result column="isMixedPay" property="isMixedPay"/>
        <result column="invoiceAmount" property="invoiceAmount"/>
        <result column="invoiceDate" property="invoiceDate"/>
        <result column="sendStatus" property="sendStatus"/>
        <result column="invoiceStatus" property="invoiceStatus"/>
        <result column="invoiceNo" property="invoiceNo"/>
        <result column="invoiceFileName" property="invoiceFileName"/>
        <result column="invoiceUrls" property="invoiceUrls"/>
    </resultMap>

    <select id="queryOrderInvoiceList" resultMap="PersonInvoiceQueryRespMap">
        SELECT
            oi.id,
            o.id as orderId,
            o.order_code as orderCode,
            o.agent_name as agentName,
            o.hotel_name as hotelName,
            o.guest,
            DATE_FORMAT(o.start_date, '%Y-%m-%d') as startTime,
            DATE_FORMAT(o.end_date, '%Y-%m-%d') as endTime,
            o.order_amt as orderAmt,
            o.is_mixed_pay as isMixedPay,
            oi.invoice_amount as invoiceAmount,
            oi.invoice_date as invoiceDate,
            oi.created_dt as applyDate,
            oi.send_status as sendStatus,
            oi.invoice_status as invoiceStatus,
            oi.invoice_no as invoiceNo,
            oi.invoice_file_name as invoiceFileName,
            oi.invoice_urls as invoiceUrls,
            o.sale_currency as saleCurrency
        FROM
            o_order_invoice oi
        left JOIN
            o_order o ON oi.order_code = o.order_code
        <where>
            <if test="request.orderCode != null and request.orderCode != ''">
                AND o.order_code = #{request.orderCode}
            </if>
            <if test="request.agentCode != null and request.agentCode != ''">
                AND o.agent_code = #{request.agentCode}
            </if>
            <if test="request.guest != null and request.guest != ''">
                AND o.guest LIKE CONCAT('%', #{request.guest}, '%')
            </if>
            <if test="request.hotelId != null and request.hotelId != ''">
                AND o.hotel_id = #{request.hotelId}
            </if>
            <!-- 查询口径 0-下单日期、1-入住日期、2-离店日期-->
            <if test="request.startDate != null and request.startDate != '' and request.endDate != null and request.endDate != ''">
                <choose>
                    <when test="request.dateQueryType != null and request.dateQueryType == 0">
                        AND date_format(o.created_dt, '%Y-%m-%d') BETWEEN #{request.startDate} AND #{request.endDate}
                    </when>
                    <when test="request.dateQueryType != null and request.dateQueryType == 1">
                        AND date_format(o.start_date, '%Y-%m-%d') BETWEEN #{request.startDate} AND #{request.endDate}
                    </when>
                    <when test="request.dateQueryType != null and request.dateQueryType == 2">
                        AND date_format(o.end_date, '%Y-%m-%d') BETWEEN #{request.startDate} AND #{request.endDate}
                    </when>
                </choose>
            </if>
            <if test="request.startApplyDate != null and request.startApplyDate != ''">
                AND oi.apply_date &gt;= #{request.startApplyDate}
            </if>
            <if test="request.endApplyDate != null and request.endApplyDate != ''">
                AND oi.apply_date &lt;= #{request.endApplyDate}
            </if>
            <if test="request.sendStatus != null">
                AND oi.send_status = #{request.sendStatus}
            </if>
            <if test="request.invoiceStatus != null">
                AND oi.invoice_status = #{request.invoiceStatus}
            </if>
        </where>
        ORDER BY oi.id DESC
    </select>

    <select id="queryOrderReceivedAmt" resultMap="PersonInvoiceQueryRespMap">
        SELECT
            f.order_id as orderId,
            f.received_amt as receivedAmt
        FROM
            o_order_finance f
        WHERE
            f.order_id IN
            <foreach collection="orderIds" item="orderId" open="(" separator="," close=")">
                #{orderId}
            </foreach>
    </select>
</mapper>
