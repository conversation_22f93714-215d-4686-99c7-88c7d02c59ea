<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tiangong.order.mapper.SupplyProductPriceMapper">
    <resultMap id="BaseResultMap" type="com.tiangong.order.domain.SupplyProductPricePO">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="supply_product_id" jdbcType="INTEGER" property="supplyProductId"/>
        <result column="supply_order_id" jdbcType="INTEGER" property="supplyOrderId"/>
        <result column="sale_date" jdbcType="DATE" property="saleDate"/>
        <result column="sale_price" jdbcType="DECIMAL" property="salePrice"/>
        <result column="base_price" jdbcType="DECIMAL" property="basePrice"/>
        <result column="debucted_quota" jdbcType="INTEGER" property="debuctedQuota"/>
    </resultMap>

    <select id="querySupplyOrderPriceList" parameterType="java.lang.Integer"
            resultType="com.tiangong.order.remote.response.PriceResponseDTO">
        SELECT
        sp.sale_date saleDate,
        SUM(sp.base_price) basePrice
        FROM o_supply_product_price sp
        WHERE sp.sale_date is not null and sp.supply_order_id=#{supplyOrderId}
        GROUP BY sp.sale_date
        ORDER BY sp.sale_date
    </select>

    <select id="selectSupplyOrderPriceListByOrderCode" resultType="com.tiangong.order.remote.response.PriceResponseDTO">
        SELECT
            sp.room_number roomNumber,
            sp.sale_date saleDate,
            sp.base_price basePrice
        FROM o_supply_product_price sp
        LEFT JOIN o_supply_order so ON so.id = sp.supply_order_id
        LEFT JOIN o_order oo ON oo.id = so.order_id
        WHERE sp.sale_date is not null and oo.order_code=#{orderCode}
    </select>
</mapper>