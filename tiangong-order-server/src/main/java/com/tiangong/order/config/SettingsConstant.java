package com.tiangong.order.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * 订单退款配置
 *
 * <AUTHOR>
 */
@Data
@Configuration
@RefreshScope// 实时更新nacos配置
public class SettingsConstant {

    /**
     * 平安航旅接口调用私钥
     */
    @Value("${order.refund.hanglvShaPrivateKey}")
    private String hanglvShaPrivateKey;

    /**
     * 平安航旅接口调用地址
     */
    @Value("${order.refund.hanglvUrl}")
    private String hanglvUrl;

    /**
     * 普通客户接口调用
     */
    @Value("${order.refund.orderRefundPushURL}")
    private String orderRefundPushURL;

    /**
     * 每次消费计算热度预订间夜分数酒店数量
     */
    @Value("${consumer.calculateRoomNightScore.count}")
    private Integer consumerCalculateRoomNightScoreCount;

    /**
     * 天宫系统地址
     */
    @Value("${tiangongUrl.url}")
    private String tiangongUrl;

    /**
     * vcc主域名
     */
    @Value("${vcc.url.domain}")
    private String vccDomainUrl;

    /**
     * 查询付款主体接口
     */
    @Value("${vcc.url.paySubjectListUrl}")
    private String paySubjectListUrl;

    /**
     * 生成VCC卡号接口
     */
    @Value("${vcc.url.generateVirtualCardUrl}")
    private String generateVirtualCardUrl;

    /**
     * 取消业务单接口
     */
    @Value("${vcc.url.cancelNoticeUrl}")
    private String cancelNoticeUrl;

    /**
     * 获取vcc账号配置信息url
     */
    @Value("${vcc.url.queryVccPayConfigUrl}")
    private String queryVccPayConfigUrl;

    /**
     * mastercard图标url
     */
    @Value("${vcc.image.mastercardImageUrl}")
    private String mastercardImageUrl;

    /**
     * visa图标url
     */
    @Value("${vcc.image.visaImageUrl}")
    private String visaImageUrl;

    /**
     * amex图标url
     */
    @Value("${vcc.image.amexImageUrl}")
    private String amexImageUrl;

    /**
     * vcc卡可用数量
     */
    @Value("${vcc.cardAvailableCount}")
    private Integer VccCardAvailableCount;

    /**
     * hdp待确认订单未支付超时自动取消订单 单位：分钟
     */
    @Value("${hdp.autoCancelUnconfirmedOrderMinute}")
    private Integer hdpAutoCancelUnconfirmedOrderMinute;

    /**
     * 自动开票启用状态(0 禁用, 1 启用)
     */
    @Value("${invoice.autoInvoice.enabled:0}")
    private Integer autoInvoiceEnabled;

    /**
     * 百望云接口地址
     */
    @Value("${invoice.baiwang.url:xxx}")
    private String baiwangUrl;

    /**
     * 百望云用户名
     */
    @Value("${invoice.baiwang.username:xxx}")
    private String baiwangUsername;

    /**
     * 百望云密码
     */
    @Value("${invoice.baiwang.password:xxx}")
    private String baiwangPassword;

    /**
     * 百望云appKey
     */
    @Value("${invoice.baiwang.appKey:xxx}")
    private String baiwangAppKey;

    /**
     * 百望云appSecret
     */
    @Value("${invoice.baiwang.appSecret:xxx}")
    private String baiwangAppSecret;

    /**
     * 百望云用户盐值
     */
    @Value("${invoice.baiwang.salt:xxx}")
    private String baiwangSalt;
    /**
     * 百望云销售方税号
     */
    @Value("${invoice.baiwang.taxNo:xxx}")
    private String baiwangTaxNo;
    /**
     * 百望云销售方名称
     */
    @Value("${invoice.baiwang.orgName:xxx}")
    private String baiwangOrgName;
    /**
     * 百望云模拟开票开关
     */
    @Value("${invoice.baiwang.mockIssueEnable:false}")
    private Boolean baiwangMockIssue;
    /**
     * 百望云模拟开票失败
     */
    @Value("${invoice.baiwang.mockIssueFail:false}")
    private Boolean baiwangMockIssueFail;
    /**
     * 百望云模拟全版下载开关
     */
    @Value("${invoice.baiwang.mockDownLoadPdfEnable:false}")
    private Boolean baiwangMockDownLoadPdf;
    /**
     * 百望云模拟开票详情失败
     */
    @Value("${invoice.baiwang.mockDownLoadPdfFail:false}")
    private Boolean baiwangMockDownLoadPdfFail;
}
