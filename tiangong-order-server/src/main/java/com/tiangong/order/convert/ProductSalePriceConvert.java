package com.tiangong.order.convert;

import com.tiangong.dto.order.OrderGuestDTO;
import com.tiangong.dto.product.PriceInfoDetail;
import com.tiangong.dto.product.RoomTaxDetail;
import com.tiangong.dto.product.response.BedInfoDto;
import com.tiangong.dto.product.response.TipInfosDTO;
import com.tiangong.file.resp.FileResp;
import com.tiangong.finance.remote.statement.request.CreateOrderStatementTaskDTO;
import com.tiangong.finance.remote.statement.response.ProtocolOrderSettlementCostResponseDTO;
import com.tiangong.finance.remote.workorder.request.WorkOrderAttchDTO;
import com.tiangong.order.domain.*;
import com.tiangong.order.domain.req.AddOrderFileReq;
import com.tiangong.order.dto.NonVccAutoBillConfigDTO;
import com.tiangong.order.remote.dto.OrderCheckDetailDTO;
import com.tiangong.order.remote.dto.OrderCheckInfoDTO;
import com.tiangong.order.remote.request.*;
import com.tiangong.order.remote.response.*;
import com.tiangong.organization.remote.dto.AgentAccountConfig;
import com.tiangong.organization.remote.dto.AgentSelectDTO;
import com.tiangong.product.dto.ProductSalePriceDTO;
import com.tiangong.product.dto.ProductSalePriceDetailDTO;
import com.tiangong.product.dto.ProductSalePriceItemDTO;
import com.tiangong.supply.direct.remote.request.RoomGuestNumber;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @program: tiangong
 * @ClassName ProductSalePriceConvert
 * @description:
 * @author: 湫
 * @create: 2024/09/27/ 20:01
 * @Version 1.0
 **/

@Mapper
public interface ProductSalePriceConvert {

    ProductSalePriceConvert INSTANCE = Mappers.getMapper(ProductSalePriceConvert.class);

    ProductSalePriceDTO convert(ProductSalePriceDetailDTO productSalePriceDetailDTO);


    default PriceInfoDetail productSalePriceItemDTOToPriceInfoDetail(ProductSalePriceItemDTO productSalePriceItemDTO) {
        if ( productSalePriceItemDTO == null ) {
            return null;
        }

        PriceInfoDetail priceInfoDetail = new PriceInfoDetail();
        priceInfoDetail.setDate( productSalePriceItemDTO.getSaleDate() );
        priceInfoDetail.setBaseCurrency( productSalePriceItemDTO.getBaseCurrency());
        priceInfoDetail.setSettlementCurrency( productSalePriceItemDTO.getSettlementCurrency() );
        priceInfoDetail.setSalePrice( productSalePriceItemDTO.getSalePrice() );
        priceInfoDetail.setBasePrice( productSalePriceItemDTO.getBasePrice() );
        priceInfoDetail.setTaxDetail( productSalePriceItemDTO.getTaxDetail() );
        priceInfoDetail.setBreakfastType( productSalePriceItemDTO.getBreakfastType() );
        priceInfoDetail.setBreakfastNum( productSalePriceItemDTO.getBreakfastNum() );
        priceInfoDetail.setQuotaNum( productSalePriceItemDTO.getQuotaNum() );
        priceInfoDetail.setRoomStatus( productSalePriceItemDTO.getRoomStatus() );
        priceInfoDetail.setCanOverDraft(productSalePriceItemDTO.getOverDraftStatus());
        return priceInfoDetail;
    }

    List<TipInfosDTO> TipInfosDTOConvert(List<TipInfosDTO> tips);

    com.tiangong.dto.product.TaxDetailDto TaxDetailDtoConvert(RoomTaxDetail roomTaxDetail);

    PriceInfoDetail PriceInfoDetailConvert(PriceInfoDetail preBookSaleItem);

//    OrderPO OrderPOConvert(AddManualOrderDTO request);

//    OrderExtendPO orderExtendPOConvert(AddManualOrderDTO request);

//    SupplyOrderPO supplyOrderPOConvert(AddManualOrderDTO request);

    SupplyOrderFinancePO supplyRewardFinancePOConvert(SupplyOrderFinancePO supplyOrderFinancePO);

//    SupplyProductPO SupplyProductPOConvert(AddManualOrderDTO request);

    List<com.tiangong.supply.direct.remote.dto.BedInfoDto> bedInfoDtoConvert(List<BedInfoDto> bedInfos);

    SupplyOrderFinancePO supplyRebateFinancePOConvert(SupplyOrderFinancePO supplyRewardFinancePO);

    List<com.tiangong.dto.product.request.RoomGuestNumber> roomGuestNumberConvert(List<RoomGuestNumber> roomGuestNumbers);

//    ProductSalePriceResultDTO productSalePriceResultDTO(ProductSalePriceDTO productSalePriceDTO);

    GuestDTO guestDTOConvert(GuestPO item);

    AgentAccountConfig agentAccountConfigConvert(AgentSelectDTO agentSelectDTO);

    SaveSupplyResultDTO saveSupplyResultDTOConvert(SaveEmailSupplyResultDTO request);

    SaveSupplyResultDTO saveSupplyResultConverts(SaveSupplyResultDTO request);

    OrderCheckInfoEntity OrderCheckInfoEntityConvert(OrderCheckInfoDTO orderCheckInfoDTO);

    OrderCheckDetailEntity OrderCheckDetailEntityConvert(OrderCheckDetailDTO orderCheckDetailDTO);

    PriceInfoDetail priceInfoDetail(PriceInfoDetail priceInfoDetail);

    OrderDTO OrderDTOConvert(OrderPO orderPO);

    List<OrderGuestDTO> OrderGuestDTOConvert(List<GuestPO> orderGuestPOList);

    List<ProtocolOrderSettleInfoDTO> protocolOrderSettleInfoDTOConvert(List<ProtocolOrderSettlementCostResponseDTO> protocolOrderSettleInfoList);

    SupplyOrderDTO supplyOrderDTOConvert(SupplyOrderPO supplyOrderPO);

    SupplyOrderRewardDTO rewardDTOConvert(SupplyOrderFinancePO supplyOrderFinanceReward);

    SupplyOrderRebateDTO rebateDTOConvert(SupplyOrderFinancePO supplyOrderFinanceRebate);

    SupplyProductDTO supplyProductDTOConvert(SupplyProductPO supplyProductPO);

    SupplyOrderAmtLogDTO supplyOrderAmtLogDTOConvert(SupplyOrderAmtLogPO po);

    SupplyOrderPreviewDTO supplyOrderPreviewDTOConvert(SupplyOrderPO supplyOrderPO);

    SupplyProductDetailDTO supplyProductDetailDTOConvert(SupplyProductPO supplyProductPO);

    SupplyProductPriceDTO supplyProductPriceDTOConvert(SupplyProductPricePO supplyProductPricePO);

//    OrderInvoiceDTO orderInvoiceConvert(OrderInvoicePO i);
//
//    NotifyCollectionDTO notifyCollectionDTOConvert(NotifyCollectionOfOrderDTO request);

    List<WorkOrderAttchDTO> workOrderAttchDTOListConvert(List<NotifyAttchDTO> photoList);

//    NotifyPaymentDTO notifyPaymentDTOConvert(NotifyPaymentOfOrderDTO request);
//
//    NotifyCollectionDTO notifyCollectionConvert(NotifyCollectionOfOrderListDTO request);
//
//    NotifyPaymentDTO notifyPaymentConvert(NotifyPaymentOfOrderListDTO request);

    OrderRefundTaskPO refundTaskConvert(OrderRefundTaskDTO request);

    OrderRefundTaskDetailDTO detailDTOConvert(OrderRefundTaskPO orderRefundTaskPO);

    OrderFilePO filePOConvert(AddOrderFileReq req);

    CreateOrderStatementTaskDTO orderStatementTaskConvert(OrderPayDetailPO payDetailPO);

    GuestPO guestPOConvert(OrderGuestDTO request);

    OrderRemarkPO orderRemarkInsertConvert(AddRemarkDTO request);

    OrderAttachmentPO orderAttachmentInsertConvert(SaveOrderAttachmentDTO request);

    List<FileAttachDTO> fileAttachDTOConvert(List<FileResp> model);

    OrderInvoicePO orderInvoicePOConvert(OrderInvoiceDTO orderInvoiceDTO);

    List<FileAttachDTO> fileAttachDTOListConvert(List<FileResp> model);

    OrderPayDetailPO orderPayDetailPOConvert(AddOrderPayDetailDTO addOrderPayDetailDTO);

    SupplyOrderPO supplyOrderConvert(AddProductDTO request);

    SupplyProductPO SupplyProductConvert(AddProductDTO request);

    SupplyAttachmentPO supplyAttachmentInsertConvert(SaveSupplyAttachmentDTO request);

//    NotifyCollectionDTO notifyColleConvert(NotifyCollectionOfSupplyOrderDTO request);
//
//    NotifyPaymentDTO notifyPaymentSupplyOrderConvert(NotifyPaymentOfSupplyOrderDTO request);
//
//    NotifyCollectionDTO notifyConvert(NotifyCollectionOfSupplyOrderListDTO request);
//
//    NotifyPaymentDTO notifyPaymentDTO(NotifyPaymentOfSupplyOrderListDTO request);

    List<WorkOrderAttchDTO> NotifyAttchDTOConvert(List<NotifyAttchDTO> photoList);

    SupplyProductDailyPriceDTO supplyProductDailyPriceDTOConvert(SupplyProductPricePO item);

    OrderInvoiceDTO orderInvoiceConvert(OrderInvoicePO i);

    OrderLogPO orderLogPOConvert(SaveOrderLogDTO logDTO);

    SupplyOrderNonVccAutoBillInfoPO supplyOrderNonVccAutoBillInfoPOConvert(NonVccAutoBillConfigDTO request);

    List<SupplyOrderLabelDetails> supplyOrderLabelDetailsListConvert(List<SupplyOrderNonVccAutoBillInfoPO> request);
}
