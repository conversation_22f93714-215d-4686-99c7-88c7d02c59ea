package com.tiangong.order.util;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;

import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class CancellationConvertUtil {
    /**
     * 条款转英文
     *
     * @param cancellationTerm
     * @return
     * @throws JsonProcessingException
     */
    public static List<String> convertCancellationToEn(String cancellationTerm) throws JsonProcessingException {
        return convertCancellationToEn(cancellationTerm, false);
    }

    /**
     * 条款转英文
     *
     * @param cancellationTerm
     * @param dateZoneChange   开始 结束时间的时区是否转化
     * @return
     * @throws JsonProcessingException
     */
    public static List<String> convertCancellationToEn(String cancellationTerm, Boolean dateZoneChange) throws JsonProcessingException {
        List<String> cancelList = new ArrayList<>();
        if (null == cancellationTerm || cancellationTerm.equals("无")) {
            cancelList.add("None");
        } else {
            ObjectMapper objectMapper = new ObjectMapper();
            // 将 JSON 字符串转换为 JsonNode 数组
            JsonNode arrayNode = objectMapper.readTree(cancellationTerm);


            // 遍历数组中的每个对象
            for (JsonNode node : arrayNode) {
                Integer cancelRestrictionType = node.get("cancelRestrictionType").asInt();

                String cancelRestrictionDate = node.has("cancelRestrictionDate") ? node.get("cancelRestrictionDate").asText() : null;
                String cancelRestrictionTime = node.has("cancelRestrictionTime") ? node.get("cancelRestrictionTime").asText() : null;

                //判断cancelRestrictionTime不为空，则需要截取时间
                if (null != cancelRestrictionTime && !cancelRestrictionTime.isEmpty()) {
                    //把1800：00 转换为 18:00
                    cancelRestrictionTime = cancelRestrictionTime.substring(0, 2) + ":" + cancelRestrictionTime.substring(2, 4);
                }


                if (cancelRestrictionType.equals(0) || cancelRestrictionType.equals(4)) {
                    //0：免费取消
                    cancelList.add("Cancel");
                } else if (cancelRestrictionType.equals(1)) {
                    //1：如果修改或取消订单, 将无法获得退款
                    cancelList.add("If you modify or cancel the order, a refund will not be obtained");
                } else if (cancelRestrictionType.equals(2)) {
                    //2：判断cancelRestrictionRemark  备注字段是否为数组或者为空
                    boolean flag = node.has("cancelRestrictionRemark");
                    //判断cancelRestrictionRemark是否存在
                    String cancelRestrictionRemark = flag ? node.get("cancelRestrictionRemark").asText() : null;

                    //判断cancelRestrictionRemark备注字段是否为数组
                    if (null != cancelRestrictionRemark && isJsonArray(cancelRestrictionRemark)) {
                        // 将 JSON 字符串转换为 JsonNode 数组
                        JsonNode arrayNode2 = objectMapper.readTree(cancelRestrictionRemark);

                        // 遍历数组中的每个对象
                        for (JsonNode node2 : arrayNode2) {
                            String startDate2 = dateZoneChange ? convertToSystemTimezone(node2.get("startDate").asText(), "yyyy-MM-dd HH:mm:ss") : node2.get("startDate").asText();
                            String endDate2 = dateZoneChange ? convertToSystemTimezone(node2.get("endData").asText(), "yyyy-MM-dd HH:mm:ss") : node2.get("endData").asText();
                            Integer penaltiesType = node2.get("penaltiesType").asInt();
                            String penaltiesValue = node2.get("penaltiesValue").asText();
                            String currencyCode = node2.get("currencyCode").asText();

                            String cancelText = "";
                            if (penaltiesType.equals(1)) {
                                //"取消订单，将收取 + " + penaltiesValue + "晚房费加税费"
                                cancelText = " Cancel orders, will be charged" + penaltiesValue + "Evening house fees increase taxes and fees";
                            }
                            if (penaltiesType.equals(2)) {
                                //"取消订单，将收取 + " + penaltiesValue + currencyCode + "费用"
                                cancelText = " Cancel orders, will be charged " + penaltiesValue + currencyCode + " cost";
                            }
                            if (penaltiesType.equals(3)) {
                                //"取消订单，将收取 + " + penaltiesValue + "晚房费加税费"
                                cancelText = " Cancel orders, will be charged " + penaltiesValue + "Order fee";
                            }
                            if (penaltiesType.equals(4)) {
                                //取消订单，将收取首晚房费加税费
                                cancelText = " Cancel the order, the first night housing fee will be charged to increase taxes and fees";
                            }

                            //第一段：前取消订单，可全额退款
                            cancelList.add(startDate2 + " Cancel the order before, which can be refunded in full");
                            //第二段
                            cancelList.add(startDate2 + "~" + endDate2 + cancelText);
                            //第三段
                            cancelList.add(endDate2 + " After modifying or canceling the order, you will not be refunded");
                            //第四段
                            cancelList.add("Time is based on the local time of the hotel");
                        }
                    } else {
                        //非数组情况
                        //第一段
                        cancelList.add(cancelRestrictionDate + " " + cancelRestrictionTime + " Cancel the order before, which can be refunded in full");

                        String cancelText = "";
                        cancelText = getEnString(cancelRestrictionRemark, cancelText);
                        //第二段
                        cancelList.add(cancelRestrictionDate + " " + cancelRestrictionTime + cancelText);
                        //第三段
                        cancelList.add("Time is based on the local time of the hotel");
                    }
                } else if (cancelRestrictionType.equals(3)) {
                    //第一段
                    cancelList.add(cancelRestrictionDate + " " + cancelRestrictionTime + " Cancel the order before, which can be refunded in full");

                    //2：判断cancelRestrictionRemark  备注字段是否为数组或者为空
                    boolean flag = node.has("cancelRestrictionRemark");
                    //判断cancelRestrictionRemark是否存在
                    String cancelRestrictionRemark = flag ? node.get("cancelRestrictionRemark").asText() : null;

                    String cancelText = "";
                    cancelText = getEnString(cancelRestrictionRemark, cancelText);
                    //第二段
                    cancelList.add(cancelRestrictionDate + " " + cancelRestrictionTime + cancelText);
                    //第三段
                    cancelList.add("Time is based on the local time of the hotel");
                } else {
                    cancelList.add("None");
                }
            }
        }
        return cancelList;
    }

    /**
     * 条款转中文
     *
     * @param cancellationTerm
     * @return
     * @throws JsonProcessingException
     */
    public static List<String> convertCancellationToCn(String cancellationTerm) throws JsonProcessingException {
        List<String> cancelList = new ArrayList<>();
        if (null == cancellationTerm || cancellationTerm.equals("无")) {
            cancelList.add("无");
        } else {
            ObjectMapper objectMapper = new ObjectMapper();
            // 将 JSON 字符串转换为 JsonNode 数组
            JsonNode arrayNode = objectMapper.readTree(cancellationTerm);


            // 遍历数组中的每个对象
            for (JsonNode node : arrayNode) {
                Integer cancelRestrictionType = node.get("cancelRestrictionType").asInt();

                String cancelRestrictionDate = node.has("cancelRestrictionDate") ? node.get("cancelRestrictionDate").asText() : null;
                String cancelRestrictionTime = node.has("cancelRestrictionTime") ? node.get("cancelRestrictionTime").asText() : null;

                //判断cancelRestrictionTime不为空，则需要截取时间
                if (null != cancelRestrictionTime && !cancelRestrictionTime.isEmpty()) {
                    //把1800：00 转换为 18:00
                    cancelRestrictionTime = cancelRestrictionTime.substring(0, 2) + ":" + cancelRestrictionTime.substring(2, 4);
                }


                if (cancelRestrictionType.equals(0) || cancelRestrictionType.equals(4)) {
                    //0：免费取消
                    cancelList.add("免费取消");
                } else if (cancelRestrictionType.equals(1)) {
                    //1：如果修改或取消订单, 将无法获得退款
                    cancelList.add("如果修改或取消订单, 将无法获得退款");
                } else if (cancelRestrictionType.equals(2)) {
                    //2：判断cancelRestrictionRemark  备注字段是否为数组或者为空
                    boolean flag = node.has("cancelRestrictionRemark");
                    //判断cancelRestrictionRemark是否存在
                    String cancelRestrictionRemark = flag ? node.get("cancelRestrictionRemark").asText() : null;

                    //判断cancelRestrictionRemark备注字段是否为数组
                    if (null != cancelRestrictionRemark && isJsonArray(cancelRestrictionRemark)) {
                        // 将 JSON 字符串转换为 JsonNode 数组
                        JsonNode arrayNode2 = objectMapper.readTree(cancelRestrictionRemark);

                        // 遍历数组中的每个对象
                        for (JsonNode node2 : arrayNode2) {
                            String startDate2 = convertToSystemTimezone(node2.get("startDate").asText(), "yyyy-MM-dd HH:mm:ss");
                            String endDate2 = convertToSystemTimezone(node2.get("endData").asText(), "yyyy-MM-dd HH:mm:ss");
                            Integer penaltiesType = node2.get("penaltiesType").asInt();
                            String penaltiesValue = node2.get("penaltiesValue").asText();
                            String currencyCode = node2.get("currencyCode").asText();

                            String cancelText = "";
                            if (penaltiesType.equals(1)) {
                                //"取消订单，将收取 + " + penaltiesValue + "晚房费加税费"
                                cancelText = " 取消订单，将收取" + penaltiesValue + "晚房费加税费";
                            }
                            if (penaltiesType.equals(2)) {
                                //"取消订单，将收取 + " + penaltiesValue + currencyCode + "费用"
                                cancelText = " 取消订单，将收取 " + penaltiesValue + currencyCode + " 费用";
                            }
                            if (penaltiesType.equals(3)) {
                                //"取消订单，将收取 + " + penaltiesValue + "晚房费加税费"
                                cancelText = " 取消订单，将收取 " + penaltiesValue + "订单费用";
                            }
                            if (penaltiesType.equals(4)) {
                                //取消订单，将收取首晚房费加税费
                                cancelText = " 取消订单，将收取首晚房费加税费";
                            }

                            //第一段：前取消订单，可全额退款
                            cancelList.add(startDate2 + " 前取消订单，可全额退款");
                            //第二段
                            cancelList.add(startDate2 + "~" + endDate2 + cancelText);
                            //第三段
                            cancelList.add(endDate2 + " 后修改或取消订单，将无法退款");
                            //第四段
                            cancelList.add("时间以酒店当地时间为准");
                        }
                    } else {
                        //非数组情况
                        //第一段
                        cancelList.add(cancelRestrictionDate + " " + cancelRestrictionTime + " 前取消订单，可全额退款");

                        String cancelText = "";
                        cancelText = getCNString(cancelRestrictionRemark, cancelText);
                        //第二段
                        cancelList.add(cancelRestrictionDate + " " + cancelRestrictionTime + cancelText);
                        //第三段
                        cancelList.add("时间以酒店当地时间为准");
                    }
                } else if (cancelRestrictionType.equals(3)) {
                    //第一段
                    cancelList.add(cancelRestrictionDate + " " + cancelRestrictionTime + " 前取消订单，可全额退款");

                    //2：判断cancelRestrictionRemark  备注字段是否为数组或者为空
                    boolean flag = node.has("cancelRestrictionRemark");
                    //判断cancelRestrictionRemark是否存在
                    String cancelRestrictionRemark = flag ? node.get("cancelRestrictionRemark").asText() : null;

                    String cancelText = "";
                    cancelText = getCNString(cancelRestrictionRemark, cancelText);
                    //第二段
                    cancelList.add(cancelRestrictionDate + " " + cancelRestrictionTime + cancelText);
                    //第三段
                    cancelList.add("时间以酒店当地时间为准");
                } else {
                    cancelList.add("None");
                }
            }
        }
        return cancelList;
    }

    private static boolean containsDigit(String s) {
        return s.matches(".*\\d.*");
    }

    private static boolean isJsonArray(String str) {
        return str.trim().startsWith("[") && str.trim().endsWith("]");
    }

    private static String getEnString(String cancelRestrictionRemark, String cancelText) {
        if (StringUtils.isEmpty(cancelRestrictionRemark)) {
            cancelText = " you will be charged the total order amount";
        } else if (cancelRestrictionRemark.contains("首晚房费") ||
                cancelRestrictionRemark.equals("收取首晚")
        ) {
            cancelText = " you will be charged for the first night of your stay plus taxes and fees";
        } else if (cancelRestrictionRemark.equals("全部房费")) {
            cancelText = " you will be charged a fee of full Room Charge";
        } else if (cancelRestrictionRemark.contains("全部房费")) {
            cancelText = " you will be charged a fee of " + cancelRestrictionRemark.replace("全部房费", "");
        } else if (cancelRestrictionRemark.contains("收取全额")) {
            cancelText = " you will be charged" + cancelRestrictionRemark.replace("收取全额", "") + " of the total stay amount";
        } else if (cancelRestrictionRemark.contains("订单总金额")) {
            cancelText = " you will be charged" + cancelRestrictionRemark.replace("订单总金额", "") + " of the total stay amount";
        } else if (cancelRestrictionRemark.contains("晚房费")) {
            if (containsDigit(cancelRestrictionRemark)) {
                //判断存在数字，则取出数字
                String regex = "\\d+";
                Pattern pattern = Pattern.compile(regex);
                Matcher matcher = pattern.matcher(cancelRestrictionRemark);
                if (matcher.find()) {
                    int number = Integer.parseInt(matcher.group());
                    if (number == 1 || number == 0) {
                        cancelText = " you will be charged for the 1 night of your stay plus taxes and fees";
                    } else if (number > 1) {
                        cancelText = " you will be charged for " + number + " nights of your stay plus taxes and fees";
                    }
                }
            }
        }
        return cancelText;
    }

    private static String getCNString(String cancelRestrictionRemark, String cancelText) {
        if (StringUtils.isEmpty(cancelRestrictionRemark)) {
            cancelText = " 后取消，将收取全部订单费用";
        } else if (cancelRestrictionRemark.contains("首晚房费")
        ) {
            cancelText = " 后取消，将收取" + cancelRestrictionRemark;
        } else if (cancelRestrictionRemark.equals("全部房费")) {
            cancelText = " 后取消，将收取" + cancelRestrictionRemark;
        } else if (cancelRestrictionRemark.contains("全部房费")) {
            cancelText = " 后取消，将收取 " + cancelRestrictionRemark.replace("全部房费", "") + "的费用";
        } else if (cancelRestrictionRemark.contains("收取全额")) {
            cancelText = " 后取消，将收取" + cancelRestrictionRemark.replace("收取全额", "订单金额");
        } else if (cancelRestrictionRemark.contains("订单总金额")) {
            cancelText = " 后取消，将收取" + cancelRestrictionRemark;
        } else if (cancelRestrictionRemark.contains("晚房费")) {
            cancelText = " 后取消，将收取" + cancelRestrictionRemark;
        } else if (cancelRestrictionRemark.contains("收取首晚")) {
            cancelText = " 后取消，将收取 " + cancelRestrictionRemark.replace("收取首晚", "首晚房费");
        }
        return cancelText;
    }

    /**
     * 将带有时区信息的时间转换为系统时区的时间，并指定输出格式
     *
     * @param timestampWithZone 带有时区信息的时间字符串，格式为：yyyy-MM-dd'T'HH:mm:ss.SSSXXX
     * @param outputPattern     输出格式，例如："yyyy-MM-dd HH:mm:ss"
     * @return 转换后的时间字符串
     */
    public static String convertToSystemTimezone(String timestampWithZone, String outputPattern) {
        try {
            // 尝试解析带有时区信息的时间戳
            ZonedDateTime sourceDateTime;
            try {
                sourceDateTime = ZonedDateTime.parse(timestampWithZone);
            } catch (DateTimeParseException e) {
                // 如果不是标准ISO格式，直接返回原始字符串
                return timestampWithZone;
            }

            // 检查是否已经是系统时区或者时区偏移量相同
            ZoneId systemZoneId = ZoneId.systemDefault();
            if (sourceDateTime.getZone().getRules().getOffset(sourceDateTime.toInstant())
                    .equals(systemZoneId.getRules().getOffset(sourceDateTime.toInstant()))) {
                // 如果时区偏移量相同，则不需要转换，直接格式化输出
                DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern(outputPattern);
                return sourceDateTime.format(outputFormatter);
            }

            // 转换为系统默认时区
            ZonedDateTime systemDateTime = sourceDateTime.withZoneSameInstant(systemZoneId);

            // 格式化为输出格式
            DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern(outputPattern);
            return systemDateTime.format(outputFormatter);
        } catch (Exception e) {
            log.error("Failed to parse timestamp with zone: {}", timestampWithZone, e);
            return timestampWithZone; // 如果解析失败，返回原始时间戳
        }
    }
}
