package com.tiangong.order.mapper;

import com.tiangong.dto.common.MyMapper;
import com.tiangong.order.domain.OrgPO;
import com.tiangong.organization.remote.dto.AgentCreditLineDTO;
import com.tiangong.organization.remote.dto.AgentSelectDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/6/19 21:14
 **/
@Mapper
public interface OrgMapper extends MyMapper<OrgPO> {
    /**
     * 根据客户编码查询客户详情
     */
    AgentSelectDTO AgentList(@Param("orgCode") String orgCode);

    /**
     * 查询该分销商是否存在该订单的扣退额度
     */
    List<AgentCreditLineDTO> getAgentCreditLine(@Param("agentCode") String agentCode);

    /**
     * 获取分销合作商编码通过客户编码
     *
     * @param agentCode 客户编码
     * @return
     */
    String getPartnerCodeByAgentCode(@Param("agentCode") String agentCode);
}
