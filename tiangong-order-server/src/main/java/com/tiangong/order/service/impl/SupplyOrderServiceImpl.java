package com.tiangong.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tiangong.common.Constant;
import com.tiangong.common.Response;
import com.tiangong.config.EmailConfigProperties;
import com.tiangong.dis.dto.B2BOrderStatusPushRequest;
import com.tiangong.dis.dto.ProductRestrictDTO;
import com.tiangong.dis.remote.DisB2BRemote;
import com.tiangong.dto.common.CompanyDTO;
import com.tiangong.dto.common.ResultX;
import com.tiangong.dto.hotel.DestinationReq;
import com.tiangong.dto.hotel.EsHotelDto;
import com.tiangong.dto.order.OrderRoomGuestDTO;
import com.tiangong.dto.order.request.PriceRequestDTO;
import com.tiangong.dto.order.response.OrderFeeDTO;
import com.tiangong.dto.order.response.OrderFeeDetailDTO;
import com.tiangong.enums.*;
import com.tiangong.exception.SysException;
import com.tiangong.finance.OrgDTO;
import com.tiangong.finance.enums.CheckStatusEnum;
import com.tiangong.finance.remote.ExchangeRateRemote;
import com.tiangong.hotel.remote.HotelRemote;
import com.tiangong.initializer.CommonInitializer;
import com.tiangong.keys.RedisKey;
import com.tiangong.order.config.SettingsConstant;
import com.tiangong.order.convert.ProductSalePriceConvert;
import com.tiangong.order.domain.*;
import com.tiangong.order.dto.*;
import com.tiangong.order.enums.*;
import com.tiangong.order.mapper.*;
import com.tiangong.order.remote.SupplyOrderRemote;
import com.tiangong.order.remote.dto.*;
import com.tiangong.order.remote.request.*;
import com.tiangong.order.remote.request.OrderCodeDTO;
import com.tiangong.order.remote.response.*;
import com.tiangong.order.server.SupplyOrderServer;
import com.tiangong.order.service.*;
import com.tiangong.order.service.common.OrderCommonService;
import com.tiangong.order.util.CancellationConvertUtil;
import com.tiangong.order.util.DistributedCodeGenerator;
import com.tiangong.organization.remote.dto.AddSupplierReq;
import com.tiangong.product.dto.DebitedQuotaDTO;
import com.tiangong.product.dto.ProductDTO;
import com.tiangong.product.dto.QuotaDTO;
import com.tiangong.product.remote.DebitedQuotaRemote;
import com.tiangong.product.remote.ProductRemote;
import com.tiangong.product.remote.QuotaRemote;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.redis.util.RedisUtil;
import com.tiangong.remote.OrderNotifyRemote;
import com.tiangong.req.SendEmailReq;
import com.tiangong.settle.remote.SettleServerRemote;
import com.tiangong.settle.req.SettleSupplyResultReq;
import com.tiangong.supply.direct.remote.SupplyDirectShubOrderRemote;
import com.tiangong.supply.direct.remote.dto.BedInfoDto;
import com.tiangong.supply.direct.remote.dto.BedTypesDetailDto;
import com.tiangong.supply.direct.remote.request.QuerySupplyOrderRequest;
import com.tiangong.supply.direct.remote.response.QuerySupplyOrderResponse;
import com.tiangong.util.*;
import freemarker.template.Configuration;
import freemarker.template.Template;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.io.StringWriter;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.tiangong.util.DateUtilX.hour_format;
import static com.tiangong.util.DateUtilX.only_hour_format;
import static java.util.stream.Collectors.groupingBy;

@Service
@Slf4j
public class SupplyOrderServiceImpl implements SupplyOrderService {

    @Autowired
    private OrderMapper orderMapper;

    @Autowired
    private SupplyOrderMapper supplyOrderMapper;

    @Autowired
    private SupplyProductMapper supplyProductMapper;

    @Autowired
    private SupplyProductPriceMapper supplyProductPriceMapper;

    @Autowired
    private SupplyRequestMapper supplyRequestMapper;

    @Autowired
    private SupplyAttachmentMapper supplyAttachmentMapper;

    @Autowired
    private OrderRemarkMapper orderRemarkMapper;

    @Autowired
    private GuestMapper guestMapper;

    @Autowired
    private OrderCommonService orderCommonService;

    @Autowired
    private SupplyOrderFinanceMapper supplyOrderFinanceMapper;

    @Autowired
    private OrderCheckInfoMapper orderCheckInfoMapper;

    @Autowired
    private OrderCheckDetailMapper orderCheckDetailMapper;

    @Autowired
    private ExchangeRateRemote exchangeRateRemote;

    @Autowired
    private DebitedQuotaRemote debitedQuotaRemote;

    @Autowired
    private QuotaRemote quotaRemote;

    @Autowired
    private SupplyOrderRemote supplyOrderRemote;

    @Autowired
    private OrderQueryService orderQueryService;

    @Autowired
    private OrderService orderService;

    @Autowired
    private OrderRequestMapper orderRequestMapper;

    @Autowired
    private ProductRemote productRemote;

    @Autowired
    private SupplyDirectShubOrderRemote supplyDirectShubOrderRemote;

    @Autowired
    private OrderInvoiceMapper orderInvoiceMapper;

    @Autowired
    private OrderCheckInfoService orderCheckInfoService;

    @Autowired
    private OrderExtendMapper orderExtendMapper;

    @Autowired
    private SettleServerRemote settleServerRemote;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private SupplyOrderAmtLogMapper supplyOrderAmtLogMapper;

    @Autowired
    private OrderNotifyRemote orderNotifyRemote;

    @Autowired
    private SendCodeUtil sendCodeUtil;

    @Autowired
    private SettingsConstant settingsConstant;

    @Autowired
    private EmailConfigProperties emailConfigProperties;

    @Autowired
    private DisB2BRemote disB2BRemote;

    @Autowired
    private HotelRemote hotelRemote;

    @Autowired
    private VirtualCardService virtualCardService;

    @Resource(name = "defaultPool")
    private ThreadPoolTaskExecutor processingOrderExecutor;

    @Override
    @Transactional
    public void modifySupplyProduct(ModifySupplyProductDTO request) {
        StringBuilder logSb = new StringBuilder("供货单 (");
        boolean isSupplyProductChange = false;
        boolean isSaleDateChange = false;
        BigDecimal basePriceTotalAmt = BigDecimal.ZERO;

        SupplyProductPO supplyProductPO = supplyProductMapper.selectByPrimaryKey(request.getSupplyProductId());
        SupplyOrderPO supplyOrderPO = supplyOrderMapper.selectByPrimaryKey(supplyProductPO.getSupplyOrderId());
        OrderPO orderPO = orderMapper.selectByPrimaryKey(supplyOrderPO.getOrderId());
        Integer oldSupplyConfirmationStatus = supplyOrderPO.getConfirmationStatus();
        logSb.append(orderPO.getOrderCode()).append(") ");

        // 1. 更新产品
        SupplyProductPO supplyProductUpdate = new SupplyProductPO();
        supplyProductUpdate.setId(request.getSupplyProductId());
        supplyProductUpdate.setUpdatedBy(request.getOperator());
        supplyProductUpdate.setUpdatedDt(DateUtilX.dateToString(new Date(), hour_format));
        if (DateUtilX.compare(supplyProductPO.getStartDate(), DateUtilX.stringToDate(request.getStartDate())) != 0) {
            supplyProductUpdate.setStartDate(DateUtilX.stringToDate(request.getStartDate()));
            logSb.append(",入住日期由").append(DateUtilX.dateToString(supplyProductPO.getStartDate()))
                    .append("变更为").append(request.getStartDate());
            isSaleDateChange = true;
            isSupplyProductChange = true;
        }
        if (DateUtilX.compare(supplyProductPO.getEndDate(), DateUtilX.stringToDate(request.getEndDate())) != 0) {
            supplyProductUpdate.setEndDate(DateUtilX.stringToDate(request.getEndDate()));
            logSb.append(",离店日期由").append(DateUtilX.dateToString(supplyProductPO.getEndDate()))
                    .append("变更为").append(request.getEndDate());
            isSaleDateChange = true;
            isSupplyProductChange = true;
        }
        if (!Objects.equals(supplyProductPO.getRoomQty(), request.getRoomQty())) {
            supplyProductUpdate.setRoomQty(request.getRoomQty());
            logSb.append(",房间数量由").append(supplyProductPO.getRoomQty())
                    .append("变更为").append(request.getRoomQty());
            isSupplyProductChange = true;
        }
        if (supplyProductPO.getGuest() == null && request.getGuest() != null
                || !Objects.equals(supplyProductPO.getGuest(), request.getGuest())) {
            supplyProductUpdate.setGuest(request.getGuest());
            logSb.append(",入住人由").append(supplyProductPO.getGuest())
                    .append("变更为").append(request.getGuest());
            isSupplyProductChange = true;
        }

        // 2. 更新价格明细
        SupplyProductPricePO supplyProductPriceQuery = new SupplyProductPricePO();
        supplyProductPriceQuery.setSupplyProductId(request.getSupplyProductId());
        List<SupplyProductPricePO> supplyProductPricePOList = supplyProductPriceMapper.select(supplyProductPriceQuery);

        if (isSaleDateChange) {
            //如果入住离店日期变更，则先清空，再重新插入
            supplyProductPriceMapper.delete(supplyProductPriceQuery);

            List<SupplyProductPricePO> insertList = new ArrayList<>();
            for (PriceRequestDTO priceDTO : request.getPriceList()) {
                SupplyProductPricePO supplyProductPricePO = new SupplyProductPricePO();
                supplyProductPricePO.setSupplyProductId(supplyProductPO.getId());
                supplyProductPricePO.setSupplyOrderId(supplyOrderPO.getId());
                supplyProductPricePO.setSaleDate(DateUtilX.stringToDate(priceDTO.getSaleDate()));
                supplyProductPricePO.setBasePrice(priceDTO.getBasePrice());
                supplyProductPricePO.setBreakfastType(priceDTO.getBreakfastType());
                supplyProductPricePO.setBreakfastQty(priceDTO.getBreakfastNum());
                insertList.add(supplyProductPricePO);

                basePriceTotalAmt = basePriceTotalAmt.add(priceDTO.getBasePrice().multiply(BigDecimal.valueOf(request.getRoomQty().doubleValue())));
            }
            supplyProductPriceMapper.insertList(insertList);

            logSb.append(",底价由(");
            for (SupplyProductPricePO supplyProductPricePO : supplyProductPricePOList) {
                logSb.append("日期").append(DateUtilX.dateToString(supplyProductPricePO.getSaleDate()))
                        .append("价格").append(supplyProductPricePO.getBasePrice());
            }
            logSb.append(")变更为(");
            for (PriceRequestDTO priceDTO : request.getPriceList()) {
                logSb.append("日期").append(priceDTO.getSaleDate())
                        .append("价格").append(priceDTO.getBasePrice());
            }
            logSb.append(")");
        } else {
            logSb.append(",底价变更");
            for (PriceRequestDTO priceDTO : request.getPriceList()) {
                for (SupplyProductPricePO supplyProductPricePO : supplyProductPricePOList) {
                    if (DateUtilX.compare(DateUtilX.stringToDate(priceDTO.getSaleDate()), supplyProductPricePO.getSaleDate()) == 0) {
                        if (priceDTO.getBasePrice().compareTo(supplyProductPricePO.getBasePrice()) != 0) {
                            logSb.append(",").append(priceDTO.getSaleDate())
                                    .append("由").append(supplyProductPricePO.getBasePrice())
                                    .append("变更为").append(priceDTO.getBasePrice());

                            supplyProductPricePO.setBasePrice(priceDTO.getBasePrice());
                            supplyProductPriceMapper.updateByPrimaryKeySelective(supplyProductPricePO);
                            isSupplyProductChange = true;
                        }
                        break;
                    }
                }
                basePriceTotalAmt = basePriceTotalAmt.add(priceDTO.getBasePrice().multiply(BigDecimal.valueOf(request.getRoomQty().doubleValue())));
            }
        }
        // 供货单产品没有变更
        if (!isSupplyProductChange) {
            return;
        }
        BigDecimal changeAmt = basePriceTotalAmt.subtract(supplyProductPO.getBasePriceTotalAmt());
        if (supplyProductPO.getBasePriceTotalAmt().compareTo(basePriceTotalAmt) != 0) {
            logSb.append(",供货单总金额由").append(supplyOrderPO.getSupplyOrderAmt())
                    .append("变更为").append(supplyOrderPO.getSupplyOrderAmt().add(changeAmt).stripTrailingZeros().toPlainString());
        }

        supplyProductUpdate.setBasePriceTotalAmt(basePriceTotalAmt);
        supplyProductMapper.updateByPrimaryKeySelective(supplyProductUpdate);

        // 3. 更新供货单金额和状态
        SupplyOrderPO supplyOrderUpdate = new SupplyOrderPO();
        supplyOrderUpdate.setId(supplyOrderPO.getId());
        supplyOrderUpdate.setBasePrice(supplyOrderPO.getBasePrice().add(changeAmt));
        //供货单金额=底价+退订费-返佣
        if (supplyOrderPO.getConfirmationStatus().equals(ConfirmationStatusEnum.CANCELED.key)) {//供货单是取消状态，不计算退订费
            supplyOrderUpdate.setSupplyOrderAmt(supplyOrderUpdate.getBasePrice().subtract(null != supplyOrderPO.getCommission() ? supplyOrderPO.getCommission() :
                    BigDecimal.ZERO).add(null != supplyOrderPO.getAdditionalCharges() ? supplyOrderPO.getAdditionalCharges() : BigDecimal.ZERO));
            supplyOrderUpdate.setRefundFee(BigDecimal.ZERO);//退订费清0
        } else {
            supplyOrderUpdate.setSupplyOrderAmt(supplyOrderUpdate.getBasePrice().add(null != supplyOrderPO.getRefundFee() ? supplyOrderPO.getRefundFee() :
                            BigDecimal.ZERO).subtract(null != supplyOrderPO.getCommission() ? supplyOrderPO.getCommission() : BigDecimal.ZERO)
                    .add(null != supplyOrderPO.getAdditionalCharges() ? supplyOrderPO.getAdditionalCharges() : BigDecimal.ZERO));
        }
        //20230627修改，修改价格时，不动供货单状态
        supplyOrderMapper.updateByPrimaryKeySelective(supplyOrderUpdate);

        // 4. 更新订单金额
        SupplyOrderPO supplyOrderQuery = new SupplyOrderPO();
        supplyOrderQuery.setOrderId(supplyOrderPO.getOrderId());
        List<SupplyOrderPO> supplyOrderPOList = supplyOrderMapper.select(supplyOrderQuery);
        BigDecimal supplyOrderSum = BigDecimal.ZERO;
        BigDecimal rewardSum = BigDecimal.ZERO;
        BigDecimal rebateSum = BigDecimal.ZERO;
        for (SupplyOrderPO supplyOrder : supplyOrderPOList) {
            BigDecimal supplierToAgentRate = Objects.nonNull(supplyOrder.getSupplierToAgentRate()) ? supplyOrder.getSupplierToAgentRate() : BigDecimal.ONE;
            supplyOrderSum = supplyOrderSum.add(supplyOrder.getSupplyOrderAmt().multiply(supplierToAgentRate));
            rewardSum = rewardSum.add((Objects.nonNull(supplyOrder.getRewardAmt()) ? supplyOrder.getRewardAmt() : BigDecimal.ZERO).multiply(supplierToAgentRate));
            rebateSum = rebateSum.add((Objects.nonNull(supplyOrder.getRebateAmt()) ? supplyOrder.getRebateAmt() : BigDecimal.ZERO).multiply(supplierToAgentRate));
        }
        OrderPO orderUpdate = new OrderPO();
        orderUpdate.setId(orderPO.getId());
        // orderUpdate.setSupplyOrderConfirmationStatus(ConfirmationStatusEnum.UNCONFIRM.key.toString());
        orderUpdate.setProfit(orderPO.getOrderAmt().subtract(supplyOrderSum).add(rewardSum).subtract(rebateSum));
        orderMapper.updateByPrimaryKeySelective(orderUpdate);


        try {
            // 5. 退扣配额
            if (!supplyProductPO.getProductId().contains("_")) {
                Map<String, Integer> map = new HashMap<>();
                if (StrUtilX.isNotEmpty(supplyProductPO.getProductId())) {
                    map.put("productId", Integer.valueOf(supplyProductPO.getProductId()));
                }
                map.put("orderId", orderPO.getId());
                map.put("type", 1);//查询以扣的配额
                Response<List<DebitedQuotaDTO>> debitedQuota = debitedQuotaRemote.queryDebitedQuota(map);
                List<DebitedQuotaDTO> debitedQuotaDTO = new ArrayList<>();
                if (debitedQuota.getResult().equals(ResultCodeEnum.SUCCESS.code) && null != debitedQuota.getModel()) {
                    debitedQuotaDTO = debitedQuota.getModel();
                }
                QuotaDTO quotaDTO = new QuotaDTO();
                quotaDTO.setOrderCode(orderPO.getOrderCode());
                quotaDTO.setOrderId(orderPO.getId());
                quotaDTO.setProductId(Integer.valueOf(supplyProductPO.getProductId()));

                quotaDTO.setSupplyOrderCode(supplyOrderPO.getSupplyOrderCode());
                quotaDTO.setSupplyOrderId(supplyOrderPO.getId());

                if (debitedQuotaDTO.size() > 0) {
                    //返还配额
                    StringBuilder saleDate = new StringBuilder();
                    for (DebitedQuotaDTO debitedQuotaDTOs : debitedQuotaDTO) {
                        saleDate.append(DateUtilX.dateToString(DateUtilX.stringToDate(debitedQuotaDTOs.getSaleDate(), "yyyy-MM-dd HH:mm:ss"), "yyyy-MM-dd")).append(",");
                    }
                    quotaDTO.setSaleDate(saleDate.append(DateUtilX.getCurrentDateStr(2)).toString());//加一段日期，用以处理后续逻辑
                    quotaDTO.setQuota(-debitedQuotaDTO.get(0).getQuota());//以扣配额为负数，负负得正，加配额
                    quotaRemote.modifyQuota(quotaDTO);
                }

                //扣除配额
                Map<String, String> data = new HashMap<>();
                data.put("begin", request.getStartDate());
                data.put("end", request.getEndDate());
                List<String> list = orderMapper.queryBetweenDate(data);
                StringBuilder saleDate1 = new StringBuilder();
                for (String s : list) {
                    saleDate1.append(s).append(",");
                }
                quotaDTO.setSaleDate(saleDate1.substring(0, saleDate1.length() - 1));
                quotaDTO.setQuota(-request.getRoomQty());//减配额
                quotaRemote.modifyQuota(quotaDTO);
            }
        } catch (Exception e) {
            log.error("扣配额异常！订单ID：" + orderPO.getId(), e);
        }


        if (changeAmt.compareTo(BigDecimal.ZERO) != 0) {
            //更新订单未结算金额和结算状态
            SupplyOrderFinancePO supplyOrderFinanceQuery = new SupplyOrderFinancePO();
            supplyOrderFinanceQuery.setSupplyOrderId(supplyOrderPO.getId());
            supplyOrderFinanceQuery.setFinanceType(StatementTypeEnum.SUPPLY_ORDER_AMT.key);
            SupplyOrderFinancePO supplyOrderFinancePO = supplyOrderFinanceMapper.selectOne(supplyOrderFinanceQuery);
            SupplyOrderFinancePO supplyOrderFinanceUpdate = new SupplyOrderFinancePO();
            supplyOrderFinanceUpdate.setId(supplyOrderFinancePO.getId());
            //如果原来供货单是取消状态，未付金额=供货单金额
            if (oldSupplyConfirmationStatus.equals(ConfirmationStatusEnum.CANCELED.key)) {
                supplyOrderFinanceUpdate.setUnpaidAmt(supplyOrderUpdate.getSupplyOrderAmt());
            } else {
                supplyOrderFinanceUpdate.setUnpaidAmt(supplyOrderFinancePO.getUnpaidAmt().add(changeAmt));
            }

            if (BigDecimal.ZERO.compareTo(supplyOrderFinanceUpdate.getUnpaidAmt()) == 0) {
                supplyOrderFinanceUpdate.setSettlementStatus(1);
            } else {
                supplyOrderFinanceUpdate.setSettlementStatus(0);
            }
            //更新对账状态
            if (!Objects.equals(supplyOrderFinancePO.getCheckStatus(), CheckStatusEnum.CHECKING.key)) {
                if (BigDecimal.ZERO.compareTo(supplyOrderFinanceUpdate.getUnpaidAmt()) == 0) {
                    //如果未收金额为0，则改为已对账
                    supplyOrderFinanceUpdate.setCheckStatus(CheckStatusEnum.CANNOT_CHECK.key);
                } else {
                    //如果未收金额不为0，则改为可出账
                    supplyOrderFinanceUpdate.setCheckStatus(CheckStatusEnum.CAN_CHECK.key);
                }
            }
            supplyOrderFinanceMapper.updateByPrimaryKeySelective(supplyOrderFinanceUpdate);
        }

        // 5. 记日志
        orderCommonService.saveOrderLog(
                orderPO.getId(),
                request.getOperator(),
                request.getOrderOwnerName(),
                supplyOrderPO.getSupplyOrderCode(),
                logSb.toString()
        );
    }

    @Override
    @Transactional
    public void addProduct(AddProductDTO request) {
        OrderPO orderPO = orderMapper.selectByPrimaryKey(request.getOrderId());

        // 1. 如果供应商对应的供货单不存在，则创建供货单
        SupplyOrderPO supplyOrderPO = ProductSalePriceConvert.INSTANCE.supplyOrderConvert(request);
        supplyOrderPO.setCityCode(orderPO.getCityCode());
        supplyOrderPO.setCityName(orderPO.getCityName());
        supplyOrderPO.setHotelId(orderPO.getHotelId());
        supplyOrderPO.setHotelName(orderPO.getHotelName());
        supplyOrderPO.setSupplyOrderAmt(request.getBasePriceTotalAmt());
        supplyOrderPO.setBasePrice(request.getBasePriceTotalAmt());
        supplyOrderPO.setStartDate(DateUtilX.stringToDate(request.getStartDate()));
        supplyOrderPO.setEndDate(DateUtilX.stringToDate(request.getEndDate()));
        supplyOrderPO.setConfirmationStatus(ConfirmationStatusEnum.UNCONFIRM.key);
        supplyOrderPO.setSendingStatus(SendingStatusEnum.UNSEND.key);
        supplyOrderPO.setCreatedBy(request.getOperator());
        supplyOrderPO.setCreatedDt(DateUtilX.dateToString(new Date(), hour_format));
        supplyOrderPO.setIsSpProduct(request.getIsSpProduct());
        supplyOrderPO.setSupplyOrderCode(DistributedCodeGenerator.generateSupplyOrderCode());

        // 查询供应商缓存
        AddSupplierReq supplier = JSON.parseObject(RedisTemplateX.hashGet(RedisKey.SUPPLY_ACCOUNT_CONFIG, request.getSupplierCode()).toString(), AddSupplierReq.class);
        if (supplier == null) {
            log.error("供应商缓存不存在，supplyCode={}", request.getSupplierCode());
            throw new SysException(ErrorCodeEnum.SUPPLY_CHECK_NOT_EXIST);
        }
        if (null != supplier.getPurchaseManagerId()) {
            supplyOrderPO.setMerchantPm(supplier.getPurchaseManagerId().toString());
        }
        supplyOrderPO.setSettlementType(supplier.getSettlementType());

        // 查询汇率
        BigDecimal rate = getRateToOrgCurrency(request.getBaseCurrency());
        supplyOrderPO.setRate(rate);
        supplyOrderMapper.insert(supplyOrderPO);

        SupplyOrderPO supplyOrderNew = supplyOrderMapper.selectByPrimaryKey(supplyOrderPO.getId());
        SupplyOrderFinancePO supplyOrderFinanceInsert = new SupplyOrderFinancePO();
        supplyOrderFinanceInsert.setSupplyOrderId(supplyOrderNew.getId());
        supplyOrderFinanceInsert.setFinanceType(StatementTypeEnum.SUPPLY_ORDER_AMT.key);
        supplyOrderFinanceInsert.setSupplyOrderCode(supplyOrderNew.getSupplyOrderCode());
        supplyOrderFinanceInsert.setPaidAmt(BigDecimal.ZERO);
        supplyOrderFinanceInsert.setUnpaidAmt(BigDecimal.ZERO);
        supplyOrderFinanceInsert.setUnconfirmedReceivedAmt(BigDecimal.ZERO);
        supplyOrderFinanceInsert.setUnconfirmedPaidAmt(BigDecimal.ZERO);
        supplyOrderFinanceInsert.setSettlementStatus(0);
        supplyOrderFinanceInsert.setSettlementDate(DateUtilX.stringToDate(DateUtilX.dateToString(new Date())));
        supplyOrderFinanceInsert.setCheckStatus(CheckStatusEnum.CANNOT_CHECK.key);
        supplyOrderFinanceInsert.setFinanceLockStatus(0);
        supplyOrderFinanceInsert.setCreatedBy(request.getOperator());
        supplyOrderFinanceInsert.setCreatedDt(DateUtilX.dateToString(new Date(), hour_format));
        supplyOrderFinanceMapper.insert(supplyOrderFinanceInsert);

        // 2. 保存产品
        SupplyProductPO supplyProductPO = ProductSalePriceConvert.INSTANCE.SupplyProductConvert(request);
        supplyProductPO.setSupplyOrderId(supplyOrderPO.getId());
        supplyProductPO.setOrderId(orderPO.getId());
        supplyProductPO.setStartDate(DateUtilX.stringToDate(request.getStartDate()));
        supplyProductPO.setEndDate(DateUtilX.stringToDate(request.getEndDate()));
        supplyProductPO.setGuest(orderPO.getGuest());
        supplyProductPO.setBasePriceTotalAmt(request.getBasePriceTotalAmt());
        supplyProductPO.setCreatedBy(request.getOperator());
        supplyProductPO.setCreatedDt(DateUtilX.dateToString(new Date(), hour_format));
        supplyProductPO.setSpProductId(request.getSpProductId());
        supplyProductMapper.insert(supplyProductPO);

        // 3. 保存价格明细
        List<SupplyProductPricePO> supplyProductPricePOList = new ArrayList<>();
        for (PriceRequestDTO priceDTO : request.getPriceList()) {
            SupplyProductPricePO supplyProductPricePO = new SupplyProductPricePO();
            supplyProductPricePO.setSupplyProductId(supplyProductPO.getId());
            supplyProductPricePO.setSupplyOrderId(supplyOrderPO.getId());
            supplyProductPricePO.setSaleDate(DateUtilX.stringToDate(priceDTO.getSaleDate()));
            supplyProductPricePO.setBasePrice(priceDTO.getBasePrice());
            supplyProductPricePO.setBreakfastType(priceDTO.getBreakfastType());
            supplyProductPricePO.setBreakfastQty(priceDTO.getBreakfastNum());
            supplyProductPricePOList.add(supplyProductPricePO);
        }
        supplyProductPriceMapper.insertList(supplyProductPricePOList);

        // 4. 更新订单金额
        SupplyOrderPO supplyOrderParam = new SupplyOrderPO();
        supplyOrderParam.setOrderId(supplyOrderPO.getOrderId());
        List<SupplyOrderPO> supplyOrderList = supplyOrderMapper.select(supplyOrderParam);
        BigDecimal supplyOrderSum = BigDecimal.ZERO;
        BigDecimal rewardSum = BigDecimal.ZERO;
        BigDecimal rebateSum = BigDecimal.ZERO;
        for (SupplyOrderPO supplyOrder : supplyOrderList) {
            BigDecimal supplierToAgentRate = Objects.nonNull(supplyOrder.getSupplierToAgentRate()) ? supplyOrder.getSupplierToAgentRate() : BigDecimal.ONE;
            supplyOrderSum = supplyOrderSum.add(supplyOrder.getSupplyOrderAmt().multiply(supplierToAgentRate));
            rewardSum = rewardSum.add((Objects.nonNull(supplyOrder.getRewardAmt()) ? supplyOrder.getRewardAmt() : BigDecimal.ZERO).multiply(supplierToAgentRate));
            rebateSum = rebateSum.add((Objects.nonNull(supplyOrder.getRebateAmt()) ? supplyOrder.getRebateAmt() : BigDecimal.ZERO).multiply(supplierToAgentRate));
        }
        OrderPO orderUpdate = new OrderPO();
        orderUpdate.setId(orderPO.getId());
        orderUpdate.setSupplyOrderConfirmationStatus(ConfirmationStatusEnum.UNCONFIRM.key.toString());
        orderUpdate.setProfit(orderPO.getOrderAmt().subtract(supplyOrderSum).add(rewardSum).subtract(rebateSum));
        orderMapper.updateByPrimaryKeySelective(orderUpdate);


        try {
            // 5. 扣配额
            Map<String, String> data = new HashMap<>();
            data.put("begin", request.getStartDate());
            data.put("end", request.getEndDate());
            List<String> list = orderMapper.queryBetweenDate(data);
            StringBuilder saleDate1 = new StringBuilder();
            for (String s : list) {
                saleDate1.append(s).append(",");
            }
            QuotaDTO quotaDTO = new QuotaDTO();
            quotaDTO.setSaleDate(saleDate1.substring(0, saleDate1.length() - 1));
            quotaDTO.setQuota(-request.getRoomQty());//减配额
            quotaDTO.setOrderId(orderPO.getId());
            quotaDTO.setOrderCode(orderPO.getOrderCode());
            quotaDTO.setSupplyOrderId(supplyOrderPO.getId());
            quotaDTO.setSupplyOrderCode(supplyOrderPO.getSupplyOrderCode());
            quotaDTO.setSupplierCode(request.getSupplierCode());
            quotaDTO.setProductId(Integer.valueOf(supplyProductPO.getProductId()));
            quotaRemote.modifyQuota(quotaDTO);
        } catch (Exception e) {
            log.error("扣配额异常！订单ID：" + orderPO.getId(), e);
        }

        // 更新订单未结算金额和结算状态
        SupplyOrderFinancePO supplyOrderFinanceQuery = new SupplyOrderFinancePO();
        supplyOrderFinanceQuery.setSupplyOrderId(supplyOrderPO.getId());
        supplyOrderFinanceQuery.setFinanceType(StatementTypeEnum.SUPPLY_ORDER_AMT.key);
        SupplyOrderFinancePO supplyOrderFinancePO = supplyOrderFinanceMapper.selectOne(supplyOrderFinanceQuery);
        SupplyOrderFinancePO supplyOrderFinanceUpdate = new SupplyOrderFinancePO();
        supplyOrderFinanceUpdate.setId(supplyOrderFinancePO.getId());
        supplyOrderFinanceUpdate.setUnpaidAmt(supplyOrderFinancePO.getUnpaidAmt().add(request.getBasePriceTotalAmt()));
        if (BigDecimal.ZERO.compareTo(supplyOrderFinanceUpdate.getUnpaidAmt()) == 0) {
            supplyOrderFinanceUpdate.setSettlementStatus(1);
        } else {
            supplyOrderFinanceUpdate.setSettlementStatus(0);
        }
        // 更新对账状态
        if (!Objects.equals(supplyOrderFinancePO.getCheckStatus(), CheckStatusEnum.CHECKING.key)) {
            if (BigDecimal.ZERO.compareTo(supplyOrderFinanceUpdate.getUnpaidAmt()) == 0) {
                // 如果未收金额为0，则改为已对账
                supplyOrderFinanceUpdate.setCheckStatus(CheckStatusEnum.CANNOT_CHECK.key);
            } else {
                // 如果未收金额不为0，则改为可出账
                supplyOrderFinanceUpdate.setCheckStatus(CheckStatusEnum.CAN_CHECK.key);
            }
        }
        supplyOrderFinanceMapper.updateByPrimaryKeySelective(supplyOrderFinanceUpdate);

        // 6. 记日志
        orderCommonService.saveOrderLog(
                orderPO.getId(),
                request.getOperator(),
                request.getOrderOwnerName(),
                supplyOrderPO.getSupplyOrderCode(),
                "添加客房（" + supplyProductPO.getRoomName() + "|" + supplyProductPO.getProductName() + "）成功"
        );
    }

    @Override
    @Transactional
    public void deleteProduct(SupplyProductIdDTO request) {
        SupplyProductPO supplyProductPO = supplyProductMapper.selectByPrimaryKey(request.getSupplyProductId());
        SupplyOrderPO supplyOrderPO = supplyOrderMapper.selectByPrimaryKey(supplyProductPO.getSupplyOrderId());
        OrderPO orderPO = orderMapper.selectByPrimaryKey(supplyOrderPO.getOrderId());

        // 1. 删除供货价格表
        SupplyProductPricePO productPriceDelete = new SupplyProductPricePO();
        productPriceDelete.setSupplyProductId(request.getSupplyProductId());
        supplyProductPriceMapper.delete(productPriceDelete);
        // 2. 删除供货产品表
        SupplyProductPO supplyProductDelete = new SupplyProductPO();
        supplyProductDelete.setId(request.getSupplyProductId());
        supplyProductMapper.delete(supplyProductDelete);
        // 3. 如果供货单下面没产品， 删除供货单
        SupplyProductPO supplyProductQuery = new SupplyProductPO();
        supplyProductQuery.setSupplyOrderId(supplyOrderPO.getId());
        List<SupplyProductPO> supplyProductPOList = supplyProductMapper.select(supplyProductQuery);
        if (CollUtilX.isEmpty(supplyProductPOList)) {
            supplyOrderMapper.deleteByPrimaryKey(supplyOrderPO.getId());
        } else {
            // 4. 如果供货单下面有产品， 更新供货单表总金额、冗余字段
            SupplyOrderPO supplyOrderUpdate = new SupplyOrderPO();
            supplyOrderUpdate.setId(supplyOrderPO.getId());
            supplyOrderUpdate.setBasePrice(supplyOrderPO.getBasePrice().subtract(supplyProductPO.getBasePriceTotalAmt()));
            supplyOrderUpdate.setSupplyOrderAmt(supplyOrderPO.getSupplyOrderAmt().subtract(supplyProductPO.getBasePriceTotalAmt()));
            // 更新供货单冗余的产品信息
            SupplyProductPO firstSupplyProductQuery = new SupplyProductPO();
            firstSupplyProductQuery.setSupplyOrderId(supplyOrderPO.getId());
            SupplyProductPO supplyFirstProduct = supplyProductMapper.select(firstSupplyProductQuery).get(0);
            supplyOrderUpdate.setStartDate(supplyFirstProduct.getStartDate());
            supplyOrderUpdate.setEndDate(supplyFirstProduct.getEndDate());
            supplyOrderUpdate.setRoomQty(supplyFirstProduct.getRoomQty());
            supplyOrderUpdate.setRoomName(supplyFirstProduct.getRoomName());
            supplyOrderUpdate.setProductName(supplyFirstProduct.getProductName());
            supplyOrderMapper.updateByPrimaryKeySelective(supplyOrderUpdate);
        }

        // 5. 更新订单金额
        SupplyOrderPO supplyOrderQuery = new SupplyOrderPO();
        supplyOrderQuery.setOrderId(supplyOrderPO.getOrderId());
        List<SupplyOrderPO> supplyOrderPOList = supplyOrderMapper.select(supplyOrderQuery);
        BigDecimal supplyOrderSum = BigDecimal.ZERO;
        BigDecimal rewardSum = BigDecimal.ZERO;
        BigDecimal rebateSum = BigDecimal.ZERO;
        for (SupplyOrderPO supplyOrder : supplyOrderPOList) {
            BigDecimal supplierToAgentRate = Objects.nonNull(supplyOrder.getSupplierToAgentRate()) ? supplyOrder.getSupplierToAgentRate() : BigDecimal.ONE;
            supplyOrderSum = supplyOrderSum.add(supplyOrder.getSupplyOrderAmt().multiply(supplierToAgentRate));
            rewardSum = rewardSum.add((Objects.nonNull(supplyOrder.getRewardAmt()) ? supplyOrder.getRewardAmt() : BigDecimal.ZERO).multiply(supplierToAgentRate));
            rebateSum = rebateSum.add((Objects.nonNull(supplyOrder.getRebateAmt()) ? supplyOrder.getRebateAmt() : BigDecimal.ZERO).multiply(supplierToAgentRate));
        }
        OrderPO orderUpdate = new OrderPO();
        orderUpdate.setId(orderPO.getId());
        orderUpdate.setProfit(orderPO.getOrderAmt().subtract(supplyOrderSum).add(rewardSum).subtract(rebateSum));
        orderMapper.updateByPrimaryKeySelective(orderUpdate);


        try {
            // 6. 退配额
            Map<String, String> data = new HashMap<>();
            data.put("begin", DateUtilX.dateToString(supplyProductPO.getStartDate()));
            data.put("end", DateUtilX.dateToString(supplyProductPO.getEndDate()));
            List<String> list = orderMapper.queryBetweenDate(data);
            StringBuilder saleDate1 = new StringBuilder();
            for (String s : list) {
                saleDate1.append(s).append(",");
            }
            QuotaDTO quotaDTO = new QuotaDTO();
            quotaDTO.setSaleDate(saleDate1.substring(0, saleDate1.length() - 1));
            quotaDTO.setQuota(-supplyProductPO.getRoomQty());//减配额
            quotaDTO.setOrderId(orderPO.getId());
            quotaDTO.setOrderCode(orderPO.getOrderCode());
            quotaDTO.setSupplyOrderId(supplyOrderPO.getId());
            quotaDTO.setSupplyOrderCode(supplyOrderPO.getSupplyOrderCode());
            quotaDTO.setSupplierCode(supplyOrderPO.getSupplierCode());
            quotaDTO.setProductId(Integer.valueOf(supplyProductPO.getProductId()));
            quotaRemote.modifyQuota(quotaDTO);
        } catch (Exception e) {
            log.error("扣配额异常！订单ID：" + orderPO.getId(), e);
        }

        // 更新订单未结算金额和结算状态
        if (CollUtilX.isNotEmpty(supplyProductPOList)) {
            SupplyOrderFinancePO supplyOrderFinanceQuery = new SupplyOrderFinancePO();
            supplyOrderFinanceQuery.setSupplyOrderId(supplyOrderPO.getId());
            supplyOrderFinanceQuery.setFinanceType(StatementTypeEnum.SUPPLY_ORDER_AMT.key);
            SupplyOrderFinancePO supplyOrderFinancePO = supplyOrderFinanceMapper.selectOne(supplyOrderFinanceQuery);
            SupplyOrderFinancePO supplyOrderFinanceUpdate = new SupplyOrderFinancePO();
            supplyOrderFinanceUpdate.setId(supplyOrderFinancePO.getId());
            supplyOrderFinanceUpdate.setUnpaidAmt(supplyOrderFinancePO.getUnpaidAmt().subtract(supplyProductPO.getBasePriceTotalAmt()));
            if (BigDecimal.ZERO.compareTo(supplyOrderFinanceUpdate.getUnpaidAmt()) == 0) {
                supplyOrderFinanceUpdate.setSettlementStatus(1);
            } else {
                supplyOrderFinanceUpdate.setSettlementStatus(0);
            }
            // 更新对账状态
            if (!Objects.equals(supplyOrderFinancePO.getCheckStatus(), CheckStatusEnum.CHECKING.key)) {
                if (BigDecimal.ZERO.compareTo(supplyOrderFinanceUpdate.getUnpaidAmt()) == 0) {
                    // 如果未收金额为0，则改为已对账
                    supplyOrderFinanceUpdate.setCheckStatus(CheckStatusEnum.CANNOT_CHECK.key);
                } else {
                    // 如果未收金额不为0，则改为可出账
                    supplyOrderFinanceUpdate.setCheckStatus(CheckStatusEnum.CAN_CHECK.key);
                }
            }
            supplyOrderFinanceMapper.updateByPrimaryKeySelective(supplyOrderFinanceUpdate);
        }

        // 7. 记日志
        orderCommonService.saveOrderLog(
                orderPO.getId(),
                request.getOperator(),
                request.getOrderOwnerName(),
                supplyOrderPO.getSupplyOrderCode(),
                "删除客房（" + supplyProductPO.getRoomName() + "|" + supplyProductPO.getProductName() + "）成功"
        );
    }

    @Override
    public Response<Object> sendToSupplier(SendToSupplierDTO request) {
        try {
            // 基础校验和初始化
            SupplyOrderContext context = initializeContext(request);

            // 执行核心业务流程
            processSupplyOrderRequest(context);

            // 处理结果操作
            handlePostProcessing(context);

            // 构建最终响应
            return buildFinalResponse(context);
        } catch (SysException e) {
            log.error("System exception occurred: {}", e.getMessage(), e);
            return Response.error(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("Unexpected error occurred: {}", e.getMessage(), e);
            return Response.error(ErrorCodeEnum.SYSTEM_EXCEPTION.errorCode, ErrorCodeEnum.SYSTEM_EXCEPTION.errorDesc);
        }
    }

    /**
     * 初始化上下文和数据
     */
    private SupplyOrderContext initializeContext(SendToSupplierDTO request) {
        request.setSendingType(SendingTypeEnum.DIRECT_LINK.key);// 默认直连

        SupplyOrderContext context = new SupplyOrderContext();
        context.setRequest(request);

        // 验证并获取供货单信息
        context.setSupplyOrder(validateAndGetSupplyOrder(request));
        // 验证并获取订单信息
        context.setOrder(validateAndGetOrder(context.getSupplyOrder()));
        // 获取供货单产品
        context.setSupplyOrderProduct(getSupplyProduct(request.getSupplyOrderId()));
        // 创建发单请求记录
        context.setSupplyRequest(createSupplyRequestRecord(request));

        // 设置其他依赖的Mapper和Service
        context.setOrderMapper(orderMapper);
        context.setOrderService(orderService);
        context.setGuestMapper(guestMapper);
        context.setOrderRequestMapper(orderRequestMapper);
        context.setSupplyProductPriceMapper(supplyProductPriceMapper);
        context.setSupplyOrderMapper(supplyOrderMapper);
        context.setSupplyOrderService(this);
        context.setSupplyRequestMapper(supplyRequestMapper);
        context.setOrderNotifyRemote(orderNotifyRemote);
        context.setSupplyOrderFinanceMapper(supplyOrderFinanceMapper);
        context.setOrderCommonService(orderCommonService);
        context.setSupplyDirectShubOrderRemote(supplyDirectShubOrderRemote);
        context.setDisB2BRemote(disB2BRemote);

        return context;
    }

    /**
     * 验证并获取订单信息
     */
    private OrderPO validateAndGetOrder(SupplyOrderPO supplyOrder) {
        OrderPO order = orderMapper.selectByPrimaryKey(supplyOrder.getOrderId());
        if (order == null) {
            log.error("Order not found for supply order: {}", supplyOrder.getId());
            throw new SysException(ErrorCodeEnum.ORDER_NOT_EXISTS);
        }

        if (order.getOrderConfirmationStatus() == OrderStatusEnum.REFUSE.getNo()) {
            log.error("Completed order cannot be processed: {}", order.getId());
            throw new SysException(ErrorCodeEnum.REFUSE_ORDERS_CAN_BE_ISSUED_TO_SUPPLIERS);
        }
        return order;
    }

    /**
     * 获取供货单产品信息
     */
    private SupplyOrderProductDTO getSupplyProduct(Integer supplyOrderId) {
        List<SupplyOrderProductDTO> dtoList = supplyProductMapper.querySupplyOrderProductList(String.valueOf(supplyOrderId));

        if (CollUtilX.isEmpty(dtoList)) {
            log.warn("No products found for supply order: {}", supplyOrderId);
            return null;
        }

        return dtoList.get(0);
    }

    /**
     * 创建发单请求记录
     */
    private SupplyRequestPO createSupplyRequestRecord(SendToSupplierDTO request) {
        SupplyRequestPO record = new SupplyRequestPO();
        record.setSupplyOrderId(request.getSupplyOrderId());
        record.setSupplyOrderType(request.getSupplyOrderType());
        record.setSendingType(request.getSendingType());// 直连
        record.setSendingResult(SendingResultEnum.UNSEND.key);
        record.setMerchantRemark(request.getRemark());
        record.setCreatedBy(request.getOperator());
        record.setCreatedDt(DateUtilX.dateToString(new Date(), hour_format));

        supplyRequestMapper.insertSelective(record);
        return record;
    }

    /**
     * 执行供货单请求
     */
    private void processSupplyOrderRequest(SupplyOrderContext context) {
        // 基础校验
        validateBasicConditions(context);

        // 发单类型校验
        validateSupplyOrderType(context);

        // 记录操作日志
        logOperation(context);

        // 执行发单操作
        executeOrderSending(context);

        // 更新发单状态
        updateSendingStatus(context);
    }

    /**
     * 验证基础条件
     */
    private void validateBasicConditions(SupplyOrderContext context) {
        // 自动发单验证
        validateAutoSendConditions(context);
        // 验证产品要求
        validateProductRequirements(context);
    }

    /**
     * 自动发单验证
     */
    private void validateAutoSendConditions(SupplyOrderContext context) {
        if (!context.getRequest().isAutoSendOrder()) return;

        // 检查取消修改申请
        if (hasPendingModificationRequest(context.getOrder().getId())) {
            log.info("Pending modification requests exist");
            throw new SysException(ErrorCodeEnum.CANCELLATION_MODIFICATION_REQUEST);
        }

        // 验证支付状态条件
        validatePaymentConditions(context.getOrder());
    }

    /**
     * 检查是否有待处理的取消修改申请
     */
    private boolean hasPendingModificationRequest(Integer orderId) {
        Example example = new Example(OrderRequestPO.class);
        example.createCriteria()
                .andEqualTo("orderId", orderId)
                .andEqualTo("handleResult", 0); // 0表示未处理
        return orderRequestMapper.selectCountByExample(example) > 0;
    }

    /**
     * 验证支付状态条件
     */
    private void validatePaymentConditions(OrderPO order) {
        boolean isPayOnStore = PayMethodEnum.PAY.key.equals(order.getPayMethod());
        boolean validStatus = order.getOrderConfirmationStatus().equals(ConfirmationStatusEnum.UNCONFIRM.key)
                && order.getPayStatus().equals(PayStatusEnum.PAY.getNo());

        if (!isPayOnStore && !validStatus) {
            log.info("Invalid payment conditions for order: {}", order.getId());
            throw new SysException(ErrorCodeEnum.AUTOMATIC_ISSUE_ORDER_STATUS_AND_PAYMENT_STATUS_ARE_NOT_SATISFIED_ISSUE_FAILURE);
        }
    }

    /**
     * 验证产品要求
     */
    private void validateProductRequirements(SupplyOrderContext context) {
        if (!requiresProductCheck(context.getRequest().getSupplyOrderType())) return;

        // 查询供货单产品
        SupplyProductPO supplyProductQuery = new SupplyProductPO();
        supplyProductQuery.setSupplyOrderId(context.getRequest().getSupplyOrderId());
        List<SupplyProductPO> supplyProductPOList = supplyProductMapper.select(supplyProductQuery);
        if (CollUtilX.isEmpty(supplyProductPOList) || (StrUtilX.isEmpty(supplyProductPOList.get(0).getProductId()))) {
            log.info("Missing product information");
            throw new SysException(ErrorCodeEnum.FAILED_TO_QUERY_THE_PRODUCT_UNABLE_TO_ISSUE_AN_ORDER);
        }
    }

    /**
     * 判断是否需要产品检查
     */
    private boolean requiresProductCheck(Integer supplyOrderType) {
        return Objects.equals(supplyOrderType, SupplyOrderTypeEnum.BOOK.key)
                || Objects.equals(supplyOrderType, SupplyOrderTypeEnum.CANCEL.key);
    }

    /**
     * 处理结果操作
     */
    private void handlePostProcessing(SupplyOrderContext context) {
        // 处理备注信息
        handleOrderRemarks(context);

        // 处理异常情况
        handleExceptions(context);
    }

    /**
     * 处理订单备注
     */
    private void handleOrderRemarks(SupplyOrderContext context) {
        if (StrUtilX.isEmpty(context.getRequest().getRemark())) return;

        OrderRemarkPO remark = new OrderRemarkPO();
        remark.setOrderId(context.getSupplyOrder().getOrderId());
        remark.setRemarkType(RemarkTypeEnum.SUPPLY_NOTE.key);
        remark.setReceiver(context.getSupplyOrder().getSupplyOrderCode());
        remark.setRemark(context.getRequest().getRemark());
        remark.setCreatedBy(context.getRequest().getOperator());
        remark.setCreatedDt(DateUtilX.dateToString(new Date(), hour_format));

        orderRemarkMapper.insert(remark);
    }

    /**
     * 验证并获取供货单信息
     */
    private SupplyOrderPO validateAndGetSupplyOrder(SendToSupplierDTO request) {
        SupplyOrderPO supplyOrder = supplyOrderMapper.selectByPrimaryKey(request.getSupplyOrderId());
        if (supplyOrder == null) {
            log.error("Supply order not found: {}", request);
            throw new SysException(ErrorCodeEnum.SUPPLYORDER_NOT_EXIST);
        }

        if (supplyOrder.getConfirmationStatus() == OrderStatusEnum.CANCELED.getNo()
                || supplyOrder.getConfirmationStatus() == OrderStatusEnum.REFUSE.getNo()) {
            log.error("Invalid order status: {}", supplyOrder);
            throw new SysException(ErrorCodeEnum.CENTER_CONFIRMED_NO_MORE_SUPPLY_ORDERS_CAN_BE_ISSUED_TO_SUPPLIERS);
        }
        return supplyOrder;
    }

    /**
     * 验证发单类型
     */
    private void validateSupplyOrderType(SupplyOrderContext context) {
        switch (SupplyOrderTypeEnum.getEnumByKey(context.getRequest().getSupplyOrderType())) {
            case BOOK:
                validateBookOrder(context);
                break;
            case CANCEL:
                validateCancelOrder(context);
                validateYetBookOrder(context);
                break;
            case REVISE:
                validateReviseOrder(context);
                validateYetBookOrder(context);
                break;
            case RESEND:
                validateResendOrder(context);
                validateYetBookOrder(context);
                break;
            default:
                throw new SysException(ErrorCodeEnum.INVALID_SUPPLY_ORDER_TYPE);
        }
    }

    /**
     * 验证预定单
     */
    private void validateBookOrder(SupplyOrderContext context) {
        // 1. 创建 Example 对象
        Example example = new Example(SupplyRequestPO.class);

        // 2. 创建 Criteria 并添加到 Example 中
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("supplyOrderId", context.getRequest().getSupplyOrderId())
                .andEqualTo("supplyOrderType", SupplyOrderTypeEnum.BOOK.key)
                .andIn("sendingResult", Arrays.asList(
                        SendingResultEnum.SUCCESS.key,
                        SendingResultEnum.ABNORMAL.key
                ));

        // 3. 传递完整的 Example 对象到 Mapper
        List<SupplyRequestPO> existingRequests = supplyRequestMapper.selectByExample(example);

        // 4. 处理预定单异常
        if (CollUtilX.isNotEmpty(existingRequests)) {
            handleBookOrderExceptions(context, existingRequests);
        }
    }

    /**
     * 处理预定单异常
     */
    private void handleBookOrderExceptions(SupplyOrderContext context, List<SupplyRequestPO> requests) {
        boolean hasSuccess = requests.stream()
                .anyMatch(r -> Objects.equals(r.getSendingResult(), SendingResultEnum.SUCCESS.key));

        if (hasSuccess) {
            throw new SysException(ErrorCodeEnum.RESERVATIONS_CAN_ONLY_BE_SENT_ONCE);
        }

        boolean hasAbnormal = requests.stream()
                .anyMatch(r -> Objects.equals(r.getSendingResult(), SendingResultEnum.ABNORMAL.key));

        if (hasAbnormal && !isRetryRequest(context.getRequest())) {
            throw new SysException(ErrorCodeEnum.RESERVATION_HAS_BEEN_ISSUED);
        }
    }

    /**
     * 判断是否为重复请求
     */
    private boolean isRetryRequest(SendToSupplierDTO request) {
        return request.getIsRepeat() != null
                && request.getIsRepeat() == 1
                && SupplyOrderTypeEnum.BOOK.key.equals(request.getSupplyOrderType());
    }

    /**
     * 验证取消单
     */
    private void validateCancelOrder(SupplyOrderContext context) {
        if (StrUtilX.isEmpty(context.getSupplyOrder().getSupplierOrderCode())) {
            throw new SysException(ErrorCodeEnum.NO_RESERVATION_HAS_BEEN_ISSUED_CANNOT_OPERATE);
        }
        // 存在未完成的修改请求
        Example example = new Example(SupplyRequestPO.class);
        example.createCriteria()
                .andEqualTo("supplyOrderId", context.getRequest().getSupplyOrderId())
                .andEqualTo("sendingResult", SendingResultEnum.SUCCESS.key)
                .andEqualTo("thisConfirmationStatus", ConfirmationStatusEnum.UNCONFIRM.key)
                .andIn("supplyOrderType", Arrays.asList(
                        SupplyOrderTypeEnum.REVISE.key,
                        SupplyOrderTypeEnum.CANCEL.key
                ));

        if (supplyRequestMapper.selectCountByExample(example) > 0) {
            throw new SysException(ErrorCodeEnum.UNABLE_TO_INITIATE_A_NEW_APPLICATION);
        }

        // 取消超时校验
        if (context.getSupplyOrderProduct() != null && context.getSupplyOrderProduct().getProductId() != null) {
            context.setCancelIsTimeout(cancelOrderIsTimeout(
                    context.getSupplyOrderProduct().getProductId(),
                    context.getSupplyOrder().getStartDate(),
                    context.getSupplyOrder().getEndDate()
            ));
        }
    }

    /**
     * 验证修改单
     */
    private void validateReviseOrder(SupplyOrderContext context) {
        // 供货单必须已确认
        if (!Objects.equals(context.getSupplyOrder().getConfirmationStatus(), ConfirmationStatusEnum.CONFIRMED.key)) {
            throw new SysException(ErrorCodeEnum.SUPPLYORDER_NOT_CONFIRMED_NO_MODIFICATION_ORDERS_CAN_BE_ISSUED);
        }
        // 存在未完成的修改请求
        Example example = new Example(SupplyRequestPO.class);
        example.createCriteria()
                .andEqualTo("supplyOrderId", context.getRequest().getSupplyOrderId())
                .andEqualTo("sendingResult", SendingResultEnum.SUCCESS.key)
                .andEqualTo("thisConfirmationStatus", ConfirmationStatusEnum.UNCONFIRM.key)
                .andIn("supplyOrderType", Arrays.asList(
                        SupplyOrderTypeEnum.REVISE.key,
                        SupplyOrderTypeEnum.CANCEL.key
                ));

        if (supplyRequestMapper.selectCountByExample(example) > 0) {
            throw new SysException(ErrorCodeEnum.UNABLE_TO_INITIATE_A_NEW_APPLICATION);
        }
    }

    /**
     * 验证重发单
     */
    private void validateResendOrder(SupplyOrderContext context) {
        // 供货单不能已确认
        if (Objects.equals(context.getSupplyOrder().getConfirmationStatus(), ConfirmationStatusEnum.CONFIRMED.key)) {
            throw new SysException(ErrorCodeEnum.SUPPLYORDER_CONFIRMED_RESERVATIONS_CANNOT_BE_REISSUED);
        }
    }

    /**
     * 验证订单是否已发过预订单
     */
    private void validateYetBookOrder(SupplyOrderContext context) {
        // 检查原预定单是否成功
        Example example = new Example(SupplyRequestPO.class);
        example.createCriteria()
                .andEqualTo("supplyOrderId", context.getRequest().getSupplyOrderId())
                .andEqualTo("supplyOrderType", SupplyOrderTypeEnum.BOOK.key)
                .andEqualTo("sendingResult", SendingResultEnum.SUCCESS.key);

        if (supplyRequestMapper.selectCountByExample(example) == 0) {
            throw new SysException(ErrorCodeEnum.NO_RESERVATION_HAS_BEEN_ISSUED_CANNOT_OPERATE);
        }
    }

    /**
     * 执行发单操作
     */
    private void executeOrderSending(SupplyOrderContext context) {
        OrderSendingStrategy strategy = OrderSendingStrategyFactory.getStrategy(
                context.getRequest().getSendingType(),
                context.getRequest().getSupplyOrderType()
        );
        strategy.execute(context);
    }

    /**
     * 更新发单状态
     */
    private void updateSendingStatus(SupplyOrderContext context) {
        SupplyOrderPO update = new SupplyOrderPO();
        update.setId(context.getRequest().getSupplyOrderId());
        update.setSendingStatus(determineSendingStatus(context));

        if (context.getRequest().getSendingType() == SendingTypeEnum.DIRECT_LINK.key) {
            update.setOrderMethod(OrderMethodEnum.ORDER_METHOD_ENUM_1.key);
        }

        supplyOrderMapper.updateByPrimaryKeySelective(update);
    }

    /**
     * 判断发单状态
     */
    private Integer determineSendingStatus(SupplyOrderContext context) {
        if (SupplyOrderTypeEnum.BOOK == SupplyOrderTypeEnum.getEnumByKey(context.getRequest().getSupplyOrderType())) {
            return SendingStatusEnum.SEND_RESERVATION.key;
        } else if (SupplyOrderTypeEnum.RESEND == SupplyOrderTypeEnum.getEnumByKey(context.getRequest().getSupplyOrderType())) {
            return SendingStatusEnum.SEND_RESERVATION_AGAIN.key;
        } else if (SupplyOrderTypeEnum.REVISE == SupplyOrderTypeEnum.getEnumByKey(context.getRequest().getSupplyOrderType())) {
            return SendingStatusEnum.SEND_MODIFY.key;
        } else if (SupplyOrderTypeEnum.CANCEL == SupplyOrderTypeEnum.getEnumByKey(context.getRequest().getSupplyOrderType())) {
            return SendingStatusEnum.SEND_CANCEL.key;
        }
        return null;
    }

    /**
     * 记录操作日志
     */
    private void logOperation(SupplyOrderContext context) {
        String operation = String.format("发单给供应商（供货单：%s，发送方式：%s，发单类型：%s）",
                context.getSupplyOrder().getSupplyOrderCode(),
                SendingTypeEnum.getValueByKey(context.getRequest().getSendingType()),
                SupplyOrderTypeEnum.getValueByKey(context.getRequest().getSupplyOrderType()));

        orderCommonService.saveOrderLog(
                context.getSupplyOrder().getOrderId(),
                context.getRequest().getOperator(),
                context.getRequest().getOrderOwnerName(),
                context.getSupplyOrder().getSupplyOrderCode(),
                operation,
                null
        );
    }

    /**
     * 异常统一处理
     */
    private void handleExceptions(SupplyOrderContext context) {
        if (context.getResponse().getResult().equals(ResultCodeEnum.FAILURE.code)) {
            // 修改失败请求状态
            updateFailedRequest(context);
        } else {
            // 更新供货请求为：发送成功
            SupplyRequestPO supplyRequestUpdate = new SupplyRequestPO();
            supplyRequestUpdate.setId(context.getSupplyRequest().getId());
            supplyRequestUpdate.setSendingResult(SendingResultEnum.SUCCESS.key);
            supplyRequestMapper.updateByPrimaryKeySelective(supplyRequestUpdate);
        }

        // 处理取消订单超时
        if (context.isCancelIsTimeout()) {
            context.getResponse().setFailCode(ErrorCodeEnum.CANCELABLE_TIME_HAS_EXPIRED.errorCode);
            context.getResponse().setFailReason(ErrorCodeEnum.CANCELABLE_TIME_HAS_EXPIRED.errorDesc);
        }
    }

    /**
     * 修改失败请求状态
     */
    private void updateFailedRequest(SupplyOrderContext context) {
        if (context.getSendingResult() != null) {
            SupplyRequestPO update = new SupplyRequestPO();
            update.setId(context.getSupplyRequest().getId());
            update.setSendingResult(context.getSendingResult());
            supplyRequestMapper.updateByPrimaryKeySelective(update);
        }
    }

    /**
     * 构建最终响应
     */
    private Response<Object> buildFinalResponse(SupplyOrderContext context) {
        if (context.isCancelIsTimeout()) {
            return Response.error(ErrorCodeEnum.CANCELABLE_TIME_HAS_EXPIRED);
        }
        return context.getResponse();
    }

    /**
     * 推送状态给下游
     */
    private void pushAgentCancelFail(B2BOrderStatusPushRequest b2BOrderStatusPushRequest) {
        CompletableFuture.runAsync(() -> {
            try {
                disB2BRemote.orderStatusPush(b2BOrderStatusPushRequest);
            } catch (Exception e) {
                log.error("调用渠道取消订单结果异常 request:{}", JSONObject.toJSONString(b2BOrderStatusPushRequest), e);
            }
        });
    }

    /**
     * 判断订单条款是否超时
     */
    private boolean cancelOrderIsTimeout(int productId, Date startDate, Date endDate) {
        Map<String, String> product = new HashMap<>();
        product.put("productId", String.valueOf(productId));
        Response<ProductDTO> product1 = productRemote.queryProduct(product);
        if (product1.getResult().equals(ResultCodeEnum.SUCCESS.code) && null != product1.getModel()) {
            ProductDTO productDTOS = product1.getModel();
            if (productDTOS.getSupplyType() != null && productDTOS.getSupplyType() == 2) {
                productDTOS = assemblyRestrict(productId, startDate, endDate);
            }
            if (productDTOS.getCancellationType() != null && productDTOS.getCancellationType() == 1) {
                int nowDay = productDTOS.getCancellationAdvanceDays() != null ? productDTOS.getCancellationAdvanceDays() : 0;
                int nowHours = StrUtilX.isNotEmpty(productDTOS.getCancellationDueTime()) ? Integer.parseInt(productDTOS.getCancellationDueTime().split(":")[0]) : 0;
                int nowMinute = StrUtilX.isNotEmpty(productDTOS.getCancellationDueTime()) && productDTOS.getCancellationDueTime().contains(":") ?
                        Integer.parseInt(productDTOS.getCancellationDueTime().split(":")[1]) : 0;
                Date advanceTime = DateUtilX.getDate(startDate, -nowDay, nowHours, nowMinute);
                Date nowTime = DateUtilX.getCurrentDate();
                return advanceTime.compareTo(nowTime) <= 0;
            }
        }
        return false;
    }

    /**
     * 组装取消条款
     */
    private ProductDTO assemblyRestrict(Integer productId, Date startDate, Date endDate) {
        Set<String> saleDateList = DateUtilX.getDateListToString(startDate, DateUtilX.addDate(endDate, -1));

        ProductDTO productDTO = new ProductDTO();
        productDTO.setProductId(productId);
        if (CollUtilX.isNotEmpty(saleDateList)) {

            Integer cancellationType = null;
            Integer cancellationAdvanceDays = null;
            String cancellationDueTime = "";
            String cancellationDeductionTerm = "";
            for (String saleDate : saleDateList) {
                ProductRestrictDTO productRestrictDTO = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.productRestrictKey, StrUtilX.concat(String.valueOf(productId), "_", saleDate)),
                        ProductRestrictDTO.class);
                if (productRestrictDTO != null) {
                    //处理首日条款为空
                    if (null == cancellationType) {
                        cancellationType = productRestrictDTO.getCancellationType();
                        cancellationAdvanceDays = productRestrictDTO.getCancellationAdvanceDays() == null ? 0 : productRestrictDTO.getCancellationAdvanceDays();
                        cancellationDueTime = productRestrictDTO.getCancellationDueTime();
                        cancellationDeductionTerm = productRestrictDTO.getCancellationDeductionTerm();
                    } else {
                        if (cancellationType > productRestrictDTO.getCancellationType()) {
                            cancellationType = productRestrictDTO.getCancellationType();
                            cancellationAdvanceDays = productRestrictDTO.getCancellationAdvanceDays() == null ? 0 : productRestrictDTO.getCancellationAdvanceDays();
                            cancellationDueTime = productRestrictDTO.getCancellationDueTime();
                            cancellationDeductionTerm = productRestrictDTO.getCancellationDeductionTerm();
                        } else if (cancellationType.equals(productRestrictDTO.getCancellationType())) {
                            // 当前条款
                            int nowDay = productRestrictDTO.getCancellationAdvanceDays() != null ? productRestrictDTO.getCancellationAdvanceDays() : 0;
                            int nowHours = StrUtilX.isNotEmpty(productRestrictDTO.getCancellationDueTime()) ? Integer.parseInt(productRestrictDTO.getCancellationDueTime().split(":")[0]) : 0;

                            //筛选的条款
                            int beforeDay = cancellationAdvanceDays != null ? cancellationAdvanceDays : 0;
                            int beforeHours = StrUtilX.isNotEmpty(cancellationDueTime) ? Integer.parseInt(cancellationDueTime.split(":")[0]) : 0;
                            int beforeMinute = StrUtilX.isNotEmpty(cancellationDueTime) && cancellationDueTime.contains(":") ?
                                    Integer.parseInt(cancellationDueTime.split(":")[1]) : 0;

                            // 天数越大越严格
                            // 小时越小越严格
                            // 分钟越小越严格
                            if (beforeDay < nowDay || beforeHours > nowHours || beforeMinute > nowHours) {
                                cancellationType = productRestrictDTO.getCancellationType();
                                cancellationAdvanceDays = productRestrictDTO.getCancellationAdvanceDays();
                                cancellationDueTime = productRestrictDTO.getCancellationDueTime();
                                cancellationDeductionTerm = productRestrictDTO.getCancellationDeductionTerm();
                            }
                        }
                    }
                }
            }
            productDTO.setCancellationType(cancellationType);
            productDTO.setCancellationAdvanceDays(cancellationAdvanceDays);
            productDTO.setCancellationDeductionTerm(cancellationDeductionTerm);
            productDTO.setCancellationDueTime(cancellationDueTime);
        }
        return productDTO;
    }

//    /**
//     * 查询供货单产品信息
//     */
//    private SupplyOrderProductDTO getSupplyOrderProductDTO(String supplyOrderId) {
//        List<SupplyOrderProductDTO> supplyOrderProductDTOS = supplyProductMapper.querySupplyOrderProductList(supplyOrderId);
//        SupplyOrderProductDTO supplyProductPO = null;
//        if (CollUtilX.isNotEmpty(supplyOrderProductDTOS)) {
//            supplyProductPO = supplyOrderProductDTOS.get(0);
//        }
//        return supplyProductPO;
//    }


    @Override
    public Response<Object> saveSupplyResult(SaveSupplyResultDTO request) {
        Example example = new Example(SupplyOrderPO.class);
        //三个值 必须有其中一个
        if (StrUtilX.isEmpty(request.getSupplyOrderCode()) && StrUtilX.isEmpty(request.getSupplierOrderCode())
                && null == request.getSupplyOrderId()) {
            log.error("saveSupplyResult Exception param: " + JSONObject.toJSONString(request) + " getSupplyOrderCode or getSupplierOrderCode is null !");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
        Example.Criteria criterias = example.createCriteria();
        if (request.getSupplyOrderId() != null) {
            criterias.andEqualTo("id", request.getSupplyOrderId());
        } else {
            //2个条件可能其中有个没有传回来 但是数据库有值
            if (StrUtilX.isNotEmpty(request.getSupplyOrderCode())) {
                criterias.andEqualTo("supplyOrderCode", request.getSupplyOrderCode());
            }
//            if (StrUtilX.isNotEmpty(request.getSupplierOrderCode())) {
//                criterias.andEqualTo("supplierOrderCode", request.getSupplierOrderCode());
//            }
        }
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("supplyOrderMapper.selectOneByExample");
        SupplyOrderPO supplyOrderPO = supplyOrderMapper.selectOneByExample(example);
        stopWatch.stop();
        if (supplyOrderPO == null) {
            log.error("saveSupplyResult Exception param: " + JSONObject.toJSONString(request) + " SupplyOrderPO is null !");
            throw new SysException(ErrorCodeEnum.SUPPLY_ORDER_NOT_EXIST);
        }
        // 供应商推送状态，当供货单是已取消的时候，直接返回成功
        if (Objects.equals(supplyOrderPO.getConfirmationStatus(), 2) && request.getIsTask() == 1) {
            return Response.success();
        }
        stopWatch.start("orderMapper.selectByPrimaryKey");
        OrderPO orderPO = orderMapper.selectByPrimaryKey(supplyOrderPO.getOrderId());
        stopWatch.stop();
        if (orderPO == null) {
            throw new SysException(ErrorCodeEnum.ORDER_NOT_EXISTS);
        }
        //取消拒绝结果特殊处理
        if (Objects.equals(supplyOrderPO.getConfirmationStatus(), 3) && request.getIsTask() == 1) {//处理redis消费订单状态结果 供应商拒绝取消处理
            this.cancelOrderFailResult(orderPO.getId(), "system");
            return Response.success();
        }
        //考虑一单多供和状态修改 供应商酒店确认号变更修改 需要修改供应商中的确认号以及订单确认号
        if ((StrUtilX.isNotEmpty(supplyOrderPO.getConfirmationCode()) && StrUtilX.isNotEmpty(request.getConfirmationCode())
                && !supplyOrderPO.getConfirmationCode().equals(request.getConfirmationCode()))
                || (StrUtilX.isEmpty(supplyOrderPO.getConfirmationCode()) && StrUtilX.isNotEmpty(request.getConfirmationCode()))) {
            SupplyOrderPO supplyOrderPOupdate = new SupplyOrderPO();
            supplyOrderPOupdate.setId(supplyOrderPO.getId());
            supplyOrderPOupdate.setConfirmationCode(request.getConfirmationCode());
            supplyOrderMapper.updateByPrimaryKeySelective(supplyOrderPOupdate);
            //酒店确认号变更修改 需要修改订单的扩展表确认号 订单表中的确认号无效了
            Set<String> supplyConfirmationCodeSet = new HashSet<>();
            //查询供货单 排序下
            Example exampleSupplyOrderDesc = new Example(SupplyOrderPO.class);
            exampleSupplyOrderDesc.setOrderByClause("id DESC");
            Example.Criteria criteria = exampleSupplyOrderDesc.createCriteria();
            criteria.andEqualTo("orderId", supplyOrderPO.getOrderId());
            List<SupplyOrderPO> supplyOrderPOList = supplyOrderMapper.selectByExample(exampleSupplyOrderDesc);
            for (SupplyOrderPO supplyOrder : supplyOrderPOList) {
                //有确认号，并且确认状态为待确认的时候，才把确认号写到订单扩展中
                if (StrUtilX.isNotEmpty(supplyOrder.getConfirmationCode())) {
                    supplyConfirmationCodeSet.add(supplyOrder.getConfirmationCode());
                }
            }
            OrderExtendPO queryPO = new OrderExtendPO();
            queryPO.setOrderId(supplyOrderPO.getOrderId());
            List<OrderExtendPO> orderExtendPOList = orderExtendMapper.select(queryPO);
            if (CollUtilX.isNotEmpty(orderExtendPOList)) {
                OrderExtendPO updatePO = new OrderExtendPO();
                updatePO.setConfirmationCode(StrUtilX.listToString(supplyConfirmationCodeSet, ","));
                updatePO.setId(orderExtendPOList.get(0).getId());
                orderExtendMapper.updateByPrimaryKeySelective(updatePO);
            }
        }
        // 状态一致不处理
        if (Objects.equals(supplyOrderPO.getConfirmationStatus(), request.getConfirmationStatus())) {
            log.info("保存供货单结果，状态一致不处理，thisConfirmationStatus={}, request={}", supplyOrderPO.getConfirmationStatus(), JSONUtil.toJsonStr(request));
            return Response.success();
        }
        if (request.getRefundFee() != null && supplyOrderPO.getBasePrice().compareTo(request.getRefundFee()) < 0) {
            throw new SysException(ErrorCodeEnum.CANCEL_SUPPLIER_ORDER_AMOUNT_IS_ERROR);
        }

        try {
            stopWatch.start("exchangeRateRemote.queryExchangeRate");
            stopWatch.stop();

            stopWatch.start("1. 更新更新供货请求结果");
            // 1. 更新更新供货请求结果
            Example supplyRequestExample = new Example(SupplyRequestPO.class);
            supplyRequestExample.setOrderByClause("id desc");
            Example.Criteria supplyRequestCriteria = supplyRequestExample.createCriteria();
            supplyRequestCriteria.andEqualTo("supplyOrderId", supplyOrderPO.getId());//处理紧急bug 之前使request.getSupplyOrderId()肯定错误
            List<SupplyRequestPO> supplyRequestPOList = supplyRequestMapper.selectByExample(supplyRequestExample);
            if (CollUtilX.isNotEmpty(supplyRequestPOList) && !Objects.equals(request.getConfirmationStatus(), ConfirmationStatusEnum.DONE.key)) {
                SupplyRequestPO supplyRequestUpdate = new SupplyRequestPO();
                supplyRequestUpdate.setId(supplyRequestPOList.get(0).getId());
                supplyRequestUpdate.setThisConfirmationStatus(request.getConfirmationStatus());
                supplyRequestUpdate.setThisConfirmationCode(request.getConfirmationCode());
                supplyRequestUpdate.setThisSupplierConfirmer(request.getSupplierConfirmer());
                supplyRequestUpdate.setThisRefundFee(request.getRefundFee());
                supplyRequestUpdate.setThisRefusedReason(request.getRefusedReason());
                if (supplyRequestUpdate.getThisRefusedReason() != null && supplyRequestUpdate.getThisRefusedReason().length() > 128) {
                    supplyRequestUpdate.setThisRefusedReason(supplyRequestUpdate.getThisRefusedReason().substring(0, 128));
                }
                supplyRequestUpdate.setThisConfirmationRemark(request.getRemark());
                supplyRequestUpdate.setUpdatedBy(request.getOperator());
                supplyRequestUpdate.setUpdatedDt(DateUtilX.dateToString(new Date(), hour_format));
                supplyRequestMapper.updateByPrimaryKeySelective(supplyRequestUpdate);
            }
            stopWatch.stop();

            stopWatch.start("2. 更新供货单状态");
            // 2. 更新供货单状态
            SupplyOrderPO supplyOrderUpdate = new SupplyOrderPO();
            supplyOrderUpdate.setId(supplyOrderPO.getId());
            supplyOrderUpdate.setConfirmationStatus(request.getConfirmationStatus());
            supplyOrderUpdate.setConfirmationCode(request.getConfirmationCode());
            supplyOrderUpdate.setSupplierConfirmer(request.getSupplierConfirmer());
            supplyOrderUpdate.setConfirmationRemark(request.getRemark());
            if (request.getRefundFee() != null && BigDecimal.ZERO.compareTo(request.getRefundFee()) != 0) {
                // 如果退订费不为0，则更新供货单金额
                supplyOrderUpdate.setRefundFee(request.getRefundFee());
                BigDecimal commission = supplyOrderPO.getCommission() == null ? BigDecimal.ZERO : supplyOrderPO.getCommission();
                supplyOrderUpdate.setSupplyOrderAmt(supplyOrderPO.getBasePrice().add(request.getRefundFee()).subtract(commission));
            }
            int eCancelSupplyOrder = 0;// 异常取消供货单标识 0否 1是 必需设置 否者update无法针对null 恢复这个状态
            String eCancelSupplyOrderContext = null;//异常取消供货单描述
            if (request.getConfirmationStatus().equals(ConfirmationStatusEnum.CANCELED.key)) {
                if (supplyOrderPO.getCancelExceptionFlag() != null && supplyOrderPO.getCancelExceptionFlag() == 1) {
                    SupplyOrderPO supplyOrderPO11 = new SupplyOrderPO();
                    supplyOrderPO11.setId(supplyOrderPO.getId());
                    supplyOrderPO11.setCancelExceptionFlag(0);
                    supplyOrderMapper.updateByPrimaryKeySelective(supplyOrderPO11);//只需要把异常标识改掉
                }
                supplyOrderUpdate.setRewardAmt(BigDecimal.ZERO);
                supplyOrderUpdate.setRebateAmt(BigDecimal.ZERO);
                // 现付订单、金额 = 0、预付订单、金额 = 退改费
                if (orderPO.getPayMethod() != null && orderPO.getPayMethod().equals(PayMethodEnum.PAY.key)) {
                    supplyOrderUpdate.setSupplyOrderAmt(BigDecimal.ZERO);
                } else {
                    supplyOrderUpdate.setSupplyOrderAmt(request.getRefundFee() == null ? BigDecimal.ZERO : request.getRefundFee());
                }
                //取消时，保持原有的佣金，不重置为0
                //supplyOrderUpdate.setCommission(BigDecimal.ZERO);//取消时，佣金设置为0
                // 由已确认变取消
                if (request.getIsTask() == 1 && supplyOrderPO.getConfirmationStatus() != null && supplyOrderPO.getConfirmationStatus().equals(ConfirmationStatusEnum.CONFIRMED.key)) {
                    eCancelSupplyOrder = 1;// 标记为异常取消
                    eCancelSupplyOrderContext = "供货单异常取消：供货单" + supplyOrderPO.getSupplyOrderCode() + "于" + DateUtilX.dateToString(DateUtilX.getCurrentDate(), hour_format) + "由供应商取消，请确认供货单是否异常，并处理。";
                }
            } else {
                // 供应商奖励不为空
                if (request.getSupplyReward() != null) {
                    supplyOrderUpdate.setRewardAmt(request.getSupplyReward());
                }
            }
            if (StrUtilX.isNotEmpty(request.getSupplierOrderCode())) {
                supplyOrderUpdate.setSupplierOrderCode(request.getSupplierOrderCode());
            }
            supplyOrderUpdate.setRefusedReason(request.getRefusedReason());
            supplyOrderUpdate.setECancelSupplyOrder(eCancelSupplyOrder);
            supplyOrderUpdate.setECancelSupplyOrderContext(eCancelSupplyOrderContext);
            supplyOrderUpdate.setUpdatedBy(request.getOperator());
            supplyOrderUpdate.setUpdatedDt(DateUtilX.dateToString(new Date(), hour_format));
            supplyOrderMapper.updateByPrimaryKeySelective(supplyOrderUpdate);
            stopWatch.stop();
//            stopWatch.start("supplyAttachmentMapper.updateByExampleSelective");
            //将所有有效的供货单附件设置为无效
//            Example supplyAttachmentExample = new Example(SupplyAttachmentPO.class);
//            Example.Criteria supplyAttachmentCriteria = supplyAttachmentExample.createCriteria();
//            SupplyAttachmentPO clearAllEffectiveSupplyAttachment = new SupplyAttachmentPO();
//            clearAllEffectiveSupplyAttachment.setActive(0);
//            supplyAttachmentCriteria.andEqualTo("supplyOrderId", supplyOrderPO.getId()).andEqualTo("active", 1);
//            supplyAttachmentMapper.updateByExampleSelective(clearAllEffectiveSupplyAttachment, supplyAttachmentExample);
//            stopWatch.stop();

            // 取消vcc卡
            if (Objects.equals(request.getConfirmationStatus(), ConfirmationStatusEnum.CANCELED.key) && supplyOrderPO.getVccSendStatus() != null && supplyOrderPO.getVccSendStatus().equals(1)) {
                try {
                    virtualCardService.cancelNotice(new CancelVCCRequestDTO(supplyOrderPO.getSupplyOrderCode(), "3"));
                } catch (Exception e) {
                    log.error("供货单取消成功取消vcc卡异常", e);
                }
            }

            stopWatch.start("记日志");
            // 记日志
            StringBuilder content = new StringBuilder();
            if (request.getManualFlag() != null && request.getManualFlag() == 1) {
                content.append("手动录供货单状态");
            } else {
                content.append("更新供货单确认状态");
            }
            content.append("，供货单：").append(supplyOrderPO.getSupplyOrderCode()).append("，").append("由")
                    .append(ConfirmationStatusEnum.getValueByKey(supplyOrderPO.getConfirmationStatus()))
                    .append("变更为").append(ConfirmationStatusEnum.getValueByKey(request.getConfirmationStatus()));
            if (StrUtilX.isNotEmpty(request.getRemark())) {
                content.append("，备注：").append(request.getRemark());
            }
            if (StrUtilX.isNotEmpty(request.getConfirmationCode())) {
                content.append("，确认号：").append(request.getConfirmationCode());
            }
            if (StrUtilX.isNotEmpty(request.getSupplierConfirmer())) {
                content.append("，确认人：").append(request.getSupplierConfirmer());
            }
            orderCommonService.saveOrderLog(
                    orderPO.getId(),
                    request.getOperator(),
                    request.getOrderOwnerName(),
                    supplyOrderPO.getSupplyOrderCode(),
                    content.toString(), 4
            );
            //通知自助结算结果
            //2024年1月31日 只同步预付的结算单结果
            if (orderPO.getPayMethod().equals(PayMethodEnum.PREPAY.key) && (Objects.equals(request.getConfirmationStatus(), ConfirmationStatusEnum.CANCELED.key) ||
                    Objects.equals(request.getConfirmationStatus(), ConfirmationStatusEnum.CONFIRMED.key))) {
                try {
                    SettleSupplyResultReq req = new SettleSupplyResultReq();
                    req.setSupplyResult(request.getConfirmationStatus());
                    req.setOrderId(Long.valueOf(supplyOrderPO.getOrderId()));
                    req.setHotelId(supplyOrderPO.getHotelId());
                    req.setSupplyOrderId(Long.valueOf(supplyOrderPO.getId()));
                    req.setSupplierCode(supplyOrderPO.getSupplierCode());
                    req.setConfirmNo(request.getConfirmationCode());
                    settleServerRemote.writeSettleTaskToRedis(req);
                } catch (Exception e) {
                    log.error("writeSettleTaskToRedis Exception 通知自助结算结果异常！ param: {}", JSONObject.toJSONString(request), e);
                }
            }
            stopWatch.stop();

            //如果是取消请求并且operater是system则认为是供应商推送
            if (null != request.getOperator() && request.getOperator().equals(Constant.SYSTEM) && request.getConfirmationStatus().equals(ConfirmationStatusEnum.CANCELED.key)) {
                stopWatch.start("供应商推送");
                Example orderRequestExample = new Example(OrderRequestPO.class);
                Example.Criteria orderRequestCriteria = orderRequestExample.createCriteria();
                orderRequestCriteria.andEqualTo("orderId", orderPO.getId());
                OrderRequestPO orderRequestPO = new OrderRequestPO();
                orderRequestPO.setHandleResult(1);
                orderRequestMapper.updateByExampleSelective(orderRequestPO, orderRequestExample);

//                //更新订单取消状态
//                OrderPO orderPOUpdate = new OrderPO();
//                orderPOUpdate.setId(orderPO.getId());
//                orderPOUpdate.setModificationStatus(0);
//                orderMapper.updateByPrimaryKeySelective(orderPOUpdate);

                orderCommonService.saveOrderLog(
                        orderPO.getId(),
                        Constant.SYSTEM,
                        request.getOrderOwnerName(),
                        supplyOrderPO.getSupplyOrderCode(),
                        "自动消费取消任务", 4
                );
                stopWatch.stop();
            }
            stopWatch.start("4. 更新订单表冗余字段：订单确认号、供货单状态、订单总金额");
            // 4. 更新订单表冗余字段：订单确认号、供货单状态、订单总金额
            SupplyOrderPO supplyOrderQuery = new SupplyOrderPO();
            supplyOrderQuery.setOrderId(supplyOrderPO.getOrderId());
            List<SupplyOrderPO> supplyOrderPOList = supplyOrderMapper.select(supplyOrderQuery);
            BigDecimal supplyOrderSum = BigDecimal.ZERO;
            BigDecimal rewardSum = BigDecimal.ZERO;
            BigDecimal rebateSum = BigDecimal.ZERO;
//            Set<String> supplyConfirmationCodeSet = new HashSet<>();
            boolean flag = false;
            Integer supplyConfirmationStatus = ConfirmationStatusEnum.CANCELED.key;
            for (SupplyOrderPO supplyOrder : supplyOrderPOList) {
//                //有确认号，并且确认状态为待确认的时候，才把确认号写到订单中
//                if (StrUtilX.isNotEmpty(supplyOrder.getConfirmationCode())
//                        && null != supplyOrder.getConfirmationStatus()
//                        && supplyOrder.getConfirmationStatus().equals(1)) {
//                    supplyConfirmationCodeSet.add(supplyOrder.getConfirmationCode());
//                }
                if (Objects.equals(supplyOrder.getConfirmationStatus(), ConfirmationStatusEnum.UNCONFIRM.key)) {
                    supplyConfirmationStatus = ConfirmationStatusEnum.UNCONFIRM.key;
                }
                if (!Objects.equals(supplyConfirmationStatus, ConfirmationStatusEnum.UNCONFIRM.key)
                        && Objects.equals(supplyOrder.getConfirmationStatus(), ConfirmationStatusEnum.CONFIRMED.key)) {
                    supplyConfirmationStatus = ConfirmationStatusEnum.CONFIRMED.key;
                    flag = true;
                }
                if (Objects.equals(supplyOrder.getConfirmationStatus(), ConfirmationStatusEnum.CANCELED.key)) {
//                    supplyOrder.setCommission(BigDecimal.ZERO);
                    supplyOrderMapper.updateByPrimaryKey(supplyOrder);
                }
                //BigDecimal commission = supplyOrder.getCommission() == null ? BigDecimal.ZERO : supplyOrder.getCommission() ;
                if (supplyOrder.getSupplierToAgentRate() != null) {
                    supplyOrderSum = supplyOrderSum.add(supplyOrder.getSupplyOrderAmt().multiply(supplyOrder.getSupplierToAgentRate()));
                } else {
                    supplyOrderSum = supplyOrderSum.add(supplyOrder.getSupplyOrderAmt());
                }
                if (supplyOrder.getRewardAmt() != null) {
                    rewardSum = rewardSum.add(supplyOrder.getRewardAmt());
                }
                if (supplyOrder.getRebateAmt() != null) {
                    rebateSum = rebateSum.add(supplyOrder.getRebateAmt());
                }
                if (!Objects.equals(ConfirmationStatusEnum.CANCELED.key, supplyOrder.getConfirmationStatus()) && null != supplyOrder.getRefundFee() && null != supplyOrder.getSupplierToAgentRate()) {
                    //if (ConfirmationStatusEnum.CANCELED.key != supplyOrder.getConfirmationStatus() && null != supplyOrder.getRefundFee() && null != supplyOrder.getRate()) {
                    //空指针使用错误
                    supplyOrderSum = supplyOrderSum.add(supplyOrder.getRefundFee().multiply(supplyOrder.getSupplierToAgentRate()));
                }
            }
            if (Objects.equals(request.getConfirmationStatus(), OrderStatusEnum.REFUSE.no)) {
                supplyConfirmationStatus = ConfirmationStatusEnum.DONE.key;
            }
            stopWatch.stop();

            // 多供货单时不处理订单
            if (supplyOrderPOList.size() < 2) {
                // 1-1判断订单是否自动确认
                // 1-2通过订单编号或者id直接查到该订单下面的所有供货单列表
                OrderPO orderUpdate = new OrderPO();
                if (flag) {
                    // 判断自动确认渠道是否开通
                    String companyCode = orderPO.getCompanyCode();
                    stopWatch.start("orderMapper.queryAutoConfirmChannel");
                    QueryAutoConfirmChannelDto autoConfirmChannelDto = orderMapper.queryAutoConfirmChannel(companyCode);
                    stopWatch.stop();
                    if (autoConfirmChannelDto != null && autoConfirmChannelDto.getAutomaticStatus().equals("1")) {
                        QueryConfirmOrderInfoDTO queryConfirmOrderInfoDTO = new QueryConfirmOrderInfoDTO();
                        queryConfirmOrderInfoDTO.setOrderId(orderPO.getId());
                        queryConfirmOrderInfoDTO.setConfirmType(orderPO.getModificationStatus());
                        stopWatch.start("orderQueryService.queryConfirmOrderInfo");
                        String orderInfo = orderQueryService.queryConfirmOrderInfo(queryConfirmOrderInfoDTO);
                        stopWatch.stop();
                        ConfirmOrderDTO confirmOrderDTO = new ConfirmOrderDTO();
                        confirmOrderDTO.setConfirmationContent(orderInfo);
                        confirmOrderDTO.setOrderId(orderPO.getId());
                        confirmOrderDTO.setOperator(request.getOperator());
                        stopWatch.start("orderService.confirmOrder");
                        orderService.confirmOrder(confirmOrderDTO);
                        stopWatch.stop();
                    }
                }
                stopWatch.start("orderMapper.updateByPrimaryKeySelective");
                orderUpdate.setId(supplyOrderPO.getOrderId());
                orderUpdate.setSupplyOrderConfirmationStatus(supplyConfirmationStatus.toString());
                orderUpdate.setProfit(orderPO.getOrderAmt().subtract(supplyOrderSum).add(rewardSum).subtract(rebateSum));
                orderMapper.updateByPrimaryKeySelective(orderUpdate);

//                OrderExtendPO queryPO = new OrderExtendPO();
//                queryPO.setOrderId(supplyOrderPO.getOrderId());
//                List<OrderExtendPO> orderExtendPOList = orderExtendMapper.select(queryPO);

//                if (CollUtilX.isNotEmpty(orderExtendPOList)) {
//                    OrderExtendPO updatePO = new OrderExtendPO();
//                    updatePO.setConfirmationCode(StrUtilX.listToString(supplyConfirmationCodeSet, ","));
//                    updatePO.setId(orderExtendPOList.get(0).getId());
//                    orderExtendMapper.updateByPrimaryKeySelective(updatePO);
//                }

                stopWatch.stop();
                stopWatch.start("是否自动取消订单");
                List<Integer> cancelOrderStatus = Arrays.asList(OrderStatusEnum.CANCELED.no, OrderStatusEnum.REFUSE.no);
                // 判断是否自动取消订单
                if (cancelOrderStatus.contains(supplyOrderUpdate.getConfirmationStatus())) {
                    OrderAutoCancelDTO autoCancelDTO = orderMapper.queryAutoCancel(orderPO.getCompanyCode());
                    if (null != autoCancelDTO && autoCancelDTO.getAutomaticStatus().equals("1")) {
                        CancelOrderDTO cancelOrderDTO = new CancelOrderDTO();
                        cancelOrderDTO.setOrderId(orderPO.getId());
                        // 判断供应商是否返回了退订费
                        if (request.getSupplyRefundFee() != null) {
                            // 判断币种是否一致
                            if (request.getRefundFeeCurrency() != null && !request.getRefundFeeCurrency().equals(orderPO.getSaleCurrency())) {
                                // 转换商家币种
                                BigDecimal orgRate = getRateToOrgCurrency(request.getRefundFeeCurrency());
                                // 商家转客户币种
                                OrgDTO orgDTO = CommonInitializer.getOrgInfo();
                                BigDecimal rateToAgentRate = RedisUtil.getRateToTargetCurrency(Integer.parseInt(orgDTO.getOrgCurrency()), CompanyDTO.COMPANY_CODE, orderPO.getSaleCurrency());
                                // 计算退订费并进位
                                BigDecimal refundFee = CommonTgUtils.setScale(request.getSupplyRefundFee().multiply(orgRate).multiply(rateToAgentRate), 2, 1);
                                cancelOrderDTO.setRefundFee(refundFee);
                            } else {
                                cancelOrderDTO.setRefundFee(request.getSupplyRefundFee());
                            }
                        } else {
                            cancelOrderDTO.setRefundFee(request.getRefundFee());
                        }
                        cancelOrderDTO.setCancelledReason(request.getRefusedReason());
                        cancelOrderDTO.setCancelledContent(request.getRemark());
                        cancelOrderDTO.setOperator(request.getOperator());
                        cancelOrderDTO.setSupplyResult(request.getConfirmationStatus());
                        orderService.cancelOrder(cancelOrderDTO, 1, false);
                    }
                }
                stopWatch.stop();

                try {
                    // 7. 退扣配额
                    //自签产品才走扣退配额逻辑
                    if (null == supplyOrderPO.getIsSpProduct()) {
                        stopWatch.start("orderMapper.queryBetweenDate");
                        SupplyProductPO firstSupplyProductQuery = new SupplyProductPO();
                        firstSupplyProductQuery.setSupplyOrderId(supplyOrderPO.getId());
                        SupplyProductPO supplyFirstProduct = supplyProductMapper.select(firstSupplyProductQuery).get(0);
                        Map<String, String> data = new HashMap<>();
                        data.put("begin", DateUtilX.dateToString(supplyOrderPO.getStartDate()));
                        data.put("end", DateUtilX.dateToString(supplyOrderPO.getEndDate()));
                        List<String> list = orderMapper.queryBetweenDate(data);
                        StringBuilder saleDate1 = new StringBuilder();
                        for (String s : list) {
                            saleDate1.append(s).append(",");
                        }
                        stopWatch.stop();
                        QuotaDTO quotaDTO = new QuotaDTO();
                        quotaDTO.setSaleDate(saleDate1.substring(0, saleDate1.length() - 1));
                        quotaDTO.setOrderId(orderPO.getId());
                        quotaDTO.setOrderCode(orderPO.getOrderCode());
                        quotaDTO.setSupplyOrderId(supplyOrderPO.getId());
                        quotaDTO.setSupplyOrderCode(supplyOrderPO.getSupplyOrderCode());
                        quotaDTO.setSupplierCode(supplyOrderPO.getSupplierCode());
                        String productId = null;
                        if (StrUtilX.isNotEmpty(supplyFirstProduct.getProductId())) {
                            if (!supplyFirstProduct.getProductId().contains("_")) {
                                quotaDTO.setProductId(Integer.valueOf(supplyFirstProduct.getProductId()));
                                productId = supplyFirstProduct.getProductId();
                            }
                        }
                        if (!Objects.equals(supplyOrderPO.getConfirmationStatus(), supplyOrderUpdate.getConfirmationStatus()) && null != productId) {
                            if (Objects.equals(supplyOrderUpdate.getConfirmationStatus(), ConfirmationStatusEnum.CANCELED.key)) {
                                // 调退配额接口
                                Map<String, Integer> map = new HashMap<>();
                                if (StrUtilX.isNotEmpty(productId)) {
                                    map.put("productId", Integer.valueOf(productId));
                                }
                                map.put("orderId", orderPO.getId());
                                map.put("type", 1);//查询以扣的配额
                                stopWatch.start("debitedQuotaRemote.queryDebitedQuota");
                                Response<List<DebitedQuotaDTO>> debitedQuota = debitedQuotaRemote.queryDebitedQuota(map);
                                stopWatch.stop();
                                List<DebitedQuotaDTO> debitedQuotaDTO = new ArrayList<>();
                                if (debitedQuota.getResult().equals(ResultCodeEnum.SUCCESS.code) && null != debitedQuota.getModel()) {
                                    debitedQuotaDTO = debitedQuota.getModel();
                                }
                                if (debitedQuotaDTO.size() > 0) {
                                    quotaDTO.setQuota(-debitedQuotaDTO.get(debitedQuotaDTO.size() - 1).getQuota());//以扣配额为负数，负负得正 ，加配额
                                } else {
                                    quotaDTO.setQuota(0);
                                }
                                stopWatch.start("quotaRemote.modifyQuota");
                                quotaRemote.modifyQuota(quotaDTO);
                                stopWatch.stop();
                            }
                            if (Objects.equals(supplyOrderPO.getConfirmationStatus(), ConfirmationStatusEnum.CANCELED.key)
                                    && Objects.equals(supplyOrderUpdate.getConfirmationStatus(), ConfirmationStatusEnum.CONFIRMED.key)) {
                                // 调扣配额接口
                                stopWatch.start("quotaRemote.modifyQuota");
                                quotaDTO.setQuota(-supplyOrderPO.getRoomQty());//减配额
                                quotaRemote.modifyQuota(quotaDTO);
                                stopWatch.stop();
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error("saveSupplyResult Exception 扣退配额异常！ param: {}, e=", JSONObject.toJSONString(request), e);
                }
            }

            stopWatch.start("更新供货单应付金额");
            //如果是取消，更新供货单应付金额
            if (null != request.getConfirmationStatus() && request.getConfirmationStatus().equals(ConfirmationStatusEnum.CANCELED.key)) {
                SupplyOrderFinancePO supplyOrderFinanceQuerys = new SupplyOrderFinancePO();
                supplyOrderFinanceQuerys.setSupplyOrderId(supplyOrderPO.getId());
                List<SupplyOrderFinancePO> supplyOrderFinancePOs = supplyOrderFinanceMapper.select(supplyOrderFinanceQuerys);
                for (SupplyOrderFinancePO supplyOrderFinancePO : supplyOrderFinancePOs) {
                    SupplyOrderFinancePO supplyOrderFinanceUpdates = new SupplyOrderFinancePO();
                    supplyOrderFinanceUpdates.setId(supplyOrderFinancePO.getId());
                    if (Objects.equals(supplyOrderFinancePO.getFinanceType(), StatementTypeEnum.REWARD_AMT.key)) {
                        //供应商奖励
                        //已支付，修改未收
                        // 供应商奖励不为空
                        BigDecimal unpaidAmt = BigDecimal.ZERO;
                        if (request.getSupplyReward() != null) {
                            unpaidAmt = request.getSupplyReward();
                        }
                        supplyOrderFinanceUpdates.setUnpaidAmt(unpaidAmt);
                        if (supplyOrderFinancePO.getPaidAmt() != null && supplyOrderFinancePO.getPaidAmt().compareTo(new BigDecimal(0)) > 0) {
                            supplyOrderFinanceUpdates.setUnpaidAmt(unpaidAmt.subtract(supplyOrderFinancePO.getPaidAmt()));
                        }
                    } else if (Objects.equals(supplyOrderFinancePO.getFinanceType(), StatementTypeEnum.REBATE_AMT.key)) {
                        //供应商返佣
                        //已支付，修改未付
                        supplyOrderFinanceUpdates.setUnpaidAmt(BigDecimal.ZERO);
                        if (supplyOrderFinancePO.getPaidAmt() != null && supplyOrderFinancePO.getPaidAmt().compareTo(new BigDecimal(0)) > 0) {
                            supplyOrderFinanceUpdates.setUnpaidAmt(BigDecimal.ZERO.subtract(supplyOrderFinancePO.getPaidAmt()));
                        }
                    } else {
                        //供应商金额
                        //以支付，退订费减以付金额等于未付金额
                        if (supplyOrderFinancePO.getPaidAmt() != null && supplyOrderFinancePO.getPaidAmt().compareTo(new BigDecimal(0)) > 0) {
                            if (request.getRefundFee() == null) {
                                request.setRefundFee(new BigDecimal(0));
                            }
                            supplyOrderFinanceUpdates.setUnpaidAmt(request.getRefundFee().subtract(supplyOrderFinancePO.getPaidAmt()));
                        } else {
                            supplyOrderFinanceUpdates.setUnpaidAmt(request.getRefundFee() == null ? new BigDecimal(0) : request.getRefundFee());
                        }
                    }
                    // 未收金额=0结算状态为已结算
                    if (BigDecimal.ZERO.compareTo(supplyOrderFinanceUpdates.getUnpaidAmt()) == 0) {
                        supplyOrderFinanceUpdates.setSettlementStatus(1);
                    } else {
                        supplyOrderFinanceUpdates.setSettlementStatus(0);
                    }
                    //更新对账状态
                    if (!Objects.equals(supplyOrderFinancePO.getCheckStatus(), CheckStatusEnum.CHECKING.key)) {
                        if (BigDecimal.ZERO.compareTo(supplyOrderFinanceUpdates.getUnpaidAmt()) == 0) {
                            //如果未收金额为0，则改为已对账
                            supplyOrderFinanceUpdates.setCheckStatus(CheckStatusEnum.CANNOT_CHECK.key);
                        } else {
                            //如果未收金额不为0，则改为可出账
                            supplyOrderFinanceUpdates.setCheckStatus(CheckStatusEnum.CAN_CHECK.key);
                        }
                    }
                    supplyOrderFinanceMapper.updateByPrimaryKeySelective(supplyOrderFinanceUpdates);
                }
            } else {
                SupplyOrderFinancePO supplyOrderFinanceQuerys = new SupplyOrderFinancePO();
                supplyOrderFinanceQuerys.setSupplyOrderId(supplyOrderPO.getId());
                List<SupplyOrderFinancePO> supplyOrderFinancePOs = supplyOrderFinanceMapper.select(supplyOrderFinanceQuerys);
                for (SupplyOrderFinancePO supplyOrderFinancePO : supplyOrderFinancePOs) {
                    SupplyOrderFinancePO supplyOrderFinanceUpdates = new SupplyOrderFinancePO();
                    supplyOrderFinanceUpdates.setId(supplyOrderFinancePO.getId());
                    supplyOrderFinanceUpdates.setUnpaidAmt(supplyOrderFinancePO.getUnpaidAmt());
                    if (Objects.equals(supplyOrderFinancePO.getFinanceType(), StatementTypeEnum.REWARD_AMT.key)) {
                        // 供应商奖励不为空
                        if (request.getSupplyReward() != null) {
                            supplyOrderFinanceUpdates.setUnpaidAmt(request.getSupplyReward());
                            if (request.getSupplyReward().compareTo(BigDecimal.ZERO) != 0) {
                                // 可出账
                                supplyOrderFinanceUpdates.setCheckStatus(1);
                            }
                        }
                    }
                    // 未收金额=0结算状态为已结算
                    if (BigDecimal.ZERO.compareTo(supplyOrderFinanceUpdates.getUnpaidAmt()) == 0) {
                        supplyOrderFinanceUpdates.setSettlementStatus(1);
                    } else {
                        supplyOrderFinanceUpdates.setSettlementStatus(0);
                    }
                    supplyOrderFinanceMapper.updateByPrimaryKeySelective(supplyOrderFinanceUpdates);
                }
            }
            stopWatch.stop();

            stopWatch.start("更新对账状态");
            if (null != supplyOrderPO.getSettlementType() && supplyOrderPO.getSettlementType() != SettlementTypeEnum.SINGLE.key
                    && Objects.equals(request.getConfirmationStatus(), ConfirmationStatusEnum.CONFIRMED.key)) {

                SupplyOrderFinancePO supplyOrderFinanceQuery = new SupplyOrderFinancePO();
                supplyOrderFinanceQuery.setSupplyOrderId(supplyOrderPO.getId());
                supplyOrderFinanceQuery.setFinanceType(StatementTypeEnum.SUPPLY_ORDER_AMT.key);
                SupplyOrderFinancePO supplyOrderFinancePO = supplyOrderFinanceMapper.selectOne(supplyOrderFinanceQuery);
                SupplyOrderFinancePO supplyOrderFinanceUpdate = new SupplyOrderFinancePO();
                supplyOrderFinanceUpdate.setId(supplyOrderFinancePO.getId());
                //非单结更新对账状态
                if (!Objects.equals(supplyOrderFinancePO.getCheckStatus(), CheckStatusEnum.CHECKING.key)) {
                    if (Objects.equals(supplyOrderPO.getConfirmationStatus(), ConfirmationStatusEnum.CANCELED.key)) {
                        supplyOrderFinanceUpdate.setUnpaidAmt(supplyOrderPO.getSupplyOrderAmt().subtract(supplyOrderPO.getCommission()).subtract(supplyOrderFinancePO.getPaidAmt()));
                    }
                    if (BigDecimal.ZERO.compareTo(supplyOrderFinancePO.getUnpaidAmt()) == 0) {
                        //如果未收金额为0，则改为已对账
                        supplyOrderFinanceUpdate.setCheckStatus(CheckStatusEnum.CANNOT_CHECK.key);
                    } else {
                        //如果未收金额不为0，则改为可出账
                        supplyOrderFinanceUpdate.setCheckStatus(CheckStatusEnum.CAN_CHECK.key);
                    }
                    supplyOrderFinanceMapper.updateByPrimaryKeySelective(supplyOrderFinanceUpdate);
                }
            }
            stopWatch.stop();
            if (Objects.equals(request.getConfirmationStatus(), OrderStatusEnum.REFUSE.no)) {
                CompletableFuture.runAsync(() -> orderService.pushOrderStatusDone(orderPO));
            }

            log.info("saveSupplyResult总耗时:{}", stopWatch.prettyPrint());
        } catch (Exception e) {
            log.error("saveSupplyResult Exception param :{} e=", JSONObject.toJSONString(request), e);
            // 3. 记日志
            orderCommonService.saveOrderLog(
                    orderPO.getId(),
                    request.getOperator(),
                    request.getOrderOwnerName(),
                    supplyOrderPO.getSupplyOrderCode(),
                    "接收" + (request.getConfirmationStatus() == 1 ? "确认单" : request.getConfirmationStatus() == 2 ? "取消单" : "其他单")
                            + "异常 （供货单：" + supplyOrderPO.getSupplyOrderCode() + ",供应商订单号：" + request.getSupplierOrderCode() + "）",
                    request.getConfirmationStatus() == 1 ? Integer.valueOf(0) : request.getConfirmationStatus() == 2 ? 3 : null
            );
            throw e;
        }

        try {
            //加入到统计报表队列中
            stringRedisTemplate.convertAndSend(RedisKey.REPORT_STATISTICS, orderPO.getOrderCode());
        } catch (Exception e) {
            log.error("加入到统计报表队列异常,error:", e);
        }

        return Response.success();
    }

    /**
     * 比较供货单结果是否一致
     */
    @Override
    public boolean compareSupplyOrderStatus(SaveSupplyResultDTO request) {
        Example example = new Example(SupplyOrderPO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("id", request.getSupplyOrderId());
        SupplyOrderPO supplyOrderPO = supplyOrderMapper.selectOneByExample(example);
        // 状态一致不处理
        if (Objects.equals(supplyOrderPO.getConfirmationStatus(), request.getConfirmationStatus())) {
            log.info("【邮件发单】保存供货单结果，状态一致不处理，thisConfirmationStatus={}, request={}", supplyOrderPO.getConfirmationStatus(), JSONUtil.toJsonStr(request));
            return false;
        }
        return true;
    }

    @Override
    public void updateSupplyOrderNormal(SendToSupplierDTO request) {
        if (request.getSupplyOrderId() != null) {
            SupplyOrderPO supplyOrderPO = new SupplyOrderPO();
            supplyOrderPO.setId(request.getSupplyOrderId());
            supplyOrderPO.setCancelExceptionFlag(0);
            supplyOrderMapper.updateByPrimaryKeySelective(supplyOrderPO);//只需要把异常标识改掉
        }
    }

    @Override
    public void cancelOrderFailResult(Integer orderId, String operater) {
        if (orderId != null) {
            OrderPO orderPO = orderMapper.selectByPrimaryKey(orderId);
            if (orderPO.getModificationStatus() != null && (orderPO.getModificationStatus() == 1 || orderPO.getModificationStatus() == 6)) {
                OrderPO updateOrderPO = new OrderPO();
                updateOrderPO.setId(orderPO.getId());
                updateOrderPO.setModificationStatus(0);
                orderMapper.updateByPrimaryKeySelective(updateOrderPO);
            }
            //所有状态正常
            Example supplyOrderExample = new Example(SupplyOrderPO.class);
            Example.Criteria supplyOrderCriteria = supplyOrderExample.createCriteria();
            supplyOrderCriteria.andEqualTo("orderId", orderPO.getId());
            SupplyOrderPO supplyOrderPO = new SupplyOrderPO();
            supplyOrderPO.setCancelExceptionFlag(0);
            supplyOrderMapper.updateByExampleSelective(supplyOrderPO, supplyOrderExample);
            // 自动消费取消任务
            Example orderRequestExample = new Example(OrderRequestPO.class);
            Example.Criteria orderRequestCriteria = orderRequestExample.createCriteria();
            orderRequestCriteria.andEqualTo("orderId", orderPO.getId());
            OrderRequestPO orderRequestPO = new OrderRequestPO();
            orderRequestPO.setHandleResult(2);
            //推送给下游结果
            B2BOrderStatusPushRequest b2BOrderStatusPushRequest = new B2BOrderStatusPushRequest();
            b2BOrderStatusPushRequest.setCoOrderCode(orderPO.getChannelOrderCode());
            b2BOrderStatusPushRequest.setFcOrderCode(orderPO.getOrderCode());
            b2BOrderStatusPushRequest.setOrderStatus(7);
            b2BOrderStatusPushRequest.setMessage("拒绝取消");
            //b2BOrderStatusPushRequest.setHotelConfirmNo(null != orderExtendPO ? orderExtendPO.getConfirmationCode() : null);
            b2BOrderStatusPushRequest.setAgentCode(orderPO.getAgentCode());
            b2BOrderStatusPushRequest.setPartnerCode(orderPO.getPartnerCode());
            orderCommonService.saveOrderLog(
                    orderId,
                    operater,
                    operater,
                    orderPO.getOrderCode(),
                    "操作拒绝取消同步下游取消失败",
                    null
            );
            pushAgentCancelFail(b2BOrderStatusPushRequest);
        }
    }

    @Override
    public void modifySupplyOrderSettlementType(ModifySupplyOrderSettlementTypeDTO request) {
        SupplyOrderPO supplyOrderPO = supplyOrderMapper.selectByPrimaryKey(request.getSupplyOrderId());
        OrderPO orderPO = orderMapper.selectByPrimaryKey(supplyOrderPO.getOrderId());

        // 1. 校验
        if (Objects.equals(supplyOrderPO.getSettlementType(), request.getSettlementType())) {
            return;
        }
        if (request.getSettlementType() != SettlementTypeEnum.SINGLE.key) {
            //单结订单不能改为非单结，非单结只能修改为单结
            log.error(SettlementTypeEnum.getValueByKey(supplyOrderPO.getSettlementType()) + "不能改为" + SettlementTypeEnum.getValueByKey(request.getSettlementType()));
            throw new SysException(ErrorCodeEnum.FAIL);
        }
        if (Objects.equals(supplyOrderPO.getConfirmationStatus(), ConfirmationStatusEnum.CONFIRMED.key)) {
            throw new SysException(ErrorCodeEnum.SUPPLYORDER_CONFIRMED_NOT_UPDATE_SETTLEMENTTYPE);
        }

        // 2. 更新订单结算方式
        SupplyOrderPO supplyOrderUpdate = new SupplyOrderPO();
        supplyOrderUpdate.setId(request.getSupplyOrderId());
        supplyOrderUpdate.setSettlementType(request.getSettlementType());
        supplyOrderUpdate.setUpdatedBy(request.getOperator());
        supplyOrderUpdate.setUpdatedDt(DateUtilX.dateToString(new Date(), hour_format));
        supplyOrderMapper.updateByPrimaryKeySelective(supplyOrderUpdate);

        // 3. 记日志
        orderCommonService.saveOrderLog(
                orderPO.getId(),
                request.getOperator(),
                request.getOrderOwnerName(),
                supplyOrderPO.getSupplyOrderCode(),
                "修改供货单结算方式：由(" + SettlementTypeEnum.getValueByKey(orderPO.getSettlementType())
                        + ")改为(" + SettlementTypeEnum.getValueByKey(request.getSettlementType()) + ")"
        );
    }

    @Override
    public void saveSupplyAttachment(SaveSupplyAttachmentDTO request) {
        if (null == request.getSupplyOrderId() || StrUtilX.isEmpty(request.getUrl())) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }

        SupplyOrderPO supplyOrderPO = supplyOrderMapper.selectByPrimaryKey(request.getSupplyOrderId());

//        SupplyAttachmentPO supplyAttachmentInsert = new SupplyAttachmentPO();
//        BeanUtils.copyProperties(request, supplyAttachmentInsert);
        SupplyAttachmentPO supplyAttachmentInsert = ProductSalePriceConvert.INSTANCE.supplyAttachmentInsertConvert(request);
        supplyAttachmentInsert.setCreatedBy(request.getOperator());
        supplyAttachmentInsert.setActive(1);
        supplyAttachmentInsert.setCreatedDt(DateUtilX.dateToString(new Date(), hour_format));
        supplyAttachmentMapper.insert(supplyAttachmentInsert);

        //记日志
        orderCommonService.saveOrderLog(
                supplyOrderPO.getOrderId(),
                request.getOperator(),
                request.getOrderOwnerName(),
                supplyOrderPO.getSupplyOrderCode(),
                "上传供货单附件（" + request.getName() + "）成功"
        );
    }

    @Override
    public void deleteSupplyAttachment(SupplyAttachmentIdDTO request) {
        SupplyAttachmentPO supplyAttachmentPO = supplyAttachmentMapper.selectByPrimaryKey(request.getSupplyAttachmentId());
        SupplyOrderPO supplyOrderPO = supplyOrderMapper.selectByPrimaryKey(supplyAttachmentPO.getSupplyOrderId());
        supplyAttachmentMapper.deleteByPrimaryKey(request.getSupplyAttachmentId());

        // 记日志
        orderCommonService.saveOrderLog(
                supplyOrderPO.getOrderId(),
                request.getOperator(),
                request.getOrderOwnerName(),
                supplyOrderPO.getSupplyOrderCode(),
                "删除供货单附件（" + supplyAttachmentPO.getName() + "）成功"
        );
    }

    @Override
    @Transactional
    public void modifySupplierOrderCode(ModifySupplierOrderCodeDTO request) {
        SupplyOrderPO supplyOrderPO = supplyOrderMapper.selectByPrimaryKey(request.getSupplyOrderId());
        OrderPO orderPO = orderMapper.selectByPrimaryKey(supplyOrderPO.getOrderId());

        SupplyOrderPO supplyOrderUpdate = new SupplyOrderPO();
        supplyOrderUpdate.setId(request.getSupplyOrderId());
        supplyOrderUpdate.setSupplierOrderCode(request.getSupplierOrderCode());
        supplyOrderUpdate.setUpdatedBy(request.getOperator());
        supplyOrderUpdate.setUpdatedDt(DateUtilX.dateToString(new Date(), hour_format));
        supplyOrderMapper.updateByPrimaryKeySelective(supplyOrderUpdate);

        // 记日志
        String content = "更新供应商订单号为：" +
                request.getSupplierOrderCode();
        orderCommonService.saveOrderLog(
                supplyOrderPO.getOrderId(),
                request.getOperator(),
                request.getOrderOwnerName(),
                supplyOrderPO.getSupplyOrderCode(),
                content, 4
        );

        try {
            //加入到统计报表队列中
            stringRedisTemplate.convertAndSend(RedisKey.REPORT_STATISTICS, orderPO.getOrderCode());
        } catch (Exception e) {
            log.error("加入到统计报表队列异常,error:", e);
        }
    }

    @Override
    @Transactional
    public Response<PrintSupplyOrderDTO> batchPrintSupplierOrder(Map<String, String> request) {
        String[] supplyOrderCodes = request.get("supplyOrderCodes").split(",");
        PrintSupplyOrderDTO printSupplyOrderDTO = new PrintSupplyOrderDTO();
        List<SupplyOrderListDTO> supplyOrderListDTOS = new ArrayList<>();
        SupplyOrderPO supplyOrderPO = new SupplyOrderPO();
        supplyOrderPO.setSupplyOrderCode(supplyOrderCodes[0]);

        SupplyOrderPO supplyOrder = new SupplyOrderPO();
        supplyOrder = supplyOrderMapper.selectOne(supplyOrderPO);

        // 查询酒店基本信息
//        BaseinfoHotelReq baseinfoHotelReq = new BaseinfoHotelReq();
//        baseinfoHotelReq.setId(supplyOrder.getHotelId());
        // 查询酒店详情需要修改
//        Response hotelResponse = hotelRemote.queryHotelInfo(baseinfoHotelReq);
//        if (hotelResponse.getResult().equals(ResultCodeEnum.SUCCESS.code) && null != hotelResponse.getModel()) {
//            BaseinfoHotelResp baseinfoHotelResp = JSON.parseObject(JSON.toJSONString(hotelResponse.getModel()), BaseinfoHotelResp.class);
//            printSupplyOrderDTO.setHotelName(baseinfoHotelResp.getHotelName());
//            printSupplyOrderDTO.setHotelAddress(baseinfoHotelResp.getHotelAddress());
//            printSupplyOrderDTO.setHotelTel(baseinfoHotelResp.getHotelTel());
//        }

        // 查询特殊要求
        OrderExtendPO orderQueryPO = new OrderExtendPO();
        orderQueryPO.setOrderId(supplyOrder.getOrderId());
        List<OrderExtendPO> orderExtendPOList = orderExtendMapper.select(orderQueryPO);
        if (CollUtilX.isNotEmpty(orderExtendPOList)
                && orderExtendPOList.get(0).getIsShowOnSupplyOrder().equals(1)) {
            printSupplyOrderDTO.setSpecialRequest(orderExtendPOList.get(0).getSpecialRequest());
        }

        String SupplierCode = "";
        for (String supplyOrderCode : supplyOrderCodes) {
            SupplyOrderListDTO supplyOrderListDTO = new SupplyOrderListDTO();
            supplyOrderListDTO.setTotalAmt(new BigDecimal(0));
            SupplyOrderPO supplyOrderPOS = new SupplyOrderPO();
            supplyOrderPO.setSupplyOrderCode(supplyOrderCode);
            supplyOrderPOS = supplyOrderMapper.selectOne(supplyOrderPO);
            supplyOrderListDTO.setSupplyOrderCode(supplyOrderPOS.getSupplyOrderCode());
            supplyOrderListDTO.setCurrency(supplyOrderPOS.getBaseCurrency());
            if (!"".equals(SupplierCode) && !SupplierCode.equals(supplyOrderPOS.getSupplierCode())) {
                throw new SysException(ErrorCodeEnum.SAME_SUPPLIER_SEND_ORDER_ERROR);
            }
            SupplierCode = supplyOrderPOS.getSupplierCode();

            Set<String> guestSet = new HashSet<>();
            Map<Integer, SupplyProductPO> supplyProductPOMap = new HashMap<>();
            SupplyProductPO supplyProductQuery = new SupplyProductPO();
            supplyProductQuery.setSupplyOrderId(supplyOrderPOS.getId());
            List<SupplyProductPO> supplyProductPOList = supplyProductMapper.select(supplyProductQuery);
            for (SupplyProductPO supplyProductPO : supplyProductPOList) {
                supplyProductPOMap.put(supplyProductPO.getId(), supplyProductPO);
                //过滤重复的入住人
                if (StrUtilX.isNotEmpty(supplyProductPO.getGuest())) {
                    if (supplyProductPO.getGuest().contains("、")) {
                        guestSet.addAll(Arrays.asList(supplyProductPO.getGuest().split("、")));
                    } else {
                        guestSet.add(supplyProductPO.getGuest());
                    }
                }
            }
            printSupplyOrderDTO.setGuest(StrUtilX.listToString(guestSet, ","));

            Example supplyProductPriceExample = new Example(SupplyProductPricePO.class);
            supplyProductPriceExample.setOrderByClause("supply_product_id,sale_date");
            Example.Criteria supplyProductPriceCriteria = supplyProductPriceExample.createCriteria();
            supplyProductPriceCriteria.andEqualTo("supplyOrderId", supplyOrderPOS.getId());
            List<SupplyProductPricePO> supplyProductPricePOList = supplyProductPriceMapper.selectByExample(supplyProductPriceExample);
            BigDecimal nightQty;
            BigDecimal lastBasePrice = BigDecimal.ZERO;
            List<SupplyProductPreviewDTO> list = new ArrayList<>();
            for (SupplyProductPricePO supplyProductPricePO : supplyProductPricePOList) {
                if (supplyProductPricePO.getBasePrice().compareTo(lastBasePrice) != 0) {
                    nightQty = BigDecimal.ONE;
                    SupplyProductPO supplyProductPO = supplyProductPOMap.get(supplyProductPricePO.getSupplyProductId());
                    SupplyProductPreviewDTO supplyProductPreviewDTO = new SupplyProductPreviewDTO();
                    supplyProductPreviewDTO.setRoomName(supplyProductPO.getRoomName());
                    supplyProductPreviewDTO.setProductName(supplyProductPO.getProductName());
                    supplyProductPreviewDTO.setStartDate(DateUtilX.dateToString(supplyProductPricePO.getSaleDate()));
                    supplyProductPreviewDTO.setEndDate(DateUtilX.dateToString(supplyProductPricePO.getSaleDate()));
                    supplyProductPreviewDTO.setNightQty(nightQty);
                    supplyProductPreviewDTO.setRoomQty(supplyProductPO.getRoomQty());
                    supplyProductPreviewDTO.setBasePrice(supplyProductPricePO.getBasePrice());
                    supplyProductPreviewDTO.setTotalAmt(supplyProductPricePO.getBasePrice()
                            .multiply(BigDecimal.valueOf(supplyProductPO.getRoomQty()))
                            .multiply(nightQty));
                    supplyOrderListDTO.setTotalAmt(supplyOrderListDTO.getTotalAmt().add(supplyProductPreviewDTO.getTotalAmt()));
                    list.add(supplyProductPreviewDTO);
                }
            }
            supplyOrderListDTO.setProductList(list);
            supplyOrderListDTOS.add(supplyOrderListDTO);
        }
        printSupplyOrderDTO.setSupplyOrderList(supplyOrderListDTOS);
        return Response.success(printSupplyOrderDTO);
    }

    @Override
    @Transactional
    public Response<Object> urgeOrder(UrgeOrderRequestDTO urgeOrderRequestDTO) {
        Response<Object> response = supplyOrderRemote.urgeOrder(urgeOrderRequestDTO);
        SupplyOrderPO supplyOrderPO = supplyOrderMapper.selectByPrimaryKey(urgeOrderRequestDTO.getSupplierOrerId());
        String content = response.getModel() +
                supplyOrderPO.getSupplierOrderCode();
        //记日志
        orderCommonService.saveOrderLog(
                supplyOrderPO.getOrderId(),
                urgeOrderRequestDTO.getOperator(),
                urgeOrderRequestDTO.getOrderOwnerName(),
                supplyOrderPO.getSupplyOrderCode(),
                content
        );
        return response;
    }

    /**
     * 修改供货单佣金
     */
    @Override
    @Transactional
    public void modifySupplyCommission(ModifySupplyCommissionDTO request) {
        if (request.getNewCommission() == null) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_COMMISSION);
        }
        SupplyOrderPO supplyOrderPO = supplyOrderMapper.selectByPrimaryKey(request.getSupplyOrderId());
        BigDecimal commission = supplyOrderPO.getCommission() == null ? BigDecimal.ZERO : supplyOrderPO.getCommission();
        Example example = new Example(SupplyProductPO.class);
        example.createCriteria().andEqualTo("supplyOrderId", request.getSupplyOrderId());
        SupplyProductPO supplyProductPO = supplyProductMapper.selectOneByExample(example);
        OrderPO orderPO = new OrderPO();
        orderPO.setId(supplyOrderPO.getOrderId());
        OrderPO order = orderMapper.selectByPrimaryKey(orderPO);
        BigDecimal supplyOrdercommission = supplyOrderPO.getCommission() == null ? BigDecimal.ZERO : supplyOrderPO.getCommission();
        BigDecimal supplyProductcommission = supplyProductPO.getCommission() == null ? BigDecimal.ZERO : supplyProductPO.getCommission();
        if (!supplyOrdercommission.equals(request.getNewCommission()) && !supplyProductcommission.equals(request.getNewCommission())) {
            supplyOrderPO.setCommission(request.getNewCommission());
            //供货单金额=原供货单金额-(新佣金-旧佣金)
            supplyOrderPO.setSupplyOrderAmt(supplyOrderPO.getSupplyOrderAmt().subtract(request.getNewCommission().subtract(commission)));
            supplyOrderMapper.updateByPrimaryKeySelective(supplyOrderPO);
            supplyProductPO.setCommission(request.getNewCommission());
            supplyProductMapper.updateByPrimaryKeySelective(supplyProductPO);
            order.setProfit(order.getProfit().add(request.getNewCommission().subtract(commission)));
            orderMapper.updateByPrimaryKeySelective(order);

            //更新订单未结算金额和结算状态
            SupplyOrderFinancePO supplyOrderFinanceQuery = new SupplyOrderFinancePO();
            supplyOrderFinanceQuery.setSupplyOrderId(supplyOrderPO.getId());
            supplyOrderFinanceQuery.setFinanceType(StatementTypeEnum.SUPPLY_ORDER_AMT.key);
            SupplyOrderFinancePO supplyOrderFinancePO = supplyOrderFinanceMapper.selectOne(supplyOrderFinanceQuery);
            SupplyOrderFinancePO supplyOrderFinanceUpdate = new SupplyOrderFinancePO();
            supplyOrderFinanceUpdate.setId(supplyOrderFinancePO.getId());
            supplyOrderFinanceUpdate.setUnpaidAmt(supplyOrderPO.getSupplyOrderAmt().subtract(null == supplyOrderFinancePO.getPaidAmt() ? BigDecimal.ZERO : supplyOrderFinancePO.getPaidAmt()));
            if (BigDecimal.ZERO.compareTo(supplyOrderFinanceUpdate.getUnpaidAmt()) == 0) {
                supplyOrderFinanceUpdate.setSettlementStatus(1);
            } else {
                supplyOrderFinanceUpdate.setSettlementStatus(0);
            }
            //出账状态
            if (!Objects.equals(supplyOrderFinancePO.getCheckStatus(), CheckStatusEnum.CHECKING.key)) {
                if (BigDecimal.ZERO.compareTo(supplyOrderFinanceUpdate.getUnpaidAmt()) == 0) {
                    //如果未收金额为0，则改为不可出账
                    supplyOrderFinanceUpdate.setCheckStatus(CheckStatusEnum.CANNOT_CHECK.key);
                } else {
                    //如果未收金额不为0，则改为可出账
                    supplyOrderFinanceUpdate.setCheckStatus(CheckStatusEnum.CAN_CHECK.key);
                }
            }
            supplyOrderFinanceMapper.updateByPrimaryKeySelective(supplyOrderFinanceUpdate);

            String content = "供货单 (" + supplyOrderPO.getSupplyOrderCode() + ")" +
                    "佣金变更,佣金由" + commission +
                    "变更为" + request.getNewCommission();
            //记日志
            orderCommonService.saveOrderLog(
                    supplyOrderPO.getOrderId(),
                    request.getOperator(),
                    request.getOrderOwnerName(),
                    supplyOrderPO.getSupplyOrderCode(),
                    content
            );

            try {
                //加入到统计报表队列中
                stringRedisTemplate.convertAndSend(RedisKey.REPORT_STATISTICS, order.getOrderCode());
            } catch (Exception e) {
                log.error("加入到统计报表队列异常,error:", e);
            }

        }
    }

//    /**
//     * 组装供应商订单参数
//     */
//    private CreateSupplyOrderRequest assemblySupplierOrder(SendToSupplierDTO request, SupplyOrderProductDTO supplyProductPO, SupplyOrderPO supplyOrderPO, OrderPO orderPO) {
//        CreateSupplyOrderRequest createSupplyOrderRequest = new CreateSupplyOrderRequest();
//        createSupplyOrderRequest.setSupplyCode(supplyProductPO.getSupplyCode());
//        createSupplyOrderRequest.setHotelId(String.valueOf(supplyProductPO.getHotelId()));
//        createSupplyOrderRequest.setRoomId(String.valueOf(supplyProductPO.getRoomId()));
//        createSupplyOrderRequest.setSpProductId(supplyProductPO.getSpProductId());
//        createSupplyOrderRequest.setPricePlanName(supplyProductPO.getProductName());
//        createSupplyOrderRequest.setBedType(supplyProductPO.getBedType());
//        createSupplyOrderRequest.setRoomNum(supplyOrderPO.getRoomQty());
//        createSupplyOrderRequest.setRemark(request.getRemark());
//        if (StrUtilX.isNotEmpty(request.getSpecialRequest())) {
//            createSupplyOrderRequest.setRemark(request.getRemark() + "," + request.getSpecialRequest());
//        }
//        Date startDate = supplyOrderPO.getStartDate();
//        Date endDate = supplyOrderPO.getEndDate();
//        createSupplyOrderRequest.setCheckInDate(startDate);
//        createSupplyOrderRequest.setCheckOutDate(endDate);
//        createSupplyOrderRequest.setOrderSum(supplyOrderPO.getBasePrice());
//        createSupplyOrderRequest.setSupplyRequestUser(request.getOperator());
//        createSupplyOrderRequest.setSupplyOrderCode(supplyOrderPO.getSupplyOrderCode());
//        createSupplyOrderRequest.setIsSpProduct(supplyOrderPO.getIsSpProduct());
//        createSupplyOrderRequest.setSpHotelId(String.valueOf(supplyProductPO.getHotelId()));
//        createSupplyOrderRequest.setSpRoomId(String.valueOf(supplyProductPO.getRoomId()));
//        // 设置支付方式
//        createSupplyOrderRequest.setPayMethod(orderPO.getPayMethod());
//        createSupplyOrderRequest.setUserAccount(orderPO.getUserAccount());
//
//        //组装不落地供应商的酒店，房型，价格计划id
//        if (supplyOrderPO.getIsSpProduct() != null && supplyOrderPO.getIsSpProduct() == 1) {
//            if (StrUtilX.isNotEmpty(supplyProductPO.getSpProductId())) {
//                createSupplyOrderRequest.setSpProductId(supplyProductPO.getSpProductId());
//            }
//        }
//
//        //查询组装入住人
//        GuestPO guestQuery = new GuestPO();
//        guestQuery.setOrderId(supplyOrderPO.getOrderId());
//        List<GuestPO> guestPOList = guestMapper.select(guestQuery);
//        List<com.tiangong.supply.direct.remote.dto.SupplyGuestDTO> supplyGuests = new ArrayList<>();
//        Map<Integer, RoomGuestNumber> roomGuestMap = new HashMap<>();
//        List<String> roomNumbers = StrUtilX.stringToList(supplyOrderPO.getRoomNumbers(), ",");
//        for (GuestPO guestPO : guestPOList) {
//            // 一个多供 判断是否属于当前供货单房间
//            if (CollUtilX.isNotEmpty(roomNumbers)) {
//                if (guestPO.getRoomNumber() != null && !roomNumbers.contains(String.valueOf(guestPO.getRoomNumber()))) {
//                    continue;
//                }
//            }
//            com.tiangong.supply.direct.remote.dto.SupplyGuestDTO supplyGuestDTO = new com.tiangong.supply.direct.remote.dto.SupplyGuestDTO();
//            supplyGuestDTO.setGuestName(guestPO.getName());
//            supplyGuestDTO.setPhone(orderPO.getContactPhone());//客人联系电话
//            supplyGuestDTO.setCountryCode(orderPO.getContactCountryCode());
//            supplyGuestDTO.setEmail(orderPO.getContactEmail());//客人邮箱
//            supplyGuestDTO.setEarnPoints(guestPO.getEarnPoints());
//            supplyGuestDTO.setFirstName(guestPO.getFirstName());
//            supplyGuestDTO.setLastName(guestPO.getLastName());
//            supplyGuestDTO.setRoomIndex(guestPO.getRoomNumber());
//            supplyGuests.add(supplyGuestDTO);
//
//            if (null == roomGuestMap.get(guestPO.getRoomNumber())) {
//                RoomGuestNumber roomGuestNumber = new RoomGuestNumber();
//                roomGuestNumber.setAdultNum(guestPO.getAdultQty());
//                roomGuestNumber.setRoomIndex(guestPO.getRoomNumber());
//                if (StrUtilX.isNotEmpty(guestPO.getChildrenAge())) {
//                    List<ChildrenInfo> childrenInfos = new ArrayList<>();
//                    String[] ages = guestPO.getChildrenAge().split(",");
//                    for (String age : ages) {
//                        ChildrenInfo childrenInfo = new ChildrenInfo();
//                        childrenInfo.setChildrenAge(Integer.valueOf(age));
//                        childrenInfos.add(childrenInfo);
//                    }
//                    roomGuestNumber.setChildrenInfos(childrenInfos);
//                }
//                roomGuestMap.put(guestPO.getRoomNumber(), roomGuestNumber);
//            }
//        }
//        createSupplyOrderRequest.setSupplyGuests(supplyGuests);
//        createSupplyOrderRequest.setRoomGuestNumbers(new ArrayList<>(roomGuestMap.values()));
//
//        //查询产品明细
//        SupplyProductPricePO supplyProductPriceQuery = new SupplyProductPricePO();
//        supplyProductPriceQuery.setSupplyOrderId(supplyOrderPO.getId());
//        List<SupplyProductPricePO> supplyProductPricePOList = supplyProductPriceMapper.select(supplyProductPriceQuery);
//
//        //费用按房间转Map
//        Map<Integer, List<SupplyProductPricePO>> supplyFeeMap = supplyProductPricePOList.stream()
//                .collect(Collectors.groupingBy(SupplyProductPricePO::getRoomNumber));
//
//        Map<String, SupplyDetailsDTO> supplyPriceMap = new HashMap<>();
//
//        Date checkIn = orderPO.getStartDate();
//        String checkInDate = DateUtilX.dateToString(checkIn);
//
//        for (Integer roomNumber : supplyFeeMap.keySet()) {
//
//            for (SupplyProductPricePO supplyProductPricePO : supplyFeeMap.get(roomNumber)) {
//                Date saleD = supplyProductPricePO.getSaleDate();
//                String saleDate = DateUtilX.dateToString(saleD);
//                if (null != saleDate) {
//                    if (null == supplyPriceMap.get(saleDate)) {
//                        SupplyDetailsDTO supplyDetailsDTO = new SupplyDetailsDTO();
//                        supplyDetailsDTO.setRoomDate(DateUtilX.stringToDate(saleDate));
//                        supplyDetailsDTO.setCurrency(supplyProductPricePO.getCurrency());
//                        supplyDetailsDTO.setRoomPrice(CommonTgUtils.formatBigDecimal(supplyProductPricePO.getRoomPrice())
//                                .add(CommonTgUtils.formatBigDecimal(supplyProductPricePO.getTax()))
//                                .add(CommonTgUtils.formatBigDecimal(supplyProductPricePO.getSalesTax()))
//                                .add(CommonTgUtils.formatBigDecimal(supplyProductPricePO.getExtraTaxFee())));
//                        supplyDetailsDTO.setBreakFastType(supplyProductPricePO.getBreakfastType());
//                        supplyDetailsDTO.setBreakFastNum(supplyProductPricePO.getBreakfastQty());
//                        supplyPriceMap.put(saleDate, supplyDetailsDTO);
//                    } else {
//                        supplyPriceMap.get(saleDate).setRoomPrice(supplyPriceMap.get(saleDate).getRoomPrice()
//                                .add(CommonTgUtils.formatBigDecimal(supplyProductPricePO.getRoomPrice()))
//                                .add(CommonTgUtils.formatBigDecimal(supplyProductPricePO.getTax()))
//                                .add(CommonTgUtils.formatBigDecimal(supplyProductPricePO.getSalesTax()))
//                                .add(CommonTgUtils.formatBigDecimal(supplyProductPricePO.getExtraTaxFee())));
//                    }
//                } else {
//                    if (null == supplyPriceMap.get(checkInDate)) {
//                        SupplyDetailsDTO supplyDetailsDTO = new SupplyDetailsDTO();
//                        supplyDetailsDTO.setRoomDate(DateUtilX.stringToDate(checkInDate));
//                        supplyDetailsDTO.setCurrency(supplyProductPricePO.getCurrency());
//                        supplyDetailsDTO.setRoomPrice(CommonTgUtils.formatBigDecimal(supplyProductPricePO.getRoomPrice())
//                                .add(CommonTgUtils.formatBigDecimal(supplyProductPricePO.getTax()))
//                                .add(CommonTgUtils.formatBigDecimal(supplyProductPricePO.getSalesTax()))
//                                .add(CommonTgUtils.formatBigDecimal(supplyProductPricePO.getExtraTaxFee())));
//                        supplyDetailsDTO.setBreakFastType(supplyProductPricePO.getBreakfastType());
//                        supplyDetailsDTO.setBreakFastNum(supplyProductPricePO.getBreakfastQty());
//                        supplyPriceMap.put(checkInDate, supplyDetailsDTO);
//                    } else {
//                        supplyPriceMap.get(checkInDate).setRoomPrice(supplyPriceMap.get(checkInDate).getRoomPrice()
//                                .add(CommonTgUtils.formatBigDecimal(supplyProductPricePO.getRoomPrice()))
//                                .add(CommonTgUtils.formatBigDecimal(supplyProductPricePO.getTax()))
//                                .add(CommonTgUtils.formatBigDecimal(supplyProductPricePO.getSalesTax()))
//                                .add(CommonTgUtils.formatBigDecimal(supplyProductPricePO.getExtraTaxFee())));
//                    }
//                }
//            }
//        }
//
//        List<SupplyDetailsDTO> supplyDetails = new ArrayList<>(supplyPriceMap.values());
//        createSupplyOrderRequest.setSupplyDetails(supplyDetails);
//
//        createSupplyOrderRequest.setNumberOfAdults(orderPO.getAdultQty());
//        createSupplyOrderRequest.setChildrenAges(orderPO.getChildrenAge());
//        createSupplyOrderRequest.setNumberOfChildren(orderPO.getChildrenQty());
////        createSupplyOrderRequest.setPayMethod(orderPO.getPayMethod());
//        createSupplyOrderRequest.setGuaranteeFlag(orderPO.getGuaranteeFlag());
//        createSupplyOrderRequest.setIsVipOrder(orderPO.getIsVipOrder());
//        createSupplyOrderRequest.setTravelType(orderPO.getTravelType());
//        createSupplyOrderRequest.setHourlyRoom(orderPO.getHourly());
//        if (createSupplyOrderRequest.getHourlyRoom() == 0) {//钟点房不需要这个字段
//            //0906 到店时间只要HH：ss
//            if (StrUtilX.isNotEmpty(orderPO.getEarliestArriveTime()) && StrUtilX.isNotEmpty(orderPO.getLatestArriveTime())) {
//                createSupplyOrderRequest.setArrivalEarlyTime(orderPO.getEarliestArriveTime().substring(0, 5));
//                createSupplyOrderRequest.setArrivalLateTime(orderPO.getLatestArriveTime().substring(0, 5));
//            } else {
//                createSupplyOrderRequest.setArrivalEarlyTime("12:00");//暂时写死
//                createSupplyOrderRequest.setArrivalLateTime("20:00");//暂时写死
//            }
//        } else {
//            HourlyRoomDetail hourlyRoomDetail = new HourlyRoomDetail();
//            hourlyRoomDetail.setCheckInTime(DateUtilX.dateToString(supplyOrderPO.getStartTime(), "yyyy-MM-dd HH:mm:ss"));
//            hourlyRoomDetail.setCheckOutTime(DateUtilX.dateToString(supplyOrderPO.getEndTime(), "yyyy-MM-dd HH:mm:ss"));
//            createSupplyOrderRequest.setHourlyRoomDetail(hourlyRoomDetail);
//        }
//        createSupplyOrderRequest.setRequestId(request.getRequestId());
//        createSupplyOrderRequest.setNationality(orderPO.getNationality());
//        return createSupplyOrderRequest;
//    }


    /**
     * 根据订单id批量查询供货单订单号
     */
    @Override
    public List<SupplyOrderPO> querySupplyOrder(List<Integer> list) {
        Example example = new Example(SupplyOrderPO.class);
        example.createCriteria().andIn("orderId", list)
                .andEqualTo("confirmationStatus", ConfirmationStatusEnum.CONFIRMED.key);
        return supplyOrderMapper.selectByExample(example);
    }

    /**
     * 根据供货单id批量更新供货单订单号
     */
    @Override
    public void batchupdateSupplyOrder(List<Integer> list) {
        supplyOrderMapper.batchUpdateOrderStatusDone(list);
    }

    /**
     * 订单入住明细
     */
    @Override
    @Transactional
    public void pushOrderCheckDetail(OrderCheckDetailInfoDTO orderCheckDetailInfoDTO) {
        Example example = new Example(SupplyOrderPO.class);
        example.createCriteria().andEqualTo("supplyOrderCode", orderCheckDetailInfoDTO.getSupplyOrderCode());
        SupplyOrderPO supplyOrderPO = supplyOrderMapper.selectOneByExample(example);
        if (supplyOrderPO == null) {
            throw new SysException(ErrorCodeEnum.SUPPLY_ORDER_NOT_EXIST);
        }
        OrderPO orderPO = orderMapper.selectByPrimaryKey(supplyOrderPO.getOrderId());
        List<OrderCheckInfoDTO> orderCheckInfoDTOS = getOrderCheckInfoDTOS(orderCheckDetailInfoDTO, supplyOrderPO, orderPO);
        // 直接覆盖，删除入住明细再新增
        List<OrderCheckInfoEntity> infoList = orderCheckInfoMapper.selectList(new QueryWrapper<OrderCheckInfoEntity>().eq("supply_order_code", orderCheckDetailInfoDTO.getSupplyOrderCode()));
        if (CollUtilX.isNotEmpty(infoList)) {
            Date date = new Date();
            for (OrderCheckInfoEntity orderCheckInfoEntity : infoList) {
                OrderCheckInfoEntity checkInfoEntity = new OrderCheckInfoEntity();
                checkInfoEntity.setId(orderCheckInfoEntity.getId());
                checkInfoEntity.setDeleted(1);
                checkInfoEntity.setUpdatedDt(date);
                checkInfoEntity.setUpdatedBy(orderCheckDetailInfoDTO.getOperator());
                orderCheckInfoMapper.updateById(checkInfoEntity);

                OrderCheckDetailEntity checkDetailEntity = new OrderCheckDetailEntity();
                checkDetailEntity.setDeleted(1);
                checkDetailEntity.setUpdatedDt(date);
                checkDetailEntity.setUpdatedBy(orderCheckDetailInfoDTO.getOperator());
                orderCheckDetailMapper.update(checkDetailEntity, new UpdateWrapper<OrderCheckDetailEntity>().eq("check_info_id", orderCheckInfoEntity.getId()));
            }
        }
        orderCheckInfoService.saveOrUpdateOrderCheckInfo(orderCheckInfoDTOS, orderCheckDetailInfoDTO.getOperator(), orderCheckDetailInfoDTO.getIsSync());
        // 异步通知入住明细给分销商
        CompletableFuture.runAsync(() -> orderCheckInfoService.notifyAgent(orderPO.getOrderCode()));
    }


    /**
     * 组装入住明细信息
     */
    private List<OrderCheckInfoDTO> getOrderCheckInfoDTOS(OrderCheckDetailInfoDTO orderCheckDetailInfoDTO, SupplyOrderPO supplyOrderPO, OrderPO orderPO) {
        return orderCheckDetailInfoDTO.getCheckDetails().stream()
                .map(orderCheckDetail -> {
                    OrderCheckInfoDTO orderCheckInfoDTO = new OrderCheckInfoDTO();
                    orderCheckInfoDTO.setOrderCode(orderPO.getOrderCode());
                    orderCheckInfoDTO.setStartDate(DateUtilX.stringToDate(orderCheckDetail.getCheckInDate()));
                    orderCheckInfoDTO.setEndDate(DateUtilX.stringToDate(orderCheckDetail.getCheckOutDate()));
                    orderCheckInfoDTO.setCheckInState(orderCheckDetail.getCheckInState());
                    orderCheckInfoDTO.setGuestName(StrUtilX.listToString(orderCheckDetail.getGuestInfos(), ","));
                    orderCheckInfoDTO.setRoomNumber(orderCheckDetail.getRoomNumber());
                    orderCheckInfoDTO.setSupplyOrderCode(supplyOrderPO.getSupplyOrderCode());
                    List<OrderCheckDetailDTO> orderCheckDetailDTOList = orderCheckDetail.getPriceItems().stream()
                            .map(orderCheckDetailPriceItem -> {
                                OrderCheckDetailDTO orderCheckDetailDTO = new OrderCheckDetailDTO();
                                orderCheckDetailDTO.setOrderCode(orderPO.getOrderCode());
                                orderCheckDetailDTO.setSaleDate(DateUtilX.stringToDate(orderCheckDetailPriceItem.getSaleDate()));
                                orderCheckDetailDTO.setSalePrice(orderCheckDetailPriceItem.getSalePrice());
                                orderCheckDetailDTO.setRefundPrice(orderCheckDetailPriceItem.getRefundPrice());
                                orderCheckDetailDTO.setSupplyOrderCode(supplyOrderPO.getSupplyOrderCode());
                                orderCheckDetailDTO.setDeleted(0);
                                return orderCheckDetailDTO;
                            }).collect(Collectors.toList());
                    orderCheckInfoDTO.setCheckInDetailList(orderCheckDetailDTOList);
                    orderCheckInfoDTO.setDeleted(0);
                    return orderCheckInfoDTO;
                }).collect(Collectors.toList());
    }

    /**
     * 发票开票状态
     */
    @Override
    public void pushInvoiceStatus(InvoiceNotifyRequest request) {
        Example example = new Example(SupplyOrderPO.class);
        example.createCriteria().andEqualTo("supplyOrderCode", request.getSupplyOrderCode())
                .andEqualTo("supplierOrderCode", request.getSupplierOrderCode());
        SupplyOrderPO supplyOrderPO = supplyOrderMapper.selectOneByExample(example);
        OrderPO orderPO = orderMapper.selectByPrimaryKey(supplyOrderPO.getOrderId());
        OrderInvoicePO orderInvoicePO = new OrderInvoicePO();
        orderInvoicePO.setOrderCode(orderPO.getOrderCode());
        OrderInvoicePO orderInvoice = orderInvoiceMapper.selectOne(orderInvoicePO);
        if (Objects.equals(orderInvoice.getInvoiceStatus(), 1)) {
            throw new SysException(ErrorCodeEnum.SYSTEM_EXCEPTION.errorCode, "此订单已开票");
        } else {
            orderInvoice.setInvoiceStatus(request.getInvoiceStatus());
            orderInvoice.setInvoiceUrls(String.join(",", request.getInvoiceUrls()));
            orderInvoiceMapper.updateByPrimaryKeySelective(orderInvoice);
        }
    }

    @Override
    public List<SupplyOrderDTO> queryInBlankCheckInfoSupplyOrderCodeList(InBlankCheckInfoDTO inBlankCheckInfoDTO) {
        // 时间为空就设置当前时间
        if (StrUtilX.isEmpty(inBlankCheckInfoDTO.getThisDate())) {
            inBlankCheckInfoDTO.setThisDate(DateUtilX.dateToString(DateUtilX.getCurrentDate()));
        }
        return supplyOrderMapper.selectInBlankCheckInfoSupplyOrderCodeList(inBlankCheckInfoDTO);
    }

    /**
     * 删除供货单附件(逻辑删除)
     */
    @Override
    public void deleteSupplyOrderAttachmentByLogic(DeleteSupplyOrderAttachmentByLogicDTO deleteSupplyOrderAttachmentByLogicDTO) {
        if (deleteSupplyOrderAttachmentByLogicDTO.getSupplyOrderId() == null) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_SUPPLYORDERID);
        }
        if (deleteSupplyOrderAttachmentByLogicDTO.getFileUrl() == null) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_FILEURL);
        }
        String fileUrl = deleteSupplyOrderAttachmentByLogicDTO.getFileUrl();
        SupplyAttachmentPO supplyAttachmentPO = new SupplyAttachmentPO();
        supplyAttachmentPO.setActive(0);
        Example supplyAttachmentExample = new Example(SupplyAttachmentPO.class);
        Example.Criteria supplyAttachmentCriteria = supplyAttachmentExample.createCriteria();
        supplyAttachmentCriteria
                .andEqualTo("supplyOrderId", deleteSupplyOrderAttachmentByLogicDTO.getSupplyOrderId())
                .andEqualTo("url", fileUrl);
        supplyAttachmentMapper.updateByExampleSelective(supplyAttachmentPO, supplyAttachmentExample);
    }

    /**
     * 同步供货单状态
     */
    @Override
    public Response<SyncOrderDTO> syncOrderStatus(QueryOrderStatusDTO request) {
        SupplyOrderPO supplyOrderPO = supplyOrderMapper.selectByPrimaryKey(request.getSupplyOrderId());
        if (StrUtilX.isEmpty(supplyOrderPO.getSupplierOrderCode())) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_SUPPLYORDERCODE);
        }
        QuerySupplyOrderRequest supplyOrderRequest = new QuerySupplyOrderRequest();
//        supplyOrderRequest.setSupplyCode(supplyOrderPO.getSupplierCode());
        supplyOrderRequest.setSupplyOrderCode(supplyOrderPO.getSupplyOrderCode());
        supplyOrderRequest.setSupplierOrderCode(supplyOrderPO.getSupplierOrderCode());

        OrderPO orderPO = orderMapper.selectByPrimaryKey(supplyOrderPO.getOrderId());
        if (orderPO == null) {
            throw new SysException(ErrorCodeEnum.ORDER_NOT_EXISTS);
        }
        supplyOrderRequest.setUserAccount(orderPO.getUserAccount());
        supplyOrderRequest.setRequestId(request.getRequestId());

        // 查询供应商单号信息
        Response<QuerySupplyOrderResponse> querySupplyOrderResp = supplyDirectShubOrderRemote.querySupplierOrder(supplyOrderRequest);
        if (querySupplyOrderResp.isError()) {
            throw new SysException(querySupplyOrderResp.getFailCode(), querySupplyOrderResp.getFailReason());
        }
        QuerySupplyOrderResponse supplyOrder = querySupplyOrderResp.getModel();
        // 添加提示语和供应商订单状态
        SyncOrderDTO syncOrderDTO = new SyncOrderDTO();
        syncOrderDTO.setConfirmationNumber(supplyOrder.getConfirmationNumber());
        syncOrderDTO.setSupplyReward(supplyOrder.getSupplyReward());
        syncOrderDTO.setSupplyShouldRackBack(supplyOrder.getSupplyShouldRackBack());
        syncOrderDTO.setRefundPrice(supplyOrder.getRefundFee());
        syncOrderDTO.setRefundCurrency(supplyOrder.getRefundFeeCurrency());
        if (supplyOrder.getSupplyResult() == null || supplyOrder.getSupplyResult().equals(supplyOrderPO.getConfirmationStatus())) {
            syncOrderDTO.setPrompt("供应商订单状态与我方供货单状态一致，无需变更。");
        } else if (supplyOrderPO.getConfirmationStatus().equals(OrderStatusEnum.CONFIRMING.no)) {
            syncOrderDTO.setSupplierOrderStatus(supplyOrder.getSupplyResult());
            syncOrderDTO.setPrompt("供应商订单状态为" + OrderStatusEnum.getValue(supplyOrder.getSupplyResult()) + "，是否把供应商订单状态写入我方供货单状态？");
        } else {
            syncOrderDTO.setSupplierOrderStatus(supplyOrder.getSupplyResult());
            syncOrderDTO.setPrompt("供应商订单状态为" + OrderStatusEnum.getValue(supplyOrder.getSupplyResult()) + "，与我方供货单状态不一致，是否用供应商订单状态覆盖我方供货单状态？");
        }

        return Response.success(syncOrderDTO);
    }

    @Override
    public void modifyOrderStatus(QueryOrderStatusDTO request) {
        SupplyOrderPO supplyOrderPO = supplyOrderMapper.selectByPrimaryKey(request.getSupplyOrderId());
        Integer oldStatus = supplyOrderPO.getConfirmationStatus();
        supplyOrderPO.setConfirmationStatus(request.getSupplierOrderStatus());
        // 修改确认号
        if (StrUtilX.isNotEmpty(request.getConfirmationNumber())) {
            supplyOrderPO.setConfirmationCode(request.getConfirmationNumber());
        }
        // 修改奖励
        if (request.getSupplyReward() != null) {
            supplyOrderPO.setRewardAmt(request.getSupplyReward());
        }
        // 修改退订费
        if (request.getRefundPrice() != null) {
            // 判断币种是否一致
            if (request.getRefundCurrency() == null || request.getRefundPrice().compareTo(BigDecimal.ZERO) == 0 ||
                    supplyOrderPO.getBaseCurrency().equals(request.getRefundCurrency())) {
                supplyOrderPO.setRefundFee(request.getRefundPrice());
            } else {
                // 转换商家币种
                BigDecimal orgRate = getRateToOrgCurrency(request.getRefundCurrency());
                // 商家转底价币种
                OrgDTO orgDTO = CommonInitializer.getOrgInfo();
                BigDecimal rateToAgentRate = RedisUtil.getRateToTargetCurrency(Integer.parseInt(orgDTO.getOrgCurrency()), CompanyDTO.COMPANY_CODE, supplyOrderPO.getBaseCurrency());
                // 计算退订费并进位
                BigDecimal refundFee = CommonTgUtils.setScale(request.getRefundPrice().multiply(orgRate).multiply(rateToAgentRate), 2, 1);
                supplyOrderPO.setRefundFee(refundFee);
            }
            if (supplyOrderPO.getBasePrice().compareTo(supplyOrderPO.getRefundFee()) < 0) {
                throw new SysException(ErrorCodeEnum.CANCEL_SUPPLIER_ORDER_AMOUNT_IS_ERROR);
            }
        }
        if (ConfirmationStatusEnum.CANCELED.key.equals(request.getSupplierOrderStatus())) {
            supplyOrderPO.setRewardAmt(BigDecimal.ZERO);
            supplyOrderPO.setRebateAmt(BigDecimal.ZERO);
            supplyOrderPO.setSupplyOrderAmt(request.getRefundPrice() == null ? BigDecimal.ZERO : request.getRefundPrice());
        }
        supplyOrderPO.setUpdatedBy(request.getOperator());
        supplyOrderPO.setUpdatedDt(DateUtilX.dateToString(new Date()));
        supplyOrderMapper.updateByPrimaryKeySelective(supplyOrderPO);

        // 有确认号，并且确认状态为待确认的时候，才把确认号写到订单中
        if (StrUtilX.isNotEmpty(supplyOrderPO.getConfirmationCode()) && null != supplyOrderPO.getConfirmationStatus() && supplyOrderPO.getConfirmationStatus().equals(1)) {
            OrderExtendPO queryPO = new OrderExtendPO();
            queryPO.setOrderId(supplyOrderPO.getOrderId());
            List<OrderExtendPO> orderExtendPOList = orderExtendMapper.select(queryPO);

            if (CollUtilX.isNotEmpty(orderExtendPOList)) {
                OrderExtendPO orderExtendPO = orderExtendPOList.get(0);
                OrderExtendPO updatePO = new OrderExtendPO();
                String confirmationCode = supplyOrderPO.getConfirmationCode();
                if (StrUtilX.isNotEmpty(orderExtendPO.getConfirmationCode())) {
                    confirmationCode = orderExtendPO.getConfirmationCode() + "," + supplyOrderPO.getConfirmationCode();
                }
                updatePO.setConfirmationCode(confirmationCode);
                updatePO.setId(orderExtendPO.getId());
                orderExtendMapper.updateByPrimaryKeySelective(updatePO);
            }
        }

        //2024年1月31日 只同步预付的结算单结果
        OrderPO orderPO = orderMapper.selectByPrimaryKey(supplyOrderPO.getOrderId());
        //通知自助结算结果
        if (orderPO.getPayMethod().equals(PayMethodEnum.PREPAY.key) && (request.getSupplierOrderStatus() == OrderStatusEnum.CONFIRMED.no || request.getSupplierOrderStatus() == OrderStatusEnum.CANCELED.no)) {
            try {
                SettleSupplyResultReq req = new SettleSupplyResultReq();
                req.setSupplyResult(request.getSupplierOrderStatus());
                req.setOrderId(Long.valueOf(supplyOrderPO.getOrderId()));
                req.setHotelId(supplyOrderPO.getHotelId());
                req.setSupplyOrderId(Long.valueOf(supplyOrderPO.getId()));
                req.setSupplierCode(supplyOrderPO.getSupplierCode());
                req.setConfirmNo(supplyOrderPO.getConfirmationCode());
                settleServerRemote.writeSettleTaskToRedis(req);
            } catch (Exception e) {
                log.error("writeSettleTaskToRedis Exception 通知自助结算结果异常！ param: {} e=", JSONObject.toJSONString(request), e);
            }
        }

        // 查询订单
        SupplyOrderPO supplyOrderQuery = new SupplyOrderPO();
        supplyOrderQuery.setOrderId(supplyOrderPO.getOrderId());
        List<SupplyOrderPO> supplyOrderPOList = supplyOrderMapper.select(supplyOrderQuery);
        // 多供货单时不处理
        if (supplyOrderPOList.size() < 2) {
            // 如果是确认订单状态，判断是否需要自动确认订单
            if (null != supplyOrderPO.getConfirmationStatus() && supplyOrderPO.getConfirmationStatus().equals(ConfirmationStatusEnum.CONFIRMED.key)) {
                // 判断自动确认渠道是否开通
                String companyCode = orderPO.getCompanyCode();
                QueryAutoConfirmChannelDto autoConfirmChannelDto = orderMapper.queryAutoConfirmChannel(companyCode);
                if (autoConfirmChannelDto != null && autoConfirmChannelDto.getAutomaticStatus().equals("1")) {
                    QueryConfirmOrderInfoDTO queryConfirmOrderInfoDTO = new QueryConfirmOrderInfoDTO();
                    queryConfirmOrderInfoDTO.setOrderId(orderPO.getId());
                    queryConfirmOrderInfoDTO.setConfirmType(orderPO.getModificationStatus());
                    String orderInfo = orderQueryService.queryConfirmOrderInfo(queryConfirmOrderInfoDTO);
                    ConfirmOrderDTO confirmOrderDTO = new ConfirmOrderDTO();
                    confirmOrderDTO.setConfirmationContent(orderInfo);
                    confirmOrderDTO.setOrderId(orderPO.getId());
                    confirmOrderDTO.setOperator(request.getOperator());
                    orderService.confirmOrder(confirmOrderDTO);
                }
            } else if (null != supplyOrderPO.getConfirmationStatus() && supplyOrderPO.getConfirmationStatus().equals(ConfirmationStatusEnum.CANCELED.key)) {
                OrderAutoCancelDTO autoCancelDTO = orderMapper.queryAutoCancel(orderPO.getCompanyCode());
                if (null != autoCancelDTO && autoCancelDTO.getAutomaticStatus().equals("1")) {
                    CancelOrderDTO cancelOrderDTO = new CancelOrderDTO();
                    cancelOrderDTO.setOrderId(orderPO.getId());
                    // 判断供应商是否返回了退订费
                    if (request.getRefundPrice() != null) {
                        // 判断币种是否一致
                        if (request.getRefundCurrency() != null && !request.getRefundCurrency().equals(orderPO.getSaleCurrency())) {
                            // 转换商家币种
                            BigDecimal orgRate = getRateToOrgCurrency(request.getRefundCurrency());
                            // 商家转客户币种
                            OrgDTO orgDTO = CommonInitializer.getOrgInfo();
                            BigDecimal rateToAgentRate = RedisUtil.getRateToTargetCurrency(Integer.parseInt(orgDTO.getOrgCurrency()), CompanyDTO.COMPANY_CODE, orderPO.getSaleCurrency());
                            // 计算退订费并进位
                            BigDecimal refundFee = CommonTgUtils.setScale(request.getRefundPrice().multiply(orgRate).multiply(rateToAgentRate), 2, 1);
                            cancelOrderDTO.setRefundFee(refundFee);
                        } else {
                            cancelOrderDTO.setRefundFee(request.getRefundPrice());
                        }
                    }
                    cancelOrderDTO.setOperator(request.getOperator());
                    cancelOrderDTO.setSupplyResult(request.getSupplierOrderStatus());
                    orderService.cancelOrder(cancelOrderDTO, 1, false);
                }
            }
        }

        // 奖励不为空，报表统计重新计算
        try {
            // 加入到统计报表队列中
            stringRedisTemplate.convertAndSend(RedisKey.REPORT_STATISTICS, orderPO.getOrderCode());
        } catch (Exception e) {
            log.error("加入到统计报表队列异常,error:", e);
        }

        String content = "同步供货单状态由" + OrderStatusEnum.getValue(oldStatus) + "变为" + OrderStatusEnum.getValue(request.getSupplierOrderStatus());

        // 记日志
        orderCommonService.saveOrderLog(
                supplyOrderPO.getOrderId(),
                request.getOperator(),
                request.getOrderOwnerName(),
                supplyOrderPO.getSupplyOrderCode(),
                content
        );

        // 供货单状态为确认时修改财务状态
        if (null != supplyOrderPO.getSettlementType() && supplyOrderPO.getSettlementType() != SettlementTypeEnum.SINGLE.key
                && Objects.equals(request.getSupplierOrderStatus(), ConfirmationStatusEnum.CONFIRMED.key)) {

            SupplyOrderFinancePO supplyOrderFinanceQuery = new SupplyOrderFinancePO();
            supplyOrderFinanceQuery.setSupplyOrderId(supplyOrderPO.getId());
            supplyOrderFinanceQuery.setFinanceType(StatementTypeEnum.SUPPLY_ORDER_AMT.key);
            SupplyOrderFinancePO supplyOrderFinancePO = supplyOrderFinanceMapper.selectOne(supplyOrderFinanceQuery);
            SupplyOrderFinancePO supplyOrderFinanceUpdate = new SupplyOrderFinancePO();
            supplyOrderFinanceUpdate.setId(supplyOrderFinancePO.getId());
            //非单结更新对账状态
            if (!Objects.equals(supplyOrderFinancePO.getCheckStatus(), CheckStatusEnum.CHECKING.key)) {
                if (Objects.equals(supplyOrderPO.getConfirmationStatus(), ConfirmationStatusEnum.CANCELED.key)) {
                    supplyOrderFinanceUpdate.setUnpaidAmt(supplyOrderPO.getSupplyOrderAmt().subtract(supplyOrderPO.getCommission()).subtract(supplyOrderFinancePO.getPaidAmt()));
                }
                if (BigDecimal.ZERO.compareTo(supplyOrderFinancePO.getUnpaidAmt()) == 0) {
                    //如果未收金额为0，则改为已对账
                    supplyOrderFinanceUpdate.setCheckStatus(CheckStatusEnum.CANNOT_CHECK.key);
                } else {
                    //如果未收金额不为0，则改为可出账
                    supplyOrderFinanceUpdate.setCheckStatus(CheckStatusEnum.CAN_CHECK.key);
                }
                supplyOrderFinanceMapper.updateByPrimaryKeySelective(supplyOrderFinanceUpdate);
            }
        }
    }

    /**
     * 获取币种对CNY的汇率
     *
     * @param originalCurrency 币种
     * @return 汇率
     */
    private BigDecimal getRateToOrgCurrency(Integer originalCurrency) {
        return RedisUtil.getRateToOrgCurrency(originalCurrency, CompanyDTO.COMPANY_CODE);
    }

    @Override
    @Transactional
    public void modifySupplyRewardAmt(ModifySupplyRewardDTO request) {
        SupplyOrderPO supplyOrderPO = supplyOrderMapper.selectByPrimaryKey(request.getSupplyOrderId());
        SupplyOrderFinancePO supplyOrderFinanceQuery = new SupplyOrderFinancePO();
        supplyOrderFinanceQuery.setSupplyOrderId(supplyOrderPO.getId());
        supplyOrderFinanceQuery.setFinanceType(StatementTypeEnum.REWARD_AMT.key);
        SupplyOrderFinancePO supplyOrderFinancePO = supplyOrderFinanceMapper.selectOne(supplyOrderFinanceQuery);
        //已锁定
        if (supplyOrderFinancePO != null && supplyOrderFinancePO.getFinanceLockStatus() == 1) {
            throw new SysException(ErrorCodeEnum.SUPPLY_REWARD_IS_LOCK);
        }
        BigDecimal reward = supplyOrderPO.getRewardAmt() == null ? BigDecimal.ZERO : supplyOrderPO.getRewardAmt();
        OrderPO orderPO = new OrderPO();
        orderPO.setId(supplyOrderPO.getOrderId());
        OrderPO order = orderMapper.selectByPrimaryKey(orderPO);
        if (!reward.equals(request.getRewardAmt())) {
            supplyOrderPO.setRewardAmt(request.getRewardAmt());
            //利润=订单总额-供货单总额+供货单奖励-供货单返佣
            supplyOrderMapper.updateByPrimaryKeySelective(supplyOrderPO);
            order.setProfit(order.getProfit().add(request.getRewardAmt()).subtract(reward));
            orderMapper.updateByPrimaryKeySelective(order);

            //更新供货单奖励结算状态

            SupplyOrderFinancePO supplyOrderFinanceUpdate = new SupplyOrderFinancePO();
            if (supplyOrderFinancePO != null) {
                supplyOrderFinanceUpdate.setId(supplyOrderFinancePO.getId());
                supplyOrderFinanceUpdate.setUnpaidAmt(supplyOrderPO.getRewardAmt().subtract(null == supplyOrderFinancePO.getPaidAmt() ? BigDecimal.ZERO : supplyOrderFinancePO.getPaidAmt()));
                //结算状态
                if (BigDecimal.ZERO.compareTo(supplyOrderFinanceUpdate.getUnpaidAmt()) == 0) {
//                    supplyOrderFinanceUpdate.setSettlementStatus(1);
                } else {
                    supplyOrderFinanceUpdate.setSettlementStatus(0);
                }
                //出账状态
                if (!Objects.equals(supplyOrderFinancePO.getCheckStatus(), CheckStatusEnum.CHECKING.key)) {
                    if (BigDecimal.ZERO.compareTo(supplyOrderFinanceUpdate.getUnpaidAmt()) == 0) {
                        //如果未收金额为0，则改为不可出账
                        supplyOrderFinanceUpdate.setCheckStatus(CheckStatusEnum.CANNOT_CHECK.key);
                    } else {
                        //如果未收金额不为0，则改为可出账
                        supplyOrderFinanceUpdate.setCheckStatus(CheckStatusEnum.CAN_CHECK.key);
                    }
                }
                supplyOrderFinanceMapper.updateByPrimaryKeySelective(supplyOrderFinanceUpdate);
            } else {
                supplyOrderFinanceUpdate.setSupplyOrderId(supplyOrderPO.getId());
                supplyOrderFinanceUpdate.setSupplyOrderCode(supplyOrderPO.getSupplyOrderCode());
                supplyOrderFinanceUpdate.setPaidAmt(BigDecimal.ZERO);
                supplyOrderFinanceUpdate.setUnpaidAmt(request.getRewardAmt());
                supplyOrderFinanceUpdate.setFinanceType(StatementTypeEnum.REWARD_AMT.key);
                supplyOrderFinanceUpdate.setUnconfirmedReceivedAmt(BigDecimal.ZERO);
                supplyOrderFinanceUpdate.setUnconfirmedPaidAmt(BigDecimal.ZERO);
                supplyOrderFinanceUpdate.setSettlementDate(DateUtilX.stringToDate(DateUtilX.dateToString(new Date())));
                supplyOrderFinanceUpdate.setFinanceLockStatus(0);
                supplyOrderFinanceUpdate.setCreatedBy(request.getOperator());
                supplyOrderFinanceUpdate.setCreatedDt(DateUtilX.dateToString(new Date(), hour_format));
                //结算状态
                if (BigDecimal.ZERO.compareTo(supplyOrderFinanceUpdate.getUnpaidAmt()) == 0) {
                    supplyOrderFinanceUpdate.setSettlementStatus(1);
                } else {
                    supplyOrderFinanceUpdate.setSettlementStatus(0);
                }
                //出账状态
                if (BigDecimal.ZERO.compareTo(supplyOrderFinanceUpdate.getUnpaidAmt()) == 0) {
                    //如果未收金额为0，则改为不可出账
                    supplyOrderFinanceUpdate.setCheckStatus(CheckStatusEnum.CANNOT_CHECK.key);
                } else {
                    //如果未收金额不为0，则改为可出账
                    supplyOrderFinanceUpdate.setCheckStatus(CheckStatusEnum.CAN_CHECK.key);
                }
                supplyOrderFinanceMapper.insert(supplyOrderFinanceUpdate);
            }

            String content = "供货单 (" + supplyOrderPO.getSupplyOrderCode() + ")" +
                    "奖励变更,奖励由" + reward.toString() +
                    "变更为" + request.getRewardAmt();
            //记日志
            orderCommonService.saveOrderLog(
                    supplyOrderPO.getOrderId(),
                    request.getOperator(),
                    request.getOrderOwnerName(),
                    supplyOrderPO.getSupplyOrderCode(),
                    content
            );

            try {
                //加入到统计报表队列中
                stringRedisTemplate.convertAndSend(RedisKey.REPORT_STATISTICS, order.getOrderCode());
            } catch (Exception e) {
                log.error("加入到统计报表队列异常,error:", e);
            }

        }
    }


    @Override
    public void updatePushOrderStatus(UpdatePushOrderStatusReq updatePushOrderStatusReq) {
        OrderPO po = new OrderPO();
        po.setId(updatePushOrderStatusReq.getId());
        po.setPushOrderStatus(0);
        orderMapper.updateByPrimaryKeySelective(po);
    }

    @Override
    @Transactional
    public void modifySupplyRebateAmt(ModifySupplyRebateDTO request) {
        SupplyOrderPO supplyOrderPO = supplyOrderMapper.selectByPrimaryKey(request.getSupplyOrderId());
        SupplyOrderFinancePO supplyOrderFinanceQuery = new SupplyOrderFinancePO();
        supplyOrderFinanceQuery.setSupplyOrderId(supplyOrderPO.getId());
        supplyOrderFinanceQuery.setFinanceType(StatementTypeEnum.REBATE_AMT.key);
        SupplyOrderFinancePO supplyOrderFinancePO = supplyOrderFinanceMapper.selectOne(supplyOrderFinanceQuery);
        //已锁定
        if (supplyOrderFinancePO != null && supplyOrderFinancePO.getFinanceLockStatus() == 1) {
            throw new SysException(ErrorCodeEnum.SUPPLY_REBATE_IS_LOCK);
        }
        BigDecimal rebate = supplyOrderPO.getRebateAmt() == null ? BigDecimal.ZERO : supplyOrderPO.getRebateAmt();
        OrderPO orderPO = new OrderPO();
        orderPO.setId(supplyOrderPO.getOrderId());
        OrderPO order = orderMapper.selectByPrimaryKey(orderPO);
        if (!rebate.equals(request.getRebateAmt())) {
            supplyOrderPO.setRebateAmt(request.getRebateAmt());
            //利润=订单总额-供货单总额+供货单奖励-供货单返佣
            supplyOrderMapper.updateByPrimaryKeySelective(supplyOrderPO);

            // 转换汇率
            BigDecimal rebateAmt = rebate.subtract(request.getRebateAmt());
            if (supplyOrderPO.getSupplierToAgentRate() != null) {
                rebateAmt = CommonTgUtils.setRateAmt(rebateAmt, supplyOrderPO.getSupplierToAgentRate());
            }
            order.setProfit(order.getProfit().add(rebateAmt));
            orderMapper.updateByPrimaryKeySelective(order);

            //更新供货单奖励结算状态
            SupplyOrderFinancePO supplyOrderFinanceUpdate = new SupplyOrderFinancePO();
            if (supplyOrderFinancePO != null) {
                supplyOrderFinanceUpdate.setId(supplyOrderFinancePO.getId());
                supplyOrderFinanceUpdate.setUnpaidAmt(supplyOrderPO.getRebateAmt().subtract(null == supplyOrderFinancePO.getPaidAmt() ? BigDecimal.ZERO : supplyOrderFinancePO.getPaidAmt()));
                if (BigDecimal.ZERO.compareTo(supplyOrderFinanceUpdate.getUnpaidAmt()) == 0) {
//                    supplyOrderFinanceUpdate.setSettlementStatus(1);
                } else {
                    supplyOrderFinanceUpdate.setSettlementStatus(0);
                }
                //出账状态
                if (!Objects.equals(supplyOrderFinancePO.getCheckStatus(), CheckStatusEnum.CHECKING.key)) {
                    if (BigDecimal.ZERO.compareTo(supplyOrderFinanceUpdate.getUnpaidAmt()) == 0) {
                        //如果未收金额为0，则改为不可出账
                        supplyOrderFinanceUpdate.setCheckStatus(CheckStatusEnum.CANNOT_CHECK.key);
                    } else {
                        //如果未收金额不为0，则改为可出账
                        supplyOrderFinanceUpdate.setCheckStatus(CheckStatusEnum.CAN_CHECK.key);
                    }
                }
                supplyOrderFinanceMapper.updateByPrimaryKeySelective(supplyOrderFinanceUpdate);
            } else {
                supplyOrderFinanceUpdate.setSupplyOrderId(supplyOrderPO.getId());
                supplyOrderFinanceUpdate.setSupplyOrderCode(supplyOrderPO.getSupplyOrderCode());
                supplyOrderFinanceUpdate.setPaidAmt(BigDecimal.ZERO);
                supplyOrderFinanceUpdate.setUnpaidAmt(request.getRebateAmt());
                supplyOrderFinanceUpdate.setFinanceType(StatementTypeEnum.REBATE_AMT.key);
                supplyOrderFinanceUpdate.setUnconfirmedReceivedAmt(BigDecimal.ZERO);
                supplyOrderFinanceUpdate.setUnconfirmedPaidAmt(BigDecimal.ZERO);
                supplyOrderFinanceUpdate.setSettlementDate(DateUtilX.stringToDate(DateUtilX.dateToString(new Date())));
                supplyOrderFinanceUpdate.setFinanceLockStatus(0);
                supplyOrderFinanceUpdate.setCreatedBy(request.getOperator());
                supplyOrderFinanceUpdate.setCreatedDt(DateUtilX.dateToString(new Date(), hour_format));
                if (BigDecimal.ZERO.compareTo(supplyOrderFinanceUpdate.getUnpaidAmt()) == 0) {
                    supplyOrderFinanceUpdate.setSettlementStatus(1);
                } else {
                    supplyOrderFinanceUpdate.setSettlementStatus(0);
                }
                if (BigDecimal.ZERO.compareTo(supplyOrderFinanceUpdate.getUnpaidAmt()) == 0) {
                    //如果未收金额为0，则改为不可出账
                    supplyOrderFinanceUpdate.setCheckStatus(CheckStatusEnum.CANNOT_CHECK.key);
                } else {
                    //如果未收金额不为0，则改为可出账
                    supplyOrderFinanceUpdate.setCheckStatus(CheckStatusEnum.CAN_CHECK.key);
                }
                supplyOrderFinanceMapper.insert(supplyOrderFinanceUpdate);
            }

            String content = "供货单 (" + supplyOrderPO.getSupplyOrderCode() + ")" +
                    "返佣变更,返佣由" + rebate.toString() +
                    "变更为" + request.getRebateAmt();
            //记日志
            orderCommonService.saveOrderLog(
                    supplyOrderPO.getOrderId(),
                    request.getOperator(),
                    request.getOrderOwnerName(),
                    supplyOrderPO.getSupplyOrderCode(),
                    content
            );
            try {
                //加入到统计报表队列中
                stringRedisTemplate.convertAndSend(RedisKey.REPORT_STATISTICS, order.getOrderCode());
            } catch (Exception e) {
                log.error("加入到统计报表队列异常,error:", e);
            }
        }
    }

    @Override
    public Response<Object> modifySupplyOrderRoomPrice(ModifyRoomPriceDTO request) {
        StringBuilder logSb = new StringBuilder();
        boolean isChange = false;
        BigDecimal basePriceTotalAmt = BigDecimal.ZERO;

        SupplyOrderPO supplyOrderPO = supplyOrderMapper.selectByPrimaryKey(request.getSupplyOrderId());
        SupplyProductPO querySupplyProduct = new SupplyProductPO();
        querySupplyProduct.setSupplyOrderId(supplyOrderPO.getId());
        SupplyProductPO supplyProductPO = supplyProductMapper.selectOne(querySupplyProduct);
        OrderPO orderPO = orderMapper.selectByPrimaryKey(supplyOrderPO.getOrderId());
        Integer oldSupplyConfirmationStatus = supplyOrderPO.getConfirmationStatus();

        // 1. 更新产品价格
        SupplyProductPO supplyProductUpdate = new SupplyProductPO();
        supplyProductUpdate.setId(supplyProductPO.getId());
        supplyProductUpdate.setUpdatedBy(request.getOperator());
        supplyProductUpdate.setUpdatedDt(DateUtilX.dateToString(new Date(), hour_format));

        // 2. 更新价格明细
        SupplyProductPricePO supplyProductPriceQuery = new SupplyProductPricePO();
        supplyProductPriceQuery.setSupplyOrderId(request.getSupplyOrderId());
        List<SupplyProductPricePO> supplyProductPricePOList = supplyProductPriceMapper.select(supplyProductPriceQuery);

        //根据房间，日期价格，修改房费
        for (ModifyRoomDetailDTO room : request.getRoomList()) {
            StringBuilder info = new StringBuilder();
            info.append("房间").append(room.getRoomNumber()).append(":供货单房费");
            boolean flag = false;
            for (ModifyPriceDetailDTO price : room.getPriceList()) {
                for (SupplyProductPricePO supplyProductPricePO : supplyProductPricePOList) {
                    // 判断日期是否为空，为空说明是税费
                    if (supplyProductPricePO.getSaleDate() == null) {
                        continue;
                    }
                    if (Objects.equals(supplyProductPricePO.getRoomNumber(), room.getRoomNumber()) &&
                            DateUtilX.dateToString(supplyProductPricePO.getSaleDate()).equals(price.getSaleDate())) {
                        if (price.getRoomPrice().compareTo(supplyProductPricePO.getRoomPrice()) != 0) {
                            info.append(",").append(price.getSaleDate()).append("改为").append(price.getRoomPrice());
                            supplyProductPricePO.setBasePrice(supplyProductPricePO.getBasePrice().subtract(supplyProductPricePO.getRoomPrice()).add(price.getRoomPrice()));
                            supplyProductPricePO.setRoomPrice(price.getRoomPrice());
                            supplyProductPriceMapper.updateByPrimaryKeySelective(supplyProductPricePO);
                            flag = true;
                        }
                        basePriceTotalAmt = basePriceTotalAmt.add(supplyProductPricePO.getBasePrice());
                        break;
                    }
                }
            }
            if (flag) {
                isChange = true;
                logSb.append(info).append(";");
            }
        }

        //供货单房费没有变更
        if (!isChange) {
            return Response.success();
        }
        BigDecimal changeAmt = basePriceTotalAmt.subtract(supplyProductPO.getBasePriceTotalAmt());
        if (supplyProductPO.getBasePriceTotalAmt().compareTo(basePriceTotalAmt) != 0) {
            logSb.append("供货单总金额由").append(supplyOrderPO.getSupplyOrderAmt().stripTrailingZeros().toPlainString())
                    .append("变更为").append(supplyOrderPO.getSupplyOrderAmt().add(changeAmt).stripTrailingZeros().toPlainString());
        }
        supplyProductUpdate.setBasePriceTotalAmt(basePriceTotalAmt);
        supplyProductMapper.updateByPrimaryKeySelective(supplyProductUpdate);

        // 3. 更新供货单金额和状态
        SupplyOrderPO supplyOrderUpdate = new SupplyOrderPO();
        supplyOrderUpdate.setId(supplyOrderPO.getId());
        supplyOrderUpdate.setBasePrice(supplyOrderPO.getBasePrice().add(changeAmt));
        //供货单金额=底价+退订费-返佣
        if (supplyOrderPO.getConfirmationStatus().equals(ConfirmationStatusEnum.CANCELED.key)) {//供货单是取消状态，不计算退订费
            if (!PayMethodEnum.PAY.key.equals(orderPO.getPayMethod())) {
                supplyOrderUpdate.setSupplyOrderAmt(supplyOrderUpdate.getBasePrice().subtract(null != supplyOrderPO.getCommission() ? supplyOrderPO.getCommission() :
                        BigDecimal.ZERO).add(null != supplyOrderPO.getAdditionalCharges() ? supplyOrderPO.getAdditionalCharges() : BigDecimal.ZERO));
            }
            supplyOrderUpdate.setRefundFee(BigDecimal.ZERO);//退订费清0
        } else {
            if (!PayMethodEnum.PAY.key.equals(orderPO.getPayMethod())) {
                supplyOrderUpdate.setSupplyOrderAmt(supplyOrderUpdate.getBasePrice().add(null != supplyOrderPO.getRefundFee() ? supplyOrderPO.getRefundFee() :
                                BigDecimal.ZERO).subtract(null != supplyOrderPO.getCommission() ? supplyOrderPO.getCommission() : BigDecimal.ZERO)
                        .add(null != supplyOrderPO.getAdditionalCharges() ? supplyOrderPO.getAdditionalCharges() : BigDecimal.ZERO));
            }
        }
        supplyOrderMapper.updateByPrimaryKeySelective(supplyOrderUpdate);

        // 4. 更新订单金额
        SupplyOrderPO supplyOrderQuery = new SupplyOrderPO();
        supplyOrderQuery.setOrderId(supplyOrderPO.getOrderId());
        List<SupplyOrderPO> supplyOrderPOList = supplyOrderMapper.select(supplyOrderQuery);
        BigDecimal supplyOrderSum = BigDecimal.ZERO;
        BigDecimal rewardSum = BigDecimal.ZERO;
        BigDecimal rebateSum = BigDecimal.ZERO;
        for (SupplyOrderPO supplyOrder : supplyOrderPOList) {
            BigDecimal supplierToAgentRate = Objects.nonNull(supplyOrder.getSupplierToAgentRate()) ? supplyOrder.getSupplierToAgentRate() : BigDecimal.ONE;
            supplyOrderSum = supplyOrderSum.add(supplyOrder.getSupplyOrderAmt().multiply(supplierToAgentRate));
            rewardSum = rewardSum.add((Objects.nonNull(supplyOrder.getRewardAmt()) ? supplyOrder.getRewardAmt() : BigDecimal.ZERO).multiply(supplierToAgentRate));
            rebateSum = rebateSum.add((Objects.nonNull(supplyOrder.getRebateAmt()) ? supplyOrder.getRebateAmt() : BigDecimal.ZERO).multiply(supplierToAgentRate));
        }
        OrderPO orderUpdate = new OrderPO();
        orderUpdate.setId(orderPO.getId());
        // orderUpdate.setSupplyOrderConfirmationStatus(ConfirmationStatusEnum.UNCONFIRM.key.toString());
        orderUpdate.setProfit(orderPO.getOrderAmt().subtract(supplyOrderSum).add(rewardSum).subtract(rebateSum));
        orderMapper.updateByPrimaryKeySelective(orderUpdate);


        try {
            // 5. 退扣配额
            if (!supplyProductPO.getProductId().contains("_")) {
                Map<String, Integer> map = new HashMap<>();
                if (StrUtilX.isNotEmpty(supplyProductPO.getProductId())) {
                    map.put("productId", Integer.valueOf(supplyProductPO.getProductId()));
                }
                map.put("orderId", orderPO.getId());
                map.put("type", 1);//查询以扣的配额
                Response<List<DebitedQuotaDTO>> debitedQuota = debitedQuotaRemote.queryDebitedQuota(map);
                log.info("查询扣退配额:{}", JSON.toJSONString(debitedQuota));
                List<DebitedQuotaDTO> debitedQuotaDTO = new ArrayList<>();
                if (debitedQuota.getResult().equals(ResultCodeEnum.SUCCESS.code) && null != debitedQuota.getModel()) {
                    debitedQuotaDTO = debitedQuota.getModel();
                }
                QuotaDTO quotaDTO = new QuotaDTO();
                quotaDTO.setOrderCode(orderPO.getOrderCode());
                quotaDTO.setOrderId(orderPO.getId());
                quotaDTO.setProductId(Integer.valueOf(supplyProductPO.getProductId()));

                quotaDTO.setSupplyOrderCode(supplyOrderPO.getSupplyOrderCode());
                quotaDTO.setSupplyOrderId(supplyOrderPO.getId());

                if (debitedQuotaDTO.size() > 0) {
                    //返还配额
                    StringBuilder saleDate = new StringBuilder();
                    for (DebitedQuotaDTO debitedQuotaDTOs : debitedQuotaDTO) {
                        saleDate.append(DateUtilX.dateToString(DateUtilX.stringToDate(debitedQuotaDTOs.getSaleDate(), "yyyy-MM-dd HH:mm:ss"), "yyyy-MM-dd")).append(",");
                    }
                    quotaDTO.setSaleDate(saleDate.append(DateUtilX.getCurrentDateStr(2)).toString());//加一段日期，用以处理后续逻辑
                    quotaDTO.setQuota(-debitedQuotaDTO.get(0).getQuota());//以扣配额为负数，负负得正，加配额
                    quotaRemote.modifyQuota(quotaDTO);
                }

                //扣除配额
                Map<String, String> data = new HashMap<>();
                data.put("begin", DateUtilX.dateToString(supplyOrderPO.getStartDate()));
                data.put("end", DateUtilX.dateToString(supplyOrderPO.getEndDate()));
                List<String> list = orderMapper.queryBetweenDate(data);
                StringBuilder saleDate1 = new StringBuilder();
                for (String s : list) {
                    saleDate1.append(s).append(",");
                }
                quotaDTO.setSaleDate(saleDate1.substring(0, saleDate1.length() - 1));
                quotaDTO.setQuota(-supplyOrderPO.getRoomQty());//减配额
                quotaRemote.modifyQuota(quotaDTO);
            }
        } catch (Exception e) {
            log.error("扣配额异常！订单ID：" + orderPO.getId(), e);
        }


        if (changeAmt.compareTo(BigDecimal.ZERO) != 0) {
            //更新订单未结算金额和结算状态
            SupplyOrderFinancePO supplyOrderFinanceQuery = new SupplyOrderFinancePO();
            supplyOrderFinanceQuery.setSupplyOrderId(supplyOrderPO.getId());
            supplyOrderFinanceQuery.setFinanceType(StatementTypeEnum.SUPPLY_ORDER_AMT.key);
            SupplyOrderFinancePO supplyOrderFinancePO = supplyOrderFinanceMapper.selectOne(supplyOrderFinanceQuery);
            SupplyOrderFinancePO supplyOrderFinanceUpdate = new SupplyOrderFinancePO();
            supplyOrderFinanceUpdate.setId(supplyOrderFinancePO.getId());
            //如果原来供货单是取消状态，未付金额=供货单金额
            if (oldSupplyConfirmationStatus.equals(ConfirmationStatusEnum.CANCELED.key) || PayMethodEnum.PAY.key.equals(orderPO.getPayMethod())) {
                supplyOrderFinanceUpdate.setUnpaidAmt(supplyOrderUpdate.getSupplyOrderAmt() == null ? BigDecimal.ZERO : supplyOrderUpdate.getSupplyOrderAmt());
            } else {
                supplyOrderFinanceUpdate.setUnpaidAmt(supplyOrderFinancePO.getUnpaidAmt().add(changeAmt));
            }

            if (BigDecimal.ZERO.compareTo(supplyOrderFinanceUpdate.getUnpaidAmt()) == 0) {
                supplyOrderFinanceUpdate.setSettlementStatus(1);
            } else {
                supplyOrderFinanceUpdate.setSettlementStatus(0);
            }
            //更新对账状态
            if (!Objects.equals(supplyOrderFinancePO.getCheckStatus(), CheckStatusEnum.CHECKING.key)) {
                if (BigDecimal.ZERO.compareTo(supplyOrderFinanceUpdate.getUnpaidAmt()) == 0) {
                    //如果未收金额为0，则改为不可出账
                    supplyOrderFinanceUpdate.setCheckStatus(CheckStatusEnum.CANNOT_CHECK.key);
                } else {
                    //如果未收金额不为0，则改为可出账
                    supplyOrderFinanceUpdate.setCheckStatus(CheckStatusEnum.CAN_CHECK.key);
                }
            }
            supplyOrderFinanceMapper.updateByPrimaryKeySelective(supplyOrderFinanceUpdate);
        }

        // 5. 记日志
        orderCommonService.saveOrderLog(
                orderPO.getId(),
                request.getOperator(),
                request.getOrderOwnerName(),
                supplyOrderPO.getSupplyOrderCode(),
                logSb.toString()
        );

        try {
            //加入到统计报表队列中
            stringRedisTemplate.convertAndSend(RedisKey.REPORT_STATISTICS, orderPO.getOrderCode());
        } catch (Exception e) {
            log.error("加入到统计报表队列异常,error:", e);
        }

        return Response.success();
    }

    @Override
    public Response<Object> handleExceptionCancelOrder(ExceptionCancelOrderDTO request) {
        if (request.getSupplyOrderId() == null) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_SUPPLYORDERID);
        }
        SupplyOrderPO supplyOrderUpdate = new SupplyOrderPO();
        supplyOrderUpdate.setId(request.getSupplyOrderId());
        supplyOrderUpdate.setECancelSupplyOrder(0);
        supplyOrderUpdate.setUpdatedBy(request.getOperator());
        supplyOrderUpdate.setUpdatedDt(DateUtilX.dateToString(new Date(), hour_format));
        int updateRows = supplyOrderMapper.updateByPrimaryKeySelective(supplyOrderUpdate);
        if (updateRows != 1) {
            return Response.error(ErrorCodeEnum.OPERATE_FAIL.errorCode, ErrorCodeEnum.OPERATE_FAIL.errorDesc);
        }
        return Response.success();
    }


    @Override
    public Response<Object> updateSupplierCheckInAbnormal(UpdateSupplierCheckInAbnormalReq req) {
        SupplyOrderAmtLogPO po = new SupplyOrderAmtLogPO();
        po.setId(req.getId());
        po.setStatus(1);
        supplyOrderAmtLogMapper.updateById(po);
        List<SupplyOrderAmtLogPO> supplyOrderAmtLogPOS = supplyOrderAmtLogMapper.selectList(new LambdaQueryWrapper<SupplyOrderAmtLogPO>().
                eq(SupplyOrderAmtLogPO::getSupplyOrderCode, req.getSupplyOrderCode())
                .eq(SupplyOrderAmtLogPO::getStatus, 0)
        );
        //判断供货单号是否还有没有处理完的数据，如果已经处理完则修改状态
        if (supplyOrderAmtLogPOS == null || supplyOrderAmtLogPOS.size() == 0) {
            supplyOrderMapper.updateSupplierCheckInAbnormal(req.getSupplyOrderCode());
        }
        return Response.success();
    }

//    private void sendSupplyOrderNotifyMessage(OrderPO orderPO, SupplyOrderPO supplyOrderPO) {
//        CompletableFuture.runAsync(() -> {
//            OrderNotifyMessageReq orderNotifyMessageReq = OrderNotifyMessageReq.builder()
//                    .orderNotifyType(OrderNotifyType.SUPPLY_ORDER_FAIL.getType())
//                    .orderCode(orderPO.getOrderCode())
//                    .supplyOrderCode(supplyOrderPO.getSupplyOrderCode())
//                    .supplierName(supplyOrderPO.getSupplierName())
//                    .build();
//            log.info("sendSupplyOrderNotifyMessage--supply order message notify:{}", JSON.toJSONString(orderNotifyMessageReq));
//            orderNotifyRemote.sendOrderNotifyMessageToAllUsers(orderNotifyMessageReq);
//        });
//    }

    /**
     * 电邮发单
     *
     * @param request          发单请求信息
     * @param emailSendingType 参考EmailSendingTypeEnum枚举值
     * @return 返回结果
     */
    @Override
    @Transactional
    public Response<Object> sendToEmail(SendToSupplierDTO request, Integer emailSendingType) {
        try {
            //按供货单查询供货单信息
            SupplyOrderPO supplyOrderPO = supplyOrderMapper.selectByPrimaryKey(request.getSupplyOrderId());
            //判断供货单是否为空
            if (null == supplyOrderPO) {
                //供货单不存在
                return Response.error(ErrorCodeEnum.SUPPLYORDER_NOT_EXIST.errorCode, ErrorCodeEnum.SUPPLYORDER_NOT_EXIST.errorDesc);
            }

            //必须是已发预定单或已重发预定单,才能发起取消单
            if (emailSendingType.equals(EmailSendingTypeEnum.CANCELLATION.key) &&
                    !(supplyOrderPO.getSendingStatus().equals(SendingStatusEnum.SEND_RESERVATION.key) || supplyOrderPO.getSendingStatus().equals(SendingStatusEnum.SEND_RESERVATION_AGAIN.key))) {
                //没发过预订单，不能操作
                return Response.error(ErrorCodeEnum.NO_RESERVATION_HAS_BEEN_ISSUED_CANNOT_OPERATE.errorCode, ErrorCodeEnum.NO_RESERVATION_HAS_BEEN_ISSUED_CANNOT_OPERATE.errorDesc);
            }

            //查询发单配置
            SupplierAutoChannelDto autoChannelDto = new SupplierAutoChannelDto();
            autoChannelDto.setSupplierCode(supplyOrderPO.getSupplierCode());
            Response<SupplierAutoChannelDto> response = orderService.querySupplierAutoChannel(autoChannelDto);
            SupplierAutoChannelDto supplierAutoChannelDto = response.getModel();


            //判断配置是否为空或者配置的非电邮发单
            if (null == supplierAutoChannelDto || null == supplierAutoChannelDto.getPreorderEmail()) {
                //当前供应商邮件发单未配置
                return Response.error(ErrorCodeEnum.EMAIL_SEND_NOT_CONFIG_SUPPLIER.errorCode, ErrorCodeEnum.EMAIL_SEND_NOT_CONFIG_SUPPLIER.errorDesc);
            }


            SupplyOrderIdDTO supplyOrderIdDTO = new SupplyOrderIdDTO();
            supplyOrderIdDTO.setSupplyOrderId(request.getSupplyOrderId());
            //查询供货单预览信息
            SupplyOrderPreviewDTO supplyOrder = orderQueryService.previewSupplyOrder(supplyOrderIdDTO);

            //判断供货单预览信息是否为空
            if (null == supplyOrder) {
                //供货单不存在
                return Response.error(ErrorCodeEnum.PREVIEW_SUPPLY_ORDER_INFO_IS_NULL.errorCode, ErrorCodeEnum.PREVIEW_SUPPLY_ORDER_INFO_IS_NULL.errorDesc);
            }


            OrderPO orderPO = orderMapper.selectByPrimaryKey(supplyOrderPO.getOrderId());
            if (orderPO == null) {
                log.error("订单信息为空,{}", JSON.toJSON(request));
                throw new SysException(ErrorCodeEnum.ORDER_NOT_EXISTS);
            }

            //【到店付 + 确认单】 不需要发送邮件
            if (orderPO.getPayMethod().equals(PayMethodEnum.PAY.key) && emailSendingType.equals(EmailSendingTypeEnum.CONFIRMATION.key)) {
                log.info("到店付 + 确认订单不需要发送确认邮件:{}", JSON.toJSON(orderPO));
                return Response.error(ErrorCodeEnum.ORDER_CONFIRMATION_EMAIL_NOT_REQUIRED.errorCode, ErrorCodeEnum.ORDER_CONFIRMATION_EMAIL_NOT_REQUIRED.errorDesc);
            }


            //已经取消和完成的订单 不能发任何供货单
            if (orderPO.getOrderConfirmationStatus() == OrderStatusEnum.CANCELED.getNo()
                    || orderPO.getOrderConfirmationStatus() == OrderStatusEnum.REFUSE.getNo()) {
                log.error("订单已完成或已取消，不能再次邮件发单 sendToSupplier param: {}", JSON.toJSON(request));
                throw new SysException(ErrorCodeEnum.CENTER_CONFIRMED_NO_MORE_ORDERS_CAN_BE_ISSUED_TO_SUPPLIERS);
            }


            //判断发单类型为【预定单】 + 【预付单】的情况
            if (request.isAutoSendOrder() && emailSendingType == EmailSendingTypeEnum.BOOKING_ORDER.key && orderPO.getPayMethod().equals(PayMethodEnum.PREPAY.key)) {
                //待支付订单，不支持自动发单
                if (orderPO.getPayStatus() != null && !(orderPO.getOrderConfirmationStatus().equals(ConfirmationStatusEnum.UNCONFIRM.key) && orderPO.getPayStatus().equals(PayStatusEnum.PAY.getNo()))) {
                    //判断订单是否是到店付--到店付不需要判断支付状态
                    log.error("自动发单,订单状态和支付状态不满足,发单失败,sendToSupplier param: {}", request);
                    throw new SysException(ErrorCodeEnum.AUTOMATIC_ISSUE_ORDER_STATUS_AND_PAYMENT_STATUS_ARE_NOT_SATISFIED_ISSUE_FAILURE);
                }
            }

            // 创建FTL数据模型
            Map<String, Object> dataModel = new HashMap<>();


            /*
             * 邮件内容相关信息
             */

            //logo图片Url
            String logoUrl = null;
            //印章图片Url
            String sealUrl = null;

            //VCC接收邮箱
            String vccEmail = supplierAutoChannelDto.getVccEmail();


            //接收人名称
            String recipientName = supplierAutoChannelDto.getRecipientName();
            //公司名称
            String companyName = supplierAutoChannelDto.getCompanyName();


            String token = "";
            boolean isExist = RedisTemplateX.hExists(RedisKey.EMAIL_ORDER_TOKEN, String.valueOf(request.getSupplyOrderId()));
            //存在直接取值
            if (isExist) {
                token = (String) RedisTemplateX.hGet(RedisKey.EMAIL_ORDER_TOKEN, String.valueOf(request.getSupplyOrderId()));
            } else {
                //不存在，则生成token数据
                token = generateToken();
                RedisTemplateX.hPut(RedisKey.EMAIL_ORDER_TOKEN, String.valueOf(request.getSupplyOrderId()), token);
            }

            dataModel.put("vccEmail", vccEmail);
            dataModel.put("recipientName", recipientName);
            dataModel.put("companyName", companyName);
            dataModel.put("emailSendingType", emailSendingType);
            dataModel.put("payMethod", orderPO.getPayMethod());


            //文件列表
            List<FileAttachDTO> fileAttachDTOList = supplierAutoChannelDto.getFileAttachDTOList();
            if (null != fileAttachDTOList && fileAttachDTOList.size() > 0) {
                for (FileAttachDTO fileAttachDTO : fileAttachDTOList) {
                    if (fileAttachDTO.getFieldName().equals("logo")) {
                        logoUrl = fileAttachDTO.getFileUrl();
                        RedisTemplateX.hPut(RedisKey.EMAIL_ORDER_LOGO_URL, supplyOrderPO.getSupplierCode(), logoUrl);
                    } else if (fileAttachDTO.getFieldName().equals("seal")) {
                        sealUrl = fileAttachDTO.getFileUrl();
                        RedisTemplateX.hPut(RedisKey.EMAIL_ORDER_SEAL_URL, supplyOrderPO.getSupplierCode(), sealUrl);
                    }
                }
            } else {
                //此场景，确认单才存在
                logoUrl = (String) RedisTemplateX.hGet(RedisKey.EMAIL_ORDER_LOGO_URL, supplyOrderPO.getSupplierCode());
                sealUrl = (String) RedisTemplateX.hGet(RedisKey.EMAIL_ORDER_SEAL_URL, supplyOrderPO.getSupplierCode());
                //删除缓存
                RedisTemplateX.hDelete(RedisKey.EMAIL_ORDER_LOGO_URL, supplyOrderPO.getSupplierCode());
                RedisTemplateX.hDelete(RedisKey.EMAIL_ORDER_SEAL_URL, supplyOrderPO.getSupplierCode());
            }

            dataModel.put("logoUrl", logoUrl);
            dataModel.put("sealUrl", sealUrl);


            /*
             * 订单相关
             */

            //供货单
            String supplyOrderCode = supplyOrder.getSupplyOrderCode();
            //邮件发送日期
            String sendDate = DateUtilX.dateToString(new Date());
            //邮件发送时间
            String sendTime = DateUtilX.dateToString(new Date(), only_hour_format);
            //发送方邮箱
            String senderEmail = emailConfigProperties.getUsername();
            //酒店名称
            String hotelName = supplyOrder.getHotelName();
            //产品相关
            List<SupplyProductPreviewDTO> productList = supplyOrder.getProductList();
            SupplyProductPreviewDTO supplyProductPreviewDTO = productList.get(0);

            //入住时间
            String startDate = supplyProductPreviewDTO.getStartDate();
            //离店时间
            String endDate = supplyProductPreviewDTO.getEndDate();
            //间数
            Integer roomQty = supplyProductPreviewDTO.getRoomQty();
            //晚数
            BigDecimal nightQty = supplyProductPreviewDTO.getNightQty();


            dataModel.put("supplyOrderCode", supplyOrderCode);
            dataModel.put("sendDate", sendDate);
            dataModel.put("sendTime", sendTime);
            dataModel.put("hotelName", hotelName);
            dataModel.put("startDate", startDate);
            dataModel.put("endDate", endDate);
            dataModel.put("roomQty", roomQty);
            dataModel.put("nightQty", nightQty);
            dataModel.put("senderEmail", senderEmail);


            //处理预订邮箱
            String preorderEmail = "";

            //是否带附件
            Integer isAttachment = 0;

            //邮件备注
            String remarks = request.getRemark();

            //remarks为空字符或者空 则取配置信息
            if (StringUtils.isEmpty(remarks)) {
                remarks = supplierAutoChannelDto.getRemarks();
            }

            //按订单查询入住人信息
            List<GuestPO> guestPOList = queryOrderRoomGuest(orderPO.getId());

            //入住人按roomNumber 分组
            Map<Integer, List<GuestPO>> groupedByRoomNumber = guestPOList.stream()
                    .collect(Collectors.groupingBy(GuestPO::getRoomNumber));


            //供货单的房间号
            List<String> roomNumberList = StrUtilX.stringToList(supplyOrderPO.getRoomNumbers(), ",");

            // 将房间号列表转换为 Set，以便进行快速查找
            Set<Integer> validRoomNumbers = roomNumberList.stream()
                    .map(String::trim) // 去除空格
                    .map(Integer::valueOf) // 转换为 Integer
                    .collect(Collectors.toSet());

            // 过滤不属于房间的入住人
            Map<Integer, List<GuestPO>> filteredGroupedByRoomNumber = groupedByRoomNumber.entrySet().stream()
                    .filter(entry -> validRoomNumbers.contains(entry.getKey())) // 只保留有效的房间号
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

            List<String> roomGuestStrList = new ArrayList<>();

            // 输出结果
            filteredGroupedByRoomNumber.forEach((roomNumber, guests) -> {
                String guestInfo = guests.stream()
                        .map(guest -> {
                            String membershipInfo = StringUtils.isNotEmpty(guest.getMembershipCardNumber())
                                    ? " (Membership: " + guest.getMembershipCardNumber() + ")"
                                    : ""; // 如果会员卡号不为 null，拼接；否则为空字符串
                            return guest.getName() + membershipInfo; // 拼接名字和可能的会员卡号信息
                        })
                        .collect(Collectors.joining("、")); // 使用逗号拼接所有拼接后的信息
                roomGuestStrList.add("Room" + roomNumber + ": " + guestInfo);
            });

            // 获取vcc账号信息参数
            VccPayConfigReqDTO vccPayConfigReqDTO = new VccPayConfigReqDTO();
            vccPayConfigReqDTO.setSupplyClass(SupplyClassEnum.ZIQIAN.getCode());// 自签供应商
            if (orderPO.getPayMethod() != null) {
                if (Objects.equals(PayMethodEnum.PREPAY.key, orderPO.getPayMethod())) {
                    vccPayConfigReqDTO.setPayMethod(2);
                } else if (Objects.equals(PayMethodEnum.PAY.key, orderPO.getPayMethod())) {
                    vccPayConfigReqDTO.setPayMethod(1);
                }
            }
            vccPayConfigReqDTO.setAgentCode(orderPO.getAgentCode());
            vccPayConfigReqDTO.setSupplyCode(supplyOrderPO.getSupplierCode());
            // vcc账号信息
            VccPayConfigDTO vccPayConfigDTO = null;

            //自动发单
            if (request.isAutoSendOrder()) {
                //自动发单直接取值配置信息
                remarks = supplierAutoChannelDto.getRemarks();
                preorderEmail = supplierAutoChannelDto.getPreorderEmail();
                isAttachment = 1;

                supplyOrderPO.setSendingStatus(SendingStatusEnum.SEND_RESERVATION.key); //已发预定单
                if (emailSendingType == EmailSendingTypeEnum.CANCELLATION.key) {
                    supplyOrderPO.setSendingStatus(SendingStatusEnum.SEND_CANCEL.key); //已发取消单
                }
                supplyOrderPO.setOrderMethod(OrderMethodEnum.ORDER_METHOD_ENUM_2.key); //电邮
                supplyOrderPO.setPreorderEmail(preorderEmail); //邮箱
                supplyOrderPO.setIsAttachment(isAttachment); //自动发单不带附件

            } else {
                //手动发单场景

                //预定单
                if (emailSendingType.equals(EmailSendingTypeEnum.BOOKING_ORDER.key)) {
                    //前端传入
                    preorderEmail = request.getPreorderEmail();
                    isAttachment = request.getIsAttachment();

                    //判断已发预定单，则写入重发预定单
                    if (Objects.equals(supplyOrderPO.getSendingStatus(), SendingStatusEnum.SEND_RESERVATION.key)) {
                        supplyOrderPO.setSendingStatus(SendingStatusEnum.SEND_RESERVATION_AGAIN.key); //重发预定单
                    } else if (Objects.equals(supplyOrderPO.getSendingStatus(), SendingStatusEnum.UNSEND.key)) {
                        //判断未发单，则写入已发预定单
                        supplyOrderPO.setSendingStatus(SendingStatusEnum.SEND_RESERVATION.key); //已发预定单
                    } else if (Objects.equals(supplyOrderPO.getSendingStatus(), SendingStatusEnum.SEND_CANCEL.key)) {
                        //判断未发单，则写入已发预定单
                        supplyOrderPO.setSendingStatus(SendingStatusEnum.SEND_RESERVATION_AGAIN.key); //已发预定单
                    }

                    supplyOrderPO.setOrderMethod(OrderMethodEnum.ORDER_METHOD_ENUM_2.key); //电邮
                    supplyOrderPO.setPreorderEmail(preorderEmail); //邮箱
                    supplyOrderPO.setIsAttachment(isAttachment); //是否带附件
                    //取消单
                } else if (emailSendingType.equals(EmailSendingTypeEnum.CANCELLATION.key)) {
                    //前端传入
                    preorderEmail = request.getPreorderEmail();
                    isAttachment = request.getIsAttachment();

                    supplyOrderPO.setSendingStatus(SendingStatusEnum.SEND_CANCEL.key); //已发取消单
                    supplyOrderPO.setOrderMethod(OrderMethodEnum.ORDER_METHOD_ENUM_2.key);  //电邮
                    supplyOrderPO.setPreorderEmail(preorderEmail); //邮箱
                    supplyOrderPO.setIsAttachment(isAttachment); //是否带附件
                    //确认单--直接查询预定单的邮箱
                } else if (emailSendingType.equals(EmailSendingTypeEnum.CONFIRMATION.key)) {
                    // 判断vcc支付，如果为1，则取vcc邮箱
                    if (supplierAutoChannelDto.getVccPay() != null && supplierAutoChannelDto.getVccPay() == 1) {
                        preorderEmail = supplierAutoChannelDto.getVccEmail();

                        // 获取vcc账号配置
                        vccPayConfigDTO = getPayConfig(vccPayConfigReqDTO);
                        if (vccPayConfigDTO == null) {
                            // 手动操作不记录
                            if (request.getManual() == null || request.getManual() != 1) {
                                // 异步记录日志、标识失败
                                CompletableFuture.runAsync(() -> {
                                    orderCommonService.saveOrderLog(
                                            supplyOrderPO.getOrderId(),
                                            request.getOperator(),
                                            null,
                                            supplyOrderPO.getSupplyOrderCode(),
                                            "发通知支付邮件给供应商（供货单：" + supplyOrderPO.getSupplyOrderCode() + "，发送方式：电邮），VCC付款配置不存在导致通知支付邮箱发送异常", 4
                                    );
                                    // 预付需要标识生卡失败
                                    if (orderPO.getPayMethod() != null && Objects.equals(PayMethodEnum.PREPAY.key, orderPO.getPayMethod())) {
                                        supplyOrderPO.setVccSendStatus(2);
                                        supplyOrderMapper.updateByPrimaryKey(supplyOrderPO);
                                    }
                                });
                            }
                            throw new SysException(ErrorCodeEnum.VCC_PAY_ACCOUNT_INFO_NOT_EXIST);
                        }

                        // 动态VCC需要生成卡
                        if (ModeOfPayEnum.DYNAMICVCC.getCode().equals(vccPayConfigDTO.getModeOfPay())) {
                            // 构建生成vcc卡参数
                            VCCInfoRequestDTO vccInfoRequestDTO = buildVccInfoRequestDTO(supplyOrderPO, filteredGroupedByRoomNumber, vccPayConfigDTO);
                            // 生成虚拟卡
                            ResultX<VCCInfoResponseDTO> vccInfoResponseDTOResultX = virtualCardService.generateVirtualCard(vccInfoRequestDTO);
                            if (vccInfoResponseDTOResultX.isError() || vccInfoResponseDTOResultX.getData() == null) {
                                // 手动操作不记录
                                if (request.getManual() == null || request.getManual() != 1) {
                                    // 异步记录日志、标识失败
                                    CompletableFuture.runAsync(() -> {
                                        orderCommonService.saveOrderLog(
                                                supplyOrderPO.getOrderId(),
                                                request.getOperator(),
                                                null,
                                                supplyOrderPO.getSupplyOrderCode(),
                                                "发通知支付邮件给供应商（供货单：" + supplyOrderPO.getSupplyOrderCode() + "，发送方式：电邮），VCC生成失败导致通知支付邮箱发送异常", 4
                                        );
                                        // 预付需要标识生卡失败
                                        if (orderPO.getPayMethod() != null && Objects.equals(PayMethodEnum.PREPAY.key, orderPO.getPayMethod())) {
                                            supplyOrderPO.setVccSendStatus(2);
                                            supplyOrderMapper.updateByPrimaryKey(supplyOrderPO);
                                        }
                                    });
                                }
                                throw new SysException(ErrorCodeEnum.GENERATE_VIRTUAL_CARD_FAIL);
                            }
                            String vccCardType = vccInfoResponseDTOResultX.getData().getVccCardType();
                            dataModel.put("vccImageUrl", settingsConstant.getMastercardImageUrl());
                            if (StrUtilX.isNotEmpty(vccCardType)) {
                                // 判断vcc卡类型
                                switch (vccCardType) {
                                    case "0": // mastercard(CA)
                                        dataModel.put("vccImageUrl", settingsConstant.getMastercardImageUrl());
                                        break;
                                    case "1": // visa(VI)
                                        dataModel.put("vccImageUrl", settingsConstant.getVisaImageUrl());
                                        break;
                                    case "2": // amex(AX)
                                        dataModel.put("vccImageUrl", settingsConstant.getAmexImageUrl());
                                        break;
                                }
                            }
                            dataModel.put("cardNumber", vccInfoResponseDTOResultX.getData().getVccCardNumber());// VCC卡号
                            dataModel.put("cardValid", vccInfoResponseDTOResultX.getData().getCardExpiryDate().substring(0, 2) + "/"
                                    + vccInfoResponseDTOResultX.getData().getCardExpiryDate().substring(vccInfoResponseDTOResultX.getData().getCardExpiryDate().length() - 2));// VCC卡号有效期
                            dataModel.put("payCvv", vccInfoResponseDTOResultX.getData().getCardVerificationValue());// VCC码
                            supplyOrderPO.setVccSendStatus(1);// 标识已发VCC卡
                        } else {
                            dataModel.put("vccImageUrl", settingsConstant.getMastercardImageUrl());
                            if (vccPayConfigDTO.getCardType() != null) {
                                // 判断vcc卡类型
                                if (vccPayConfigDTO.getCardType() == 0) {// mastercard(CA)
                                    dataModel.put("vccImageUrl", settingsConstant.getMastercardImageUrl());
                                } else if (vccPayConfigDTO.getCardType() == 1) {// visa(VI)
                                    dataModel.put("vccImageUrl", settingsConstant.getVisaImageUrl());
                                } else if (vccPayConfigDTO.getCardType() == 2) {// amex(AX)
                                    dataModel.put("vccImageUrl", settingsConstant.getAmexImageUrl());
                                }
                            }
                            dataModel.put("cardNumber", vccPayConfigDTO.getCardNumber());// VCC卡号
                            dataModel.put("cardValid", vccPayConfigDTO.getActiveDate().substring(0, 2) + "/"
                                    + vccPayConfigDTO.getActiveDate().substring(vccPayConfigDTO.getActiveDate().length() - 2));// VCC卡号有效期
                            dataModel.put("payCvv", vccPayConfigDTO.getCardCode());// VCC码
                        }
                    }
                    isAttachment = supplyOrderPO.getIsAttachment();
                }
            }

            //发送邮箱备注
            dataModel.put("remarks", remarks);

            //处理邮箱
            dataModel.put("preorderEmail", preorderEmail);

            // 获取订单详情
            OrderDTO orderDTO = queryOrderDetail(orderPO.getOrderCode());

            //床型手动描述信息
            String specialRequest = orderDTO.getSpecialRequest();
            //床型组合数据
            String bedType = orderDTO.getBedType();

            //床型组合数据生成床描述
            String bedDescription = BedInfoUtils.generateBedDescription(bedType, LanguageTypeEnum.en_US.getValue());

            // 根据 specialRequest 判断是否拼接
            String finalResult;
            if (specialRequest != null && !specialRequest.trim().isEmpty()) {
                if (null != bedDescription) {
                    finalResult = bedDescription + "," + specialRequest; // 拼接床描述和特殊请求
                } else {
                    finalResult = specialRequest;
                }
            } else {
                finalResult = bedDescription; // 仅返回床描述
            }

            //到店付产品
            if (orderPO.getPayMethod() == 1) {
                //担保条款
                OrderRestrictDTO orderRestrictDTO = orderDTO.getOrderRestrictDTO();

                //担保状态
                String guaranteeStatus = null;
                //担保内容
                String guaranteeText = null;

                Integer guaranteeFlag = orderDTO.getGuaranteeFlag();
                if (null == guaranteeFlag || guaranteeFlag == 0) {
                    guaranteeStatus = "Unguaranteed";
                } else if (guaranteeFlag == 1) {
                    guaranteeStatus = "Guarantee";
                }

                if (null != orderRestrictDTO) {
                    Integer guaranteeType = orderRestrictDTO.getGuaranteeType();
                    if (null == guaranteeType || guaranteeType == 0) {
                        guaranteeText = "No guarantee clauses";
                    } else if (guaranteeType == 1) {
                        guaranteeText = "The time to the store is later than " + orderRestrictDTO.getGuaranteeCondition().substring(0, 2) + ":" + orderRestrictDTO.getGuaranteeCondition().substring(2, 4);

                        if (orderRestrictDTO.getGuaranteeFeeType() == 1) {
                            guaranteeText = guaranteeText + " The full housing price of the booking order must be guaranteed";
                        }
                        if (orderRestrictDTO.getGuaranteeFeeType() == 2) {
                            guaranteeText = guaranteeText + " The first evening house price of the booking order must be guaranteed";
                        }

                    } else if (guaranteeType == 2) {
                        guaranteeText = "Number of reservation rooms greater than" + orderRestrictDTO.getGuaranteeCondition() + "between，";

                        if (orderRestrictDTO.getGuaranteeFeeType() == 1) {
                            guaranteeText = guaranteeText + " The full housing price of the booking order must be guaranteed";
                        }
                        if (orderRestrictDTO.getGuaranteeFeeType() == 2) {
                            guaranteeText = guaranteeText + " The first evening house price of the booking order must be guaranteed";
                        }

                    } else if (guaranteeType == 3) {
                        if (orderRestrictDTO.getGuaranteeFeeType() == 1) {
                            guaranteeText = "Booking must guarantee the full housing price of the booking order";
                        }
                        if (orderRestrictDTO.getGuaranteeFeeType() == 2) {
                            guaranteeText = " The first night house price of the booking order must be guaranteed";
                        }
                    }
                }

                dataModel.put("guaranteeStatus", guaranteeStatus);// 担保条款状态
                dataModel.put("guaranteeText", guaranteeText);// 担保条款内容

                // 判断vcc支付
                if (supplierAutoChannelDto.getVccPay() != null && supplierAutoChannelDto.getVccPay() == 1) {
                    // 配置为空再去获取
                    if (vccPayConfigDTO == null) {
                        // 获取vcc账号配置
                        vccPayConfigDTO = getPayConfig(vccPayConfigReqDTO);
                        if (vccPayConfigDTO == null) {
                            orderCommonService.saveOrderLog(
                                    supplyOrderPO.getOrderId(),
                                    request.getOperator(),
                                    null,
                                    supplyOrderPO.getSupplyOrderCode(),
                                    "发送邮件给供应商异常（供货单：" + supplyOrderPO.getSupplyOrderCode() + "，发送方式：电邮）：VCC付款配置不存在", 4
                            );
                            throw new SysException(ErrorCodeEnum.VCC_PAY_ACCOUNT_INFO_NOT_EXIST);
                        }
                    }
                    // 动态VCC需要生成卡
                    if (ModeOfPayEnum.DYNAMICVCC.getCode().equals(vccPayConfigDTO.getModeOfPay())) {
                        // 构建生成vcc卡参数
                        VCCInfoRequestDTO vccInfoRequestDTO = buildVccInfoRequestDTO(supplyOrderPO, filteredGroupedByRoomNumber, vccPayConfigDTO);
                        // 生成虚拟卡
                        ResultX<VCCInfoResponseDTO> vccInfoResponseDTOResultX = virtualCardService.generateVirtualCard(vccInfoRequestDTO);
                        if (vccInfoResponseDTOResultX.isError() || vccInfoResponseDTOResultX.getData() == null) {
                            // 手动操作不记录
                            if (request.getManual() == null || request.getManual() != 1) {
                                // 异步记录日志、标识失败
                                CompletableFuture.runAsync(() -> {
                                    orderCommonService.saveOrderLog(
                                            supplyOrderPO.getOrderId(),
                                            request.getOperator(),
                                            null,
                                            supplyOrderPO.getSupplyOrderCode(),
                                            "发送邮件给供应商异常（供货单：" + supplyOrderPO.getSupplyOrderCode() + "，发送方式：电邮）：生成VCC虚拟卡失败", 4
                                    );
                                    // 预付需要标识生卡失败
                                    if (orderPO.getPayMethod() != null && Objects.equals(PayMethodEnum.PREPAY.key, orderPO.getPayMethod())) {
                                        supplyOrderPO.setVccSendStatus(2);
                                        supplyOrderMapper.updateByPrimaryKey(supplyOrderPO);
                                    }
                                });
                            }
                            throw new SysException(ErrorCodeEnum.GENERATE_VIRTUAL_CARD_FAIL);
                        }
                        dataModel.put("payCardNumber", vccInfoResponseDTOResultX.getData().getVccCardNumber());// VCC卡号
                        if (StrUtilX.isNotEmpty(vccInfoResponseDTOResultX.getData().getCardExpiryDate())) {
                            dataModel.put("payCardValid", vccInfoResponseDTOResultX.getData().getCardExpiryDate().substring(0, 2) + "/"
                                    + vccInfoResponseDTOResultX.getData().getCardExpiryDate().substring(vccInfoResponseDTOResultX.getData().getCardExpiryDate().length() - 2));// VCC卡号有效期
                        }
                        dataModel.put("payCvv", vccInfoResponseDTOResultX.getData().getCardVerificationValue());// VCC码
                        supplyOrderPO.setVccSendStatus(1);// 标识已发VCC卡
                    } else {
                        dataModel.put("payCardNumber", vccPayConfigDTO.getCardNumber());// VCC卡号
                        dataModel.put("payCardValid", vccPayConfigDTO.getActiveDate().substring(0, 2) + "/" + vccPayConfigDTO.getActiveDate().substring(vccPayConfigDTO.getActiveDate().length() - 2));// VCC卡号有效期
                        dataModel.put("payCvv", vccPayConfigDTO.getCardCode());// VCC码
                    }
                }
            }


            /*
             * 取消条款
             */
            Map<Object, Object> cancelLists = getCancelList(orderDTO, request.getSupplyOrderId(), supplyOrderPO);
            //确认号
            String confirmationCode = (String) cancelLists.get("confirmationCode");

            List<String> cancelList = (List<String>) cancelLists.get("cancelList");

            String contactName = orderDTO.getContactName();
            String clientName = orderDTO.getAgentName();
            String contactPhone = orderDTO.getContactPhone();
            SupplyOrderDTO supplyOrderDTO = orderDTO.getSupplyOrderList().stream()
                    .filter(s -> s.getSupplyOrderId().equals(supplyOrderPO.getId()))
                    .findFirst()
                    .orElse(null);
            SupplyProductDTO supplyProductDTO = supplyOrderDTO.getProductList().get(0);

            String roomName = supplyProductDTO.getRoomName();
            Integer breakfastQty = supplyProductDTO.getBreakfastQty();
            //早餐描述
            String breakfastQtyDesc = BreakFastEnum.getEnDescByKey(breakfastQty);

            String productName = supplyProductDTO.getProductName();

            List<OrderFeeDTO> feeList = supplyOrderDTO.getFeeList(); // 假设 supplyOrderDTO 已定义并初始化

            // 使用 Map 按照 saleDate 分组 priceList
            Map<String, List<OrderFeeDetailDTO>> groupedBySaleDate = feeList.stream()
                    .flatMap(orderFeeDTO -> orderFeeDTO.getPriceList().stream()) // 提取所有 priceList 的元素
                    .collect(groupingBy(OrderFeeDetailDTO::getSaleDate)); // 按 saleDate 分组

            //到店支付费用
            BigDecimal totalPayAtHotelFee = feeList.stream()
                    .map(OrderFeeDTO::getPayAtHotelFee) // 提取 payAtHotelFee
                    .filter(Objects::nonNull) // 过滤掉 null 值
                    .reduce(BigDecimal.ZERO, BigDecimal::add); // 汇总

            //到店支付币种
            String payAtHotelCurrency = feeList.stream()
                    .map(OrderFeeDTO::getPayAtHotelCurrency) // 提取 payAtHotelCurrency
                    .filter(Objects::nonNull) // 过滤掉 null 值
                    .map(String::valueOf) // 将 Integer 转换为 String
                    .findFirst() // 找到第一个非 null 的值
                    .orElse(null); // 如果没有，返回 null

            // 判断 totalPayAtHotelFee 是否为零
            String totalPayAtHotelFeeStr = totalPayAtHotelFee.compareTo(BigDecimal.ZERO) == 0
                    ? "--" // 如果是零，则赋值为空字符串
                    : totalPayAtHotelFee.toString(); // 否则，将其转换为字符串


            // 根据 payAtHotelCurrency 获取 payCurrency
            // payAtHotelCurrency不为空，并且totalPayAtHotelFeeStr有数据的情况下，才能获取到币种，否则直接返回空
            String payCurrency = (payAtHotelCurrency != null) && !(totalPayAtHotelFeeStr.equals("--"))
                    ? SettlementCurrencyEnum.getCodeByKey(payAtHotelCurrency)
                    : ""; // 如果 payAtHotelCurrency 为 null, payCurrency 也为 null


            // 创建一个新的 Map 来存储汇总结果
            Map<String, BigDecimal> salePriceMap = new HashMap<>();

            // 将分组结果汇总到新的 Map 中
            groupedBySaleDate.forEach((date, orders) -> {
                // 计算该日期下所有销售价格的总和
                BigDecimal total = orders.stream()
                        .map(OrderFeeDetailDTO::getSalePrice)
                        .reduce(BigDecimal.ZERO, BigDecimal::add); // 汇总价格
                salePriceMap.put(date, total); // 将总和放入 resultMap
            });

            //获取商家币种
            OrgDTO orgDTO = CommonInitializer.getOrgInfo();
            String orgCurrency = SettlementCurrencyEnum.getCodeByKey(orgDTO.getOrgCurrency());

            // 设置 FreeMarker 配置
            Configuration cfg = new Configuration();
            cfg.setClassForTemplateLoading(SupplyOrderServer.class, "/templates"); // 模板位置
            cfg.setDefaultEncoding("UTF-8");

            try {
                // 为空不发送
                if (StrUtilX.isNotEmpty(preorderEmail)) {
                    //预订邮件确认
                    String confirmUrl = settingsConstant.getTiangongUrl() + "/emailConfirmation?token=" + token + "&supplyOrderId=" + request.getSupplyOrderId();
                    //预订邮件取消
                    String cancelUrl = settingsConstant.getTiangongUrl() + "/rejectionEmail?token=" + token + "&supplyOrderId=" +
                            request.getSupplyOrderId() + "&productId=" + orderDTO.getProductId() + "&startDate=" + startDate + "&endDate=" + endDate + "&channelCode=B2B";
                    //同意邮件取消
                    String agreeCancelUrl = settingsConstant.getTiangongUrl() + "/cancelEmail?token=" + token + "&supplyOrderId=" + request.getSupplyOrderId();


                    dataModel.put("confirmUrl", confirmUrl);  //订单确认
                    dataModel.put("cancelUrl", cancelUrl);    //订单拒绝
                    dataModel.put("agreeCancelUrl", agreeCancelUrl); //同意取消
                    dataModel.put("orgCurrency", orgCurrency);   //商家币种
                    dataModel.put("supplyCurrency", SettlementCurrencyEnum.getCodeByKey(String.valueOf(supplyOrderDTO.getBaseCurrency())));   //商家币种
                    dataModel.put("payCurrency", payCurrency);   //商家币种


                    // 加载 FTL 模板
                    Template template = cfg.getTemplate("emailOrder.ftl");

                    // 创建数据模型
                    dataModel.put("contactName", contactName);
                    dataModel.put("clientName", clientName);
                    dataModel.put("contactPhone", contactPhone);
                    dataModel.put("username", emailConfigProperties.getUsername());
                    dataModel.put("roomName", roomName);
                    dataModel.put("breakfastQty", breakfastQty);
                    dataModel.put("breakfastQtyDesc", breakfastQtyDesc);

                    dataModel.put("productName", productName);
                    dataModel.put("groupedBySaleDate", groupedBySaleDate);
                    dataModel.put("salePriceMap", salePriceMap);
                    dataModel.put("roomGuestStrList", roomGuestStrList);
                    dataModel.put("totalPayAtHotelFee", totalPayAtHotelFeeStr);
                    dataModel.put("specialRequest", finalResult);
                    dataModel.put("remarks", remarks);
                    dataModel.put("order", orderDTO);
                    dataModel.put("cancelList", cancelList);
                    dataModel.put("confirmationCode", confirmationCode);

                    if(preorderEmail.contains(",")){
                        String[] emails = preorderEmail.split(",");
                        for(String email : emails){
                            dataModel.put("preorderEmail", email);
                            // 将模板和数据模型结合，生成 HTML 内容
                            StringWriter writer = new StringWriter();
                            template.process(dataModel, writer);
                            String htmlContent = writer.toString(); // 生成的 HTML 内容
                            // 邮件标题
                            String topic = getTopic(emailSendingType, hotelName, supplyOrderCode);

                            SendEmailReq sendEmailReq = new SendEmailReq();
                            sendEmailReq.setEmail(email);
                            sendEmailReq.setType(1);
                            sendEmailReq.setTopic(topic);
                            sendEmailReq.setContent(htmlContent);
                            sendCodeUtil.sendEmail(sendEmailReq, isAttachment);
                        }
                    }else {
                        dataModel.put("preorderEmail", preorderEmail);
                        // 将模板和数据模型结合，生成 HTML 内容
                        StringWriter writer = new StringWriter();
                        template.process(dataModel, writer);
                        String htmlContent = writer.toString(); // 生成的 HTML 内容
                        // 邮件标题
                        String topic = getTopic(emailSendingType, hotelName, supplyOrderCode);

                        SendEmailReq sendEmailReq = new SendEmailReq();
                        sendEmailReq.setEmail(preorderEmail);
                        sendEmailReq.setType(1);
                        sendEmailReq.setTopic(topic);
                        sendEmailReq.setContent(htmlContent);
                        sendCodeUtil.sendEmail(sendEmailReq, isAttachment);
                    }
                }

                // 判断是否是手动vcc支付通知
                if (emailSendingType.equals(EmailSendingTypeEnum.CONFIRMATION.key) && request.getManual() != null && request.getManual() == 1) {
                    // 记录日志
                    orderCommonService.saveOrderLog(
                            supplyOrderPO.getOrderId(),
                            request.getOperator(),
                            null,
                            supplyOrderPO.getSupplyOrderCode(),
                            "发通知支付邮件给供应商（供货单：" + supplyOrderPO.getSupplyOrderCode()
                                    + "，发送方式：电邮）", 4
                    );
                } else {
                    // 记录日志
                    orderCommonService.saveOrderLog(
                            supplyOrderPO.getOrderId(),
                            request.getOperator(),
                            null,
                            supplyOrderPO.getSupplyOrderCode(),
                            "发邮件给供应商（供货单：" + supplyOrderPO.getSupplyOrderCode()
                                    + "，发送方式：电邮，发单类型：" + EmailSendingTypeEnum.getValueByKey(emailSendingType) + "）", 4
                    );
                }

                //更新供货单结果
                supplyOrderMapper.updateByPrimaryKey(supplyOrderPO);

                return Response.success();
            } catch (Exception e) {
                log.error("邮件模版生成异常：", e);
                return Response.error(ErrorCodeEnum.EMAIL_TEMPLATE_GENERATE_ERROR.errorCode, ErrorCodeEnum.EMAIL_TEMPLATE_GENERATE_ERROR.errorDesc);
            }
        } catch (SysException e) {
            log.error("邮件发单失败：", e);
            return Response.error(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("邮件发单异常：", e);
            return Response.error(ErrorCodeEnum.EMAIL_SEND_ERROR_CODE.errorCode, ErrorCodeEnum.EMAIL_SEND_ERROR_CODE.errorDesc);
        }
    }

    /**
     * 邮件标题
     *
     * @param emailSendingType 邮件发送类型
     * @param hotelName        酒店名称
     * @return 返回邮件标题
     */
    private String getTopic(Integer emailSendingType, String hotelName, String supplyOrderCode) {
        String emailTitle = "";
        if (emailSendingType.equals(EmailSendingTypeEnum.BOOKING_ORDER.key)) {
            emailTitle = "Booking Email";
        } else if (emailSendingType.equals(EmailSendingTypeEnum.CONFIRMATION.key)) {
            emailTitle = "Payment Notification Email";
        } else if (emailSendingType.equals(EmailSendingTypeEnum.CANCELLATION.key)) {
            emailTitle = "Cancel Email";
        }
        //发送邮件
        return "【AMT】" + supplyOrderCode + "（" + hotelName + "）" + emailTitle;
    }

    /**
     * 构建生成vcc卡参数
     */
    private VCCInfoRequestDTO buildVccInfoRequestDTO(SupplyOrderPO supplyOrderPO, Map<Integer, List<GuestPO>> filteredGroupedByRoomNumber, VccPayConfigDTO vccPayConfigDTO) {
        // 获取付款主体币种
        PaySubjectReqDTO paySubjectReqDTO = new PaySubjectReqDTO();
        paySubjectReqDTO.setPaySubjectId(vccPayConfigDTO.getPayAccountId());
        paySubjectReqDTO.setModeOfPay(vccPayConfigDTO.getModeOfPay());
        ResultX<List<PaySubjectDTO>> listResultX = virtualCardService.queryPaySubjectList(paySubjectReqDTO);
        if (listResultX.isError()) {
            throw new SysException(ErrorCodeEnum.QUERY_PAY_SUBJECT_FAIL);
        }
        if (CollUtilX.isEmpty(listResultX.getData())) {
            throw new SysException(ErrorCodeEnum.THE_PAYER_DOES_NOT_EXIST);
        }
        PaySubjectDTO paySubjectDTO = listResultX.getData().get(0);
        // 供货单币种
        String originalCurrency = SettlementCurrencyEnum.getValueByKey(String.valueOf(supplyOrderPO.getBaseCurrency()));
        // 付款主体币种 CNY
        String targetCurrency = paySubjectDTO.getCurrencyCode();
        // 总金额
        BigDecimal totalPrice = supplyOrderPO.getBasePrice();

        // 只有在币种不同的情况下才存在汇率
        if (!originalCurrency.equals(targetCurrency)) {
            // 1. 获取直接汇率
            BigDecimal exchangeRate = exchangeRateRemote.getRateToTargetCurrency(supplyOrderPO.getBaseCurrency(), CompanyDTO.COMPANY_CODE, Integer.parseInt(SettlementCurrencyEnum.getKeyByCode(targetCurrency)));
            if (exchangeRate == null) {
                // 2. 获取直接汇率失败，获取间接汇率
                // 2.1 获取供货单币种和商家币种的汇率
                BigDecimal rateToOrgCurrency = exchangeRateRemote.getRateToOrgCurrency(supplyOrderPO.getBaseCurrency(), CompanyDTO.COMPANY_CODE);
                if (rateToOrgCurrency == null) {
                    log.error("获取供货单币种和商家币种的汇率为空，baseCurrency={}", supplyOrderPO.getBaseCurrency());
                    // 汇率为空则直接结束
                    throw new SysException(ErrorCodeEnum.EXCHANGE_RATE_IS_NOT_ALREADT);
                }
                // 2.3 总金额= 供货单总金额*汇率
                totalPrice = totalPrice.multiply(rateToOrgCurrency);

                // 2.4 获取商家币种和支付主体的汇率
                // 获取商家币种
                OrgDTO orgDTO = CommonInitializer.getOrgInfo();
                BigDecimal rateToTargetCurrency = exchangeRateRemote.getRateToTargetCurrency(Integer.parseInt(orgDTO.getOrgCurrency()), CompanyDTO.COMPANY_CODE, Integer.parseInt(SettlementCurrencyEnum.getKeyByCode(targetCurrency)));
                if (rateToTargetCurrency == null) {
                    log.error("获取商家币种和支付主体的汇率为空，targetCurrency={}", targetCurrency);
                    // 汇率为空则直接结束
                    throw new SysException(ErrorCodeEnum.EXCHANGE_RATE_IS_NOT_ALREADT);
                }
                // 2.5 总金额= 供货单总金额*商家币种汇率*支付主体汇率
                totalPrice = totalPrice.multiply(rateToTargetCurrency);
            } else {
                // 总金额币种准换
                totalPrice = totalPrice.multiply(exchangeRate);
            }
        }

        VCCInfoRequestDTO vccInfoRequestDTO = new VCCInfoRequestDTO();
        vccInfoRequestDTO.setPaySubjectId(vccPayConfigDTO.getPayAccountId());
        vccInfoRequestDTO.setSupplierCode(supplyOrderPO.getSupplierCode());
        vccInfoRequestDTO.setSupplierName(supplyOrderPO.getSupplierName());
        vccInfoRequestDTO.setBusinessCode(supplyOrderPO.getSupplyOrderCode());
        // 交易标题
        vccInfoRequestDTO.setTransactionTitle(supplyOrderPO.getSupplyOrderCode());
        // 交易内容
        vccInfoRequestDTO.setTransactionContent(getTransactionContent(supplyOrderPO, filteredGroupedByRoomNumber));
        // 业务应付金额
        vccInfoRequestDTO.setBusinessPayableAmount(supplyOrderPO.getSupplyOrderAmt());
        // 业务应付币种
        vccInfoRequestDTO.setBusinessPayableCurrency(originalCurrency);
        // 刷卡次数
        vccInfoRequestDTO.setVccCardAvailableCount(settingsConstant.getVccCardAvailableCount());
        // 有效开始日期
        vccInfoRequestDTO.setValidStartDt(DateUtilX.dateToString(supplyOrderPO.getStartDate()));
        // 有效结束日期
        vccInfoRequestDTO.setValidEndDt(DateUtilX.dateToString(supplyOrderPO.getEndDate()));
        vccInfoRequestDTO.setReturnCvv(true);
        // 获取当前时间 并且设置时分秒为0
        Date thisDate = DateUtilX.stringToDate(DateUtilX.dateToString(new Date()));
        // 获取相差天数
        long day = DateUtilX.getDay(thisDate, supplyOrderPO.getStartDate());

        // 供货单金额
        BigDecimal totalAmount = totalPrice;

        // 默认开卡金额= 供货单总金额*0.1 小数点后2位 向上取整
        BigDecimal cardOpeningAmount = totalAmount.multiply(new BigDecimal("0.1")).setScale(2, RoundingMode.CEILING);

        // 计算总充值金额 = 供货单总金额 * 1.1 小数点后2位 向上取整
        totalAmount = totalAmount.multiply(new BigDecimal("1.1")).setScale(2, RoundingMode.CEILING);
        if (day <= 1) {
            // 如果是当天预警当天入住|或者是明天入住 则不需要充值 开卡时直接预存所有金额
            cardOpeningAmount = totalAmount;
        } else {
            // 总金额减去开卡金额 = 充值金额
            BigDecimal rechargeAmount = totalAmount.subtract(cardOpeningAmount);

            // 充值金额
            vccInfoRequestDTO.setRechargeAmount(rechargeAmount);

            // 充值日期 = 入住前一天
            String rechargeDt = DateUtilX.dateToString(DateUtilX.getDate(supplyOrderPO.getStartDate(), -1));
            vccInfoRequestDTO.setRechargeDt(rechargeDt);
        }
        // 开卡金额
        vccInfoRequestDTO.setCardOpeningAmount(cardOpeningAmount);
        return vccInfoRequestDTO;
    }

    /**
     * 获取交易标题
     */
    public static String getTransactionContent(SupplyOrderPO supplyOrderPO, Map<Integer, List<GuestPO>> filteredGroupedByRoomNumber) {
        StringBuilder sb = new StringBuilder();
        for (List<GuestPO> guestPOList : filteredGroupedByRoomNumber.values()) {
            if (CollUtilX.isNotEmpty(guestPOList)) {
                for (GuestPO guest : guestPOList) {
                    sb.append(StrUtil.isEmpty(guest.getName()) ? guest.getLastName() + "/" + guest.getFirstName() : guest.getName()).append(",");
                }
            }
        }
        sb.append(DateUtilX.dateToString(supplyOrderPO.getStartDate())).append("入住,");

        sb.append(DateUtilX.dateToString(supplyOrderPO.getEndDate())).append("离店。");
        return sb.toString();
    }

    @Override
    public Map<Object, Object> getCancelListBySupplyOrderId(Integer supplyOrderId) {

        //按供货单查询供货单信息
        SupplyOrderPO supplyOrderPO = supplyOrderMapper.selectByPrimaryKey(supplyOrderId);

        //查询订单
        OrderPO orderPO = new OrderPO();
        orderPO.setId(supplyOrderPO.getOrderId());
        OrderPO order = orderMapper.selectOne(orderPO);

        OrderDTO orderDTO = queryOrderDetail(order.getOrderCode());

        /*
         * 取消条款
         */
        try {
            return getCancelList(orderDTO, supplyOrderId, supplyOrderPO);
        } catch (Exception e) {
            log.error("获取条件数据异常");
        }
        return null;
    }

    @Override
    public List<OrderCodeInfoDTO> queryOrderCodeBySupplyOrderCode(SupplyOrderIdDTO request) {
        return supplyOrderMapper.selectOrderCodeBySupplyOrderCode(request);
    }

    @Override
    public Response<Map<String, String>> getEmailPreview(SupplyOrderIdDTO req) {
        try {
//            VccPayConfigReqDTO infoReq = new VccPayConfigReqDTO();
//            infoReq.setSupplyClass("ZIQIAN");// 自签供应商
//            infoReq.setSupplyCode(req.getSupplyCode());
//            infoReq.setAgentCode(req.getAgentCode());
//            if (req.getPayMethod() != null) {
//                if (Objects.equals(PayMethodEnum.PREPAY.key, req.getPayMethod())) {
//                    infoReq.setPayMethod(1);
//                } else if (Objects.equals(PayMethodEnum.PAY.key, req.getPayMethod())) {
//                    infoReq.setPayMethod(2);
//                }
//            }
//            ResultX<VccPayConfigDTO> resultX = virtualCardService.queryVccPayConfig(infoReq);
//            if (resultX.isError()) {
//                throw new SysException(resultX.getCode(), resultX.getMsg());
//            }
//            VccPayConfigDTO vccPayConfigDTO = resultX.getData();
//            if (vccPayConfigDTO == null) {
//                return Response.success(null);
//            }
            Map<String, String> resultMap = new HashMap<>();
            //发件邮箱
            resultMap.put("sendEmail", emailConfigProperties.getUsername());
            //邮件发送日期
            String sendDate = DateUtilX.dateToString(new Date());
            resultMap.put("sendDate", sendDate);
            //邮件发送时间
            String sendTime = DateUtilX.dateToString(new Date(), only_hour_format);
            resultMap.put("sendTime", sendTime);
//            resultMap.put("payCardNumber", vccPayConfigDTO.getCardNumber());// VCC卡号
//            resultMap.put("payCardValid", vccPayConfigDTO.getActiveDate());// 有效时间
//            resultMap.put("payCvv", vccPayConfigDTO.getCardCode());// VCC码
            return Response.success(resultMap);
        } catch (SysException e) {
            return Response.error(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("邮件确认/取消预览异常", e);
            return Response.error(ErrorCodeEnum.SYSTEM_EXCEPTION.errorCode, ErrorCodeEnum.SYSTEM_EXCEPTION.errorDesc);
        }
    }

    @Override
    public SupplyOrderDTO querySupplyOrderInfo(SupplyOrderIdDTO request) {
        if (StrUtilX.isEmpty(request.getSupplyOrderCode())) {
            throw new SysException(ParamErrorEnum.SUPPLY_ORDER_ISEMPTY);
        }
        SupplyOrderDTO supplyOrderDTO = supplyOrderMapper.selectSupplyOrderInfo(request);

        if (supplyOrderDTO != null) {
            // 查询入住人
            GuestPO guestPO = new GuestPO();
            guestPO.setOrderId(supplyOrderDTO.getOrderId());
            List<GuestPO> orderGuests = guestMapper.select(guestPO);
            if (StrUtilX.isNotEmpty(supplyOrderDTO.getRoomNumbers()) && CollUtilX.isNotEmpty(orderGuests)) {
                List<String> roomNumbers = StrUtilX.stringToList(supplyOrderDTO.getRoomNumbers(), ",");
                if (CollUtilX.isNotEmpty(roomNumbers)) {
                    List<com.tiangong.order.remote.response.SupplyGuestDTO> collect = orderGuests.stream()
                            .filter(item -> item.getRoomNumber() != null && roomNumbers.contains(item.getRoomNumber().toString())).map(item -> {
                                com.tiangong.order.remote.response.SupplyGuestDTO supplyGuestDTO = new com.tiangong.order.remote.response.SupplyGuestDTO();
                                supplyGuestDTO.setRoomIndex(item.getRoomNumber());
                                supplyGuestDTO.setGuest(item.getName());
                                supplyGuestDTO.setFirstName(item.getFirstName());
                                supplyGuestDTO.setLastName(item.getLastName());
                                return supplyGuestDTO;
                            }).collect(Collectors.toList());
                    supplyOrderDTO.setGuestList(collect);
                }
            }

            // 查询酒店的国家，城市名称
            if(supplyOrderDTO != null) {
                DestinationReq req = new DestinationReq();
                HashSet<Long> hotelIds = new HashSet<>();
                hotelIds.add(Long.valueOf(supplyOrderDTO.getHotelId()));
                req.setHotelIds(hotelIds);
                req.setLanguage(request.getLanguageType());
                // 设置默认为 空串
                supplyOrderDTO.setHotelName("");
                supplyOrderDTO.setCityName("");
                supplyOrderDTO.setCountryName("");
                // 当语言类型为英文
                if(req.getLanguage().equals(LanguageTypeEnum.en_US.getValue())){
                    Response<List<EsHotelDto>> listResponse = hotelRemote.searchDestinationHotel2(req);
                    if(CollUtilX.isNotEmpty(listResponse.getModel())) {
                        EsHotelDto esHotelDto = listResponse.getModel().get(0);
                        // 数据组装，当没有英文的数据也没有相应名字时不进行组装
                        assembleData(supplyOrderDTO, esHotelDto);
                    }
                }else{
                    // 语言类型为中文
                    Response<List<EsHotelDto>> listResponse = hotelRemote.searchDestinationHotel2(req);
                    if(CollUtilX.isNotEmpty(listResponse.getModel())){
                        EsHotelDto esHotelDto = listResponse.getModel().get(0);
                        // 没有中文数据，则去查询英文数据
                        if(null == esHotelDto) {
                            req.setLanguage(LanguageTypeEnum.en_US.getValue());
                            Response<List<EsHotelDto>> listResponseEN = hotelRemote.searchDestinationHotel2(req);
                            if(CollUtilX.isNotEmpty(listResponseEN.getModel())){
                                esHotelDto = listResponseEN.getModel().get(0);
                            }
                        }
                        // 数据组装，当没有英文的数据也没有相应名字时不进行组装
                        assembleData(supplyOrderDTO, esHotelDto);
                    }
                }

            }

            // 查询供货单产品
            SupplyProductPO supplyProductQuery = new SupplyProductPO();
            supplyProductQuery.setSupplyOrderId(supplyOrderDTO.getSupplyOrderId());
            List<SupplyProductPO> supplyProductPOList = supplyProductMapper.select(supplyProductQuery);
            supplyOrderDTO.setProductList(new ArrayList<>());
            for (SupplyProductPO supplyProductPO : supplyProductPOList) {
                SupplyProductDTO supplyProductDTO = ProductSalePriceConvert.INSTANCE.supplyProductDTOConvert(supplyProductPO);
                supplyProductDTO.setSupplyProductId(supplyProductPO.getId());
                supplyProductDTO.setProductId(supplyProductPO.getProductId());
                supplyProductDTO.setStartDate(DateUtilX.dateToString(supplyProductPO.getStartDate()));
                supplyProductDTO.setEndDate(DateUtilX.dateToString(supplyProductPO.getEndDate()));
                supplyProductDTO.setRoomName(supplyProductPO.getRoomName());
                supplyProductDTO.setBasePrice(supplyProductPO.getBasePriceTotalAmt());
                supplyProductDTO.setNightQty((int) DateUtilX.getDay(DateUtilX.dateFormat(supplyProductPO.getStartDate(), DateUtilX.defaultFormat),
                        DateUtilX.dateFormat(supplyProductPO.getEndDate(), DateUtilX.defaultFormat)));
                supplyOrderDTO.getProductList().add(supplyProductDTO);
            }

            // 设置最迟免费取消时间
            SupplyProductPO supplyProductPO = supplyProductPOList.get(0);
            List<Date> cancelDateList = new ArrayList<>();
            ObjectMapper objectMapper = new ObjectMapper();
            try {
                JsonNode arrayNode = objectMapper.readTree(supplyProductPO.getCancellationTerm());
                for(JsonNode node : arrayNode){
                    // 取消条款的类型
                    Integer cancelRestrictionType = node.get("cancelRestrictionType").asInt();

                    if(cancelRestrictionType.equals(2) || cancelRestrictionType.equals(3)){
                        //取消时间
                        String cancelRestrictionDate = node.has("cancelRestrictionDate") ? node.get("cancelRestrictionDate").asText() : null;
                        String cancelRestrictionTime = node.has("cancelRestrictionTime") ? node.get("cancelRestrictionTime").asText() : null;

                        //判断cancelRestrictionTime不为空，则需要截取时间
                        if (null != cancelRestrictionTime && !cancelRestrictionTime.isEmpty()) {
                            //把1800：00 转换为 18:00
                            cancelRestrictionTime = cancelRestrictionTime.substring(0, 2) + ":" + cancelRestrictionTime.substring(2, 4);
                        }
                        String cancelDate = cancelRestrictionDate + " " +cancelRestrictionTime;

                        cancelDateList.add(DateUtilX.stringToDate(cancelDate,"yyyy-MM-dd HH:mm"));
                    }
                }
                Date earliestDate = null;
                for (Date date : cancelDateList) {
                    if (earliestDate == null || date.before(earliestDate)) {
                        earliestDate = date;
                    }
                }
                String date = DateUtilX.dateToString(earliestDate,"yyyy/MM/dd HH:ss");
                supplyOrderDTO.setLatestCancelDate(date);
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }

            //设置供货单的币种名称
            String supplyCurrencyCode = SettlementCurrencyEnum.getCodeByKey(String.valueOf(supplyOrderDTO.getBaseCurrency()));
            supplyOrderDTO.setSupplyCurrencyCode(supplyCurrencyCode);

            //设置订单的币种名称
            String orderCurrencyCode = SettlementCurrencyEnum.getCodeByKey(String.valueOf(supplyOrderDTO.getSaleCurrency()));
            supplyOrderDTO.setOrderCurrencyCode(orderCurrencyCode);

        }
        return supplyOrderDTO;
    }

    /**
     * 组装国家名称，城市名称，酒店名称
     * @param supplyOrderDTO
     * @param esHotelDto
     */
    private void assembleData(SupplyOrderDTO supplyOrderDTO, EsHotelDto esHotelDto) {
        supplyOrderDTO.setHotelName(esHotelDto.getHotelName());
        supplyOrderDTO.setCountryName(esHotelDto.getCountryName());
        supplyOrderDTO.setCityName(esHotelDto.getCityName());
    }

    private Map<Object, Object> getCancelList(OrderDTO orderDTO, Integer supplyOrderId, SupplyOrderPO supplyOrderPO) throws JsonProcessingException {
        SimpleDateFormat sdfTmp = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Map<Object, Object> map = new HashMap<>();

        //取消订单
        BigDecimal supplyOrderTotalAmt = null;
        //到店付直接取【房费已含税/费】
        if (orderDTO.getPayMethod().equals(PayMethodEnum.PAY.key)) {
            supplyOrderTotalAmt = supplyOrderPO.getBasePrice();
        } else {
            //预付直接取【供货单应付】
            supplyOrderTotalAmt = orderDTO.getSupplyOrderTotalAmt();
        }

        List<String> cancelList = new ArrayList<>();
        map.put("bedType",orderDTO.getBedType());
        map.put("specialRequest",orderDTO.getSpecialRequest());
        map.put("supplyOrderTotalAmt", supplyOrderTotalAmt);
        map.put("sendTime", sdfTmp.format(new Date()));


        //取消条款
        String cancellationTerm = null;
        String confirmationCode = "";

        for (SupplyOrderDTO supplyOrderDTO :
                orderDTO.getSupplyOrderList()) {
            if (supplyOrderDTO.getSupplyOrderId().equals(supplyOrderId)) {
                cancellationTerm = supplyOrderDTO.getCancellationTerm();
                confirmationCode = supplyOrderDTO.getConfirmationCode();
                break;
            }
        }
        map.put("confirmationCode", confirmationCode);
        //取消条款
        cancelList = CancellationConvertUtil.convertCancellationToEn(cancellationTerm, true);
        map.put("cancelList", cancelList);
        return map;
    }

    /**
     * 查询订单入住信息
     *
     * @param orderId 订单id
     * @return 入住人信息
     */
    private List<GuestPO> queryOrderRoomGuest(Integer orderId) {
        //查询入住人
        GuestPO guestPO = new GuestPO();
        guestPO.setOrderId(orderId);
        //按间入住人
        return guestMapper.select(guestPO);
    }

    /**
     * 查询订单详情
     *
     * @param orderCode 订单编码
     * @return 订单信息
     */
    private OrderDTO queryOrderDetail(String orderCode) {
        OrderCodeDTO request = new OrderCodeDTO();
        request.setOrderCode(orderCode);
        request.setPageFlag(0); //不锁单
        return orderQueryService.queryOrderDetail(request);
    }


    private String generateToken() {
        // 实现生成唯一 token 的逻辑，例如 UUID
        return UUID.randomUUID().toString();
    }

    /**
     * 获取支付配置信息
     */
    private VccPayConfigDTO getPayConfig(VccPayConfigReqDTO reqDTO) throws Exception {
        CountDownLatch countDownLatch = new CountDownLatch(3);

        //供应商查询
        Future<ResultX<VccPayConfigDTO>> supply = processingOrderExecutor.submit(() -> {
            ResultX<VccPayConfigDTO> supplyCodeResult = null;
            try {
                VccPayConfigReqDTO vccPayConfigReqDTO = new VccPayConfigReqDTO();
                vccPayConfigReqDTO.setSupplyClass(SupplyClassEnum.ZIQIAN.getCode());
                vccPayConfigReqDTO.setSupplyCode(reqDTO.getSupplyCode());
                vccPayConfigReqDTO.setPayMethod(reqDTO.getPayMethod());
                vccPayConfigReqDTO.setPayDimension(1);
                supplyCodeResult = virtualCardService.queryVccPayConfig(vccPayConfigReqDTO);
            } catch (Exception e) {
                log.info("供应商查询 系统异常", e);
            } finally {
                countDownLatch.countDown();
            }
            return supplyCodeResult;
        });

        //客户查询
        Future<ResultX<VccPayConfigDTO>> agent = processingOrderExecutor.submit(() -> {
            ResultX<VccPayConfigDTO> agentCodeResult = null;
            try {
                VccPayConfigReqDTO vccPayConfigReqDTO = new VccPayConfigReqDTO();
                vccPayConfigReqDTO.setSupplyClass(SupplyClassEnum.ZIQIAN.getCode());
                vccPayConfigReqDTO.setAgentCode(reqDTO.getAgentCode());
                vccPayConfigReqDTO.setPayMethod(reqDTO.getPayMethod());
                vccPayConfigReqDTO.setPayDimension(4);
                agentCodeResult = virtualCardService.queryVccPayConfig(vccPayConfigReqDTO);
            } catch (Exception e) {
                log.info("客户查询 系统异常", e);
            } finally {
                countDownLatch.countDown();
            }
            return agentCodeResult;
        });

        //供应类型查询
        Future<ResultX<VccPayConfigDTO>> supplyClass = processingOrderExecutor.submit(() -> {
            ResultX<VccPayConfigDTO> supplyClassResult = null;
            try {
                VccPayConfigReqDTO vccPayConfigReqDTO = new VccPayConfigReqDTO();
                vccPayConfigReqDTO.setSupplyClass(SupplyClassEnum.ZIQIAN.getCode());
                vccPayConfigReqDTO.setPayMethod(reqDTO.getPayMethod());
                vccPayConfigReqDTO.setPayDimension(0);
                supplyClassResult = virtualCardService.queryVccPayConfig(vccPayConfigReqDTO);
            } catch (Exception e) {
                log.info("供应类型查询 系统异常", e);
            } finally {
                countDownLatch.countDown();
            }
            return supplyClassResult;
        });

        boolean noTimeout = true;
        try {
            //超时返回false
            noTimeout = countDownLatch.await(10000, TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            log.error("电邮发单前查询支付配置信息超时", e);
        }
        if (!noTimeout) {
            throw new SysException(ErrorCodeEnum.CONNECT_TIME_OUT);
        }

        ResultX<VccPayConfigDTO> supplyCodeResult = supply.get();
        ResultX<VccPayConfigDTO> agentCodeResult = agent.get();
        ResultX<VccPayConfigDTO> supplyClassResult = supplyClass.get();

        if (supplyCodeResult == null || supplyCodeResult.isError() ||
                agentCodeResult == null || agentCodeResult.isError() ||
                supplyClassResult == null || supplyClassResult.isError()) {
            log.error("查询支付配置信息失败，req={}，supplyCodeResult={}，agentCodeResult={}，supplyClassResult={}",
                    JSONUtil.toJsonStr(reqDTO), JSONUtil.toJsonStr(supplyCodeResult), JSONUtil.toJsonStr(agentCodeResult), JSONUtil.toJsonStr(supplyClassResult));
            throw new SysException(ErrorCodeEnum.QUERY_PAY_ACCOUNT_INFO_FAIL);
        }

        if (supplyCodeResult.isSuccess() && supplyCodeResult.getData() != null) {
            //供应商
            return supplyCodeResult.getData();
        } else if (agentCodeResult.isSuccess() && agentCodeResult.getData() != null) {
            //客户
            return agentCodeResult.getData();
        } else if (supplyClassResult.isSuccess() && supplyClassResult.getData() != null) {
            //供应类型
            return supplyClassResult.getData();
        }
        return null;
    }

    /**
     * 修改供货单信息
     *
     * @param request
     * @return
     */
    @Override
    @Transactional
    public Response<Object> modifySupplyOrderInfos(ModifySupplyOrderInfosReq request) {

        //校验供货单的合法性
        SupplyOrderPO supplyOrderPO = supplyOrderMapper.selectByPrimaryKey(request.getSupplyOrderId());
        if (Objects.isNull(supplyOrderPO) || Objects.isNull(supplyOrderPO.getOrderId())) {
            return Response.error(ErrorCodeEnum.SUPPLY_ORDER_NOT_EXIST.errorCode, ErrorCodeEnum.SUPPLY_ORDER_NOT_EXIST.errorDesc);
        }
        Integer oldSupplyConfirmationStatus = supplyOrderPO.getConfirmationStatus();
        Integer currentSupplyOrderCurrency = supplyOrderPO.getBaseCurrency();

        //已取消不支持修改
        if (Objects.nonNull(supplyOrderPO.getConfirmationStatus())
                && Objects.equals(supplyOrderPO.getConfirmationStatus(), ConfirmationStatusEnum.CANCELED.key)) {
            return Response.error(ErrorCodeEnum.SUPPLYORDER_CANCEL_CANNOT_OPERATE.errorCode, ErrorCodeEnum.SUPPLYORDER_CANCEL_CANNOT_OPERATE.errorDesc);
        }

        //查询对应的订单
        OrderPO orderPO = orderMapper.selectByPrimaryKey(supplyOrderPO.getOrderId());
        if (Objects.isNull(orderPO)) {
            return Response.error(ErrorCodeEnum.ORDER_NOT_EXISTS.errorCode, ErrorCodeEnum.ORDER_NOT_EXISTS.errorDesc);
        }

        //存在到店费用
        Map<Long, ModifySupplyOrderPayAtHotelDTO> modifySupplyOrderPayAtHotelMap = new HashMap<>();
        if (CollUtil.isNotEmpty(request.getModifySupplyOrderPayAtHotel())) {
            for (ModifySupplyOrderPayAtHotelDTO modifySupplyOrderPayAtHotelDTO : request.getModifySupplyOrderPayAtHotel()) {
//                if (Objects.nonNull(orderPO.getPayAtHotelCurrency())
//                        && Objects.nonNull(modifySupplyOrderPayAtHotelDTO.getPayAtHotelCurrency())
//                        && !Objects.equals(orderPO.getPayAtHotelCurrency(), modifySupplyOrderPayAtHotelDTO.getPayAtHotelCurrency())) {
//                    return Response.error(ErrorCodeEnum.PAY_AT_HOTEL_CURRENCY_UNLIKE_ORDER_CURRENCY.errorCode, ErrorCodeEnum.PAY_AT_HOTEL_CURRENCY_UNLIKE_ORDER_CURRENCY.errorDesc);
//                }
                if (Objects.nonNull(modifySupplyOrderPayAtHotelDTO.getPayAtHotelFee()) && Objects.isNull(modifySupplyOrderPayAtHotelDTO.getPayAtHotelCurrency())) {
                    return Response.error(ErrorCodeEnum.PAY_AT_HOTEL_CURRENCY_IS_NULL.errorCode, ErrorCodeEnum.PAY_AT_HOTEL_CURRENCY_IS_NULL.errorDesc);
                }
                modifySupplyOrderPayAtHotelMap.put(Long.valueOf(modifySupplyOrderPayAtHotelDTO.getRoomNumber()), modifySupplyOrderPayAtHotelDTO);
            }
        }

        //查询供货单产品
        Example supplyProductExample = new Example(SupplyProductPO.class);
        supplyProductExample.createCriteria()
                .andEqualTo("supplyOrderId", supplyOrderPO.getId())
                .andEqualTo("orderId", orderPO.getId());
        List<SupplyProductPO> supplyProductPOS = supplyProductMapper.selectByExample(supplyProductExample);
        if (CollUtil.isEmpty(supplyProductPOS) || supplyProductPOS.size() != 1) {
            return Response.error(ErrorCodeEnum.SUPPLY_NO_HAS_PRODUCT.errorCode, ErrorCodeEnum.SUPPLY_NO_HAS_PRODUCT.errorDesc);
        }
        SupplyProductPO supplyProductPO = supplyProductPOS.get(0);

        //查询供货单产品每日明细
        Example supplyProductPriceExample = new Example(SupplyProductPricePO.class);
        supplyProductPriceExample.createCriteria()
                .andEqualTo("supplyOrderId", supplyOrderPO.getId())
                .andEqualTo("supplyProductId", supplyProductPO.getId());
        List<SupplyProductPricePO> supplyProductPricePOList = supplyProductPriceMapper.selectByExample(supplyProductPriceExample);
        if (CollUtil.isEmpty(supplyProductPricePOList)) {
            return Response.error(ErrorCodeEnum.NO_SALE_PRICE.errorCode, ErrorCodeEnum.NO_SALE_PRICE.errorDesc);
        }

        //根据房间号进行分组
        Map<Integer, List<SupplyProductPricePO>> roomGroup = supplyProductPricePOList.stream()
                .filter(p -> Objects.nonNull(p.getRoomNumber()))
                .collect(Collectors.groupingBy(SupplyProductPricePO::getRoomNumber));

        //查询入住人信息
        Example queryGuestExample = new Example(GuestPO.class);
        queryGuestExample.createCriteria().andEqualTo("orderId", orderPO.getId());
        List<GuestPO> orderGuestsPO = guestMapper.selectByExample(queryGuestExample);
        Map<Integer, List<GuestPO>> orderGuestGroup = orderGuestsPO.stream()
                .filter(p -> Objects.nonNull(p.getRoomNumber()))
                .collect(Collectors.groupingBy(GuestPO::getRoomNumber));

        StringBuilder updateContent = new StringBuilder();
        updateContent.append("修改供货单，");

        Boolean isUpdateSupplyOrder = Boolean.FALSE;
        SupplyOrderPO updateSupplyOrder = new SupplyOrderPO();

        Boolean isUpdateSupplyProduct = Boolean.FALSE;
        SupplyProductPO updateSupplyProduct = new SupplyProductPO();

        //新增的房型集合
        Set<Long> newRoomNumbersSet = null;
        //要移除掉的房型集合
        Set<Long> removeRoomNumbersSet = null;
        //保留的房间集合
        Set<Long> retainRoomNumbersSet = null;
        //移除的所有房型的底价之和
        BigDecimal allRemoveBasePriceSum = BigDecimal.ZERO;
        //新增的所有房型的底价之和
        BigDecimal allNewBasePriceSum = BigDecimal.ZERO;
        //新增的房间的每天价格明细
        List<SupplyProductPricePO> newSupplyProductPriceList = new ArrayList<>();
        //需要移除的供货单产品价格记录
        Set<Integer> removeSupplyProductPriceIdSet = new HashSet<>();

        //1.修改价格计划名称(不改变价格计划id)
        if (StrUtil.isNotBlank(request.getProductName())
                && !Objects.equals(request.getProductName(), supplyProductPO.getProductName())) {
            //修改供货单的价格计划名称
            updateSupplyOrder.setProductName(request.getProductName());
            isUpdateSupplyOrder = Boolean.TRUE;

            //修改供货单产品的价格计划名称
            updateSupplyProduct.setProductName(request.getProductName());
            isUpdateSupplyProduct = Boolean.TRUE;

            updateContent.append("价格计划名称")
                    .append(supplyProductPO.getProductName())
                    .append("改为")
                    .append(request.getProductName())
                    .append("，");
        }

        //2.修改床型
        List<BedInfoDto> oldBedInfos = new ArrayList<>();
        List<BedInfoDto> newBedInfos = new ArrayList<>();
        if (StrUtil.isNotBlank(supplyOrderPO.getBedType())) {
            BedTypesDetailDto bedTypesDetailDto = JSONUtil.toBean(supplyOrderPO.getBedType(), BedTypesDetailDto.class);
            if (Objects.nonNull(bedTypesDetailDto) && CollUtil.isNotEmpty(bedTypesDetailDto.getBedInfos())) {
                oldBedInfos = bedTypesDetailDto.getBedInfos();
            }
        }
        if (CollUtil.isNotEmpty(request.getBedInfos())) {
            newBedInfos = ProductSalePriceConvert.INSTANCE.bedInfoDtoConvert(request.getBedInfos());
        }
        //比较oldBedInfos和request.getBedInfos是否一致
        if (!isBedInfosEqual(oldBedInfos, newBedInfos)) {
            //修改供货单的床型
            BedTypesDetailDto bedTypesDetailDto = new BedTypesDetailDto();
            bedTypesDetailDto.setBedInfos(newBedInfos);
            updateSupplyOrder.setBedType(JSONUtil.toJsonStr(bedTypesDetailDto));
            isUpdateSupplyOrder = Boolean.TRUE;

            //供货单产品存在床型,则进行覆盖
            if (StrUtil.isNotBlank(supplyProductPO.getBedType())) {
                updateSupplyProduct.setBedType(JSONUtil.toJsonStr(bedTypesDetailDto));
                isUpdateSupplyProduct = Boolean.TRUE;
            }

            //记录床型修改的日志内容
            updateContent.append("床型");
            if (CollUtil.isNotEmpty(oldBedInfos)) {
                String oldBedTypeName = oldBedInfos.stream()
                        .filter(o -> StrUtil.isNotBlank(o.getBedTypeName()))
                        .map(BedInfoDto::getBedTypeName)
                        .collect(Collectors.joining("和"));
                updateContent.append(oldBedTypeName);
            }
            updateContent
                    .append("改为")
                    .append(newBedInfos.stream()
                            .filter(o -> StrUtil.isNotBlank(o.getBedTypeName()))
                            .map(BedInfoDto::getBedTypeName)
                            .collect(Collectors.joining("和")))
                    .append("，");
        }

        //3.修改入离日期
        if (StrUtil.isNotBlank(request.getStartDate())
                && !Objects.equals(request.getStartDate(), DateUtilX.dateToString(supplyProductPO.getStartDate()))) {
            updateSupplyOrder.setStartDate(DateUtilX.stringToDate(request.getStartDate()));
            isUpdateSupplyOrder = Boolean.TRUE;

            updateSupplyProduct.setStartDate(DateUtilX.stringToDate(request.getStartDate()));
            isUpdateSupplyProduct = Boolean.TRUE;

            updateContent.append("入住日期")
                    .append(DateUtilX.dateToString(supplyProductPO.getStartDate()))
                    .append("改为")
                    .append(request.getStartDate())
                    .append("，");
        }
        if (StrUtil.isNotBlank(request.getEndDate())
                && !Objects.equals(request.getEndDate(), DateUtilX.dateToString(supplyProductPO.getEndDate()))) {
            updateSupplyOrder.setEndDate(DateUtilX.stringToDate(request.getEndDate()));
            isUpdateSupplyOrder = Boolean.TRUE;

            updateSupplyProduct.setEndDate(DateUtilX.stringToDate(request.getEndDate()));
            isUpdateSupplyProduct = Boolean.TRUE;

            updateContent.append("离店日期")
                    .append(DateUtilX.dateToString(supplyProductPO.getEndDate()))
                    .append("改为")
                    .append(request.getEndDate())
                    .append("，");
        }

        BigDecimal basePriceTotalAmt = BigDecimal.ZERO;
        //4.修改绑定的房间
        Map<Long, OrderRoomGuestDTO> roomGuestGroup = new HashMap<>();
        Map<Long, ModifyRoomDetailDTO> modifyRoomDetailGroup = new HashMap<>();
        if (CollUtil.isNotEmpty(request.getRoomGuestList())) {
            //新房型的所有客人姓名
            StringBuilder newGuest = new StringBuilder();
            //新绑定的房间号
            for (OrderRoomGuestDTO roomGuestList : request.getRoomGuestList()) {
                if (Objects.nonNull(roomGuestList.getRoomNumber())) {
                    roomGuestGroup.put(Long.valueOf(roomGuestList.getRoomNumber()), roomGuestList);

                    List<GuestPO> orderGuests = orderGuestGroup.get(roomGuestList.getRoomNumber());
                    if (CollUtil.isNotEmpty(orderGuests)) {
                        for (GuestPO orderGuest : orderGuests) {
                            if (StrUtil.isNotBlank(orderGuest.getName())) {
                                newGuest.append(orderGuest.getName()).append(",");
                            }
                        }
                    }
                }
            }
            if (StrUtil.isNotBlank(newGuest.toString())) {
                newGuest.deleteCharAt(newGuest.length() - 1);
            }

            List<Long> newRoomNumbersCollect = new ArrayList<>();
            for (ModifyRoomDetailDTO modifyRoomDetailDTO : request.getRoomList()) {
                if (Objects.nonNull(modifyRoomDetailDTO.getRoomNumber())) {
                    modifyRoomDetailGroup.put(Long.valueOf(modifyRoomDetailDTO.getRoomNumber()), modifyRoomDetailDTO);
                    newRoomNumbersCollect.add(Long.valueOf(modifyRoomDetailDTO.getRoomNumber()));
                }
            }
            Collections.sort(newRoomNumbersCollect);

            if (CollUtil.isEmpty(newRoomNumbersCollect) || StrUtil.isBlank(supplyOrderPO.getRoomNumbers())
                    || CollUtil.isEmpty(modifyRoomDetailGroup)) {
                throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM.errorCode, ErrorCodeEnum.INVALID_INPUTPARAM.errorDesc);
            }

            //原来绑定的房间号
            List<Long> oldRoomNumbersCollect = Arrays.stream(supplyOrderPO.getRoomNumbers().split(","))
                    .map(obj -> {
                        try {
                            return Long.valueOf(obj);
                        } catch (NumberFormatException e) {
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)
                    .distinct()
                    .sorted()
                    .collect(Collectors.toList());
            //供货单绑定的房间号发生变化，需要将新的房间号保存进去
            if (!Objects.equals(oldRoomNumbersCollect, newRoomNumbersCollect)) {
                updateSupplyOrder.setRoomNumbers(StrUtilX.listToString(newRoomNumbersCollect));
                updateSupplyOrder.setRoomQty(newRoomNumbersCollect.size());
                isUpdateSupplyOrder = Boolean.TRUE;

                updateSupplyProduct.setGuest(newGuest.toString());
                updateSupplyProduct.setRoomQty(newRoomNumbersCollect.size());
                isUpdateSupplyProduct = Boolean.TRUE;

                //供货单绑定的房间发生变化，需要更新房间信息
                Set<Long> oldRoomNumbersSetTmp = new HashSet<>(oldRoomNumbersCollect);
                Set<Long> newRoomNumbersSetTmp = new HashSet<>(newRoomNumbersCollect);

                //新增的房型集合
                newRoomNumbersSet = new HashSet<>(newRoomNumbersCollect);
                newRoomNumbersSet.removeAll(oldRoomNumbersSetTmp);

                //要移除掉的房型集合
                removeRoomNumbersSet = new HashSet<>(oldRoomNumbersCollect);
                removeRoomNumbersSet.removeAll(newRoomNumbersSetTmp);

                //保留的房间集合
                retainRoomNumbersSet = new HashSet<>(oldRoomNumbersCollect);
                retainRoomNumbersSet.retainAll(newRoomNumbersSetTmp);

                if (CollUtil.isNotEmpty(removeRoomNumbersSet)) {
                    //计算出所有要移除掉的房型的底价只和
                    for (Long removeRoomNumber : removeRoomNumbersSet) {
                        List<SupplyProductPricePO> supplyProductPricePOS = roomGroup.get(Math.toIntExact(removeRoomNumber));
                        allRemoveBasePriceSum = supplyProductPricePOS.stream()
                                .filter(p -> Objects.nonNull(p.getSaleDate()))
                                .map(p -> p.getBasePrice() == null ? BigDecimal.ZERO : p.getBasePrice())
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                    }
                }

                if (CollUtil.isNotEmpty(newRoomNumbersSet)) {
                    //计算出所有要新增的房型的底价只和
                    for (Long newRoomNumber : newRoomNumbersSet) {
                        ModifyRoomDetailDTO modifyRoomDetailDTO = modifyRoomDetailGroup.get(newRoomNumber);
                        //新增的房型的每一天的底价默认等于房费
                        if (CollUtil.isNotEmpty(modifyRoomDetailDTO.getPriceList())) {
                            for (ModifyPriceDetailDTO modifyPriceDetailDTO : modifyRoomDetailDTO.getPriceList()) {
                                BigDecimal roomPrice = modifyPriceDetailDTO.getRoomPrice() == null ? BigDecimal.ZERO : modifyPriceDetailDTO.getRoomPrice();
                                allNewBasePriceSum = allNewBasePriceSum.add(roomPrice);

                                SupplyProductPricePO newSupplyProductPrice = new SupplyProductPricePO();
                                newSupplyProductPrice.setSupplyProductId(supplyProductPO.getId());
                                newSupplyProductPrice.setSupplyOrderId(supplyOrderPO.getId());
                                newSupplyProductPrice.setSaleDate(DateUtilX.stringToDate(modifyPriceDetailDTO.getSaleDate()));
                                newSupplyProductPrice.setBasePrice(roomPrice);
                                newSupplyProductPrice.setRoomNumber(modifyRoomDetailDTO.getRoomNumber());
                                OrderRoomGuestDTO orderRoomGuestDTO = roomGuestGroup.get(Long.valueOf(modifyRoomDetailDTO.getRoomNumber()));
                                String roomPersons = null;
                                if (Objects.nonNull(orderRoomGuestDTO) && Objects.nonNull(orderRoomGuestDTO.getAdultQty())) {
                                    roomPersons = orderRoomGuestDTO.getAdultQty().toString();
                                    if (StrUtilX.isNotEmpty(orderRoomGuestDTO.getChildrenAge())) {
                                        roomPersons += ("," + orderRoomGuestDTO.getChildrenAge());
                                    }
                                }
                                newSupplyProductPrice.setRoomPersons(roomPersons);
                                newSupplyProductPrice.setCurrency(currentSupplyOrderCurrency);
                                newSupplyProductPrice.setRoomPrice(roomPrice);
                                newSupplyProductPrice.setTax(BigDecimal.ZERO);
                                newSupplyProductPrice.setSalesTax(BigDecimal.ZERO);
                                newSupplyProductPrice.setExtraTaxFee(BigDecimal.ZERO);
                                newSupplyProductPrice.setBreakfastQty(0);
                                newSupplyProductPrice.setDiscount(BigDecimal.ZERO);
                                newSupplyProductPriceList.add(newSupplyProductPrice);
                            }
                        }
                        //新增的房型明确有到店付，才保存到店付记录
                        ModifySupplyOrderPayAtHotelDTO modifySupplyOrderPayAtHotelDTO = modifySupplyOrderPayAtHotelMap.get(newRoomNumber);
                        if (Objects.nonNull(modifySupplyOrderPayAtHotelDTO)
                                && Objects.nonNull(modifySupplyOrderPayAtHotelDTO.getPayAtHotelFee())
                                && Objects.nonNull(modifySupplyOrderPayAtHotelDTO.getPayAtHotelCurrency())) {
                            SupplyProductPricePO newSupplyProductPrice = new SupplyProductPricePO();
                            newSupplyProductPrice.setSupplyProductId(supplyProductPO.getId());
                            newSupplyProductPrice.setSupplyOrderId(supplyOrderPO.getId());
                            newSupplyProductPrice.setRoomNumber(modifyRoomDetailDTO.getRoomNumber());
                            newSupplyProductPrice.setCurrency(currentSupplyOrderCurrency);
                            newSupplyProductPrice.setRoomPrice(BigDecimal.ZERO);
                            newSupplyProductPrice.setTax(BigDecimal.ZERO);
                            newSupplyProductPrice.setSalesTax(BigDecimal.ZERO);
                            newSupplyProductPrice.setExtraTaxFee(BigDecimal.ZERO);
                            newSupplyProductPrice.setPayAtHotelFee(modifySupplyOrderPayAtHotelDTO.getPayAtHotelFee());
                            newSupplyProductPrice.setPayAtHotelCurrency(modifySupplyOrderPayAtHotelDTO.getPayAtHotelCurrency());
                            newSupplyProductPrice.setBreakfastQty(0);
                            newSupplyProductPrice.setDiscount(BigDecimal.ZERO);
                            newSupplyProductPriceList.add(newSupplyProductPrice);
                        }
                    }
                }

                updateContent.append("房间");
                for (Long oldRoomNumber : oldRoomNumbersCollect) {
                    updateContent.append("房间").append(oldRoomNumber).append("、");
                }
                //全部都新的房间
                if (updateContent.toString().endsWith("、")) {
                    updateContent.deleteCharAt(updateContent.length() - 1);
                }
                updateContent.append("改为");
                for (Long newRoomNumber : newRoomNumbersCollect) {
                    updateContent.append("房间").append(newRoomNumber).append("、");
                }
                if (updateContent.toString().endsWith("、")) {
                    updateContent.deleteCharAt(updateContent.length() - 1);
                }
                updateContent.append("，");
            } else {
                retainRoomNumbersSet = new HashSet<>(oldRoomNumbersCollect);
            }
        }


        //5.修改房费和到店付价格
        if (Objects.nonNull(retainRoomNumbersSet) && CollUtil.isNotEmpty(retainRoomNumbersSet)) {
            for (Long retainRoomNumber : retainRoomNumbersSet) {
                BigDecimal oldBasePriceTotalAmt = BigDecimal.ZERO;
                BigDecimal newBasePriceTotalAmt = BigDecimal.ZERO;
                ModifyRoomDetailDTO modifyRoomDetailDTO = modifyRoomDetailGroup.get(retainRoomNumber);
                List<SupplyProductPricePO> supplyProductPriceListTmp = roomGroup.get(retainRoomNumber.intValue());
                if (Objects.isNull(modifyRoomDetailDTO) || CollUtil.isEmpty(modifyRoomDetailDTO.getPriceList())
                        || CollUtil.isEmpty(supplyProductPriceListTmp)) {
                    continue;
                }
                //获取当前房间的修改到店付信息
                ModifySupplyOrderPayAtHotelDTO modifySupplyOrderPayAtHotelDTO = modifySupplyOrderPayAtHotelMap.get(retainRoomNumber);

                // 将modifyRoomDetailDTO的priceList按日期分组
                Map<String, ModifyPriceDetailDTO> modifyPriceMap = modifyRoomDetailDTO.getPriceList().stream()
                        .filter(modifyPriceDetailDTO -> StrUtil.isNotBlank(modifyPriceDetailDTO.getSaleDate()))
                        .collect(Collectors.toMap(ModifyPriceDetailDTO::getSaleDate, Function.identity(), (s1, s2) -> s1));

                // 将supplyProductPriceListTmp按日期分组(排除saleDate为空的数据)
                Map<String, SupplyProductPricePO> supplyPriceMap = new HashMap<>();
                SupplyProductPricePO payAtHotelPrice = null;
                for (SupplyProductPricePO supplyProductPricePO : supplyProductPriceListTmp) {
                    if (Objects.nonNull(supplyProductPricePO.getSaleDate())) {
                        supplyPriceMap.put(DateUtilX.dateToString(supplyProductPricePO.getSaleDate()), supplyProductPricePO);
                    } else {
                        //到店付的每间房的价格
                        payAtHotelPrice = supplyProductPricePO;
                    }
                }

                // 处理新增的日期数据
                for (Map.Entry<String, ModifyPriceDetailDTO> entry : modifyPriceMap.entrySet()) {
                    if (!supplyPriceMap.containsKey(entry.getKey())) {
                        ModifyPriceDetailDTO priceDetail = entry.getValue();
                        BigDecimal roomPrice = priceDetail.getRoomPrice() == null ? BigDecimal.ZERO : priceDetail.getRoomPrice();

                        SupplyProductPricePO newSupplyProductPrice = new SupplyProductPricePO();
                        newSupplyProductPrice.setSupplyProductId(supplyProductPO.getId());
                        newSupplyProductPrice.setSupplyOrderId(supplyOrderPO.getId());
                        newSupplyProductPrice.setSaleDate(DateUtilX.stringToDate(entry.getKey()));
                        newSupplyProductPrice.setBasePrice(roomPrice);
                        newSupplyProductPrice.setRoomNumber(Math.toIntExact(retainRoomNumber));
                        OrderRoomGuestDTO orderRoomGuestDTO = roomGuestGroup.get(Long.valueOf(modifyRoomDetailDTO.getRoomNumber()));
                        String roomPersons = null;
                        if (Objects.nonNull(orderRoomGuestDTO) && Objects.nonNull(orderRoomGuestDTO.getAdultQty())) {
                            roomPersons = orderRoomGuestDTO.getAdultQty().toString();
                            if (StrUtilX.isNotEmpty(orderRoomGuestDTO.getChildrenAge())) {
                                roomPersons += ("," + orderRoomGuestDTO.getChildrenAge());
                            }
                        }
                        newSupplyProductPrice.setRoomPersons(roomPersons);
                        newSupplyProductPrice.setCurrency(currentSupplyOrderCurrency);
                        newSupplyProductPrice.setRoomPrice(roomPrice);
                        newSupplyProductPrice.setTax(BigDecimal.ZERO);
                        newSupplyProductPrice.setSalesTax(BigDecimal.ZERO);
                        newSupplyProductPrice.setExtraTaxFee(BigDecimal.ZERO);
                        newSupplyProductPrice.setBreakfastQty(0);
                        newSupplyProductPrice.setDiscount(BigDecimal.ZERO);
                        newSupplyProductPriceList.add(newSupplyProductPrice);

                        basePriceTotalAmt = basePriceTotalAmt.add(roomPrice);
                        newBasePriceTotalAmt = newBasePriceTotalAmt.add(roomPrice);
                    }
                }

                // 处理需要删除的日期数据
                for (Map.Entry<String, SupplyProductPricePO> entry : supplyPriceMap.entrySet()) {
                    if (!modifyPriceMap.containsKey(entry.getKey())) {
                        SupplyProductPricePO supplyPrice = entry.getValue();
                        removeSupplyProductPriceIdSet.add(supplyPrice.getId());
                        oldBasePriceTotalAmt = oldBasePriceTotalAmt.add(supplyPrice.getBasePrice());
                    }
                }

                boolean isAppendRoomNum = true;
                //原先没有到店付
                if (Objects.isNull(payAtHotelPrice)
                        && Objects.nonNull(modifySupplyOrderPayAtHotelDTO)
                        && Objects.nonNull(modifySupplyOrderPayAtHotelDTO.getPayAtHotelFee())) {
                    //到店付
                    SupplyProductPricePO newSupplyProductPrice = new SupplyProductPricePO();
                    newSupplyProductPrice.setSupplyProductId(supplyProductPO.getId());
                    newSupplyProductPrice.setSupplyOrderId(supplyOrderPO.getId());
                    newSupplyProductPrice.setRoomNumber(Math.toIntExact(retainRoomNumber));
                    newSupplyProductPrice.setCurrency(currentSupplyOrderCurrency);
                    newSupplyProductPrice.setRoomPrice(BigDecimal.ZERO);
                    newSupplyProductPrice.setTax(BigDecimal.ZERO);
                    newSupplyProductPrice.setSalesTax(BigDecimal.ZERO);
                    newSupplyProductPrice.setExtraTaxFee(BigDecimal.ZERO);
                    newSupplyProductPrice.setPayAtHotelFee(modifySupplyOrderPayAtHotelDTO.getPayAtHotelFee());
                    newSupplyProductPrice.setPayAtHotelCurrency(modifySupplyOrderPayAtHotelDTO.getPayAtHotelCurrency());
                    newSupplyProductPrice.setBreakfastQty(0);
                    newSupplyProductPrice.setDiscount(BigDecimal.ZERO);
                    newSupplyProductPriceList.add(newSupplyProductPrice);
                    updateContent.append("供货单房间")
                            .append(retainRoomNumber)
                            .append("到店另付改为")
                            .append(modifySupplyOrderPayAtHotelDTO.getPayAtHotelFee())
                            .append(SettlementCurrencyEnum.getCodeByKey(String.valueOf(modifySupplyOrderPayAtHotelDTO.getPayAtHotelCurrency())))
                            .append("，");
                    isAppendRoomNum = false;
                } else if (Objects.nonNull(payAtHotelPrice)
                        && Objects.nonNull(modifySupplyOrderPayAtHotelDTO)
                        && Objects.nonNull(modifySupplyOrderPayAtHotelDTO.getPayAtHotelFee())
                        && Objects.nonNull(payAtHotelPrice.getId())
                        && ((Objects.nonNull(payAtHotelPrice.getPayAtHotelCurrency()) && !Objects.equals(payAtHotelPrice.getPayAtHotelCurrency(), modifySupplyOrderPayAtHotelDTO.getPayAtHotelCurrency()))
                        || (Objects.nonNull(payAtHotelPrice.getPayAtHotelFee()) && modifySupplyOrderPayAtHotelDTO.getPayAtHotelFee().compareTo(payAtHotelPrice.getPayAtHotelFee()) != 0)
                        || Objects.isNull(payAtHotelPrice.getPayAtHotelFee()))) {
                    updateContent.append("供货单房间")
                            .append(retainRoomNumber)
                            .append("到店另付");
                    if (Objects.nonNull(payAtHotelPrice.getPayAtHotelFee())
                            && StrUtil.isNotBlank(SettlementCurrencyEnum.getCodeByKey(String.valueOf(payAtHotelPrice.getPayAtHotelCurrency())))) {
                        updateContent.append(payAtHotelPrice.getPayAtHotelFee())
                                .append(SettlementCurrencyEnum.getCodeByKey(String.valueOf(payAtHotelPrice.getPayAtHotelCurrency())));
                    }
                    updateContent.append("改为")
                            .append(modifySupplyOrderPayAtHotelDTO.getPayAtHotelFee())
                            .append(SettlementCurrencyEnum.getCodeByKey(String.valueOf(modifySupplyOrderPayAtHotelDTO.getPayAtHotelCurrency())))
                            .append("，");
                    isAppendRoomNum = false;

                    SupplyProductPricePO updatePrice = new SupplyProductPricePO();
                    updatePrice.setId(payAtHotelPrice.getId());
                    updatePrice.setPayAtHotelFee(modifySupplyOrderPayAtHotelDTO.getPayAtHotelFee());
                    updatePrice.setPayAtHotelCurrency(modifySupplyOrderPayAtHotelDTO.getPayAtHotelCurrency());
                    supplyProductPriceMapper.updateByPrimaryKeySelective(updatePrice);
                }

                // 处理需要更新的日期数据
                for (Map.Entry<String, ModifyPriceDetailDTO> entry : modifyPriceMap.entrySet()) {
                    String saleDate = entry.getKey();
                    ModifyPriceDetailDTO modifyPrice = entry.getValue();
                    SupplyProductPricePO supplyPrice = supplyPriceMap.get(saleDate);
                    if (Objects.nonNull(supplyPrice) && modifyPrice.getRoomPrice().compareTo(supplyPrice.getRoomPrice()) != 0) {
                        if (isAppendRoomNum) {
                            updateContent.append("供货单房间").append(retainRoomNumber);
                            isAppendRoomNum = false;
                        }
                        updateContent.append(saleDate).append("改为").append(modifyPrice.getRoomPrice()).append("，");

                        SupplyProductPricePO updatePrice = new SupplyProductPricePO();
                        updatePrice.setId(supplyPrice.getId());
                        updatePrice.setBasePrice(supplyPrice.getBasePrice().subtract(supplyPrice.getRoomPrice()).add(modifyPrice.getRoomPrice()));
                        updatePrice.setRoomPrice(modifyPrice.getRoomPrice());
                        supplyProductPriceMapper.updateByPrimaryKeySelective(updatePrice);

                        basePriceTotalAmt = basePriceTotalAmt.add(updatePrice.getBasePrice());
                        oldBasePriceTotalAmt = oldBasePriceTotalAmt.add(supplyPrice.getBasePrice());
                        newBasePriceTotalAmt = newBasePriceTotalAmt.add(updatePrice.getBasePrice());
                    } else if (Objects.nonNull(supplyPrice) && Objects.nonNull(supplyPrice.getBasePrice())) {
                        //非新增日期，且房费一样，则底价没变
                        basePriceTotalAmt = basePriceTotalAmt.add(supplyPrice.getBasePrice());
                        oldBasePriceTotalAmt = oldBasePriceTotalAmt.add(supplyPrice.getBasePrice());
                        newBasePriceTotalAmt = newBasePriceTotalAmt.add(supplyPrice.getBasePrice());
                    }
                }
                if (oldBasePriceTotalAmt.compareTo(newBasePriceTotalAmt) != 0) {
                    updateContent.append("总底价").append(oldBasePriceTotalAmt).append("改成").append(newBasePriceTotalAmt).append("，");
                }
            }
        }

        //6.更新供货单产品金额和供货单底价金额
        //新的底价=已有房型新的总底价(新的日期总底价+原有日期修改之后的总底价)+新增房型的总底价
        basePriceTotalAmt = basePriceTotalAmt.add(allNewBasePriceSum);
        BigDecimal changeAmt = basePriceTotalAmt.subtract(supplyProductPO.getBasePriceTotalAmt());
        if (changeAmt.compareTo(BigDecimal.ZERO) != 0) {
            updateSupplyProduct.setBasePriceTotalAmt(basePriceTotalAmt);
            isUpdateSupplyProduct = Boolean.TRUE;

            updateSupplyOrder.setBasePrice(supplyOrderPO.getBasePrice().add(changeAmt));
            isUpdateSupplyOrder = Boolean.TRUE;

            //供货单金额=底价+退订费-返佣
            if (supplyOrderPO.getConfirmationStatus().equals(ConfirmationStatusEnum.CANCELED.key)) {//供货单是取消状态，不计算退订费
                if (!PayMethodEnum.PAY.key.equals(orderPO.getPayMethod())) {
                    updateSupplyOrder.setSupplyOrderAmt(updateSupplyOrder.getBasePrice().subtract(null != supplyOrderPO.getCommission() ? supplyOrderPO.getCommission() :
                            BigDecimal.ZERO).add(null != supplyOrderPO.getAdditionalCharges() ? supplyOrderPO.getAdditionalCharges() : BigDecimal.ZERO));
                }
                updateSupplyOrder.setRefundFee(BigDecimal.ZERO);//退订费清0
            } else {
                if (!PayMethodEnum.PAY.key.equals(orderPO.getPayMethod())) {
                    updateSupplyOrder.setSupplyOrderAmt(updateSupplyOrder.getBasePrice().add(null != supplyOrderPO.getRefundFee() ? supplyOrderPO.getRefundFee() :
                                    BigDecimal.ZERO).subtract(null != supplyOrderPO.getCommission() ? supplyOrderPO.getCommission() : BigDecimal.ZERO)
                            .add(null != supplyOrderPO.getAdditionalCharges() ? supplyOrderPO.getAdditionalCharges() : BigDecimal.ZERO));
                }
            }
        }


        //7.统一修改数据，后续需要用到最新结果
        if (isUpdateSupplyOrder) {
            updateSupplyOrder.setId(supplyOrderPO.getId());
            updateSupplyOrder.setUpdatedBy(request.getOperator());
            updateSupplyOrder.setUpdatedDt(DateUtilX.dateToString(new Date(), hour_format));
            supplyOrderMapper.updateByPrimaryKeySelective(updateSupplyOrder);
        }

        if (isUpdateSupplyProduct) {
            updateSupplyProduct.setId(supplyProductPO.getId());
            updateSupplyProduct.setUpdatedBy(request.getOperator());
            updateSupplyProduct.setUpdatedDt(DateUtilX.dateToString(new Date(), hour_format));
            supplyProductMapper.updateByPrimaryKeySelective(updateSupplyProduct);
        }

        //删除被移除掉的房型的每日价格明细
        if (CollUtil.isNotEmpty(removeRoomNumbersSet)) {
            Example deleteSupplyProductPriceExample = new Example(SupplyProductPricePO.class);
            Example.Criteria deleteSupplyProductPriceCriteria = deleteSupplyProductPriceExample.createCriteria();
            deleteSupplyProductPriceCriteria
                    .andEqualTo("supplyOrderId", supplyOrderPO.getId())
                    .andEqualTo("supplyProductId", supplyProductPO.getId())
                    .andIn("roomNumber", removeRoomNumbersSet);
            supplyProductPriceMapper.deleteByExample(deleteSupplyProductPriceExample);
        }
        if (CollUtil.isNotEmpty(removeSupplyProductPriceIdSet)) {
            Example deleteSupplyProductPriceExample = new Example(SupplyProductPricePO.class);
            Example.Criteria deleteSupplyProductPriceCriteria = deleteSupplyProductPriceExample.createCriteria();
            deleteSupplyProductPriceCriteria
                    .andEqualTo("supplyOrderId", supplyOrderPO.getId())
                    .andEqualTo("supplyProductId", supplyProductPO.getId())
                    .andIn("id", removeSupplyProductPriceIdSet);
            supplyProductPriceMapper.deleteByExample(deleteSupplyProductPriceExample);
        }

        //插入新增房型的每日价格明细
        if (CollUtil.isNotEmpty(newSupplyProductPriceList)) {
            supplyProductPriceMapper.insertList(newSupplyProductPriceList);
        }

        SupplyOrderPO supplyOrderQuery = new SupplyOrderPO();
        supplyOrderQuery.setOrderId(supplyOrderPO.getOrderId());
        List<SupplyOrderPO> supplyOrderPOList = supplyOrderMapper.select(supplyOrderQuery);

//        Set<Integer> allSupplyOrderIds = supplyOrderPOList.stream().map(SupplyOrderPO::getId).collect(Collectors.toSet());

        //统计出最新的到店付价格
//        Example querySupplyProductPriceExample = new Example(SupplyProductPricePO.class);
//        querySupplyProductPriceExample.createCriteria().andIn("supplyOrderId", allSupplyOrderIds);
//        List<SupplyProductPricePO> supplyProductPriceListTmp = supplyProductPriceMapper.selectByExample(querySupplyProductPriceExample);
//        BigDecimal newOrderPayAtHotelFeeSum = null;
//        Integer newOrderPayAtHotelFeeCurrency = null;
//        Boolean isExistPayAtHotelFee = Boolean.TRUE;
//        if (CollUtil.isNotEmpty(supplyProductPriceListTmp)) {
//            //统计出所有payAtHotelFee之和
//            for (SupplyProductPricePO supplyProductPricePO : supplyProductPriceListTmp) {
//                if (Objects.isNull(supplyProductPricePO.getSaleDate())
//                        && Objects.nonNull(supplyProductPricePO.getPayAtHotelFee())
//                        && Objects.nonNull(supplyProductPricePO.getPayAtHotelCurrency())) {
//                    if (isExistPayAtHotelFee) {
//                        newOrderPayAtHotelFeeSum = BigDecimal.ZERO;
//                        newOrderPayAtHotelFeeCurrency = supplyProductPricePO.getPayAtHotelCurrency();
//                        isExistPayAtHotelFee = Boolean.FALSE;
//                    }
//                    newOrderPayAtHotelFeeSum = newOrderPayAtHotelFeeSum.add(supplyProductPricePO.getPayAtHotelFee());
//                }
//            }
//        }

        //8.更新订单金额
        BigDecimal supplyOrderSum = BigDecimal.ZERO;
        BigDecimal rewardSum = BigDecimal.ZERO;
        BigDecimal rebateSum = BigDecimal.ZERO;
        for (SupplyOrderPO supplyOrder : supplyOrderPOList) {
            BigDecimal supplierToAgentRate = Objects.nonNull(supplyOrder.getSupplierToAgentRate()) ? supplyOrder.getSupplierToAgentRate() : BigDecimal.ONE;
            supplyOrderSum = supplyOrderSum.add(supplyOrder.getSupplyOrderAmt().multiply(supplierToAgentRate));
            rewardSum = rewardSum.add((Objects.nonNull(supplyOrder.getRewardAmt()) ? supplyOrder.getRewardAmt() : BigDecimal.ZERO).multiply(supplierToAgentRate));
            rebateSum = rebateSum.add((Objects.nonNull(supplyOrder.getRebateAmt()) ? supplyOrder.getRebateAmt() : BigDecimal.ZERO).multiply(supplierToAgentRate));
        }

        Boolean isUpdateOrder = Boolean.FALSE;
        OrderPO orderUpdate = new OrderPO();
        orderUpdate.setId(orderPO.getId());
        // orderUpdate.setSupplyOrderConfirmationStatus(ConfirmationStatusEnum.UNCONFIRM.key.toString());
        //订单的利润或者到到店另付价格发生变化才更新订单
//        if ((Objects.nonNull(newOrderPayAtHotelFeeSum) && Objects.nonNull(orderPO.getPayAtHotelFee()) && newOrderPayAtHotelFeeSum.compareTo(orderPO.getPayAtHotelFee()) != 0)
//                || (Objects.nonNull(newOrderPayAtHotelFeeSum) && Objects.isNull(orderPO.getPayAtHotelCurrency()))) {
//            orderUpdate.setPayAtHotelFee(newOrderPayAtHotelFeeSum);
//            isUpdateOrder = Boolean.TRUE;
//
//            //如果订单到店付币种是空的，则表示原先是没有到店付
//            if (Objects.isNull(orderPO.getPayAtHotelCurrency())) {
//                orderUpdate.setPayAtHotelCurrency(newOrderPayAtHotelFeeCurrency);
//            }
//        }

        BigDecimal orderProfit = orderPO.getOrderAmt().subtract(supplyOrderSum).add(rewardSum).subtract(rebateSum);
        if (orderProfit.compareTo(orderPO.getProfit()) != 0) {
            orderUpdate.setProfit(orderProfit);
            isUpdateOrder = Boolean.TRUE;
        }
        if (isUpdateOrder) {
            orderMapper.updateByPrimaryKeySelective(orderUpdate);
        }

        try {
            //9.退扣配额
            if (!supplyProductPO.getProductId().contains("_")) {
                Map<String, Integer> map = new HashMap<>();
                if (StrUtilX.isNotEmpty(supplyProductPO.getProductId())) {
                    map.put("productId", Integer.valueOf(supplyProductPO.getProductId()));
                }
                map.put("orderId", orderPO.getId());
                map.put("type", 1);//查询以扣的配额
                Response<List<DebitedQuotaDTO>> debitedQuota = debitedQuotaRemote.queryDebitedQuota(map);
                log.info("查询扣退配额:{}", JSON.toJSONString(debitedQuota));
                List<DebitedQuotaDTO> debitedQuotaDTO = new ArrayList<>();
                if (debitedQuota.getResult().equals(ResultCodeEnum.SUCCESS.code) && null != debitedQuota.getModel()) {
                    debitedQuotaDTO = debitedQuota.getModel();
                }
                QuotaDTO quotaDTO = new QuotaDTO();
                quotaDTO.setOrderCode(orderPO.getOrderCode());
                quotaDTO.setOrderId(orderPO.getId());
                quotaDTO.setProductId(Integer.valueOf(supplyProductPO.getProductId()));

                quotaDTO.setSupplyOrderCode(supplyOrderPO.getSupplyOrderCode());
                quotaDTO.setSupplyOrderId(supplyOrderPO.getId());

                if (debitedQuotaDTO.size() > 0) {
                    //返还配额
                    StringBuilder saleDate = new StringBuilder();
                    for (DebitedQuotaDTO debitedQuotaDTOs : debitedQuotaDTO) {
                        saleDate.append(DateUtilX.dateToString(DateUtilX.stringToDate(debitedQuotaDTOs.getSaleDate(), "yyyy-MM-dd HH:mm:ss"), "yyyy-MM-dd")).append(",");
                    }
                    quotaDTO.setSaleDate(saleDate.append(DateUtilX.getCurrentDateStr(2)).toString());//加一段日期，用以处理后续逻辑
                    quotaDTO.setQuota(-debitedQuotaDTO.get(0).getQuota());//以扣配额为负数，负负得正，加配额
                    quotaRemote.modifyQuota(quotaDTO);
                }

                //扣除配额
                Map<String, String> data = new HashMap<>();
                data.put("begin", DateUtilX.dateToString(supplyOrderPO.getStartDate()));
                data.put("end", DateUtilX.dateToString(supplyOrderPO.getEndDate()));
                List<String> list = orderMapper.queryBetweenDate(data);
                StringBuilder saleDate1 = new StringBuilder();
                for (String s : list) {
                    saleDate1.append(s).append(",");
                }
                quotaDTO.setSaleDate(saleDate1.substring(0, saleDate1.length() - 1));
                quotaDTO.setQuota(-supplyOrderPO.getRoomQty());//减配额
                quotaRemote.modifyQuota(quotaDTO);
            }
        } catch (
                Exception e) {
            log.error("扣配额异常！订单ID：" + orderPO.getId(), e);
        }

        if (changeAmt.compareTo(BigDecimal.ZERO) != 0) {
            //10.更新订单未结算金额和结算状态
            SupplyOrderFinancePO supplyOrderFinanceQuery = new SupplyOrderFinancePO();
            supplyOrderFinanceQuery.setSupplyOrderId(supplyOrderPO.getId());
            supplyOrderFinanceQuery.setFinanceType(StatementTypeEnum.SUPPLY_ORDER_AMT.key);
            SupplyOrderFinancePO supplyOrderFinancePO = supplyOrderFinanceMapper.selectOne(supplyOrderFinanceQuery);
            SupplyOrderFinancePO supplyOrderFinanceUpdate = new SupplyOrderFinancePO();
            supplyOrderFinanceUpdate.setId(supplyOrderFinancePO.getId());
            //如果原来供货单是取消状态，未付金额=供货单金额
            if (oldSupplyConfirmationStatus.equals(ConfirmationStatusEnum.CANCELED.key) || PayMethodEnum.PAY.key.equals(orderPO.getPayMethod())) {
                supplyOrderFinanceUpdate.setUnpaidAmt(updateSupplyOrder.getSupplyOrderAmt() == null ? BigDecimal.ZERO : updateSupplyOrder.getSupplyOrderAmt());
            } else {
                supplyOrderFinanceUpdate.setUnpaidAmt(supplyOrderFinancePO.getUnpaidAmt().add(changeAmt));
            }

            if (BigDecimal.ZERO.compareTo(supplyOrderFinanceUpdate.getUnpaidAmt()) == 0) {
                supplyOrderFinanceUpdate.setSettlementStatus(1);
            } else {
                supplyOrderFinanceUpdate.setSettlementStatus(0);
            }
            //更新对账状态
            if (!Objects.equals(supplyOrderFinancePO.getCheckStatus(), CheckStatusEnum.CHECKING.key)) {
                if (BigDecimal.ZERO.compareTo(supplyOrderFinanceUpdate.getUnpaidAmt()) == 0) {
                    //如果未收金额为0，则改为不可出账
                    supplyOrderFinanceUpdate.setCheckStatus(CheckStatusEnum.CANNOT_CHECK.key);
                } else {
                    //如果未收金额不为0，则改为可出账
                    supplyOrderFinanceUpdate.setCheckStatus(CheckStatusEnum.CAN_CHECK.key);
                }
            }
            supplyOrderFinanceMapper.updateByPrimaryKeySelective(supplyOrderFinanceUpdate);
        }

        // 如果updateContent的最后一个字符是,或者，则删除最后一个字符
        if (updateContent.toString().endsWith(",") || updateContent.toString().endsWith("，")) {
            updateContent.deleteCharAt(updateContent.length() - 1);
        }
        //全部信息未修改,则不用记录日志
        if (!updateContent.toString().endsWith("修改供货单")) {
            //11.记日志
            orderCommonService.saveOrderLog(
                    orderPO.getId(),
                    request.getOperator(),
                    request.getOrderOwnerName(),
                    supplyOrderPO.getSupplyOrderCode(),
                    updateContent.toString()
            );
        }

        //重新统计报表
        try {
            //12.加入到统计报表队列中
            stringRedisTemplate.convertAndSend(RedisKey.REPORT_STATISTICS, orderPO.getOrderCode());
        } catch (Exception e) {
            log.error("加入到统计报表队列异常,error:", e);
        }

        return Response.success();
    }

    /**
     * 比较两个床型信息列表是否一致，不考虑对象在列表中的顺序
     *
     * @param oldBedInfos 旧床型信息列表
     * @param newBedInfos 新床型信息列表
     * @return 两个列表是否一致
     */
    public static boolean isBedInfosEqual(List<BedInfoDto> oldBedInfos, List<BedInfoDto> newBedInfos) {
        // 如果两个列表都为空，认为相等
        if (oldBedInfos == null && newBedInfos == null) {
            return true;
        }

        // 如果只有一个列表为空，认为不相等
        if (oldBedInfos == null || newBedInfos == null) {
            return false;
        }

        // 检查列表大小是否相同
        if (oldBedInfos.size() != newBedInfos.size()) {
            return false;
        }

        // 创建新列表的副本，用于匹配过程中移除已匹配的元素
        List<BedInfoDto> newBedInfosCopy = new ArrayList<>(newBedInfos);

        // 对每个旧列表中的元素，在新列表中查找匹配项
        for (BedInfoDto oldBed : oldBedInfos) {
            boolean found = false;

            // 遍历新列表副本，查找匹配的元素
            for (int i = 0; i < newBedInfosCopy.size(); i++) {
                BedInfoDto newBed = newBedInfosCopy.get(i);

                // 比较两个BedInfoDto对象是否匹配
                if (isSameBedInfo(oldBed, newBed)) {
                    // 找到匹配项，从副本中移除
                    newBedInfosCopy.remove(i);
                    found = true;
                    break;
                }
            }

            // 如果没有找到匹配项，说明列表不相等
            if (!found) {
                return false;
            }
        }

        // 如果所有元素都匹配上，则两个列表相等
        return newBedInfosCopy.isEmpty();
    }

    /**
     * 比较两个床型信息对象是否相同
     *
     * @param bed1 第一个床型信息对象
     * @param bed2 第二个床型信息对象
     * @return 两个对象是否相同
     */
    private static boolean isSameBedInfo(BedInfoDto bed1, BedInfoDto bed2) {
        // 处理null情况
        if (bed1 == null && bed2 == null) {
            return true;
        }

        if (bed1 == null || bed2 == null) {
            return false;
        }

        // 比较bedTypeName
        if (!Objects.equals(bed1.getBedTypeName(), bed2.getBedTypeName())) {
            return false;
        }

        // 比较bedTypeCode
        if (!Objects.equals(bed1.getBedTypeCode(), bed2.getBedTypeCode())) {
            return false;
        }

        // 比较bedNum，如果为空则默认为1
        Integer bedNum1 = bed1.getBedNum() == null ? 1 : bed1.getBedNum();
        Integer bedNum2 = bed2.getBedNum() == null ? 1 : bed2.getBedNum();

        return Objects.equals(bedNum1, bedNum2);
    }

}
