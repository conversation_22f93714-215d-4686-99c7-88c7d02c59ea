package com.tiangong.order.service;

import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.dto.order.request.QueryOrderStatisticsDTOB2BRequest;
import com.tiangong.dto.order.response.OrderStatisticsDTOB2BResponse;
import com.tiangong.fuzzyquery.dto.FuzzySupplierDTO;
import com.tiangong.order.domain.OrderPO;
import com.tiangong.order.remote.request.*;
import com.tiangong.order.remote.response.*;
import com.tiangong.organization.remote.dto.ContactSupplierDTO;

import java.util.List;
import java.util.Map;

public interface OrderQueryService {

    /**
     * 查询订单列表
     */
    PaginationSupportDTO<OrderSimpleDTO> queryOrderList(QueryOrderListDTO request);

    /**
     * 查询订单列表统计
     */
    OrderStatisticsDTO queryOrderStatistics(QueryOrderStatisticsDTO request);

    /**
     * 查询订单统计--B2B
     */
    OrderStatisticsDTOB2BResponse queryOrderStatisticsB2B(QueryOrderStatisticsDTOB2BRequest request);

    /**
     * 查询订单详情
     */
    OrderDTO queryOrderDetail(OrderCodeDTO request);

    /**
     * 查询订单备注
     */
    List<OrderRemarkDTO> queryOrderRemark(QueryOrderRemarkDTO request);

    /**
     * 查询订单日志
     */
    List<OrderLogDTO> queryOrderLog(OrderIdDTO request);

    /**
     * 查询订单申请
     */
    List<OrderRequestDTO> queryOrderRequest(OrderIdDTO request);

    /**
     * 查询订单确认信息
     */
    String queryConfirmOrderInfo(QueryConfirmOrderInfoDTO request);

    /**
     * 查询订单价格明细
     */
    List<PriceResponseDTO> queryOrderPriceItem(OrderIdDTO request);

    /**
     * 供货单预览
     */
    SupplyOrderPreviewDTO previewSupplyOrder(SupplyOrderIdDTO request);

    /**
     * 查询供货单结果
     */
    SupplyResultDTO querySupplyOrderResult(SupplyOrderIdDTO request);

    /**
     * 查询供货单产品详情
     */
    SupplyProductDetailDTO querySupplyProduct(SupplyProductIdDTO request);

    /**
     * 查询酒店的供应商
     */
    List<FuzzySupplierDTO> querySupplier(Map<String, String> requestMap);

    /**
     * 查询订单发票
     */
    OrderInvoiceDTO queryOrderInvoice(QueryOrderInvoiceDTO queryOrderInvoiceDTO);

    /**
     * 根据订单号查找订单的基本信息
     */
    OrderPO queryOrderBasicInfoByOrderCode(String orderCode);

    /**
     * （对外提供的fegin接口）查询订单信息
     * @param orderId 客户编码
     * @return orderDTO
     */
    OrderDTO queryContactOrder(Integer orderId);
}
