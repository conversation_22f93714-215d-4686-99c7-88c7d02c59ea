package com.tiangong.order.service.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.tiangong.common.Constant;
import com.tiangong.common.Response;
import com.tiangong.dis.dto.HotelInfoReq;
import com.tiangong.dis.dto.ProductRestrictDTO;
import com.tiangong.dto.common.CompanyDTO;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.dto.common.TipsDTO;
import com.tiangong.dto.common.TipsResp;
import com.tiangong.dto.hotel.*;
import com.tiangong.dto.hotel.base.HotelInfoCollectionDTO;
import com.tiangong.dto.hotel.base.HotelInfoCollectionReq;
import com.tiangong.dto.order.OrderGuestDTO;
import com.tiangong.dto.order.OrderRoomGuestDTO;
import com.tiangong.dto.order.request.QueryOrderStatisticsDTOB2BRequest;
import com.tiangong.dto.order.response.OrderFeeDTO;
import com.tiangong.dto.order.response.OrderFeeDetailDTO;
import com.tiangong.dto.order.response.OrderStatisticsDTOB2BResponse;
import com.tiangong.dto.order.response.TaxDTO;
import com.tiangong.dto.product.response.TipInfosDTO;
import com.tiangong.dto.product.response.TipsDetail;
import com.tiangong.enums.*;
import com.tiangong.finance.remote.statement.ProtocolOrderSettlementCostRemote;
import com.tiangong.finance.remote.statement.request.ProtocolOrderSettlementCostVO;
import com.tiangong.finance.remote.statement.response.ProtocolOrderSettlementCostResponseDTO;
import com.tiangong.fuzzyquery.dto.FuzzySupplierDTO;
import com.tiangong.hotel.remote.HotelRemote;
import com.tiangong.keys.RedisKey;
import com.tiangong.order.convert.ProductSalePriceConvert;
import com.tiangong.order.domain.*;
import com.tiangong.order.domain.req.OrderChangeWordOrderTaskReq;
import com.tiangong.order.domain.resp.OrderChangeWordOrderTaskResp;
import com.tiangong.order.dto.OrderRateDto;
import com.tiangong.order.enums.AdditionalChargesTypeEnum;
import com.tiangong.order.enums.ConfirmationStatusEnum;
import com.tiangong.order.enums.OrderInvoiceErrorTypeEnum;
import com.tiangong.order.mapper.*;
import com.tiangong.order.remote.dto.OrderCheckInfoDTO;
import com.tiangong.order.remote.dto.OrderRestrictDTO;
import com.tiangong.order.remote.request.*;
import com.tiangong.order.remote.response.*;
import com.tiangong.order.service.OrderChangeWordOrderTaskService;
import com.tiangong.order.service.OrderQueryService;
import com.tiangong.order.service.OrderService;
import com.tiangong.order.service.common.MongodbCommonService;
import com.tiangong.order.util.StringTemplateUtil;
import com.tiangong.organization.remote.AgentRemote;
import com.tiangong.organization.remote.CompanyRemote;
import com.tiangong.organization.remote.TipsRemote;
import com.tiangong.organization.remote.dto.AgentAccountConfig;
import com.tiangong.organization.remote.dto.CompanyAddDTO;
import com.tiangong.organization.remote.dto.CompanySelectDTO;
import com.tiangong.product.dto.ProductDTO;
import com.tiangong.product.remote.ProductRemote;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.settle.remote.SettleOrderRemote;
import com.tiangong.settle.remote.SettleServerRemote;
import com.tiangong.settle.req.SettleOrderReq;
import com.tiangong.settle.req.TgHotelConfigReq;
import com.tiangong.settle.resp.SettleOrderResp;
import com.tiangong.util.CollUtilX;
import com.tiangong.util.CommonTgUtils;
import com.tiangong.util.DateUtilX;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toMap;

@Service
@Slf4j
public class OrderQueryServiceImpl implements OrderQueryService {

    @Autowired
    private OrderMapper orderMapper;

    @Autowired
    private OrderExtendMapper orderExtendMapper;

    @Autowired
    private OrderConfirmRecordMapper orderConfirmRecordMapper;

    @Autowired
    private OrderProductPriceMapper orderProductPriceMapper;

    @Autowired
    private OrderAttachmentMapper orderAttachmentMapper;

    @Autowired
    private SupplyOrderMapper supplyOrderMapper;

    @Autowired
    private SupplyProductMapper supplyProductMapper;

    @Autowired
    private SupplyProductPriceMapper supplyProductPriceMapper;

    @Autowired
    private GuestMapper guestMapper;

    @Autowired
    private OrderRequestMapper orderRequestMapper;

    @Autowired
    private OrderRemarkMapper orderRemarkMapper;

    @Autowired
    private OrderLogMapper orderLogMapper;

    @Autowired
    private SupplyRequestMapper supplyRequestMapper;

    @Autowired
    private SupplyAttachmentMapper supplyAttachmentMapper;

    @Autowired
    private HotelRemote hotelRemote;

    @Autowired
    private SupplyOrderFinanceMapper supplyOrderFinanceMapper;

    @Autowired
    private OrderFinanceMapper orderFinanceMapper;

    @Autowired
    private OrderInvoiceMapper orderInvoiceMapper;

    @Autowired
    private OrderCheckInfoMapper orderCheckInfoMapper;

    @Autowired
    private OrderRestrictMapper orderRestrictMapper;

    @Autowired
    private MongodbCommonService mongodbCommonService;

    @Autowired
    private CompanyRemote companyRemote;

    @Autowired
    private SettleServerRemote settleServerRemote;

    @Autowired
    private SettleOrderRemote settleOrderRemote;

    @Autowired
    private OrderService orderService;

    @Autowired
    private TipsRemote tipsRemote;

    @Autowired
    private SupplyOrderAmtLogMapper supplyOrderAmtLogMapper;

    @Autowired
    private OrderProductTipMapper orderProductTipMapper;

    @Autowired
    private OrderChangeWordOrderTaskService orderChangeWordOrderTaskService;

    @Autowired
    private OrderChangeWordOrderTaskMapper orderChangeWordOrderTaskMapper;

    @Autowired
    private ProtocolOrderSettlementCostRemote protocolOrderSettleInfoRemote;

    @Autowired
    private OrderAdditionalChargesMapper orderAdditionalChargesMapper;

    @Autowired
    private SupplyOrderNonVccAutoBillInfoMapper supplyOrderNonVccAutoBillInfoMapper;

    public static void main(String[] args) {
        System.out.println(BigDecimal.ONE.divide(new BigDecimal("2.8345"),6,RoundingMode.HALF_UP));
    }
    @Override
    public PaginationSupportDTO<OrderSimpleDTO> queryOrderList(QueryOrderListDTO request) {
        log.info("查询传递的参数是：{}", request);
        PageHelper.startPage(request.getCurrentPage(), request.getPageSize());
        log.info("查询渠道是：" + request.getChannel());
        // 解析订单来源参数
        if (request.getOrderSource() != null) {
            if (OrderSourceEnum.MANUAL.key == request.getOrderSource()) {
                request.setIsManualOrder(1);
            } else if (OrderSourceEnum.GDP.key == request.getOrderSource()) {
                request.setChannelCode(ChannelEnum.B2B.key);
                request.setIsManualOrder(0);
            } else if (OrderSourceEnum.API.key == request.getOrderSource()) {
                request.setChannelCode(ChannelEnum.DHUB.key);
                request.setIsManualOrder(0);
            }
        }
        List<OrderSimpleDTO> list = orderMapper.queryOrderList(request);

        PageInfo<OrderSimpleDTO> page = new PageInfo<>(list);
        if (CollUtilX.isNotEmpty(list)) {
            for (OrderSimpleDTO orderSimpleDTO : list) {
                if (orderSimpleDTO.getSaleCurrency() != null) {
                    orderSimpleDTO.setCurrency(SettlementCurrencyEnum.getCodeByKey(String.valueOf(orderSimpleDTO.getSaleCurrency())));
                }

                if (null != orderSimpleDTO.getPayAtHotelCurrency()) {
                    orderSimpleDTO.setPayAtHotelCurrencyCode(SettlementCurrencyEnum.getCodeByKey(String.valueOf(orderSimpleDTO.getPayAtHotelCurrency())));
                }
            }

            // 组装响应对象
            for (OrderSimpleDTO orderSimpleDTO : list) {
                if (!org.apache.commons.lang3.StringUtils.isEmpty(request.getOperator()) && request.getOperator().equals(orderSimpleDTO.getLockName())) {
                    orderSimpleDTO.setLockName(null);
                }

                orderSimpleDTO.setNightQty(convertString(orderSimpleDTO.getNightQty()));

                // 0:无,1:取,2:修,3:二,4:今,5:即,6:退,7:帮,9:手,10:问,11:钟,12:零,13:VIP,14:快速处理,15:混支
                orderSimpleDTO.setOrderTagList(new ArrayList<>());
                if (Math.abs(DateUtilX.getDay(DateUtilX.stringToDate(orderSimpleDTO.getStartDate()), DateUtilX.stringToDate(DateUtilX.dateToString(new Date())))) < 1) {
                    orderSimpleDTO.getOrderTagList().add(String.valueOf(OrderTagListEnum.FOUR.key));
                }

                if (null != orderSimpleDTO.getModificationRequestStatus() && orderSimpleDTO.getModificationRequestStatus() > 0 && orderSimpleDTO.getModificationRequestStatus() < 4) {
                    orderSimpleDTO.getOrderTagList().add(String.valueOf(orderSimpleDTO.getModificationRequestStatus()));
                }

                if (null != orderSimpleDTO.getInstantConfimationStatus() && orderSimpleDTO.getInstantConfimationStatus() == 1) {
                    orderSimpleDTO.getOrderTagList().add(String.valueOf(OrderTagListEnum.FIVE.key));
                }

                if (null != orderSimpleDTO.getModificationRequestStatus() && orderSimpleDTO.getModificationRequestStatus() == 4) {
                    orderSimpleDTO.getOrderTagList().add(String.valueOf(OrderTagListEnum.SIX.key));
                }

                if (null != orderSimpleDTO.getIsSubstituted() && orderSimpleDTO.getIsSubstituted() == 2) {
                    orderSimpleDTO.getOrderTagList().add(String.valueOf(OrderTagListEnum.SEVEN.key));
                }

                if (StrUtilX.isEmpty(orderSimpleDTO.getOrderOwnerName())) {
                    orderSimpleDTO.setOrderOwnerName(Constant.SYSTEM);
                }
                if (null != orderSimpleDTO.getInstantConfimationStatus() && orderSimpleDTO.getIsManualOrder() == 1) {
                    orderSimpleDTO.getOrderTagList().add(String.valueOf(OrderTagListEnum.NINE.key));
                }
                if (null != orderSimpleDTO.getIsAbnormal() && orderSimpleDTO.getIsAbnormal() == 1) {
                    orderSimpleDTO.getOrderTagList().add(String.valueOf(OrderTagListEnum.TEN.key));
                }
                if (null != orderSimpleDTO.getHourly() && orderSimpleDTO.getHourly() == 1) {
                    orderSimpleDTO.getOrderTagList().add(String.valueOf(OrderTagListEnum.ELEVEN.key));
                }
                if (null != orderSimpleDTO.getIsEarlyMorningRoomOrder() && orderSimpleDTO.getIsEarlyMorningRoomOrder() == 1) {
                    orderSimpleDTO.getOrderTagList().add(String.valueOf(OrderTagListEnum.TWELVE.key));
                }
                if (StrUtilX.isNotEmpty(orderSimpleDTO.getIsVipOrder()) && orderSimpleDTO.getIsVipOrder().equals("1")) {
                    orderSimpleDTO.getOrderTagList().add(String.valueOf(OrderTagListEnum.THIRTEEN.key));
                }
                if (null != orderSimpleDTO.getQuickProcessingSwitch() && orderSimpleDTO.getQuickProcessingSwitch() == 1) {
                    orderSimpleDTO.getOrderTagList().add(String.valueOf(OrderTagListEnum.FOURTEEN.key));
                }
                if (null != orderSimpleDTO.getIsMixedPay() && orderSimpleDTO.getIsMixedPay() == 1) {
                    orderSimpleDTO.getOrderTagList().add(String.valueOf(OrderTagListEnum.FIFTEEN.key));
                }
                if (null != orderSimpleDTO.getIsRefund() && orderSimpleDTO.getIsRefund() == 1) {
                    orderSimpleDTO.getOrderTagList().add(String.valueOf(OrderTagListEnum.SIXTEEN.key));
                }
                if (StrUtilX.isNotEmpty(orderSimpleDTO.getTaskOrderCode())) {
                    orderSimpleDTO.getOrderTagList().add(String.valueOf(OrderTagListEnum.SIX.key));
                }

                // 设置供货单状态，状态优先展示顺序：待确认、已确认、已完成、已取消
                if (CollUtilX.isNotEmpty(orderSimpleDTO.getSupplyOrderInfos())) {
                    if (orderSimpleDTO.getSupplyOrderInfos().size() == 1) {
                        SupplyOrderInfo supplyOrderInfo = orderSimpleDTO.getSupplyOrderInfos().get(0);
                        orderSimpleDTO.setSupplyOrderConfirmationStatus(supplyOrderInfo.getConfirmationStatus());
                        orderSimpleDTO.setBaseCurrency(supplyOrderInfo.getBaseCurrency());
                    } else {
                        for (SupplyOrderInfo supplyOrderInfo : orderSimpleDTO.getSupplyOrderInfos()) {
                            if (supplyOrderInfo.getConfirmationStatus() == null || supplyOrderInfo.getConfirmationStatus().equals(ConfirmationStatusEnum.UNCONFIRM.key)) {
                                orderSimpleDTO.setSupplyOrderConfirmationStatus(ConfirmationStatusEnum.UNCONFIRM.key);
                                orderSimpleDTO.setBaseCurrency(supplyOrderInfo.getBaseCurrency());
                                break;
                            }
                        }
                        if (orderSimpleDTO.getSupplyOrderConfirmationStatus() == null) {
                            for (SupplyOrderInfo supplyOrderInfo : orderSimpleDTO.getSupplyOrderInfos()) {
                                if (supplyOrderInfo.getConfirmationStatus().equals(ConfirmationStatusEnum.CONFIRMED.key)) {
                                    orderSimpleDTO.setSupplyOrderConfirmationStatus(ConfirmationStatusEnum.CONFIRMED.key);
                                    orderSimpleDTO.setBaseCurrency(supplyOrderInfo.getBaseCurrency());
                                    break;
                                }
                            }
                        }
                        if (orderSimpleDTO.getSupplyOrderConfirmationStatus() == null) {
                            for (SupplyOrderInfo supplyOrderInfo : orderSimpleDTO.getSupplyOrderInfos()) {
                                if (supplyOrderInfo.getConfirmationStatus().equals(ConfirmationStatusEnum.DONE.key)) {
                                    orderSimpleDTO.setSupplyOrderConfirmationStatus(ConfirmationStatusEnum.DONE.key);
                                    orderSimpleDTO.setBaseCurrency(supplyOrderInfo.getBaseCurrency());
                                    break;
                                }
                            }
                        }
                        if (orderSimpleDTO.getSupplyOrderConfirmationStatus() == null) {
                            orderSimpleDTO.setSupplyOrderConfirmationStatus(ConfirmationStatusEnum.CANCELED.key);
                            SupplyOrderInfo supplyOrderInfo = orderSimpleDTO.getSupplyOrderInfos().get(0);
                            orderSimpleDTO.setBaseCurrency(supplyOrderInfo.getBaseCurrency());
                        }
                    }
                    BigDecimal supplyOrderAmt = BigDecimal.ZERO;
                    for (SupplyOrderInfo supplyOrderInfo : orderSimpleDTO.getSupplyOrderInfos()) {
                        if (supplyOrderInfo.getSupplyOrderAmt() != null) {
                            supplyOrderAmt = supplyOrderAmt.add(supplyOrderInfo.getSupplyOrderAmt());
                        }
                    }
                    orderSimpleDTO.setSupplyOrderAmt(supplyOrderAmt);
                }

                if (orderSimpleDTO.getIsManualOrder() != null && orderSimpleDTO.getIsManualOrder() == 1) {
                    orderSimpleDTO.setOrderSource(OrderSourceEnum.MANUAL.key);
                } else if (StrUtilX.isNotEmpty(orderSimpleDTO.getChannelCode()) && ChannelEnum.B2B.key.equals(orderSimpleDTO.getChannelCode())) {
                    orderSimpleDTO.setOrderSource(OrderSourceEnum.GDP.key);
                } else if (StrUtilX.isNotEmpty(orderSimpleDTO.getChannelCode()) && ChannelEnum.DHUB.key.equals(orderSimpleDTO.getChannelCode())) {
                    orderSimpleDTO.setOrderSource(OrderSourceEnum.API.key);
                }
            }
        }

        PaginationSupportDTO<OrderSimpleDTO> paginationSupport = new PaginationSupportDTO<>();
        paginationSupport.setItemList(list);
        paginationSupport.setPageSize(page.getPageSize());
        paginationSupport.setTotalCount(page.getTotal());
        paginationSupport.setTotalPage(page.getPages());
        paginationSupport.setCurrentPage(page.getPageNum());
        return paginationSupport;
    }

    /**
     * 转换字符串为整数
     * 如果存在效数则返回原始字符串
     */
    public static String convertString(String str) {
        // 尝试解析为浮点数
        double value = Double.parseDouble(str);
        // 检查值是否为整数
        if (value == Math.floor(value) && !Double.isInfinite(value)) {
            return String.valueOf((int) value); // 转换为整数并返回字符串
        }
        return str; // 返回原始字符串
    }

    @Override
    public OrderStatisticsDTO queryOrderStatistics(QueryOrderStatisticsDTO request) {
        // 获取未处理订单数、未处理供货单数、我的订单、标星订单数
        OrderStatisticsDTO orderStatisticsDTO = orderMapper.queryOrderStatistics(request);
        if (orderStatisticsDTO == null) {
            orderStatisticsDTO = new OrderStatisticsDTO();
        }

        // 今日新单
        Example todayNewOrderQtyExample = new Example(OrderPO.class);
        Example.Criteria todayNewOrderQtyCriteria = todayNewOrderQtyExample.createCriteria();
        todayNewOrderQtyCriteria.andEqualTo("companyCode", request.getCompanyCode());
        todayNewOrderQtyCriteria.andBetween("createdDt", DateUtilX.dateToString(new Date()), DateUtilX.dateToString(new Date()) + " 23:59:59");
        todayNewOrderQtyCriteria.andIsNull("orderOwnerUser");
        int todayNewOrderQty = orderMapper.selectCountByExample(todayNewOrderQtyExample);

        // 今日入住订单数
        Example checkInTodayOrderQtyExample = new Example(OrderPO.class);
        Example.Criteria checkInTodayOrderQtyCriteria = checkInTodayOrderQtyExample.createCriteria();
        checkInTodayOrderQtyCriteria.andEqualTo("companyCode", request.getCompanyCode());
        checkInTodayOrderQtyCriteria.andEqualTo("startDate", DateUtilX.dateToString(new Date()));
        checkInTodayOrderQtyCriteria.andEqualTo("orderConfirmationStatus", 1);
        int checkInTodayOrderQty = orderMapper.selectCountByExample(checkInTodayOrderQtyExample);

        // 明日入住订单数
        Example checkInTomorrowOrderQtyExample = new Example(OrderPO.class);
        Example.Criteria checkInTomorrowOrderQtyCriteria = checkInTomorrowOrderQtyExample.createCriteria();
        checkInTomorrowOrderQtyCriteria.andEqualTo("companyCode", request.getCompanyCode());
        checkInTomorrowOrderQtyCriteria.andEqualTo("startDate", DateUtilX.dateToString(DateUtilX.getDate(new Date(), 1, 0)));
        checkInTomorrowOrderQtyCriteria.andEqualTo("orderConfirmationStatus", 1);
        int checkInTomorrowOrderQty = orderMapper.selectCountByExample(checkInTomorrowOrderQtyExample);
        // 问题单数
        Example abnormalOrderQtyExample = new Example(OrderPO.class);
        Example.Criteria abnormalOrderQtyCriteria = abnormalOrderQtyExample.createCriteria();
        abnormalOrderQtyCriteria.andEqualTo("companyCode", request.getCompanyCode());
        abnormalOrderQtyCriteria.andEqualTo("isAbnormal", 1);
        int abnormalOrderQty = orderMapper.selectCountByExample(abnormalOrderQtyExample);

        // 取消申请数
        Example cancelledRequestQtyExample = new Example(OrderPO.class);
        Example.Criteria cancelledRequestQtyCriteria = cancelledRequestQtyExample.createCriteria();
        cancelledRequestQtyCriteria.andEqualTo("companyCode", request.getCompanyCode());
        cancelledRequestQtyCriteria.andEqualTo("modificationStatus", 1);
        int cancelledRequestQty = orderMapper.selectCountByExample(cancelledRequestQtyExample);
        OrderRequestCountDTO orderRequestCountDTO = orderRequestMapper.queryOrderRequestStatistics(request);
        if (orderRequestCountDTO != null) {
            orderStatisticsDTO.setCancelledRequestQty(orderRequestCountDTO.getCancelCount());
        }

        // 退房任务数量
        Integer checkoutTaskQty = orderChangeWordOrderTaskMapper.selectCount(new QueryWrapper<OrderChangeWordOrderTaskPO>().notIn("status", ChangeWordOrderTaskStatusEnum.FINISHED.getKey()));

        // 即时确认订单数
        Example instantConfimationQtyExample = new Example(OrderPO.class);
        instantConfimationQtyExample.createCriteria().andEqualTo("companyCode", request.getCompanyCode()).andEqualTo("instantConfirmationStatus", 1);
        int instantConfimationQty = orderMapper.selectCountByExample(instantConfimationQtyExample);
        orderStatisticsDTO.setTodayNewOrderQty(todayNewOrderQty);
        orderStatisticsDTO.setCheckInTodayOrderQty(checkInTodayOrderQty);
        orderStatisticsDTO.setCancelledRequestQty(cancelledRequestQty);
        orderStatisticsDTO.setCheckInTomorrowOrderQty(checkInTomorrowOrderQty);
        orderStatisticsDTO.setInstantConfimationQty(instantConfimationQty);
        orderStatisticsDTO.setAbnormalOrderQty(abnormalOrderQty);
        orderStatisticsDTO.setCheckoutTaskQty(checkoutTaskQty);

        List<Integer> checkInDetailAbnormalNum = supplyOrderMapper.selectCheckInDetailAbnormalNum();
        if (checkInDetailAbnormalNum != null && checkInDetailAbnormalNum.size() > 0) {
            orderStatisticsDTO.setIsCheckInDetailAbnormal(checkInDetailAbnormalNum.size());
        } else {
            orderStatisticsDTO.setIsCheckInDetailAbnormal(0);
        }

        return orderStatisticsDTO;
    }

    @Override
    public OrderStatisticsDTOB2BResponse queryOrderStatisticsB2B(QueryOrderStatisticsDTOB2BRequest request) {
        //获取未处理订单数、未处理供货单数、我的订单、标星订单数
        OrderStatisticsDTOB2BResponse orderStatisticsDTO = orderMapper.queryOrderStatisticsB2B(request);

        Integer cancelCount = orderMapper.queryCancelOrderStatisticsB2B(request);
        orderStatisticsDTO.setCancelledRequestQty(cancelCount);

        return orderStatisticsDTO;
    }

    @Override
    public OrderDTO queryOrderDetail(OrderCodeDTO request) {
        OrderPO orderQuery = new OrderPO();
        if (StrUtilX.isNotEmpty(request.getOrderCode())) {
            orderQuery.setOrderCode(request.getOrderCode());
        }
        if (StrUtilX.isNotEmpty(request.getCoOrderCode())) {
            orderQuery.setChannelOrderCode(request.getCoOrderCode());
        }
        if (StrUtilX.isNotEmpty(request.getAgentCode())) {
            orderQuery.setAgentCode(request.getAgentCode());
        }
        if (StrUtilX.isNotEmpty(request.getChannelCode())) {
            orderQuery.setChannelCode(request.getChannelCode());
        }

        OrderPO orderPO = orderMapper.selectOne(orderQuery);
        if (orderPO == null) {
            return null;
        }
        //获取分销商数据
        String agent = (String) RedisTemplateX.hashGet(RedisKey.AGENT_ACCOUNT_CONFIG, orderPO.getAgentCode());
        AgentAccountConfig agentAccountConfig = JSON.parseObject(agent, AgentAccountConfig.class);

        OrderExtendPO orderExtendPO = null;
        OrderExtendPO orderExtendQuery = new OrderExtendPO();
        orderExtendQuery.setOrderId(orderPO.getId());
        List<OrderExtendPO> orderExtendPOList = orderExtendMapper.select(orderExtendQuery);
        if (CollUtilX.isNotEmpty(orderExtendPOList)) {
            orderExtendPO = orderExtendPOList.get(0);
        }
        Integer SettlementStatus = orderMapper.querySettlementStatus(orderPO.getId());
        OrderDTO orderDTO = ProductSalePriceConvert.INSTANCE.OrderDTOConvert(orderPO);
        orderDTO.setContactTel(orderPO.getContactPhone());
        orderDTO.setSettlementStatus(SettlementStatus);
        orderDTO.setOrderId(orderPO.getId());
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        orderDTO.setStartDate(sdf.format(orderPO.getStartDate()));
        orderDTO.setEndDate(sdf.format(orderPO.getEndDate()));
        SimpleDateFormat sdf11 = new SimpleDateFormat("HH:mm:ss");
        if (orderPO.getStartTime() != null) {
            orderDTO.setStartTime(sdf11.format(orderPO.getStartTime()));
            orderDTO.setEndTime(sdf11.format(orderPO.getEndTime()));
        }
        orderDTO.setCreateTime(orderPO.getCreatedDt());

        if (orderPO.getPushOrderStatus() != null && orderPO.getPushOrderStatus() == 1) {
            orderDTO.setPushOrderStatusContent("订单状态推送异常：订单号" + orderPO.getOrderCode() + "向下游传输订单状态异常，请及时手动推送订单状态，并及时和下游客户确认订单状态是否正常接收。");
        }

        orderDTO.setSupplyOrderTotalAmt(orderDTO.getOrderAmt().subtract(orderDTO.getProfit()));
        if (orderDTO.getPayAtHotelAgentCurrencyFee() != null && orderDTO.getOrderAmt().compareTo(BigDecimal.ZERO) != 0) {
            orderDTO.setOrderFinallyAmt(orderDTO.getOrderAmt().add(orderDTO.getPayAtHotelAgentCurrencyFee()));
        } else {
            orderDTO.setOrderFinallyAmt(orderDTO.getOrderAmt());
        }
        orderDTO.setNightQty(String.valueOf(DateUtilX.getDay(orderPO.getStartDate(), orderPO.getEndDate())));
        if (orderDTO.getHourly() == 1) {
            orderDTO.setNightQty(String.valueOf(0.5));//前端处理
        }
        if (null == orderDTO.getOrderOwnerName()) {
            orderDTO.setOrderOwnerName(Constant.SYSTEM);
        }

        if (StrUtilX.isNotEmpty(orderDTO.getEarliestArriveTime()) && StrUtilX.isNotEmpty(orderDTO.getLatestArriveTime())) {
            try {
                orderDTO.setEarliestArriveTime(orderDTO.getEarliestArriveTime());
                orderDTO.setLatestArriveTime(orderDTO.getLatestArriveTime());
            } catch (Exception e) {
                log.error("queryOrderDetail orderDTO:{} Exception:", JSONObject.toJSONString(orderDTO), e);
            }
        }

        //订单金额换算
        if (null == orderPO.getSaleCurrency()) {
            orderDTO.setSaleCurrency(0);
        }

        orderDTO.setSaleCurrency(orderPO.getSaleCurrency());
        orderDTO.setSaleRate(orderPO.getSaleRate());

        if (StrUtilX.isNotEmpty(orderDTO.getLockName())) {
            orderDTO.setLockStatus(1);
        } else {
            orderDTO.setLockStatus(0);
        }
        orderDTO.setModificationRequestStatus(orderPO.getModificationStatus());

        List<OrderRequestDTO> orderRequests = orderMapper.queryOrderRequest(orderPO.getId());


        int isRefused = 0;
        //确认取消单时，状态会自动改成已取消。判断是否拒单，就得有取消申请，并且同意该申请
        if (orderPO.getOrderConfirmationStatus() == 2 && !CollUtilX.isNotEmpty(orderRequests)) {
            isRefused = 1;
        } else if (orderPO.getOrderConfirmationStatus() == 2 && CollUtilX.isNotEmpty(orderRequests)) {
            isRefused = 1;
            for (OrderRequestDTO orderRequestDTO : orderRequests) {
                if (orderRequestDTO.getRequestType() == 0 && orderRequestDTO.getHandledResult() == 1) {
                    isRefused = 0;
                    break;
                }
            }
        }
        orderDTO.setIsRefused(isRefused);

        //如果是月结客户，显示其业务联系人
//        if (orderPO.getSettlementType() != null && orderPO.getSettlementType() == 0) {
//            ContactSupplierDTO contactSupplierDTO = orderMapper.selectContact(orderPO.getAgentCode(), DeleteEnum.STATUS_EXIST.key);
//            if (!StringUtils.isEmpty(contactSupplierDTO)) {
//                orderDTO.setContactName(contactSupplierDTO.getContactName());
//                orderDTO.setContactTel(contactSupplierDTO.getContactTel());
//            }
//        }
        //查询以收未收，财务锁单状态
        OrderFinancePO orderFinancePO = new OrderFinancePO();
        orderFinancePO.setOrderId(orderPO.getId());
        OrderFinancePO orderFinancePO1 = orderFinanceMapper.selectOne(orderFinancePO);
        orderDTO.setUnconfirmedPaidAmt(orderFinancePO1.getUnconfirmedPaidAmt());
        orderDTO.setUnconfirmedReceivedAmt(orderFinancePO1.getUnconfirmedReceivedAmt());
        orderDTO.setUnreceivedAmt(orderFinancePO1.getUnreceivedAmt());
        orderDTO.setReceivedAmt(orderFinancePO1.getReceivedAmt());
        orderDTO.setFinanceLockStatus(orderFinancePO1.getFinanceLockStatus());
        orderDTO.setEarliestArriveTime(orderPO.getEarliestArriveTime());
        orderDTO.setLatestArriveTime(orderPO.getLatestArriveTime());
        // 查询条款
        if (null != orderExtendPO) {
            if (StrUtilX.isNotEmpty(orderExtendPO.getCancellationTerm())) {
//                String cancel = OrderCancellationConvert(orderExtendPO.getCancellationTerm(), orderPO.getStartDate());
                orderDTO.setCancellationTerm(orderExtendPO.getCancellationTerm());
            } else {
                orderDTO.setCancellationTerm("无");
            }
            orderDTO.setConfirmationCode(orderExtendPO.getConfirmationCode());
            orderDTO.setOtherTerm(orderExtendPO.getOtherTerm());
            orderDTO.setSpecialRequest(orderExtendPO.getSpecialRequest());
            orderDTO.setBedType(orderExtendPO.getBedType());
            //床型描述
            orderDTO.setBedInfoDesc(orderExtendPO.getBedInfoDesc());
            orderDTO.setIsShownOnSupplyOrder(orderExtendPO.getIsShowOnSupplyOrder());
            //设置订单交通方式
            orderDTO.setOrderTrafficJson(orderExtendPO.getOrderTrafficJson());
        }


        //查询确认人
//            Example orderConfirmRecordExample=new Example(OrderConfirmRecordPO.class);
//            orderConfirmRecordExample.setOrderByClause("id desc ");
//            Example.Criteria orderConfirmRecordCriteria = orderConfirmRecordExample.createCriteria();
//            orderConfirmRecordCriteria.andEqualTo("orderId",orderDTO.getOrderId());
        OrderConfirmRecordPO orderConfirmRecordPO = orderConfirmRecordMapper.queryConfirmingPerson(orderDTO.getOrderId());
        if (orderConfirmRecordPO != null) {
            orderDTO.setConfirmer(orderConfirmRecordPO.getCreatedBy());
            orderDTO.setConfirmTime(orderConfirmRecordPO.getCreatedDt());
            orderDTO.setConfirmTime(orderConfirmRecordPO.getCreatedDt());
        }

        //查询入住人
        GuestPO guestPO = new GuestPO();
        guestPO.setOrderId(orderPO.getId());
        //按间入住人
        Map<Integer, List<OrderGuestDTO>> guestMap = null;
        Map<Integer, String> guestStrMap = new HashMap<>();
        Map<Integer, String> guestQtyMap = new HashMap<>();
        List<GuestPO> orderGuestPOList = guestMapper.select(guestPO);
        //int adultQty = 0;
        StringBuffer childrenAge = new StringBuffer();
        StringBuilder roomGuestStr = null;
        if (CollUtilX.isNotEmpty(orderGuestPOList)) {
            List<OrderGuestDTO> orderGuestDTOList = ProductSalePriceConvert.INSTANCE.OrderGuestDTOConvert(orderGuestPOList);
            //将入住人手机号码和国籍号拼接一起输出
            if (CollectionUtils.isNotEmpty(orderGuestDTOList)) {
                for (OrderGuestDTO orderGuestDTO : orderGuestDTOList) {
                    if (StrUtilX.isNotEmpty(orderGuestDTO.getMobileNo()) && !orderGuestDTO.getMobileNo().contains("-")) {
                        orderGuestDTO.setMobileNo(orderGuestDTO.getCountryCode() + "-" + orderGuestDTO.getMobileNo());
                    }
                }
            }
            orderDTO.setGuestList(orderGuestDTOList);
            for (GuestPO po : orderGuestPOList) {
                if (StrUtilX.isEmpty(po.getName())) {
                    po.setName(po.getLastName() + "/" + po.getFirstName());
                }
                //adultQty = adultQty + po.getAdultQty();
                if (!org.apache.commons.lang3.StringUtils.isEmpty(po.getChildrenAge())) {
                    childrenAge.append(po.getChildrenAge()).append(",");
                }

                guestQtyMap.put(po.getRoomNumber(), po.getAdultQty() == null ? "0" : po.getAdultQty() + (null != po.getChildrenQty() ? "," + po.getChildrenQty() : ",0"));
            }

            guestMap = orderGuestDTOList.stream().collect(groupingBy(OrderGuestDTO::getRoomNumber));
            List<OrderRoomGuestDTO> roomGuestDTOS = new ArrayList<>();
            for (Integer roomNumber : guestMap.keySet()) {
                OrderRoomGuestDTO orderRoomGuestDTO = new OrderRoomGuestDTO();
                orderRoomGuestDTO.setAdultQty(guestMap.get(roomNumber).get(0).getAdultQty());
                if (StrUtilX.isNotEmpty(guestMap.get(roomNumber).get(0).getChildrenAge())) {
                    String[] ageArr = guestMap.get(roomNumber).get(0).getChildrenAge().split(",");
                    orderRoomGuestDTO.setChildrenQty(ageArr.length);
                }

                orderRoomGuestDTO.setChildrenAge(guestMap.get(roomNumber).get(0).getChildrenAge());
                orderRoomGuestDTO.setOrderId(guestMap.get(roomNumber).get(0).getOrderId());
                orderRoomGuestDTO.setRoomNumber(guestMap.get(roomNumber).get(0).getRoomNumber());
                roomGuestStr = new StringBuilder();
                for (OrderGuestDTO orderGuestDTO : guestMap.get(roomNumber)) {
                    roomGuestStr.append(orderGuestDTO.getName()).append(",");
                }
                guestStrMap.put(roomNumber, roomGuestStr.substring(0, roomGuestStr.length() - 1));
                orderRoomGuestDTO.setOrderGuestList(guestMap.get(roomNumber));
                roomGuestDTOS.add(orderRoomGuestDTO);
            }
            orderDTO.setRoomGuestList(roomGuestDTOS);
        }

        //查询订单价格明细
        Example orderProductPriceExample = new Example(OrderProductPricePO.class);
        Example.Criteria orderProductPriceCriteria = orderProductPriceExample.createCriteria();
        orderProductPriceCriteria.andEqualTo("orderId", orderDTO.getOrderId());
        List<OrderProductPricePO> orderProductPricePOList = orderProductPriceMapper.selectByExample(orderProductPriceExample);
        //费用按房间转Map
        Map<Integer, List<OrderProductPricePO>> feeMap = orderProductPricePOList.stream()
                .collect(Collectors.groupingBy(OrderProductPricePO::getRoomNumber));
        Map<String, PriceResponseDTO> priceMap = new HashMap<>();
        List<OrderFeeDTO> orderFeeDTOList = new ArrayList<>();
        //总税费
        TaxDTO totalTax = new TaxDTO();
        for (Integer roomNumber : feeMap.keySet()) {
            OrderFeeDTO orderFeeDTO = new OrderFeeDTO();
            orderFeeDTO.setRoomNumber(roomNumber.toString());
            orderFeeDTO.setStartDate(DateUtilX.dateToString(orderPO.getStartDate()));
            orderFeeDTO.setEndDate(DateUtilX.dateToString(orderPO.getEndDate()));
            orderFeeDTO.setOrderId(orderPO.getId());
            orderFeeDTO.setOrderCode(orderPO.getOrderCode());
            orderFeeDTO.setGuestList(guestStrMap.get(roomNumber));
            String[] guestQtyArr = guestQtyMap.get(roomNumber).split(",");
            if (guestQtyArr.length > 0) {
                orderFeeDTO.setAdultQty(Integer.valueOf(guestQtyArr[0]));
                if (guestQtyArr.length > 1) {
                    orderFeeDTO.setChildrenQty(Integer.valueOf(guestQtyArr[1]));
                }
            }

            //按次税费
            TaxDTO timesTaxDTO = new TaxDTO();
            List<OrderFeeDetailDTO> orderFeeDetailDTOList = new ArrayList<>();
            for (OrderProductPricePO orderProductPricePO : feeMap.get(roomNumber)) {
                if (null != orderProductPricePO.getSaleDate()) {
                    OrderFeeDetailDTO orderFeeDetailDTO = new OrderFeeDetailDTO();

                    orderFeeDetailDTO.setSaleDate(DateUtilX.dateToString(orderProductPricePO.getSaleDate()));
                    orderFeeDetailDTO.setCurrency(orderProductPricePO.getCurrency());
                    orderFeeDetailDTO.setSalePrice(CommonTgUtils.setScale(orderProductPricePO.getSalePrice(), agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType()));
                    orderFeeDetailDTO.setDiscount(CommonTgUtils.setScale(orderProductPricePO.getDiscount(), agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType()));
                    orderFeeDetailDTO.setRoomPrice(CommonTgUtils.setScale(orderProductPricePO.getRoomPrice(), agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType()));
                    //每日税费
                    orderFeeDetailDTO.setTaxList(assemblyTax(orderProductPricePO, agentAccountConfig));
                    if (orderProductPricePO.getExtraTaxFee() != null || orderProductPricePO.getTax() != null || orderProductPricePO.getSalesTax() != null) {
                        orderFeeDetailDTO.setTaxTotalAmount(CommonTgUtils.setScale(BigDecimal.ZERO.
                                        add(CommonTgUtils.formatBigDecimal(orderProductPricePO.getExtraTaxFee())).
                                        add(CommonTgUtils.formatBigDecimal(orderProductPricePO.getTax())).
                                        add(CommonTgUtils.formatBigDecimal(orderProductPricePO.getSalesTax()))
                                , agentAccountConfig.getDecimalPlaces()
                                , agentAccountConfig.getRoundingType()));
                    }

                    orderFeeDetailDTOList.add(orderFeeDetailDTO);

                    //设置按日价格
                    if (null == priceMap.get(orderFeeDetailDTO.getSaleDate())) {
                        PriceResponseDTO priceResponseDTO = new PriceResponseDTO();
                        priceResponseDTO.setSaleDate(orderFeeDetailDTO.getSaleDate());
                        priceResponseDTO.setSalePrice(orderProductPricePO.getSalePrice());
                        priceResponseDTO.setBreakfastNum(orderPO.getBreakfastQty());
                        if (orderFeeDetailDTO.getCurrency() == null) {
                            priceResponseDTO.setCurrency("");
                        } else {
                            String currency = SettlementCurrencyEnum.getCodeByKey(String.valueOf(orderFeeDetailDTO.getCurrency()));
                            if (StrUtilX.isEmpty(currency)) {
                                priceResponseDTO.setCurrency("");
                            } else {
                                priceResponseDTO.setCurrency(currency);
                            }
                        }
                        priceMap.put(orderFeeDetailDTO.getSaleDate(), priceResponseDTO);
                    } else {
                        priceMap.get(orderFeeDetailDTO.getSaleDate()).setSalePrice(priceMap.get(orderFeeDetailDTO.getSaleDate()).getSalePrice().add(orderProductPricePO.getSalePrice()));
                    }

                    //计算总税费
                    totalTax.setSalesTax(CommonTgUtils.formatBigDecimal(totalTax.getSalesTax()).add(CommonTgUtils.formatBigDecimal(orderProductPricePO.getSalesTax())));
                    totalTax.setTax(CommonTgUtils.formatBigDecimal(totalTax.getTax()).add(CommonTgUtils.formatBigDecimal(orderProductPricePO.getTax())));
                } else {
                    timesTaxDTO = assemblyTax(orderProductPricePO, agentAccountConfig);
                    orderFeeDTO.setPayAtHotelFee(orderProductPricePO.getPayAtHotelFee());
                    orderFeeDTO.setPayAtHotelCurrency(orderProductPricePO.getPayAtHotelCurrency());

                    //计算总税费
                    totalTax.setSalesTax(CommonTgUtils.formatBigDecimal(totalTax.getSalesTax()).add(CommonTgUtils.formatBigDecimal(orderProductPricePO.getSalesTax())));
                    totalTax.setTax(CommonTgUtils.formatBigDecimal(totalTax.getTax()).add(CommonTgUtils.formatBigDecimal(orderProductPricePO.getTax())));
                    totalTax.setExtraTaxFee(CommonTgUtils.formatBigDecimal(totalTax.getExtraTaxFee())
                            .add(CommonTgUtils.formatBigDecimal(orderProductPricePO.getExtraTaxFee()))
                    );
                }
            }

            //按次税费设置在首日,计算每日价格
            for (OrderFeeDetailDTO orderFeeDetailDTO : orderFeeDetailDTOList) {
                if (orderFeeDetailDTO.getSaleDate().equals(orderDTO.getStartDate())) {
                    orderFeeDetailDTO.setTimesTaxFeeList(timesTaxDTO);
                    if (timesTaxDTO.getExtraTaxFee() != null || timesTaxDTO.getTax() != null || timesTaxDTO.getSalesTax() != null) {
                        orderFeeDetailDTO.setTimesTaxFee(BigDecimal.ZERO.
                                add(CommonTgUtils.formatBigDecimal(timesTaxDTO.getExtraTaxFee())).
                                add(CommonTgUtils.formatBigDecimal(timesTaxDTO.getTax())).
                                add(CommonTgUtils.formatBigDecimal(timesTaxDTO.getSalesTax())));
                    }
                }

            }
            orderFeeDTO.setPriceList(orderFeeDetailDTOList);
            orderFeeDTOList.add(orderFeeDTO);
        }

        //处理税费
        BigDecimal extraTaxFee = BigDecimal.ZERO;
        for (OrderFeeDTO orderFeeDTO : orderFeeDTOList) {
            for (OrderFeeDetailDTO orderFeeDetailDTO : orderFeeDTO.getPriceList()) {
                extraTaxFee = extraTaxFee.add(CommonTgUtils.formatBigDecimal(orderFeeDetailDTO.getTaxList().getExtraTaxFee()));
            }
        }

        totalTax.setExtraTaxFee(CommonTgUtils.setScale(extraTaxFee, agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType()));
        totalTax.setSalesTax(CommonTgUtils.setScale(totalTax.getSalesTax(), agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType()));
        totalTax.setTax(CommonTgUtils.setScale(totalTax.getTax(), agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType()));
        orderDTO.setTotalTax(totalTax);
        orderDTO.setFeeList(orderFeeDTOList);
        orderDTO.setPayAtHotelFee(orderPO.getPayAtHotelFee());
        orderDTO.setPayAtHotelCurrency(orderPO.getPayAtHotelCurrency());

        String saleCurrency = SettlementCurrencyEnum.getCodeByKey(String.valueOf(orderDTO.getSaleCurrency()));
        if (StrUtilX.isNotEmpty(saleCurrency)) {
            orderDTO.setSaleCurrencyCode(saleCurrency);
        } else {
            orderDTO.setSaleCurrencyCode("");
        }
        String payAtHotelCurrency = SettlementCurrencyEnum.getCodeByKey(String.valueOf(orderDTO.getPayAtHotelCurrency()));
        if (StrUtilX.isNotEmpty(payAtHotelCurrency)) {
            orderDTO.setPayAtHotelCurrencyCode(payAtHotelCurrency);
        } else {
            orderDTO.setPayAtHotelCurrencyCode("");
        }

        //设置每日价格
        List<PriceResponseDTO> priceResponseDTOList = new ArrayList<>();
        List<Date> collect = priceMap.keySet().stream().map(DateUtilX::stringToDate).collect(Collectors.toList());
        // 时间排序
        collect.sort(Comparator.naturalOrder());
        for (Date saleDate : collect) {
            priceResponseDTOList.add(priceMap.get(DateUtilX.dateToString(saleDate)));
        }

        //每日价格需要除以间数
        for (PriceResponseDTO priceResponseDTO : priceResponseDTOList) {
            BigDecimal avgPrice = CommonTgUtils.divideSetScale(priceResponseDTO.getSalePrice(), new BigDecimal(orderDTO.getRoomQty()), agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType());
            priceResponseDTO.setSalePrice(avgPrice);
        }
        orderDTO.setSalePriceList(priceResponseDTOList);

        //查询订单附件
        OrderAttachmentPO orderAttachmentQuery = new OrderAttachmentPO();
        orderAttachmentQuery.setOrderId(orderDTO.getOrderId());
        orderAttachmentQuery.setActive(1);
        List<OrderAttachmentPO> orderAttachmentPOList = orderAttachmentMapper.select(orderAttachmentQuery);
        orderDTO.setOrderAttachmentList(new ArrayList<>());
        orderAttachmentPOList.forEach(orderAttachmentPO -> {
            orderDTO.getOrderAttachmentList().add(new OrderAttachmentDTO(
                    orderAttachmentPO.getId(),
                    orderAttachmentPO.getName(),
                    orderAttachmentPO.getUrl(),
                    orderAttachmentPO.getFileFormat(),
                    orderAttachmentPO.getFileSize()
            ));
        });

        // 查询退改任务
        OrderChangeWordOrderTaskReq taskReq = new OrderChangeWordOrderTaskReq();
        taskReq.setOrderCode(orderPO.getOrderCode());
        OrderChangeWordOrderTaskResp orderChangeWordOrderTaskResp = orderChangeWordOrderTaskService.queryOrderChangeWordOrderTaskInfo(taskReq);
        if (orderChangeWordOrderTaskResp != null) {
            OrderChangeWordOrderTaskDTO changeWordOrderTaskDTO = JSON.parseObject(JSONUtil.toJsonStr(orderChangeWordOrderTaskResp), new TypeReference<OrderChangeWordOrderTaskDTO>() {
            });
            orderDTO.setChangeWordOrderTaskDTO(changeWordOrderTaskDTO);
        }

        // 查询协议结算成本
        ProtocolOrderSettlementCostVO settleInfoDTO = new ProtocolOrderSettlementCostVO();
        settleInfoDTO.setOrderCode(orderPO.getOrderCode());
        Response<List<ProtocolOrderSettlementCostResponseDTO>> protocolOrderSettleResponse = protocolOrderSettleInfoRemote.queryProtocolOrderSettlementCostList(settleInfoDTO);
        if (protocolOrderSettleResponse.isSuccess()) {
            List<ProtocolOrderSettlementCostResponseDTO> protocolOrderSettleInfoList = protocolOrderSettleResponse.getModel();
            if (CollUtilX.isNotEmpty(protocolOrderSettleInfoList)) {
                List<ProtocolOrderSettleInfoDTO> protocolOrderSettleInfos = ProductSalePriceConvert.INSTANCE.protocolOrderSettleInfoDTOConvert(protocolOrderSettleInfoList);
                orderDTO.setProtocolOrderSettleInfos(protocolOrderSettleInfos);
            }
        }

        // 查询订单服务费
        OrderAdditionalChargesPO orderAdditionalChargesPO = orderAdditionalChargesMapper.selectOne(new QueryWrapper<OrderAdditionalChargesPO>().lambda()
                .eq(OrderAdditionalChargesPO::getOrderCode, orderDTO.getOrderCode())
                .eq(OrderAdditionalChargesPO::getAdditionalChargesType, AdditionalChargesTypeEnum.SERVICE_CHARGE.key));
        if (orderAdditionalChargesPO != null && orderAdditionalChargesPO.getAdditionalCharges() != null) {
            orderDTO.setServiceCharge(orderAdditionalChargesPO.getAdditionalCharges());
        }

        //查询供货单
        SupplyOrderPO supplyOrderQuery = new SupplyOrderPO();
        supplyOrderQuery.setOrderId(orderDTO.getOrderId());
        Example example = new Example(SupplyOrderPO.class);
        example.setOrderByClause("id DESC");
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("orderId", orderDTO.getOrderId());
        List<SupplyOrderPO> supplyOrderPOList = supplyOrderMapper.selectByExample(example);

        orderDTO.setSupplyOrderList(new ArrayList<>());
        BigDecimal supplysum = BigDecimal.ZERO;
        String supplyCode = null;
        List<SupplyOrderExceptionDTO> supplyOrderExceptionDTOList = new ArrayList<>();
        for (SupplyOrderPO supplyOrderPO : supplyOrderPOList) {
            if (supplyOrderPO.getCancelExceptionFlag() != null && supplyOrderPO.getCancelExceptionFlag() == 1) {
                SupplyOrderExceptionDTO supplyOrderExceptionDTO = new SupplyOrderExceptionDTO();
                supplyOrderExceptionDTO.setSupplyOrderId(supplyOrderPO.getId());
                supplyOrderExceptionDTO.setDesc(supplyOrderPO.getCancelExceptionContext());
                supplyOrderExceptionDTOList.add(supplyOrderExceptionDTO);
            }
            SupplyOrderDTO supplyOrderDTO = ProductSalePriceConvert.INSTANCE.supplyOrderDTOConvert(supplyOrderPO);
            if (org.apache.commons.lang3.StringUtils.isEmpty(supplyCode)) {
                supplyCode = supplyOrderPO.getSupplierCode();
            }
            //查询结算状态
            SupplyOrderFinancePO supplyOrderFinancePO = new SupplyOrderFinancePO();
            supplyOrderFinancePO.setSupplyOrderId(supplyOrderPO.getId());
            supplyOrderFinancePO.setFinanceType(StatementTypeEnum.SUPPLY_ORDER_AMT.key);
            SupplyOrderFinancePO supplyOrderFinancePO1 = supplyOrderFinanceMapper.selectOne(supplyOrderFinancePO);
            supplyOrderDTO.setSettlementStatus(supplyOrderFinancePO1.getSettlementStatus());
            supplyOrderDTO.setSupplyOrderId(supplyOrderPO.getId());
            supplyOrderDTO.setUnconfirmedPaidAmt(supplyOrderFinancePO1.getUnconfirmedPaidAmt());
            supplyOrderDTO.setUnconfirmedReceivedAmt(supplyOrderFinancePO1.getUnconfirmedReceivedAmt());
            supplyOrderDTO.setPaidAmt(supplyOrderFinancePO1.getPaidAmt());
            supplyOrderDTO.setUnpaidAmt(supplyOrderFinancePO1.getUnpaidAmt());
            supplyOrderDTO.setFinanceLockStatus(supplyOrderFinancePO1.getFinanceLockStatus());
            supplyOrderDTO.setBaseRate(supplyOrderPO.getRate());
            supplyOrderDTO.setSupplierToAgentRate(supplyOrderPO.getSupplierToAgentRate());
            supplyOrderDTO.setRoomNumbers(supplyOrderPO.getRoomNumbers());
            supplyOrderDTO.setConfirmationCode(supplyOrderPO.getConfirmationCode());
            if (StrUtilX.isNotEmpty(supplyOrderPO.getRoomNumbers())) {
                List<String> roomNumberList = StrUtilX.stringToList(supplyOrderPO.getRoomNumbers(), ",");
                supplyOrderDTO.setRoomNumberList(roomNumberList);
            }

            if (Objects.isNull(supplyOrderPO.getRate())) {
                supplyOrderDTO.setSupplyOrderOrgCurrencyAmt(supplyOrderDTO.getSupplyOrderAmt());
            } else {
                supplyOrderDTO.setSupplyOrderOrgCurrencyAmt(CommonTgUtils.set2UpScale(supplyOrderDTO.getSupplyOrderAmt().multiply(supplyOrderPO.getRate())));
            }

            //是否自助结算订单
            supplyOrderDTO.setIsSettle(0);
            try {
                TgHotelConfigReq tgHotelConfigReq = new TgHotelConfigReq();
                tgHotelConfigReq.setHotelId(supplyOrderPO.getHotelId());
                tgHotelConfigReq.setSupplierCode(supplyOrderPO.getSupplierCode());
                Response<Boolean> response = settleServerRemote.querySettleHotelConfigIsExists(tgHotelConfigReq);
                if (response.getModel()) {
                    supplyOrderDTO.setIsSettle(1);

                    // 查询自助结算订单付款时间
                    SettleOrderReq settleOrderReq = new SettleOrderReq();
                    settleOrderReq.setSupplyOrderCode(supplyOrderPO.getSupplyOrderCode());
                    Response<SettleOrderResp> settleOrderRespResponse = settleOrderRemote.findOne(settleOrderReq);
                    if (settleOrderRespResponse.isSuccess() && settleOrderRespResponse.getModel() != null) {
                        if (settleOrderRespResponse.getModel().getSettleDt() != null) {
                            supplyOrderDTO.setSettleDt(DateUtilX.dateToString(settleOrderRespResponse.getModel().getSettleDt()));
                        }
                    }
                }
            } catch (Exception e) {
                log.error("查询自助结算接口异常", e);
            }

            //是否是非VCC自助出账的供货单
            List<SupplyOrderNonVccAutoBillInfoPO> supplyOrderNonVccAutoBillInfoPOList = supplyOrderNonVccAutoBillInfoMapper.selectList(new QueryWrapper<SupplyOrderNonVccAutoBillInfoPO>().lambda()
                    .eq(SupplyOrderNonVccAutoBillInfoPO::getSupplyOrderId, supplyOrderPO.getId()));
            if (CollUtilX.isNotEmpty(supplyOrderNonVccAutoBillInfoPOList)) {
                List<SupplyOrderLabelDetails> supplyOrderLabelDetailsList = ProductSalePriceConvert.INSTANCE.supplyOrderLabelDetailsListConvert(supplyOrderNonVccAutoBillInfoPOList);
                supplyOrderDTO.setSupplyOrderLabelDetails(supplyOrderLabelDetailsList);
            }

            //供应商奖励
            orderDTO.setRewardAmt(BigDecimal.ZERO);
            supplyOrderFinancePO.setFinanceType(StatementTypeEnum.REWARD_AMT.key);
            SupplyOrderFinancePO supplyOrderFinanceReward = supplyOrderFinanceMapper.selectOne(supplyOrderFinancePO);
            SupplyOrderRewardDTO rewardDTO = new SupplyOrderRewardDTO();
            if (supplyOrderFinanceReward != null) {
//                BeanUtils.copyProperties(supplyOrderFinanceReward, rewardDTO);
                rewardDTO = ProductSalePriceConvert.INSTANCE.rewardDTOConvert(supplyOrderFinanceReward);
                rewardDTO.setReceivedAmt(supplyOrderFinanceReward.getPaidAmt());
                rewardDTO.setUnreceivedAmt(supplyOrderFinanceReward.getUnpaidAmt());
                rewardDTO.setRewardAmt(supplyOrderPO.getRewardAmt());
                orderDTO.setRewardAmt(supplyOrderPO.getRewardAmt());
            }
            supplyOrderDTO.setSupplyOrderRewardDTO(rewardDTO);

            //供应商返佣
            orderDTO.setRebateAmt(BigDecimal.ZERO);
            supplyOrderFinancePO.setFinanceType(StatementTypeEnum.REBATE_AMT.key);
            SupplyOrderFinancePO supplyOrderFinanceRebate = supplyOrderFinanceMapper.selectOne(supplyOrderFinancePO);
            SupplyOrderRebateDTO rebateDTO = new SupplyOrderRebateDTO();
            if (supplyOrderFinanceRebate != null) {
//                BeanUtils.copyProperties(supplyOrderFinanceRebate, rebateDTO);
                rebateDTO = ProductSalePriceConvert.INSTANCE.rebateDTOConvert(supplyOrderFinanceRebate);
                rebateDTO.setRebateAmt(supplyOrderPO.getRebateAmt());
                orderDTO.setRebateAmt(supplyOrderPO.getRebateAmt());
            }
            supplyOrderDTO.setSupplyOrderRebateDTO(rebateDTO);

            //查询供货单价格明细
            Example supplyProductPriceExample = new Example(SupplyProductPricePO.class);
            Example.Criteria supplyProductPriceCriteria = supplyProductPriceExample.createCriteria();
            supplyProductPriceCriteria.andEqualTo("supplyOrderId", supplyOrderPO.getId());
            List<SupplyProductPricePO> supplyProductPricePOList = supplyProductPriceMapper.selectByExample(supplyProductPriceExample);
            //费用按房间转Map
            Map<Integer, List<SupplyProductPricePO>> supplyFeeMap = supplyProductPricePOList.stream()
                    .collect(Collectors.groupingBy(SupplyProductPricePO::getRoomNumber));
            Map<String, PriceResponseDTO> supplyPriceMap = new HashMap<>();
            List<OrderFeeDTO> supplyFeeDTOList = new ArrayList<>();
            for (Integer roomNumber : supplyFeeMap.keySet()) {
                OrderFeeDTO orderFeeDTO = new OrderFeeDTO();
                orderFeeDTO.setRoomNumber(roomNumber.toString());
                orderFeeDTO.setStartDate(DateUtilX.dateToString(supplyOrderPO.getStartDate()));
                orderFeeDTO.setEndDate(DateUtilX.dateToString(supplyOrderPO.getEndDate()));
                orderFeeDTO.setOrderId(orderPO.getId());
                orderFeeDTO.setOrderCode(orderPO.getOrderCode());
                orderFeeDTO.setGuestList(guestStrMap.get(roomNumber));
                String[] guestQtyArr = guestQtyMap.get(roomNumber).split(",");
                if (guestQtyArr.length > 0) {
                    orderFeeDTO.setAdultQty(Integer.valueOf(guestQtyArr[0]));
                    if (guestQtyArr.length > 1) {
                        orderFeeDTO.setChildrenQty(Integer.valueOf(guestQtyArr[1]));
                    }
                }
                //按次税费
                TaxDTO timesTaxDTO = new TaxDTO();
                List<OrderFeeDetailDTO> orderFeeDetailDTOList = new ArrayList<>();
                for (SupplyProductPricePO supplyProductPricePO : supplyFeeMap.get(roomNumber)) {
                    if (null != supplyProductPricePO.getSaleDate()) {
                        OrderFeeDetailDTO orderFeeDetailDTO = new OrderFeeDetailDTO();
                        orderFeeDetailDTO.setSaleDate(DateUtilX.dateToString(supplyProductPricePO.getSaleDate()));
                        orderFeeDetailDTO.setCurrency(supplyProductPricePO.getCurrency());
                        orderFeeDetailDTO.setSalePrice(supplyProductPricePO.getBasePrice());
                        orderFeeDetailDTO.setDiscount(supplyProductPricePO.getDiscount());
                        orderFeeDetailDTO.setRoomPrice(supplyProductPricePO.getRoomPrice());
                        //每日税费
                        orderFeeDetailDTO.setTaxList(assemblyTax(supplyProductPricePO));
                        if (supplyProductPricePO.getExtraTaxFee() != null || supplyProductPricePO.getTax() != null || supplyProductPricePO.getSalesTax() != null) {
                            orderFeeDetailDTO.setTaxTotalAmount(BigDecimal.ZERO.
                                    add(CommonTgUtils.formatBigDecimal(supplyProductPricePO.getExtraTaxFee())).
                                    add(CommonTgUtils.formatBigDecimal(supplyProductPricePO.getTax())).
                                    add(CommonTgUtils.formatBigDecimal(supplyProductPricePO.getSalesTax())).
                                    setScale(2, RoundingMode.UP));
                        }
                        orderFeeDetailDTOList.add(orderFeeDetailDTO);

                        //设置按日价格
                        if (null == supplyPriceMap.get(orderFeeDetailDTO.getSaleDate())) {
                            PriceResponseDTO priceResponseDTO = new PriceResponseDTO();
                            priceResponseDTO.setSaleDate(orderFeeDetailDTO.getSaleDate());
                            priceResponseDTO.setSalePrice(supplyProductPricePO.getBasePrice());
                            priceResponseDTO.setBreakfastNum(orderPO.getBreakfastQty());
                            supplyPriceMap.put(orderFeeDetailDTO.getSaleDate(), priceResponseDTO);
                        } else {
                            supplyPriceMap.get(orderFeeDetailDTO.getSaleDate()).setSalePrice(supplyPriceMap.get(orderFeeDetailDTO.getSaleDate()).getSalePrice().add(supplyProductPricePO.getBasePrice()));
                        }
                    } else {
                        timesTaxDTO = assemblyTax(supplyProductPricePO);
                        orderFeeDTO.setPayAtHotelFee(supplyProductPricePO.getPayAtHotelFee());
                        orderFeeDTO.setPayAtHotelCurrency(supplyProductPricePO.getPayAtHotelCurrency());
                        if (supplyOrderDTO.getPayAtHotelFee() != null) {
                            supplyOrderDTO.setPayAtHotelFee(supplyOrderDTO.getPayAtHotelFee().add(supplyProductPricePO.getPayAtHotelFee()));
                        } else {
                            supplyOrderDTO.setPayAtHotelFee(supplyProductPricePO.getPayAtHotelFee());
                        }
                        supplyOrderDTO.setPayAtHotelCurrency(supplyProductPricePO.getPayAtHotelCurrency());
                    }
                }
                if (supplyOrderDTO.getPayAtHotelFee() != null && supplyOrderDTO.getPayAtHotelFee().compareTo(BigDecimal.ZERO) == 0) {
                    supplyOrderDTO.setPayAtHotelFee(null);
                    supplyOrderDTO.setPayAtHotelCurrency(null);
                }
                if (supplyOrderDTO.getPayAtHotelCurrency() != null) {
                    String currency = SettlementCurrencyEnum.getCodeByKey(String.valueOf(orderDTO.getPayAtHotelCurrency()));
                    if (StrUtilX.isNotEmpty(currency)) {
                        supplyOrderDTO.setPayAtHotelCurrencyCode(currency);
                    } else {
                        supplyOrderDTO.setPayAtHotelCurrencyCode("");
                    }
                }

                // 按次税费设置在首日,计算每日价格
                for (OrderFeeDetailDTO orderFeeDetailDTO : orderFeeDetailDTOList) {
                    if (orderFeeDetailDTO.getSaleDate().equals(orderDTO.getStartDate())) {
                        orderFeeDetailDTO.setTimesTaxFeeList(timesTaxDTO);
                        if (timesTaxDTO.getExtraTaxFee() != null || timesTaxDTO.getTax() != null || timesTaxDTO.getSalesTax() != null) {
                            orderFeeDetailDTO.setTimesTaxFee(BigDecimal.ZERO.
                                    add(CommonTgUtils.formatBigDecimal(timesTaxDTO.getExtraTaxFee())).
                                    add(CommonTgUtils.formatBigDecimal(timesTaxDTO.getTax())).
                                    add(CommonTgUtils.formatBigDecimal(timesTaxDTO.getSalesTax())));
                        }
                    }
                }
                orderFeeDetailDTOList.sort(Comparator.comparing(OrderFeeDetailDTO::getSaleDate));
                orderFeeDTO.setPriceList(orderFeeDetailDTOList);
                supplyFeeDTOList.add(orderFeeDTO);
            }
            supplyOrderDTO.setFeeList(supplyFeeDTOList);

            //设置每日价格
            List<PriceResponseDTO> supplyPriceResponseDTOList = new ArrayList<>();
            List<Date> supplyCollect = supplyPriceMap.keySet().stream().map(DateUtilX::stringToDate).collect(Collectors.toList());
            // 时间排序
            supplyCollect.sort(Comparator.naturalOrder());
            for (Date saleDate : supplyCollect) {
                supplyPriceResponseDTOList.add(supplyPriceMap.get(DateUtilX.dateToString(saleDate)));
            }

            //每日价格需要除以间数
            for (PriceResponseDTO priceResponseDTO : supplyPriceResponseDTOList) {
                BigDecimal avgPrice = CommonTgUtils.divideSetScale(priceResponseDTO.getSalePrice(), new BigDecimal(orderDTO.getRoomQty()), agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType());
                priceResponseDTO.setSalePrice(avgPrice);
            }
            supplyOrderDTO.setBasePriceList(supplyPriceResponseDTOList);

            //查询供货单产品
            SupplyProductPO supplyProductQuery = new SupplyProductPO();
            supplyProductQuery.setSupplyOrderId(supplyOrderDTO.getSupplyOrderId());
            List<SupplyProductPO> supplyProductPOList = supplyProductMapper.select(supplyProductQuery);
            supplyOrderDTO.setProductList(new ArrayList<>());
            for (SupplyProductPO supplyProductPO : supplyProductPOList) {
//                SupplyProductDTO supplyProductDTO = new SupplyProductDTO();
//                BeanUtils.copyProperties(supplyProductPO, supplyProductDTO);
                SupplyProductDTO supplyProductDTO = ProductSalePriceConvert.INSTANCE.supplyProductDTOConvert(supplyProductPO);
                supplyProductDTO.setSupplyProductId(supplyProductPO.getId());
                supplyProductDTO.setProductId(supplyProductPO.getProductId());
                supplyProductDTO.setStartDate(sdf.format(supplyProductPO.getStartDate()));
                supplyProductDTO.setEndDate(sdf.format(supplyProductPO.getEndDate()));
                supplyProductDTO.setRoomName(supplyProductPO.getRoomName());
                if (orderPO.getHourly() == 1) {
                    supplyProductDTO.setStartTime(sdf11.format(orderPO.getStartTime()));
                    supplyProductDTO.setEndTime(sdf11.format(orderPO.getEndTime()));
                }
                supplyProductDTO.setBasePrice(supplyProductPO.getBasePriceTotalAmt());
                supplyProductDTO.setNightQty((int) DateUtilX.getDay(DateUtilX.dateFormat(supplyProductPO.getStartDate(), "yyyy-MM-dd"), DateUtilX.dateFormat(supplyProductPO.getEndDate(), "yyyy-MM-dd")));
                supplyOrderDTO.getProductList().add(supplyProductDTO);
            }
            // 设置取消条款
            SupplyProductPO supplyProductPO = supplyProductPOList.get(0);
            supplyOrderDTO.setCancellationTerm(supplyProductPO.getCancellationTerm());
            supplysum = supplysum.add(supplyOrderPO.getSupplyOrderAmt());
            orderDTO.getSupplyOrderList().add(supplyOrderDTO);
        }
        orderDTO.setSupplyOrderExceptionDTOList(supplyOrderExceptionDTOList);

        orderDTO.setSupplyOrderTotalAmt(supplysum);
        if (null != request.getOrderOwnerName() && null != orderPO.getOrderOwnerName()) {
            orderDTO.setIsMyOrder((request.getOrderOwnerName().equals(orderPO.getOrderOwnerName())) ? 1 : 0);
        }

        //查询申请取消订单
        if (null != orderPO.getModificationStatus() && (orderPO.getModificationStatus() == 1 || orderPO.getModificationStatus() == 3 || orderPO.getModificationStatus() == 4)) {
            Example examples = new Example(OrderRequestPO.class);
            examples.setOrderByClause("created_dt DESC");
            Example.Criteria criterias = examples.createCriteria();
            criterias.andEqualTo("orderId", orderPO.getId());
            List<OrderRequestPO> orderRequestPOs = orderRequestMapper.selectByExample(examples);

            // 查询缓存客户信息
            Object agentInfo = RedisTemplateX.hashGet(RedisKey.AGENT_ACCOUNT_CONFIG, orderPO.getAgentCode());
            String agentName = orderPO.getAgentName();
            if (agentInfo != null) {
                AgentAccountConfig accountConfig = JSON.parseObject(JSONUtil.toJsonStr(agentInfo), AgentAccountConfig.class);
                agentName = accountConfig.getAgentName();
            }

            if (null != orderRequestPOs && orderRequestPOs.size() > 0 && orderPO.getModificationStatus() == 1) {
                StringBuilder stringBuilder = new StringBuilder();
                stringBuilder.append("申请退款订单：客户");
                stringBuilder.append(agentName);
                stringBuilder.append("于").append(DateUtilX.dateToString(DateUtilX.stringToDate(orderRequestPOs.get(0).getCreatedDt(), "yyyy-MM-dd HH:mm:ss"), "yyyy-MM-dd HH:mm:ss"));

                if (orderRequestPOs.get(0).getHandleResult() == 1) {
                    stringBuilder.append("已");
                    if (orderRequestPOs.get(0).getHardCancel() != null && orderRequestPOs.get(0).getHardCancel() == 1) {
                        stringBuilder.append("强制");
                    }
                    stringBuilder.append("申请退款");
                } else if (orderRequestPOs.get(0).getHandleResult() == 0) {
                    stringBuilder.append("申请取消订单");
                }

                stringBuilder.append(",请查看订单是否已处理。");
                orderDTO.setModificationRequestContent(stringBuilder.toString());
                orderDTO.setModificationResponseType(orderRequestPOs.get(0).getHardCancel() == null ? 0 : orderRequestPOs.get(0).getHardCancel());
            }

            if (null != orderRequestPOs && orderRequestPOs.size() > 0 && orderPO.getOrderConfirmationStatus() == 1 && orderPO.getModificationStatus() == 3) {
                String stringBuilder = "二次确认订单：携程" +
                        "于" + DateUtilX.dateToString(DateUtilX.stringToDate(orderRequestPOs.get(0).getCreatedDt(), "yyyy-MM-dd HH:mm:ss"), "yyyy-MM-dd HH:mm:ss") + "申请二次确认。";
                orderDTO.setModificationRequestContent(stringBuilder);
                orderDTO.setModificationResponseType(3);
            }

            if (CollUtilX.isNotEmpty(orderRequestPOs) && orderPO.getModificationStatus() == 4) {
                String stringBuffer = agentName +
                        "于" + DateUtilX.dateToString(DateUtilX.stringToDate(orderRequestPOs.get(0).getCreatedDt(), "yyyy-MM-dd HH:mm:ss"), "yyyy-MM-dd HH:mm:ss") +
                        "申请退款。" +
                        "请先到" + ChannelEnum.getValueByKey(orderPO.getChannelCode()) + "后台" + "查看是否已处理";
                orderDTO.setModificationRequestContent(stringBuffer);
                orderDTO.setModificationResponseType(2);
            }
        }

        //查询入住明细
        List<OrderCheckInfoDTO> orderCheckInfoDTOList = orderCheckInfoMapper.orderCheckInfoList(orderPO.getOrderCode());
        if (CollUtilX.isNotEmpty(orderCheckInfoDTOList)) {
            Map<String, List<OrderCheckInfoDTO>> supplyCheckMap = orderCheckInfoDTOList.stream().collect(groupingBy(OrderCheckInfoDTO::getSupplyOrderCode));
            Map<String, SupplyOrderDTO> supplyOrderMap = orderDTO.getSupplyOrderList().stream().collect(toMap(SupplyOrderDTO::getSupplyOrderCode, Function.identity(), (key1, key2) -> key2));
            List<OrderCheckInfoDTOS> orderCheckInfos = new ArrayList<>();
            for (Map.Entry<String, List<OrderCheckInfoDTO>> item : supplyCheckMap.entrySet()) {
                OrderCheckInfoDTOS dtos = new OrderCheckInfoDTOS();
                dtos.setSupplyOrderCode(item.getKey());
                dtos.setOrderCheckInfoDTOList(item.getValue());
                SupplyOrderDTO supplyOrderDTO = supplyOrderMap.get(item.getKey());
                if (supplyOrderDTO != null) {
                    dtos.setIsCheckInDetailAbnormal(supplyOrderDTO.getIsCheckInDetailAbnormal());
                }
                orderCheckInfos.add(dtos);
            }
            orderDTO.setOrderCheckInfos(orderCheckInfos);
        }

        //查询担保条款
        List<OrderRestrictDTO> orderRestrictDTOList = orderRestrictMapper.selectRestrict(orderPO.getOrderCode());
        if (CollUtilX.isNotEmpty(orderRestrictDTOList)) {
            orderDTO.setOrderRestrictDTO(orderRestrictDTOList.get(0));
        }

        orderDTO.setAdultQty(orderPO.getAdultQty());
        if (!org.apache.commons.lang3.StringUtils.isEmpty(childrenAge)) {
            orderDTO.setChildrenAge(childrenAge.substring(0, childrenAge.length() - 1));
        } else {
            orderDTO.setChildrenAge("");
        }

        //查询时区
        QueryCityTimeZoneReq queryCityTimeZoneReq = new QueryCityTimeZoneReq();
        queryCityTimeZoneReq.setCityCode(orderPO.getCityCode());
        Response<QueryCityTimeZoneResp> queryCityTimeZoneRespResponse = hotelRemote.queryCityTimeZone(queryCityTimeZoneReq);
        if (queryCityTimeZoneRespResponse.isSuccess() && queryCityTimeZoneRespResponse.getModel() != null) {
            orderDTO.setTimeZone(queryCityTimeZoneRespResponse.getModel().getTimeZero());
        } else {
            log.error("查询时区失败，req={}, resp={}", JSONUtil.toJsonStr(queryCityTimeZoneReq), JSONUtil.toJsonStr(queryCityTimeZoneRespResponse));
        }

        // 查询订单金额汇率转换后数据
        OrderRateDto orderRateDto = mongodbCommonService.queryOrderRateFromMongoDb(orderPO.getOrderCode());
        if (orderRateDto != null) {
            orderDTO.setProfitOrgCurrencyAmt(orderRateDto.getProfitOrgCurrencyAmt());
            orderDTO.setOrderOrgCurrencyAmt(orderRateDto.getReceivableOrgCurrencyAmt());
            orderDTO.setSupplyOrderTotalOrgCurrencyAmt(orderRateDto.getSuppleOrderTotalPayableOrgCurrencyAmt());
            orderDTO.setRebateOrgCurrencyAmt(orderRateDto.getSupplierTotalRebateOrgCurrencyAmt());
            orderDTO.setRewardOrgCurrencyAmt(orderRateDto.getSupplierTotalRewardOrgCurrencyAmt());
        }

        // 页面请求需要判断是否加锁
        if (request.getPageFlag() != null && request.getPageFlag() == 1 && StrUtilX.isEmpty(orderDTO.getLockName())) {
            LockOrderDTO lockOrderDTO = new LockOrderDTO();
            lockOrderDTO.setOrderId(orderPO.getId());
            lockOrderDTO.setLockStatus(1);
            lockOrderDTO.setOrderOwnerName(request.getOrderOwnerName());
            lockOrderDTO.setOperator(request.getOperator());
            lockOrderDTO.setOperatorUser(request.getOrderOwnerName());
            orderService.lockOrder(lockOrderDTO);

            orderDTO.setLockStatus(1);
            orderDTO.setLockName(request.getOperator());
            orderDTO.setLockUser(request.getOrderOwnerName());
        }

        // 查询订单发票异常信息
        List<OrderInvoiceErrorDTO> orderInvoiceErrorList = getOrderInvoiceErrorList(orderPO.getOrderCode());
        orderDTO.setOrderInvoiceErrorList(orderInvoiceErrorList);
        try {
            TipsDTO dto = new TipsDTO();
            dto.setSupplierCode(supplyCode);
            if (StrUtilX.isNotEmpty(request.getLanguage())) {
                dto.setLanguage(request.getLanguage());
            } else {
                dto.setLanguage(LanguageTypeEnum.en_US.getValue());
            }
            Response<List<TipsResp>> tips = tipsRemote.findTips(dto);
            if (tips.isSuccess()) {
                orderDTO.setTipsList(tips.getModel());
            }

            //新增入住明细不符列表
            List<SupplyOrderAmtLogPO> supplyOrderAmtLogPOS = supplyOrderAmtLogMapper.selectList(new LambdaQueryWrapper<SupplyOrderAmtLogPO>()
                    .eq(SupplyOrderAmtLogPO::getOrderCode, orderPO.getOrderCode())
                    .eq(SupplyOrderAmtLogPO::getStatus, 0)
                    .orderByDesc(SupplyOrderAmtLogPO::getCreatedDt)
            );
            List<SupplyOrderAmtLogDTO> amtLogDTOS = new ArrayList<>();
            for (SupplyOrderAmtLogPO po : supplyOrderAmtLogPOS) {
//                SupplyOrderAmtLogDTO supplyOrderAmtLogDTO = new SupplyOrderAmtLogDTO();
//                BeanUtils.copyProperties(po, supplyOrderAmtLogDTO);
                SupplyOrderAmtLogDTO supplyOrderAmtLogDTO = ProductSalePriceConvert.INSTANCE.supplyOrderAmtLogDTOConvert(po);
                amtLogDTOS.add(supplyOrderAmtLogDTO);
            }
            orderDTO.setSupplyOrderAmtLogDTOList(amtLogDTOS);
        } catch (Exception e) {
            //throw new RuntimeException(e);
        }

        // 查询订单产品提示信息
        List<OrderProductTipPO> tips = orderProductTipMapper.selectList(new QueryWrapper<OrderProductTipPO>().eq("order_code", orderDTO.getOrderCode()));
        if (CollUtilX.isNotEmpty(tips)) {
            Map<Integer, List<OrderProductTipPO>> tipMap = new HashMap<>();
            for (OrderProductTipPO tip : tips) {
                if (tipMap.containsKey(tip.getTipType())) {
                    tipMap.get(tip.getTipType()).add(tip);
                } else {
                    List<OrderProductTipPO> tipPOS = new ArrayList<>();
                    tipPOS.add(tip);
                    tipMap.put(tip.getTipType(), tipPOS);
                }
            }

            List<TipInfosDTO> dtoList = new ArrayList<>();
            for (Map.Entry<Integer, List<OrderProductTipPO>> entry : tipMap.entrySet()) {
                TipInfosDTO tipInfosDTO = new TipInfosDTO();
                tipInfosDTO.setTipsType(entry.getKey());
                List<TipsDetail> details = entry.getValue().stream().map(item -> {
                    TipsDetail tipsDetail = new TipsDetail();
                    tipsDetail.setTitle(item.getTitle());
                    tipsDetail.setDetails(item.getContent());
                    return tipsDetail;
                }).collect(Collectors.toList());
                tipInfosDTO.setTipsDetails(details);
                dtoList.add(tipInfosDTO);
            }
            orderDTO.setTips(dtoList);
        }
        return orderDTO;
    }

    private TaxDTO assemblyTax(OrderProductPricePO orderProductPricePO, AgentAccountConfig agentAccountConfig) {
        TaxDTO taxDTO = new TaxDTO();
        if (agentAccountConfig != null) {
            taxDTO.setTax(CommonTgUtils.setScale(orderProductPricePO.getTax(), agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType()));
            taxDTO.setExtraTaxFee(CommonTgUtils.setScale(orderProductPricePO.getExtraTaxFee(), agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType()));
            taxDTO.setSalesTax(CommonTgUtils.setScale(orderProductPricePO.getSalesTax(), agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType()));
        } else {
            taxDTO.setTax(orderProductPricePO.getTax());
            taxDTO.setExtraTaxFee(orderProductPricePO.getExtraTaxFee());
            taxDTO.setSalesTax(orderProductPricePO.getSalesTax());
        }
        return taxDTO;
    }

    private TaxDTO assemblyTax(SupplyProductPricePO supplyProductPricePO) {
        TaxDTO taxDTO = new TaxDTO();
        taxDTO.setTax(supplyProductPricePO.getTax());
        taxDTO.setExtraTaxFee(supplyProductPricePO.getExtraTaxFee());
        taxDTO.setSalesTax(supplyProductPricePO.getSalesTax());
        return taxDTO;
    }

    @Override
    public List<OrderRemarkDTO> queryOrderRemark(QueryOrderRemarkDTO request) {
        List<OrderRemarkDTO> orderRemarkDTOList = new ArrayList<>();
        Example orderRemarkExample = new Example(OrderRemarkPO.class);
        orderRemarkExample.setOrderByClause("created_dt desc");
        Example.Criteria orderRemarkCriteria = orderRemarkExample.createCriteria();
        orderRemarkCriteria.andEqualTo("orderId", request.getOrderId());
        if (null != request.getRemarkType() && !request.getRemarkType().equals(-1)) {
            orderRemarkCriteria.andEqualTo("remarkType", request.getRemarkType());
        }
        List<OrderRemarkPO> orderRemarkPOList = orderRemarkMapper.selectByExample(orderRemarkExample);
        for (OrderRemarkPO orderRemarkPO : orderRemarkPOList) {
            OrderRemarkDTO orderRemarkDTO = new OrderRemarkDTO();
            orderRemarkDTO.setRemark(orderRemarkPO.getRemark());
            orderRemarkDTO.setReceiver(orderRemarkPO.getReceiver());
            orderRemarkDTO.setCreatedBy(orderRemarkPO.getCreatedBy());
            orderRemarkDTO.setCreatedDt(DateUtilX.dateToString(DateUtilX.stringToDate(orderRemarkPO.getCreatedDt(), "yyyy-MM-dd HH:mm:ss"), "yyyy-MM-dd HH:mm:ss"));
            orderRemarkDTOList.add(orderRemarkDTO);
        }
        return orderRemarkDTOList;
    }

    @Override
    public List<OrderLogDTO> queryOrderLog(OrderIdDTO request) {
        List<OrderLogDTO> orderLogDTOList = new ArrayList<>();
        Example orderLogExample = new Example(OrderLogPO.class);
        orderLogExample.setOrderByClause("created_dt");
        Example.Criteria orderLogCriteria = orderLogExample.createCriteria();
        orderLogCriteria.andEqualTo("orderId", request.getOrderId());
        orderLogExample.setOrderByClause("id desc");
        List<OrderLogPO> orderLogPOList = orderLogMapper.selectByExample(orderLogExample);
        for (OrderLogPO orderLogPO : orderLogPOList) {
            OrderLogDTO orderLogDTO = new OrderLogDTO();
            orderLogDTO.setContent(orderLogPO.getContent());
            orderLogDTO.setTarget(orderLogPO.getTarget());
            orderLogDTO.setCreatedBy(orderLogPO.getCreatedBy());
            orderLogDTO.setCreatedDt(orderLogPO.getCreatedDt());
            orderLogDTOList.add(orderLogDTO);
        }
        return orderLogDTOList;
    }

    @Override
    public List<OrderRequestDTO> queryOrderRequest(OrderIdDTO request) {
        OrderPO orderPO = orderMapper.selectByPrimaryKey(request.getOrderId());

        List<OrderRequestDTO> orderRequestDTOList = new ArrayList<>();
        Example orderRequestExample = new Example(OrderRequestPO.class);
        orderRequestExample.setOrderByClause("created_dt");
        Example.Criteria orderRequestCriteria = orderRequestExample.createCriteria();
        orderRequestCriteria.andEqualTo("orderId", request.getOrderId());
        List<OrderRequestPO> orderRequestPOList = orderRequestMapper.selectByExample(orderRequestExample);
        for (OrderRequestPO orderRequestPO : orderRequestPOList) {
            OrderRequestDTO orderRequestDTO = new OrderRequestDTO();
            orderRequestDTO.setOrderRequestId(orderRequestPO.getId());
            orderRequestDTO.setRequestType(orderRequestPO.getRequestType());
            orderRequestDTO.setHandledResult(orderRequestPO.getHandleResult());
            orderRequestDTO.setRemark(orderRequestPO.getRemark());
            orderRequestDTO.setAgentName(orderPO.getAgentName());
            orderRequestDTO.setCreatedBy(orderRequestPO.getCreatedBy());
            orderRequestDTO.setCreatedDt(orderRequestPO.getCreatedDt());
            orderRequestDTO.setUpdatedBy(orderRequestPO.getUpdatedBy());
            orderRequestDTO.setUpdatedDt(orderRequestPO.getUpdatedDt());
            orderRequestDTOList.add(orderRequestDTO);
        }
        return orderRequestDTOList;
    }

    @Override
    public String queryConfirmOrderInfo(QueryConfirmOrderInfoDTO request) {
        OrderPO orderPO = orderMapper.selectByPrimaryKey(request.getOrderId());
        Map<String, Object> map = new HashMap<>();
        map.put("orderCode", orderPO.getOrderCode());
        map.put("startDate", DateUtilX.dateToString(orderPO.getStartDate()));
        map.put("endDate", DateUtilX.dateToString(orderPO.getEndDate()));
        map.put("hotelName", orderPO.getHotelName());
        map.put("roomQty", orderPO.getRoomQty());
        map.put("roomName", orderPO.getRoomName());

        //确认模板
        if (request.getConfirmType() == 0) {
            // 查询酒店基本信息接口
            HotelInfoCollectionReq infoReq = new HotelInfoCollectionReq();
            infoReq.setHotelIds(Collections.singletonList(orderPO.getHotelId()));
            infoReq.setLanguageType(LanguageTypeEnum.zh_CN.getValue());
            List<String> set = new ArrayList<>();
            set.add("hotelName");
            set.add("tel");
            set.add("location");
            infoReq.setSettings(set);
            Response<HotelInfoCollectionDTO> response = hotelRemote.queryHotelInfo(infoReq);
            if (response.isSuccess()) {
                HotelInfoCollectionDTO hotelInfo = response.getModel();
                if (Objects.nonNull(hotelInfo)) {
                    map.put("hotelAddress", hotelInfo.getLocation().getAddress());
                    map.put("hotelPhone", hotelInfo.getTel());
                }
            }

            if (StrUtilX.isNotEmpty(orderPO.getRoomName())) {
                String content = RedisTemplateX.get(RedisKey.LOG_CODE + "O000001") + ":${orderCode},${startDate} "
                        + RedisTemplateX.get(RedisKey.LOG_CODE + "O000002") + ",${endDate} "
                        + RedisTemplateX.get(RedisKey.LOG_CODE + "O000003") +
                        " ${hotelName} " + RedisTemplateX.get(RedisKey.LOG_CODE + "O000004") + " ${roomQty} "
                        + RedisTemplateX.get(RedisKey.LOG_CODE + "O000005") + " ${roomName},"
                        + RedisTemplateX.get(RedisKey.LOG_CODE + "O000006") +
                        RedisTemplateX.get(RedisKey.LOG_CODE + "O000007") + ":${hotelAddress}；" +
                        RedisTemplateX.get(RedisKey.LOG_CODE + "O000008") + ":${hotelPhone}。";
                return StringTemplateUtil.processFreemarker(content, map);
            } else {
                log.info("queryConfirmOrderInfo Exception map:{}", JSONObject.toJSONString(map));
                return null;//模板数据缺失 待查 TODO
            }
        }
        //取消模板
        else if (request.getConfirmType() == 1) {
            String content = RedisTemplateX.get(RedisKey.LOG_CODE + "O000001") + ":${orderCode},${startDate}"
                    + RedisTemplateX.get(RedisKey.LOG_CODE + "O000002") + ",${endDate}"
                    + RedisTemplateX.get(RedisKey.LOG_CODE + "O000003") +
                    "${hotelName}" + RedisTemplateX.get(RedisKey.LOG_CODE + "O000004") + "${roomQty}"
                    + RedisTemplateX.get(RedisKey.LOG_CODE + "O000005") + "${roomName},"
                    + RedisTemplateX.get(RedisKey.LOG_CODE + "O000009");
            return StringTemplateUtil.processFreemarker(content, map);
        }
        return null;
    }


    @Override
    public List<PriceResponseDTO> queryOrderPriceItem(OrderIdDTO request) {
        List<PriceResponseDTO> priceDTOList = new ArrayList<>();

        Example orderProductPriceExample = new Example(OrderProductPricePO.class);
        orderProductPriceExample.setOrderByClause("sale_date");
        Example.Criteria orderProductPriceCriteria = orderProductPriceExample.createCriteria();
        orderProductPriceCriteria.andEqualTo("orderId", request.getOrderId());
        List<OrderProductPricePO> orderProductPricePOList = orderProductPriceMapper.selectByExample(orderProductPriceExample);
        for (OrderProductPricePO orderProductPricePO : orderProductPricePOList) {
            PriceResponseDTO priceDTO = new PriceResponseDTO();
            priceDTO.setSaleDate(DateUtilX.dateToString(orderProductPricePO.getSaleDate(), "yyyy-MM-dd HH:mm:ss"));
            priceDTO.setSalePrice(orderProductPricePO.getSalePrice());
            priceDTOList.add(priceDTO);
        }
        return priceDTOList;
    }

    @Override
    public SupplyOrderPreviewDTO previewSupplyOrder(SupplyOrderIdDTO request) {
//        SupplyOrderPreviewDTO supplyOrderPreviewDTO = new SupplyOrderPreviewDTO();

        //查询供货单
        SupplyOrderPO supplyOrderPO = supplyOrderMapper.selectByPrimaryKey(request.getSupplyOrderId());
//        BeanUtils.copyProperties(supplyOrderPO, supplyOrderPreviewDTO);
        SupplyOrderPreviewDTO supplyOrderPreviewDTO = ProductSalePriceConvert.INSTANCE.supplyOrderPreviewDTOConvert(supplyOrderPO);
        supplyOrderPreviewDTO.setSupplyOrderId(request.getSupplyOrderId());

        String officialSealUrl = supplyOrderMapper.queryOfficialSealUrl(supplyOrderPO.getSupplierCode());
        supplyOrderPreviewDTO.setOfficialSealUrl(officialSealUrl);

        // 查询酒店基本信息
        HotelInfoCollectionReq infoReq = new HotelInfoCollectionReq();
        infoReq.setHotelIds(Collections.singletonList(supplyOrderPO.getHotelId()));
        if (StrUtilX.isNotEmpty(request.getLanguageType())) {
            infoReq.setLanguageType(request.getLanguageType());
        } else {
            infoReq.setLanguageType(LanguageTypeEnum.zh_CN.getValue());
        }
        List<String> set = new ArrayList<>();
        set.add("hotelName");
        set.add("tel");
        set.add("location");
        infoReq.setSettings(set);
        Response<HotelInfoCollectionDTO> response = hotelRemote.queryHotelInfo(infoReq);
        if (response.isSuccess()) {
            HotelInfoCollectionDTO hotelInfo = response.getModel();
            if (Objects.nonNull(hotelInfo)) {
                supplyOrderPreviewDTO.setHotelName(hotelInfo.getHotelName());
                supplyOrderPreviewDTO.setHotelAddress(hotelInfo.getLocation().getAddress());
                supplyOrderPreviewDTO.setHotelTel(hotelInfo.getTel());
            }
        }

        //查询特殊要求
        OrderExtendPO orderExtendPO = null;
        OrderExtendPO orderExtendQuery = new OrderExtendPO();
        orderExtendQuery.setOrderId(supplyOrderPO.getOrderId());
        List<OrderExtendPO> orderExtendPOList = orderExtendMapper.select(orderExtendQuery);
        if (CollUtilX.isNotEmpty(orderExtendPOList)) {
            orderExtendPO = orderExtendPOList.get(0);
        }
        if (null != orderExtendPO && orderExtendPO.getIsShowOnSupplyOrder() == 1) {
            supplyOrderPreviewDTO.setSpecialRequest(orderExtendPO.getSpecialRequest());
        }
        if (null != orderExtendPO) {
            supplyOrderPreviewDTO.setBedType(orderExtendPO.getBedType());
        }

        //查询产品
        Set<String> guestSet = new HashSet<>();
        SupplyProductPO supplyProductQuery = new SupplyProductPO();
        supplyProductQuery.setSupplyOrderId(request.getSupplyOrderId());
        List<SupplyProductPO> supplyProductPOList = supplyProductMapper.select(supplyProductQuery);
        for (SupplyProductPO supplyProductPO : supplyProductPOList) {
            // 过滤重复的入住人
            if (StrUtilX.isNotEmpty(supplyProductPO.getGuest())) {
                if (supplyProductPO.getGuest().contains("、")) {
                    guestSet.addAll(Arrays.asList(supplyProductPO.getGuest().split("、")));
                } else {
                    guestSet.add(supplyProductPO.getGuest());
                }
            }
        }
        supplyOrderPreviewDTO.setGuest(StrUtilX.listToString(guestSet, ","));

        // 查询价格明细
        supplyOrderPreviewDTO.setProductList(new ArrayList<>());
        Example supplyProductPriceExample = new Example(SupplyProductPricePO.class);
        supplyProductPriceExample.setOrderByClause("supply_product_id,sale_date");
        Example.Criteria supplyProductPriceCriteria = supplyProductPriceExample.createCriteria();
        supplyProductPriceCriteria.andEqualTo("supplyOrderId", request.getSupplyOrderId());
        List<SupplyProductPricePO> supplyProductPricePOList = supplyProductPriceMapper.selectByExample(supplyProductPriceExample);
        BigDecimal totalAmt = BigDecimal.ZERO;
        for (SupplyProductPricePO supplyProductPricePO : supplyProductPricePOList) {
            totalAmt = totalAmt.add(CommonTgUtils.formatBigDecimal(supplyProductPricePO.getRoomPrice()))
                    .add(CommonTgUtils.formatBigDecimal(supplyProductPricePO.getTax()))
                    .add(CommonTgUtils.formatBigDecimal(supplyProductPricePO.getSalesTax()))
                    .add(CommonTgUtils.formatBigDecimal(supplyProductPricePO.getExtraTaxFee()));
        }

        SupplyProductPreviewDTO supplyProductPreviewDTO = new SupplyProductPreviewDTO();
        supplyProductPreviewDTO.setStartDate(DateUtilX.dateToString(supplyOrderPO.getStartDate()));
        supplyProductPreviewDTO.setEndDate(DateUtilX.dateToString(supplyOrderPO.getEndDate()));
        supplyProductPreviewDTO.setNightQty(new BigDecimal(DateUtilX.getDay(supplyOrderPO.getStartDate(), supplyOrderPO.getEndDate())));
        supplyProductPreviewDTO.setRoomQty(supplyOrderPO.getRoomQty());
        supplyProductPreviewDTO.setRoomName(supplyOrderPO.getRoomName());
        supplyProductPreviewDTO.setProductName(supplyOrderPO.getProductName());
        supplyProductPreviewDTO.setTotalAmt(totalAmt.setScale(2, RoundingMode.UP));
        BigDecimal basePrice = CommonTgUtils.divideSetScale(totalAmt, supplyProductPreviewDTO.getNightQty().multiply(new BigDecimal(supplyProductPreviewDTO.getRoomQty())), 2, 1);
        supplyProductPreviewDTO.setBasePrice(basePrice);
        supplyOrderPreviewDTO.getProductList().add(supplyProductPreviewDTO);

        // 发送方
        CompanyAddDTO companyAddDTO = new CompanyAddDTO();
        companyAddDTO.setCompanyCode(CompanyDTO.COMPANY_CODE);
        Response<CompanySelectDTO> response4 = companyRemote.queryCompanyDetail(companyAddDTO);
        CompanySelectDTO companySelectDTO = response4.getModel();
        supplyOrderPreviewDTO.setCompanyName(companySelectDTO.getCompanyName());
        return supplyOrderPreviewDTO;
    }

    @Override
    public SupplyResultDTO querySupplyOrderResult(SupplyOrderIdDTO request) {
        //查询最新供货结果
        SupplyResultDTO supplyResultDTO = new SupplyResultDTO();
        supplyResultDTO.setSupplyOrderId(request.getSupplyOrderId());

        // 获取供货单信息
        SupplyOrderPO supplyOrderPO = supplyOrderMapper.selectByPrimaryKey(request.getSupplyOrderId());
        // 直接使用供货单数据封装
        supplyResultDTO.setConfirmationStatus(supplyOrderPO.getConfirmationStatus());
        supplyResultDTO.setConfirmationCode(supplyOrderPO.getConfirmationCode());
        supplyResultDTO.setSupplierConfirmer(supplyOrderPO.getSupplierConfirmer());
        supplyResultDTO.setRefundFee(supplyOrderPO.getRefundFee());
        supplyResultDTO.setCurrency(SettlementCurrencyEnum.getCodeByKey(String.valueOf(supplyOrderPO.getBaseCurrency())));
        supplyResultDTO.setRefusedReason(supplyOrderPO.getRefusedReason());
        supplyResultDTO.setRemark(supplyOrderPO.getConfirmationRemark());
        supplyResultDTO.setOperatedBy(supplyOrderPO.getUpdatedBy());
        supplyResultDTO.setOperatedTime(supplyOrderPO.getUpdatedDt());
        // 查询供货单附件
        supplyResultDTO.setSupplyAttachmentList(new ArrayList<>());
        SupplyAttachmentPO supplyAttachmentQuery = new SupplyAttachmentPO();
        supplyAttachmentQuery.setSupplyOrderId(request.getSupplyOrderId());
        supplyAttachmentQuery.setActive(1);
        List<SupplyAttachmentPO> supplyAttachmentPOList = supplyAttachmentMapper.select(supplyAttachmentQuery);
        for (SupplyAttachmentPO supplyAttachmentPO : supplyAttachmentPOList) {
            SupplyAttachmentDTO supplyAttachmentDTO = new SupplyAttachmentDTO();
            supplyAttachmentDTO.setSupplyAttachmentId(supplyAttachmentPO.getId());
            supplyAttachmentDTO.setName(supplyAttachmentPO.getName());
            supplyAttachmentDTO.setUrl(supplyAttachmentPO.getUrl());
            supplyResultDTO.getSupplyAttachmentList().add(supplyAttachmentDTO);
        }
        return supplyResultDTO;
    }

    @Override
    public SupplyProductDetailDTO querySupplyProduct(SupplyProductIdDTO request) {
//        SupplyProductDetailDTO supplyProductDetailDTO = new SupplyProductDetailDTO();
        SupplyProductPO supplyProductPO = supplyProductMapper.selectByPrimaryKey(request.getSupplyProductId());
//        BeanUtils.copyProperties(supplyProductPO, supplyProductDetailDTO);
        SupplyProductDetailDTO supplyProductDetailDTO = ProductSalePriceConvert.INSTANCE.supplyProductDetailDTOConvert(supplyProductPO);
        supplyProductDetailDTO.setSupplyProductId(request.getSupplyProductId());
        supplyProductDetailDTO.setBasePrice(supplyProductPO.getBasePriceTotalAmt());
        supplyProductDetailDTO.setStartDate(DateUtilX.dateToString(supplyProductPO.getStartDate()));
        supplyProductDetailDTO.setEndDate(DateUtilX.dateToString(supplyProductPO.getEndDate()));
        SupplyOrderPO supplyOrderPO = supplyOrderMapper.selectByPrimaryKey(supplyProductPO.getSupplyOrderId());
        supplyProductDetailDTO.setSupplierName(supplyOrderPO.getSupplierName());
        supplyProductDetailDTO.setSettlementCurrency(supplyOrderPO.getBaseCurrency());
        supplyProductDetailDTO.setBaseCurrency(supplyOrderPO.getBaseCurrency());
        supplyProductDetailDTO.setExchangeBasePrice(supplyProductPO.getBasePriceTotalAmt().multiply(supplyOrderPO.getRate()));

        //查询入住人
        supplyProductDetailDTO.setGuestList(new ArrayList<>());
        GuestPO guestQuery = new GuestPO();
        guestQuery.setOrderId(supplyOrderPO.getOrderId());
        List<GuestPO> guestPOList = guestMapper.select(guestQuery);
        for (GuestPO guestPO : guestPOList) {
            SupplyGuestDTO supplyGuestDTO = new SupplyGuestDTO();
            supplyGuestDTO.setGuest(guestPO.getName());
            if (supplyProductPO.getGuest().contains(guestPO.getName())) {
                supplyGuestDTO.setSelected(1);
            }
            supplyProductDetailDTO.getGuestList().add(supplyGuestDTO);
        }

        //查询产品明细
        SupplyProductPricePO supplyProductPriceQuery = new SupplyProductPricePO();
        supplyProductPriceQuery.setSupplyProductId(request.getSupplyProductId());
        List<SupplyProductPricePO> supplyProductPricePOList = supplyProductPriceMapper.select(supplyProductPriceQuery);
        supplyProductDetailDTO.setPriceList(new ArrayList<>());
        for (SupplyProductPricePO supplyProductPricePO : supplyProductPricePOList) {
//            SupplyProductPriceDTO supplyProductPriceDTO = new SupplyProductPriceDTO();
//            BeanUtils.copyProperties(supplyProductPricePO, supplyProductPriceDTO);
            SupplyProductPriceDTO supplyProductPriceDTO = ProductSalePriceConvert.INSTANCE.supplyProductPriceDTOConvert(supplyProductPricePO);
            supplyProductPriceDTO.setSaleDate(DateUtilX.dateToString(supplyProductPricePO.getSaleDate()));
            supplyProductDetailDTO.getPriceList().add(supplyProductPriceDTO);
        }
        return supplyProductDetailDTO;
    }

    /**
     * 查询供货单的供应商列表
     * 有产品的供应商+直连供应商
     */
    @Override
    public List<FuzzySupplierDTO> querySupplier(Map<String, String> requestMap) {
        return orderMapper.querySupplier(requestMap);
    }

    /**
     * 查询订单发票
     */
    @Override
    public OrderInvoiceDTO queryOrderInvoice(QueryOrderInvoiceDTO queryOrderInvoiceDTO) {
        OrderInvoicePO orderInvoicePO = new OrderInvoicePO();
        orderInvoicePO.setOrderCode(queryOrderInvoiceDTO.getOrderCode());
        OrderInvoicePO orderInvoicePOS = orderInvoiceMapper.selectOne(orderInvoicePO);
        return Optional.ofNullable(orderInvoicePOS).map(i -> ProductSalePriceConvert.INSTANCE.orderInvoiceConvert(i)).orElse(null);
    }

    /**
     * 获取订单发票异常信息列表
     *
     * @param orderCode 订单编码
     * @return 发票异常信息列表
     */
    private List<OrderInvoiceErrorDTO> getOrderInvoiceErrorList(String orderCode) {
        // 创建发票异常信息列表
        List<OrderInvoiceErrorDTO> errorList = new ArrayList<>();

        // 查询订单发票
        OrderInvoicePO orderInvoicePO = new OrderInvoicePO();
        orderInvoicePO.setOrderCode(orderCode);
        OrderInvoicePO invoice = orderInvoiceMapper.selectOne(orderInvoicePO);

        // 如果没有发票记录，返回空列表
        if (invoice == null) {
            return errorList;
        }

        // 检查客户未配置电子发票推送地址异常
        if (invoice.getEPushAddressConfigStatus() != null && invoice.getEPushAddressConfigStatus() == 1) {
            OrderInvoiceErrorDTO error = new OrderInvoiceErrorDTO();
            error.setEType(OrderInvoiceErrorTypeEnum.PUSH_ADDRESS_CONFIG_ERROR.getCode());
            error.setDesc(invoice.getEPushAddressConfigMessage());
            error.setOrderInvoiceId(invoice.getId());
            errorList.add(error);
        }
        // 检查创建开票失败错误状态
        if (invoice.getECreateBillStatus() != null && invoice.getECreateBillStatus() == 1) {
            OrderInvoiceErrorDTO error = new OrderInvoiceErrorDTO();
            error.setEType(OrderInvoiceErrorTypeEnum.CREATE_BILL_ERROR.getCode());
            error.setDesc(invoice.getECreateBillMessage());
            error.setOrderInvoiceId(invoice.getId());
            errorList.add(error);
        }
        // 检查客户配置地址错误状态
        if (invoice.getEPushAddressInvalidStatus() != null && invoice.getEPushAddressInvalidStatus() == 1) {
            OrderInvoiceErrorDTO error = new OrderInvoiceErrorDTO();
            error.setEType(OrderInvoiceErrorTypeEnum.PUSH_ADDRESS_INVALID_ERROR.getCode());
            error.setDesc(invoice.getEPushAddressInvalidMessage());
            error.setOrderInvoiceId(invoice.getId());
            errorList.add(error);
        }

        // 检查手工推送发票失败
        if (invoice.getEInvoiceFileMissingStatus() != null && invoice.getEInvoiceFileMissingStatus() == 1) {
            OrderInvoiceErrorDTO error = new OrderInvoiceErrorDTO();
            error.setEType(OrderInvoiceErrorTypeEnum.INVOICE_FILE_MISSING_ERROR.getCode());
            error.setDesc(invoice.getEInvoiceFileMissingMessage());
            error.setOrderInvoiceId(invoice.getId());
            errorList.add(error);
        }

        // 检查申请开票失败异常状态
        if (invoice.getEInvoiceApplyFailedStatus() != null && invoice.getEInvoiceApplyFailedStatus() == 1) {
            OrderInvoiceErrorDTO error = new OrderInvoiceErrorDTO();
            error.setEType(OrderInvoiceErrorTypeEnum.INVOICE_APPLY_FAILED_ERROR.getCode());
            error.setDesc(invoice.getEInvoiceApplyFailedMessage());
            error.setOrderInvoiceId(invoice.getId());
            errorList.add(error);
        }

        // 检查下载开票文件失败异常状态
        if (invoice.getEFileDownloadFailedStatus() != null && invoice.getEFileDownloadFailedStatus() == 1) {
            OrderInvoiceErrorDTO error = new OrderInvoiceErrorDTO();
            error.setEType(OrderInvoiceErrorTypeEnum.FILE_DOWNLOAD_FAILED_ERROR.getCode());
            error.setDesc(invoice.getEFileDownloadFailedMessage());
            error.setOrderInvoiceId(invoice.getId());
            errorList.add(error);
        }

        // 检查发票发送客户邮箱失败异常状态
        if (invoice.getEEmailSendFailedStatus() != null && invoice.getEEmailSendFailedStatus() == 1) {
            OrderInvoiceErrorDTO error = new OrderInvoiceErrorDTO();
            error.setEType(OrderInvoiceErrorTypeEnum.EMAIL_SEND_FAILED_ERROR.getCode());
            error.setDesc(invoice.getEEmailSendFailedMessage());
            error.setOrderInvoiceId(invoice.getId());
            errorList.add(error);
        }

        return errorList;
    }

    /**
     * 根据订单号查找订单的基本信息
     */
    @Override
    public OrderPO queryOrderBasicInfoByOrderCode(String orderCode) {
        if (StrUtilX.isEmpty(orderCode)) {
            return null;
        }
        Example example = new Example(OrderPO.class);
        example.createCriteria().andEqualTo("orderCode", orderCode);
        List<OrderPO> orderPOS = orderMapper.selectByExample(example);
        if (CollUtilX.isNotEmpty(orderPOS)) {
            return orderPOS.get(0);
        }
        return null;
    }

    /**
     * 组装取消条款
     */
    private ProductDTO assemblyRestrict(Integer productId, String startDate, String endDate) {
        Set<String> saleDateList = DateUtilX.getDateListToString(DateUtilX.stringToDate(startDate), DateUtilX.addDate(DateUtilX.stringToDate(endDate), -1));

        ProductDTO productDTO = new ProductDTO();
        productDTO.setProductId(productId);
        if (CollUtilX.isNotEmpty(saleDateList)) {

            Integer cancellationType = null;
            Integer cancellationAdvanceDays = null;
            String cancellationDueTime = "";
            String cancellationDeductionTerm = "";
            for (String saleDate : saleDateList) {
                ProductRestrictDTO productRestrictDTO = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.productRestrictKey, StrUtilX.concat(String.valueOf(productId), "_", saleDate)),
                        ProductRestrictDTO.class);
                log.info("订单条款为 {}", JSON.toJSONString(productRestrictDTO));
                if (productRestrictDTO != null) {
                    //处理首日条款为空
                    if (null == cancellationType) {
                        cancellationType = productRestrictDTO.getCancellationType();
                        cancellationAdvanceDays = productRestrictDTO.getCancellationAdvanceDays() == null ? 0 : productRestrictDTO.getCancellationAdvanceDays();
                        cancellationDueTime = productRestrictDTO.getCancellationDueTime();
                        cancellationDeductionTerm = productRestrictDTO.getCancellationDeductionTerm();
                    } else {
                        if (cancellationType > productRestrictDTO.getCancellationType()) {
                            cancellationType = productRestrictDTO.getCancellationType();
                            cancellationAdvanceDays = productRestrictDTO.getCancellationAdvanceDays() == null ? 0 : productRestrictDTO.getCancellationAdvanceDays();
                            cancellationDueTime = productRestrictDTO.getCancellationDueTime();
                            cancellationDeductionTerm = productRestrictDTO.getCancellationDeductionTerm();
                        } else if (cancellationType.equals(productRestrictDTO.getCancellationType())) {
                            // 当前条款
                            int nowDay = productRestrictDTO.getCancellationAdvanceDays() != null ? productRestrictDTO.getCancellationAdvanceDays() : 0;
                            int nowHours = StrUtilX.isNotEmpty(productRestrictDTO.getCancellationDueTime()) ? Integer.parseInt(productRestrictDTO.getCancellationDueTime().split(":")[0]) : 0;
                            Integer nowMinute = StrUtilX.isNotEmpty(productRestrictDTO.getCancellationDueTime()) && productRestrictDTO.getCancellationDueTime().contains(":") ?
                                    Integer.parseInt(productRestrictDTO.getCancellationDueTime().split(":")[1]) : 0;

                            //筛选的条款
                            int beforeDay = cancellationAdvanceDays != null ? cancellationAdvanceDays : 0;
                            int beforeHours = StrUtilX.isNotEmpty(cancellationDueTime) ? Integer.parseInt(cancellationDueTime.split(":")[0]) : 0;
                            int beforeMinute = StrUtilX.isNotEmpty(cancellationDueTime) && cancellationDueTime.contains(":") ?
                                    Integer.parseInt(cancellationDueTime.split(":")[1]) : 0;

                            // 天数越大越严格
                            // 小时越小越严格
                            // 分钟越小越严格
                            if (beforeDay < nowDay || beforeHours > nowHours || beforeMinute > nowHours) {
                                cancellationType = productRestrictDTO.getCancellationType();
                                cancellationAdvanceDays = productRestrictDTO.getCancellationAdvanceDays();
                                cancellationDueTime = productRestrictDTO.getCancellationDueTime();
                                cancellationDeductionTerm = productRestrictDTO.getCancellationDeductionTerm();
                            }
                        }
                    }
                }
            }
            productDTO.setCancellationType(cancellationType);
            productDTO.setCancellationAdvanceDays(cancellationAdvanceDays);
            productDTO.setCancellationDeductionTerm(cancellationDeductionTerm);
            productDTO.setCancellationDueTime(cancellationDueTime);
        }
        return productDTO;
    }

    /**
     * （对外提供接口）纯查询订单表数据
     *
     * @param orderId 客户编码
     * @return 订单数据
     */
    @Override
    public OrderDTO queryContactOrder(Integer orderId) {
        OrderPO orderQuery = new OrderPO();
        orderQuery.setId(orderId);
        return orderMapper.selectContactOrder(orderQuery);
    }

}
