package com.tiangong.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.tiangong.agent.remote.AgentConfirmationLetterConfigRemote;
import com.tiangong.agent.req.QueryAgentConfirmationLetterConfigReq;
import com.tiangong.agent.resp.QueryAgentConfirmationLetterConfigResp;
import com.tiangong.cloud.common.enums.result.ResultEnum;
import com.tiangong.common.Constant;
import com.tiangong.common.Response;
import com.tiangong.dis.dto.B2BOrderStatusPushRequest;
import com.tiangong.dis.remote.DisB2BRemote;
import com.tiangong.dis.remote.HotelProductRemote;
import com.tiangong.dto.common.CompanyDTO;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.dto.hotel.DestinationReq;
import com.tiangong.dto.hotel.EsHotelDto;
import com.tiangong.dto.hotel.base.*;
import com.tiangong.dto.order.OrderGuestDTO;
import com.tiangong.dto.order.OrderRoomGuestDTO;
import com.tiangong.dto.order.request.PriceRequestDTO;
import com.tiangong.dto.order.response.OrderFeeDTO;
import com.tiangong.dto.order.response.TaxDTO;
import com.tiangong.enums.*;
import com.tiangong.exception.SysException;
import com.tiangong.file.remote.FileRemote;
import com.tiangong.file.req.FileReq;
import com.tiangong.file.resp.FileResp;
import com.tiangong.finance.enums.CheckStatusEnum;
import com.tiangong.finance.remote.statement.AgentStatementRemote;
import com.tiangong.finance.remote.statement.request.CreateOrderStatementTaskDTO;
import com.tiangong.hotel.BaseHotelInfoUrl;
import com.tiangong.hotel.remote.HotelHeatRemote;
import com.tiangong.hotel.remote.HotelRemote;
import com.tiangong.hotel.req.HotelHeatReq;
import com.tiangong.keys.RedisKey;
import com.tiangong.order.config.BedNameConfig;
import com.tiangong.order.config.SettingsConstant;
import com.tiangong.order.convert.ProductSalePriceConvert;
import com.tiangong.order.domain.*;
import com.tiangong.order.dto.HotelHeatRoomNightDTO;
import com.tiangong.order.dto.HotelHeatRoomNightReq;
import com.tiangong.order.enums.*;
import com.tiangong.order.mapper.*;
import com.tiangong.order.remote.dto.*;
import com.tiangong.order.remote.request.*;
import com.tiangong.order.remote.response.*;
import com.tiangong.order.server.SupplyOrderServer;
import com.tiangong.order.service.*;
import com.tiangong.order.service.common.OrderCommonService;
import com.tiangong.order.util.CancellationConvertUtil;
import com.tiangong.organization.remote.AgentRemote;
import com.tiangong.organization.remote.CompanyRemote;
import com.tiangong.organization.remote.dto.AgentAccountConfig;
import com.tiangong.organization.remote.dto.AgentCreditLineDTO;
import com.tiangong.organization.remote.dto.AgentCreditLineResultDTO;
import com.tiangong.organization.remote.dto.CompanyAddDTO;
import com.tiangong.organization.remote.dto.CompanySelectDTO;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.req.SendEmailReq;
import com.tiangong.util.*;
import freemarker.template.Configuration;
import freemarker.template.Template;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.StringWriter;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.tiangong.util.DateUtilX.hour_format;

@Service
@Slf4j
public class OrderServiceImpl implements OrderService {

    @Autowired
    private OrderMapper orderMapper;

    @Autowired
    private OrderExtendMapper orderExtendMapper;

    @Autowired
    private GuestMapper guestMapper;

    @Autowired
    private OrderConfirmRecordMapper orderConfirmRecordMapper;

    @Autowired
    private OrderProductPriceMapper orderProductPriceMapper;

    @Autowired
    private OrderAttachmentMapper orderAttachmentMapper;

    @Autowired
    private SupplyOrderMapper supplyOrderMapper;

    @Autowired
    private OrderRequestMapper orderRequestMapper;

    @Autowired
    private OrderRemarkMapper orderRemarkMapper;

    @Autowired
    private SupplyProductMapper supplyProductMapper;

    @Autowired
    private OrderCommonService orderCommonService;

    @Autowired
    private OrderFinanceMapper orderFinanceMapper;

    @Autowired
    private AgentRemote agentRemote;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private SupplyOrderFinanceMapper supplyOrderFinanceMapper;

    @Autowired
    private OrderLogMapper orderLogMapper;

    @Autowired
    private SupplyOrderService supplyOrderService;

    @Autowired
    private Executor asyncSendCancelOrder;

    @Autowired
    private OrderInvoiceMapper orderInvoiceMapper;

    @Autowired
    private PersonInvoiceService personInvoiceService;

    @Autowired
    private SupplyOrderAdditionalChargesMapper supplyOrderAdditionalChargesMapper;

    @Autowired
    private OrderAdditionalChargesMapper orderAdditionalChargesMapper;

    @Autowired
    private OrderAdditionalChargesService orderAdditionalChargesService;

    @Autowired
    private SupplyProductPriceMapper supplyProductPriceMapper;

    @Autowired
    private OrderPayDetailMapper orderPayDetailMapper;

    @Autowired
    private AgentStatementRemote agentStatementRemote;

    @Resource
    private DisB2BRemote disB2BRemote;

    @Resource
    private OrgMapper orgMapper;

    @Autowired
    private SlsLoggerUtil slsLoggerUtil;

    @Autowired
    private SettingsConstant settingsConstant;

    @Autowired
    private HotelHeatRemote hotelHeatRemote;

    @Autowired
    private FileRemote fileRemote;

    @Autowired
    private AgentConfirmationLetterConfigRemote agentConfirmationLetterConfigRemote;

    @Autowired
    private OrderQueryService orderQueryService;

    @Autowired
    private SendCodeUtil sendCodeUtil;

    @Autowired
    private HotelProductRemote hotelProductRemote;

    @Autowired
    private CompanyRemote companyRemote;

    @Autowired
    private BedNameConfig bedNameConfig;

    @Autowired
    private OrderConfirmationLetterServer orderConfirmationLetterServer;

    @Autowired
    private HotelRemote hotelRemote;

    @Override
    @Transactional
    public Response<Object> confirmOrder(ConfirmOrderDTO request) {
        // 1. 检查并更新订单状态
        OrderPO orderPO = orderMapper.selectByPrimaryKey(request.getOrderId());
        if (Objects.equals(orderPO.getOrderConfirmationStatus(), ConfirmationStatusEnum.CANCELED.key)) {
            throw new SysException(ErrorCodeEnum.ORDER_IS_ALREADY_CANCEL);
        }

        OrderExtendPO extendQuery = new OrderExtendPO();
        extendQuery.setOrderId(orderPO.getId());
        List<OrderExtendPO> orderExtendPOList = orderExtendMapper.select(extendQuery);
        OrderExtendPO orderExtendPO = orderExtendPOList.get(0);

        OrderPO orderUpdate = new OrderPO();
        orderUpdate.setId(request.getOrderId());
        orderUpdate.setOrderConfirmationStatus(ConfirmationStatusEnum.CONFIRMED.key);
        orderUpdate.setUpdatedBy(request.getOperator());
        orderUpdate.setUpdatedDt(DateUtilX.dateToString(new Date(), hour_format));
        //取消掉取消中的状态
        orderUpdate.setModificationStatus(ModificationStatusEnum.MODIFICATION_STATUS_0.key);
        orderMapper.updateByPrimaryKeySelective(orderUpdate);

        //更新确认号
        if (StrUtilX.isNotEmpty(request.getConfirmationCode())) {
            OrderExtendPO orderExtendPO1 = new OrderExtendPO();
            orderExtendPO1.setId(orderExtendPO.getId());
            orderExtendPO1.setConfirmationCode(request.getConfirmationCode());
            orderExtendMapper.updateByPrimaryKeySelective(orderExtendPO1);
        }
        OrderConfirmRecordPO orderConfirmRecordPO = new OrderConfirmRecordPO();
        orderConfirmRecordPO.setOrderId(request.getOrderId());
        orderConfirmRecordPO.setConfirmationCode(request.getConfirmationCode());
        orderConfirmRecordPO.setConfirmationContent(request.getConfirmationContent());
        orderConfirmRecordPO.setCreatedBy(request.getOperator());
        orderConfirmRecordPO.setCreatedDt(DateUtilX.dateToString(new Date(), hour_format));
        orderConfirmRecordMapper.insert(orderConfirmRecordPO);
        OrderFinancePO orderFinanceQuery = new OrderFinancePO();
        orderFinanceQuery.setOrderId(request.getOrderId());
        OrderFinancePO orderFinancePO = orderFinanceMapper.selectOne(orderFinanceQuery);
        OrderFinancePO orderFinanceUpdate = new OrderFinancePO();
        orderFinanceUpdate.setId(orderFinancePO.getId());
        if (orderPO.getSettlementType() != SettlementTypeEnum.SINGLE.key) {
            //非单结更新对账状态
            if (!Objects.equals(orderFinancePO.getCheckStatus(), CheckStatusEnum.CHECKING.key)) {
                if (BigDecimal.ZERO.compareTo(orderFinancePO.getUnreceivedAmt()) == 0) {
                    //如果未收金额为0，则改为已对账
                    orderFinanceUpdate.setCheckStatus(CheckStatusEnum.CANNOT_CHECK.key);
                } else {
                    //如果未收金额不为0，则改为可出账
                    orderFinanceUpdate.setCheckStatus(CheckStatusEnum.CAN_CHECK.key);
                }
            }
        }
        // 未收金额=0结算状态为已结算
        if (BigDecimal.ZERO.compareTo(orderFinancePO.getUnreceivedAmt()) == 0) {
            orderFinanceUpdate.setSettlementStatus(1);
        } else {
            orderFinanceUpdate.setSettlementStatus(0);
        }
        orderFinanceMapper.updateByPrimaryKeySelective(orderFinanceUpdate);

        // 2. 发消息
        // TODO: 2019/7/11 发消息通知客户
        //stringRedisTemplate.convertAndSend(request.getCompanyCode() + "9", "all/订单号：" + orderPO.getOrderCode() + "<br>" + "订单以确认请尽快处理！");

        // 3. OTA操作订单（确认、取消）
        try {
            if (StrUtilX.isNotEmpty(orderPO.getChannelOrderCode()) && orderPO.getChannelCode().equals(ChannelEnum.DHUB.key)) {
                B2BOrderStatusPushRequest b2BOrderStatusPushRequest = new B2BOrderStatusPushRequest();
                b2BOrderStatusPushRequest.setCoOrderCode(orderPO.getChannelOrderCode());
                b2BOrderStatusPushRequest.setFcOrderCode(orderPO.getOrderCode());
                b2BOrderStatusPushRequest.setOrderStatus(3);
                b2BOrderStatusPushRequest.setMessage(null != orderExtendPO ? orderExtendPO.getCancelledReason() : null);
                b2BOrderStatusPushRequest.setHotelConfirmNo(null != orderExtendPO ? orderExtendPO.getConfirmationCode() : null);
                b2BOrderStatusPushRequest.setAgentCode(orderPO.getAgentCode());
                b2BOrderStatusPushRequest.setPartnerCode(orderPO.getPartnerCode());
                orderStatusPushRetry(b2BOrderStatusPushRequest);
            }
        } catch (Exception e) {
            orderCommonService.saveOrderLog(
                    request.getOrderId(),
                    request.getOperator(),
                    request.getOrderOwnerName(),
                    orderPO.getOrderCode(),
                    "", 0
            );
            log.error("渠道确认失败", e);
            return Response.success();
        }

        // 为已支付，查询支付明细，有个人支付走自助结算任务
        if (orderPO.getPayStatus() == PayStatusEnum.PAY.getNo() && orderPO.getOrderConfirmationStatus() == ConfirmationStatusEnum.UNCONFIRM.key) {
            OrderPayDetailPO queryOrderPay = new OrderPayDetailPO();
            queryOrderPay.setOrderCode(orderPO.getOrderCode());
            queryOrderPay.setPayType(0);
            List<OrderPayDetailPO> orderPayDetailList = orderPayDetailMapper.select(queryOrderPay);
            if (CollUtilX.isNotEmpty(orderPayDetailList)) {
                for (OrderPayDetailPO payDetailPO : orderPayDetailList) {
                    if (payDetailPO.getPartnerPayMethod() == 2) {
                        CreateOrderStatementTaskDTO orderStatementTask = ProductSalePriceConvert.INSTANCE.orderStatementTaskConvert(payDetailPO);
                        orderStatementTask.setTaskSource(0);
                        orderStatementTask.setSerialNo(payDetailPO.getPaySerialNo());
                        orderStatementTask.setAgentName(orderPO.getAgentName());
                        orderStatementTask.setPayer(payDetailPO.getPayAccount());
                        orderStatementTask.setChangeTime(orderPO.getCreatedDt());
                        orderStatementTask.setReceiveAmt(payDetailPO.getPersonPayAmount());
                        agentStatementRemote.createOrderStatementTask(orderStatementTask);
                    }
                }
            }
        }
        // 订单状态 已确认 且已支付 情况下 发送确认函 异步
        if (orderPO.getPayStatus() != null && orderPO.getPayStatus() == PayStatusEnum.PAY.getNo()) {
            SendOrderConfirmationLetterReqDTO sendOrderConfirmationLetterReqDTO = new SendOrderConfirmationLetterReqDTO();
            sendOrderConfirmationLetterReqDTO.setId(request.getOrderId());
            sendOrderConfirmationLetterReqDTO.setOrderCode(orderPO.getOrderCode());
            sendOrderConfirmationLetterReqDTO.setAgentCode(orderPO.getAgentCode());
            sendOrderConfirmationLetterReqDTO.setEmail(orderPO.getContactEmail());
            asyncSendOrderConfirmation(sendOrderConfirmationLetterReqDTO);
        }

        // 4. 记日志
        String content = "确认订单";
        orderCommonService.saveOrderLog(
                request.getOrderId(),
                request.getOperator(),
                request.getOrderOwnerName(),
                orderPO.getOrderCode(),
                content, 4
        );

        try {
            //加入到统计报表队列中
            stringRedisTemplate.convertAndSend(RedisKey.REPORT_STATISTICS, orderPO.getOrderCode());
        } catch (Exception e) {
            log.error("加入到统计报表队列异常,error:", e);
        }

        return Response.success();
    }

    @Override
    @Transactional
    public void cancelOrder(CancelOrderDTO request, Integer type, boolean checkCancel) {
        Date start = new Date();
        // 1. 检查订单状态
        OrderPO orderPO = orderMapper.selectByPrimaryKey(request.getOrderId());
        saveSlsLog("排查慢问题：取消订单，查询订单详情", start, request.getRequestId());
        // 判断是否校验取消状态
        if (request.getNotCheckCancelStatus() != null && request.getNotCheckCancelStatus()) {
            if (orderPO.getOrderConfirmationStatus().equals(ConfirmationStatusEnum.CANCELED.key)) {
                log.info("取消订单，订单已取消，orderId={}", request.getOrderId());
                return;
            }
        } else {
            if (orderPO.getOrderConfirmationStatus().equals(ConfirmationStatusEnum.CANCELED.key)) {
                // 判断是否需要抛出异常
                if (checkCancel) {
                    throw new SysException(ErrorCodeEnum.ORDER_IS_CANCEL);
                } else {
                    return;
                }
            }
        }

        if (request.getRefundFee() != null && request.getRefundFee().compareTo(orderPO.getOrderAmt()) > 0) {
            throw new SysException(ErrorCodeEnum.CANCEL_ORDER_AMOUNT_IS_ERROR);
        }

        Date start1 = new Date();
        OrderExtendPO orderExtendQuery = new OrderExtendPO();
        orderExtendQuery.setOrderId(request.getOrderId());
        List<OrderExtendPO> orderExtendPOList = orderExtendMapper.select(orderExtendQuery);
        OrderExtendPO orderExtendPO = orderExtendPOList.get(0);

        // 2. 检查供货单状态：没有已确认的供货单才能取消
        SupplyOrderPO supplyOrderQuery = new SupplyOrderPO();
        supplyOrderQuery.setOrderId(request.getOrderId());
        List<SupplyOrderPO> supplyOrderPOList = supplyOrderMapper.select(supplyOrderQuery);

        saveSlsLog("排查慢问题：取消订单，查询订单扩展、供货单信息", start1, request.getRequestId());
        BigDecimal supplyOrderTotalAmt = BigDecimal.ZERO;
        for (SupplyOrderPO supplyOrderPO : supplyOrderPOList) {
            // 判断已确认和待确认情况，都不允许直接取消订单
            if (Objects.equals(supplyOrderPO.getConfirmationStatus(), ConfirmationStatusEnum.CONFIRMED.key)
                    || Objects.equals(supplyOrderPO.getConfirmationStatus(), ConfirmationStatusEnum.DONE.key)
                    || (Objects.equals(supplyOrderPO.getConfirmationStatus(), ConfirmationStatusEnum.UNCONFIRM.key) && type == 1)) {
                // 通过具体枚举值取
                String failReason = "供货单（" + supplyOrderPO.getSupplyOrderCode() + "）" + ConfirmationStatusEnum.getValueByKey(supplyOrderPO.getConfirmationStatus()) + "，请先取消！";
                throw new SysException(ErrorCodeEnum.SUPPLY_ORDER_IS_PENDING_CONFIRMATION_PLEASE_CANCEL_FIRST.errorCode, failReason);
            }
            supplyOrderTotalAmt = supplyOrderTotalAmt.add(supplyOrderPO.getSupplyOrderAmt());
        }

        // 默认是已取消
        if (null == request.getSupplyResult()) {
            request.setSupplyResult(ConfirmationStatusEnum.CANCELED.key);
        }

        // 3. 更新订单状态
        if (Objects.equals(ConfirmationStatusEnum.CANCELED.key, request.getSupplyResult())) {
            Date start3 = new Date();
            OrderPO orderUpdate = new OrderPO();
            orderUpdate.setId(request.getOrderId());
            orderUpdate.setOrderConfirmationStatus(ConfirmationStatusEnum.CANCELED.key);
            orderUpdate.setModificationStatus(0);
            BigDecimal orderSum = BigDecimal.ZERO;
            if (request.getRefundFee() != null) {
                orderUpdate.setRefundFee(request.getRefundFee());
                // 现付订单、金额 = 0
                if (orderPO.getPayMethod() == null || !orderPO.getPayMethod().equals(PayMethodEnum.PAY.key)) {
                    orderSum = request.getRefundFee();
                }
            }

            orderUpdate.setOrderAmt(orderSum);
            orderUpdate.setProfit(orderSum.subtract(supplyOrderTotalAmt));
            orderUpdate.setUpdatedBy(request.getOperator());
            orderUpdate.setUpdatedDt(DateUtilX.dateToString(new Date(), hour_format));
            //取消订单时,如果为问题单则将问题单改成正常单
            if (orderPO.getIsAbnormal() != null && orderPO.getIsAbnormal() == 1) {
                orderUpdate.setIsAbnormal(0);
            }
            orderMapper.updateByPrimaryKeySelective(orderUpdate);

            //更新取消原因
            if (StrUtilX.isNotEmpty(request.getCancelledReason())) {
                OrderExtendPO orderExtendPO1 = new OrderExtendPO();
                orderExtendPO1.setId(orderExtendPO.getId());
                orderExtendPO1.setCancelledReason(request.getCancelledReason());
                orderExtendMapper.updateByPrimaryKeySelective(orderExtendPO1);
            }
            saveSlsLog("排查慢问题：取消订单，更新取消原因", start3, request.getRequestId());

            // 4. 更新未收金额和结算状态
            Date start4 = new Date();
            OrderFinancePO orderFinanceQuery = new OrderFinancePO();
            orderFinanceQuery.setOrderId(request.getOrderId());
            OrderFinancePO orderFinancePO = orderFinanceMapper.selectOne(orderFinanceQuery);
            OrderFinancePO orderFinanceUpdate = new OrderFinancePO();
            orderFinanceUpdate.setId(orderFinancePO.getId());
            orderFinanceUpdate.setUnreceivedAmt(orderSum.subtract(orderFinancePO.getReceivedAmt()));
            if (orderFinancePO.getReceivedAmt().compareTo(orderSum) == 0) {
                orderFinanceUpdate.setSettlementStatus(1);
            } else {
                orderFinanceUpdate.setSettlementStatus(0);
            }
            orderFinanceMapper.updateByPrimaryKeySelective(orderFinanceUpdate);
            saveSlsLog("排查慢问题：取消订单，更新未收金额和结算状态", start4, request.getRequestId());

            Date start5 = new Date();
            if (orderPO.getSettlementType() != SettlementTypeEnum.SINGLE.key && orderPO.getPayMethod() != null && !orderPO.getPayMethod().equals(PayMethodEnum.PAY.key)) {
                //非单结更新对账状态
                if (!Objects.equals(orderFinancePO.getCheckStatus(), CheckStatusEnum.CHECKING.key)) {
                    if (BigDecimal.ZERO.compareTo(orderFinanceUpdate.getUnreceivedAmt()) == 0) {
                        //如果未收金额为0，则改为已对账
                        orderFinanceUpdate.setCheckStatus(CheckStatusEnum.CANNOT_CHECK.key);
                    } else {
                        //如果未收金额不为0，则改为可出账
                        orderFinanceUpdate.setCheckStatus(CheckStatusEnum.CAN_CHECK.key);
                    }
                    orderFinanceMapper.updateByPrimaryKeySelective(orderFinanceUpdate);
                }

                //非单结自动退额度
                // TODO: 2019/7/12 调分销商信用账户明细接口，查询已挂账金额
                //退额度=原订单金额-现订单金额

                BigDecimal subtract = orderPO.getOrderAmt().subtract(orderSum);
                AgentCreditLineDTO agentCreditLineDTO = new AgentCreditLineDTO();
                if (StrUtilX.isNotEmpty(orderPO.getSubtractLineAccount())) {
                    agentCreditLineDTO.setAgentCode(orderPO.getSubtractLineAccount());
                } else {
                    agentCreditLineDTO.setAgentCode(orderPO.getAgentCode());
                }
                agentCreditLineDTO.setOrderCode(orderPO.getOrderCode());
                agentCreditLineDTO.setCreatedBy(request.getOperator());
                agentCreditLineDTO.setDeductRefundCreditLine(subtract);

                Response<AgentCreditLineResultDTO> creditLineResponse = agentRemote.modifyDeductRefundCreditLine(Collections.singletonList(agentCreditLineDTO));
                if (!creditLineResponse.isSuccess()) {
                    throw new SysException(creditLineResponse.getFailCode(), creditLineResponse.getFailReason());
                }
                int updateCount = creditLineResponse.getModel().getUpdateCount();
                if (updateCount==0) {
                    throw new SysException(ErrorCodeEnum.WITHDRAWAL_QUOTA_FAIL);
//                    response.setFailCode(ResultCodeEnum.FAILURE.code + "");
//                    response.setFailReason("退额失败，请稍后重试");
//                    return response;
                }
            }
            saveSlsLog("排查慢问题：取消订单，非单结更新对账状态", start5, request.getRequestId());
        }
        // 5. 如果申请id不为空，则更新订单申请状态
        if (request.getOrderRequestId() != null && request.getOrderRequestId() != 0) {
            Date start6 = new Date();
            OrderRequestPO orderRequestUpdate = new OrderRequestPO();
            orderRequestUpdate.setId(request.getOrderRequestId());
            orderRequestUpdate.setUpdatedBy(request.getOperator());
            orderRequestUpdate.setUpdatedDt(DateUtilX.dateToString(new Date(), hour_format));
            if (Objects.equals(ConfirmationStatusEnum.CANCELED.key, request.getSupplyResult())) {
                orderRequestUpdate.setHandleResult(1);
            } else {
                orderRequestUpdate.setHandleResult(2);
            }
            orderRequestMapper.updateByPrimaryKeySelective(orderRequestUpdate);
            saveSlsLog("排查慢问题：取消订单，更新订单申请状态", start6, request.getRequestId());
        } else {
            Date start6 = new Date();
            //自动消费取消任务
            Example orderRequestExample = new Example(OrderRequestPO.class);
            Example.Criteria orderRequestCriteria = orderRequestExample.createCriteria();
            orderRequestCriteria.andEqualTo("orderId", request.getOrderId());
            OrderRequestPO orderRequestPO = new OrderRequestPO();
            orderRequestPO.setHandleResult(1);
            orderRequestMapper.updateByExampleSelective(orderRequestPO, orderRequestExample);

            OrderPO orderPOUpdate = new OrderPO();
            orderPOUpdate.setId(request.getOrderId());
            orderPOUpdate.setModificationStatus(0);
            orderMapper.updateByPrimaryKeySelective(orderPOUpdate);
            saveSlsLog("排查慢问题：取消订单，自动消费取消任务", start6, request.getRequestId());
        }

        // 异步执行通知下游
        CompletableFuture.runAsync(() -> {
            Date start7 = new Date();
            try {
                // 6. OTA操作订单（确认、取消）
                // 2024-09-05：b2b 订单不需要通知下游，dhub 创建订单 但是请求 来源于dhub 也不发起通知，会导致重复推送，所以去掉
                // 只有一种情况会通知 当订单来源与 dhub 且 人工在页面点击取消订单 需要通知下游
                if (StrUtilX.isNotEmpty(orderPO.getChannelOrderCode())
                        && !(StrUtilX.isNotEmpty(request.getChannelCode()) && request.getChannelCode().equals(ChannelEnum.DHUB.key))
                        && (StrUtilX.isNotEmpty(orderPO.getChannelCode()) && orderPO.getChannelCode().equals(ChannelEnum.DHUB.key))
                ) {
                    B2BOrderStatusPushRequest b2BOrderStatusPushRequest = new B2BOrderStatusPushRequest();
                    b2BOrderStatusPushRequest.setCoOrderCode(orderPO.getChannelOrderCode());
                    b2BOrderStatusPushRequest.setFcOrderCode(orderPO.getOrderCode());
                    if (Objects.equals(request.getSupplyResult(), 2)) {
                        if (orderPO.getOrderConfirmationStatus() == OrderStatusEnum.CONFIRMING.no) {
                            //待确认的时候表示拒单，订单状态推送为4
                            b2BOrderStatusPushRequest.setOrderStatus(4);
                        } else {
                            //确认后的订单状态再次取消，则推送6，表示取消订单成功
                            b2BOrderStatusPushRequest.setOrderStatus(6);
                            //输出取消罚金
                            b2BOrderStatusPushRequest.setCurrency(SettlementCurrencyEnum.getCodeByKey(String.valueOf(orderPO.getSaleCurrency())));
                            b2BOrderStatusPushRequest.setPenaltiesValue(orderPO.getRefundFee());
                        }
                    } else if (Objects.equals(request.getSupplyResult(), 5)) {
                        b2BOrderStatusPushRequest.setOrderStatus(7);
                    }
                    b2BOrderStatusPushRequest.setMessage(request.getCancelledReason());
                    b2BOrderStatusPushRequest.setHotelConfirmNo(null != orderExtendPO ? orderExtendPO.getConfirmationCode() : null);
                    b2BOrderStatusPushRequest.setAgentCode(orderPO.getAgentCode());
                    b2BOrderStatusPushRequest.setPartnerCode(orderPO.getPartnerCode());
                    b2BOrderStatusPushRequest.setPenaltiesValue(request.getRefundFee());
                    orderStatusPushRetry(b2BOrderStatusPushRequest);
                }
            } catch (Exception e) {
                log.error("调用渠道取消订单失败", e);
                orderCommonService.saveOrderLog(
                        request.getOrderId(),
                        request.getOperator(),
                        request.getOrderOwnerName(),
                        orderPO.getOrderCode(),
                        "调用渠道取消订单失败",
                        3
                );
            }
            saveSlsLog("排查慢问题：取消订单，OTA操作订单（确认、取消）", start7, request.getRequestId());
        });

        Date start8 = new Date();
        // 7. 记日志
        String content = "取消订单";
        if (Objects.equals(ConfirmationStatusEnum.CANCELED.key, request.getSupplyResult())) {
            content = content + "，订单状态更新为：已取消";
        }
        if (request.getRefundFee() != null) {
            content = content + "，退订费：" + request.getRefundFee();
        } else {
            content = content + "，退订费：0";
        }
        if (StrUtilX.isNotEmpty(request.getCancelledReason())) {
            content = content + "，取消原因：" + request.getCancelledReason();
        }

        if (StrUtilX.isNotEmpty(request.getCancelledContent())) {
            content = content + "，取消内容：" + request.getCancelledContent();
        }

        orderCommonService.saveOrderLog(
                request.getOrderId(),
                request.getOperator(),
                request.getOrderOwnerName(),
                orderPO.getOrderCode(),
                content, 4
        );
        saveSlsLog("排查慢问题：取消订单，记日志", start8, request.getRequestId());

        Date start9 = new Date();
        try {
            //加入到统计报表队列中
            stringRedisTemplate.convertAndSend(RedisKey.REPORT_STATISTICS, orderPO.getOrderCode());
        } catch (Exception e) {
            log.error("加入到统计报表队列异常,error:", e);
        }
        saveSlsLog("排查慢问题：取消订单，自动消费取消任务", start9, request.getRequestId());
    }

    /**
     * 保存日志
     */
    private void saveSlsLog(String message, Date start, String logId) {
        Map<String, String> map = new HashMap<>();
        map.put(SlsEnum.LEVEL.getType(), SlsLogLevelEnum.INFO.getLevel());
        map.put(SlsEnum.NAME.getType(), "cancelOrder");
        map.put("logFlag", "排查慢问题");
        map.put("logId", logId);
        map.put(SlsEnum.MESSAGE.getType(), message);
        map.put("start", DateUtilX.dateToString(start, DateUtilX.hour_ms_format));
        slsLoggerUtil.saveLog(map, "cancelOrder", "tiangong-order-server");
    }

    @Override
    @Transactional
    public void modifyGuest(OrderGuestDTO request) {
        OrderPO orderPO = orderMapper.selectByPrimaryKey(request.getOrderId());

        //查询入住人列表
        GuestPO guestPO = new GuestPO();
        guestPO.setOrderId(orderPO.getId());
        List<GuestPO> orderGuestPOList = guestMapper.select(guestPO);
        boolean needUpdate = false;
        StringBuilder guestSb = new StringBuilder();
        StringBuilder logSb = new StringBuilder();
        if (CollUtilX.isNotEmpty(orderGuestPOList)) {
            for (GuestPO guestPO1 : orderGuestPOList) {
                if (guestPO1.getId().equals(request.getId())) {
                    //如果是改名字的话，需要修改订单表和供货单表的入住人名字
                    if (!guestPO1.getName().equals(request.getName())) {
                        needUpdate = true;
                    }
                    if (request.getName() == null || !guestPO1.getName().equals(request.getName())) {
                        logSb.append("入住人姓名").append(Optional.ofNullable(guestPO1.getName()).orElse("空"))
                                .append("变成").append(Optional.ofNullable(request.getName()).orElse("空")).append(",");
                    }
                    if (StrUtilX.isEmpty(guestPO1.getName()) && StrUtilX.isNotEmpty(request.getFirstName()) && StrUtilX.isNotEmpty(request.getLastName())) {
                        logSb.append("入住人姓名")
                                .append(Optional.ofNullable(guestPO1.getName()).orElse("空"))
                                .append("变成")
                                .append(Optional.ofNullable(request.getLastName()).orElse("空"))
                                .append("/")
                                .append(Optional.ofNullable(request.getFirstName()).orElse("空"))
                                .append(",");
                    }
                    if ((StrUtilX.isNotEmpty(guestPO1.getFirstName()) && StrUtilX.isNotEmpty(guestPO1.getLastName())
                            && StrUtilX.isEmpty(request.getFirstName()) && StrUtilX.isEmpty(request.getLastName()))
                            || (StrUtilX.isEmpty(guestPO1.getFirstName()) && StrUtilX.isEmpty(guestPO1.getLastName())
                            && StrUtilX.isNotEmpty(request.getFirstName()) && StrUtilX.isNotEmpty(request.getLastName()))
                            || ((StrUtilX.isNotEmpty(guestPO1.getFirstName()) && StrUtilX.isNotEmpty(guestPO1.getLastName())
                            && StrUtilX.isNotEmpty(request.getFirstName()) && StrUtilX.isNotEmpty(request.getLastName()))
                            && (!guestPO1.getFirstName().equals(request.getFirstName()) || !guestPO1.getLastName().equals(request.getLastName())))) {
                        logSb.append("入住人姓名英文")
                                .append(Optional.ofNullable(guestPO1.getLastName()).orElse("空"))
                                .append("/")
                                .append(Optional.ofNullable(guestPO1.getFirstName()).orElse("空"))
                                .append("变成")
                                .append(Optional.ofNullable(request.getLastName()).orElse("空"))
                                .append("/")
                                .append(Optional.ofNullable(request.getFirstName()).orElse("空"))
                                .append(",");
                    }
                    if (guestPO1.getIdCardType() != null && request.getIdCardType() == null || guestPO1.getIdCardType() == null &&
                            request.getIdCardType() != null || guestPO1.getIdCardType() != null && !guestPO1.getIdCardType().equals(request.getIdCardType())) {
                        logSb.append("证件类型").append(Optional.of(getIdCardTypeStr(guestPO1.getIdCardType())).orElse("空"))
                                .append("变成").append(Optional.of(getIdCardTypeStr(request.getIdCardType())).orElse("空")).append(",");
                    }
                    if (guestPO1.getIdCardNo() != null && request.getIdCardNo() == null || guestPO1.getIdCardNo() == null &&
                            request.getIdCardNo() != null || guestPO1.getIdCardNo() != null && !guestPO1.getIdCardNo().equals(request.getIdCardNo())) {
                        logSb.append("证件号").append(Optional.ofNullable(guestPO1.getIdCardNo()).orElse("空"))
                                .append("变成").append(Optional.ofNullable(request.getIdCardNo()).orElse("空")).append(",");
                    }
                    if (guestPO1.getMembershipCardNumber() != null && request.getMembershipCardNumber() == null || guestPO1.getMembershipCardNumber() == null &&
                            request.getMembershipCardNumber() != null || guestPO1.getMembershipCardNumber() != null && !guestPO1.getMembershipCardNumber().equals(request.getMembershipCardNumber())) {
                        logSb.append("会员卡号").append(Optional.ofNullable(guestPO1.getMembershipCardNumber()).orElse("空"))
                                .append("变成").append(Optional.ofNullable(request.getMembershipCardNumber()).orElse("空")).append(",");
                    }

                    if (guestPO1.getMobileNo() != null && request.getMobileNo() == null || guestPO1.getMobileNo() == null &&
                            request.getMobileNo() != null || guestPO1.getMobileNo() != null && !guestPO1.getMobileNo().equals(request.getMobileNo())) {
                        logSb.append("手机号码").append(Optional.ofNullable(guestPO1.getMobileNo()).orElse("空"))
                                .append("变成").append(Optional.ofNullable(request.getMobileNo()).orElse("空")).append(",");
                    }

                    if ((guestPO1.getEarnPoints() != null && request.getEarnPoints() == null)
                            || (guestPO1.getEarnPoints() == null && request.getEarnPoints() != null)
                            || (guestPO1.getEarnPoints() != null && request.getEarnPoints() != null && !guestPO1.getEarnPoints().equals(request.getEarnPoints()))) {
                        logSb.append("入住人是否获得积分").append(Optional.of(getEarnPointsStr(guestPO1.getEarnPoints())).orElse("空"))
                                .append("变成").append(Optional.of(getEarnPointsStr(request.getEarnPoints())).orElse("空")).append(",");
                    }

                    guestSb.append(request.getName()).append(",");
                } else {
                    guestSb.append(guestPO1.getName()).append(",");
                }
            }
            guestSb.deleteCharAt(guestSb.length() - 1);
        }


        if (needUpdate) {
            // 1.更新订单表
            OrderPO orderUpdate = new OrderPO();
            orderUpdate.setId(request.getOrderId());
            orderUpdate.setGuest(guestSb.toString());
            orderMapper.updateByPrimaryKeySelective(orderUpdate);

            // 3. 更新供货产品关联入住人
            SupplyProductPO supplyProductUpdate = new SupplyProductPO();
            supplyProductUpdate.setOrderId(request.getOrderId());
            supplyProductUpdate.setGuest(guestSb.toString());
            Example supplyProductExample = new Example(SupplyProductPO.class);
            Example.Criteria supplyProductCriteria = supplyProductExample.createCriteria();
            supplyProductCriteria.andEqualTo("orderId", request.getOrderId());
            supplyProductMapper.updateByExampleSelective(supplyProductUpdate, supplyProductExample);
        }

        // 2.更新入住人表
        GuestPO guestUpdate = ProductSalePriceConvert.INSTANCE.guestPOConvert(request);

        if (StrUtilX.isEmpty(guestUpdate.getName()) && StrUtilX.isNotEmpty(guestUpdate.getFirstName()) && StrUtilX.isNotEmpty(guestUpdate.getLastName())) {
            guestUpdate.setName(guestUpdate.getLastName() + "/" + guestUpdate.getFirstName());
        }
        guestMapper.updateByPrimaryKeySelective(guestUpdate);

        if (logSb.length() > 1) {
            logSb.deleteCharAt(logSb.length() - 1);
            // 3. 记日志
            orderCommonService.saveOrderLog(
                    request.getOrderId(),
                    request.getUpdatedBy(),
                    request.getUpdatedBy(),
                    orderPO.getOrderCode(),
                    "订单客人修改：" + logSb
            );
        }
    }

    private String getIdCardTypeStr(Integer idCardType) {
        if (idCardType == null) {
            return "空";
        } else if (idCardType == 1) {
            return "身份证";
        } else if (idCardType == 2) {
            return "驾驶证";
        } else if (idCardType == 3) {
            return "护照";
        } else if (idCardType == 4) {
            return "回乡证";
        } else if (idCardType == 5) {
            return "台胞证";
        } else if (idCardType == 6) {
            return "港澳台居民居住证";
        } else {
            return "未知";
        }
    }

    private String getEarnPointsStr(Integer earnPoints) {
        if (earnPoints == null) {
            return "空";
        } else if (earnPoints == 1) {
            return "是";
        } else if (earnPoints == 0) {
            return "否";
        } else {
            return "未知";
        }
    }

    /**
     * 修改房型
     */
    @Override
    public int modifyRoom(ModifyRoomDTO request) {
        OrderPO orderPO = new OrderPO();
        orderPO.setId(request.getOrderId());
        OrderPO beforeOrder = orderMapper.selectOne(orderPO);

        orderPO.setRoomId(request.getRoomId());
        orderPO.setRoomName(request.getRoomName());
        int i = orderMapper.updateByPrimaryKeySelective(orderPO);

        orderCommonService.saveOrderLog(request.getOrderId(), request.getOperator(),
                request.getLoginName(), beforeOrder.getOrderCode(), "订单房型" + beforeOrder.getRoomName() + "修改为" + orderPO.getRoomName());
        return i;
    }

    @Override
    @Transactional
    public void modifySpecialRequirement(ModifySpecialRequirementDTO request) {
        OrderExtendPO orderExtendQuery = new OrderExtendPO();
        orderExtendQuery.setOrderId(request.getOrderId());
        List<OrderExtendPO> orderExtendPOList = orderExtendMapper.select(orderExtendQuery);
        OrderExtendPO orderExtendPO = orderExtendPOList.get(0);

        // 1. 保存特殊要求
        OrderExtendPO orderUpdate = new OrderExtendPO();
        orderUpdate.setId(orderExtendPO.getId());
        orderUpdate.setSpecialRequest(request.getSpecialRequest());
        orderUpdate.setIsShowOnSupplyOrder(request.getIsShownOnSupplyOrder());
        orderUpdate.setUpdatedBy(request.getOperator());
        orderUpdate.setUpdatedDt(DateUtilX.dateToString(new Date(), hour_format));
        orderExtendMapper.updateByPrimaryKeySelective(orderUpdate);

        // 2. 记日志
        String content = "特殊要求改为："
                + request.getSpecialRequest();
        orderCommonService.saveOrderLog(
                request.getOrderId(),
                request.getOperator(),
                request.getOrderOwnerName(),
                orderExtendPO.getOrderCode(),
                content
        );
    }

    @Override
    @Transactional
    public void modifySalePrice(ModifySalePriceDTO request) {
        StringBuilder logSb = new StringBuilder("订单售价");
        OrderPO orderPO = orderMapper.selectByPrimaryKey(request.getOrderId());
        BigDecimal salePriceTotalAmt = BigDecimal.ZERO;

        boolean isChange = false;
        OrderProductPricePO orderProductPriceQuery = new OrderProductPricePO();
        orderProductPriceQuery.setOrderId(request.getOrderId());
        List<OrderProductPricePO> orderProductPricePOList = orderProductPriceMapper.select(orderProductPriceQuery);
        for (PriceRequestDTO priceDTO : request.getSalePriceList()) {
            for (OrderProductPricePO orderProductPricePO : orderProductPricePOList) {
                if (DateUtilX.compare(DateUtilX.stringToDate(priceDTO.getSaleDate()), orderProductPricePO.getSaleDate()) == 0) {
                    if (priceDTO.getSalePrice().compareTo(orderProductPricePO.getSalePrice()) != 0) {
                        logSb.append(priceDTO.getSaleDate())
                                .append("改为").append(priceDTO.getSalePrice()).append(",");

                        orderProductPricePO.setSalePrice(priceDTO.getSalePrice());
                        orderProductPriceMapper.updateByPrimaryKeySelective(orderProductPricePO);
                        isChange = true;
                    }
                    break;
                }
            }
            salePriceTotalAmt = salePriceTotalAmt.add(priceDTO.getSalePrice().multiply(BigDecimal.valueOf(orderPO.getRoomQty().doubleValue())));
        }
        if (!isChange) {
            return;
        }

        BigDecimal changeAmt = salePriceTotalAmt.subtract(orderPO.getSalePrice());
        if (orderPO.getSalePrice().compareTo(salePriceTotalAmt) != 0) {
            logSb.append("总售价由").append(orderPO.getOrderAmt())
                    .append("改为").append(orderPO.getOrderAmt().add(changeAmt));
        }
        OrderPO orderUpdate = new OrderPO();
        orderUpdate.setId(request.getOrderId());
        if (!orderPO.getOrderConfirmationStatus().equals(OrderStatusEnum.CANCELED.no)) {//非取消状态，才修改订单金额和利润
            log.info("修改订单价格，订单为取消状态，订单Id：{}", orderPO.getId());
            orderUpdate.setOrderAmt(orderPO.getOrderAmt().add(changeAmt));
            orderUpdate.setProfit(orderPO.getProfit().add(changeAmt));
        }
        orderUpdate.setSalePrice(salePriceTotalAmt);
        orderMapper.updateByPrimaryKeySelective(orderUpdate);

        if (changeAmt.compareTo(BigDecimal.ZERO) != 0 && !orderPO.getOrderConfirmationStatus().equals(OrderStatusEnum.CANCELED.no)) {//非取消状态，才修改订单结算金额和状态等
            //更新订单未结算金额和结算状态
            OrderFinancePO orderFinanceQuery = new OrderFinancePO();
            orderFinanceQuery.setOrderId(request.getOrderId());
            OrderFinancePO orderFinancePO = orderFinanceMapper.selectOne(orderFinanceQuery);
            OrderFinancePO orderFinanceUpdate = new OrderFinancePO();
            orderFinanceUpdate.setId(orderFinancePO.getId());
            orderFinanceUpdate.setUnreceivedAmt(orderFinancePO.getUnreceivedAmt().add(changeAmt));
            if (BigDecimal.ZERO.compareTo(orderFinanceUpdate.getUnreceivedAmt()) == 0) {
                orderFinanceUpdate.setSettlementStatus(1);
            } else {
                orderFinanceUpdate.setSettlementStatus(0);
            }
            orderFinanceMapper.updateByPrimaryKeySelective(orderFinanceUpdate);

            if (orderPO.getSettlementType() != SettlementTypeEnum.SINGLE.key) {
                //非单结更新对账状态
                if (!Objects.equals(orderFinancePO.getCheckStatus(), CheckStatusEnum.CHECKING.key)) {
                    if (BigDecimal.ZERO.compareTo(orderFinanceUpdate.getUnreceivedAmt()) == 0) {
                        //如果未收金额为0，则改为已对账
                        orderFinanceUpdate.setCheckStatus(CheckStatusEnum.CANNOT_CHECK.key);
                    } else {
                        //如果未收金额不为0，则改为可出账
                        orderFinanceUpdate.setCheckStatus(CheckStatusEnum.CAN_CHECK.key);
                    }
                    orderFinanceMapper.updateByPrimaryKeySelective(orderFinanceUpdate);
                }

                // 不是现付才退额度
                if (!PayMethodEnum.PAY.key.equals(orderPO.getPayMethod())) {
                    // 非单结订单扣退额度，非取消状态，就扣退额度
                    // TODO: 2019/7/12 调分销商信用账户明细接口，查询已挂账金额
                    AgentCreditLineDTO agentCreditLineDTO = new AgentCreditLineDTO();
                    if (StrUtilX.isNotEmpty(orderPO.getSubtractLineAccount())) {
                        agentCreditLineDTO.setAgentCode(orderPO.getSubtractLineAccount());
                    } else {
                        agentCreditLineDTO.setAgentCode(orderPO.getAgentCode());
                    }
                    agentCreditLineDTO.setAgentCode(orderPO.getAgentCode());
                    agentCreditLineDTO.setOrderCode(orderPO.getOrderCode());
                    //原订单金额减去修改后金额等于扣退额度i

                    if ((orderPO.getOrderAmt().subtract(orderPO.getOrderAmt().add(changeAmt)).compareTo(BigDecimal.ZERO) < 0)) {
//                    agentCreditLineDTO.setDeductRefundCreditLine("-" + (orderPO.getOrderAmt().subtract(orderPO.getOrderAmt().add(changeAmt)).toPlainString()));
                    } else {
//                    agentCreditLineDTO.setDeductRefundCreditLine("+" + (orderPO.getOrderAmt().subtract(orderPO.getOrderAmt().add(changeAmt)).toPlainString()));

                    }
                    Response<AgentCreditLineResultDTO> creditLineResponse = agentRemote.modifyDeductRefundCreditLine(Collections.singletonList(agentCreditLineDTO));
                    if (!creditLineResponse.isSuccess()) {
                        throw new SysException(creditLineResponse.getFailCode(), creditLineResponse.getFailReason());
                    }
                }

            }
        }

        //记日志
        orderCommonService.saveOrderLog(
                request.getOrderId(),
                request.getOperator(),
                request.getOrderOwnerName(),
                orderPO.getOrderCode(),
                logSb.toString()
        );
    }

    @Override
    public void addRemark(AddRemarkDTO request) {
        OrderRemarkPO orderRemarkInsert = ProductSalePriceConvert.INSTANCE.orderRemarkInsertConvert(request);
        if (1 != request.getRemarkType()) {
            orderRemarkInsert.setReceiver(RemarkEnum.getremark(request.getRemarkType()));
        }
        orderRemarkInsert.setCreatedBy(request.getOperator());
        orderRemarkInsert.setCreatedDt(DateUtilX.dateToString(new Date(), hour_format));
        orderRemarkMapper.insert(orderRemarkInsert);

        // 记日志
        StringBuilder logSb = new StringBuilder();
        if (request.getRemarkType() != null) {
            if (request.getRemarkType() == 0) {
                logSb.append("添加与客人备注信息：");
            } else if (request.getRemarkType() == 1) {
                logSb.append("添加与供应商备注信息：");
            } else if (request.getRemarkType() == 2) {
                logSb.append("添加内部备注信息：");
            } else {
                logSb.append("添加备注信息：");
            }
        } else {
            logSb.append("添加备注信息：");
        }
        logSb.append(request.getRemark());
        OrderPO orderPO = orderMapper.selectByPrimaryKey(request.getOrderId());
        orderCommonService.saveOrderLog(
                request.getOrderId(),
                request.getOperator(),
                request.getOrderOwnerName(),
                orderPO.getOrderCode(),
                logSb.toString()
        );
    }

    @Override
    public void saveOrderAttachment(SaveOrderAttachmentDTO request) {
        if (null == request.getOrderId() || StrUtilX.isEmpty(request.getUrl())) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }

        OrderPO orderPO = orderMapper.selectByPrimaryKey(request.getOrderId());

        OrderAttachmentPO orderAttachmentInsert = ProductSalePriceConvert.INSTANCE.orderAttachmentInsertConvert(request);
        orderAttachmentInsert.setCreatedBy(request.getOperator());
        orderAttachmentInsert.setActive(1);
        orderAttachmentInsert.setCreatedDt(DateUtilX.dateToString(new Date(), hour_format));
        orderAttachmentMapper.insert(orderAttachmentInsert);

        //记日志(new Date
        orderCommonService.saveOrderLog(
                request.getOrderId(),
                request.getOperator(),
                request.getOrderOwnerName(),
                orderPO.getOrderCode(),
                "上传附件（" + request.getName() + "）"
        );
    }

    @Override
    public void deleteOrderAttachment(OrderAttachmentIdDTO request) {
        OrderAttachmentPO orderAttachmentPO = orderAttachmentMapper.selectByPrimaryKey(request.getOrderAttachmentId());
        OrderPO orderPO = orderMapper.selectByPrimaryKey(orderAttachmentPO.getOrderId());
        orderAttachmentMapper.deleteByPrimaryKey(request.getOrderAttachmentId());

        //记日志
        orderCommonService.saveOrderLog(
                orderPO.getId(),
                request.getOperator(),
                request.getOrderOwnerName(),
                orderPO.getOrderCode(),
                "删除订单附件（" + orderAttachmentPO.getName() + "）成功"
        );
    }

    @Override
    @Transactional
    public Response<Object> addOrderRequest(AddOrderRequestDTO request) {
        OrderPO orderPO = orderMapper.selectByPrimaryKey(request.getOrderId());

        // 如果订单已经取消，则不再添加申请（取消、修改、二次确认）
        if (!orderPO.getOrderConfirmationStatus().equals(ConfirmationStatusEnum.CANCELED.key)) {
            // 1. 校验
            if (request.getRequestType() == 0 || request.getRequestType() == 2) {
                // 1.1 如果有未处理的取消申请，不重复添加取消申请
                OrderRequestPO orderRequestQuery = new OrderRequestPO();
                orderRequestQuery.setOrderId(request.getOrderId());
                orderRequestQuery.setRequestType(request.getRequestType());
                orderRequestQuery.setHandleResult(0);
                List<OrderRequestPO> orderRequestDOS = orderRequestMapper.select(orderRequestQuery);
                if (CollUtilX.isNotEmpty(orderRequestDOS)) {
                    return Response.success();
                }
            }

            // 修改订单状态
            OrderPO orderPOUpdate = new OrderPO();
            orderPOUpdate.setId(orderPO.getId());
            orderPOUpdate.setModificationStatus(request.getRequestType() + 1);//订单状态 1取消，2修改，3二次确认，所以加1
            orderMapper.updateByPrimaryKeySelective(orderPOUpdate);

            // 2. 添加订单申请
            OrderRequestPO orderRequestInsert = new OrderRequestPO();
            orderRequestInsert.setOrderId(request.getOrderId());
            orderRequestInsert.setRequestType(request.getRequestType());
            orderRequestInsert.setHandleResult(request.getResponseType() == null ? 0 : request.getResponseType());
            orderRequestInsert.setRemark(request.getRemark());
            orderRequestInsert.setHandlerReason(request.getHandlerReason());
            orderRequestInsert.setHardCancel(request.getHardCancel());
            orderRequestInsert.setCreatedBy(request.getOperator());
            orderRequestInsert.setCreatedDt(DateUtilX.dateToString(new Date(), hour_format));
            if (request.getResponseType() != null) {
                orderRequestInsert.setHandleResult(request.getResponseType());
            }
            orderRequestMapper.insert(orderRequestInsert);

            //超条款（超时）取消，不自动发取消单给供应商
            boolean overTimeCancel = null != request.getOverTimeCancel() && request.getOverTimeCancel().equals(1);

            String requestType = null;
            switch (request.getRequestType()) {
                case 0:
                    if (overTimeCancel) {
                        requestType = "取消申请，此申请为超时取消，不会自动发单，需人工处理！";
                    } else {
                        requestType = "取消申请";
                    }

                    //推前台消息
                    //查询分销商
                    StringBuilder message = new StringBuilder();
                    AgentAccountConfig agentAccountConfig = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.AGENT_ACCOUNT_CONFIG, orderPO.getAgentCode()), AgentAccountConfig.class);
                    if (agentAccountConfig == null) {
                        throw new SysException(ErrorCodeEnum.AGENT_IS_INVALID);
                    }
                    message.append(agentAccountConfig.getAgentName());
                    message.append("申请取消").append(orderPO.getStartDate()).append("入住").append(orderPO.getHotelName());
                    message.append(orderPO.getRoomQty()).append("间").append(DateUtilX.getDay(orderPO.getStartDate(), orderPO.getEndDate())).append("晚的订单,请尽快处理!");

                    if (StrUtilX.isNotEmpty(request.getHandlerReason())) {
                        message.append("取消原因为：").append(request.getHandlerReason());
                    }

                    Map<String, Object> content = new HashMap<>();
                    content.put("orderCode", orderPO.getOrderCode());
                    content.put("content", message.toString());
                    content.put("messageType", MessageTypeEnum.CANCEL_REQUEST.key);
                    JSONObject json = new JSONObject(content);

                    stringRedisTemplate.convertAndSend(orderPO.getCompanyCode() + "9", "all/" + JSON.toJSONString(json));
                    break;
                case 1:
                    requestType = "修改申请";
                    break;
                case 2:
                    requestType = "二次确认申请";
                    break;
                case 3:
                    requestType = "退款申请";
                default:

            }

            // 3. 记日志
            orderCommonService.saveOrderLog(
                    orderPO.getId(),
                    request.getOperator(),
                    "",
                    orderPO.getOrderCode(),
                    "添加" + requestType);

//
//            if (request.getOperator().equals(ChannelEnum.MEITUAN.key) && request.getRequestType() == 0
//                    && request.getResponseType() == 2) {
//                orderCommonService.saveOrderLog(
//                        orderPO.getId(),
//                        request.getOperator(),
//                        "",
//                        orderPO.getOrderCode(),
//                        "因" + request.getRemark() + "自动拒绝订单取消");
//            }


            Response<Object> response = Response.success();
            if (!overTimeCancel && (request.getRequestType() == 0 || request.getRequestType() == 3)) {
                try {
                    asyncSendCancelOrder(orderPO, request.getRequestId());
                } catch (Exception e) {
                    log.error("自动发取消单功能异常", e);
                    response.setFailCode(ErrorCodeEnum.AUTOMATICALLY_CANCEL_A_SINGLE_FUNCTION_EXCEPTION.errorCode);
                    response.setFailReason(ErrorCodeEnum.AUTOMATICALLY_CANCEL_A_SINGLE_FUNCTION_EXCEPTION.errorDesc);
                }
            }

            response.setResult(ResultCodeEnum.SUCCESS.code);
            return response;
        } else {
            Response<Object> response = Response.success();
            response.setFailCode(ErrorCodeEnum.ORDER_CANCELED.errorCode);
            response.setFailReason(ErrorCodeEnum.ORDER_CANCELED.errorDesc);
            return response;
        }
    }

    /**
     * 同步发取消单
     */
    private void asyncSendCancelOrder(OrderPO orderPO, String requestId) {
        // 异步发取消单
        asyncSendCancelOrder.execute(() -> {
            try {
                Thread.sleep(2000);
            } catch (InterruptedException e) {
                log.error("异步发取消单线程异常");
            }

            // 查询供货单
            Example example = new Example(SupplyOrderPO.class);
            example.setOrderByClause("id DESC");
            Example.Criteria criteria = example.createCriteria();
            criteria.andEqualTo("orderId", orderPO.getId());
            List<SupplyOrderPO> supplyOrderPOList = supplyOrderMapper.selectByExample(example);
            // 为空或多供货单不走自动取消
            if (CollUtilX.isEmpty(supplyOrderPOList) || supplyOrderPOList.size() > 1) {
                return;
            }
            SupplyOrderPO supplyOrderPO = supplyOrderPOList.get(0);
            // 状态是已发单、（未确认和已确认）的供货单，自动发取消单
            if ((supplyOrderPO.getConfirmationStatus().equals(ConfirmationStatusEnum.CONFIRMED.key) ||
                    supplyOrderPO.getConfirmationStatus().equals(ConfirmationStatusEnum.UNCONFIRM.key))) {
                // 获取渠道编码
                String[] channel = orderPO.getChannelCode().split("_");

                // 通过供货单信息去查询供应商是否开通发单设置
                Map<String, String> request = new HashMap<>();
                request.put("supplierCode", supplyOrderPO.getSupplierCode());
                request.put("available", "1");//直接查询有设置直接发单{是否自动发单，是:1;否:0}
                request.put("companyCode", orderPO.getCompanyCode());
                List<SupplierListDTO> supplierListDTOList = orderMapper.orderAutomaticList(request);

                // 判断该供应商是否开通自动发单
                if (CollUtilX.isEmpty(supplierListDTOList)) {
                    log.error("该供应商没有开通自动发单:" + supplyOrderPO.getSupplierName() + "供应商家编码为:" +
                            supplyOrderPO.getSupplierCode() + "请求参数:" + request + ",返回参数" + JSON.toJSONString(supplierListDTOList));
                    return;
                }
                for (SupplierListDTO cancelListDTO : supplierListDTOList) {
                    // 判断商家是否相等
                    if (!orderPO.getCompanyCode().equals(cancelListDTO.getCompanyCode())) {
                        log.error("商家不一致不自动发单:" + orderPO.getCompanyCode() + "商家编码:" + cancelListDTO.getCompanyCode());
                        continue;
                    }

                    // 查询供应商code是否一致
                    if (!cancelListDTO.getSupplierCode().equals(supplyOrderPO.getSupplierCode())) {
                        log.error("该供应商没有开通自动发单:" + supplyOrderPO.getSupplierName() + "供应商家编码为:" + supplyOrderPO.getSupplierCode());
                        continue;
                    }
                    // 判断该供应商是否支持该渠道发单
                    String[] split = new String[]{"B2B", "DHUB"};
                    for (String channelCode : split) {
                        if (channelCode.equals(channel[0])) {
                            if (supplyOrderPO.getOrderMethod().equals(OrderMethodEnum.ORDER_METHOD_ENUM_2.key)) {
                                SendToSupplierDTO sendToSupplierDTO = new SendToSupplierDTO();
                                sendToSupplierDTO.setSupplyOrderId(supplyOrderPO.getId());
                                sendToSupplierDTO.setAutoSendOrder(true);
                                sendToSupplierDTO.setOperator(Constant.SYSTEM);
                                sendToSupplierDTO.setSupplyOrderType(3);
                                sendToSupplierDTO.setRequestId(requestId);
                                Response<Object> response = supplyOrderService.sendToEmail(sendToSupplierDTO, EmailSendingTypeEnum.CANCELLATION.key);
                                if (response.getResult() == 0) {
                                    log.error("自动发取消单失败：" + response.getFailReason());
                                } else {
                                    log.info("自动发取消单成功：" + supplyOrderPO.getId());
                                }
                            } else {
                                SendToSupplierDTO sendToSupplierDTO = new SendToSupplierDTO();
                                sendToSupplierDTO.setSupplyOrderId(supplyOrderPO.getId());
                                sendToSupplierDTO.setAutoSendOrder(false);
                                sendToSupplierDTO.setOperator(Constant.SYSTEM);
                                sendToSupplierDTO.setSupplyOrderType(3);
                                sendToSupplierDTO.setRequestId(requestId);
                                Response<Object> response = supplyOrderService.sendToSupplier(sendToSupplierDTO);
                                if (response.getResult() == 0) {
                                    log.error("自动发取消单失败：" + response.getFailReason());
                                } else {
                                    log.info("自动发取消单成功：" + supplyOrderPO.getId());
                                }
                            }
                            break;
                        }
                    }
                }
            }
        });
    }

    /**
     * 标记订单请求为已处理
     */
    @Override
    @Transactional
    public void removeOrderRequest(HandleOrderRequestDTO request, List<OrderRequestPO> orderRequestPOs) {
        OrderPO orderPO = orderMapper.selectByPrimaryKey(orderRequestPOs.get(0).getOrderId());
        OrderRequestPO orderRequestUpdate = new OrderRequestPO();
        orderRequestUpdate.setId(orderRequestPOs.get(0).getId());
        orderRequestUpdate.setUpdatedBy(request.getOperator());
        orderRequestUpdate.setUpdatedDt(DateUtilX.dateToString(new Date(), hour_format));
        orderRequestUpdate.setHandleResult(1);//默认为同意处理
        orderRequestUpdate.setRemark(request.getRemark());
        orderRequestMapper.updateByPrimaryKeySelective(orderRequestUpdate);

        OrderPO orderPOUpdate = new OrderPO();
        orderPOUpdate.setId(orderPO.getId());
        orderPOUpdate.setModificationStatus(0);
        orderMapper.updateByPrimaryKeySelective(orderPOUpdate);
        //记日志
        StringBuilder content = new StringBuilder();
        content.append("删除申请");
        if (StrUtilX.isNotEmpty(request.getRemark())) {
            content.append("，备注：").append(request.getRemark());
        }

        // 异步记录日志
        CompletableFuture.runAsync(() -> {
            orderCommonService.saveOrderLog(
                    orderPO.getId(),
                    request.getOperator(),
                    request.getOrderOwnerName(),
                    orderPO.getOrderCode(),
                    content.toString()
            );
        });
    }

    @Override
    @Transactional
    public void handleOrderRequest(HandleOrderRequestDTO request, List<OrderRequestPO> orderRequestPOs) {
        OrderPO orderPO = orderMapper.selectByPrimaryKey(orderRequestPOs.get(0).getOrderId());
        OrderExtendPO orderExtendPO = null;
        OrderExtendPO orderExtendQuery = new OrderExtendPO();
        orderExtendQuery.setOrderId(orderRequestPOs.get(0).getOrderId());
        List<OrderExtendPO> orderExtendPOList = orderExtendMapper.select(orderExtendQuery);
        if (CollUtilX.isNotEmpty(orderExtendPOList)) {
            orderExtendPO = orderExtendPOList.get(0);
        }

        OrderRequestPO orderRequestUpdate = new OrderRequestPO();
        orderRequestUpdate.setId(orderRequestPOs.get(0).getId());
        orderRequestUpdate.setUpdatedBy(request.getOperator());
        orderRequestUpdate.setUpdatedDt(DateUtilX.dateToString(new Date(), hour_format));
        orderRequestUpdate.setHandleResult(request.getHandledResult() == 0 ? 2 : 1);//接口0为拒绝，数据库2为拒绝
        orderRequestUpdate.setRemark(request.getRemark());
        orderRequestMapper.updateByPrimaryKeySelective(orderRequestUpdate);

        B2BOrderStatusPushRequest b2BOrderStatusPushRequest = new B2BOrderStatusPushRequest();

        b2BOrderStatusPushRequest.setFcOrderCode(orderPO.getOrderCode());
        b2BOrderStatusPushRequest.setMessage(null != orderExtendPO ? orderExtendPO.getCancelledReason() : null);
        b2BOrderStatusPushRequest.setHotelConfirmNo(null != orderExtendPO ? orderExtendPO.getConfirmationCode() : null);
        b2BOrderStatusPushRequest.setAgentCode(orderPO.getAgentCode());
        b2BOrderStatusPushRequest.setPartnerCode(orderPO.getPartnerCode());
        b2BOrderStatusPushRequest.setCancellationPrice(orderPO.getRefundFee());
        if (request.getHandledResult() != null && request.getHandledResult() == 1) {
            if (StrUtilX.isEmpty(orderPO.getChannelOrderCode())) {
                throw new SysException(ErrorCodeEnum.SYSTEM_EXCEPTION.errorCode, "渠道单号不能为空!");
            }
            b2BOrderStatusPushRequest.setCoOrderCode(orderPO.getChannelOrderCode());
            b2BOrderStatusPushRequest.setOrderStatus(6);
            // 输出取消罚金
            b2BOrderStatusPushRequest.setCurrency(SettlementCurrencyEnum.getCodeByKey(String.valueOf(orderPO.getSaleCurrency())));
            b2BOrderStatusPushRequest.setPenaltiesValue(orderPO.getRefundFee());
        } else {
            if (StrUtilX.isEmpty(orderPO.getChannelOrderCode())) {
                throw new SysException(ErrorCodeEnum.SYSTEM_EXCEPTION.errorCode, "渠道单号不能为空!");
            }
            b2BOrderStatusPushRequest.setCoOrderCode(orderPO.getChannelOrderCode());
            b2BOrderStatusPushRequest.setOrderStatus(7);
        }

        orderStatusPushRetry(b2BOrderStatusPushRequest);

        OrderPO orderPOUpdate = new OrderPO();
        orderPOUpdate.setId(orderPO.getId());
        orderPOUpdate.setModificationStatus(0);
        orderMapper.updateByPrimaryKeySelective(orderPOUpdate);
        //记日志
        StringBuilder content = new StringBuilder();
        if (orderRequestPOs.get(0).getRequestType() < 2) {
            content.append("处理订单申请：申请类型：").append(RequestTypeEnum.getDesc(orderRequestPOs.get(0).getRequestType())).append(request.getHandledResult() == 1 ? "同意申请" : "拒绝申请");
        } else if (orderRequestPOs.get(0).getRequestType() == 3) {
            content.append("接收退款申请");
        } else {
            content.append("处理二次确认订单：").append(request.getHandledResult() == 1 ? "确定二次确认" : "推翻二次确认");
        }
        if (StrUtilX.isNotEmpty(request.getRemark()) && orderRequestPOs.get(0).getHandleResult() == null) {
            content.append("，备注：").append(request.getRemark());
        }


        orderCommonService.saveOrderLog(
                orderPO.getId(),
                request.getOperator(),
                request.getOrderOwnerName(),
                orderPO.getOrderCode(),
                content.toString()
        );
    }

    @Override
    @Transactional
    public void modifyOrderRoom(ModifyOrderRoomDTO request) {
        if (request.getOrderId() == null) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_ORDERID);
        }
        if ((request.getHotelId() == null || StrUtilX.isEmpty(request.getHotelName())) && (request.getRoomId() == null || StrUtilX.isEmpty(request.getRoomName()))) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
        OrderPO orderPO = orderMapper.selectByPrimaryKey(request.getOrderId());
        // 校验订单是否是手工单，不是手工单不允许修改
        if (Objects.isNull(orderPO.getIsManualOrder()) || orderPO.getIsManualOrder() != 1) {
            throw new SysException(ErrorCodeEnum.UNSUPPORTED_ORDER_OPERATION);
        }

        OrderPO orderUpdate = new OrderPO();
        orderUpdate.setId(request.getOrderId());
        if (request.getHotelId() != null && StrUtilX.isNotEmpty(request.getHotelName())) {
            // 校验酒店id的合法性
            // 查询基础信息的ES获取对应的酒店信息
            DestinationReq destinationReq = new DestinationReq();
            destinationReq.setLanguage(request.getLanguage());
            destinationReq.setHotelIds(Collections.singleton(request.getHotelId()));
            Response<List<EsHotelDto>> esHotelDTOResponse = hotelRemote.searchDestinationHotel(destinationReq);
            if (esHotelDTOResponse.isError() || CollUtil.isEmpty(esHotelDTOResponse.getModel())) {
                throw new SysException(ErrorCodeEnum.NOT_EXIST_HOTEL);
            }
            EsHotelDto esHotelDto = esHotelDTOResponse.getModel().stream()
                    .filter(hotel -> hotel.getHotelId().equals(request.getHotelId()))
                    .findFirst()
                    .orElse(null);
            if (Objects.isNull(esHotelDto)) {
                throw new SysException(ErrorCodeEnum.NOT_EXIST_HOTEL);
            }

            // 修改当前订单下面的所有供货单的酒店id和酒店名称
            // 查询此订单下的所有供货单
            Example queryAllSupplyOrderExample = new Example(SupplyOrderPO.class);
            queryAllSupplyOrderExample.createCriteria().andEqualTo("orderId", request.getOrderId());
            List<SupplyOrderPO> supplyOrderPOS = supplyOrderMapper.selectByExample(queryAllSupplyOrderExample);
            if (CollUtil.isEmpty(supplyOrderPOS)) {
                throw new SysException(ErrorCodeEnum.SUPPLY_ORDER_NOT_EXIST);
            }

            SupplyOrderPO supplyOrderUpdate = new SupplyOrderPO();
            supplyOrderUpdate.setHotelId(request.getHotelId());
            supplyOrderUpdate.setHotelName(request.getHotelName());
            if (request.getRoomId() != null && StrUtilX.isNotEmpty(request.getRoomName())) {
                supplyOrderUpdate.setRoomName(request.getRoomName());
            }
            supplyOrderUpdate.setCityCode(esHotelDto.getCityCode());
            supplyOrderUpdate.setCityName(esHotelDto.getCityName());
            supplyOrderUpdate.setUpdatedBy(request.getOperator());
            supplyOrderUpdate.setUpdatedDt(DateUtilX.dateToString(new Date(), hour_format));
            for (SupplyOrderPO supplyOrderPO : supplyOrderPOS) {
                supplyOrderUpdate.setId(supplyOrderPO.getId());
                supplyOrderMapper.updateByPrimaryKeySelective(supplyOrderUpdate);
            }

            orderUpdate.setHotelId(request.getHotelId());
            orderUpdate.setHotelName(request.getHotelName());
            orderUpdate.setCityCode(esHotelDto.getCityCode());
            orderUpdate.setCityName(esHotelDto.getCityName());
            orderUpdate.setProvinceCode(esHotelDto.getProvinceCode());
            orderUpdate.setProvinceName(esHotelDto.getProvinceName());
            orderUpdate.setCountryCode(esHotelDto.getCountryCode());
            orderUpdate.setCountryName(esHotelDto.getCountryName());
        }
        // 判断房型是否修改
        if (request.getRoomId() != null && StrUtilX.isNotEmpty(request.getRoomName())) {
            orderUpdate.setRoomId(request.getRoomId());
            orderUpdate.setRoomName(request.getRoomName());
        }

        orderUpdate.setUpdatedBy(request.getOperator());
        orderUpdate.setUpdatedDt(DateUtilX.dateToString(new Date(), hour_format));
        orderMapper.updateByPrimaryKeySelective(orderUpdate);

        // 判断是否记录酒店修改日志
        if (request.getHotelId() != null && StrUtilX.isNotEmpty(request.getHotelName())) {
            // 记录操作日志
            String content = "酒店名称:" +
                    orderPO.getHotelName() +
                    "改为:" +
                    request.getHotelName();
            orderCommonService.saveOrderLog(
                    orderPO.getId(),
                    request.getOperator(),
                    request.getOrderOwnerName(),
                    orderPO.getOrderCode(),
                    content
            );
        }

        // 判断是否记录房型修改日志
        if (request.getRoomId() != null && StrUtilX.isNotEmpty(request.getRoomName())) {
            // 记录操作日志
            String content = "更新订单房型：由(" +
                    orderPO.getRoomName() +
                    ")改为(" +
                    request.getRoomName() +
                    ")";
            orderCommonService.saveOrderLog(
                    orderPO.getId(),
                    request.getOperator(),
                    request.getOrderOwnerName(),
                    orderPO.getOrderCode(),
                    content
            );
        }

        // 注册事务同步器
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                // 事务提交后，加入到统计报表
                CompletableFuture.runAsync(() -> {
                    try {
                        // 加入到统计报表队列中
                        stringRedisTemplate.convertAndSend(RedisKey.REPORT_STATISTICS, orderPO.getOrderCode());
                    } catch (Exception e) {
                        log.error("加入到统计报表队列异常,error:", e);
                    }
                });
            }
        });
    }

    @Override
    @Transactional
    public void modifyOrderSettlementType(ModifyOrderSettlementTypeDTO request) {
        OrderPO orderPO = orderMapper.selectByPrimaryKey(request.getOrderId());
        // 1. 校验
        if (Objects.equals(orderPO.getSettlementType(), request.getSettlementType())) {
            return;
        }
        if (request.getSettlementType() != SettlementTypeEnum.SINGLE.key) {
            log.error(SettlementTypeEnum.getValueByKey(orderPO.getSettlementType()) + "不能改为" + SettlementTypeEnum.getValueByKey(request.getSettlementType()));
            // 单结订单不能改为非单结，非单结只能修改为单结
            throw new SysException(ErrorCodeEnum.CANNOT_OPERATE);
        }
        if (Objects.equals(orderPO.getOrderConfirmationStatus(), ConfirmationStatusEnum.CONFIRMED.key)) {
            throw new SysException(ErrorCodeEnum.ORDER_CONFIRMED_NOT_UPDATE_SETTLEMENTTYPE);
        }

        // 2. 更新订单结算方式
        OrderPO orderUpdate = new OrderPO();
        orderUpdate.setId(request.getOrderId());
        orderUpdate.setSettlementType(request.getSettlementType());
        orderUpdate.setUpdatedBy(request.getOperator());
        orderUpdate.setUpdatedDt(DateUtilX.dateToString(new Date(), hour_format));
        int i = orderMapper.updateByPrimaryKeySelective(orderUpdate);
        // 修改为单结时退客户额度
        if (request.getSettlementType().equals(SettlementTypeEnum.SINGLE.key) && i > 0) {
            AgentCreditLineDTO creditLineDTO = new AgentCreditLineDTO();
            creditLineDTO.setUpdatedBy(request.getOperator());
            creditLineDTO.setOrderCode(request.getOrderCode());
            if (StrUtilX.isNotEmpty(orderPO.getSubtractLineAccount())) {
                creditLineDTO.setAgentCode(orderPO.getSubtractLineAccount());
            } else {
                creditLineDTO.setAgentCode(orderPO.getAgentCode());
            }
            creditLineDTO.setDeductRefundCreditLine(orderPO.getOrderAmt());
            Response<AgentCreditLineResultDTO> creditLineResponse = agentRemote.modifyDeductRefundCreditLine(Collections.singletonList(creditLineDTO));
            if (!creditLineResponse.isSuccess()) {
                throw new SysException(creditLineResponse.getFailCode(), creditLineResponse.getFailReason());
            }
        }
        // 3. 记日志
        orderCommonService.saveOrderLog(
                orderPO.getId(),
                request.getOperator(),
                request.getOrderOwnerName(),
                orderPO.getOrderCode(),
                "订单改为" + SettlementTypeEnum.getValueByKey(request.getSettlementType())
        );

    }

    @Override
    @Transactional
    public void lockOrder(LockOrderDTO request) {
        // 1. 校验
        OrderPO orderPO = orderMapper.selectByPrimaryKey(request.getOrderId());
        /*if (StrUtilX.isNotEmpty(orderPO.getLockUser()) && request.getLockStatus() == 1) {
            if (orderPO.getLockUser().equals(request.getOperator())) {
                response.setResult(ResultCodeEnum.SUCCESS.code);
                return response;
            } else {
                response.setResult(ResultCodeEnum.FAILURE.code);
                response.setFailReason("订单已被" + orderPO.getLockName() + "锁定");
                return response;
            }
        }*/
        if (StrUtilX.isNotEmpty(orderPO.getLockName()) && request.getLockStatus() == 1) {
            throw new SysException(ErrorCodeEnum.ORDER_IS_BEING_PROCESSED);
        }

        // 2. 加解锁
        if (request.getLockStatus() == 1) {
            OrderPO orderUpdate = new OrderPO();
            orderUpdate.setId(request.getOrderId());
            orderUpdate.setLockUser(request.getOperatorUser());
            orderUpdate.setLockName(request.getOperator());
            orderUpdate.setLockTime(new Date());
            orderMapper.updateByPrimaryKeySelective(orderUpdate);
        } else {
            orderPO.setLockUser(null);
            orderPO.setLockName(null);
            orderPO.setLockTime(null);
            orderMapper.updateByPrimaryKey(orderPO);
        }

        // 3. 记日志
        orderCommonService.saveOrderLog(
                orderPO.getId(),
                request.getOperator(),
                request.getOrderOwnerName(),
                orderPO.getOrderCode(),
                request.getLockStatus() == 1 ? "订单已锁定" : "订单已解锁"
        );
    }

    @Override
    public void markOrder(MarkOrderDTO request) {
        OrderPO orderUpdate = new OrderPO();
        orderUpdate.setId(request.getOrderId());
        orderUpdate.setMarkedStatus(request.getMarkedStatus());
        if (request.getModificationRequestStatus() != null) {
            orderUpdate.setModificationStatus(ModificationStatusEnum.MODIFICATION_STATUS_0.key);
        }
        orderMapper.updateByPrimaryKeySelective(orderUpdate);

        //记录日志
        OrderLogPO orderLogPO = new OrderLogPO();
        orderLogPO.setOrderId(request.getOrderId());
        orderLogPO.setCreatedBy(request.getOperator());
        orderLogPO.setCreatedDt(DateUtilX.dateToString(new Date(), hour_format));
        orderLogPO.setTarget(request.getOrderOwnerName());
        if (request.getModificationRequestStatus() != null) {
            orderLogPO.setContent("【" + ModificationStatusEnum.getValueByKey(request.getModificationRequestStatus()) + "】已处理");
            orderLogMapper.insert(orderLogPO);
        }
    }

    @Override
    public void modifyChannelOrderCode(Map<String, String> request) {
        Example orderExample = new Example(OrderPO.class);
        orderExample.createCriteria().andEqualTo("channelOrderCode", request.get("channelOrderCode"));
        OrderPO orderInfo = orderMapper.selectOneByExample(orderExample);
        if (orderInfo != null && orderInfo.getId().compareTo(Integer.parseInt(request.get("orderId"))) != 0) {
            throw new SysException(ErrorCodeEnum.CHANNEL_ORDER_CODE_EXISTS.errorCode, ErrorCodeEnum.CHANNEL_ORDER_CODE_EXISTS.errorDesc);
        }

        OrderPO orderUpdate = new OrderPO();
        orderUpdate.setId(Integer.parseInt(request.get("orderId")));
        orderUpdate.setChannelOrderCode(request.get("channelOrderCode"));
        orderMapper.updateByPrimaryKeySelective(orderUpdate);
        // 3. 记日志
        orderCommonService.saveOrderLog(
                Integer.parseInt(request.get("orderId")), request.get("operator"), request.get("orderOwnerName"), "",
                "客户单号改为" + request.get("channelOrderCode")
                // 3. 记日志
        );

        try {
            OrderPO orderPO = orderMapper.selectByPrimaryKey(Integer.parseInt(request.get("orderId")));
            //加入到统计报表队列中
            stringRedisTemplate.convertAndSend(RedisKey.REPORT_STATISTICS, orderPO.getOrderCode());
        } catch (Exception e) {
            log.error("加入到统计报表队列异常,error:", e);
        }
    }

    /**
     * 订单自动发单列表
     */
    @Override
    public Response<PaginationSupportDTO<SupplierListDTO>> orderAutomaticList(Map<String, String> request) {
        List<String> allChannelCodes = orderMapper.selectCompanyCode();
        StringBuilder s = new StringBuilder();
        for (String channelCode : allChannelCodes) {
            s.append(channelCode).append(",");
        }
        PageHelper.startPage(Integer.parseInt(request.get("currentPage")), Integer.parseInt(request.get("pageSize")));
        List<SupplierListDTO> supplierListDTOList = orderMapper.orderAutomaticList(request);
        PageInfo<SupplierListDTO> page = new PageInfo<>(supplierListDTOList);
        PaginationSupportDTO<SupplierListDTO> paginationSupport = new PaginationSupportDTO<>();
        for (SupplierListDTO supplierListDTO : supplierListDTOList) {
            StringBuilder channelNames = new StringBuilder();
            StringBuilder channelCodes = new StringBuilder();
            if (supplierListDTO.getChannelCodeList() == null || supplierListDTO.getChannelCodeList().equals("")) {
                supplierListDTO.setChannelNameList("全部渠道");
                supplierListDTO.setChannelCodeList(s.toString());
            } else {
                String[] channelCodeArr = supplierListDTO.getChannelCodeList().split(",");
                List<String> list = new ArrayList<>();
                for (String channelCode : channelCodeArr) {
                    String channelName = ChannelEnum.getValueByKey(channelCode);
                    if (StrUtilX.isEmpty(channelName)) {
                        continue;
                    }
                    list.add(channelCode);
                    channelNames.append(channelName).append(",");
                    channelCodes.append(channelCode).append(",");
                }
                String[] listStirng = list.toArray(new String[0]);
                String[] channelCodeString = allChannelCodes.toArray(new String[0]);
                Arrays.sort(channelCodeString);
                Arrays.sort(listStirng);
                if (Arrays.equals(listStirng, channelCodeString)) {
                    supplierListDTO.setChannelNameList("全部渠道");
                    supplierListDTO.setChannelCodeList(s.toString());
                } else {
                    supplierListDTO.setChannelNameList(channelNames.toString());
                    supplierListDTO.setChannelCodeList(channelCodes.toString());
                }
            }
            if (supplierListDTO.getStartTime() == null && supplierListDTO.getEndTime() == null) {
                supplierListDTO.setStartTime("00:00:00");
                supplierListDTO.setEndTime("23:59:59");
            }

            if (supplierListDTO.getAvailable() == null) {
                supplierListDTO.setAvailable(0);
            }
        }

        paginationSupport.setItemList(supplierListDTOList);
        paginationSupport.setPageSize(page.getPageSize());
        paginationSupport.setTotalCount(page.getTotal());
        paginationSupport.setTotalPage(page.getPages());
        paginationSupport.setCurrentPage(page.getPageNum());
        return Response.success(paginationSupport);
    }

    /**
     * 添加自动发单渠道及时间
     */
    @Override
    public void updateSupplierAutoChannel(AddSupplierAutoChannelDTO channelDTO) {
        SupplierAutoChannelDto dto = new SupplierAutoChannelDto();
        dto.setChannelCodeList("B2B");
        dto.setId(channelDTO.getId());
        dto.setAvailable(String.valueOf(channelDTO.getAvailable()));
        dto.setSupplierCode(channelDTO.getSupplierCode());
        dto.setStartTime(channelDTO.getStartTime());
        dto.setEndTime(channelDTO.getEndTime());
        dto.setSuppliertimeType(channelDTO.getSuppliertimeType());
        dto.setCreatedBy(channelDTO.getCreatedBy());
        dto.setCreatedDt(channelDTO.getCreatedDt());
        dto.setUpdatedBy(channelDTO.getUpdatedBy());
        dto.setUpdatedDt(channelDTO.getUpdatedDt());
        dto.setCompanyCode(channelDTO.getCompanyCode());

        //以下是2024年09月版本新增字段
        dto.setOrderMethod(channelDTO.getOrderMethod());
        dto.setRecipientName(channelDTO.getRecipientName());
        dto.setPreorderEmail(channelDTO.getPreorderEmail());
        dto.setVccPay(channelDTO.getVccPay());
        dto.setVccEmail(channelDTO.getVccEmail());
        dto.setCompanyName(channelDTO.getCompanyName());
        dto.setRemarks(channelDTO.getRemarks());
        dto.setEntrance(channelDTO.getEntrance());

        orderMapper.updateSupplierAutoChannel(dto);
    }

    /**
     * 修改自动发单渠道及时间
     */
    @Override
    public Integer addSupplierAutoChannel(AddSupplierAutoChannelDTO channelDTO) {
        // 如果是自定义时间直接传输，如果不是默认
        SupplierAutoChannelDto dto = new SupplierAutoChannelDto();
        dto.setChannelCodeList("B2B");
        dto.setId(channelDTO.getId());
        dto.setAvailable(String.valueOf(channelDTO.getAvailable()));
        dto.setSupplierCode(channelDTO.getSupplierCode());
        dto.setStartTime(channelDTO.getStartTime());
        dto.setEndTime(channelDTO.getEndTime());
        dto.setCreatedBy(channelDTO.getCreatedBy());
        dto.setCreatedDt(channelDTO.getCreatedDt());
        dto.setUpdatedBy(channelDTO.getUpdatedBy());
        dto.setUpdatedDt(channelDTO.getUpdatedDt());
        dto.setCompanyCode(channelDTO.getCompanyCode());
        dto.setSuppliertimeType(channelDTO.getSuppliertimeType());

        //以下是2024年09月版本新增字段
        dto.setOrderMethod(channelDTO.getOrderMethod());
        dto.setRecipientName(channelDTO.getRecipientName());
        dto.setPreorderEmail(channelDTO.getPreorderEmail());
        dto.setVccPay(channelDTO.getVccPay());
        dto.setVccEmail(channelDTO.getVccEmail());
        dto.setCompanyName(channelDTO.getCompanyName());
        dto.setRemarks(channelDTO.getRemarks());

        orderMapper.addSupplierAutoChannel(dto);

        return dto.getId();
    }

    @Override
    public Response<SupplierAutoChannelDto> querySupplierAutoChannel(SupplierAutoChannelDto supplierAutoChannelDto) {
        SupplierAutoChannelDto supplierAutoChannel = orderMapper.querySupplierAutoChannel(supplierAutoChannelDto);

        //EMAIL_ORDER_LOGO_URL队列不存在则查询文件
        //只有一种场景，确定单不需要再次查询文件
        if (null != supplierAutoChannel) {
            FileReq fileReq = new FileReq();
            fileReq.setObjId(String.valueOf(supplierAutoChannel.getId()));
            fileReq.setTableName("t_org_order_automatic");
            Response<List<FileResp>> response = fileRemote.list(fileReq);
            if (response.getModel() != null && response.getModel().size() > 0) {
                //存在文件，则直接赋值
                List<FileAttachDTO> photoList = ProductSalePriceConvert.INSTANCE.fileAttachDTOConvert(response.getModel());
                supplierAutoChannel.setFileAttachDTOList(photoList);
            }
        }
        return Response.success(supplierAutoChannel);
    }

    @Override
    public Long queryOrderIdByOrderCode(String orderCode) {
        return orderMapper.queryOrderIdByOrderCode(orderCode);
    }

    /**
     * 查询自动确认的渠道
     */
    @Override
    public Response<QueryAutoConfirmChannelDto> queryAutoConfirmChannel(Map<String, String> request) {
        String companyCode = request.get("companyCode");
        if (StrUtilX.isEmpty(companyCode)) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
        QueryAutoConfirmChannelDto autoConfirmChannelDto = orderMapper.queryAutoConfirmChannel(companyCode);
        return Response.success(autoConfirmChannelDto);
    }

    /**
     * 查询商家自动取消
     */
    @Override
    public Response<OrderAutoCancelDTO> queryAutoCancel(Map<String, String> request) {
        String companyCode = request.get("companyCode");
        if (StrUtilX.isEmpty(companyCode)) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
        OrderAutoCancelDTO autoCancelDTO = orderMapper.queryAutoCancel(companyCode);
        return Response.success(autoCancelDTO);
    }

    /**
     * 新增修改商家自动确认的渠道配置
     */
    @Override
    public void modifyAutoCancel(OrderAutoCancelDTO orderAutoCancelDTO) {
        orderMapper.modifyAutoCancel(orderAutoCancelDTO);
    }

    /**
     * 新增修改自动确认的渠道设置
     */
    @Override
    public void modifyAutoConfirmChannel(QueryAutoConfirmChannelDto autoConfirmChannelDto) {
        orderMapper.modifyAutoConfirmChannel(autoConfirmChannelDto);
    }

    /**
     * 添加自动发单渠道
     */
    @Override
    public void addAutoConfirmChannel(QueryAutoConfirmChannelDto autoConfirmChannelDto) {
        QueryAutoConfirmRequestDto autoConfirmRequestDto = new QueryAutoConfirmRequestDto();
        autoConfirmRequestDto.setCompanyCode(autoConfirmChannelDto.getCompanyCode());
        autoConfirmRequestDto.setChannelCodeList(autoConfirmChannelDto.getChannelCodeList());
        autoConfirmRequestDto.setAutomaticStatus(autoConfirmChannelDto.getAutomaticStatus());
        orderMapper.addAutoConfirmChannel(autoConfirmRequestDto);
    }

    @Override
    public void modifyRefundFee(ModifyRefundFeeDTO request) {
        OrderPO orderPO = orderMapper.selectByPrimaryKey(request.getOrderId());
        BigDecimal refundFee = request.getRefundFee();
        StringBuilder content = new StringBuilder();

        //修改供货单退改费
        if (request.getSupplyOrderId() != null) {
            SupplyOrderPO supplyOrderPO = supplyOrderMapper.selectByPrimaryKey(request.getSupplyOrderId());
            if (supplyOrderPO.getConfirmationStatus() != OrderStatusEnum.CANCELED.no) {
                throw new SysException(ErrorCodeEnum.NOT_CANCEL_STATUS_CANCELLATION_FEE_CANNOT_BE_MODIFIED.errorCode, ErrorCodeEnum.NOT_CANCEL_STATUS_CANCELLATION_FEE_CANNOT_BE_MODIFIED.errorDesc);
            }
            BigDecimal supplyRefundFee = supplyOrderPO.getRefundFee() != null ? supplyOrderPO.getRefundFee() : BigDecimal.ZERO;
            BigDecimal changeAmt = refundFee.subtract(supplyRefundFee);
            if (supplyRefundFee.equals(refundFee)) {
                return;
            }
            content.append("供货单 (").append(supplyOrderPO.getSupplyOrderCode()).append(")")
                    .append("退订费变更,退订费由").append(supplyRefundFee)
                    .append("变更为").append(refundFee);

            Example example = new Example(SupplyProductPO.class);
            example.createCriteria().andEqualTo("supplyOrderId", request.getSupplyOrderId());
            supplyOrderPO.setRefundFee(refundFee);
            supplyOrderPO.setSupplyOrderAmt(supplyOrderPO.getSupplyOrderAmt().add(changeAmt));
            supplyOrderMapper.updateByPrimaryKeySelective(supplyOrderPO);
            // 转换汇率
            if (supplyOrderPO.getSupplierToAgentRate() != null) {
                changeAmt = CommonTgUtils.setRateAmt(changeAmt, supplyOrderPO.getSupplierToAgentRate());
            }
            orderPO.setProfit(orderPO.getProfit().subtract(changeAmt));
            orderMapper.updateByPrimaryKeySelective(orderPO);

            //更新订单未结算金额和结算状态
            SupplyOrderFinancePO supplyOrderFinanceQuery = new SupplyOrderFinancePO();
            supplyOrderFinanceQuery.setSupplyOrderId(supplyOrderPO.getId());
            supplyOrderFinanceQuery.setFinanceType(StatementTypeEnum.SUPPLY_ORDER_AMT.key);
            SupplyOrderFinancePO supplyOrderFinancePO = supplyOrderFinanceMapper.selectOne(supplyOrderFinanceQuery);
            SupplyOrderFinancePO supplyOrderFinanceUpdate = new SupplyOrderFinancePO();
            supplyOrderFinanceUpdate.setId(supplyOrderFinancePO.getId());
            supplyOrderFinanceUpdate.setUnpaidAmt(supplyOrderPO.getSupplyOrderAmt().subtract(null == supplyOrderFinancePO.getPaidAmt() ? BigDecimal.ZERO : supplyOrderFinancePO.getPaidAmt()));
            if (BigDecimal.ZERO.compareTo(supplyOrderFinanceUpdate.getUnpaidAmt()) == 0) {
                supplyOrderFinanceUpdate.setSettlementStatus(1);
            } else {
                supplyOrderFinanceUpdate.setSettlementStatus(0);
            }
            //更新对账状态
            if (!Objects.equals(supplyOrderFinancePO.getCheckStatus(), CheckStatusEnum.CHECKING.key)) {
                if (BigDecimal.ZERO.compareTo(supplyOrderFinanceUpdate.getUnpaidAmt()) == 0) {
                    //如果未收金额为0，则改为已对账
                    supplyOrderFinanceUpdate.setCheckStatus(CheckStatusEnum.CANNOT_CHECK.key);
                } else {
                    //如果未收金额不为0，则改为可出账
                    supplyOrderFinanceUpdate.setCheckStatus(CheckStatusEnum.CAN_CHECK.key);
                }
            }
            supplyOrderFinanceMapper.updateByPrimaryKeySelective(supplyOrderFinanceUpdate);
        } else {
            if (orderPO.getOrderConfirmationStatus() != OrderStatusEnum.CANCELED.no) {
                throw new SysException(ErrorCodeEnum.NOT_CANCEL_STATUS_CANCELLATION_FEE_CANNOT_BE_MODIFIED.errorCode, ErrorCodeEnum.NOT_CANCEL_STATUS_CANCELLATION_FEE_CANNOT_BE_MODIFIED.errorDesc);
            }
            //修改订单退改费
            BigDecimal changeAmt = refundFee.subtract(orderPO.getRefundFee());
            OrderProductPricePO orderProductPriceQuery = new OrderProductPricePO();
            orderProductPriceQuery.setOrderId(request.getOrderId());

            if (Objects.equals(orderPO.getRefundFee(), refundFee)) {
                return;
            }
            content.append("订单退订费由").append(orderPO.getRefundFee())
                    .append("改为").append(refundFee);
            OrderPO orderUpdate = new OrderPO();
            orderUpdate.setId(request.getOrderId());
            orderUpdate.setOrderAmt(orderPO.getOrderAmt().add(changeAmt));
            orderUpdate.setRefundFee(refundFee);
            orderUpdate.setProfit(orderPO.getProfit().add(changeAmt));
            orderMapper.updateByPrimaryKeySelective(orderUpdate);

            if (changeAmt.compareTo(BigDecimal.ZERO) != 0) {
                //更新订单未结算金额和结算状态
                OrderFinancePO orderFinanceQuery = new OrderFinancePO();
                orderFinanceQuery.setOrderId(request.getOrderId());
                OrderFinancePO orderFinancePO = orderFinanceMapper.selectOne(orderFinanceQuery);
                OrderFinancePO orderFinanceUpdate = new OrderFinancePO();
                orderFinanceUpdate.setId(orderFinancePO.getId());
                orderFinanceUpdate.setUnreceivedAmt(orderFinancePO.getUnreceivedAmt().add(changeAmt));
                if (BigDecimal.ZERO.compareTo(orderFinanceUpdate.getUnreceivedAmt()) == 0) {
                    orderFinanceUpdate.setSettlementStatus(1);
                } else {
                    orderFinanceUpdate.setSettlementStatus(0);
                }

//                //如果订单之前为已对账，修改为可出账（应对取消时未录入取消费用，后续修改取消费用的场景）
//                if (CheckStatusEnum.CHECKED.key.equals(orderFinancePO.getCheckStatus())) {
//                    orderFinanceUpdate.setCheckStatus(CheckStatusEnum.CAN_CHECK.key);
//                }

                orderFinanceMapper.updateByPrimaryKeySelective(orderFinanceUpdate);

                if (orderPO.getSettlementType() != SettlementTypeEnum.SINGLE.key) {
                    //非单结更新对账状态
                    if (!Objects.equals(orderFinancePO.getCheckStatus(), CheckStatusEnum.CHECKING.key)) {
                        if (BigDecimal.ZERO.compareTo(orderFinanceUpdate.getUnreceivedAmt()) == 0) {
                            //如果未收金额为0，则改为已对账
                            orderFinanceUpdate.setCheckStatus(CheckStatusEnum.CANNOT_CHECK.key);
                        } else {
                            //如果未收金额不为0，则改为可出账
                            orderFinanceUpdate.setCheckStatus(CheckStatusEnum.CAN_CHECK.key);
                        }
                        orderFinanceMapper.updateByPrimaryKeySelective(orderFinanceUpdate);
                    }

                    // 不是现付才退额度
                    if (!PayMethodEnum.PAY.key.equals(orderPO.getPayMethod())) {
                        // 非单结订单扣退额度
                        // TODO: 2019/7/12 调分销商信用账户明细接口，查询已挂账金额
                        AgentCreditLineDTO agentCreditLineDTO = new AgentCreditLineDTO();
                        if (StrUtilX.isNotEmpty(orderPO.getSubtractLineAccount())) {
                            agentCreditLineDTO.setAgentCode(orderPO.getSubtractLineAccount());
                        } else {
                            agentCreditLineDTO.setAgentCode(orderPO.getAgentCode());
                        }
                        agentCreditLineDTO.setOrderCode(orderPO.getOrderCode());
                        //原订单金额减去修改后金额等于扣退额度i

                        if ((orderPO.getOrderAmt().subtract(orderPO.getOrderAmt().add(changeAmt)).compareTo(BigDecimal.ZERO) < 0)) {
                            agentCreditLineDTO.setDeductRefundCreditLine(orderPO.getOrderAmt().subtract(orderPO.getOrderAmt().add(changeAmt)).negate());
                        } else {
                            agentCreditLineDTO.setDeductRefundCreditLine(orderPO.getOrderAmt().subtract(orderPO.getOrderAmt().add(changeAmt)));
                        }
                        Response<AgentCreditLineResultDTO> creditLineResponse = agentRemote.modifyDeductRefundCreditLine(Collections.singletonList(agentCreditLineDTO));
                        if (!creditLineResponse.isSuccess()) {
                            throw new SysException(creditLineResponse.getFailCode(), creditLineResponse.getFailReason());
                        }
                    }
                }
            }
        }


        //记日志
        orderCommonService.saveOrderLog(
                request.getOrderId(),
                request.getOperator(),
                request.getOrderOwnerName(),
                orderPO.getOrderCode(),
                content.toString()
        );

        try {
            //加入到统计报表队列中
            stringRedisTemplate.convertAndSend(RedisKey.REPORT_STATISTICS, orderPO.getOrderCode());
        } catch (Exception e) {
            log.error("加入到统计报表队列异常,error:", e);
        }
    }

    @Override
    public void modifyOrderInvoice(OrderInvoiceDTO orderInvoiceDTO) throws Exception {
        if (orderInvoiceDTO.getInvoiceStatus() == null || orderInvoiceDTO.getInvoiceAmount() == null
                || orderInvoiceDTO.getInvoiceType() == null || StrUtilX.isEmpty(orderInvoiceDTO.getInvoiceTitle())
                || null == orderInvoiceDTO.getInvoiceName() || StrUtilX.isEmpty(orderInvoiceDTO.getApplyDate())
                || StrUtilX.isEmpty(orderInvoiceDTO.getOrderCode()) || orderInvoiceDTO.getInvoiceTitleType() == null) {
            log.warn("修改订单发票信息失败：请求参数为空{}", orderInvoiceDTO);
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
        OrderInvoicePO orderInvoicePO = ProductSalePriceConvert.INSTANCE.orderInvoicePOConvert(orderInvoiceDTO);
        // 因api创建订单也会调用这个接口，所以存在没有id并且有记录
        OrderInvoicePO queryOrderInvoice = new OrderInvoicePO();
        queryOrderInvoice.setOrderCode(orderInvoiceDTO.getOrderCode());
        OrderInvoicePO oldOrderInvoice = orderInvoiceMapper.selectOne(queryOrderInvoice);

        OrderPO orderPO = new OrderPO();
        orderPO.setOrderCode(orderInvoiceDTO.getOrderCode());
        OrderPO order = orderMapper.selectOne(orderPO);

        if (oldOrderInvoice == null) {
            if (orderInvoicePO.getCreatedBy() == null) {
                orderInvoicePO.setCreatedBy(orderInvoiceDTO.getOperator());
            }
            if (orderInvoicePO.getCreatedDt() == null) {
                orderInvoicePO.setCreatedDt(DateUtilX.dateToString(new Date(), "yyyy-MM-dd HH:mm:ss"));
            }
            orderInvoicePO.setSerialNo(IdWorker.getIdStr());
            orderInvoicePO.setAutoOpenInvoiceStatus(0);
            orderInvoicePO.setFileUrlStatus(0);
            orderInvoiceMapper.insert(orderInvoicePO);
            orderInvoiceDTO.setId(orderInvoicePO.getId());
            orderCommonService.saveOrderLog(
                    order.getId(),
                    orderInvoiceDTO.getOperator(),
                    orderPO.getOrderOwnerName(),
                    orderPO.getOrderCode(),
                    "新增了发票信息"
            );
        } else {
            orderInvoicePO.setId(oldOrderInvoice.getId());
            orderInvoicePO.setApplyId(oldOrderInvoice.getApplyId());
            orderInvoicePO.setSortNum(oldOrderInvoice.getSortNum());
            orderInvoicePO.setSpecs(oldOrderInvoice.getSpecs());
            orderInvoicePO.setPerUnit(oldOrderInvoice.getPerUnit());
            orderInvoicePO.setCountNum(oldOrderInvoice.getCountNum());
            orderInvoicePO.setEInvoiceFileMissingStatus(null);
            orderInvoicePO.setEInvoiceFileMissingMessage(null);
            orderInvoicePO.setEInvoiceApplyFailedStatus(null);
            orderInvoicePO.setEInvoiceApplyFailedMessage(null);
            orderInvoicePO.setEFileDownloadFailedStatus(null);
            orderInvoicePO.setEFileDownloadFailedMessage(null);
            orderInvoicePO.setEEmailSendFailedStatus(null);
            orderInvoicePO.setEEmailSendFailedMessage(null);
            orderInvoicePO.setEPushAddressConfigStatus(null);
            orderInvoicePO.setEPushAddressConfigMessage(null);
            orderInvoicePO.setEPushAddressInvalidStatus(null);
            orderInvoicePO.setEPushAddressInvalidMessage(null);
            orderInvoicePO.setECreateBillStatus(null);
            orderInvoicePO.setECreateBillMessage(null);
            updateOrderInvoice(orderInvoiceDTO.getOperator(), orderInvoicePO, oldOrderInvoice, order, false);
        }
    }

    /**
     * 更新订单发票信息
     */
    @Override
    public Integer updateOrderInvoice(OrderInvoiceDTO orderInvoiceDTO, Integer status) {
        if (orderInvoiceDTO.getId() == null && orderInvoiceDTO.getOrderCode() == null) {
            throw new SysException(ErrorCodeEnum.INVOICE_UPDATE_FAILED_NO_ORDER_INFO);
        }
        OrderInvoicePO orderInvoicePO = ProductSalePriceConvert.INSTANCE.orderInvoicePOConvert(orderInvoiceDTO);
        OrderInvoicePO queryOrderInvoice = new OrderInvoicePO();
        queryOrderInvoice.setOrderCode(orderInvoicePO.getOrderCode());
        queryOrderInvoice.setInvoiceStatus(status);
        queryOrderInvoice.setId(orderInvoicePO.getId());
        OrderInvoicePO oldOrderInvoice = orderInvoiceMapper.selectOne(queryOrderInvoice);
        if (ObjectUtil.isEmpty(oldOrderInvoice)) {
            log.info("发票更新失败 未找到发票信息:{} 发票状态{}", orderInvoiceDTO, status);
            return 0;
        }
        OrderPO orderPO = new OrderPO();
        orderPO.setOrderCode(oldOrderInvoice.getOrderCode());
        OrderPO order = orderMapper.selectOne(orderPO);
        if (ObjectUtil.isEmpty(order)) {
            throw new SysException(ErrorCodeEnum.ORDER_CODE_IS_NOT_EXIST);
        }
        orderInvoicePO.setId(oldOrderInvoice.getId());
        return updateOrderInvoice(orderInvoiceDTO.getOperator(), orderInvoicePO, oldOrderInvoice, order, true);
    }

    private Integer updateOrderInvoice(String operator, OrderInvoicePO orderInvoicePO, OrderInvoicePO oldOrderInvoice, OrderPO order, Boolean needPush) {
        int update = orderInvoiceMapper.updateByPrimaryKeySelective(orderInvoicePO);
        if (update > 0) {
            // 组装内容
            String content = compareContent(orderInvoicePO, oldOrderInvoice);
            if (StrUtilX.isNotEmpty(content)) {
                //记日志
                orderCommonService.saveOrderLog(
                        order.getId(),
                        operator,
                        order.getOrderOwnerName(),
                        order.getOrderCode(),
                        content
                );
            }
            if (!needPush) {
                return update;
            }
            // 开票完成后操作
            try {
                // 调用推送发票状态通知接口
                personInvoiceService.pushInvoiceStatusNotifyById(oldOrderInvoice.getId(), operator, false);
            } catch (Exception e) {
                log.error("推送发票状态通知异常", e);
            }
        }
        return update;
    }

    private String compareContent(OrderInvoicePO orderInvoicePO, OrderInvoicePO oldOrderInvoice) {
        StringBuilder stringBuffer = new StringBuilder();

        if (CommonTgUtils.compareInteger(orderInvoicePO.getInvoiceStatus(), oldOrderInvoice.getInvoiceStatus(), true) > 0) {
            stringBuffer.append("开票状态由").append(Optional.ofNullable(InvoiceStatusEnum.getDesc(oldOrderInvoice.getInvoiceStatus())).orElse("空"))
                    .append("变成").append(Optional.ofNullable(InvoiceStatusEnum.getDesc(orderInvoicePO.getInvoiceStatus())).orElse("空")).append(",");
        }

        if (StrUtil.compare(orderInvoicePO.getInvoiceDate(), oldOrderInvoice.getInvoiceDate(), true) > 0) {
            stringBuffer.append("开票时间由").append(Optional.ofNullable(oldOrderInvoice.getInvoiceDate()).orElse("空"))
                    .append("变成").append(Optional.ofNullable(orderInvoicePO.getInvoiceDate()).orElse("空")).append(",");
        }

        if (CommonTgUtils.compareBigDecimal(orderInvoicePO.getInvoiceAmount(), oldOrderInvoice.getInvoiceAmount(), true) > 0) {
            stringBuffer.append("开票金额由").append(Optional.ofNullable(oldOrderInvoice.getInvoiceAmount()).map(BigDecimal::toString).orElse("空"))
                    .append("变成").append(Optional.ofNullable(orderInvoicePO.getInvoiceAmount()).map(BigDecimal::toString).orElse("空")).append(",");
        }

        if (CommonTgUtils.compareInteger(orderInvoicePO.getInvoiceType(), oldOrderInvoice.getInvoiceType(), true) > 0) {
            stringBuffer.append("发票类型由").append(Optional.ofNullable(InvoiceTypeEnum.getDesc(oldOrderInvoice.getInvoiceType())).orElse("空"))
                    .append("变成").append(Optional.ofNullable(InvoiceTypeEnum.getDesc(orderInvoicePO.getInvoiceType())).orElse("空")).append(",");
        }

        if (StrUtil.compare(orderInvoicePO.getInvoiceTitle(), oldOrderInvoice.getInvoiceTitle(), true) > 0) {
            stringBuffer.append("发票抬头由").append(Optional.ofNullable(oldOrderInvoice.getInvoiceTitle()).orElse("空"))
                    .append("变成").append(Optional.ofNullable(orderInvoicePO.getInvoiceTitle()).orElse("空")).append(",");
        }

        if (StrUtil.compare(orderInvoicePO.getTaxNumber(), oldOrderInvoice.getTaxNumber(), true) > 0) {
            stringBuffer.append("税号由").append(Optional.ofNullable(oldOrderInvoice.getTaxNumber()).orElse("空"))
                    .append("变成").append(Optional.ofNullable(orderInvoicePO.getTaxNumber()).orElse("空")).append(",");
        }

        if (StrUtil.compare(orderInvoicePO.getAccountBank(), oldOrderInvoice.getAccountBank(), true) > 0) {
            stringBuffer.append("开户行由").append(Optional.ofNullable(oldOrderInvoice.getAccountBank()).orElse("空"))
                    .append("变成").append(Optional.ofNullable(orderInvoicePO.getAccountBank()).orElse("空")).append(",");
        }

        if (StrUtil.compare(orderInvoicePO.getCompanyPhone(), oldOrderInvoice.getCompanyPhone(), true) > 0) {
            stringBuffer.append("公司电话由").append(Optional.ofNullable(oldOrderInvoice.getCompanyPhone()).orElse("空"))
                    .append("变成").append(Optional.ofNullable(orderInvoicePO.getCompanyPhone()).orElse("空")).append(",");
        }

        if (StrUtil.compare(orderInvoicePO.getReceivedAddr(), oldOrderInvoice.getReceivedAddr(), true) > 0) {
            stringBuffer.append("注册地址由").append(Optional.ofNullable(oldOrderInvoice.getReceivedAddr()).orElse("空"))
                    .append("变成").append(Optional.ofNullable(orderInvoicePO.getReceivedAddr()).orElse("空")).append(",");
        }

        if (StrUtil.compare(orderInvoicePO.getAccountNo(), oldOrderInvoice.getAccountNo(), true) > 0) {
            stringBuffer.append("银行账号由").append(Optional.ofNullable(oldOrderInvoice.getAccountNo()).orElse("空"))
                    .append("变成").append(Optional.ofNullable(orderInvoicePO.getAccountNo()).orElse("空")).append(",");
        }

        if (StrUtil.compare(orderInvoicePO.getInvoiceRemark(), oldOrderInvoice.getInvoiceRemark(), true) > 0) {
            stringBuffer.append("发票备注由").append(Optional.ofNullable(oldOrderInvoice.getInvoiceRemark()).orElse("空"))
                    .append("变成").append(Optional.ofNullable(orderInvoicePO.getInvoiceRemark()).orElse("空")).append(",");
        }

        if (StrUtil.compare(orderInvoicePO.getApplyDate(), oldOrderInvoice.getApplyDate(), true) > 0) {
            stringBuffer.append("申请日期由").append(Optional.ofNullable(oldOrderInvoice.getApplyDate()).orElse("空"))
                    .append("变成").append(Optional.ofNullable(orderInvoicePO.getApplyDate()).orElse("空")).append(",");
        }


        if (CommonTgUtils.compareInteger(orderInvoicePO.getTicketType(), oldOrderInvoice.getTicketType(), true) > 0) {
            stringBuffer.append("取票方式由").append(Optional.ofNullable(TicketTypeEnum.getDesc(oldOrderInvoice.getTicketType(), orderInvoicePO.getInvoiceType())).orElse("空"))
                    .append("变成").append(Optional.ofNullable(TicketTypeEnum.getDesc(orderInvoicePO.getTicketType(), orderInvoicePO.getInvoiceType())).orElse("空")).append(",");
        }

        if (CommonTgUtils.compareInteger(orderInvoicePO.getSendStatus(), oldOrderInvoice.getSendStatus(), true) > 0) {
            stringBuffer.append("发送状态由").append(Optional.ofNullable(SendStatusEnum.getDesc(oldOrderInvoice.getSendStatus())).orElse("空"))
                    .append("变成").append(Optional.ofNullable(SendStatusEnum.getDesc(orderInvoicePO.getSendStatus())).orElse("空")).append(",");
        }

        if (StrUtil.compare(orderInvoicePO.getReceivedName(), oldOrderInvoice.getReceivedName(), true) > 0) {
            stringBuffer.append("收件人由").append(Optional.ofNullable(oldOrderInvoice.getReceivedName()).orElse("空"))
                    .append("变成").append(Optional.ofNullable(orderInvoicePO.getReceivedName()).orElse("空")).append(",");
        }

        if (StrUtil.compare(orderInvoicePO.getReceivedPhone(), oldOrderInvoice.getReceivedPhone(), true) > 0) {
            stringBuffer.append("收件人电话由").append(Optional.ofNullable(oldOrderInvoice.getReceivedPhone()).orElse("空"))
                    .append("变成").append(Optional.ofNullable(orderInvoicePO.getReceivedPhone()).orElse("空")).append(",");
        }

        if (StrUtil.compare(orderInvoicePO.getReceivedAddr(), oldOrderInvoice.getReceivedAddr(), true) > 0) {
            stringBuffer.append("收件人地址由").append(Optional.ofNullable(oldOrderInvoice.getReceivedAddr()).orElse("空"))
                    .append("变成").append(Optional.ofNullable(orderInvoicePO.getReceivedAddr()).orElse("空")).append(",");
        }

        if (StrUtil.compare(orderInvoicePO.getTrackingNo(), oldOrderInvoice.getTrackingNo(), true) > 0) {
            stringBuffer.append("快递单号由").append(Optional.ofNullable(oldOrderInvoice.getTrackingNo()).orElse("空"))
                    .append("变成").append(Optional.ofNullable(orderInvoicePO.getTrackingNo()).orElse("空")).append(",");
        }

        if (StrUtil.compare(orderInvoicePO.getEmail(), oldOrderInvoice.getEmail(), true) > 0) {
            stringBuffer.append(" 邮箱由").append(Optional.ofNullable(oldOrderInvoice.getEmail()).orElse("空"))
                    .append("变成").append(Optional.ofNullable(orderInvoicePO.getEmail()).orElse("空")).append(",");
        }

        if (CommonTgUtils.compareInteger(orderInvoicePO.getInvoiceTitleType(), oldOrderInvoice.getInvoiceTitleType(), true) > 0) {
            stringBuffer.append("发票抬头类型由").append(Optional.ofNullable(InvoiceTitleTypeEnum.getDesc(oldOrderInvoice.getInvoiceTitleType())).orElse("空"))
                    .append("变成").append(Optional.ofNullable(InvoiceTitleTypeEnum.getDesc(orderInvoicePO.getInvoiceTitleType())).orElse("空")).append(",");
        }
        if (StrUtil.compare(orderInvoicePO.getInvoiceNo(), oldOrderInvoice.getInvoiceNo(), true) > 0) {
            stringBuffer.append("发票号码由").append(Optional.ofNullable(oldOrderInvoice.getInvoiceNo()).orElse("空"))
                    .append("变成").append(Optional.ofNullable(orderInvoicePO.getInvoiceNo()).orElse("空")).append(",");
        }
        if (StrUtil.compare(orderInvoicePO.getInvoiceUrls(), oldOrderInvoice.getInvoiceUrls(), true) > 0) {
            stringBuffer.append("发票附件由").append(Optional.ofNullable(oldOrderInvoice.getInvoiceUrls()).orElse("空"))
                    .append("变成").append(Optional.ofNullable(orderInvoicePO.getInvoiceUrls()).orElse("空")).append(",");
        }
        log.info("发票信息是：" + stringBuffer);
        String substring = null;
        if (stringBuffer.length() > 0) {
            substring = stringBuffer.substring(0, stringBuffer.length() - 1);
        }

        return substring;
    }


    /**
     * 分页查询订单
     */
    @Override
    public PageInfo<OrderPO> queryPage(QueryOrderReq queryOrderReq) {
        PageHelper.startPage(queryOrderReq.getPageNum(), queryOrderReq.getPageSize());
        Example example = new Example(OrderPO.class);
        Example.Criteria criteria = example.createCriteria();
        if (StrUtilX.isNotEmpty(queryOrderReq.getChannelCode())) {
            criteria.andEqualTo("channelCode", queryOrderReq.getChannelCode());
        }
        if (queryOrderReq.getOrderConfirmationStatus() != null) {
            criteria.andEqualTo("orderConfirmationStatus", queryOrderReq.getOrderConfirmationStatus());
        }
        if (queryOrderReq.getEndTime() != null) {
            criteria.andLessThanOrEqualTo("endDate", queryOrderReq.getEndTime());
        }
        if (queryOrderReq.getCreatedDt() != null) {
            criteria.andLessThanOrEqualTo("createdDt", queryOrderReq.getCreatedDt());
        }
        List<OrderPO> orderList = orderMapper.selectByExample(example);
        return new PageInfo<>(orderList);
    }

    /**
     * 根据订单更新订单状态为已完成
     */
    @Override
    public void batchUpdateOrderStatusDone(List<Integer> ids) {
        orderMapper.batchUpDateStatusDone(ids);
    }

    /**
     * 推送已完成给分销商客户
     */
    @Override
    public void pushOrderStatusDone(OrderPO orderPO) {
        B2BOrderStatusPushRequest b2BOrderStatusPushRequest = new B2BOrderStatusPushRequest();
        b2BOrderStatusPushRequest.setCoOrderCode(orderPO.getChannelOrderCode());
        b2BOrderStatusPushRequest.setFcOrderCode(orderPO.getOrderCode());
        b2BOrderStatusPushRequest.setOrderStatus(5);
        b2BOrderStatusPushRequest.setAgentCode(orderPO.getAgentCode());
        b2BOrderStatusPushRequest.setPartnerCode(orderPO.getPartnerCode());
        b2BOrderStatusPushRequest.setCancellationPrice(orderPO.getRefundFee());
        //disB2BRemote.orderStatusPush(b2BOrderStatusPushRequest);
    }

    /**
     * 删除订单附件(逻辑删除)
     */
    @Override
    public void deleteOrderAttachmentByLogic(DeleteOrderAttachmentByLogicDTO deleteOrderAttachmentByLogicDTO) {
        if (deleteOrderAttachmentByLogicDTO.getOrderId() == null || deleteOrderAttachmentByLogicDTO.getFileUrl() == null) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
        String fileUrl = deleteOrderAttachmentByLogicDTO.getFileUrl();
        OrderAttachmentPO orderAttachmentPO = new OrderAttachmentPO();
        orderAttachmentPO.setActive(0);
        Example orderAttachmentExample = new Example(OrderAttachmentPO.class);
        Example.Criteria orderAttachmentCriteria = orderAttachmentExample.createCriteria();
        orderAttachmentCriteria
                .andEqualTo("orderId", deleteOrderAttachmentByLogicDTO.getOrderId())
                .andEqualTo("url", fileUrl);
        orderAttachmentMapper.updateByExampleSelective(orderAttachmentPO, orderAttachmentExample);
    }

    /**
     * 修改订单附加项费用
     *
     * @param additionalChargesEditList 附加费对象
     * @param operator                  操作人
     * @param orderOwnerName            订单归属人
     */
    @Override
    @Transactional
    public void additionalChargesEdit(List<AdditionalChargesEditDTO> additionalChargesEditList, String operator, String orderOwnerName) {
        StringBuilder logContent = new StringBuilder();
        if (CollUtilX.isEmpty(additionalChargesEditList) || additionalChargesEditList.get(0) == null
                || additionalChargesEditList.get(0).getOrderAdditionalCharges() == null
                || additionalChargesEditList.get(0).getOrderAdditionalCharges().getOrderId() == null) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }


        Integer orderId = additionalChargesEditList.get(0).getOrderAdditionalCharges().getOrderId();

        Map<Integer, SupplyOrderAdditionalChargesPO> supplyOrderAdditionalChargesMap = supplyOrderAdditionalChargesMapper.selectList(new QueryWrapper<SupplyOrderAdditionalChargesPO>().lambda()
                .eq(SupplyOrderAdditionalChargesPO::getOrderId, orderId)).stream().collect(Collectors.toMap(SupplyOrderAdditionalChargesPO::getId, Function.identity(), (k1, k2) -> k2));
        Map<Integer, OrderAdditionalChargesPO> orderAdditionalChargesMap = orderAdditionalChargesMapper.selectList(new QueryWrapper<OrderAdditionalChargesPO>().lambda()
                .eq(OrderAdditionalChargesPO::getOrderId, orderId)).stream().collect(Collectors.toMap(OrderAdditionalChargesPO::getId, Function.identity(), (k1, k2) -> k2));


        //每次提取一个附加项列表，对id进行判断，如果存在则更新，
        for (AdditionalChargesEditDTO additionalChargesEditDTO : additionalChargesEditList) {
            OrderAdditionalChargesEditDTO orderAdditionalCharges = additionalChargesEditDTO.getOrderAdditionalCharges();
            if (orderAdditionalCharges != null && orderAdditionalCharges.getId() != null) {
                if (orderAdditionalCharges.getQuantity() == null) {
                    orderAdditionalCharges.setQuantity(1);
                }
                if (orderAdditionalCharges.getAdditionalCharges() == null) {
                    orderAdditionalCharges.setAdditionalCharges(new BigDecimal(0));
                }
                OrderAdditionalChargesPO orderAdditionalChargesPO = orderAdditionalChargesMap.get(orderAdditionalCharges.getId());
                if (orderAdditionalChargesPO != null && (!Objects.equals(orderAdditionalChargesPO.getQuantity(), orderAdditionalCharges.getQuantity())
                        || orderAdditionalChargesPO.getAdditionalCharges().compareTo(orderAdditionalCharges.getAdditionalCharges()) != 0)) {
                    orderAdditionalChargesMapper.update(null, new UpdateWrapper<OrderAdditionalChargesPO>().lambda()
                            .set(OrderAdditionalChargesPO::getQuantity, orderAdditionalCharges.getQuantity())
                            .set(OrderAdditionalChargesPO::getAdditionalCharges, orderAdditionalCharges.getAdditionalCharges())
                            .eq(OrderAdditionalChargesPO::getId, orderAdditionalCharges.getId()));
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                    logContent.append("订单:(")
                            .append(orderAdditionalChargesPO.getOrderCode())
                            .append(")附加费用:修改")
                            .append("【")
                            .append(AdditionalChargesTypeEnum.getValueByKey(orderAdditionalChargesPO.getAdditionalChargesType()))
                            .append("】(")
                            .append(orderAdditionalChargesPO.getAdditionalChargesName())
                            .append("),")
                            .append(sdf.format(orderAdditionalChargesPO.getAdditionalChargesDate()))
                            .append("(")
                            .append(orderAdditionalChargesPO.getAdditionalCharges())
                            .append("变为")
                            .append(orderAdditionalCharges.getAdditionalCharges())
                            .append(");");
                }
            }


            List<SupplyOrderAdditionalChargesEditDTO> supplyOrderAdditionalChargesList = additionalChargesEditDTO.getSupplyOrderAdditionalChargesList();
            if (CollUtilX.isEmpty(supplyOrderAdditionalChargesList)) {
                continue;
            }
            for (SupplyOrderAdditionalChargesEditDTO supplyOrderAdditionalChargesEditDTO : supplyOrderAdditionalChargesList) {
                if (supplyOrderAdditionalChargesEditDTO.getQuantity() == null) {
                    supplyOrderAdditionalChargesEditDTO.setQuantity(1);
                }
                if (supplyOrderAdditionalChargesEditDTO.getAdditionalCharges() == null) {
                    supplyOrderAdditionalChargesEditDTO.setAdditionalCharges(new BigDecimal(0));
                }
                if (supplyOrderAdditionalChargesEditDTO.getId() != null) {
                    SupplyOrderAdditionalChargesPO supplyOrderAdditionalChargesPO = supplyOrderAdditionalChargesMap.get(supplyOrderAdditionalChargesEditDTO.getId());
                    if (supplyOrderAdditionalChargesPO != null && (!Objects.equals(supplyOrderAdditionalChargesPO.getQuantity(), supplyOrderAdditionalChargesEditDTO.getQuantity())
                            || supplyOrderAdditionalChargesPO.getAdditionalCharges().compareTo(supplyOrderAdditionalChargesEditDTO.getAdditionalCharges()) != 0)) {
                        supplyOrderAdditionalChargesMapper.update(null, new UpdateWrapper<SupplyOrderAdditionalChargesPO>().lambda()
                                .set(SupplyOrderAdditionalChargesPO::getQuantity, supplyOrderAdditionalChargesEditDTO.getQuantity())
                                .set(SupplyOrderAdditionalChargesPO::getAdditionalCharges, supplyOrderAdditionalChargesEditDTO.getAdditionalCharges())
                                .eq(SupplyOrderAdditionalChargesPO::getId, supplyOrderAdditionalChargesEditDTO.getId()));
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                        logContent.append("供货单:(")
                                .append(supplyOrderAdditionalChargesPO.getSupplyOrderCode())
                                .append(")附加费用:修改")
                                .append("【")
                                .append(AdditionalChargesTypeEnum.getValueByKey(supplyOrderAdditionalChargesPO.getAdditionalChargesType()))
                                .append("】(")
                                .append(supplyOrderAdditionalChargesPO.getAdditionalChargesName())
                                .append("),")
                                .append(sdf.format(supplyOrderAdditionalChargesPO.getAdditionalChargesDate()))
                                .append("(")
                                .append(supplyOrderAdditionalChargesPO.getAdditionalCharges())
                                .append("变为")
                                .append(supplyOrderAdditionalChargesEditDTO.getAdditionalCharges())
                                .append(");");
                    }
                }
            }
        }

        //合计所有订单附加费
        BigDecimal sumOrderAdditionalChargesAmt = new BigDecimal(0);
        List<OrderAdditionalChargesPO> orderAdditionalChargesList = orderAdditionalChargesMapper.selectList(new QueryWrapper<OrderAdditionalChargesPO>().lambda()
                .eq(OrderAdditionalChargesPO::getOrderId, orderId));
        for (OrderAdditionalChargesPO orderAdditionalChargesPO : orderAdditionalChargesList) {
            sumOrderAdditionalChargesAmt = sumOrderAdditionalChargesAmt.add(orderAdditionalChargesPO.getAdditionalCharges().multiply(BigDecimal.valueOf(orderAdditionalChargesPO.getQuantity())));
        }


        //合计所有供货单附加费
        List<SupplyOrderAdditionalChargesPO> supplyOrderAdditionalChargesList = supplyOrderAdditionalChargesMapper.selectList(new QueryWrapper<SupplyOrderAdditionalChargesPO>().lambda()
                .eq(SupplyOrderAdditionalChargesPO::getOrderId, orderId));
        Map<Integer, List<SupplyOrderAdditionalChargesPO>> supplyOrderAdditionalChargesGroup = supplyOrderAdditionalChargesList.stream().collect(Collectors.groupingBy(SupplyOrderAdditionalChargesPO::getSupplyOrderId));
        Map<Integer, BigDecimal> sumSupplyOrderAmtMap = new HashMap<>();
        BigDecimal sumSupplyOrderAmtTmp = new BigDecimal(0);
        for (Map.Entry<Integer, List<SupplyOrderAdditionalChargesPO>> supplyOrderAdditionalCharges : supplyOrderAdditionalChargesGroup.entrySet()) {
            for (SupplyOrderAdditionalChargesPO supplyOrderAdditionalChargesPO : supplyOrderAdditionalCharges.getValue()) {
                sumSupplyOrderAmtTmp = sumSupplyOrderAmtTmp.add(supplyOrderAdditionalChargesPO.getAdditionalCharges().multiply(BigDecimal.valueOf(supplyOrderAdditionalChargesPO.getQuantity())));
            }
            sumSupplyOrderAmtMap.put(supplyOrderAdditionalCharges.getKey(), sumSupplyOrderAmtTmp);
        }

        Example orderExample = new Example(OrderPO.class);
        orderExample.createCriteria().andEqualTo("id", orderId);
        OrderPO orderPO = orderMapper.selectOneByExample(orderExample);

        BigDecimal sumSupplyOrderAmt = new BigDecimal(0);
        BigDecimal rewardAmt = BigDecimal.ZERO;
        BigDecimal rebateAmt = BigDecimal.ZERO;
        for (Integer supplyOrderId : supplyOrderAdditionalChargesGroup.keySet()) {
            //根据供货单id查找对应的供货单
            Example supplyOrderExample = new Example(SupplyOrderPO.class);
            supplyOrderExample.createCriteria().andEqualTo("id", supplyOrderId);
            SupplyOrderPO supplyOrderPO = supplyOrderMapper.selectOneByExample(supplyOrderExample);
            //查询供货单对应的产品
            Example supplyProductExample = new Example(SupplyProductPO.class);
            supplyProductExample.createCriteria().andEqualTo("supplyOrderId", supplyOrderId);
            List<SupplyProductPO> supplyProductPOList = supplyProductMapper.selectByExample(supplyProductExample);
            int supplyOrderRoomQty = supplyProductPOList.get(0).getRoomQty() == null ? 1 : supplyProductPOList.get(0).getRoomQty();

            BigDecimal sumBasePrice = new BigDecimal(0);
            //查询供货单价格明细
            List<PriceResponseDTO> basePriceList = supplyProductPriceMapper.querySupplyOrderPriceList(supplyOrderId);
            for (PriceResponseDTO priceResponseDTO : basePriceList) {
                BigDecimal basePrice = priceResponseDTO.getBasePrice() == null ? new BigDecimal(0) : priceResponseDTO.getBasePrice();
                sumBasePrice = sumBasePrice.add(basePrice.multiply(BigDecimal.valueOf(supplyOrderRoomQty)));
            }

            Example updateSupplyOrderExample = new Example(SupplyOrderPO.class);
            updateSupplyOrderExample.createCriteria().andEqualTo("id", supplyOrderId);
            BigDecimal supplyOrderAmtTmp = new BigDecimal(0);
            if (supplyOrderPO.getConfirmationStatus() != null && supplyOrderPO.getConfirmationStatus() != 2) {//非已取消
                SupplyOrderPO updateSupplyOrderPO = new SupplyOrderPO();
                //供货单应付金额 = 供货单底价之和 + 附加项费用(负值) - 供应商返佣
                supplyOrderAmtTmp = sumBasePrice.add(sumSupplyOrderAmtMap.get(supplyOrderId) == null ? new BigDecimal(0) : sumSupplyOrderAmtMap.get(supplyOrderId))
                        .subtract(supplyOrderPO.getCommission() == null ? new BigDecimal(0) : supplyOrderPO.getCommission());
                sumSupplyOrderAmt = sumSupplyOrderAmt.add(supplyOrderAmtTmp);
                // 不是现付才修改供货单应付金额
                if (!PayMethodEnum.PAY.key.equals(orderPO.getPayMethod())) {
                    updateSupplyOrderPO.setSupplyOrderAmt(supplyOrderAmtTmp);
                }
                updateSupplyOrderPO.setAdditionalCharges(sumSupplyOrderAmtMap.get(supplyOrderId) == null ? new BigDecimal(0) : sumSupplyOrderAmtMap.get(supplyOrderId));
                supplyOrderMapper.updateByExampleSelective(updateSupplyOrderPO, updateSupplyOrderExample);
                //非已取消时才记录日志
                if (!PayMethodEnum.PAY.key.equals(orderPO.getPayMethod())) {
                    logContent.append("供货单总金额由")
                            .append(supplyOrderPO.getSupplyOrderAmt())
                            .append("变为")
                            .append(supplyOrderAmtTmp.stripTrailingZeros().toPlainString())
                            .append(";");
                }
            } else if (supplyOrderPO.getConfirmationStatus() != null) {//已取消
                SupplyOrderPO updateSupplyOrderPO = new SupplyOrderPO();
                sumSupplyOrderAmt = sumSupplyOrderAmt.add(supplyOrderPO.getSupplyOrderAmt());
                updateSupplyOrderPO.setAdditionalCharges(sumSupplyOrderAmtMap.get(supplyOrderId) == null ? new BigDecimal(0) : sumSupplyOrderAmtMap.get(supplyOrderId));
                supplyOrderMapper.updateByExampleSelective(updateSupplyOrderPO, updateSupplyOrderExample);
            }


            if (supplyOrderAmtTmp.compareTo(supplyOrderPO.getSupplyOrderAmt()) != 0) {
                //更新订单未结算金额和结算状态
                SupplyOrderFinancePO supplyOrderFinanceQuery = new SupplyOrderFinancePO();
                supplyOrderFinanceQuery.setSupplyOrderId(supplyOrderPO.getId());
                supplyOrderFinanceQuery.setFinanceType(StatementTypeEnum.SUPPLY_ORDER_AMT.key);
                SupplyOrderFinancePO supplyOrderFinancePO = supplyOrderFinanceMapper.selectOne(supplyOrderFinanceQuery);
                SupplyOrderFinancePO supplyOrderFinanceUpdate = new SupplyOrderFinancePO();
                supplyOrderFinanceUpdate.setId(supplyOrderFinancePO.getId());
                //如果原来供货单是取消状态
                if (supplyOrderPO.getConfirmationStatus().equals(ConfirmationStatusEnum.CANCELED.key) || PayMethodEnum.PAY.key.equals(orderPO.getPayMethod())) {
                    supplyOrderFinanceUpdate.setUnpaidAmt(supplyOrderPO.getSupplyOrderAmt().subtract(supplyOrderFinancePO.getPaidAmt() == null ? new BigDecimal(0) : supplyOrderFinancePO.getPaidAmt()));
                } else {
                    supplyOrderFinanceUpdate.setUnpaidAmt(supplyOrderAmtTmp.subtract(supplyOrderFinancePO.getPaidAmt() == null ? new BigDecimal(0) : supplyOrderFinancePO.getPaidAmt()));
                }

                if (BigDecimal.ZERO.compareTo(supplyOrderFinanceUpdate.getUnpaidAmt()) == 0) {
                    supplyOrderFinanceUpdate.setSettlementStatus(1);
                } else {
                    supplyOrderFinanceUpdate.setSettlementStatus(0);
                }
                //非单结更新对账状态
                if (!Objects.equals(supplyOrderFinancePO.getCheckStatus(), CheckStatusEnum.CHECKING.key)) {
                    if (BigDecimal.ZERO.compareTo(supplyOrderFinanceUpdate.getUnpaidAmt()) == 0) {
                        //如果未收金额为0，则改为已对账
                        supplyOrderFinanceUpdate.setCheckStatus(CheckStatusEnum.CANNOT_CHECK.key);
                    } else {
                        //如果未收金额不为0，则改为可出账
                        supplyOrderFinanceUpdate.setCheckStatus(CheckStatusEnum.CAN_CHECK.key);
                    }
                }
                supplyOrderFinanceMapper.updateByPrimaryKeySelective(supplyOrderFinanceUpdate);
            }
            if (supplyOrderPO.getRewardAmt() != null) {
                rewardAmt = rewardAmt.add(supplyOrderPO.getRewardAmt());
            }

            if (supplyOrderPO.getRebateAmt() != null) {
                rebateAmt = rebateAmt.add(supplyOrderPO.getRebateAmt());
            }
        }


        //计算订单售价之和
        Example orderProductPriceExample = new Example(OrderProductPricePO.class);
        orderProductPriceExample.setOrderByClause("sale_date");
        Example.Criteria orderProductPriceCriteria = orderProductPriceExample.createCriteria();
        orderProductPriceCriteria.andEqualTo("orderId", orderId);
        orderProductPriceCriteria.andIsNotNull("saleDate");
        List<OrderProductPricePO> orderProductPricePOList = orderProductPriceMapper.selectByExample(orderProductPriceExample);
        BigDecimal sumOrderAmt = new BigDecimal(0);
        for (OrderProductPricePO orderProductPricePO : orderProductPricePOList) {
            BigDecimal orderProductPrice = orderProductPricePO.getSalePrice() == null ? new BigDecimal(0) : orderProductPricePO.getSalePrice();
            sumOrderAmt = sumOrderAmt.add(orderProductPrice);
        }

        Example updateOrderExample = new Example(OrderPO.class);
        updateOrderExample.createCriteria().andEqualTo("id", orderId);
        if (orderPO.getOrderConfirmationStatus() != null && orderPO.getOrderConfirmationStatus() != 2) {//非已取消
            OrderPO updateOrder = new OrderPO();
            // 不是现付才修改订单应收
            if (!PayMethodEnum.PAY.key.equals(orderPO.getPayMethod())) {
                //订单应收 = 售价之和 + 附加项费用(负值)
                updateOrder.setOrderAmt(sumOrderAmt.add(sumOrderAdditionalChargesAmt));
                //利润 = 订单应收 - 所有供货单应付
                updateOrder.setProfit(sumOrderAmt.add(sumOrderAdditionalChargesAmt).subtract(sumSupplyOrderAmt).add(rewardAmt).subtract(rebateAmt));
                // 记录日志
                logContent.append("订单总金额由")
                        .append(orderPO.getOrderAmt())
                        .append("变为")
                        .append(sumOrderAmt.add(sumOrderAdditionalChargesAmt))
                        .append(";");
            }
            updateOrder.setAdditionalCharges(sumOrderAdditionalChargesAmt);
            orderMapper.updateByExampleSelective(updateOrder, updateOrderExample);
        } else if (orderPO.getOrderConfirmationStatus() != null) {//已取消
            OrderPO updateOrder = new OrderPO();
            updateOrder.setAdditionalCharges(sumOrderAdditionalChargesAmt);
            orderMapper.updateByExampleSelective(updateOrder, updateOrderExample);
        }

        //记录日志
        orderCommonService.saveOrderLog(
                orderPO.getId(),
                operator,
                orderOwnerName,
                orderPO.getOrderCode(),
                logContent.toString()
        );

        try {
            //加入到统计报表队列中
            stringRedisTemplate.convertAndSend(RedisKey.REPORT_STATISTICS, orderPO.getOrderCode());
        } catch (Exception e) {
            log.error("加入到统计报表队列异常,error:", e);
        }


        BigDecimal orderAmt = sumOrderAmt.add(sumOrderAdditionalChargesAmt);
        if (orderAmt.compareTo(orderPO.getOrderAmt()) != 0) {//修改订单结算金额和状态等
            //更新订单未结算金额和结算状态
            OrderFinancePO orderFinanceQuery = new OrderFinancePO();
            orderFinanceQuery.setOrderId(orderPO.getId());
            OrderFinancePO orderFinancePO = orderFinanceMapper.selectOne(orderFinanceQuery);
            OrderFinancePO orderFinanceUpdate = new OrderFinancePO();
            orderFinanceUpdate.setId(orderFinancePO.getId());
            if (orderPO.getOrderConfirmationStatus() == 2 || PayMethodEnum.PAY.key.equals(orderPO.getPayMethod())) {//已取消
                orderFinanceUpdate.setUnreceivedAmt(orderPO.getOrderAmt().subtract(orderFinancePO.getReceivedAmt() == null ? new BigDecimal(0) : orderFinancePO.getReceivedAmt()));
            } else {
                orderFinanceUpdate.setUnreceivedAmt(orderAmt.subtract(orderFinancePO.getReceivedAmt() == null ? new BigDecimal(0) : orderFinancePO.getReceivedAmt()));
            }
            if (BigDecimal.ZERO.compareTo(orderFinanceUpdate.getUnreceivedAmt()) == 0) {
                orderFinanceUpdate.setSettlementStatus(1);
            } else {
                orderFinanceUpdate.setSettlementStatus(0);
            }
            orderFinanceMapper.updateByPrimaryKeySelective(orderFinanceUpdate);

            if (orderPO.getSettlementType() != SettlementTypeEnum.SINGLE.key
                    && Objects.equals(orderPO.getOrderConfirmationStatus(), ConfirmationStatusEnum.CONFIRMED.key)) {
                //非单结更新对账状态
                if (!Objects.equals(orderFinancePO.getCheckStatus(), CheckStatusEnum.CHECKING.key)) {
                    if (BigDecimal.ZERO.compareTo(orderFinanceUpdate.getUnreceivedAmt()) == 0) {
                        //如果未收金额为0，则改为已对账
                        orderFinanceUpdate.setCheckStatus(CheckStatusEnum.CANNOT_CHECK.key);
                    } else {
                        //如果未收金额不为0，则改为可出账
                        orderFinanceUpdate.setCheckStatus(CheckStatusEnum.CAN_CHECK.key);
                    }
                    orderFinanceMapper.updateByPrimaryKeySelective(orderFinanceUpdate);
                }

                // 不是现付才退额度
                if (!PayMethodEnum.PAY.key.equals(orderPO.getPayMethod())) {
                    // 非单结订单扣退额度
                    // TODO: 2019/7/12 调分销商信用账户明细接口，查询已挂账金额
                    AgentCreditLineDTO agentCreditLineDTO = new AgentCreditLineDTO();
                    if (StrUtilX.isNotEmpty(orderPO.getSubtractLineAccount())) {
                        agentCreditLineDTO.setAgentCode(orderPO.getSubtractLineAccount());
                    } else {
                        agentCreditLineDTO.setAgentCode(orderPO.getAgentCode());
                    }
                    agentCreditLineDTO.setOrderCode(orderPO.getOrderCode());
                    //原订单金额减去修改后金额等于扣退额度i

                    BigDecimal oldOrderAmtSubtractNewOrderAmt = orderPO.getOrderAmt().subtract(orderAmt);
                    agentCreditLineDTO.setDeductRefundCreditLine(oldOrderAmtSubtractNewOrderAmt);
                    Response<AgentCreditLineResultDTO> creditLineResponse = agentRemote.modifyDeductRefundCreditLine(Collections.singletonList(agentCreditLineDTO));
                    if (!creditLineResponse.isSuccess()) {
                        throw new SysException(creditLineResponse.getFailCode(), creditLineResponse.getFailReason());
                    }
                }
            }
        }
    }

//    /**
//     * 查询附加项
//     *
//     * @param additionalChargesQueryDTO
//     * @return
//     */
//    @Override
//    public Response additionalChargesQuery(AdditionalChargesQueryDTO additionalChargesQueryDTO) {
//        Response response = new Response();
//
//        List<AdditionalChargesQueryResponseDTO> result = new ArrayList<>();
//
//        Map<String, List<SupplyOrderAdditionalChargesPO>> supplyOrderAdditionalChargesMap = supplyOrderAdditionalChargesMapper.selectList(new QueryWrapper<SupplyOrderAdditionalChargesPO>().lambda()
//                .eq(SupplyOrderAdditionalChargesPO::getOrderId, additionalChargesQueryDTO.getOrderId())).stream().collect(Collectors.groupingBy(SupplyOrderAdditionalChargesPO::getGroupNumber));
//
//
//        Map<String, OrderAdditionalChargesPO> orderAdditionalChargesMap = orderAdditionalChargesMapper.selectList(new QueryWrapper<OrderAdditionalChargesPO>().lambda()
//                .eq(OrderAdditionalChargesPO::getOrderId, additionalChargesQueryDTO.getOrderId())).stream().collect(Collectors.toMap(OrderAdditionalChargesPO::getGroupNumber, Function.identity(), (k1, k2) -> k2));
//
//        for (Map.Entry<String, OrderAdditionalChargesPO> orderAdditionalCharges : orderAdditionalChargesMap.entrySet()) {
//            AdditionalChargesQueryResponseDTO additionalChargesQueryResponseDTO = new AdditionalChargesQueryResponseDTO();
//
//            OrderAdditionalChargesQueryResponseDTO orderAdditionalChargesQueryResponseDTO = new OrderAdditionalChargesQueryResponseDTO();
//            BeanUtils.copyProperties(orderAdditionalCharges.getValue(), orderAdditionalChargesQueryResponseDTO);
//
//            List<SupplyOrderAdditionalChargesQueryResponseDTO> list = new ArrayList<>();
//            List<SupplyOrderAdditionalChargesPO> supplyOrderAdditionalChargesPOS = supplyOrderAdditionalChargesMap.get(orderAdditionalCharges.getKey());
//            if (CollUtilX.isNotEmpty(supplyOrderAdditionalChargesPOS)) {
//                for (SupplyOrderAdditionalChargesPO supplyOrderAdditionalChargesPO : supplyOrderAdditionalChargesPOS) {
//                    SupplyOrderAdditionalChargesQueryResponseDTO supplyOrderAdditionalChargesQueryResponseDTO = new SupplyOrderAdditionalChargesQueryResponseDTO();
//                    BeanUtils.copyProperties(supplyOrderAdditionalChargesPO, supplyOrderAdditionalChargesQueryResponseDTO);
//                    list.add(supplyOrderAdditionalChargesQueryResponseDTO);
//                }
//            }
//
//            additionalChargesQueryResponseDTO.setOrderAdditionalCharges(orderAdditionalChargesQueryResponseDTO);
//            additionalChargesQueryResponseDTO.setSupplyOrderAdditionalChargesList(list);
//            result.add(additionalChargesQueryResponseDTO);
//        }
//
//        response.setResult(ResultCodeEnum.SUCCESS.code);
//        response.setModel(result);
//        return response;
//    }

    /**
     * 查询附加项
     */
    @Override
    public Response<List<AdditionalChargesQueryResponseDTO>> additionalChargesQuery(AdditionalChargesQueryDTO additionalChargesQueryDTO) {
        if (additionalChargesQueryDTO.getOrderId() == null) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_ORDERID);
        }
        List<AdditionalChargesQueryResponseDTO> result = new ArrayList<>();

        List<SupplyOrderAdditionalChargesPO> supplyOrderAdditionalChargesList = supplyOrderAdditionalChargesMapper.selectList(new QueryWrapper<SupplyOrderAdditionalChargesPO>().lambda()
                .eq(SupplyOrderAdditionalChargesPO::getOrderId, additionalChargesQueryDTO.getOrderId()));


        List<OrderAdditionalChargesPO> orderAdditionalChargesList = orderAdditionalChargesMapper.selectList(new QueryWrapper<OrderAdditionalChargesPO>().lambda()
                .eq(OrderAdditionalChargesPO::getOrderId, additionalChargesQueryDTO.getOrderId()));

        AdditionalChargesQueryResponseDTO additionalChargesQueryResponseDTO = new AdditionalChargesQueryResponseDTO();
        List<AdditionalChargesQueryItemResponseDTO> additionalChargesItemList = new ArrayList<>();

        if (CollUtilX.isNotEmpty(orderAdditionalChargesList)) {
            for (OrderAdditionalChargesPO orderAdditionalChargesPO : orderAdditionalChargesList) {
                AdditionalChargesQueryItemResponseDTO additionalChargesQueryItemResponseDTO = new AdditionalChargesQueryItemResponseDTO();
                additionalChargesQueryItemResponseDTO.setAdditionalChargesOrderType(0);
                additionalChargesQueryItemResponseDTO.setAdditionalChargesType(orderAdditionalChargesPO.getAdditionalChargesType());
                additionalChargesQueryItemResponseDTO.setAdditionalChargesName(orderAdditionalChargesPO.getAdditionalChargesName());
                additionalChargesQueryItemResponseDTO.setOrderId(orderAdditionalChargesPO.getOrderId());
                List<AdditionalChargesQueryDateItemResponseDTO> additionalChargesQueryDateItemResponseDTOList = new ArrayList<>();
                AdditionalChargesQueryDateItemResponseDTO additionalChargesQueryDateItemResponseDTO = new AdditionalChargesQueryDateItemResponseDTO();
                additionalChargesQueryDateItemResponseDTO.setAdditionalChargesOrderType(0);
                additionalChargesQueryDateItemResponseDTO.setId(orderAdditionalChargesPO.getId());
                additionalChargesQueryDateItemResponseDTO.setAdditionalChargesType(orderAdditionalChargesPO.getAdditionalChargesType());
                additionalChargesQueryDateItemResponseDTO.setAdditionalChargesName(orderAdditionalChargesPO.getAdditionalChargesName());
                additionalChargesQueryDateItemResponseDTO.setOrderId(orderAdditionalChargesPO.getOrderId());
                additionalChargesQueryDateItemResponseDTO.setAdditionalChargesDate(orderAdditionalChargesPO.getAdditionalChargesDate());
                additionalChargesQueryDateItemResponseDTO.setQuantity(orderAdditionalChargesPO.getQuantity());
                additionalChargesQueryDateItemResponseDTO.setAdditionalCharges(orderAdditionalChargesPO.getAdditionalCharges());
                additionalChargesQueryDateItemResponseDTOList.add(additionalChargesQueryDateItemResponseDTO);
                additionalChargesQueryItemResponseDTO.setAdditionalChargesDateItem(additionalChargesQueryDateItemResponseDTOList);
                additionalChargesItemList.add(additionalChargesQueryItemResponseDTO);
            }
        }

        if (CollUtilX.isNotEmpty(supplyOrderAdditionalChargesList)) {
            AdditionalChargesQueryItemResponseDTO additionalChargesQueryItemResponseDTO = new AdditionalChargesQueryItemResponseDTO();
            SupplyOrderAdditionalChargesPO supplyOrderAdditionalChargesPO = supplyOrderAdditionalChargesList.get(0);
            additionalChargesQueryItemResponseDTO.setAdditionalChargesOrderType(1);
            additionalChargesQueryItemResponseDTO.setAdditionalChargesType(supplyOrderAdditionalChargesPO.getAdditionalChargesType());
            additionalChargesQueryItemResponseDTO.setAdditionalChargesName(supplyOrderAdditionalChargesPO.getAdditionalChargesName());
            additionalChargesQueryItemResponseDTO.setOrderId(supplyOrderAdditionalChargesPO.getOrderId());
            List<AdditionalChargesQueryDateItemResponseDTO> additionalChargesQueryDateItemResponseDTOList = new ArrayList<>();
            AdditionalChargesQueryDateItemResponseDTO additionalChargesQueryDateItemResponseDTO = new AdditionalChargesQueryDateItemResponseDTO();
            additionalChargesQueryDateItemResponseDTO.setAdditionalChargesOrderType(1);
            additionalChargesQueryDateItemResponseDTO.setId(supplyOrderAdditionalChargesPO.getId());
            additionalChargesQueryDateItemResponseDTO.setAdditionalChargesType(supplyOrderAdditionalChargesPO.getAdditionalChargesType());
            additionalChargesQueryDateItemResponseDTO.setAdditionalChargesName(supplyOrderAdditionalChargesPO.getAdditionalChargesName());
            additionalChargesQueryDateItemResponseDTO.setOrderId(supplyOrderAdditionalChargesPO.getOrderId());
            additionalChargesQueryDateItemResponseDTO.setAdditionalChargesDate(supplyOrderAdditionalChargesPO.getAdditionalChargesDate());
            additionalChargesQueryDateItemResponseDTO.setQuantity(supplyOrderAdditionalChargesPO.getQuantity());
            additionalChargesQueryDateItemResponseDTO.setAdditionalCharges(supplyOrderAdditionalChargesPO.getAdditionalCharges());
            additionalChargesQueryDateItemResponseDTOList.add(additionalChargesQueryDateItemResponseDTO);
            additionalChargesQueryItemResponseDTO.setAdditionalChargesDateItem(additionalChargesQueryDateItemResponseDTOList);
            additionalChargesItemList.add(additionalChargesQueryItemResponseDTO);
        }
        additionalChargesQueryResponseDTO.setAdditionalChargesItemList(additionalChargesItemList);
        result.add(additionalChargesQueryResponseDTO);

        return Response.success(result);
    }

    @Override
    @Transactional
    public void addOrUpdateSupplierAutoChannel(AddSupplierAutoChannelDTO channelDTO) {
        if (StrUtilX.isEmpty(channelDTO.getAvailable().toString()) || StrUtilX.isEmpty(channelDTO.getSupplierCode())) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }

        //2024年9月4日新增针对供应商新增自动渠道
        //原始流程
        //判断orderMethod是否为空，为空默认为1
        channelDTO.setOrderMethod(null == channelDTO.getOrderMethod() ? OrderMethodEnum.ORDER_METHOD_ENUM_1.key : channelDTO.getOrderMethod());
        Integer id = orderMapper.querySupplierAutoChannelId(channelDTO.getSupplierCode());
        if (id != null) {
            channelDTO.setId(id);
            updateSupplierAutoChannel(channelDTO);
        } else {
            id = addSupplierAutoChannel(channelDTO);
        }

        //2024年9月4日新增针对供应商新增自动渠道
        //判断上传文件附件是否为空
        if (null != channelDTO.getFileAttachDTOList() && !channelDTO.getFileAttachDTOList().isEmpty()) {
            //根据objId查询原始的文件
            FileReq fileReq = new FileReq();
            fileReq.setObjId(String.valueOf(id));
            fileReq.setTableName("t_org_order_automatic");
            Response<List<FileResp>> response = fileRemote.list(fileReq);
            //查询历史数据是否存在
            if (response.getModel() != null && response.getModel().size() > 0) {
                List<FileAttachDTO> photoList = ProductSalePriceConvert.INSTANCE.fileAttachDTOListConvert(response.getModel());

                // 从 photoList 中找出不在 channelDTO.getFileAttachDTOList() 中的 fileId
                List<String> missingFileIds = photoList.stream()
                        .map(FileAttachDTO::getFileId) // 获取所有 fileId
                        .filter(fileId ->
                                channelDTO.getFileAttachDTOList().stream()
                                        .noneMatch(newPhoto -> newPhoto.getFileId().equals(fileId))) // 检查是否不存在于 newPhotoList
                        .collect(Collectors.toList());

                //存在则设置objId为空
                //这里是为了解决替换图片的问题，因为替换图片传值的fileId不一样，没办法替换图片关系
                for (String fileId : missingFileIds) {
                    fileReq = new FileReq();
                    fileReq.setFileId(fileId);
                    fileReq.setTableName("t_org_order_automatic");
                    fileRemote.del(fileReq);
                }
            }

            for (FileAttachDTO fileAttachDTO : channelDTO.getFileAttachDTOList()) {
                fileReq = new FileReq();
                fileReq.setFileId(fileAttachDTO.getFileId());   //文件id
                fileReq.setObjId(String.valueOf(id));           //对象id,表主键id
                fileReq.setTableName("t_org_order_automatic");  //表名
                fileReq.setFieldName(fileAttachDTO.getFieldName());  //文件字段名
                fileRemote.update(fileReq);
            }
        }
    }

    /**
     * 根据订单号修改订单的支付状态
     */
    @Transactional
    @Override
    public void updatePayStatusByOrderCode(UpdatePayStatusByOrderCodeRequest request) {
        if (request.getOrderCode() == null || request.getPayStatus() == null) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
        Example updateOrderExample = new Example(OrderPO.class);
        updateOrderExample.createCriteria().andEqualTo("orderCode", request.getOrderCode());
        OrderPO orderUpdate = new OrderPO();
        orderUpdate.setPayStatus(request.getPayStatus());
        int updateRows = orderMapper.updateByExampleSelective(orderUpdate, updateOrderExample);
        if (updateRows != 1) {
            throw new SysException(ResultEnum.E_1003.getCode(), ResultEnum.E_1003.getMessage());
        }
    }

    @Override
    public Response<Object> pushOrderStatus(PushOrderStatusDTO request) {
        OrderPO orderPO = orderMapper.selectByPrimaryKey(request.getOrderId());
        //订单扩展信息
        OrderExtendPO orderExtendPO = null;
        OrderExtendPO extendQuery = new OrderExtendPO();
        extendQuery.setOrderId(orderPO.getId());
        List<OrderExtendPO> orderExtendPOList = orderExtendMapper.select(extendQuery);
        if (CollUtilX.isNotEmpty(orderExtendPOList)) {
            orderExtendPO = orderExtendPOList.get(0);
        }

        boolean flag = true;// 标记状态
        // 2. 检查供货单状态：没有已确认的供货单才能取消
        SupplyOrderPO supplyOrderQuery = new SupplyOrderPO();
        supplyOrderQuery.setOrderId(request.getOrderId());
        List<SupplyOrderPO> supplyOrderPOList = supplyOrderMapper.select(supplyOrderQuery);
        BigDecimal refundFee = supplyOrderPOList.get(0).getRefundFee() != null ? supplyOrderPOList.get(0).getRefundFee() : BigDecimal.ZERO;
        // 推送订单状态（确认、取消）
        Response baseResponse = Response.success();
        try {
            if (orderPO.getOrderConfirmationStatus() == OrderStatusEnum.CONFIRMED.no) {
                if (StrUtilX.isNotEmpty(orderPO.getChannelOrderCode()) && ChannelEnum.DHUB.key.equals(orderPO.getChannelCode())) {
                    B2BOrderStatusPushRequest b2BOrderStatusPushRequest = new B2BOrderStatusPushRequest();
                    b2BOrderStatusPushRequest.setCoOrderCode(orderPO.getChannelOrderCode());
                    b2BOrderStatusPushRequest.setFcOrderCode(orderPO.getOrderCode());
                    b2BOrderStatusPushRequest.setOrderStatus(3);
                    b2BOrderStatusPushRequest.setMessage(null != orderExtendPO ? orderExtendPO.getCancelledReason() : null);
                    b2BOrderStatusPushRequest.setHotelConfirmNo(null != orderExtendPO ? orderExtendPO.getConfirmationCode() : null);
                    b2BOrderStatusPushRequest.setAgentCode(orderPO.getAgentCode());
                    b2BOrderStatusPushRequest.setPartnerCode(orderPO.getPartnerCode());
                    Response<Object> response = orderStatusPush(b2BOrderStatusPushRequest);
                    if (response.isError()) {
                        flag = false;
                        baseResponse.setResult(0);
                        baseResponse.setFailCode(response.getFailCode());
                        baseResponse.setFailReason(response.getFailReason());
                    }
                }
            } else {
                if (ChannelEnum.DHUB.key.equals(orderPO.getChannelCode())) {
                    if (StrUtilX.isNotEmpty(orderPO.getChannelOrderCode())) {
                        B2BOrderStatusPushRequest b2BOrderStatusPushRequest = new B2BOrderStatusPushRequest();
                        b2BOrderStatusPushRequest.setCoOrderCode(orderPO.getChannelOrderCode());
                        b2BOrderStatusPushRequest.setFcOrderCode(orderPO.getOrderCode());
                        if (orderPO.getOrderConfirmationStatus() == OrderStatusEnum.CANCELED.no) {
                            b2BOrderStatusPushRequest.setOrderStatus(6);
                            b2BOrderStatusPushRequest.setCurrency(SettlementCurrencyEnum.getCodeByKey(String.valueOf(orderPO.getSaleCurrency())));
                            b2BOrderStatusPushRequest.setPenaltiesValue(orderPO.getRefundFee());

                            //判断是否有取消申请单
                            List<OrderRequestDTO> orderRequests = orderMapper.queryOrderRequest(orderPO.getId());

                            int isRefused = 0;
                            //确认取消单时，状态会自动改成已取消。判断是否拒单，就得有取消申请，并且同意该申请
                            if (orderPO.getOrderConfirmationStatus() == 2 && !CollUtilX.isNotEmpty(orderRequests)) {
                                isRefused = 1;
                            } else if (orderPO.getOrderConfirmationStatus() == 2 && CollUtilX.isNotEmpty(orderRequests)) {
                                isRefused = 1;
                                for (OrderRequestDTO orderRequestDTO : orderRequests) {
                                    if (orderRequestDTO.getRequestType() == 0 && orderRequestDTO.getHandledResult() == 1) {
                                        isRefused = 0;
                                        break;
                                    }
                                }
                            }
                            if (isRefused == 1) {
                                b2BOrderStatusPushRequest.setOrderStatus(4);
                            }
                        } else {
                            b2BOrderStatusPushRequest.setOrderStatus(7);
                        }
//                        if (Objects.equals(request.getSupplyResult(), 2)) {
//                            b2BOrderStatusPushRequest.setOrderStatus(6);
//                        } else if (Objects.equals(request.getSupplyResult(), 5)) {
//                            b2BOrderStatusPushRequest.setOrderStatus(7);
//                        }
                        b2BOrderStatusPushRequest.setMessage(null != orderExtendPO ? orderExtendPO.getCancelledReason() : null);
                        b2BOrderStatusPushRequest.setHotelConfirmNo(null != orderExtendPO ? orderExtendPO.getConfirmationCode() : null);
                        b2BOrderStatusPushRequest.setAgentCode(orderPO.getAgentCode());
                        b2BOrderStatusPushRequest.setPartnerCode(orderPO.getPartnerCode());
                        b2BOrderStatusPushRequest.setCancellationPrice(refundFee);
                        Response<Object> response = orderStatusPush(b2BOrderStatusPushRequest);
                        if (response.isError()) {
                            flag = false;
                            baseResponse.setResult(0);
                            baseResponse.setFailCode(response.getFailCode());
                            baseResponse.setFailReason(response.getFailReason());
                        }
                    }
                }
            }
        } catch (Exception e) {
            flag = false;
            log.error("推送订单状态失败", e);
            return Response.error(ErrorCodeEnum.PUSH_ORDER_STATUS_ERROR.errorCode, ErrorCodeEnum.PUSH_ORDER_STATUS_ERROR.errorDesc);
        } finally {
            String content;
            if (flag) {
                content = "推送订单状态：" + ConfirmationStatusEnum.getValueByKey(orderPO.getOrderConfirmationStatus());
            } else {
                content = "推送订单状态失败";
            }
            // 2024-07-24 author:湫 失败时状态才为待确认异常单 否则是正常单
            orderCommonService.saveOrderLog(
                    request.getOrderId(),
                    request.getOperator(),
                    request.getOrderOwnerName(),
                    orderPO.getOrderCode(),
                    content, flag ? null : 0);
        }
        return baseResponse;
    }

    @Override
    @Transactional
    public void modifyOrderRoomPrice(ModifyRoomPriceDTO request) {
        log.info("modifyOrderRoomPrice param: {}", JSON.toJSON(request));
        StringBuilder logSb = new StringBuilder();
        OrderPO orderPO = orderMapper.selectByPrimaryKey(request.getOrderId());
        BigDecimal salePriceTotalAmt = BigDecimal.ZERO;

        boolean isChange = false;
        OrderProductPricePO orderProductPriceQuery = new OrderProductPricePO();
        orderProductPriceQuery.setOrderId(request.getOrderId());
        List<OrderProductPricePO> orderProductPricePOList = orderProductPriceMapper.select(orderProductPriceQuery);
        //根据房间，日期价格，修改房费
        for (ModifyRoomDetailDTO room : request.getRoomList()) {
            StringBuilder info = new StringBuilder();
            info.append("房间").append(room.getRoomNumber()).append(":订单房费");
            boolean flag = false;
            for (ModifyPriceDetailDTO price : room.getPriceList()) {
                for (OrderProductPricePO orderProductPricePO : orderProductPricePOList) {
                    if (Objects.equals(orderProductPricePO.getRoomNumber(), room.getRoomNumber()) &&
                            DateUtilX.dateToString(orderProductPricePO.getSaleDate()).equals(price.getSaleDate())) {
                        if (price.getRoomPrice().compareTo(orderProductPricePO.getRoomPrice()) != 0) {
                            info.append(",").append(price.getSaleDate()).append("改为").append(price.getRoomPrice());
                            orderProductPricePO.setSalePrice(orderProductPricePO.getSalePrice().subtract(orderProductPricePO.getRoomPrice()).add(price.getRoomPrice()));
                            orderProductPricePO.setRoomPrice(price.getRoomPrice());
                            orderProductPriceMapper.updateByPrimaryKeySelective(orderProductPricePO);
                            flag = true;
                        }
                        salePriceTotalAmt = salePriceTotalAmt.add(orderProductPricePO.getSalePrice());
                        break;
                    }
                }
            }
            if (flag) {
                isChange = true;
                logSb.append(info).append(";");
            }
        }
        if (!isChange) {
            return;
        }

        BigDecimal changeAmt = salePriceTotalAmt.subtract(orderPO.getSalePrice());
        OrderPO orderUpdate = new OrderPO();
        orderUpdate.setId(request.getOrderId());
        if (!orderPO.getOrderConfirmationStatus().equals(OrderStatusEnum.CANCELED.no) && !PayMethodEnum.PAY.key.equals(orderPO.getPayMethod())) {//非取消状态并且非现付，才修改订单金额和利润
            log.info("修改订单价格，订单为取消状态，订单Id：{}", orderPO.getId());
            orderUpdate.setOrderAmt(orderPO.getOrderAmt().add(changeAmt).stripTrailingZeros());
            orderUpdate.setProfit(orderPO.getProfit().add(changeAmt));

            if (orderPO.getSalePrice().compareTo(salePriceTotalAmt) != 0) {
                logSb.append("订单总额由").append(orderPO.getOrderAmt().stripTrailingZeros().toPlainString())
                        .append("改为").append(orderPO.getOrderAmt().add(changeAmt).stripTrailingZeros().toPlainString());
            }
        }
        orderUpdate.setSalePrice(salePriceTotalAmt);
        orderMapper.updateByPrimaryKeySelective(orderUpdate);

        if (changeAmt.compareTo(BigDecimal.ZERO) != 0 && !orderPO.getOrderConfirmationStatus().equals(OrderStatusEnum.CANCELED.no)) {//非取消状态，才修改订单结算金额和状态等
            //更新订单未结算金额和结算状态
            OrderFinancePO orderFinanceQuery = new OrderFinancePO();
            orderFinanceQuery.setOrderId(request.getOrderId());
            OrderFinancePO orderFinancePO = orderFinanceMapper.selectOne(orderFinanceQuery);
            OrderFinancePO orderFinanceUpdate = new OrderFinancePO();
            orderFinanceUpdate.setId(orderFinancePO.getId());
            // 现付订单不修改未收金额
            if (PayMethodEnum.PAY.key.equals(orderPO.getPayMethod())) {
                orderFinanceUpdate.setUnreceivedAmt(orderFinancePO.getUnreceivedAmt());
            } else {
                orderFinanceUpdate.setUnreceivedAmt(orderFinancePO.getUnreceivedAmt().add(changeAmt));
            }
            if (BigDecimal.ZERO.compareTo(orderFinanceUpdate.getUnreceivedAmt()) == 0) {
                orderFinanceUpdate.setSettlementStatus(1);
            } else {
                orderFinanceUpdate.setSettlementStatus(0);
            }
            //更新对账状态
            if (orderFinancePO.getCheckStatus() != CheckStatusEnum.CHECKING.key) {
                if (BigDecimal.ZERO.compareTo(orderFinanceUpdate.getUnreceivedAmt()) == 0) {
                    //如果未收金额为0，则改为已对账
                    orderFinanceUpdate.setCheckStatus(CheckStatusEnum.CANNOT_CHECK.key);
                } else {
                    //如果未收金额不为0，则改为可出账
                    orderFinanceUpdate.setCheckStatus(CheckStatusEnum.CAN_CHECK.key);
                }
            }
            orderFinanceMapper.updateByPrimaryKeySelective(orderFinanceUpdate);

            if (orderPO.getSettlementType() != SettlementTypeEnum.SINGLE.key && !PayMethodEnum.PAY.key.equals(orderPO.getPayMethod())) {

                // 非单结订单扣退额度，非取消状态，就扣退额度
                // TODO: 2019/7/12 调分销商信用账户明细接口，查询已挂账金额
                AgentCreditLineDTO agentCreditLineDTO = new AgentCreditLineDTO();
                if (StrUtilX.isNotEmpty(orderPO.getSubtractLineAccount())) {
                    agentCreditLineDTO.setAgentCode(orderPO.getSubtractLineAccount());
                } else {
                    agentCreditLineDTO.setAgentCode(orderPO.getAgentCode());
                }
                agentCreditLineDTO.setOrderCode(orderPO.getOrderCode());
                //原订单金额减去修改后金额等于扣退额度i
                agentCreditLineDTO.setDeductRefundCreditLine(orderPO.getOrderAmt().subtract(orderPO.getOrderAmt().add(changeAmt)));
                Response<AgentCreditLineResultDTO> creditLineResponse = agentRemote.modifyDeductRefundCreditLine(Collections.singletonList(agentCreditLineDTO));
                if (!creditLineResponse.isSuccess()) {
                    throw new SysException(creditLineResponse.getFailCode(), creditLineResponse.getFailReason());
                }
            }
        }

        //记日志
        orderCommonService.saveOrderLog(
                request.getOrderId(),
                request.getOperator(),
                request.getOrderOwnerName(),
                orderPO.getOrderCode(),
                logSb.toString()
        );

        try {
            //加入到统计报表队列中
            stringRedisTemplate.convertAndSend(RedisKey.REPORT_STATISTICS, orderPO.getOrderCode());
        } catch (Exception e) {
            log.error("加入到统计报表队列异常,error:", e);
        }
    }

    @Override
    public void modifyOrderGuaranteeStatus(ModifyOrderGuaranteeStatusDTO request) {
        if (request.getOrderId() == null) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_ORDERID);
        }
        if (request.getGuaranteeFlag() == null) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_STATUS);
        }
        OrderPO orderUpdate = new OrderPO();
        orderUpdate.setId(request.getOrderId());
        orderUpdate.setGuaranteeFlag(request.getGuaranteeFlag());
        orderUpdate.setUpdatedBy(request.getOperator());
        orderUpdate.setUpdatedDt(DateUtilX.dateToString(new Date(), hour_format));
        orderMapper.updateByPrimaryKeySelective(orderUpdate);
    }

    @Override
    public void calculateHotelHeatRoomNightScoreTask(String param) {
        List<String> hotelIds;
        if (StrUtilX.isNotEmpty(param)) {
            hotelIds = StrUtilX.stringToList(param, ",");
        } else {
            if (!RedisTemplateX.hasKey(RedisKey.CALCULATE_ROOM_NIGHT_SCORE_HOTEL_ID_KEY)) {
                return;
            }
            hotelIds = RedisTemplateX.setPopCount(RedisKey.CALCULATE_ROOM_NIGHT_SCORE_HOTEL_ID_KEY, settingsConstant.getConsumerCalculateRoomNightScoreCount());
        }
        if (CollUtilX.isEmpty(hotelIds)) {
            return;
        }
        try {
            HotelHeatRoomNightReq roomNightReq = new HotelHeatRoomNightReq();
            List<Long> collect = hotelIds.stream().map(Long::parseLong).collect(Collectors.toList());
            roomNightReq.setHotelIds(collect);
            roomNightReq.setStartDate(DateUtilX.dateToString(DateUtilX.getDate(DateUtilX.getCurrentDate(), -1)));
            roomNightReq.setEndDate(DateUtilX.dateToString(DateUtilX.getCurrentDate()));
            List<HotelHeatRoomNightDTO> dtoList = orderMapper.selectHotelRoomNightInfo(roomNightReq);
            if (CollUtilX.isEmpty(dtoList)) {
                return;
            }
            Date currentDate = DateUtilX.getCurrentDate();
            List<HotelHeatReq> reqList = dtoList.stream().map(item -> {
                HotelHeatReq hotelHeatReq = new HotelHeatReq();
                hotelHeatReq.setHotelId(item.getHotelId());
                if (item.getNightQty() != null) {
                    hotelHeatReq.setRoomNightScore(item.getNightQty().longValue());
                }
                hotelHeatReq.setUpdatedBy(Constant.SYSTEM);
                hotelHeatReq.setUpdatedDt(currentDate);
                return hotelHeatReq;
            }).collect(Collectors.toList());

            // 修改热度分数
            hotelHeatRemote.updateHotelHeatScore(reqList);
        } catch (Exception e) {
            log.error("计算酒店热度预订间夜分数任务异常", e);
            // 异常重新插入到缓存
            String[] ids = hotelIds.toArray(new String[0]);
            RedisTemplateX.setAdd(RedisKey.CALCULATE_ROOM_NIGHT_SCORE_HOTEL_ID_KEY, ids);
        }
    }

    /**
     * 推送订单状态 失败重试
     *
     * @param b2BOrderStatusPushRequest
     */
    private void orderStatusPushRetry(B2BOrderStatusPushRequest b2BOrderStatusPushRequest) {
        CompletableFuture.runAsync(() -> {
            try {
                disB2BRemote.orderStatusPush(b2BOrderStatusPushRequest);
            } catch (Exception e) {
                log.error("调用渠道取消订单结果异常 request:{}", JSONObject.toJSONString(b2BOrderStatusPushRequest), e);
            }
        });
    }

    /**
     * 异步发送订单确认函
     *
     * @param sendOrderConfirmationLetterDTO
     */
    private void asyncSendOrderConfirmation(SendOrderConfirmationLetterReqDTO sendOrderConfirmationLetterDTO) {
        CompletableFuture.runAsync(() -> {
            try {
                // 1. 查看订单确认函配置
                QueryAgentConfirmationLetterConfigReq queryAgentConfirmationLetterConfigReq = new QueryAgentConfirmationLetterConfigReq();
                queryAgentConfirmationLetterConfigReq.setAgentCode(sendOrderConfirmationLetterDTO.getAgentCode());

                Response<QueryAgentConfirmationLetterConfigResp> queryAgentConfirmationLetterConfigRespResponse = agentConfirmationLetterConfigRemote.queryConfirmationLetterConfig(queryAgentConfirmationLetterConfigReq);

                if (queryAgentConfirmationLetterConfigRespResponse.isSuccess()
                        && queryAgentConfirmationLetterConfigRespResponse.getModel() != null
                        && queryAgentConfirmationLetterConfigRespResponse.getModel().getAuotoFlag() == 1) {
                    QueryAgentConfirmationLetterConfigResp model = queryAgentConfirmationLetterConfigRespResponse.getModel();
                    sendOrderConfirmationLetterDTO.setLanguage(model.getLanguage());
                    sendOrderConfirmationLetterDTO.setPriceShow(model.getPriceShow());
                    sendOrderConfirmationLetterDTO.setAnnixCarry(model.getAnnexCarry());
                    sendOrderConfirmationLetterDTO.setCarbonCopy(model.getCarbonCopy());
                    sendOrderConfirmationLetterDTO.setBlindCarbonCopy(model.getBlindCarbonCopy());
                    sendOrderConfirmationLetterDTO.setLogoUrl(model.getLogoUrl());
                    sendOrderConfirmationLetterDTO.setAgentPhone(model.getAgentPhone());
                    try {
                        // 2. 发送确认函
                        boolean result = sendOrderConfirmation(sendOrderConfirmationLetterDTO);

                        //记日志
                        orderCommonService.saveOrderLog(
                                sendOrderConfirmationLetterDTO.getId(),
                                "system",
                                "system",
                                sendOrderConfirmationLetterDTO.getOrderCode(),
                                "自动发送邮件确认函" + (result ? "成功" : "失败")
                        );
                    } catch (Exception e) {
                        log.error("自动发送订单确认函失败", e);
                        //记日志
                        orderCommonService.saveOrderLog(
                                sendOrderConfirmationLetterDTO.getId(),
                                "system",
                                "system",
                                sendOrderConfirmationLetterDTO.getOrderCode(),
                                "自动发送邮件确认函失败:系统异常"
                        );
                    } finally {

                    }
                }
            } catch (Exception e) {
                log.error("调用渠道取消订单结果异常 request:{}", e);
            }
        });
    }

    /**
     * 推送订单状态 不触发重试
     *
     * @param b2BOrderStatusPushRequest
     */
    private Response<Object> orderStatusPush(B2BOrderStatusPushRequest b2BOrderStatusPushRequest) {
        b2BOrderStatusPushRequest.setRetry(false);
        return disB2BRemote.orderStatusPush(b2BOrderStatusPushRequest);
    }


    @Override
    @Transactional
    public void addOrderPayDetailList(AddOrderPayDTO request) {
        Example example = new Example(OrderPO.class);
        if (!StringUtils.isEmpty(request.getFcOrderCode())) {
            example.createCriteria().andEqualTo("orderCode", request.getFcOrderCode()).andEqualTo("agentCode", request.getAgentCode()).andEqualTo("channelCode", request.getChannelCode());
        } else if (!StringUtils.isEmpty(request.getOrderCode())) {
            example.createCriteria().andEqualTo("channelOrderCode", request.getOrderCode()).andEqualTo("agentCode", request.getAgentCode()).andEqualTo("channelCode", request.getChannelCode());
        } else {
            throw new SysException(ErrorCodeEnum.ORDER_CODE_OR_FC_ORDER_CODE_NOT_EMPTY.errorCode, ErrorCodeEnum.ORDER_CODE_OR_FC_ORDER_CODE_NOT_EMPTY.errorDesc);
        }
        List<OrderPO> orderPOS = orderMapper.selectByExample(example);
        OrderPO order = null;
        if (orderPOS != null && orderPOS.size() > 0) {
            order = orderPOS.get(0);
        } else {
            throw new SysException(ErrorCodeEnum.ORDER_NOT_EXISTS);
        }

        if (request.getPayType() == 1 && (order.getOrderConfirmationStatus() == ConfirmationStatusEnum.UNCONFIRM.key || order.getPayStatus() == PayStatusEnum.NONE.getNo())) {
            throw new SysException(ErrorCodeEnum.ORDER_NOT_REFUND.errorCode, ErrorCodeEnum.ORDER_NOT_REFUND.errorDesc);
        }

        // 新增支付明细
        for (AddOrderPayDetailDTO addOrderPayDetailDTO : request.getPayDetailList()) {
            OrderPayDetailPO orderPayDetailPO = ProductSalePriceConvert.INSTANCE.orderPayDetailPOConvert(addOrderPayDetailDTO);
            orderPayDetailPO.setAgentCode(order.getAgentCode());
            orderPayDetailPO.setCreatedBy(order.getAgentName());
            orderPayDetailPO.setOrderCode(order.getOrderCode());
            orderPayDetailPO.setCreatedDt(DateUtilX.getDateTimeStr());
            orderPayDetailPO.setOrderId(order.getId());
            orderPayDetailMapper.insert(orderPayDetailPO);

            // 判断订单为已确认，且为个人支付，则走自助结算逻辑
            if (addOrderPayDetailDTO.getPartnerPayMethod() == 2) {
                if (addOrderPayDetailDTO.getPayType() == 0 && (order.getOrderConfirmationStatus().equals(ConfirmationStatusEnum.CONFIRMED.key) || order.getOrderConfirmationStatus().equals(ConfirmationStatusEnum.DONE.key))) {
                    CreateOrderStatementTaskDTO orderStatementTask = ProductSalePriceConvert.INSTANCE.orderStatementTaskConvert(orderPayDetailPO);
                    orderStatementTask.setTaskSource(orderPayDetailPO.getPayType());
                    orderStatementTask.setSerialNo(orderPayDetailPO.getPaySerialNo());
                    orderStatementTask.setAgentName(order.getAgentName());
                    orderStatementTask.setPayer(addOrderPayDetailDTO.getPayAccount());
                    orderStatementTask.setChangeTime(order.getCreatedDt());
                    orderStatementTask.setOrderCode(order.getOrderCode());
                    orderStatementTask.setReceiveAmt(orderPayDetailPO.getPersonPayAmount());
                    agentStatementRemote.createOrderStatementTask(orderStatementTask);
                } else if (addOrderPayDetailDTO.getPayType() == 1) {
                    CreateOrderStatementTaskDTO orderStatementTask = ProductSalePriceConvert.INSTANCE.orderStatementTaskConvert(orderPayDetailPO);
                    orderStatementTask.setTaskSource(orderPayDetailPO.getPayType());
                    orderStatementTask.setSerialNo(orderPayDetailPO.getPaySerialNo());
                    orderStatementTask.setAgentName(order.getAgentName());
                    orderStatementTask.setReceiver(addOrderPayDetailDTO.getPayAccount());
                    orderStatementTask.setChangeTime(order.getCreatedDt());
                    orderStatementTask.setOrderCode(order.getOrderCode());
                    orderStatementTask.setReceiveAmt(orderPayDetailPO.getPersonPayAmount());
                    agentStatementRemote.createOrderStatementTask(orderStatementTask);
                }
            }
        }

        // 更新订单标签
        OrderPO orderUpdate = new OrderPO();
        orderUpdate.setId(order.getId());
        orderUpdate.setIsMixedPay(request.getIsMixedPay());
        orderUpdate.setUpdatedDt(DateUtilX.dateToString(new Date(), hour_format));
        orderMapper.updateByPrimaryKeySelective(orderUpdate);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modifyAdditionalCharges(List<AdditionalChargesDTO> dtoList) {
        try {
            long l1 = System.currentTimeMillis();
            if (CollUtilX.isEmpty(dtoList)) {
                return;
            }
            String operator = dtoList.get(0).getUpdatedBy();
            Set<String> orderCodes = new HashSet<>();
            Map<String, BigDecimal> orderAdditionalPriceMap = new HashMap<>();
            for (AdditionalChargesDTO additionalChargesDTO : dtoList) {
                orderCodes.add(additionalChargesDTO.getOrderCode());
                orderAdditionalPriceMap.put(additionalChargesDTO.getOrderCode(), additionalChargesDTO.getAdditionalCharges());
            }
            // 订单附加费
            List<OrderAdditionalChargesPO> orderAdditionalChargesList = orderAdditionalChargesMapper.selectList(new QueryWrapper<OrderAdditionalChargesPO>().lambda()
                    .in(OrderAdditionalChargesPO::getOrderCode, orderCodes)
                    .eq(OrderAdditionalChargesPO::getAdditionalChargesType, AdditionalChargesTypeEnum.SETTLEMENT_COST.key));
            long l2 = System.currentTimeMillis();
            log.info("修改附加费，查询订单附加费耗时={}", l2 - l1);

            // 获得订单信息
            Example orderExample = new Example(OrderPO.class);
            orderExample.createCriteria().andIn("orderCode", orderCodes);
            List<OrderPO> orderPOList = orderMapper.selectByExample(orderExample);
            Map<Integer, OrderPO> orderInfoMap = new HashMap<>();
            for (OrderPO orderPO : orderPOList) {
                orderInfoMap.put(orderPO.getId(), orderPO);
            }

            long l3 = System.currentTimeMillis();
            Map<String, OrderAdditionalChargesPO> orderAdditionalChargesMap = new HashMap<>();
            if (CollUtilX.isNotEmpty(orderAdditionalChargesList)) {
                for (OrderAdditionalChargesPO orderAdditionalChargesPO : orderAdditionalChargesList) {
                    orderAdditionalChargesMap.put(orderAdditionalChargesPO.getOrderCode(), orderAdditionalChargesPO);
                }
            }
            Date currentDate = DateUtilX.getCurrentDate();
            String currentDateStr = DateUtilX.dateToString(currentDate, hour_format);
            List<OrderAdditionalChargesPO> updates = new ArrayList<>();
            List<OrderAdditionalChargesPO> adds = new ArrayList<>();
            Map<Integer, String> logMap = new HashMap<>();
            Map<Integer, String> orderMap = new HashMap<>();
            for (AdditionalChargesDTO additionalChargesDTO : dtoList) {
                String content = "修改附加项(结算成本)：订单(" + additionalChargesDTO.getOrderCode() + ")附加费";
                // 判断数据是否存在
                if (orderAdditionalChargesMap.containsKey(additionalChargesDTO.getOrderCode())) {
                    OrderAdditionalChargesPO orderAdditionalChargesPO = orderAdditionalChargesMap.get(additionalChargesDTO.getOrderCode());
                    OrderAdditionalChargesPO updatePo = new OrderAdditionalChargesPO();
                    updatePo.setId(orderAdditionalChargesPO.getId());
                    updatePo.setAdditionalCharges(CommonTgUtils.formatBigDecimal(orderAdditionalChargesPO.getAdditionalCharges()).add(CommonTgUtils.formatBigDecimal(additionalChargesDTO.getAdditionalCharges())));
                    updatePo.setUpdatedBy(operator);
                    updatePo.setUpdatedDt(currentDate);
                    updates.add(updatePo);
                    content = content + "由" + CommonTgUtils.formatBigDecimal(orderAdditionalChargesPO.getAdditionalCharges()) + "变为" + additionalChargesDTO.getAdditionalCharges();
                } else {
                    String groupNumber = NumberUtil.generateNumber(17);
                    OrderAdditionalChargesPO addPo = OrderAdditionalChargesPO.builder()
                            .groupNumber(groupNumber)
                            .additionalChargesType(AdditionalChargesTypeEnum.SETTLEMENT_COST.key)
                            .additionalChargesName("结算成本")
                            .orderId(additionalChargesDTO.getOrderId())
                            .orderCode(additionalChargesDTO.getOrderCode())
                            .additionalChargesDate(orderInfoMap.get(additionalChargesDTO.getOrderId()) != null ? orderInfoMap.get(additionalChargesDTO.getOrderId()).getStartDate() : null)
                            .quantity(1)
                            .additionalCharges(additionalChargesDTO.getAdditionalCharges())
                            .createdBy(operator)
                            .createdDt(currentDate)
                            .build();
                    adds.add(addPo);
                    content = content + "由0变为" + additionalChargesDTO.getAdditionalCharges();
                }
                logMap.put(additionalChargesDTO.getOrderId(), content);
                orderMap.put(additionalChargesDTO.getOrderId(), additionalChargesDTO.getOrderCode());
            }
            // 修改
            if (CollUtilX.isNotEmpty(updates)) {
                orderAdditionalChargesService.updateBatchById(updates);
            }
            // 新增
            if (CollUtilX.isNotEmpty(adds)) {
                orderAdditionalChargesService.saveBatch(adds);
            }
            long l4 = System.currentTimeMillis();
            log.info("修改附加费，新增或修改附加费耗时={}", l4 - l3);

            long l5 = System.currentTimeMillis();

            Example orderFinanceExample = new Example(OrderPO.class);
            orderFinanceExample.createCriteria().andIn("orderCode", orderCodes);
            List<OrderFinancePO> orderFinancePOList = orderFinanceMapper.selectByExample(orderFinanceExample);
            Map<String, OrderFinancePO> orderFinanceMap = new HashMap<>();
            for (OrderFinancePO orderFinancePO : orderFinancePOList) {
                orderFinancePO.setFinanceLockStatus(1);
                orderFinanceMap.put(orderFinancePO.getOrderCode(), orderFinancePO);
            }
            long l6 = System.currentTimeMillis();
            log.info("修改附加费，查询订单和订单财务信息耗时={}", l6 - l5);

            long l7 = System.currentTimeMillis();
            List<OrderPO> updateOrderPOList = new ArrayList<>();
            List<AgentCreditLineDTO> agentCreditLineDTOList = new ArrayList<>();
            for (OrderPO orderPO : orderPOList) {
                BigDecimal price = orderAdditionalPriceMap.get(orderPO.getOrderCode());
                OrderPO updateOrder = new OrderPO();
                updateOrder.setId(orderPO.getId());
                updateOrder.setOrderAmt(orderPO.getOrderAmt().add(price));
                updateOrder.setProfit(orderPO.getProfit().add(price));
                updateOrder.setAdditionalCharges(CommonTgUtils.formatBigDecimal(orderPO.getAdditionalCharges()).add(price));
                updateOrder.setUpdatedBy(operator);
                updateOrder.setUpdatedDt(currentDateStr);
                updateOrderPOList.add(updateOrder);
                if (logMap.containsKey(orderPO.getId())) {
                    logMap.put(orderPO.getId(), logMap.get(orderPO.getId()) + ";订单应收金额由" + orderPO.getOrderAmt() + "变为" + updateOrder.getOrderAmt());
                }

                // 非取消状态，才修改订单结算金额和状态等
                if (!orderPO.getOrderConfirmationStatus().equals(OrderStatusEnum.CANCELED.no)) {
                    OrderFinancePO orderFinancePO = orderFinanceMap.get(orderPO.getOrderCode());
                    if (orderFinancePO != null) {
                        OrderFinancePO orderFinanceUpdate = new OrderFinancePO();
                        orderFinanceUpdate.setId(orderFinancePO.getId());
                        orderFinanceUpdate.setUnreceivedAmt(updateOrder.getOrderAmt().subtract(orderFinancePO.getReceivedAmt() == null ? new BigDecimal(0) : orderFinancePO.getReceivedAmt()));
                        if (BigDecimal.ZERO.compareTo(orderFinanceUpdate.getUnreceivedAmt()) == 0) {
                            orderFinanceUpdate.setSettlementStatus(1);
                        } else {
                            orderFinanceUpdate.setSettlementStatus(0);
                        }

                        // 判断是否单结
                        if (orderPO.getSettlementType() != SettlementTypeEnum.SINGLE.key) {
                            // 非单结更新对账状态
                            if (!Objects.equals(orderFinancePO.getCheckStatus(), CheckStatusEnum.CHECKING.key)) {
                                if (BigDecimal.ZERO.compareTo(orderFinanceUpdate.getUnreceivedAmt()) == 0) {
                                    // 如果未收金额为0，则改为已对账
                                    orderFinanceUpdate.setCheckStatus(CheckStatusEnum.CANNOT_CHECK.key);
                                } else {
                                    // 如果未收金额不为0，则改为可出账
                                    orderFinanceUpdate.setCheckStatus(CheckStatusEnum.CAN_CHECK.key);
                                }
                            }
                        }

                        // 不是现付才退额度
                        if (!PayMethodEnum.PAY.key.equals(orderPO.getPayMethod())) {
                            // 非单结订单扣退额度
                            AgentCreditLineDTO agentCreditLineDTO = new AgentCreditLineDTO();
                            if (StrUtilX.isNotEmpty(orderPO.getSubtractLineAccount())) {
                                agentCreditLineDTO.setAgentCode(orderPO.getSubtractLineAccount());
                            } else {
                                agentCreditLineDTO.setAgentCode(orderPO.getAgentCode());
                            }
                            agentCreditLineDTO.setOrderCode(orderPO.getOrderCode());
                            agentCreditLineDTO.setDeductRefundCreditLine(price);
                            agentCreditLineDTOList.add(agentCreditLineDTO);
                        }

                        orderFinanceMap.put(orderFinancePO.getOrderCode(), orderFinanceUpdate);
                    }
                }
            }
            long l8 = System.currentTimeMillis();
            log.info("修改附加费，组装信息耗时={}", l8 - l7);

            // 修改应付金额
            long l9 = System.currentTimeMillis();
            if (CollUtilX.isNotEmpty(updateOrderPOList)) {
                orderMapper.batchUpOrder(updateOrderPOList);
            }
            long l10 = System.currentTimeMillis();
            log.info("修改附加费，修改应付金额耗时={}", l10 - l9);

            // 修改订单财务、出账状态
            long l11 = System.currentTimeMillis();
            if (CollUtilX.isNotEmpty(orderFinanceMap.values())) {
                List<OrderFinancePO> collect = new ArrayList<>(orderFinanceMap.values());
                orderFinanceMapper.batchUpOrderFinance(collect);
            }
            long l12 = System.currentTimeMillis();
            log.info("修改附加费，修改订单财务、出账状态耗时={}", l12 - l11);

            // 退额度
            long l13 = System.currentTimeMillis();
            if (CollUtilX.isNotEmpty(agentCreditLineDTOList)) {
                Response<AgentCreditLineResultDTO> creditLineResponse = agentRemote.modifyDeductRefundCreditLine(agentCreditLineDTOList);
                if (!creditLineResponse.isSuccess()) {
                    throw new SysException(creditLineResponse.getFailCode(), creditLineResponse.getFailReason());
                }
            }
            long l14 = System.currentTimeMillis();

            // 记日志
            if (logMap.size() > 0) {
                List<OrderLogPO> logPOList = new ArrayList<>();
                for (Map.Entry<Integer, String> entry : logMap.entrySet()) {
                    OrderLogPO orderLogPO = new OrderLogPO();
                    orderLogPO.setOrderId(entry.getKey());
                    orderLogPO.setCreatedBy("system");
                    orderLogPO.setCreatedDt(currentDateStr);
                    orderLogPO.setTarget(orderMap.get(entry.getKey()));
                    orderLogPO.setContent(entry.getValue());
                    logPOList.add(orderLogPO);
                }
                orderLogMapper.insertList(logPOList);
            }

            log.info("修改附加费，退额度耗时={}", l14 - l13);
            log.info("修改附加费，总耗时={}", l14 - l1);
        } catch (Exception e) {
            log.error("修改附加费异常", e);
            throw e;
        }
    }


    @Override
    public Response sendOrderConfirmationLetter(SendOrderConfirmationLetterReqDTO req) {
        try {
            // 1. 查询自动确认函配置
            QueryAgentConfirmationLetterConfigReq queryAgentConfirmationLetterConfigReq = new QueryAgentConfirmationLetterConfigReq();
            queryAgentConfirmationLetterConfigReq.setAgentCode(req.getAgentCode());
            Response<QueryAgentConfirmationLetterConfigResp> agentConfirmationLetterConfigResponse = agentConfirmationLetterConfigRemote.queryConfirmationLetterConfig(queryAgentConfirmationLetterConfigReq);
            if (agentConfirmationLetterConfigResponse.isSuccess() && agentConfirmationLetterConfigResponse.getModel() != null) {
                // 2. 组装发送确认函请求
                QueryAgentConfirmationLetterConfigResp model = agentConfirmationLetterConfigResponse.getModel();
                req.setLogoUrl(model.getLogoUrl());
                req.setBlindCarbonCopy(model.getBlindCarbonCopy());
                req.setCarbonCopy(model.getCarbonCopy());
                req.setAgentPhone(StringUtils.isEmpty(model.getAgentPhone()) ? "" : model.getAgentPhone());
            }
            // 发送确认函
            boolean result = sendOrderConfirmation(req);

            //记日志
            orderCommonService.saveOrderLog(
                    req.getId(),
                    "system",
                    "system",
                    req.getOrderCode(),
                    "自动发送邮件确认函" + (result ? "成功" : "失败")
            );
            if (result) {
                return Response.success(result);
            } else {
                return Response.error(ErrorCodeEnum.EMAIL_SEND_ERROR);
            }
        } catch (Exception e) {

            log.error("系统异常", e);
            orderCommonService.saveOrderLog(
                    req.getId(),
                    "system",
                    "system",
                    req.getOrderCode(),
                    "自动发送邮件确认函失败:系统异常"
            );
            return Response.error(ErrorCodeEnum.SYSTEM_EXCEPTION);
        }
    }

    @Override
    public void orderConfirmationLetterExport(SendOrderConfirmationLetterReqDTO req, HttpServletResponse resp) {
        // 1. 查询自动确认函配置
        QueryAgentConfirmationLetterConfigReq queryAgentConfirmationLetterConfigReq = new QueryAgentConfirmationLetterConfigReq();
        queryAgentConfirmationLetterConfigReq.setAgentCode(req.getAgentCode());
        Response<QueryAgentConfirmationLetterConfigResp> agentConfirmationLetterConfigResponse = agentConfirmationLetterConfigRemote.queryConfirmationLetterConfig(queryAgentConfirmationLetterConfigReq);
        if (agentConfirmationLetterConfigResponse.isSuccess() && agentConfirmationLetterConfigResponse.getModel() != null) {
            // 2. 组装发送确认函请求
            QueryAgentConfirmationLetterConfigResp model = agentConfirmationLetterConfigResponse.getModel();
            req.setLogoUrl(model.getLogoUrl());
            req.setBlindCarbonCopy(model.getBlindCarbonCopy());
            req.setCarbonCopy(model.getCarbonCopy());
            req.setAgentPhone(model.getAgentPhone());
        }
        //获取订单数据
        OrderDTO cnOrder = null;
        OrderDTO enOrder = null;
        HotelInfoCollectionDTO cnhotelDetailResp = null;
        HotelInfoCollectionDTO enhotelDetailResp = null;
        CompanyAddDTO companyAddDTO = new CompanyAddDTO();
        companyAddDTO.setCompanyCode(CompanyDTO.COMPANY_CODE);
        String customerTel = null;
        Response<CompanySelectDTO> companyResp = companyRemote.queryCompanyDetail(companyAddDTO);
        if (companyResp != null && companyResp.getModel() != null && StringUtils.isEmpty(req.getAgentPhone())) {
            customerTel = companyResp.getModel().getCustomerTel();
        } else {
            customerTel = req.getAgentPhone();
        }

        List<String> settings = new ArrayList<>();
        settings.add(BaseHotelInfoUrl.hotelTextPolicy);
        settings.add(BaseHotelInfoUrl.hotelName);
        settings.add(BaseHotelInfoUrl.tel);
        settings.add(BaseHotelInfoUrl.location);
        settings.add(BaseHotelInfoUrl.rooms);


        OrderCodeDTO orderCodeDTO = new OrderCodeDTO();
        orderCodeDTO.setOrderCode(req.getOrderCode());
        orderCodeDTO.setAgentCode(req.getAgentCode());
        orderCodeDTO.setLanguage(LanguageTypeEnum.en_US.getValue());
        enOrder = orderQueryService.queryOrderDetail(orderCodeDTO);
        HotelInfoCollectionReq infoReq = new HotelInfoCollectionReq();
        infoReq.setHotelIds(Collections.singletonList(enOrder.getHotelId()));
        infoReq.setLanguageType(LanguageTypeEnum.en_US.getValue());
        infoReq.setSettings(settings);
        Response<HotelInfoCollectionDTO> enHotelInfoResp = hotelProductRemote.queryHotelInfo(infoReq);
        if (enHotelInfoResp.isSuccess() && enHotelInfoResp.getModel() != null) {
            enhotelDetailResp = enHotelInfoResp.getModel();
        }

        orderCodeDTO.setLanguage(LanguageTypeEnum.zh_CN.getValue());
        cnOrder = orderQueryService.queryOrderDetail(orderCodeDTO);

        infoReq.setLanguageType(LanguageTypeEnum.zh_CN.getValue());
        Response<HotelInfoCollectionDTO> cnHotelInfoResp = hotelProductRemote.queryHotelInfo(infoReq);
        if (cnHotelInfoResp.isSuccess() && cnHotelInfoResp.getModel() != null) {
            cnhotelDetailResp = cnHotelInfoResp.getModel();
        }


        //获取数据
        Map<String, Object> dataModel = null;
        try {
            dataModel = getDataModel(req, cnOrder, enOrder, customerTel, cnhotelDetailResp, enhotelDetailResp);
        } catch (Exception e) {
            log.error("导出模板获取数据异常", e);
            throw new SysException(ErrorCodeEnum.FAIL);
        }
        //获取导出模板
        InputStream resourceAsStream = this.getClass().getClassLoader().getResourceAsStream(orderConfirmationLetterServer.getOrderConfirmationLetterTemplateFileName(req.getLanguage()));
        if (resourceAsStream == null) {
            log.error("资源文件未找到");
            throw new SysException("资源文件未找到");
        }
        try {
            orderConfirmationLetterServer.exportOrderConfirmationLetter(req, resp, cnOrder, enOrder, dataModel, resourceAsStream);
        } finally {
            if (ObjectUtil.isNotEmpty(resourceAsStream)) {
                try {
                    resourceAsStream.close();
                } catch (IOException e) {
                    log.error("资源关闭异常", e);
                }
            }
        }
    }

    /**
     * 发送确认函
     */
    private boolean sendOrderConfirmation(SendOrderConfirmationLetterReqDTO req) throws Exception {
        // 3.选择确认函
        String orderConfirmationLetterTemplateFileName = getOrderConfirmationLetterTemplateFileName(req);

        OrderDTO cnOrder = null;
        OrderDTO enOrder = null;
        HotelInfoCollectionDTO cnhotelDetailResp = null;
        HotelInfoCollectionDTO enhotelDetailResp = null;
        // 4. 获取订单明细数据

        CompanyAddDTO companyAddDTO = new CompanyAddDTO();
        companyAddDTO.setCompanyCode(CompanyDTO.COMPANY_CODE);
        String customerTel = null;
        Response<CompanySelectDTO> companyResp = companyRemote.queryCompanyDetail(companyAddDTO);
        if (companyResp != null && companyResp.getModel() != null && StringUtils.isEmpty(req.getAgentPhone())) {
            customerTel = companyResp.getModel().getCustomerTel();
        } else {
            customerTel = req.getAgentPhone();
        }

        List<String> settings = new ArrayList<>();
        settings.add(BaseHotelInfoUrl.hotelTextPolicy);
        settings.add(BaseHotelInfoUrl.hotelName);
        settings.add(BaseHotelInfoUrl.tel);
        settings.add(BaseHotelInfoUrl.location);
        settings.add(BaseHotelInfoUrl.rooms);

        String channelOrderCode = null;
        Long hotelId = null;
        if (req.getLanguage().equals(LanguageTypeEnum.zh_CN.getValue())) {
            OrderCodeDTO orderCodeDTO = new OrderCodeDTO();
            orderCodeDTO.setOrderCode(req.getOrderCode());
            orderCodeDTO.setAgentCode(req.getAgentCode());
            orderCodeDTO.setLanguage(req.getLanguage());
            cnOrder = orderQueryService.queryOrderDetail(orderCodeDTO);

            // 渠道订单号
            channelOrderCode = cnOrder.getChannelOrderCode();
            hotelId = cnOrder.getHotelId();
        } else if (req.getLanguage().equals(LanguageTypeEnum.en_US.getValue())) {
            OrderCodeDTO orderCodeDTO = new OrderCodeDTO();
            orderCodeDTO.setOrderCode(req.getOrderCode());
            orderCodeDTO.setAgentCode(req.getAgentCode());
            orderCodeDTO.setLanguage(req.getLanguage());
            enOrder = orderQueryService.queryOrderDetail(orderCodeDTO);

            // 渠道订单号
            channelOrderCode = enOrder.getChannelOrderCode();
            hotelId = enOrder.getHotelId();
        } else {
            OrderCodeDTO orderCodeDTO = new OrderCodeDTO();
            orderCodeDTO.setOrderCode(req.getOrderCode());
            orderCodeDTO.setAgentCode(req.getAgentCode());
            orderCodeDTO.setLanguage(LanguageTypeEnum.en_US.getValue());
            enOrder = orderQueryService.queryOrderDetail(orderCodeDTO);
            hotelId = enOrder.getHotelId();
            // 渠道订单号
            channelOrderCode = enOrder.getChannelOrderCode();
            orderCodeDTO.setLanguage(LanguageTypeEnum.zh_CN.getValue());
            cnOrder = orderQueryService.queryOrderDetail(orderCodeDTO);
        }

        String hotelNameCN = null;
        String hotelNameEN = null;
        HotelInfoCollectionReq infoReq = new HotelInfoCollectionReq();
        infoReq.setHotelIds(Collections.singletonList(hotelId));
        infoReq.setLanguageType(LanguageTypeEnum.en_US.getValue());
        infoReq.setSettings(settings);
        enhotelDetailResp = hotelProductRemote.queryHotelInfo(infoReq).getModel();
        hotelNameEN = enhotelDetailResp.getHotelName();

        infoReq.setLanguageType(LanguageTypeEnum.zh_CN.getValue());
        cnhotelDetailResp = hotelProductRemote.queryHotelInfo(infoReq).getModel();
        hotelNameCN = enhotelDetailResp.getHotelName();


        // 获取发送邮件内容
        String content = getContent(orderConfirmationLetterTemplateFileName, req, cnOrder, enOrder, customerTel, cnhotelDetailResp, enhotelDetailResp);
        SendEmailReq sendEmailReq = new SendEmailReq();
        StringBuilder preorderEmail = new StringBuilder();
        // 存在主收件人
        if (StrUtil.isNotBlank(req.getEmail())) {
            preorderEmail.append(req.getEmail());
        } else if (StrUtil.isNotBlank(req.getCarbonCopy())) {
            // 不存在则设置抄送人为 主收件人 抄送人必须存在
            preorderEmail.append(req.getCarbonCopy().split(",")[0]);
        } else {
            // 同时为空，则直接结束
            return false;
        }
        sendEmailReq.setCcEmail(req.getCarbonCopy());
        sendEmailReq.setEmail(preorderEmail.toString());
        sendEmailReq.setType(1);
        sendEmailReq.setTopic("订单确认函" + "(" + hotelNameCN + hotelNameEN + ")" + channelOrderCode);
        sendEmailReq.setContent(content);
        sendEmailReq.setBccEmail(req.getBlindCarbonCopy());
        // 发送邮件
        return sendCodeUtil.sendEmail(sendEmailReq, req.getAnnixCarry());
    }

    /**
     * 获取邮件内容
     */
    private String getContent(String templateFileName, SendOrderConfirmationLetterReqDTO sendOrderConfirmationLetterDTO, OrderDTO cnOrder, OrderDTO enOrder, String customerTel, HotelInfoCollectionDTO cnhotelDetailResp, HotelInfoCollectionDTO enhotelDetailResp) throws Exception {
        Configuration cfg = new Configuration();
        cfg.setClassForTemplateLoading(SupplyOrderServer.class, "/orderconfirmationlettertemplates"); // 模板位置
        cfg.setDefaultEncoding("UTF-8");
        Template template = cfg.getTemplate(templateFileName);
        Map<String, Object> dataModel = getDataModel(sendOrderConfirmationLetterDTO, cnOrder, enOrder, customerTel, cnhotelDetailResp, enhotelDetailResp);
        StringWriter writer = new StringWriter();
        template.process(dataModel, writer);
        return writer.toString(); // 生成的 HTML 内容
    }

    /**
     * 获取数据模型
     */
    private Map<String, Object> getDataModel(SendOrderConfirmationLetterReqDTO sendOrderConfirmationLetterDTO, OrderDTO cnOrder, OrderDTO enOrder, String customerTel, HotelInfoCollectionDTO cnhotelDetailResp, HotelInfoCollectionDTO enhotelDetailResp) throws Exception {
        Map<String, Object> dataModel = new HashMap<>();
        dataModel.put("priceShow", sendOrderConfirmationLetterDTO.getPriceShow());
        dataModel.put("customerTel", customerTel);
        Map<String, CodeInfo> hotelPolicyMap = new HashMap<>();
        if (sendOrderConfirmationLetterDTO.getLanguage().equals(LanguageTypeEnum.zh_CN.getValue())) {
            dataModel(hotelPolicyMap, dataModel, cnOrder, "CN", cnhotelDetailResp, sendOrderConfirmationLetterDTO.getLanguage());
        } else if (sendOrderConfirmationLetterDTO.getLanguage().equals(LanguageTypeEnum.en_US.getValue())) {
            dataModel(hotelPolicyMap, dataModel, enOrder, "EN", enhotelDetailResp, sendOrderConfirmationLetterDTO.getLanguage());
        } else {
            dataModel(hotelPolicyMap, dataModel, enOrder, "EN", enhotelDetailResp, sendOrderConfirmationLetterDTO.getLanguage());
            dataModel(hotelPolicyMap, dataModel, cnOrder, "CN", cnhotelDetailResp, sendOrderConfirmationLetterDTO.getLanguage());
        }
        dataModel.put("logoUrl", sendOrderConfirmationLetterDTO.getLogoUrl());
        dataModel.put("hotelPolicy", hotelPolicyMap);
        return dataModel;
    }

    /**
     * 获取数据模型
     */
    private void dataModel(Map<String, CodeInfo> hotelPolicyMap, Map<String, Object> dataModel, OrderDTO order, String language, HotelInfoCollectionDTO hotelInfo, String baseLanguage) throws Exception {
        dataModel.put("hotelName" + language, StrUtil.isNotBlank(hotelInfo.getHotelName()) ? hotelInfo.getHotelName() : order.getHotelName());
        dataModel.put("hotelPhone", hotelInfo.getTel());
        LocationDTO location = hotelInfo.getLocation();
        if (location != null) {
            dataModel.put("hotelAddress" + language, location.getAddress());
            dataModel.put("provinceName" + language, location.getProvinceName());
            dataModel.put("countryName" + language, location.getCountryName());
            dataModel.put("cityName" + language, location.getCityName());
        }

        // 获取描述
        List<HotelTextPolicyDTO> array = hotelInfo.getHotelTextPolicyList();
        if (CollUtil.isNotEmpty(array)) {
            for (HotelTextPolicyDTO hotelPolicyDTO : array) {
                CodeInfo codeInfo = hotelPolicyMap.get(hotelPolicyDTO.getCode());
                if (codeInfo == null) {
                    codeInfo = new CodeInfo();
                }
                if (language.equals("CN")) {
                    codeInfo.setCntext(hotelPolicyDTO.getText());
                } else {
                    codeInfo.setEntext(hotelPolicyDTO.getText());
                }
                hotelPolicyMap.put(hotelPolicyDTO.getCode(), codeInfo);
            }
        }
        TaxDTO totalTax = order.getTotalTax();

        BigDecimal total = BigDecimal.ZERO;
        if (totalTax != null) {
            BigDecimal tax = totalTax.getTax();
            if (tax != null) {
                total = total.add(tax);
            }
            BigDecimal salesTax = totalTax.getSalesTax();
            if (salesTax != null) {
                total = total.add(salesTax);
            }
            BigDecimal extraTaxFee = totalTax.getExtraTaxFee();
            if (extraTaxFee != null) {
                total = total.add(extraTaxFee);
            }
        }
        if (total.compareTo(BigDecimal.ZERO) != 0) {
            dataModel.put("totalTax", total);
        }

        dataModel.put("productLabel", order.getProductLabel());
        dataModel.put("payMethod", order.getPayMethod());
        List<OrderRoomGuestDTO> roomGuestList = order.getRoomGuestList();
        Map<String, OrderRoomGuestDTO> roomGuestMap = new HashMap<>();
        for (OrderRoomGuestDTO orderRoomGuestDTO : roomGuestList) {
            if (orderRoomGuestDTO.getRoomNumber() != null) {
                roomGuestMap.put(String.valueOf(orderRoomGuestDTO.getRoomNumber()), orderRoomGuestDTO);
            }
        }
        List<OrderFeeDTO> feeList = order.getFeeList();
        for (OrderFeeDTO orderFeeDTO : feeList) {
            if (orderFeeDTO.getRoomNumber() != null) {
                OrderRoomGuestDTO orderRoomGuestDTO = roomGuestMap.get(String.valueOf(orderFeeDTO.getRoomNumber()));
                if (orderRoomGuestDTO != null) {
                    orderFeeDTO.setAdultQty(orderRoomGuestDTO.getAdultQty());
                    orderFeeDTO.setChildrenQty(orderRoomGuestDTO.getChildrenQty());
                }
            }
        }

        dataModel.put("feeList", feeList);
        dataModel.put("orderCode", order.getOrderCode());
        dataModel.put("confirmationCode", order.getConfirmationCode());
        dataModel.put("checklInDate", DateUtilX.dateToString(DateUtilX.stringToDate(order.getStartDate()), "yyyy/MM/dd"));
        dataModel.put("checklOutDate", DateUtilX.dateToString(DateUtilX.stringToDate(order.getEndDate()), "yyyy/MM/dd"));
        dataModel.put("earliestArriveTime", order.getEarliestArriveTime());
        dataModel.put("latestArriveTime", order.getLatestArriveTime());
        if (language.equals("CN")) {
            dataModel.put("startDate" + language, order.getStartDate());
            dataModel.put("endDate" + language, order.getEndDate());
            dataModel.put("checkInWeek" + language, DateUtilX.getWeekStrOfDate(DateUtilX.stringToDate(order.getStartDate())));
            dataModel.put("checkOutWeek" + language, DateUtilX.getWeekStrOfDate(DateUtilX.stringToDate(order.getEndDate())));
            // 早餐 中文
            dataModel.put("breakFast" + language, BreakFastEnum.getDescByKey(order.getBreakfastQty()));


            if (null != order.getCancellationTerm() && !order.getCancellationTerm().equals("无")) {
                List<String> cancellation = CancellationConvertUtil.convertCancellationToCn(order.getCancellationTerm());
                dataModel.put("cancellationTermLast" + language, cancellation.size() > 1 ? cancellation.get(cancellation.size() - 2) : cancellation.get(0));
                dataModel.put("cancellationTerm" + language, cancellation);
            }

        } else {
            dataModel.put("startDate" + language, convertDateEnStr(order.getStartDate()));
            dataModel.put("endDate" + language, convertDateEnStr(order.getEndDate()));
            dataModel.put("checkInWeek" + language, DateUtilX.getWeekEnStrOfDate(DateUtilX.stringToDate(order.getStartDate())));
            dataModel.put("checkOutWeek" + language, DateUtilX.getWeekEnStrOfDate(DateUtilX.stringToDate(order.getEndDate())));
            // 早餐英文
            dataModel.put("breakFast" + language, BreakFastEnum.getEnDescByKey(order.getBreakfastQty()));


            if (null != order.getCancellationTerm() && !order.getCancellationTerm().equals("无")) {
                List<String> cancellation = CancellationConvertUtil.convertCancellationToEn(order.getCancellationTerm(), true);
                dataModel.put("cancellationTermLast" + language, cancellation.size() > 1 ? cancellation.get(cancellation.size() - 2) : cancellation.get(0));
                dataModel.put("cancellationTerm" + language, cancellation);
            }
        }

        dataModel.put("roomQty", order.getRoomQty());
        dataModel.put("nightQty", order.getNightQty());
        List<RoomtypeDTO> rooms = hotelInfo.getRooms();
        String roomName = null;
        if (CollUtilX.isNotEmpty(rooms)) {
            for (RoomtypeDTO room : rooms) {
                Long roomtypeId = room.getRoomtypeId();
                String roomtypeName = room.getRoomtypeName();
                if (roomtypeId != null && order.getRoomId() != null && roomtypeId.equals(order.getRoomId().longValue()) && StrUtil.isNotBlank(roomtypeName)) {
                    roomName = roomtypeName;
                    break;
                }
            }
        }
        if (StrUtil.isBlank(roomName)) {
            roomName = order.getRoomName();
        }
        dataModel.put("roomName" + language, roomName);
        dataModel.put("productName", order.getProductName());
        dataModel.put("roomGuestList", order.getRoomGuestList());
        dataModel.put("feeList", order.getFeeList());
        dataModel.put("contactEmail", order.getContactEmail());
        dataModel.put("contactPhone", order.getContactPhone());
        if (StrUtil.isNotBlank(order.getContactCountryCode())) {
            dataModel.put("contactCountryCode", "+" + order.getContactCountryCode());
        }
        dataModel.put("contactName", order.getContactName());
        dataModel.put("saleCurrencyCode", SettlementCurrencyEnum.getCodeByKey(order.getSaleCurrency().toString()));
        String bedTypeStr = order.getBedType();
        StringBuilder sbSpecialRequest = new StringBuilder();
        if (StrUtil.isNotBlank(bedTypeStr)) {
            try {
                JSONObject jsonObject1 = JSON.parseObject(bedTypeStr);
                String bedInfosStr = jsonObject1.getString("bedInfos");
                if (StrUtil.isNotBlank(bedInfosStr)) {
                    List<JSONObject> jarray = JSON.parseArray(bedInfosStr, JSONObject.class);

                    ArrayList<String> languageList = new ArrayList<>();

                    //循环语言 床型 页面拼接展示
                    if (baseLanguage.equals(LanguageTypeEnum.zh_CN.getValue())) {
                        languageList.add(LanguageTypeEnum.zh_CN.getValue());
                    } else if (baseLanguage.equals(LanguageTypeEnum.en_US.getValue())) {
                        languageList.add(LanguageTypeEnum.en_US.getValue());
                    } else {
                        languageList.add(LanguageTypeEnum.zh_CN.getValue());
                        languageList.add(LanguageTypeEnum.en_US.getValue());
                    }
                    // 此循环代码非必要 勿动！！ 根据语言循环拼接展示前端数据 一点都别动！！！！！！！！
                    for (int k = 0; k < languageList.size(); k++) {
                        String templanguage = languageList.get(k);

                        Integer i = 0;
                        if (k == 1) {
                            sbSpecialRequest.append("(");
                        }
                        for (JSONObject object : jarray) {
                            i++;
                            String bedTypeCode = object.getString("bedTypeCode");
                            String bedNum = object.getString("bedNum");
                            if (StrUtil.isNotBlank(bedTypeCode)) {
                                if (templanguage.equals(LanguageTypeEnum.zh_CN.getValue())) {
                                    sbSpecialRequest.append(bedNum).append("张");
                                    sbSpecialRequest.append(bedNameConfig.getBedTypeName(bedTypeCode, LanguageTypeEnum.zh_CN.getValue()));
                                    if (i < jarray.size()) {
                                        sbSpecialRequest.append(" 和 ");
                                    }

                                } else {
                                    sbSpecialRequest.append(bedNum);
                                    sbSpecialRequest.append(bedNameConfig.getBedTypeName(bedTypeCode, LanguageTypeEnum.en_US.getValue()));
                                    if (i < jarray.size()) {
                                        sbSpecialRequest.append(" and ");
                                    }

                                }
                            }
                        }
                        if (k == 1) {
                            sbSpecialRequest.append(")");
                        }
                    }
                }
            } catch (Exception e) {
            }
        }

        if (StrUtil.isNotBlank(order.getSpecialRequest())) {
            if (sbSpecialRequest.length() != 0) {
                sbSpecialRequest.append(",");
            }
            sbSpecialRequest.append(order.getSpecialRequest());
        }


        dataModel.put("specialRequest", sbSpecialRequest.toString());
        dataModel.put("orderAmt", order.getOrderAmt());
        dataModel.put("salePrice", order.getSalePrice());

        dataModel.put("payAtHotelFee", order.getPayAtHotelFee());

        dataModel.put("payAtHotelAgentCurrencyFee", order.getPayAtHotelAgentCurrencyFee());

        if (order.getPayAtHotelCurrency() != null) {
            dataModel.put("payAtHotelCurrency", SettlementCurrencyEnum.getCodeByKey(order.getPayAtHotelCurrency().toString()));
        }
        if (order.getPayAtHotelCurrency() != null) {
            dataModel.put("payMethod", order.getPayMethod());
        }
    }

    /**
     * 获取确认函模板 文件名称
     */
    private static String convertDateEnStr(String oldDate) throws Exception {
        SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd"); // 输入格式
        SimpleDateFormat outputFormat = new SimpleDateFormat("MMMM dd, yyyy", Locale.ENGLISH); // 输出格式
        Date date = inputFormat.parse(oldDate); // 将字符串解析为 Date 对象
        return outputFormat.format(date); // 格式化为新的字符串
    }

    /**
     * 获取确认函模板 文件名称
     *
     * @param req
     */
    private String getOrderConfirmationLetterTemplateFileName(SendOrderConfirmationLetterReqDTO req) {
        // 根据配置获取确认函模板文件并返回
        if (req.getLanguage().equals(LanguageTypeEnum.zh_CN.getValue())) {
            // 中文+价格模板
            return "priceConfirmationTemplate.ftl";
        } else if (req.getLanguage().equals(LanguageTypeEnum.en_US.getValue())) {
            // 英文+价格模板
            return "priceConfirmationTemplateEn.ftl";
        } else {
            // 中英双语+价格模板
            return "bilingualPriceConfirmationTemplate.ftl";
        }
    }

}
