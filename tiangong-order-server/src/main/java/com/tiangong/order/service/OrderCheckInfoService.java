package com.tiangong.order.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.dto.order.request.OrderCheckInfoRequest;
import com.tiangong.order.domain.OrderCheckInfoEntity;
import com.tiangong.order.domain.OrderCheckPushInfoPO;
import com.tiangong.order.dto.OrderCheckPushInfoDTO;
import com.tiangong.order.remote.dto.OrderCheckInfoDTO;

import java.util.List;

/**
 * 订单入住信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-10-21 11:26:35
 */
public interface OrderCheckInfoService extends IService<OrderCheckInfoEntity> {

    /**
     * 订单入住信息新增或更新
     */
    int saveOrUpdateOrderCheckInfo(List<OrderCheckInfoDTO> orderCheckInfoDTOList, String operator, boolean isSync);

    /**
     * 订单入住信息列表,按订单编号查询
     */
    List<OrderCheckInfoDTO> orderCheckInfoList(String orderCode);

    /**
     * 通知分销商
     */
    void notifyAgent(String orderCode);

    /**
     * 根据订单号查询入住明细
     */
    List<OrderCheckInfoDTO> queryOrderCheckDetail(String orderCode);

    /**
     * 根据订单号查询入住明细
     */
    List<OrderCheckInfoDTO> queryOrderCheckDetailDhub(OrderCheckInfoRequest orderCheckInfoRequest);

    /**
     * 查询入住明细推送列表
     *
     * @return
     */
    PaginationSupportDTO<OrderCheckPushInfoPO> queryOrderCheckPushInfoPage(OrderCheckPushInfoDTO orderCheckPushInfoDTO);

}

