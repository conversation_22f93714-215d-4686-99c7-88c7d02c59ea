package com.tiangong.order.service;

import com.tiangong.common.Response;
import com.tiangong.order.remote.dto.SendOrderConfirmationLetterReqDTO;
import com.tiangong.order.remote.response.OrderDTO;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.Map;

/**
 * 订单确认函服务接口
 */
public interface OrderConfirmationLetterServer {
    /**
     * 导出订单确认函
     *
     * @param reqDTO              导出订单确认函请求DTO
     * @param response            HTTP响应对象
     * @param orderDTOCN          中文订单数据
     * @param orderDTOEn          英文订单数据
     * @param dataModel           数据
     * @param templateInputStream 导出模板流
     */
    void exportOrderConfirmationLetter(SendOrderConfirmationLetterReqDTO reqDTO, HttpServletResponse response
            , OrderDTO orderDTOCN, OrderDTO orderDTOEn, Map<String, Object> dataModel, InputStream templateInputStream);

    /**
     * 获取导出订单确认函模板
     *
     * @param language
     * @return
     */
    String getOrderConfirmationLetterTemplateFileName(String language);

    /**
     * 构建数据模型
     *
     * @param hotelPolicyMap  酒店政策Map
     * @param dataModel       数据模型
     * @param order           订单信息
     * @param language        语言标识
     * @param hotelDetailResp 酒店详情
     * @param baseLanguage    基础语言
     * @throws Exception 异常信息
     */
    void dataModel(Map<String, Object> hotelPolicyMap, Map<String, Object> dataModel,
                   OrderDTO order, String language, Response<String> hotelDetailResp,
                   String baseLanguage) throws Exception;
}
