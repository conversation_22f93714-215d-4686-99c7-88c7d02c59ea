package com.tiangong.order.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.tiangong.common.Response;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.order.domain.OrderFilePO;
import com.tiangong.order.domain.req.AddOrderFileReq;
import com.tiangong.order.domain.req.DelOrderFileReq;
import com.tiangong.order.domain.req.QueryOrderFilePageReq;
import com.tiangong.order.domain.req.UploadOrderFileReq;
import com.tiangong.order.domain.resp.UploadOrderFileResp;
import com.tiangong.order.dto.OrderFileDTO;

import java.util.List;

public interface OrderFileService extends IService<OrderFilePO> {

    /**
     * 新增文件
     * @param req
     * @return
     */
    Response<String> addOrderFile(AddOrderFileReq req);

    /**
     * 查询订单文件
     * @param orderCode
     * @return
     */
    PaginationSupportDTO<OrderFileDTO> queryOrderFileByOrderCode(QueryOrderFilePageReq req);

    /**
     * 删除订单文件
     * @param req
     */
    void delOrderFile(DelOrderFileReq req);

    /**
     * 上传订单文件
     * @param req
     * @return
     */
    Response<Object> uploadOrderFile(UploadOrderFileReq req);
}
