package com.tiangong.order.service;

import com.tiangong.common.Response;
import com.tiangong.order.remote.request.CancelOrderWorkOrderDTO;
import com.tiangong.order.remote.request.ConfirmOrderWorkOrderDTO;

public interface OrderFinanceHandle {

    /**
     * 确认工单
     */
    void confirmOrderWorkOrder(ConfirmOrderWorkOrderDTO request);

    /**
     * 取消工单
     */
    void cancelOrderWorkOrder(CancelOrderWorkOrderDTO request);
}
