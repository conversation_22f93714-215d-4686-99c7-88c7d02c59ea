package com.tiangong.order.mq;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.tiangong.common.Response;
import com.tiangong.constanct.MqConstants;
import com.tiangong.order.dto.OrderInvoiceDownloadDTO;
import com.tiangong.order.service.PersonInvoiceService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.common.consumer.ConsumeFromWhere;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.MessageModel;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.apache.rocketmq.spring.core.RocketMQPushConsumerLifecycleListener;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.UUID;

/**
 * 订单发票自动下载消息监听器
 *
 * <AUTHOR>
 * @date 2024/05/13
 */
@Slf4j
@Service(OrderInvoiceMQListenerBase.ORDER_INVOICE_DOWNLOAD_MQ_LISTENER)
public class OrderInvoiceDownloadMQListener implements OrderInvoiceMQListenerBase {

    @Autowired
    private PersonInvoiceService personInvoiceService;

    @Override
    public void onMessage(MessageExt messageExt) {
        // 生成请求ID
        String requestId = UUID.randomUUID().toString();
        
        try {
            // 解析消息
            String messageBody = new String(messageExt.getBody());
            OrderInvoiceDownloadDTO downloadDTO = JSON.parseObject(messageBody, OrderInvoiceDownloadDTO.class);
            
            // 获取发票信息
            Integer invoiceId = downloadDTO.getInvoiceId();
            String orderCode = downloadDTO.getOrderCode();
            String invoiceNo = downloadDTO.getInvoiceNo();
            String messageId = downloadDTO.getMessageId();
            
            log.info("收到订单发票自动下载消息，订单号：{}，发票号：{}，消息ID：{}，请求ID：{}", 
                    orderCode, invoiceNo, messageId, requestId);
            
            // 调用下载接口
            Response<String> response = personInvoiceService.downLoadAutoOpenInvoicePdf(invoiceId, "system");

            if (response.isSuccess() && StrUtil.isNotEmpty(response.getModel())) {
                log.info("订单发票自动下载成功，订单号：{}，发票号：{}，发票URL：{}，请求ID：{}", 
                        orderCode, invoiceNo, response.getModel(), requestId);
            } else {
                log.warn("订单发票自动下载失败，订单号：{}，发票号：{}，请求ID：{}", 
                        orderCode, invoiceNo, requestId);
            }
            
        } catch (Exception e) {
            log.error("处理订单发票自动下载消息异常，请求ID：{}", requestId, e);
        }
    }

}
