package com.tiangong.hdp.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @create 2023/11/3 16:05
 */
@Getter
@AllArgsConstructor
public enum ErrorEnum {

    LOGIN_TYPE_ERROR("90001", "CENTER_LOGIN_TYPE_ERROR", "登录类型错误"),
    AGENT_IS_DISABLE("90002", "CENTER_AGENT_IS_DISABLE", "客户被禁用"),
    CODE_IS_NOT_NULL("90003", "EMPTY_PARAM_VERIFICATIONCODE", "验证码不能为空"),
    USER_ACCOUNT_IS_NOT_EXIST("90004", "CENTER_USER_ACCOUNT_IS_NOT_EXIST", "账号不存在"),
    USER_PHONE_IS_NOT_EXIST("90004", "CENTER_USER_PHONE_IS_NOT_EXIST", "手机号不存在"),
    ACCOUNT_PASSWORD_IS_ERROR("90005", "CENTER_ACCOUNT_PASSWORD_IS_ERROR", "账号或密码错误"),
    USER_IS_LOCK("90006", "CENTER_USER_IS_LOCK", "很抱歉，由于您多次输入错误的密码，您的账号已被暂时锁定。"),
    CODE_IS_NOT_EXIST("90007", "CENTER_CODE_IS_NOT_EXIST", "验证码已失效,请重新获取验证码"),
    CODE_IS_ERROR("90008", "CENTER_CODE_IS_ERROR", "验证码错误"),
    CODE_IS_LIMIT("90009", "CENTER_CODE_IS_LIMIT", "获取验证码过于频繁，请1分钟后在试"),
    AGENT_CODE_IS_ERROR("90010", "CENTER_AGENT_CODE_IS_ERROR", "请先选择代订客户"),
    USER_ACCOUNT_IS_ALREADY("90011", "CENTER_USER_ACCOUNT_IS_ALREADY", "账号已存在"),
    USER_IS_NOT_AUTH("90012", "CENTER_USER_IS_NOT_AUTH", "当前账号无权限"),
    AGENT_CODE_IS_NOT_NULL("90013", "EMPTY_PARAM_AGENTCODE", "客户编码不能为空"),
    USER_ID_IS_NOT_NULL("90014", "EMPTY_PARAM_USERID", "用户id不能为空"),
    ADMIN_ACCOUNT_IS_NOT_DEL("90015", "CENTER_ADMIN_ACCOUNT_IS_NOT_DEL", "超级管理员不能删除"),
    ADMIN_ACCOUNT_IS_NOT_UPDATE("90016", "CENTER_ADMIN_ACCOUNT_IS_NOT_UPDATE", "超级管理员不支持修改"),
    NOT_AUDIT_UPDATE("90018", "CENTER_NOT_AUDIT_UPDATE", "没有修改权限"),
    PASSWORD_IS_ERROR("90019", "CENTER_ORIGINAL_PASSWORD_IS_ERROR", "原密码错误"),
    HOTEL_INFO_IS_ERROR("90020", "CENTER_HOTEL_INFO_IS_ERROR", "获取酒店信息异常"),
    PRE_BOOK_ERROR("90021", "CENTER_PRE_BOOK_ERROR", "试预订失败"),
    OLD_USER_TEL_ERROR("90023", "CENTER_OLD_USER_TEL_ERROR", "原手机号不存在"),
    NOT_USER_SELF("90024", "CENTER_NOT_USER_SELF", "非本人自己操作，不允许修改"),
    OLD_USER_TEL_EQUALS_NEW_USER_TEL("90025", "CENTER_OLD_USER_TEL_EQUALS_NEW_USER_TEL", "原手机号和新手机号一致"),
    USER_TEL_IS_ALREADY("90026", "CENTER_USER_TEL_IS_ALREADY", "修改的手机号已存在"),
    CANCEL_ORDER_ERROR("90027", "CENTER_CANCEL_ORDER_ERROR", "取消订单失败"),
    ROOM_IS_EMPTY("90028", "CENTER_ROOM_IS_EMPTY", "房型信息为空"),
    HOTEL_PRICE_IS_ERROR("90030", "CENTER_HOTEL_PRICE_IS_ERROR", "获取酒店起价失败"),
    QUERY_ORDER_IS_ERROR("90031", "CENTER_QUERY_ORDER_ERROR", "订单查询异常"),
    DOWNLOAD_ORDER_VOUCHER_IS_ERROR("90032", "CENTER_DOWNLOAD_ORDER_VOUCHER_IS_ERROR", "生成pdf文件异常"),
    SEND_ORDER_VOUCHER_IS_ERROR("90033", "CENTER_SEND_ORDER_VOUCHER_IS_ERROR", "发送电子邮件异常"),
    PRODUCT_IS_SALE("90034", "CENTER_PRODUCT_IS_SALE", "房型已售罄"),
    CREATE_ORDER_IS_ERROR("90035", "CENTER_CREATE_ORDER_IS_ERROR", "创建订单失败"),
    PASSWORD_IS_SAME("90036", "CENTER_PASSWORD_IS_SAME", "密码不能设置为原来密码"),
    ORDER_NOT_VOUCHER("90037", "CENTER_ORDER_NOT_VOUCHER", "当前订单不符合生成, 订单凭证"),
    AGENT_CODE_ERROR("90038", "CENTER_AGENT_CODE_ERROR", "客户异常或已被禁用"),
    SEND_CODE_IS_ERROR("90039", "CENTER_SEND_CODE_IS_ERROR", "发送短信失败"),
    ACCOUNT_IS_DISABLE("90041", "CENTER_ACCOUNT_IS_DISABLE", "账号已被禁用"),
    PHONE_IS_DISABLE("90042", "CENTER_PHONE_IS_DISABLE", "手机号已被禁用"),
    USER_PHONE_IS_ALREADY("90043", "CENTER_USER_PHONE_IS_ALREADY", "手机号已存在"),
    ;

    public final String code;
    public final String type;
    public final String content;
}
