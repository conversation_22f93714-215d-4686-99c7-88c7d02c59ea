package com.tiangong.hdp.convert;

import com.tiangong.entity.vo.hotel.PriceDetailItemDto;
import com.tiangong.entity.vo.hotel.PriceItemDto;
import com.tiangong.entity.vo.hotel.ProductDetailDto;
import com.tiangong.entity.vo.hotel.RoomItemDto;
import com.tiangong.hdp.controller.admin.product.vo.ProductResp;
import com.tiangong.hdp.controller.admin.product.vo.ProductRespVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface ProductConvert {

    ProductConvert INSTANCE = Mappers.getMapper(ProductConvert.class);

    ProductResp productRespConvert(ProductDetailDto product);

    List<ProductRespVO> productDetailRespConvert(List<RoomItemDto> productItem);

    PriceDetailItemDto priceDetailItemDtoConvert(PriceItemDto priceItem);
}