package com.tiangong.hdp.service.user;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tiangong.cloud.common.domain.LoginUser;
import com.tiangong.cloud.commonbean.config.security.TokenManager;
import com.tiangong.common.Response;
import com.tiangong.dto.common.CompanyDTO;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.enums.AvailableEnum;
import com.tiangong.enums.EmployeeAccountStatusEnum;
import com.tiangong.enums.UserTypeEnum;
import com.tiangong.exception.SysException;
import com.tiangong.hdp.config.SettingsConstant;
import com.tiangong.hdp.controller.admin.user.vo.*;
import com.tiangong.hdp.convert.UserConvert;
import com.tiangong.hdp.domain.po.UserPO;
import com.tiangong.hdp.domain.resp.LoginResp;
import com.tiangong.hdp.domain.resp.UserListResp;
import com.tiangong.hdp.enums.ErrorEnum;
import com.tiangong.hdp.enums.LoginTypeEnum;
import com.tiangong.hdp.mapper.AgentMapper;
import com.tiangong.hdp.mapper.UserMapper;
import com.tiangong.hdp.util.ValidatedFilter;
import com.tiangong.keys.RedisKey;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.sensitive.EncryptUtil;
import com.tiangong.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

@Slf4j
@Service
public class UserServiceImpl implements UserService {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private AgentMapper agentMapper;

    @Autowired
    private TokenManager tokenManager;

    @Autowired
    private SettingsConstant settingsConstant;

    @Override
    public Response<Map<String, Object>> login(HttpServletRequest request, LoginReqVO req) throws Exception {
        // 判断用户是否存在
        UserPO one = userMapper.selectOne(new LambdaQueryWrapper<UserPO>()
                .eq(UserPO::getUserAccount, req.getUserAccount())
                .or()
                .eq(UserPO::getUserTel, EncryptUtil.encrypt(req.getUserAccount()))
                .last("limit 1")
        );
        if (one == null) {
            return Response.error(ErrorEnum.USER_ACCOUNT_IS_NOT_EXIST.type, ErrorEnum.USER_ACCOUNT_IS_NOT_EXIST.content);
        }
        if (one.getAccountStatus() != EmployeeAccountStatusEnum.VALID.no || one.getAvailableStatus().intValue() != AvailableEnum.Start.key.intValue() || one.getActive() != 1) {
            return Response.error(ErrorEnum.ACCOUNT_IS_DISABLE.type, ErrorEnum.ACCOUNT_IS_DISABLE.content);
        }

        // 判断客户是否被禁用
        if (!CompanyDTO.COMPANY_CODE.equals(one.getOrgCode())) {
            String agentStatusByAgentCode = agentMapper.getAgentStatusByAgentCode(one.getOrgCode());
            if (agentStatusByAgentCode == null) {
                return Response.error(ErrorEnum.AGENT_IS_DISABLE.type, ErrorEnum.AGENT_IS_DISABLE.content);
            }
        }

        // 判断用户是否锁定了
        String value = RedisTemplateX.get(RedisKey.USER_IS_LOCK.concat(one.getUserTel()));
        if (value != null) {
            return Response.error(ErrorEnum.USER_IS_LOCK.type, ErrorEnum.USER_IS_LOCK.content);
        }

        // 区分密码登录和验证码登录
        if (req.getLoginType() == LoginTypeEnum.PASSWORD_LOGIN.type) {
            if (StrUtilX.isEmpty(req.getUserPassword())) {
                return Response.error(ErrorEnum.ACCOUNT_PASSWORD_IS_ERROR.type, ErrorEnum.ACCOUNT_PASSWORD_IS_ERROR.content);
            }

            // 校验密码
            String encryptUserPassword = SM4Utils.encrypt(req.getUserPassword(), Sm4O.defaultKey);
            if (!one.getUserPwd().equals(encryptUserPassword)) {
                // 记录密码错误次数--统一使用用户手机号来存储
                ValidatedFilter.loginFailRecord(one.getUserTel());

                return Response.error(ErrorEnum.ACCOUNT_PASSWORD_IS_ERROR.type, ErrorEnum.ACCOUNT_PASSWORD_IS_ERROR.content);
            }

        } else if (req.getLoginType() == LoginTypeEnum.CODE_LOGIN.type) {
            if (StrUtilX.isEmpty(req.getVerificationCode())) {
                return Response.error(ErrorEnum.CODE_IS_NOT_NULL.type, ErrorEnum.CODE_IS_NOT_NULL.content);
            }

            // 校验验证码
            String code = RedisTemplateX.get(RedisKey.CODE_KEY.concat(req.getUserAccount()));
            if (code == null) {
                return Response.error(ErrorEnum.CODE_IS_NOT_EXIST.type, ErrorEnum.CODE_IS_NOT_EXIST.content);
            }
            if (!code.equals(req.getVerificationCode())) {
                // 记录验证码错误次数--统一使用用户手机号来存储
                ValidatedFilter.loginFailRecord(one.getUserTel());

                return Response.error(ErrorEnum.CODE_IS_ERROR.type, ErrorEnum.CODE_IS_ERROR.content);
            }

            // 验证通过，清除验证码
            RedisTemplateX.delete(RedisKey.CODE_KEY.concat(req.getUserAccount()));
        } else {
            return Response.error(ErrorEnum.LOGIN_TYPE_ERROR.type, ErrorEnum.LOGIN_TYPE_ERROR.content);
        }

        // 登录成功
        LoginResp loginResp = userMapper.queryUserLogin(one.getUserAccount());

        // 判断用户身份
        if (CompanyDTO.COMPANY_CODE.equals(loginResp.getAgentCode())) {
            loginResp.setUserType(UserTypeEnum.SUPER_ADMIN.type);
        } else if (loginResp.getIsSuperAdmin() != null && loginResp.getIsSuperAdmin() == 1) {
            loginResp.setUserType(UserTypeEnum.ADMIN.type);
        } else {
            loginResp.setUserType(UserTypeEnum.NORMAL.type);
        }

        // 创建用户基础信息
        LoginUser loginUser = new LoginUser();
        loginUser.setUserId(loginResp.getUserId());
        loginUser.setUserName(loginResp.getUserName());
        loginUser.setUserAccount(loginResp.getUserAccount());
        loginUser.setCompanyCode(CompanyDTO.COMPANY_CODE);
        loginUser.setIsSuperAdmin(loginResp.getIsSuperAdmin());
        loginUser.setUserType(loginResp.getUserType());

        String user = JSON.toJSONString(loginUser);
        // 生成令牌
        String token = tokenManager.createToken(user);

        // 没有用到roles，存ip
        if (settingsConstant.getAccountToken() == 0) {
            String result = RedisTemplateX.get(StrUtilX.concat(ValidatedFilter.MULTI_USER_LOGIN_ACCOUNT, one.getUserAccount()));
            RedisTemplateX.delete(result);
        }
        RedisTemplateX.setAndExpire(RedisKey.LOGIN_TOKEN + token, IpUtil.getIpAddress(request), tokenManager.getTokenTime());
        RedisTemplateX.setAndExpire(StrUtilX.concat(RedisKey.MULTI_USER_LOGIN, IpUtil.getIpAddress(request)), one.getUserAccount(), tokenManager.getTokenTime());
        RedisTemplateX.setAndExpire(StrUtilX.concat(ValidatedFilter.MULTI_USER_LOGIN_ACCOUNT, one.getUserAccount()), token, tokenManager.getTokenTime());

        Map<String, Object> authInfo = new LinkedHashMap<String, Object>(2) {{
            put("token", token);
            put("user", loginResp);
        }};

        // 登录成功，上次密码错误次数
        RedisTemplateX.delete(RedisKey.PASSWORD_ERROR_NUM.concat(one.getUserTel()));
        return Response.success(authInfo);
    }

    @Override
    public PaginationSupportDTO<UserListResp> userPage(UserListReqVO reqVO) {
        LambdaQueryWrapper<UserPO> wrapper = new LambdaQueryWrapper<UserPO>().eq(UserPO::getOrgCode, reqVO.getAgentCode());
        if (StrUtilX.isNotEmpty(reqVO.getUserName())) {
            wrapper.like(UserPO::getUserName, reqVO.getUserName());
        }
        if (StrUtilX.isNotEmpty(reqVO.getUserAccount())) {
            wrapper.eq(UserPO::getUserAccount, reqVO.getUserAccount());
        }
        if (reqVO.getAvailableStatus() != null) {
            wrapper.eq(UserPO::getAvailableStatus, reqVO.getAvailableStatus());
        }
        if (reqVO.getIsSuperAdmin() != null) {
            wrapper.eq(UserPO::getIsSuperAdmin, reqVO.getIsSuperAdmin());
        }
        wrapper.orderByDesc(UserPO::getIsSuperAdmin);
        wrapper.orderByDesc(UserPO::getUserId);
        IPage<UserPO> iPage = new Page<>(reqVO.getCurrentPage(), reqVO.getPageSize());
        IPage<UserPO> page = userMapper.selectPage(iPage, wrapper);
        List<UserListResp> respList = new ArrayList<>();

        if (page.getRecords() != null && !page.getRecords().isEmpty()) {
            for (UserPO userPO : page.getRecords()) {
                UserListResp userListResp = UserConvert.INSTANCE.userListRespConvert(userPO);
                respList.add(userListResp);
            }
        }
        PaginationSupportDTO<UserListResp> paginationSupport = new PaginationSupportDTO<>();
        paginationSupport.setItemList(respList);
        paginationSupport.setPageSize((int) page.getSize());
        paginationSupport.setTotalCount(page.getTotal());
        paginationSupport.setTotalPage((int) page.getPages());
        paginationSupport.setCurrentPage((int) page.getCurrent());
        return paginationSupport;
    }

    @Override
    public void userAdd(AddOrUpdateUserReqVO reqVO) throws Exception {
        if (StrUtilX.isEmpty(reqVO.getAgentCode())) {
            throw new SysException(ErrorEnum.AGENT_CODE_IS_NOT_NULL.type, ErrorEnum.AGENT_CODE_IS_NOT_NULL.content);
        }
        // 判断是否有权限新增
        if (reqVO.getUserType().intValue() == UserTypeEnum.NORMAL.type.intValue()) {
            throw new SysException(ErrorEnum.USER_IS_NOT_AUTH.type, ErrorEnum.USER_IS_NOT_AUTH.content);
        }

        // 验重--先验重账号
        Boolean exist = userAccountExist(reqVO.getUserAccount(), reqVO.getUserId());
        if (exist) {
            // 在验重手机号
            exist = userAccountExist(reqVO.getUserTel(), reqVO.getUserId());
            if (!exist) {
                throw new SysException(ErrorEnum.USER_PHONE_IS_ALREADY.type, ErrorEnum.USER_PHONE_IS_ALREADY.content);
            }
        } else {
            throw new SysException(ErrorEnum.USER_ACCOUNT_IS_ALREADY.type, ErrorEnum.USER_ACCOUNT_IS_ALREADY.content);
        }
        UserPO userPO = UserConvert.INSTANCE.userPOConvert(reqVO);
        String password = SM4Utils.encrypt(CompanyDTO.PWD, Sm4O.defaultKey);
        // 邮箱用账号填充
        userPO.setEmail(userPO.getUserAccount());
        userPO.setUserPwd(password);
        userPO.setSignaturePwd(SM3Utils.encrypt(password));
        userPO.setUpdatePwdTime(new Date());
        userPO.setOrgCode(reqVO.getAgentCode());
        userPO.setIsSuperAdmin(0);
        userPO.setActive(1);
        userPO.setAccountStatus(EmployeeAccountStatusEnum.VALID.no);

        userMapper.insert(userPO);
    }

    @Override
    public void updateUser(AddOrUpdateUserReqVO req) throws Exception {
        if (req.getUserId() == null) {
            throw new SysException(ErrorEnum.USER_ID_IS_NOT_NULL.type, ErrorEnum.USER_ID_IS_NOT_NULL.content);
        }

        // 判断是否有权限修改
        if (req.getUserType().intValue() == UserTypeEnum.NORMAL.type.intValue()) {
            throw new SysException(ErrorEnum.USER_IS_NOT_AUTH.type, ErrorEnum.USER_IS_NOT_AUTH.content);
        }

        // 判断修改的是否是超级管理员
        UserPO byId = userMapper.selectById(req.getUserId());
        if (byId.getIsSuperAdmin() == 1) {
            throw new SysException(ErrorEnum.ADMIN_ACCOUNT_IS_NOT_UPDATE.type, ErrorEnum.ADMIN_ACCOUNT_IS_NOT_UPDATE.content);
        }
        // 验重--先验重账号
        Boolean exist = userAccountExist(req.getUserAccount(), req.getUserId());
        if (exist) {
            // 在验重手机号
            exist = userAccountExist(req.getUserTel(), req.getUserId());
            if (!exist) {
                throw new SysException(ErrorEnum.USER_TEL_IS_ALREADY.type, ErrorEnum.USER_TEL_IS_ALREADY.content);
            }
        } else {
            throw new SysException(ErrorEnum.USER_ACCOUNT_IS_ALREADY.type, ErrorEnum.USER_ACCOUNT_IS_ALREADY.content);
        }
        // 修改
        UserPO po = UserConvert.INSTANCE.userPOConvert(req);
        userMapper.updateById(po);
        if (req.getAvailableStatus().intValue() == AvailableEnum.End.key) {
            String token = RedisTemplateX.get(StrUtilX.concat(ValidatedFilter.MULTI_USER_LOGIN_ACCOUNT, byId.getUserAccount()));
            if (StrUtilX.isNotEmpty(token)) {
                RedisTemplateX.delete(token);
            }
        }
    }

    @Override
    public void delUser(DelUserReqVO req) {
        if (req.getUserId() == null) {
            throw new SysException(ErrorEnum.USER_ID_IS_NOT_NULL.type, ErrorEnum.USER_ID_IS_NOT_NULL.content);
        }
        // 判断是否有权限删除
        if (req.getUserType().intValue() == UserTypeEnum.NORMAL.type.intValue()) {
            throw new SysException(ErrorEnum.USER_IS_NOT_AUTH.type, ErrorEnum.USER_IS_NOT_AUTH.content);
        }

        // 判断删除的是否是超级管理员
        UserPO byId = userMapper.selectById(req.getUserId());
        if (byId.getIsSuperAdmin() == 1) {
            throw new SysException(ErrorEnum.ADMIN_ACCOUNT_IS_NOT_DEL.type, ErrorEnum.ADMIN_ACCOUNT_IS_NOT_DEL.content);
        }

        userMapper.deleteById(req.getUserId());
        String token = RedisTemplateX.get(StrUtilX.concat(ValidatedFilter.MULTI_USER_LOGIN_ACCOUNT, byId.getUserAccount()));
        if (StrUtilX.isEmpty(token)) {
            RedisTemplateX.delete(token);
        }
    }

    @Override
    public void resetPassword(ResetPasswordReqVO req) {
        // 判断是否有权限重置
        if (req.getUserType().intValue() == UserTypeEnum.NORMAL.type.intValue()) {
            throw new SysException(ErrorEnum.USER_IS_NOT_AUTH.type, ErrorEnum.USER_IS_NOT_AUTH.content);
        }
        // 判断修改的是否是超级管理员
        UserPO byId = userMapper.selectById(req.getUserId());
        if (byId.getIsSuperAdmin() == 1) {
            throw new SysException(ErrorEnum.ADMIN_ACCOUNT_IS_NOT_UPDATE.type, ErrorEnum.ADMIN_ACCOUNT_IS_NOT_UPDATE.content);
        }
        String password = SM4Utils.encrypt(CompanyDTO.PWD, Sm4O.defaultKey);
        UserPO userPO = new UserPO();
        userPO.setUserId(req.getUserId());
        userPO.setUserPwd(password);
        userPO.setSignaturePwd(SM3Utils.encrypt(password));
        userPO.setUpdatedBy(req.getUpdatedBy());
        userPO.setUpdatedDt(DateUtilX.getDateStr());
        userMapper.updateUser(userPO);
    }

    @Override
    public void updatePassword(UpdatePasswordReqVO req) {
        // 校验旧密码
        UserPO byId = userMapper.selectById(req.getUserId());
        String encryptUserPassword = SM4Utils.encrypt(req.getOldPassword(), Sm4O.defaultKey);
        if (!byId.getUserPwd().equals(encryptUserPassword)) {
            throw new SysException(ErrorEnum.PASSWORD_IS_ERROR.type, ErrorEnum.PASSWORD_IS_ERROR.content);
        }
        // 修改密码-新密码
        String newPassword = SM4Utils.encrypt(req.getNewPassword(), Sm4O.defaultKey);

        // 密码不能修改原来设置的密码
        if (byId.getUserPwd().equals(newPassword)) {
            throw new SysException(ErrorEnum.PASSWORD_IS_SAME.type, ErrorEnum.PASSWORD_IS_SAME.content);
        }

        UserPO userPO = new UserPO();
        userPO.setUserId(byId.getUserId());
        userPO.setUserPwd(newPassword);
        userPO.setSignaturePwd(SM3Utils.encrypt(newPassword));
        userMapper.updateUser(userPO);
    }

    @Override
    public UserInfoRespVO userInfo(UserInfoReqVO req) {
        UserInfoRespVO userInfo = userMapper.queryUserInfo(req.getUserId());
        // 判断用户身份
        if (CompanyDTO.COMPANY_CODE.equals(userInfo.getAgentCode())) {
            // 返回客户管理员数据
            userInfo = userMapper.queryUserInfoByAgentCode(req.getAgentCode());
            if (userInfo == null) {
                throw new SysException(ErrorEnum.AGENT_CODE_ERROR.type, ErrorEnum.AGENT_CODE_ERROR.content);
            }
            userInfo.setUserType(UserTypeEnum.SUPER_ADMIN.type);
        } else if (userInfo.getIsSuperAdmin() != null && userInfo.getIsSuperAdmin() == 1) {
            userInfo.setUserType(UserTypeEnum.ADMIN.type);
        } else {
            userInfo.setUserType(UserTypeEnum.NORMAL.type);
        }
        return userInfo;
    }

    @Override
    public Boolean userAccountExist(String userAccount, Integer userId) throws Exception {
        LambdaQueryWrapper<UserPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserPO::getUserAccount, userAccount)
                .or()
                .eq(UserPO::getUserTel, EncryptUtil.encrypt(userAccount));

        wrapper.last("limit 1");
        // 验重
        UserPO one = userMapper.selectOne(wrapper);
        return (one == null || userId != null) && (one == null || one.getUserId().intValue() == userId.intValue());
    }

    @Override
    public void loginBack(String token, String loginName, HttpServletRequest request) {
        if (StrUtilX.isNotEmpty(token)) {
            RedisTemplateX.delete(RedisKey.LOGIN_TOKEN + token);
            RedisTemplateX.delete(StrUtilX.concat(ValidatedFilter.MULTI_USER_LOGIN, IpUtil.getIpAddress(request)));
            RedisTemplateX.delete(StrUtilX.concat(ValidatedFilter.MULTI_USER_LOGIN_ACCOUNT, loginName));
        }
        RedisTemplateX.delete(token);
    }
}
