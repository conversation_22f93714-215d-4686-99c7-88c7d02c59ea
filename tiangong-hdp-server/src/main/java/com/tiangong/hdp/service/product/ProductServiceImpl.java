package com.tiangong.hdp.service.product;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.nacos.common.utils.StringUtils;
import com.tiangong.entity.request.hotel.HotelImageRequest;
import com.tiangong.entity.request.hotel.HotelInfoRequest;
import com.tiangong.entity.request.hotel.ProductDetailInfoRequest;
import com.tiangong.entity.response.hotel.HotelImageResponse;
import com.tiangong.entity.response.hotel.HotelInfoResponse;
import com.tiangong.entity.response.hotel.ProductDetailInfoResponse;
import com.tiangong.entity.response.order.PriceTaxDetail;
import com.tiangong.entity.vo.hotel.*;
import com.tiangong.enums.CancelRestrictionTypeEnum;
import com.tiangong.enums.ErrorCodeEnum;
import com.tiangong.enums.ParamErrorEnum;
import com.tiangong.exception.SysException;
import com.tiangong.hdp.controller.admin.product.vo.ProductReqVO;
import com.tiangong.hdp.controller.admin.product.vo.ProductResp;
import com.tiangong.hdp.controller.admin.product.vo.ProductRespVO;
import com.tiangong.hdp.convert.ProductConvert;
import com.tiangong.keys.RedisKey;
import com.tiangong.organization.remote.dto.AgentAccountConfig;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.service.base.HotelInfoService;
import com.tiangong.util.CollUtilX;
import com.tiangong.util.DateUtilX;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service("HdpProductService")
public class ProductServiceImpl implements ProductService {

    @Autowired
    private com.tiangong.service.base.ProductService productService;

    @Autowired
    private HotelInfoService hotelInfoService;

    @Override
    public List<ProductRespVO> queryProductDetail(ProductReqVO reqVO) {
        if (StrUtilX.isEmpty(reqVO.getAgentCode())) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_AGENTCODE);
        }
        List<ProductRespVO> resp = new ArrayList<>();
        ProductDetailInfoRequest request = new ProductDetailInfoRequest();
        request.setHotelId(reqVO.getHotelId());
        request.setCheckInDate(reqVO.getCheckInDate());
        request.setCheckOutDate(reqVO.getCheckOutDate());
        request.setRoomNum(reqVO.getRoomNum());
        request.setRoomGuestNumber(reqVO.getRoomGuestNumber());
        request.setNeedClockRoomProduct(reqVO.getNeedClockRoomProduct());
        request.setRoomGuestNumber(reqVO.getRoomGuestNumber());
        request.setNationality(reqVO.getNationality());
        request.setAgentCode(reqVO.getAgentCode());
        // 获取客户信息
        AgentAccountConfig agentAccountConfig = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.AGENT_ACCOUNT_CONFIG, reqVO.getAgentCode()), AgentAccountConfig.class);
        if (agentAccountConfig == null) {
            throw new SysException(ErrorCodeEnum.AGENT_INFO_NOT_EXIST);
        }
        request.setDomesticOrOverseas(agentAccountConfig.getDomesticOrOverseas());
        ProductDetailInfoResponse productDetailInfoResponse = productService.queryProductDetail(request);
        List<RoomItemDto> productItem = productDetailInfoResponse.getRoomItems();

        if (CollUtilX.isEmpty(productItem)) {
            return resp;
        }

        HotelInfoRequest infoRequest = new HotelInfoRequest();
        List<Long> hotelIds = Collections.singletonList(reqVO.getHotelId());
        infoRequest.setHotelIds(hotelIds);
        infoRequest.setLanguage(reqVO.getLanguage());
        infoRequest.setSettings(Collections.singletonList("hotelFacilityNew"));
        infoRequest.setAgentCode(request.getAgentCode());
        HotelInfoResponse hotelInfoResponse = hotelInfoService.queryHotelInfo(infoRequest);

        if (Objects.nonNull(hotelInfoResponse) && CollUtilX.isNotEmpty(hotelInfoResponse.getHotelInfos())) {
            HotelInfo hotelInfo = hotelInfoResponse.getHotelInfos().get(0);
            // 房型信息
            List<RoomInfo> roomInfos = hotelInfo.getRoomInfos();
            Map<Long, RoomInfo> roomMap = new HashMap<>();

            if (CollUtilX.isNotEmpty(roomInfos)) {
                roomMap = roomInfos.stream().collect(Collectors.toMap(RoomInfo::getRoomId, Function.identity(), (s1, s2) -> s1));
            }

            // 房型设施信息
            List<HotelFacilityNewResponseDto> hotelFacilityNew = hotelInfo.getHotelFacilityNew();
            if (CollUtilX.isNotEmpty(hotelFacilityNew)) {
                hotelFacilityNew = hotelFacilityNew.stream().filter(o -> "2".equals(o.getCategoryType())).collect(Collectors.toList());
            }

            // 获取房型主图
            HotelImageRequest imageRequest = new HotelImageRequest();
            imageRequest.setHotelIds(hotelIds);
            imageRequest.setLanguage(reqVO.getLanguage());
            HotelImageResponse hotelImageResponse = hotelInfoService.queryHotelImage(imageRequest);
            List<HotelImage> hotelImages = hotelImageResponse.getHotelImages();
            Map<Long, List<Image>> roomImageMap = new HashMap<>();
            if (CollUtilX.isNotEmpty(hotelImages)) {
                HotelImage hotelImage = hotelImages.get(0);
                if (CollUtilX.isNotEmpty(hotelImages)) {
                    List<RoomImage> roomImages = hotelImage.getRoomImages();
                    if (CollUtilX.isNotEmpty(roomImages)) {
                        roomImageMap = roomImages.stream().collect(Collectors.toMap(RoomImage::getRoomId, RoomImage::getImages));
                    }
                }
            }
            // 获取入住人数
            Integer guestNumber = 0;
            long dayNum = DateUtilX.getDay(DateUtilX.stringToDate(reqVO.getCheckInDate()), DateUtilX.stringToDate(reqVO.getCheckOutDate()));
            if (Objects.nonNull(reqVO.getRoomGuestNumber())) {
                Integer tempGuestNumber = reqVO.getRoomGuestNumber().getNumberOfAdults();
                if (StringUtils.isNotEmpty(reqVO.getRoomGuestNumber().getChildrenAges())) {
                    String[] split = reqVO.getRoomGuestNumber().getChildrenAges().split(",");
                    guestNumber = split.length + tempGuestNumber;
                } else {
                    guestNumber = tempGuestNumber;
                }
            }
            for (RoomItemDto roomItem : productItem) {
                // 符合预定条款的产品数据
                List<ProductResp> list = new ArrayList<>();
                // 获取最低价标识
                int i = 0;
                // 房型名称默认取产品级别
                ProductRespVO detailResp = new ProductRespVO();
                // 过滤不符合预定条款的数据
                for (ProductDetailDto product : roomItem.getProducts()) {
                    // 只要可订的产品
                    if (product.getBookType() != 1) {
                        continue;
                    }

                    // 过滤调priceItem为空的情况
                    // 每天价格不能为空
                    if (CollUtilX.isEmpty(product.getPriceItems())) {
                        continue;
                    }

                    //人数条款是否满足
                    if (product.getMaxPerson() != null
                            && product.getMaxPerson() < guestNumber) {

                        //不满足最大人数限制
                        log.info("不满足最大人数限制");
                        continue;
                    }

                    if (product.getMinPerson() != null
                            && product.getMinPerson() > guestNumber) {
                        // 不满足最少人数限制
                        log.info("不满足最少人数限制");
                        continue;
                    }

                    // 入住条款限制
                    int checkRestriction = 0;
                    outFor:
                    for (RoomItemDetailDto itemDetail : product.getRoomItemDetails()) {
                        for (PriceDetailItemDto priceItem : itemDetail.getPriceItems()) {
                            // 过滤30天后入住，到店付，一经预定不可取消，的产品
                            if (product.getPayAtHotelFlag() == 0
                                    && priceItem.getCancelRestrictionType() == CancelRestrictionTypeEnum.NO_CANCEL.getCode()) {
                                long day = DateUtilX.getDay(new Date(), DateUtilX.stringToDate(reqVO.getCheckInDate()));
                                if (day > 30) continue;
                            }

                            // 免费取消时间<入住时间时，返回不可取消
                            Integer cancelRestrictionDay = priceItem.getCancelRestrictionDay();
                            if (null != cancelRestrictionDay && priceItem.getCancelRestrictionType() == CancelRestrictionTypeEnum.FULL_CANCEL_LATE.getCode()) {
                                Date today = DateUtilX.stringToDate(DateUtilX.dateToString(new Date()));
                                String checkInDate = reqVO.getCheckInDate();

                                // 判断免费取消时间是否<入住时间
                                Date freeCancelDate = null;
                                if (cancelRestrictionDay == 0) {
                                    freeCancelDate = DateUtilX.stringToDate(checkInDate);
                                } else {
                                    freeCancelDate = DateUtilX.getDate(DateUtilX.stringToDate(checkInDate), -cancelRestrictionDay);
                                }

                                long day = DateUtilX.getDay(today, freeCancelDate);
                                if (day == 0 && StrUtilX.isNotEmpty(priceItem.getCancelRestrictionTime())) {
                                    // 返回不可取消
                                    priceItem.setCancelRestrictionType(CancelRestrictionTypeEnum.NO_CANCEL.getCode());
                                } else if (day < 0) {
                                    // 返回不可取消
                                    priceItem.setCancelRestrictionType(CancelRestrictionTypeEnum.NO_CANCEL.getCode());
                                }
                            }

                            if (CollUtilX.isNotEmpty(priceItem.getCancelPenalties())) {
                                priceItem.getCancelPenalties().forEach(item -> {
                                    try {
                                        item.setStartDate(formatDateStr(item.getStartDate()));
                                        item.setEndData(formatDateStr(item.getEndData()));
                                    } catch (Exception e) {
                                        log.error("格式化时间异常", e);
                                    }
                                });
                            }

                            // 入住间数条款
                            if (priceItem.getMinRoomCount() != null && priceItem.getMinRoomCount() > reqVO.getRoomNum()) {
                                log.info("入住间数必须大于" + priceItem.getMinRoomCount() + "间");
                                checkRestriction = 1;
                                break outFor;
                            }

                            if (priceItem.getMaxRoomCount() != null && priceItem.getMaxRoomCount() > 0 && priceItem.getMaxRoomCount() < reqVO.getRoomNum()) {
                                log.info("入住间数必须小于" + priceItem.getMaxRoomCount() + "间");
                                checkRestriction = 1;
                                break outFor;
                            }

                            // 入住天数条款
                            if (priceItem.getOccupancyRestrictionType() == null) continue;
                            if (priceItem.getOccupancyRestrictionDay() == null) continue;

                            if (priceItem.getOccupancyRestrictionType() == 1) {
                                // 入住天数需>=day天；
                                if (dayNum < priceItem.getOccupancyRestrictionDay()) {
                                    log.info("最小入住天数" + priceItem.getOccupancyRestrictionDay() + "天");
                                    checkRestriction = 1;
                                    break outFor;
                                }
                            } else if (priceItem.getOccupancyRestrictionType() == 2) {
                                // 必须入住day天；
                                if (dayNum != priceItem.getOccupancyRestrictionDay()) {
                                    log.info("入住天数必须为" + priceItem.getOccupancyRestrictionDay() + "天");
                                    checkRestriction = 1;
                                    break outFor;
                                }
                            } else if (priceItem.getOccupancyRestrictionType() == 3) {
                                // 入住天数需<=Day天。
                                if (dayNum > priceItem.getOccupancyRestrictionDay()) {
                                    log.info("入住天数必须" + priceItem.getOccupancyRestrictionDay() + "天");
                                    checkRestriction = 1;
                                    break outFor;
                                }
                            }
                        }
                    }
                    if (checkRestriction == 1) continue;
                    if (CollectionUtil.isNotEmpty(product.getRoomItemDetails()) && CollectionUtil.isNotEmpty(product.getRoomItemDetails().get(0).getPriceItems())) {
                        // 海外数据 每间每晚
                        PriceDetailItemDto priceDetailItemDto = product.getRoomItemDetails().get(0).getPriceItems().get(0);
                        String notReservationTerms = checkRestriction(priceDetailItemDto, (int) dayNum, DateUtilX.stringToDate(reqVO.getCheckInDate()));
                        if (StrUtilX.isNotEmpty(notReservationTerms)) {
                            log.info("不满足预定条款1");
                            continue;
                        }
                    }

                    if (CollectionUtil.isNotEmpty(product.getPriceItems())) {
                        // 取首日进行对比
                        PriceItemDto priceItem = product.getPriceItems().get(0);
                        PriceDetailItemDto priceDetailItemDto = ProductConvert.INSTANCE.priceDetailItemDtoConvert(priceItem);
                        String notReservationTerms = checkRestriction(priceDetailItemDto, (int) dayNum, DateUtilX.stringToDate(reqVO.getCheckInDate()));
                        if (StrUtilX.isNotEmpty(notReservationTerms)) {
                            log.info("不满足预定条款2");
                            continue;
                        }
                    }
                    ProductResp productResp = ProductConvert.INSTANCE.productRespConvert(product);

                    // 计算每个产品的最低剩余房量
                    if (CollUtilX.isNotEmpty(product.getPriceItems())) {
                        List<Integer> quotaNumList = new ArrayList<>();
                        for (PriceItemDto priceItem : product.getPriceItems()) {
                            // 与产品田野确认--当可用房量为null时，设置默认房量为99，为了统计产品的最低房量
                            if (null == priceItem.getQuotaNum()) {
                                quotaNumList.add(99);
                            } else {
                                quotaNumList.add(priceItem.getQuotaNum());
                            }
                        }
                        int quotaNum = 0;
                        if (CollUtilX.isNotEmpty(quotaNumList)) {
                            quotaNum = quotaNumList.stream().min(Integer::compareTo).get();
                        }
                        productResp.setQuotaNum(quotaNum);
                    }

                    // 计算每个产品的总税费
                    if (CollUtilX.isNotEmpty(product.getRoomItemDetails())) {
                        BigDecimal totalTax = BigDecimal.ZERO;
                        for (RoomItemDetailDto roomItemDetail : product.getRoomItemDetails()) {
                            if (roomItemDetail.getTaxDetail() != null) {
                                totalTax = convertPriceTaxDetail(roomItemDetail.getTaxDetail(), totalTax);
                            }
                            if (CollUtilX.isNotEmpty(roomItemDetail.getPriceItems())) {
                                for (PriceDetailItemDto priceItem : roomItemDetail.getPriceItems()) {
                                    if (priceItem.getTaxDetail() != null) {
                                        totalTax = convertPriceTaxDetail(priceItem.getTaxDetail(), totalTax);
                                    }
                                }
                            }
                        }
                        productResp.setTotalTax(totalTax);
                    }
                    // 转换币种，取第一天价格的币种
                    if (CollUtilX.isNotEmpty(product.getPriceItems())) {
                        String currency = product.getPriceItems().get(0).getCurrency();
                        productResp.setCurrency(currency);
                    }

                    // 增加划线价
                    if (product.getTotalSalePrice() == null || BigDecimal.ZERO.compareTo(product.getTotalSalePrice()) == 0) {
                        productResp.setAvgPrice(BigDecimal.ZERO);
                    } else {
                        // 均价 = totalSalePrice/间夜总数，保留2为小数，小数位向上进位
                        productResp.setAvgPrice(product.getTotalSalePrice().divide(new BigDecimal(reqVO.getRoomNum() * dayNum), 2, RoundingMode.UP));
                    }

                    list.add(productResp);
                    if (i == 0) {
                        detailResp.setLowestPrice(productResp.getTotalSalePrice());
                    }
                    i++;
                }

                // 没有产品的数据不输出
                if (CollUtilX.isEmpty(list)) {
                    continue;
                }
                detailResp.setProducts(list);
                detailResp.setRoomId(roomItem.getRoomId());
                detailResp.setRoomName(roomItem.getRoomName());

                // 基础信息有数据的时候取基础信息的房型名称
                RoomInfo roomInfo = roomMap.get(roomItem.getRoomId());
                if (Objects.nonNull(roomInfo)) {
                    detailResp.setRoomName(roomInfo.getRoomName());
                    detailResp.setRoomInfo(roomInfo);
                }
                detailResp.setRoomFacility(hotelFacilityNew);

                // 设置房型主图
                List<Image> images = roomImageMap.get(roomItem.getRoomId());
                if (CollUtilX.isNotEmpty(images)) {
                    // 获取主图
                    List<Image> collect = images.stream().filter(o -> o.getIsMain() != null && o.getIsMain() == 1).collect(Collectors.toList());
                    if (CollUtilX.isNotEmpty(collect)) {
                        detailResp.setRoomImage(collect.get(0).getUrl());
                    }
                }
                resp.add(detailResp);
            }
        } else {
            resp = ProductConvert.INSTANCE.productDetailRespConvert(productItem);
        }

        // 排序
        resp.sort(Comparator.comparing(ProductRespVO::getLowestPrice));
        return resp;
    }

    /**
     * 格式化时间
     */
    private String formatDateStr(String dateStr) {
        OffsetDateTime offsetDateTime = OffsetDateTime.parse(dateStr);
        return offsetDateTime.format(DateTimeFormatter.ofPattern(DateUtilX.hour_format));
    }

    /**
     * 判断是否是特殊条款
     */
    private String checkRestriction(PriceDetailItemDto priceItem, Integer day, Date checkInDate) {
        if (priceItem.getOccupancyRestrictionType() != null && priceItem.getOccupancyRestrictionDay() != null) {
            if (priceItem.getOccupancyRestrictionType() == 1 && priceItem.getOccupancyRestrictionDay() > day) {
                // 最小连住条款
                return "需连续预定大于等于" + priceItem.getOccupancyRestrictionDay() + "天";
            } else if (priceItem.getOccupancyRestrictionType() == 2 && !priceItem.getOccupancyRestrictionDay().equals(day)) {
                // 限住条款
                return "需连续预定等于" + priceItem.getOccupancyRestrictionDay() + "天";
            } else if (priceItem.getOccupancyRestrictionType() == 3 && priceItem.getOccupancyRestrictionDay() < day) {
                // 最多住条款
                return "需连续预定小于等于" + priceItem.getOccupancyRestrictionDay() + "天";
            }
        }
        if (priceItem.getBookRestrictionType() != null
                && priceItem.getBookRestrictionDay() != null
                && StrUtilX.isNotEmpty(priceItem.getBookRestrictionTime())) {
            String bookRestrictionTime = priceItem.getBookRestrictionTime();
            if (bookRestrictionTime.contains(":")) {
                bookRestrictionTime = bookRestrictionTime.replace(":", "");
            }
            String tempBookRestrictionTime = insertStringAtPosition(bookRestrictionTime, ":", 2);
            String checkDateString = DateUtilX.dateToString(DateUtilX.getDate(checkInDate, -priceItem.getBookRestrictionDay(), 0)) + " " + bookRestrictionTime;
            Date bookDate = DateUtilX.stringToDate(checkDateString, "yyyy-MM-dd HHmm");
            if (priceItem.getBookRestrictionType() == 1) {
                // 提前几天几点之前预订
                if (DateUtilX.compareInMS(bookDate, DateUtilX.dateFormat(DateUtilX.getCurrentDate(), "yyyy-MM-dd HHmm")) < 0) {
                    return "需提前" + priceItem.getBookRestrictionDay() + "天" + tempBookRestrictionTime + "点之前预定";
                }
            } else if (priceItem.getBookRestrictionType() == 2) {
                // 在几天内几点之后预订
                if (DateUtilX.compareInMS(bookDate, DateUtilX.dateFormat(DateUtilX.getCurrentDate(), "yyyy-MM-dd HHmm")) > 0) {
                    return "需提前" + priceItem.getBookRestrictionDay() + "天" + tempBookRestrictionTime + "点之后预定";
                }
            }
        }
        return null;
    }

    /**
     * 在指定位置插入字符串
     */
    public static String insertStringAtPosition(String originalString, String insertString, int position) {
        StringBuilder stringBuilder = new StringBuilder(originalString);
        stringBuilder.insert(position, insertString);
        return stringBuilder.toString();
    }

    /**
     * 计算税
     */
    private BigDecimal convertPriceTaxDetail(PriceTaxDetail priceTaxDetail, BigDecimal totalTax) {
        totalTax = totalTax.add(priceTaxDetail.getSalesTax() != null ? priceTaxDetail.getSalesTax() : BigDecimal.ZERO);
        totalTax = totalTax.add(priceTaxDetail.getOtherTax() != null ? priceTaxDetail.getOtherTax() : BigDecimal.ZERO);
        totalTax = totalTax.add(priceTaxDetail.getTaxFee() != null ? priceTaxDetail.getTaxFee() : BigDecimal.ZERO);
        return totalTax;
    }
}
