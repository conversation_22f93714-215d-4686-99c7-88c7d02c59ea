package com.tiangong.hdp.controller.admin.order.vo;

import com.tiangong.dto.product.request.RoomGuestNumber;
import com.tiangong.entity.base.BusinessRequest;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.math.BigDecimal;

@Data
public class CheckBookingReqVO extends BusinessRequest {
    /**
     * 酒店id
     */
    @NotNull(message = "EMPTY_PARAM_HOTELID")
    private Long hotelId;

    /**
     * 房间id
     */
    @NotNull(message = "EMPTY_PARAM_ROOMID")
    private Long roomId;

    /**
     * 价格计划id
     */
    @NotEmpty(message = "EMPTY_PARAM_RATEPLANID")
    private String ratePlanId;

    /**
     * 入住日期
     */
    @NotEmpty(message = "EMPTY_PARAM_CHECKINDATE")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "PARAM_FORMAT_CHECKINDATE(yyyy-MM-dd)")
    private String checkInDate;

    /**
     * 离店日期
     */
    @NotEmpty(message = "EMPTY_PARAM_CHECKOUTDATE")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "PARAM_FORMAT_CHECKOUTDATE(yyyy-MM-dd)")
    private String checkOutDate;

    /**
     * 房间数量
     */
    @NotNull(message = "EMPTY_PARAM_ROOMNUM")
    private Integer roomNum;

    /**
     * 供应商编码
     */
    @NotEmpty(message = "EMPTY_PARAM_SUPPLYCODE")
    private String supplyCode;

    /**
     * 房间入住人信息
     */
    private RoomGuestNumber roomGuestNumber;

    /**
     * 订单总金额
     */
    private BigDecimal totalSalePrice;
}