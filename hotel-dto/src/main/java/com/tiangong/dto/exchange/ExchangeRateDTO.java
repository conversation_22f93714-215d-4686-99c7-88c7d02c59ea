package com.tiangong.dto.exchange;


import com.tiangong.dto.common.BaseDTO;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
public class ExchangeRateDTO extends BaseDTO {

    /**
     * 修改时间
     */
    private String updatedDt;

    /**
     * 汇率ID
     */
    private Integer exchangeRateId;

    /**
     * 商家编码
     */
    private String companyCode;

    /**
     * 原币种
     */
    @NotEmpty(message = "EMPTY_PARAM_ORIGINALCURRENCY")
    private String originalCurrency;

    /**
     * 目标币种
     */
    @NotEmpty(message = "EMPTY_PARAM_TARGETCURRENCY")
    private String targetCurrency;

    /**
     * 最终汇率
     */
    //@NotNull(message = "EMPTY_PARAM_RATE")
    private BigDecimal rate;

    /**
     * 原币种名称
     */
    @NotEmpty(message = "EMPTY_PARAM_ORIGINALCURRENCYNAME")
    private String originalCurrencyName;

    /**
     * 目标币种名称
     */
    @NotEmpty(message = "EMPTY_PARAM_TARGETCURRENCYNAME")
    private String targetCurrencyName;

    /**
     * 增量类型0加数值 1减数值 2加百分比 3减百分比 4等于
     */
    @NotNull(message = "EMPTY_PARAM_ADJUSTMENTTYPE")
    private Integer adjustmentType;

    /**
     * 调整金额
     */
    @NotNull(message = "EMPTY_PARAM_MODIFIEDAMT")
    private BigDecimal modifiedAmt;

    /**
     * 汇率
     */
    @NotNull(message = "EMPTY_PARAM_RATE")
    private BigDecimal exchangeRate;

    /**
     * 是否自动同步汇率：0否 1是
     */
    private Integer autoSync;
    /**
     * 汇率展示
     */
    private String exchangeRateStr;
    /**
     * 最终汇率展示
     */
    private String rateStr;
}
