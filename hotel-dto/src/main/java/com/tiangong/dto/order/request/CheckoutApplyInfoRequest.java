package com.tiangong.dto.order.request;

import com.tiangong.dto.common.BusinessRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @time 2024/9/11 16:40
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CheckoutApplyInfoRequest extends BusinessRequest {

    /**
     * 订单号
     */
    @NotEmpty(message = "EMPTY_PARAM_ORDERCODE")
    private String fcOrderCode;

    /**
     * 商旅系统退单号
     */
    private String supplierRefundNumber;

    /**
     * 退房原因
     */
    @NotEmpty(message = "EMPTY_PARAM_CHECKOUT_REASON")
    private String checkoutReason;

    /**
     * 退房凭证集合
     */
    private List<String> checkoutVoucherList;

    /**
     * 退款类型，0-整单取消，1-部分取消
     */
    private Integer refundType;

    /**
     * 退房信息列表
     */
    @NotNull(message = "EMPTY_PARAM_CHECKOUT_ROOM_INFO")
    @Size(min = 1, message = "EMPTY_PARAM_CHECKOUT_ROOM_INFO")
    private List<CheckoutRoomInfo> checkoutRoomInfoList;
}
