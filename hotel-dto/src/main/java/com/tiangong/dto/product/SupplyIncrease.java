package com.tiangong.dto.product;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 供应商加幅
 **/
@Data
public class SupplyIncrease {

    /**
     * 加幅类型
     */
    private Integer adjustmentType;

    /**
     * 加幅值
     */
    private BigDecimal modifiedAmt;

    /**
     * 最低加幅金额
     */
    private BigDecimal lowestIncreaseAmt;

    /**
     * 供应商编码
     */
    private String supplyCode;

}
