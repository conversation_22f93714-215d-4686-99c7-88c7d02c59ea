package com.tiangong.dto.product;

import com.tiangong.dto.product.response.DhubProductDetailDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;
import java.util.Set;

/**
 * @Description:
 * @ClassName: PreferredProductDTO
 * @Author: xubohao
 * @CreateDate: 2025/4/10 16:16
 * @Version: 1.0
 */

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class DhubPreferredProductDTO {

    private DhubProductDetailDto productDetail;

    private Map<Integer, Set<String>> tipsDetailGroup;

}
