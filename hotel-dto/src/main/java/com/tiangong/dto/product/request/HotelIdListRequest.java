package com.tiangong.dto.product.request;

import com.tiangong.dto.common.BusinessRequest;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class HotelIdListRequest extends BusinessRequest {

    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 页码
     */
    @NotNull(message = "页码不能为空!")
    private Integer pageNo = 1;

    /**
     * 每页数量
     */
    private Integer pageSize;

    /**
     * 查询上一页时返回的maxId标记值, 查询第一页时该值为0。
     * 使用说明：首次请求必须为0，之后再根据返回值修改后续MaxId值。
     */
    private Long maxId;

    /**
     * 查询类型  为空或者1 默认国内，  2.海外
     */
    private Integer searchType;

    /**
     * 语言：zh-CN en-US
     */

    private String language;

    /**
     * 上一页的最后一个酒店id
     * 可以为空，但是查询效率会底下检一增加
     */
    private Long hotelId;
}
