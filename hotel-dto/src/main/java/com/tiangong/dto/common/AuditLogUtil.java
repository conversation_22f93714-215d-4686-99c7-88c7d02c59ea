package com.tiangong.dto.common;


import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2023/3/16 11:12
 * @Description:
 */
public class AuditLogUtil {

    public static AuditLogAddReq getAuditLogAddReq(String createdBy, Integer eventResult, Integer userId, String userAccount, String ipAddress, String targetId, String target, Integer targetType, Integer auditLogEnumNo) {
        return AuditLogAddReq.builder().createdDt(new Date()).createdBy(createdBy)
                .eventResult(eventResult).userId(userId).userAccount(userAccount).ipAddress(ipAddress).targetId(targetId).target(target)
                .targetType(targetType).auditLogEnumNo(auditLogEnumNo).build();
    }

}
