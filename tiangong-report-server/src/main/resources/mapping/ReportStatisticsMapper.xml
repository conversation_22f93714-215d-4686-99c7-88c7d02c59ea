<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tiangong.mapper.ReportStatisticsMapper">

    <select id="queryAllOrderCode" parameterType="com.tiangong.statistics.dto.QuerySaleStatisticsDTO" resultType="java.lang.String">
        select o.order_code orderCode from o_order o where 1 = 1
        <if test="startDate != null and startDate !=''">
            AND o.created_dt &gt;= #{startDate}
        </if>
        <if test="endDate != null and endDate !=''">
            AND o.created_dt &lt; date_add(#{endDate},interval 1 day)
        </if>
    </select>

    <select id="queryOrderGuest" parameterType="java.lang.Long"
                resultType="com.tiangong.domain.resp.OrderGuestResp">
        SELECT
            o.name as name,
            o.room_number as roomNumber
        from o_guest o where o.order_id = #{orderId}
    </select>

    <!-- 查询订单相关数据 -->
    <select id="queryOrderReport" parameterType="java.lang.String"
            resultType="com.tiangong.domain.resp.OrderReportResp">
        SELECT
        o.id orderId,
        o.order_code orderCode,
        o.hotel_id hotelId,
        o.hotel_name hotelName,
        o.room_name roomName,
        o.product_name productName,
        o.created_dt createdDt,
        o.start_date checkInDate,
        o.end_date checkOutDate,
        o.start_time startTime,
        o.end_time endTime,
        o.room_qty roomQty,
        o.hourly hourly,
        IF
        ( o.hourly = 1, 0.5 * o.room_qty, 0.0 + timestampdiff ( DAY, o.start_date, o.end_date ) * o.room_qty ) nightQty,
        o.guest guest,
        o.order_confirmation_status orderConfirmationStatus,
        o.settlement_type orderSettlementType,
        o.created_by createdBy,
        o.order_owner_name orderOwnerName,
        u1.user_name saleManagerName,
        o.channel_code agentType,
        o.agent_name agentName,
        o.agent_code agentCode,
        o.channel_order_code channelOrderCode,
        o.sale_currency saleCurrency,
        o.supplier_label supplierLabel,
        o.org_to_agent_rate saleRate,
        o.order_amt receivableAmt,
        CAST( CEILING( o.order_amt / o.org_to_agent_rate * 100 ) / 100 AS DECIMAL(18,2) ) AS receivableOrgCurrencyAmt,
        f.received_amt receivedAmt,
        CAST( CEILING( f.received_amt / o.org_to_agent_rate * 100 ) / 100 AS DECIMAL(18,2) ) AS receivedOrgCurrencyAmt,
        f.unreceived_amt 'unreceivedAmt',
        CAST(CEILING( o.order_amt / o.org_to_agent_rate * 100 ) / 100 - CEILING( f.received_amt / o.org_to_agent_rate * 100 ) / 100 AS DECIMAL(18,2)) AS 'unreceivedOrgCurrencyAmt',
        u1.user_name purchaseManagerName,
        o.channel_name channelName,
        too.channel_code channelCode,
        o.pay_method payMethod,
        o.product_label_type productLabelType,
        o.country_code countryCode,
        o.province_code provinceCode,
        o.city_code cityCode,
        IFNULL(p.area_name, o.province_name) provinceName,
        IFNULL(a.area_name, o.city_name) cityName,
        IFNULL(hc.area_name, o.country_name) countryName,
        DATEDIFF( o.start_date, o.created_dt ) bookingDays,
        tc.contact_name agentManagerName,
        o.is_manual_order isManualOrder
        FROM
        o_order o
        LEFT JOIN o_order_finance f ON f.order_id = o.id
        LEFT JOIN t_auth_user u1 ON u1.user_id = o.merchant_bm
        LEFT JOIN t_org_organization too ON too.org_code = o.agent_code
        LEFT JOIN t_baseinfo_areadata_zh_CN a ON o.city_code = a.area_code AND a.area_type = 3
        LEFT JOIN t_baseinfo_areadata_zh_CN p ON p.area_code = o.province_code AND p.area_type = 2
        LEFT JOIN t_baseinfo_areadata_zh_CN hc ON hc.area_code = o.country_code AND hc.area_type = 1
        LEFT JOIN t_org_contact tc ON tc.org_code = o.agent_code AND tc.contact_type = 1 AND tc.contact_role = 0  AND tc.active = 1
        WHERE
        o.order_code = #{orderCode}
    </select>

    <!-- 查询供货单相关数据 -->
    <select id="querySuppleOrderReport" parameterType="java.lang.String"
            resultType="com.tiangong.domain.resp.SuppleOrderReportResp">
        SELECT
        s.supply_order_code supplyOrderCode,
        s.room_name supplyOrderRoomName,
        s.product_name supplyOrderProductName,
        s.start_date supplyOrderStartDate,
        s.end_date supplyOrderEndDate,
        s.start_time supplyOrderStartTime,
        s.end_time supplyOrderEndTime,
        s.room_qty supplyOrderRoomQty,
        s.supplier_name supplierName,
        s.supplier_code supplierCode,
        s.supplier_order_code supplierOrderCode,
        s.confirmation_code confirmationCode,
        IFNULL( s.supply_order_amt, 0 ) payableAmt,
        IFNULL( CAST( CEIL(( s.supply_order_amt * s.rate ) * 100 ) / 100 AS DECIMAL(18,2) ),0) payableOrgCurrencyAmt,
        IFNULL( s.reward_amt, 0 ) rewardAmt,
        IFNULL( CAST( CEIL(( s.reward_amt * s.rate ) * 100 ) / 100 AS DECIMAL(18,2) ),0) rewardOrgCurrencyAmt,
        IFNULL( s.rebate_amt, 0 ) rebateAmt,
        IFNULL( CAST( CEIL(( s.rebate_amt * s.rate ) * 100 ) / 100 AS DECIMAL(18,2) ),0) rebateOrgCurrencyAmt,
        IFNULL( s.base_currency, 0 ) baseCurrency,
        IFNULL( s.rate, 0 ) baseRate,
        IFNULL( s.confirmation_status, 0 ) supplyOrderConfirmationStatus,
        o.guest supplyOrderGuest,
        s.settlement_type supplyOrderSettlementType,
        IF
        ( o.hourly = 1, 0.5 * s.room_qty, 0.0+timestampdiff ( DAY, s.start_date, s.end_date ) * s.room_qty )
        supplyOrderNightQty,
        p.purchase_type purchaseType,
        s.room_numbers as roomNumbers
        FROM
        o_order o
        LEFT JOIN o_supply_order s ON o.id = s.order_id
        LEFT JOIN o_supply_product p ON s.id = p.supply_order_id
        LEFT JOIN o_supply_order_finance sf ON sf.supply_order_id = s.id AND sf.finance_type = 0
        WHERE
        o.order_code = #{orderCode}
    </select>

    <!-- 查询供货单财务相关数据 -->
    <select id="querySuppleOrderFinanceReport" parameterType="java.lang.String"
            resultType="com.tiangong.domain.resp.SuppleOrderFinanceReportResp">
        SELECT
        s.supply_order_code as supplyOrderCode,
        IFNULL( s.rate, 0 ) as baseRate,
        SUM( CASE WHEN sf.finance_type = 0 THEN IFNULL( sf.paid_amt, 0 ) ELSE 0 END ) AS supplyOrderPaidAmt,
        SUM( CASE WHEN sf.finance_type = 0 THEN CEIL(( sf.paid_amt * s.rate ) * 100 ) / 100  ELSE 0 END ) AS supplyOrderPaidOrgCurrencyAmt,
        SUM( CASE WHEN sf.finance_type = 0 THEN IFNULL( sf.unpaid_amt, 0 ) ELSE 0 END ) AS supplyOrderUnpaidAmt,
        SUM( CASE WHEN sf.finance_type = 0 THEN CEIL(( s.supply_order_amt * s.rate ) * 100 ) / 100 - CEIL(( sf.paid_amt * s.rate ) * 100 ) / 100 ELSE 0 END ) AS supplyOrderUnpaidOrgCurrencyAmt,

        SUM( CASE WHEN sf.finance_type = 1 THEN IFNULL( sf.paid_amt, 0 ) ELSE 0 END ) AS rewardPaidAmt,
        SUM( CASE WHEN sf.finance_type = 1 THEN CEIL(( sf.paid_amt * s.rate ) * 100 ) / 100  ELSE 0 END ) AS rewardPaidOrgCurrencyAmt,
        SUM( CASE WHEN sf.finance_type = 1 THEN IFNULL( sf.unpaid_amt, 0 ) ELSE 0 END ) AS rewardUnpaidAmt,
        SUM( CASE WHEN sf.finance_type = 1 THEN CEIL(( s.reward_amt * s.rate ) * 100 ) / 100 - CEIL(( sf.paid_amt * s.rate ) * 100 ) / 100 ELSE 0 END ) AS rewardUnpaidOrgCurrencyAmt,

        SUM( CASE WHEN sf.finance_type = 2 THEN IFNULL( sf.paid_amt, 0 ) ELSE 0 END ) AS rebatePaidAmt,
        SUM( CASE WHEN sf.finance_type = 2 THEN CEIL(( sf.paid_amt * s.rate ) * 100 ) / 100  ELSE 0 END ) AS rebatePaidOrgCurrencyAmt,
        SUM( CASE WHEN sf.finance_type = 2 THEN IFNULL( sf.unpaid_amt, 0 ) ELSE 0 END ) AS rebateUnpaidAmt,
        SUM( CASE WHEN sf.finance_type = 2 THEN CEIL(( s.rebate_amt * s.rate ) * 100 ) / 100 - CEIL(( sf.paid_amt * s.rate ) * 100 ) / 100 ELSE 0 END ) AS rebateUnpaidOrgCurrencyAmt
        FROM o_order o
        LEFT JOIN o_supply_order s ON o.id = s.order_id
        LEFT JOIN ( SELECT supply_order_id, finance_type, paid_amt, unpaid_amt FROM o_supply_order_finance WHERE
        finance_type IN ( 0, 1, 2 ) ) sf ON sf.supply_order_id = s.id
        WHERE
        s.supply_order_code = #{supplyOrderCode}
    </select>
</mapper>