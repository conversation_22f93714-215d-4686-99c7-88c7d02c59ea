<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tiangong.mapper.OrderRemarkMapper">

    <select id="queryOrderRemarkInfos" parameterType="com.tiangong.domain.req.ExportOrderRemarkReq"
            resultType="com.tiangong.domain.dto.ExportOrderRemarkDTO">
        SELECT o.order_code                orderCode,
               CASE
                   when o.order_confirmation_status = 0 then '待确认'
                   when o.order_confirmation_status = 1 then '已确认'
                   when o.order_confirmation_status = 2 then '已取消'
                   when o.order_confirmation_status = 3 then '已完成'
                   ELSE '未知状态'
               END AS orderConfirmationStatusDesc,
               GROUP_CONCAT(DISTINCT os.supply_order_code) AS supplyOrderCode,
               o.agent_code                agentCode,
               o.agent_name                agentName,
               os.supplier_code            supplierCode,
               os.supplier_name            supplierName,
               oc.contact_name             contactName,
               CASE
                   when o.created_dt is not null then date_format(o.created_dt, '%Y-%m-%d %H:%i:%s')
               END AS orderCreatedDt,
               CASE
                   when o.start_date is not null then date_format(o.start_date, '%Y-%m-%d')
               END AS startDate,
               CASE
                   when o.end_date is not null then date_format(o.end_date, '%Y-%m-%d')
                   END AS endDate,
               CASE
                   when temp.created_dt is not null then date_format(temp.created_dt, '%Y-%m-%d %H:%i:%s')
               END AS confirmationCreatedDt,
               CASE
                   when oor.remark_type = 0 then '客人备注'
                   when oor.remark_type = 1 then '供应商备注'
                   when oor.remark_type = 2 then '内部备注'
               END AS remarkTypeDesc,
               oor.remark                  remark,
               oor.created_by              orderRemarkCreatedBy,
               CASE
                   when oor.created_dt is not null then date_format(oor.created_dt, '%Y-%m-%d %H:%i:%s')
               END AS orderRemarkCreatedDt
        FROM o_order o
                 LEFT JOIN (SELECT min(created_dt) created_dt, order_id FROM o_order_confirm_record GROUP BY order_id) temp
                           ON temp.order_id = o.id
                 LEFT JOIN o_supply_order os ON os.order_id = o.id
                 LEFT JOIN o_order_remark oor ON oor.order_id = o.id
                 LEFT JOIN t_org_organization oo ON oo.org_code = os.supplier_code
                 LEFT JOIN t_org_contact oc ON oc.org_code = oo.org_code and oc.contact_type = 1 and oc.contact_role = 0 and oc.active = 1
        <choose>
            <when test="dateQueryType==1">
                <if test="startDate != null and startDate !='' and endDate != null and endDate !=''">
                    where date_format(o.start_date, '%Y-%m-%d') BETWEEN #{startDate} AND #{endDate}
                </if>
            </when>
            <when test="dateQueryType==2">
                <if test="startDate != null and startDate !='' and endDate != null and endDate !=''">
                    where date_format(o.end_date, '%Y-%m-%d') BETWEEN #{startDate} AND #{endDate}
                </if>
            </when>
            <otherwise>
                <if test="startDate != null and startDate !='' and endDate != null and endDate !=''">
                    where date_format(o.created_dt, '%Y-%m-%d') BETWEEN #{startDate} AND #{endDate}
                </if>
            </otherwise>
        </choose>
        GROUP BY
        o.order_code,
        o.order_confirmation_status,
        o.agent_code,
        o.agent_name,
        os.supplier_code,
        os.supplier_name,
        oc.contact_name,
        o.created_dt,
        o.start_date,
        o.end_date,
        temp.created_dt,
        oor.remark_type,
        oor.remark,
        oor.created_by,
        oor.created_dt
    </select>

    <select id="selectOrderCancelReasonInfos" resultType="com.tiangong.domain.dto.ExportOrderCancelReasonDTO">
        SELECT
            t1.order_code orderCode,
            t3.created_dt confirmationDate,
            t4.cancelled_reason cancelReason,
            t8.content cancelContent,
            CASE
                WHEN t1.order_confirmation_status = 0 THEN '待确认'
                WHEN t1.order_confirmation_status = 1 THEN '已确认'
                WHEN t1.order_confirmation_status = 2 THEN '已取消'
                WHEN t1.order_confirmation_status = 3 THEN '已完成'
                ELSE '未知状态'
            END AS orderConfirmationStatusDesc,
            t1.agent_name agentName,
            t1.created_dt orderCreatedDt,
            t1.start_date checkInDate,
            t1.end_date checkOutDate,
            IF(
                t1.hourly=1,
                0.5 * t1.room_qty,
                0.0 + TIMESTAMPDIFF(DAY, t1.start_date, t1.end_date ) * t1.room_qty
            ) orderNightQty,
            t1.hotel_name hotelName,
            t1.city_name cityName,
            t1.province_name provinceName,
            t1.country_name countryName,
            CASE
                WHEN t1.cn_type = 1 THEN '是'
            ELSE '否'
            END AS isCnOrder
        FROM o_order t1
        LEFT JOIN o_order_extend t4 ON t4.order_id = t1.id
        LEFT JOIN o_order_log t8 ON t8.order_id = t1.id AND t8.content LIKE '%取消内容：%'
        LEFT JOIN (
            SELECT
                t5.*
            FROM o_order_confirm_record t5
            JOIN (
                SELECT
                    order_id,
                    MAX(id) AS max_id
                FROM o_order_confirm_record
                GROUP BY order_id
            ) t6 ON t5.order_id = t6.order_id AND t5.id = t6.max_id
        ) t3 ON t1.id = t3.order_id
        WHERE t1.order_confirmation_status = 2
        <choose>
            <when test="dateQueryType == 1">
                <if test="startDate != null and startDate !='' and endDate != null and endDate !=''">
                    AND DATE_FORMAT(t1.start_date, '%Y-%m-%d') BETWEEN #{startDate} AND #{endDate}
                </if>
            </when>
            <when test="dateQueryType == 2">
                <if test="startDate != null and startDate !='' and endDate != null and endDate !=''">
                    AND DATE_FORMAT(t1.end_date, '%Y-%m-%d') BETWEEN #{startDate} AND #{endDate}
                </if>
            </when>
            <otherwise>
                <if test="startDate != null and startDate !='' and endDate != null and endDate !=''">
                    AND DATE_FORMAT(t1.created_dt, '%Y-%m-%d') BETWEEN #{startDate} AND #{endDate}
                </if>
            </otherwise>
        </choose>
    </select>

</mapper>