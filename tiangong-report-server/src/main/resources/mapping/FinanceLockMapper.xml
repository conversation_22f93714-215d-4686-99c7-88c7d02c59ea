<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tiangong.mapper.FinanceLockMapper">

    <update id="lockSupplyOrder" parameterType="com.tiangong.domain.dto.FinanceLockSupplyOrderDTO">
        UPDATE o_supply_order_finance sf SET sf.finance_lock_status=#{lockStatus} WHERE
        sf.supply_order_id=#{supplyOrderId} AND sf.finance_type = #{statementType}
    </update>

    <select id="checkSupplyOrderCanLock" parameterType="java.lang.Integer" resultType="java.lang.Integer">
        SELECT
        f.id
        FROM f_supplier_statement_order f
        LEFT JOIN f_supplier_statement s ON s.id = f.statement_id
        WHERE f.supply_order_id = #{supplyOrderId} AND s.statement_status = 2
    </select>

</mapper>