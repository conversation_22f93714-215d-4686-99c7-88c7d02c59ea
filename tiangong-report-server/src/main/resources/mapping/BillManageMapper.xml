<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tiangong.mapper.BillManageMapper">

    <resultMap id="queryStatementOrderListBaseResultMap" type="com.tiangong.domain.resp.StatementOrderDTO">
        <id column="id" jdbcType="INTEGER" property="statementOrderId" />
        <result column="order_code" jdbcType="VARCHAR" property="orderCode" />
        <result column="channel_order_code" jdbcType="VARCHAR" property="channelOrderCode" />
        <result column="hotel_name" jdbcType="VARCHAR" property="hotelName" />
        <result column="room_name" jdbcType="VARCHAR" property="roomName" />
        <result column="guest" jdbcType="VARCHAR" property="guest" />
        <result column="start_date" jdbcType="DATE" property="startDate" />
        <result column="end_date" jdbcType="DATE" property="endDate" />
        <result column="room_qty" jdbcType="INTEGER" property="roomQty" />
        <result column="currency" jdbcType="INTEGER" property="currency" />
        <result column="sale_rate" jdbcType="DECIMAL" property="saleRate" />
        <result column="org_to_agent_rate" jdbcType="DECIMAL" property="orgToAgentRate" />
        <result column="receivable_amt" jdbcType="DECIMAL" property="receivableAmt" />
        <result column="received_amt" jdbcType="DECIMAL" property="receivedAmt" />
        <result column="unreceived_amt" jdbcType="DECIMAL" property="unreceivedAmt" />
        <result column="receive_amt" jdbcType="DECIMAL" property="receiveAmt" />
        <result column="confirmation_status" jdbcType="INTEGER" property="confirmationStatus" />
        <result column="order_create_date" jdbcType="TIMESTAMP" property="createdDt" />
        <result column="additional_charges" jdbcType="DECIMAL" property="additionalCharges" />
        <result column="refund_fee" jdbcType="DECIMAL" property="refundFee" />
        <result column="sum_sale_price" jdbcType="DECIMAL" property="sumSalePrice" />
        <result column="city_name" jdbcType="DECIMAL" property="cityName" />
        <result column="nightQty" jdbcType="DECIMAL" property="nightQty" />
        <result column="orderId" jdbcType="INTEGER" property="orderId" />
        <result column="swiping_card_amount" jdbcType="DECIMAL" property="swipingCardAmount" />
        <result column="swiping_card_amount_currency" jdbcType="INTEGER" property="swipingCardAmountCurrency" />
        <result column="swiping_card_rate" jdbcType="DECIMAL" property="swipingCardRate" />
        <result column="swiping_card_dt" jdbcType="DATE" property="swipingCardDt" />
        <result column="settle_amount" jdbcType="DECIMAL" property="settleAmount" />
        <result column="settle_amount_currency" jdbcType="INTEGER" property="settleAmountCurrency" />
        <result column="collection_rate" jdbcType="DECIMAL" property="collectionRate" />
        <result column="collection_amount" jdbcType="DECIMAL" property="collectionAmount" />
        <result column="rate_difference_value" jdbcType="DECIMAL" property="rateDifferenceValue" />
        <result column="settlement_cost_error_desc" jdbcType="VARCHAR" property="settlementCostErrorDesc" />
        <result column="supplier_label" jdbcType="INTEGER" property="supplierLabel" />
    </resultMap>

    <resultMap id="queryStatementSupplyOrderListBaseResultMapWithOrderCode" type="com.tiangong.domain.resp.StatementSupplyOrderWithOrderCodeDTO">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="INTEGER" property="statementOrderId" />
        <result column="id" jdbcType="INTEGER" property="statementOrderId" />
        <result column="supply_order_code" jdbcType="VARCHAR" property="supplyOrderCode" />
        <result column="hotel_name" jdbcType="VARCHAR" property="hotelName" />
        <result column="room_name" jdbcType="VARCHAR" property="roomName" />
        <result column="guest" jdbcType="VARCHAR" property="guest" />
        <result column="start_date" jdbcType="DATE" property="startDate" />
        <result column="end_date" jdbcType="DATE" property="endDate" />
        <result column="room_qty" jdbcType="INTEGER" property="roomQty" />
        <result column="currency" jdbcType="INTEGER" property="currency" />
        <result column="sale_rate" jdbcType="DECIMAL" property="saleRate" />
        <result column="payable_amt" jdbcType="DECIMAL" property="payableAmt" />
        <result column="paid_amt" jdbcType="DECIMAL" property="paidAmt" />
        <result column="unpaid_amt" jdbcType="DECIMAL" property="unpaidAmt" />
        <result column="confirmation_status" jdbcType="INTEGER" property="confirmationStatus" />
        <result column="order_create_date" jdbcType="TIMESTAMP" property="createdDt" />
        <result column="supplier_order_code" property="supplierOrderCode" />
        <result column="confirmation_code" property="confirmationCode" />
        <result column="order_code" property="orderCode" />
        <result column="commission" jdbcType="DECIMAL" property="commission" />
        <result column="additional_charges" jdbcType="DECIMAL" property="additionalCharges" />
        <result column="refund_fee" jdbcType="DECIMAL" property="refundFee" />
        <result column="sum_base_price" jdbcType="DECIMAL" property="sumBasePrice" />

        <!-- 入住人信息 -->
        <collection property="guests" select="com.tiangong.mapper.BillManageMapper.selectOrderGuests"
                    column="orderId" javaType="ArrayList" ofType="com.tiangong.order.remote.response.GuestDTO">
            <result column="name" property="name"/>
            <result column="roomNumber" property="roomNumber"/>
        </collection>
    </resultMap>

    <!-- 查询客户账单 -->
    <select id="queryCustomerBillList"
            parameterType="com.tiangong.domain.req.QueryStatementOrderListDTO"
            resultMap="queryStatementOrderListBaseResultMap">
        SELECT
        so.id,
        so.order_code,
        oo.channel_order_code,
        so.hotel_name,
        so.room_name,
        so.guest,
        so.start_date,
        so.end_date,
        so.room_qty,
        so.currency,
        so.receivable_amt,
        so.received_amt,
        so.unreceived_amt,
        so.receive_amt,
        so.confirmation_status,
        so.order_create_date,
        oo.additional_charges,
        oo.sale_rate saleRate,
        oo.org_to_agent_rate,
        oo.refund_fee,
        oo.city_name,
        oo.supplier_label,
        IF( ( oo.hourly = 1 ), 0.5, 0+DATEDIFF ( oo.end_date, oo.start_date ) ) nightQty,
        oo.id as orderId,
        ifnull(so.receivable_amt, 0) - ifnull(oo.additional_charges, 0) as sum_sale_price,
        so.swiping_card_amount,
        so.swiping_card_amount_currency,
        so.swiping_card_rate,
        so.swiping_card_dt,
        so.settle_amount,
        so.settle_amount_currency,
        so.collection_rate,
        so.collection_amount,
        so.rate_difference_value,
        so.settlement_cost_error_desc
        FROM f_agent_statement_order so,o_order oo
        WHERE so.statement_id=#{statementId}
        AND so.order_id = oo.id
        <if test="orderCode !=null and orderCode !='' ">
            AND so.order_code = #{orderCode}
        </if>
        <choose>
            <when test="dateQueryType==0">
                <if test="startDate!=null and startDate!=''">
                    AND so.order_create_date >= #{startDate}
                </if>
                <if test="endDate!=null and endDate!=''">
                    AND so.order_create_date &lt; date_add(#{endDate},interval 1 day)
                </if>
            </when>
            <when test="dateQueryType==1">
                <if test="startDate!=null and startDate!=''">
                    AND so.start_date >= #{startDate}
                </if>
                <if test="endDate!=null and endDate!=''">
                    AND so.start_date &lt;= #{endDate}
                </if>
            </when>
            <when test="dateQueryType==2">
                <if test="startDate!=null and startDate!=''">
                    AND so.end_date >= #{startDate}
                </if>
                <if test="endDate!=null and endDate!=''">
                    AND so.end_date &lt;= #{endDate}
                </if>
            </when>
        </choose>
        ORDER BY IF(so.settlement_cost_error_desc is null, 0, 1) DESC, so.id DESC
    </select>

    <!-- 查询供应商账单记录 -->
    <select id="querySupplierBillList" parameterType="com.tiangong.domain.req.QueryStatementSupplyOrderListDTO"
            resultMap="queryStatementSupplyOrderListBaseResultMapWithOrderCode">
        SELECT
        so.id,
        so.supply_order_code,
        so.hotel_name,
        so.room_name,
        so.guest,
        o.room_numbers,
        so.start_date,
        so.end_date,
        so.room_qty,
        so.currency,
        IFNULL(so.payable_amt,0) payable_amt,
        CAST( CEILING( so.payable_amt * o.rate * 100 ) / 100 AS DECIMAL(18,2) ) AS payableOrgCurrencyAmt,
        IFNULL(so.paid_amt,0) paid_amt,
        CAST( CEILING( so.paid_amt * o.rate * 100 ) / 100 AS DECIMAL(18,2) ) AS paidOrgCurrencyAmt,
        IFNULL(so.unpaid_amt,0) unpaid_amt,
        CAST( CEILING( so.unpaid_amt * o.rate * 100 ) / 100 AS DECIMAL(18,2) ) AS unpaidOrgCurrencyAmt,
        IFNULL(so.pay_amt,0) pay_amt,
        CAST( CEILING( so.pay_amt * o.rate * 100 ) / 100 AS DECIMAL(18,2) ) AS payOrgCurrencyAmt,
        so.confirmation_status,
        so.order_create_date,
        o.supplier_order_code,
        o.confirmation_code,
        oo.order_code,
        oo.id orderId,
        o.rate sale_rate,
        o.commission,
        o.additional_charges,
        o.refund_fee,
        ifnull(so.payable_amt, 0) - ifnull(o.additional_charges, 0) + ifnull(o.commission, 0) sum_base_price
        FROM f_supplier_statement_order so
        LEFT JOIN o_supply_order o ON so.supply_order_code = o.supply_order_code
        LEFT JOIN o_order oo ON o.order_id = oo.id
        WHERE so.statement_id=#{statementId}
        <if test="supplyOrderCode!=null and supplyOrderCode!=''">
            AND so.supply_order_code like concat(concat('%',#{supplyOrderCode}),'%')
        </if>
        <choose>
            <when test="dateQueryType==0">
                <if test="startDate!=null and startDate!=''">
                    AND so.order_create_date >= #{startDate}
                </if>
                <if test="endDate!=null and endDate!=''">
                    AND so.order_create_date &lt; date_add(#{endDate},interval 1 day)
                </if>
            </when>
            <when test="dateQueryType==1">
                <if test="startDate!=null and startDate!=''">
                    AND so.start_date >= #{startDate}
                </if>
                <if test="endDate!=null and endDate!=''">
                    AND so.start_date &lt;= #{endDate}
                </if>
            </when>
            <when test="dateQueryType==2">
                <if test="startDate!=null and startDate!=''">
                    AND so.end_date >= #{startDate}
                </if>
                <if test="endDate!=null and endDate!=''">
                    AND so.end_date &lt;= #{endDate}
                </if>
            </when>
        </choose>
        ORDER BY so.id DESC
    </select>

    <select id="selectOrderGuests" resultType="com.tiangong.order.remote.response.GuestDTO">
        SELECT
            t.name as name,
            t.room_number as roomNumber
        FROM o_guest t WHERE t.order_id = #{orderId}
    </select>

    <select id="queryCustomerBillInfo" resultType="com.tiangong.domain.resp.StatementDTO">
        SELECT
            statement_code statementCode,
            agent_name agentName,
            currency currency,
            statement_amt statementAmt,
            settlement_date settlementDate,
            created_dt createdDt,
            settlement_strategy settlementStrategy
        FROM f_agent_statement
        WHERE id = #{statementId}
    </select>
</mapper>