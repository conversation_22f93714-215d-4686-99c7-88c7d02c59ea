package com.tiangong.listener;

import com.tiangong.keys.RedisKey;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.listener.PatternTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.data.redis.listener.adapter.MessageListenerAdapter;

/**
 * 队列监听配置
 * <AUTHOR>
 */
@Configuration
@AutoConfigureAfter({ConsumeReportMessage.class})
public class SubscriberReportConfig {

    @Bean
    public MessageListenerAdapter getReportMessageListenerAdapter(ConsumeReportMessage automaticReportStatistics) {
        return new MessageListenerAdapter(automaticReportStatistics);
    }

    @Bean
    public RedisMessageListenerContainer getReportMessageListenerContainer(RedisConnectionFactory redisConnectionFactory, @Qualifier("getReportMessageListenerAdapter") MessageListenerAdapter messageListenerAdapter) {
        RedisMessageListenerContainer redisMessageListenerContainer = new RedisMessageListenerContainer();
        redisMessageListenerContainer.setConnectionFactory(redisConnectionFactory);
        redisMessageListenerContainer.addMessageListener(messageListenerAdapter, new PatternTopic(RedisKey.REPORT_STATISTICS));
        return redisMessageListenerContainer;
    }

}
