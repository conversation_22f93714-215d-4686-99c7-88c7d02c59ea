package com.tiangong.service.impl;

import com.tiangong.common.Response;
import com.tiangong.domain.req.SalesDetailReq;
import com.tiangong.domain.req.SummaryReq;
import com.tiangong.domain.resp.*;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.dto.hotel.base.HotelInfoCollectionDTO;
import com.tiangong.dto.hotel.base.HotelInfoCollectionReq;
import com.tiangong.enums.*;
import com.tiangong.hotel.BaseHotelInfoUrl;
import com.tiangong.hotel.remote.HotelRemote;
import com.tiangong.keys.RedisKey;
import com.tiangong.mapper.ReportStatisticsMapper;
import com.tiangong.mapper.SupplierCompanyMapper;
import com.tiangong.order.enums.ConfirmationStatusEnum;
import com.tiangong.organization.remote.dto.SupplierAddDTO;
import com.tiangong.organization.remote.dto.SupplierSelectDTO;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.service.ReportStatisticsService;
import com.tiangong.statistics.dto.QuerySaleStatisticsDTO;
import com.tiangong.util.CollUtilX;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.aggregation.ConvertOperators;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 报表服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ReportStatisticsServiceImpl implements ReportStatisticsService {

    @Autowired
    private ReportStatisticsMapper reportStatisticsMapper;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private SupplierCompanyMapper supplierCompanyMapper;

    @Autowired
    private HotelRemote hotelRemote;

    @Override
    public List<String> queryAllOrderCode(QuerySaleStatisticsDTO request) {
        return reportStatisticsMapper.queryAllOrderCode(request);
    }

    /**
     * 初始化汇总计划
     *
     * @param orderCode 订单编码
     */
    @Override
    public void queryAllReport(String orderCode) {
        try {
            //查询订单相关
            OrderReportResp orderReportResp = reportStatisticsMapper.queryOrderReport(orderCode);

            //判断订单是否为空
            if(null == orderReportResp){
                log.info("订单编码: " + orderCode + " 订单数据为空，需要核对订单数据");
                return;
            }

            //判断商家转客户币种汇率是否为空
            //为空则终止报表计算
            if(null == orderReportResp.getSaleRate()){
                log.info("订单编码: " + orderCode + " 订单商家转客户币种汇率为空");
                return;
            }

            // 设置集团、品牌、星级
            if (null != orderReportResp.getHotelId()) {
                // 查询酒店基本信息
                HotelInfoCollectionReq hotelInfoReq = new HotelInfoCollectionReq();
                hotelInfoReq.setHotelIds(Collections.singletonList(orderReportResp.getHotelId()));
                hotelInfoReq.setLanguageType(LanguageTypeEnum.zh_CN.getValue());
                List<String> settings = new ArrayList<>();
                settings.add(BaseHotelInfoUrl.group);
                settings.add(BaseHotelInfoUrl.brand);
                settings.add(BaseHotelInfoUrl.hotelStar);
                hotelInfoReq.setSettings(settings);
                Response<HotelInfoCollectionDTO> response = hotelRemote.queryHotelInfo(hotelInfoReq);
                if (response.isSuccess() && response.getModel() != null) {
                    HotelInfoCollectionDTO hotelInfo = response.getModel();
                    if (hotelInfo.getGroup() != null) {
                        orderReportResp.setGroupName(hotelInfo.getGroup().getGroupName());
                    }
                    if (hotelInfo.getBrand() != null) {
                        orderReportResp.setGroupName(hotelInfo.getBrand().getBrandName());
                    }
                    if (hotelInfo.getHotelStar() != null && StrUtilX.isNotEmpty(hotelInfo.getHotelStar().getHotelStar())) {
                        try {
                            orderReportResp.setHotelRank(Integer.parseInt(hotelInfo.getHotelStar().getHotelStar()));
                        } catch (Exception e) {
                            log.error("转换酒店星级异常", e);
                        }
                    }
                }
            }

            //查询订单入住人信息
            List<OrderGuestResp> guest = reportStatisticsMapper.queryOrderGuest(orderReportResp.getOrderId());
            Map<Integer, String> guestMap = guest.stream()
                    .collect(Collectors.groupingBy(
                            OrderGuestResp::getRoomNumber,
                            Collectors.mapping(OrderGuestResp::getName, Collectors.joining(", "))
            ));

            //供货单应付总额
            BigDecimal suppleOrderTotalPayableAmt = new BigDecimal("0");
            BigDecimal suppleOrderTotalPayableOrgCurrencyAmt = new BigDecimal("0");

            //供应商奖励总额
            BigDecimal supplierTotalRewardAmt = new BigDecimal("0");
            BigDecimal supplierTotalRewardAmtCny = new BigDecimal("0");

            //供应商返佣总额
            BigDecimal supplierTotalRebateAmt = new BigDecimal("0");
            BigDecimal supplierTotalRebateOrgCurrencyAmt = new BigDecimal("0");

            //供货单未付总金额CNY
            BigDecimal supplyOrderTotalUnpaidAmtCny = new BigDecimal("0");
            //奖励未收总金额CNY
            BigDecimal totalRewardUnpaidAmtCny = new BigDecimal("0");
            //返佣未付总金额CNY
            BigDecimal totalRebateUnpaidAmtCny = new BigDecimal("0");


            /*
             * 查询供货单相关
             */
            List<SuppleOrderReportResp> suppleOrderReportList = reportStatisticsMapper.querySuppleOrderReport(orderCode);
            for (SuppleOrderReportResp suppleOrderReportResp :
                    suppleOrderReportList) {

                //供货单入住人信息是否为空
                //如果为空则取订单默认查询的入住人信息
                if(null != suppleOrderReportResp.getRoomNumbers()){
                    List<String> roomNuberList = StrUtilX.stringToList(suppleOrderReportResp.getRoomNumbers(),",");
                    StringBuilder guests = null;
                    for (String s : roomNuberList) {
                        if (null == guests) {
                            guests = new StringBuilder(guestMap.get(Integer.valueOf(s)));
                        } else {
                            guests.append(",").append(guestMap.get(Integer.valueOf(s)));
                        }
                    }
                    if (guests != null) {
                        suppleOrderReportResp.setSupplyOrderGuest(guests.toString());
                    }
                }

                //查询供货单财务相关
                SuppleOrderFinanceReportResp suppleOrderFinanceReportResp = reportStatisticsMapper.querySuppleOrderFinanceReport(suppleOrderReportResp.getSupplyOrderCode());

                if(null == suppleOrderFinanceReportResp){
                    log.error("供货单编码:  " + suppleOrderReportResp.getSupplyOrderCode() + "  供货单财务相关数据为空");
                    return;
                }

                suppleOrderReportResp.setSuppleOrderFinanceReportResp(suppleOrderFinanceReportResp);

                //供货单应付总额
                suppleOrderTotalPayableAmt = suppleOrderTotalPayableAmt.add(suppleOrderReportResp.getPayableAmt());
                //供货单应付总额CNY
                suppleOrderTotalPayableOrgCurrencyAmt = suppleOrderTotalPayableOrgCurrencyAmt.add(suppleOrderReportResp.getPayableOrgCurrencyAmt());

                //供应商奖励总额
                supplierTotalRewardAmt = supplierTotalRewardAmt.add(suppleOrderReportResp.getRewardAmt());
                //供应商奖励总额CNY
                supplierTotalRewardAmtCny = supplierTotalRewardAmtCny.add(suppleOrderReportResp.getRewardOrgCurrencyAmt());

                //供应商返佣总额
                supplierTotalRebateAmt = supplierTotalRebateAmt.add(suppleOrderReportResp.getRebateAmt());
                //供应商返佣总额CNY
                supplierTotalRebateOrgCurrencyAmt = supplierTotalRebateOrgCurrencyAmt.add(suppleOrderReportResp.getRebateOrgCurrencyAmt());


                //供货单未付金额CNY
                supplyOrderTotalUnpaidAmtCny = supplyOrderTotalUnpaidAmtCny.add(suppleOrderFinanceReportResp.getSupplyOrderUnpaidOrgCurrencyAmt());
                //奖励未收金额CNY
                totalRewardUnpaidAmtCny = totalRewardUnpaidAmtCny.add(suppleOrderFinanceReportResp.getRewardUnpaidOrgCurrencyAmt());
                //返佣未付金额CNY
                totalRebateUnpaidAmtCny = totalRebateUnpaidAmtCny.add(suppleOrderFinanceReportResp.getRebateUnpaidOrgCurrencyAmt());
            }
            //供应商未付总金额 = 供货单【“供货单未付CNY”】之和  -  【“供应商奖励未收CNY”】之和 + 【“供应商返佣未付CNY”】之和
            //orderReportResp.setSupplierTotalUnpaidOrgCurrencyAmt(supplyOrderTotalUnpaidAmtCny.subtract(totalRewardUnpaidAmtCny).add(totalRebateUnpaidAmtCny));
            orderReportResp.setSupplierTotalUnpaidOrgCurrencyAmt(supplyOrderTotalUnpaidAmtCny);

            //供货单应付总额
            orderReportResp.setSuppleOrderTotalPayableAmt(suppleOrderTotalPayableAmt);
            orderReportResp.setSuppleOrderTotalPayableOrgCurrencyAmt(suppleOrderTotalPayableOrgCurrencyAmt);

            //供应商奖励总额
            orderReportResp.setSupplierTotalRewardAmt(supplierTotalRewardAmt);
            orderReportResp.setSupplierTotalRewardOrgCurrencyAmt(supplierTotalRewardAmtCny);

            //供应商返佣总额
            orderReportResp.setSupplierTotalRebateAmt(supplierTotalRebateAmt);
            orderReportResp.setSupplierTotalRebateOrgCurrencyAmt(supplierTotalRebateOrgCurrencyAmt);

            //供应商应付总额CNY = 供货单应付总额CNY- 供应商奖励总额CNY + 供应商返佣总额CNY
            //orderReportResp.setSupplierTotalPayableOrgCurrencyAmount(suppleOrderTotalPayableOrgCurrencyAmt.subtract(supplierTotalRewardAmtCny).add(supplierTotalRebateOrgCurrencyAmt));
            orderReportResp.setSupplierTotalPayableOrgCurrencyAmount(suppleOrderTotalPayableOrgCurrencyAmt);

            //供应商应付总额 = 供货单应付总额- 供应商奖励总额 + 供应商返佣总额
            //orderReportResp.setSupplierTotalPayableAmount(suppleOrderTotalPayableAmt.subtract(supplierTotalRewardAmt).add(supplierTotalRebateAmt));
            orderReportResp.setSupplierTotalPayableAmount(suppleOrderTotalPayableAmt);

            //利润CNY = 订单应收CNY- 供应商应付总额CNY
            //BigDecimal profitOrgCurrencyAmt = orderReportResp.getReceivableOrgCurrencyAmt().subtract(orderReportResp.getSupplierTotalPayableOrgCurrencyAmount());
             //利润=订单总额-供货单总额+供货单奖励-供货单返佣
            BigDecimal profitOrgCurrencyAmt = orderReportResp.getReceivableOrgCurrencyAmt().subtract(orderReportResp.getSupplierTotalPayableOrgCurrencyAmount()).add(orderReportResp.getSupplierTotalRewardOrgCurrencyAmt()).subtract(orderReportResp.getSupplierTotalRebateOrgCurrencyAmt());
            orderReportResp.setProfitOrgCurrencyAmt(profitOrgCurrencyAmt);

            //利润 = 订单应收- 供应商应付总额
            //orderReportResp.setProfit(orderReportResp.getReceivableAmt().subtract(orderReportResp.getSupplierTotalPayableAmount()));
            //利润=订单总额-供货单总额+供货单奖励-供货单返佣
            orderReportResp.setProfit(orderReportResp.getReceivableAmt().subtract(orderReportResp.getSupplierTotalPayableAmount()).add(orderReportResp.getSupplierTotalRewardAmt()).subtract(orderReportResp.getSupplierTotalPayableAmount()));

            //利润率 = 利润（CNY）/ 客户（订单）应收（CNY）
            //向上取整两位数
            try {
                // 执行除法计算
                BigDecimal result = profitOrgCurrencyAmt.divide(orderReportResp.getReceivableOrgCurrencyAmt(), 4, RoundingMode.CEILING);
                // 将结果转为百分比形式
                BigDecimal percent = result.multiply(new BigDecimal("100"));
                orderReportResp.setProfitOrgCurrencyRate(percent);

            } catch (Exception e) {
                orderReportResp.setProfitOrgCurrencyRate(new BigDecimal("0.00"));
            }
            orderReportResp.setSuppleOrderReportList(suppleOrderReportList);

            //存在则更新，不存在就新增
            Query query = new Query(Criteria.where("orderCode").is(orderReportResp.getOrderCode()));
            mongoTemplate.remove(query, "report_orderReport");
            mongoTemplate.save(orderReportResp, "report_orderReport");
        } catch (Exception e) {
            log.error(orderCode + " ,统计异常", e);
            throw e;
        }
    }

    /**
     * 经营概况
     *
     * @param requestMap 入参条件
     */
    @Override
    public BusinessOverviewResp queryBusinessOverview(Map<String, String> requestMap) {
        /*
         * 查询指定时间数据
         */
        Criteria criteria = new Criteria();
        criteria.and("createdDt").gte(requestMap.get("startDate") + " 00:00:00").lte(requestMap.get("endDate") + " 23:59:59");

        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.group()
                        .sum(ConvertOperators.valueOf("$receivableOrgCurrencyAmt").convertToDecimal()).as("receivableOrgCurrencyAmt")        //应收金额
                        .sum(ConvertOperators.valueOf("$supplierTotalPayableOrgCurrencyAmount").convertToDecimal()).as("supplierTotalPayableOrgCurrencyAmount")  //供应商应付（CNY）
                        .sum(ConvertOperators.valueOf("$profitOrgCurrencyAmt").convertToDecimal()).as("profitOrgCurrencyAmt")  //利润
        );
        BusinessOverviewResp businessOverviewResp = mongoTemplate.aggregate(aggregation, OrderReportResp.class, BusinessOverviewResp.class).getUniqueMappedResult();

        //查询数据为空，直接返回空数据
        if (null == businessOverviewResp) {
            businessOverviewResp = new BusinessOverviewResp();
        }

        //间夜数单独汇总
        //基于前面的条件，增加只查询【已确认】、【已完成】的订单
        criteria.and("orderConfirmationStatus").in(ConfirmationStatusEnum.CONFIRMED.key,ConfirmationStatusEnum.DONE.key);
        Aggregation aggregation3 = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.group()
                        .sum(ConvertOperators.valueOf("$nightQty").convertToDecimal()).as("nightQty")//间夜数
        );
        BusinessOverviewResp businessOverviewResp3 = mongoTemplate.aggregate(aggregation3, OrderReportResp.class, BusinessOverviewResp.class).getUniqueMappedResult();
        //判断符合条件的间夜数是否存在
        if(null != businessOverviewResp3){
            businessOverviewResp.setNightQty(businessOverviewResp3.getNightQty());
        }

        //利润率 = 利润（CNY）/ 客户（订单）应收（CNY）
        //向上取整两位数
        try {
            // 执行除法计算
            BigDecimal result = businessOverviewResp.getProfitOrgCurrencyAmt().divide(businessOverviewResp.getReceivableOrgCurrencyAmt(), 4, RoundingMode.CEILING);
            // 将结果转为百分比形式
            BigDecimal percent = result.multiply(new BigDecimal("100"));
            businessOverviewResp.setProfitOrgCurrencyRate(percent);

        } catch (Exception e) {
            businessOverviewResp.setProfitOrgCurrencyRate(new BigDecimal("0.00"));
        }
        businessOverviewResp.setReceivableOrgCurrencyAmt(businessOverviewResp.getReceivableOrgCurrencyAmt().setScale(2, RoundingMode.CEILING));
        businessOverviewResp.setSupplierTotalPayableOrgCurrencyAmount(businessOverviewResp.getSupplierTotalPayableOrgCurrencyAmount().setScale(2, RoundingMode.CEILING));
        businessOverviewResp.setProfitOrgCurrencyAmt(businessOverviewResp.getProfitOrgCurrencyAmt().setScale(2, RoundingMode.CEILING));

        // 格式化日期为指定格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        /*
         * 查询近三个月财务概况
         */
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();

        // 计算往前推91天的日期
        LocalDate previousDate = currentDate.minusDays(91);

        // 获取当前日期的字符串表示
        String currentDateString = currentDate.format(formatter);

        // 获取往前推91天日期的字符串表示
        String previousDateString = previousDate.format(formatter);

        Criteria criteria2 = new Criteria();
        criteria2.and("createdDt").gte(previousDateString + " 00:00:00").lte(currentDateString + " 23:59:59");

        Aggregation aggregation2 = Aggregation.newAggregation(
                Aggregation.match(criteria2),
                Aggregation.group()
                        .sum(ConvertOperators.valueOf("$unreceivedOrgCurrencyAmt").convertToDouble()).as("unreceivedOrgCurrencyAmt")                    //客户未收
                        .sum(ConvertOperators.valueOf("$supplierTotalUnpaidOrgCurrencyAmt").convertToDouble()).as("supplierTotalUnpaidOrgCurrencyAmt")  //供应商未付
        );
        BusinessOverviewResp businessOverviewResp2 = mongoTemplate.aggregate(aggregation2, OrderReportResp.class, BusinessOverviewResp.class).getUniqueMappedResult();
        if (null != businessOverviewResp2) {
            businessOverviewResp.setUnreceivedOrgCurrencyAmt(businessOverviewResp2.getUnreceivedOrgCurrencyAmt().setScale(2, RoundingMode.CEILING));
            businessOverviewResp.setSupplierTotalUnpaidOrgCurrencyAmt(businessOverviewResp2.getSupplierTotalUnpaidOrgCurrencyAmt().setScale(2, RoundingMode.CEILING));
        }

        return businessOverviewResp;
    }


    /**
     * 列表条件组装
     *
     * @param request 条件入参
     * @return 条件拼装
     */
    private Query conditionSales(SalesDetailReq request, List<String> supplierCodeList) {
        Query query = new Query();

        //下单时间
        if (request.getDateQueryType() != null && request.getDateQueryType().equals(DateQueryTypeEnum.DATE_QUERY_TYPE_0.type)
                && StrUtilX.isNotEmpty(request.getStartDate()) && StrUtilX.isNotEmpty(request.getEndDate())) {
            request.setStartDate(request.getStartDate() + " 00:00:00");
            request.setEndDate(request.getEndDate() + " 23:59:59");
            query.addCriteria(Criteria.where("createdDt").gte(request.getStartDate()).lte(request.getEndDate()));
        }

        //入住日期
        if (request.getDateQueryType() != null && request.getDateQueryType().equals(DateQueryTypeEnum.DATE_QUERY_TYPE_1.type)
                && StrUtilX.isNotEmpty(request.getStartDate()) && StrUtilX.isNotEmpty(request.getEndDate())) {
            query.addCriteria(Criteria.where("checkInDate").gte(request.getStartDate()).lte(request.getEndDate()));
        }

        //离店日期
        if (request.getDateQueryType() != null && request.getDateQueryType().equals(DateQueryTypeEnum.DATE_QUERY_TYPE_2.type)
                && StrUtilX.isNotEmpty(request.getStartDate()) && StrUtilX.isNotEmpty(request.getEndDate())) {
            query.addCriteria(Criteria.where("checkOutDate").gte(request.getStartDate()).lte(request.getEndDate()));
        }

        //酒店id
        if (null != request.getHotelId()) {
            query.addCriteria(Criteria.where("hotelId").is(request.getHotelId()));
        }

        //订单编码
        if (StrUtilX.isNotEmpty(request.getOrderCode())) {
            query.addCriteria(Criteria.where("orderCode").regex("^.*" + request.getOrderCode() + ".*$"));
        }

        //客户编码
        if (StrUtilX.isNotEmpty(request.getAgentCode())) {
            query.addCriteria(Criteria.where("agentCode").is(request.getAgentCode()));
        }
//        else if (StrUtilX.isNotEmpty(request.getAgentName())) { //客户名称
//            // 对 agentName 进行转义处理
//            String agentName = Pattern.quote(request.getAgentName());
//            Pattern pattern = Pattern.compile(".*" + agentName + ".*", Pattern.CASE_INSENSITIVE);
//            query.addCriteria(Criteria.where("agentName").regex(pattern));
//        }

        //订单确认状态
        if (null != request.getOrderConfirmationStatus()) {
            query.addCriteria(Criteria.where("orderConfirmationStatus").is(request.getOrderConfirmationStatus()));
        }

        //订单应收(0:不等于0   1:等于0)
        if (null != request.getOrderReceivableType() && request.getOrderReceivableType() == 0) {
            //查询订单应收不等于0的数据
            query.addCriteria(Criteria.where("receivableAmt").ne("0.00"));
        }
        if (null != request.getOrderReceivableType() && request.getOrderReceivableType() == 1) {
            //查询订单应收等于0的数据
            query.addCriteria(Criteria.where("receivableAmt").is("0.00"));
        }

        //酒店确认号
        if (StrUtilX.isNotEmpty(request.getConfirmationCode())) {
            query.addCriteria(Criteria.where("suppleOrderReportList.confirmationCode").regex("^.*" + request.getConfirmationCode() + ".*$"));
        }

        //归属人
        if (StrUtilX.isNotEmpty(request.getOrderOwnerName())) {
            query.addCriteria(Criteria.where("orderOwnerName").regex("^.*" + request.getOrderOwnerName() + ".*$"));
        }

        //是否手工单
        if (null != request.getIsManualOrder()) {
            query.addCriteria(Criteria.where("isManualOrder").is(request.getIsManualOrder()));
        }

        //渠道编码
        if (StrUtilX.isNotEmpty(request.getAgentType())) {
            query.addCriteria(Criteria.where("agentType").is(request.getAgentType()));
        }

        //国，省，市
        if(StrUtilX.isNotEmpty(request.getCountryProvinceCityCode()) && ObjectUtils.isNotEmpty(request.getDataType())){
            switch (request.getDataType()) {
                case 2:
                    query.addCriteria(Criteria.where("cityCode").is(request.getCountryProvinceCityCode()));
                    break;
                case 9:
                    query.addCriteria(Criteria.where("provinceCode").is(request.getCountryProvinceCityCode()));
                    break;
                case 10:
                    query.addCriteria(Criteria.where("countryCode").is(request.getCountryProvinceCityCode()));
                    break;
            }
        }

        //供货单号
        if (StrUtilX.isNotEmpty(request.getSupplyOrderCode())) {
            query.addCriteria(Criteria.where("suppleOrderReportList.supplyOrderCode").regex("^.*" + request.getSupplyOrderCode() + ".*$"));
        }

        //供货单应付(0:不等于0   1:等于0)
        if (null != request.getSupplyOrderPayableType() && request.getSupplyOrderPayableType() == 0) {
            //查询供货单应付不等于0的数据
            query.addCriteria(Criteria.where("suppleOrderTotalPayableAmt").ne("0.00"));
        }
        if (null != request.getSupplyOrderPayableType() && request.getSupplyOrderPayableType() == 1) {
            //查询供货单应付等于0的数据
            query.addCriteria(Criteria.where("suppleOrderTotalPayableAmt").is("0.00"));
        }

        //供应商编码
        if (StrUtilX.isNotEmpty(request.getSupplierCode())) {
            query.addCriteria(Criteria.where("suppleOrderReportList.supplierCode").is(request.getSupplierCode()));
        } else if (CollUtilX.isNotEmpty(supplierCodeList)) {
            query.addCriteria(Criteria.where("suppleOrderReportList.supplierCode").in(supplierCodeList));
        }
//        else if (StrUtilX.isNotEmpty(request.getSupplierName())) { //供应商名称
//            // 对 supplierName 进行转义处理
//            String supplierName = Pattern.quote(request.getSupplierName());
//            Pattern pattern = Pattern.compile(".*" + supplierName + ".*", Pattern.CASE_INSENSITIVE);
//            query.addCriteria(Criteria.where("suppleOrderReportList.supplierName").regex(pattern));
//        }

        //供货单确认状态
        if (null != request.getSupplyOrderConfirmationStatus()) {
            //供货单里面的条件
            query.addCriteria(Criteria.where("suppleOrderReportList.supplyOrderConfirmationStatus").is(request.getSupplyOrderConfirmationStatus()));
        }

        //支付类型（订单类型）
        if (null != request.getPayMethod()) {
            query.addCriteria(Criteria.where("payMethod").is(request.getPayMethod()));
        }

        //是否协议订单
        if (null != request.getSupplierLabel() && request.getSupplierLabel() == 0) {
            List<Integer> supplierLabelList = new ArrayList<>();
            supplierLabelList.add(LabelTypeEnum.PLATFORM.getNo());
            supplierLabelList.add(LabelTypeEnum.GROUP_ROOM.getNo());
            supplierLabelList.add(LabelTypeEnum.FIXED_PRICE_AGREEMENT.getNo());
            supplierLabelList.add(LabelTypeEnum.DISCOUNT_PRICE_AGREEMENT.getNo());
            query.addCriteria(Criteria.where("supplierLabel").nin(supplierLabelList));
        }
        if (null != request.getSupplierLabel() && request.getSupplierLabel() == 1) {
            List<Integer> supplierLabelList = new ArrayList<>();
            supplierLabelList.add(LabelTypeEnum.PLATFORM.getNo());
            supplierLabelList.add(LabelTypeEnum.GROUP_ROOM.getNo());
            supplierLabelList.add(LabelTypeEnum.FIXED_PRICE_AGREEMENT.getNo());
            supplierLabelList.add(LabelTypeEnum.DISCOUNT_PRICE_AGREEMENT.getNo());
            query.addCriteria(Criteria.where("supplierLabel").in(supplierLabelList));
        }


        //是否亏损单
        if (null != request.getDeficitStatus()) {
            //DeficitStatus=1,亏损单，则利润小于0
            if (request.getDeficitStatus() == 1) {
                query.addCriteria(Criteria.where("profitOrgCurrencyAmt").lt(BigDecimal.ZERO));
            }
            //DeficitStatus=0,非亏损单，则利润大于或等于0
            if (request.getDeficitStatus() == 0) {
                query.addCriteria(Criteria.where("profitOrgCurrencyAmt").gte(BigDecimal.ZERO));
            }
        }
        //按下单时间倒序
        query.with(Sort.by(Sort.Direction.DESC, "createdDt"));
        return query;
    }

    /**
     * 汇总条件组装
     *
     * @param request 请求参数
     * @param queryType 查询类型（1： 常规查询   2:独立查询间夜数）
     * @return 返回查询条件
     */
    private Criteria generateAggregation(SalesDetailReq request,Integer queryType) {
        Criteria criteria = new Criteria();

        // 下单日期
        if (request.getDateQueryType() != null && request.getDateQueryType().equals(DateQueryTypeEnum.DATE_QUERY_TYPE_0.type)
                && StrUtilX.isNotEmpty(request.getStartDate()) && StrUtilX.isNotEmpty(request.getEndDate())) {
            request.setStartDate(request.getStartDate() + " 00:00:00");
            request.setEndDate(request.getEndDate() + " 23:59:59");
            criteria.and("createdDt").gte(request.getStartDate()).lte(request.getEndDate());
        }
        // 入住日期
        if (request.getDateQueryType() != null && request.getDateQueryType().equals(DateQueryTypeEnum.DATE_QUERY_TYPE_1.type)
                && StrUtilX.isNotEmpty(request.getStartDate()) && StrUtilX.isNotEmpty(request.getEndDate())) {
            criteria.and("checkInDate").gte(request.getStartDate()).lte(request.getEndDate());
        }
        // 离店日期
        if (request.getDateQueryType() != null && request.getDateQueryType().equals(DateQueryTypeEnum.DATE_QUERY_TYPE_2.type)
                && StrUtilX.isNotEmpty(request.getStartDate()) && StrUtilX.isNotEmpty(request.getEndDate())) {
            criteria.and("checkOutDate").gte(request.getStartDate()).lte(request.getEndDate());
        }

        // 酒店id
        if (null != request.getHotelId()) {
            criteria.and("hotelId").is(request.getHotelId());
        }
        // 订单编码
        if (StrUtilX.isNotEmpty(request.getOrderCode())) {
            Pattern pattern = Pattern.compile(".*" + request.getOrderCode() + ".*", Pattern.CASE_INSENSITIVE);
            criteria.and("orderCode").regex(pattern);
        }
        // 客户编码
        if (StrUtilX.isNotEmpty(request.getAgentCode())) {
            criteria.and("agentCode").is(request.getAgentCode());
        }

        if(queryType == 1){
            // 订单状态
            if (null != request.getOrderConfirmationStatus()) {
                criteria.and("orderConfirmationStatus").is(request.getOrderConfirmationStatus());
            }
        } else if (queryType == 2) {
            //针对汇总统计情况
            //存在订单状态条件时，只允许查【已确认】和【已完成】数据
            if (null != request.getOrderConfirmationStatus() &&
                    (request.getOrderConfirmationStatus().equals(ConfirmationStatusEnum.CONFIRMED.key) ||
                            request.getOrderConfirmationStatus().equals(ConfirmationStatusEnum.DONE.key)
                    )) {
                criteria.and("orderConfirmationStatus").is(request.getOrderConfirmationStatus());
            }else if(null == request.getOrderConfirmationStatus()){
                //request.getOrderConfirmationStatus() == null，则查询全部，默认只查询【已确认】和【已完成】订单
                criteria.and("orderConfirmationStatus").in(ConfirmationStatusEnum.CONFIRMED.key,ConfirmationStatusEnum.DONE.key);
            }else{
                //否则，不能查询到数据
                criteria.and("orderConfirmationStatus").is(-1);
            }
        }

        // 订单应收(0:不等于0   1:等于0)
        if (null != request.getOrderReceivableType()) {
            if (request.getOrderReceivableType() == 0) {
                //不等于0
                criteria.and("receivableAmt").ne(0);
            } else if (request.getOrderReceivableType() == 1) {
                //等于0
                criteria.and("receivableAmt").is(0);
            }
        }

        //酒店确认号
        if (StrUtilX.isNotEmpty(request.getConfirmationCode())) {
            Pattern pattern = Pattern.compile(".*" + request.getConfirmationCode() + ".*", Pattern.CASE_INSENSITIVE);
            criteria.and("suppleOrderReportList.confirmationCode").regex(pattern);

        }
        //归属人
        if (StrUtilX.isNotEmpty(request.getOrderOwnerName())) {
            Pattern pattern = Pattern.compile(".*" + request.getOrderOwnerName() + ".*", Pattern.CASE_INSENSITIVE);
            criteria.and("orderOwnerName").regex(pattern);
        }

        //供货单应付(0:不等于0   1:等于0)
        if (null != request.getSupplyOrderPayableType()) {
            if (request.getOrderReceivableType() == 0) {
                //不等于0
                criteria.and("suppleOrderTotalPayableAmt").ne(0);
            } else if (request.getOrderReceivableType() == 1) {
                //等于0
                criteria.and("suppleOrderTotalPayableAmt").is(0);
            }
        }

        //供应商编码
        if (StrUtilX.isNotEmpty(request.getSupplierCode())) {
            criteria.and("supplierCode").is(request.getSupplierCode());
        }

        //供货单号
        if (StrUtilX.isNotEmpty(request.getSupplyOrderCode())) {
            Pattern pattern = Pattern.compile(".*" + request.getSupplyOrderCode() + ".*", Pattern.CASE_INSENSITIVE);
            criteria.and("supplyOrderCode").regex(pattern);
        }

        //供货单确认状态
        if (null != request.getSupplyOrderConfirmationStatus()) {
            criteria.and("suppleOrderReportList.supplyOrderConfirmationStatus").is(request.getSupplyOrderConfirmationStatus());
        }

        //支付类型（订单类型）
        if (null != request.getPayMethod()) {
            criteria.and("payMethod").is(request.getPayMethod());
        }

        //是否协议订单
        if (null != request.getSupplierLabel() && request.getSupplierLabel() == 0) {
            //0：否，非协议单，则不等于3
            criteria.and("supplierLabel").ne(3);
        }
        if (null != request.getSupplierLabel() && request.getSupplierLabel() == 1) {
            //1：是，协议订单，则等于3
            criteria.and("supplierLabel").is(3);
        }


        //是否亏损单
        if (null != request.getDeficitStatus()) {
            //DeficitStatus=1,亏损单，则利润小于0
            if (request.getDeficitStatus() == 1) {
                criteria.and("profitOrgCurrencyAmt").lt(BigDecimal.ZERO);
            }
            //DeficitStatus=0,非亏损单，则利润大于或等于0
            if (request.getDeficitStatus() == 0) {
                criteria.and("profitOrgCurrencyAmt").gte(BigDecimal.ZERO);
            }
        }
        return criteria;
    }

    /**
     * 销售列表统计汇总
     *
     * @param request 请求参数
     * @return 返回销售报表汇总数据
     */
    @Override
    public SaleStatisticsDTO querySalesStatistics(SalesDetailReq request) {
        SaleStatisticsDTO saleStatisticsDTO = new SaleStatisticsDTO();
        try {
            //销售报表汇总查询条件
            Criteria criteria = generateAggregation(request,1);
            Aggregation aggregation = Aggregation.newAggregation(
                    Aggregation.match(criteria),
                    Aggregation.group()
                            .sum(ConvertOperators.valueOf("$receivableOrgCurrencyAmt").convertToDecimal()).as("saleAmt")        //销售额（CNY）== 应收金额
                            .sum(ConvertOperators.valueOf("$profitOrgCurrencyAmt").convertToDecimal()).as("profit")                //利润（CNY）
                            .sum(ConvertOperators.valueOf("$unreceivedOrgCurrencyAmt").convertToDecimal()).as("unreceivedAmt")  //未收金额（CNY）
            );
            saleStatisticsDTO = mongoTemplate.aggregate(aggregation, OrderReportResp.class, SaleStatisticsDTO.class).getUniqueMappedResult();

            if (null == saleStatisticsDTO) {
                return new SaleStatisticsDTO();
            }

            //间夜数单独汇总
            Criteria criteria2 = generateAggregation(request,2);
            Aggregation aggregation2 = Aggregation.newAggregation(
                    Aggregation.match(criteria2),
                    Aggregation.group()
                            .sum(ConvertOperators.valueOf("$nightQty").convertToDecimal()).as("saleNightQty")           //销售间夜数
            );
            SaleStatisticsDTO saleStatisticsDTO2 = mongoTemplate.aggregate(aggregation2, OrderReportResp.class, SaleStatisticsDTO.class).getUniqueMappedResult();
            if(null != saleStatisticsDTO2){
                saleStatisticsDTO.setSaleNightQty(saleStatisticsDTO2.getSaleNightQty());
            }


            saleStatisticsDTO.setSaleAmt(saleStatisticsDTO.getSaleAmt().setScale(2, RoundingMode.CEILING));
            saleStatisticsDTO.setProfit(saleStatisticsDTO.getProfit().setScale(2, RoundingMode.CEILING));
            saleStatisticsDTO.setUnreceivedAmt(saleStatisticsDTO.getUnreceivedAmt().setScale(2, RoundingMode.CEILING));


            //判断都不为空的情况
            if (null != saleStatisticsDTO.getProfit() && null != saleStatisticsDTO.getSaleAmt()) {
                try {
                    // 执行除法计算
                    BigDecimal result = saleStatisticsDTO.getProfit().divide(saleStatisticsDTO.getSaleAmt(), 4, RoundingMode.CEILING);
                    // 将结果转为百分比形式
                    BigDecimal percent = result.multiply(new BigDecimal("100"));
                    saleStatisticsDTO.setProfitRate(percent);
                } catch (Exception e) {
                    saleStatisticsDTO.setProfitRate(new BigDecimal("0"));
                }
            }
            return saleStatisticsDTO;
        } catch (Exception e) {
            log.error("列表统计汇总数据异常：", e);
            return saleStatisticsDTO;
        }
    }

    /**
     * 查询销售统计报表
     *
     * @param request 请求参数
     * @return 返回报表数据
     */
    @Override
    public PaginationSupportDTO<OrderReportResp> querySalesDetailStatistics(SalesDetailReq request) {
        List<String> supplierCodeList = null;
        if (StrUtilX.isEmpty(request.getSupplierCode()) && (request.getOneLevelChannelType() != null || request.getTwoLevelChannelType() != null || StrUtilX.isNotEmpty(request.getThreeLevelChannelType()))) {
            SupplierSelectDTO supplierSelectDTO = new SupplierSelectDTO();
            supplierSelectDTO.setOneLevelChannelType(request.getOneLevelChannelType());
            supplierSelectDTO.setTwoLevelChannelType(request.getTwoLevelChannelType());
            supplierSelectDTO.setThreeLevelChannelType(request.getThreeLevelChannelType());
            supplierCodeList = supplierCompanyMapper.selectSupplierCodeList(supplierSelectDTO);
            if (CollUtilX.isEmpty(supplierCodeList)) {
                PaginationSupportDTO<OrderReportResp> paginationSupport = new PaginationSupportDTO<>();
                paginationSupport.setItemList(new ArrayList<>());
                paginationSupport.setPageSize(request.getPageSize());
                paginationSupport.setTotalCount(0);
                paginationSupport.setTotalPage(0);
                paginationSupport.setCurrentPage(request.getCurrentPage());
                return paginationSupport;
            }
        }
        // 解析订单来源参数
        if (request.getOrderSource() != null) {
            if (OrderSourceEnum.MANUAL.key == request.getOrderSource()) {
                request.setIsManualOrder(1);
            } else if (OrderSourceEnum.GDP.key == request.getOrderSource()) {
                request.setAgentType(ChannelEnum.B2B.key);
                request.setIsManualOrder(0);
            } else if (OrderSourceEnum.API.key == request.getOrderSource()) {
                request.setAgentType(ChannelEnum.DHUB.key);
                request.setIsManualOrder(0);
            }
        }
        //销售报表列表查询条件组装
        Query query = conditionSales(request, supplierCodeList);
        // 分页之前查询总记录数
        long totalCount = mongoTemplate.count(query, "report_orderReport");
        // 设置分页
        query.skip((long) (request.getCurrentPage() - 1) * request.getPageSize()).limit(request.getPageSize());

        List<OrderReportResp> list = mongoTemplate.find(query, OrderReportResp.class, "report_orderReport");
        if (CollUtilX.isNotEmpty(list)) {
            Set<String> supplyCodes = new HashSet<>();
            for (OrderReportResp orderReportResp : list) {
                for (SuppleOrderReportResp resp : orderReportResp.getSuppleOrderReportList()) {
                    supplyCodes.add(resp.getSupplierCode());
                }
            }
            // 获取供应商配置信息
            Map<String, SupplierAddDTO> supplierConfigMap = getSupplierConfig(new ArrayList<>(supplyCodes));
            // 设置一级二级三级渠道类型
            for (OrderReportResp orderReportResp : list) {
                for (SuppleOrderReportResp resp : orderReportResp.getSuppleOrderReportList()) {
                    SupplierAddDTO supplierAddDTO = supplierConfigMap.get(resp.getSupplierCode());
                    if (supplierAddDTO == null) {
                        continue;
                    }
                    resp.setOneLevelChannelType(supplierAddDTO.getOneLevelChannelType());
                    resp.setTwoLevelChannelType(supplierAddDTO.getTwoLevelChannelType());
                    resp.setThreeLevelChannelType(supplierAddDTO.getThreeLevelChannelType());
                }
                if (orderReportResp.getIsManualOrder() != null && orderReportResp.getIsManualOrder() == 1) {
                    orderReportResp.setOrderSource(OrderSourceEnum.MANUAL.key);
                } else if (StrUtilX.isNotEmpty(orderReportResp.getAgentType()) && ChannelEnum.B2B.key.equals(orderReportResp.getAgentType())) {
                    orderReportResp.setOrderSource(OrderSourceEnum.GDP.key);
                } else if (StrUtilX.isNotEmpty(orderReportResp.getAgentType()) && ChannelEnum.DHUB.key.equals(orderReportResp.getAgentType())) {
                    orderReportResp.setOrderSource(OrderSourceEnum.API.key);
                }
            }
        }

        //总页数默认为0
        int totalPages = 0;
        //总记录数不为0，则计算总页数
        if (totalCount != 0) {
            totalPages = (int) Math.ceil((double) totalCount / request.getPageSize());
        }
        PaginationSupportDTO<OrderReportResp> paginationSupport = new PaginationSupportDTO<>();
        paginationSupport.setItemList(list);
        paginationSupport.setPageSize(request.getPageSize());
        paginationSupport.setTotalCount(totalCount);
        paginationSupport.setTotalPage(totalPages);
        paginationSupport.setCurrentPage(request.getCurrentPage());
        return paginationSupport;
    }

    /**
     * 获取供应商配置信息
     */
    public Map<String, SupplierAddDTO> getSupplierConfig(List<String> supplyCodes) {
        Map<String, SupplierAddDTO> supplierMap = new HashMap<>();
        try {
            if (CollUtilX.isEmpty(supplyCodes)) {
                return supplierMap;
            }
            List<Object> supplierInfos = RedisTemplateX.hMultiGet(RedisKey.SUPPLY_ACCOUNT_CONFIG, supplyCodes);
            if (CollUtilX.isEmpty(supplierInfos)) {
                return supplierMap;
            }
            for (Object supplierInfo : supplierInfos) {
                SupplierAddDTO supplierAddDTO = StrUtilX.parseObject(supplierInfo, SupplierAddDTO.class);
                if (supplierAddDTO == null || StrUtilX.isEmpty(supplierAddDTO.getSupplierCode())) {
                    continue;
                }
                supplierMap.put(supplierAddDTO.getSupplierCode(), supplierAddDTO);
            }
        } catch (Exception e) {
            log.error("getSupplierConfig error!", e);
        }
        return supplierMap;
    }

    /**
     * 利润列表统计汇总
     *
     * @param request 请求参数
     * @return 返回销售报表汇总数据
     */
    @Override
    public ProfitStatisticsDTO queryProfitStatistics(SalesDetailReq request) {
        ProfitStatisticsDTO profitStatisticsDTO = new ProfitStatisticsDTO();
        try {
            //销售报表汇总查询条件
            Criteria criteria = generateAggregation(request,1);
            Aggregation aggregation = Aggregation.newAggregation(
                    Aggregation.match(criteria),
                    Aggregation.group()
                            .sum(ConvertOperators.valueOf("$receivableOrgCurrencyAmt").convertToDecimal()).as("receivableOrgCurrencyAmt")           //总订单金额
                            .sum(ConvertOperators.valueOf("$suppleOrderTotalPayableOrgCurrencyAmt").convertToDecimal()).as("suppleOrderTotalPayableOrgCurrencyAmt")       //总供货单金额
                            .sum(ConvertOperators.valueOf("$supplierTotalRewardOrgCurrencyAmt").convertToDecimal()).as("supplierTotalRewardOrgCurrencyAmt")            //总供应商奖励
                            .sum(ConvertOperators.valueOf("$supplierTotalRebateOrgCurrencyAmt").convertToDecimal()).as("supplierTotalRebateOrgCurrencyAmt")     //总供应商返佣
                            .sum(ConvertOperators.valueOf("$supplierTotalPayableOrgCurrencyAmount").convertToDecimal()).as("supplierTotalPayableOrgCurrencyAmount") //总供应商应付
                            .sum(ConvertOperators.valueOf("$profitOrgCurrencyAmt").convertToDecimal()).as("profitOrgCurrencyAmt")  //总利润
            );
            profitStatisticsDTO = mongoTemplate.aggregate(aggregation, OrderReportResp.class, ProfitStatisticsDTO.class).getUniqueMappedResult();

            if (null == profitStatisticsDTO) {
                return new ProfitStatisticsDTO();
            }

            profitStatisticsDTO.setReceivableOrgCurrencyAmt(profitStatisticsDTO.getReceivableOrgCurrencyAmt().setScale(2, RoundingMode.CEILING));
            profitStatisticsDTO.setSupplierTotalPayableOrgCurrencyAmount(profitStatisticsDTO.getSupplierTotalPayableOrgCurrencyAmount().setScale(2, RoundingMode.CEILING));
            profitStatisticsDTO.setSupplierTotalRewardOrgCurrencyAmt(profitStatisticsDTO.getSupplierTotalRewardOrgCurrencyAmt().setScale(2, RoundingMode.CEILING));
            profitStatisticsDTO.setSupplierTotalRebateOrgCurrencyAmt(profitStatisticsDTO.getSupplierTotalRebateOrgCurrencyAmt().setScale(2, RoundingMode.CEILING));
            profitStatisticsDTO.setSuppleOrderTotalPayableOrgCurrencyAmt(profitStatisticsDTO.getSuppleOrderTotalPayableOrgCurrencyAmt().setScale(2, RoundingMode.CEILING));

            try{
                // 执行除法计算
                BigDecimal result = profitStatisticsDTO.getProfitOrgCurrencyAmt().divide(profitStatisticsDTO.getReceivableOrgCurrencyAmt(), 4, RoundingMode.CEILING);
                // 将结果转为百分比形式
                BigDecimal percent = result.multiply(new BigDecimal("100"));
                profitStatisticsDTO.setProfitRate(percent);
            }catch (Exception e){
                profitStatisticsDTO.setProfitRate(new BigDecimal("0"));
            }
            return profitStatisticsDTO;
        } catch (Exception e) {
            log.error("列表统计汇总数据异常：", e);
            return profitStatisticsDTO;
        }
    }

    /**
     * 概况统计
     *
     * @param summaryReq
     * @return
     */
    @Override
    public PaginationSupportDTO<OrderReportResp> querySummaryStatistics(SummaryReq summaryReq) {
        Query query = conditionSummary(summaryReq);
        // 分页之前查询总记录数
        long totalCount = mongoTemplate.count(query, "report_orderReport");
        // 设置分页
        query.skip((long) (summaryReq.getCurrentPage() - 1) * summaryReq.getPageSize()).limit(summaryReq.getPageSize());

        //查询符合数据的记录
        List<OrderReportResp> list = mongoTemplate.find(query, OrderReportResp.class, "report_orderReport");

        //总页数默认为0
        int totalPages = 0;
        //总记录数不为0，则计算总页数
        if (totalCount != 0) {
            totalPages = (int) Math.ceil((double) totalCount / summaryReq.getPageSize());
        }
        PaginationSupportDTO<OrderReportResp> paginationSupport = new PaginationSupportDTO<>();
        paginationSupport.setItemList(list);
        paginationSupport.setPageSize(summaryReq.getPageSize());
        paginationSupport.setTotalCount(totalCount);
        paginationSupport.setTotalPage(totalPages);
        paginationSupport.setCurrentPage(summaryReq.getCurrentPage());
        return paginationSupport;
    }

    @Override
    public PaginationSupportDTO<ReceivableStatisticsDTO> queryReceivableStatistics(SalesDetailReq request) {
        //应收报表查询条件
        Criteria criteria = conditionPayableReceivable(request);

        //应收报表总条数
        Aggregation aggregationCount = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.group("agentCode")
                        .first("agentCode").as("agentCode")
        );
        int totalCount = mongoTemplate.aggregate(aggregationCount, OrderReportResp.class, ReceivableStatisticsDTO.class).getMappedResults().size();

        //分页查询应收报表
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.group("agentCode")
                        .first("agentCode").as("agentCode")
                        .first("agentName").as("agentName")
                        .addToSet("agentManagerName").as("agentManagerNames") // 将agentManagerName作为集合存储
                        .first("orderSettlementType").as("orderSettlementType")
                        .first("saleCurrency").as("saleCurrency")
                        .sum(ConvertOperators.valueOf("$nightQty").convertToInt()).as("nightQty")
                        .sum(ConvertOperators.valueOf("$receivableAmt").convertToDecimal()).as("receivableAmt")    //总订单应收
                        .sum(ConvertOperators.valueOf("$receivedAmt").convertToDecimal()).as("receivedAmt")   //总订单实收
                        .sum(ConvertOperators.valueOf("$unreceivedAmt").convertToDecimal()).as("unreceivedAmt")
                        .sum(ConvertOperators.valueOf("$receivableOrgCurrencyAmt").convertToDecimal()).as("receivableOrgCurrencyAmt")
                        .sum(ConvertOperators.valueOf("$supplierTotalPayableOrgCurrencyAmount").convertToDecimal()).as("suppleOrderPayableOrgCurrencyAmt")
                        .sum(ConvertOperators.valueOf("$profitOrgCurrencyAmt").convertToDecimal()).as("profitOrgCurrencyAmt"),   //总订单未收
                Aggregation.sort(Sort.by(Sort.Direction.DESC, "agentCode")),
                Aggregation.skip((long) (request.getCurrentPage() - 1) * request.getPageSize()),
                Aggregation.limit(request.getPageSize())
        );
        AggregationResults<ReceivableStatisticsDTO> aggregateResult = mongoTemplate.aggregate(aggregation, OrderReportResp.class, ReceivableStatisticsDTO.class);
        List<ReceivableStatisticsDTO> statisticsList = aggregateResult.getMappedResults();

        // 多个客户经理用 "," 拼接
        for (ReceivableStatisticsDTO statisticsDTO : statisticsList) {
            StringBuilder names = new StringBuilder();
            for (String name : statisticsDTO.getAgentManagerNames()) {
                names.append(name).append(",");
            }
            if (names.length() > 0){
                statisticsDTO.setAgentManagerName(names.deleteCharAt(names.length()-1).toString());
            }
        }

        //总页数默认为0
        int totalPages = 0;
        //总记录数不为0，则计算总页数
        if (totalCount != 0) {
            totalPages = (int) Math.ceil((double) totalCount / request.getPageSize());
        }
        PaginationSupportDTO<ReceivableStatisticsDTO> paginationSupport = new PaginationSupportDTO<>();
        paginationSupport.setItemList(statisticsList);
        paginationSupport.setPageSize(request.getPageSize());
        paginationSupport.setTotalCount(totalCount);
        paginationSupport.setTotalPage(totalPages);
        paginationSupport.setCurrentPage(request.getCurrentPage());
        return paginationSupport;
    }

    @Override
    public ReceivableAggregateDTO queryReceivableAggregate(SalesDetailReq request) {
        ReceivableAggregateDTO receivableAggregateDTO = new ReceivableAggregateDTO();
        try {
            //应收报表查询条件
            Criteria criteria = conditionPayableReceivable(request);
            Aggregation aggregation = Aggregation.newAggregation(
                    Aggregation.match(criteria),
                    Aggregation.group()
                            .sum(ConvertOperators.valueOf("$receivableOrgCurrencyAmt").convertToDecimal()).as("receivableAmtTotal")           //总订单应收
                            .sum(ConvertOperators.valueOf("$receivedOrgCurrencyAmt").convertToDecimal()).as("receivedAmtTotal")       //总订单实收
                            .sum(ConvertOperators.valueOf("$unreceivedOrgCurrencyAmt").convertToDecimal()).as("unreceivedAmtTotal")            //总订单未收
            );
            receivableAggregateDTO = mongoTemplate.aggregate(aggregation, OrderReportResp.class, ReceivableAggregateDTO.class).getUniqueMappedResult();

            if (null == receivableAggregateDTO) {
                return new ReceivableAggregateDTO();
            }

            receivableAggregateDTO.setReceivableAmtTotal(receivableAggregateDTO.getReceivableAmtTotal().setScale(2, RoundingMode.CEILING));
            receivableAggregateDTO.setReceivedAmtTotal(receivableAggregateDTO.getReceivedAmtTotal().setScale(2, RoundingMode.CEILING));
            receivableAggregateDTO.setUnreceivedAmtTotal(receivableAggregateDTO.getUnreceivedAmtTotal().setScale(2, RoundingMode.CEILING));
            return receivableAggregateDTO;
        } catch (Exception e) {
            log.error("应收报表统计汇总数据异常：", e);
            return receivableAggregateDTO;
        }
    }

    @Override
    public PaginationSupportDTO<PayableStatisticsResp> queryPayableStatistics(SalesDetailReq request) {
        //应付报表查询条件
        Criteria criteria = conditionPayableReceivable(request);
        Criteria criteriaSupplier = conditionPayableReceivableSupplier(request);

        //应付报表总条数
        Aggregation aggregationCount = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.unwind("suppleOrderReportList"),
                Aggregation.group("suppleOrderReportList.supplierCode")
                        .first("suppleOrderReportList.supplierCode").as("supplierCode")
        );
        int totalCount = mongoTemplate.aggregate(aggregationCount, OrderReportResp.class, PayableStatisticsDTO.class).getMappedResults().size();

        //分页查询应付报表
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.unwind("suppleOrderReportList"),
                Aggregation.match(criteriaSupplier),
                Aggregation.group("suppleOrderReportList.supplierCode")
                        .first("suppleOrderReportList.supplierName").as("supplierName")
                        .first("suppleOrderReportList.supplierCode").as("supplierCode")
                        .sum(ConvertOperators.valueOf("$suppleOrderReportList.supplyOrderNightQty").convertToInt()).as("supplyOrderNightQty")
                        .sum(ConvertOperators.valueOf("$suppleOrderReportList.payableOrgCurrencyAmt").convertToDecimal()).as("supplierOrderPayableOrgCurrencyAmt")
                        .sum(ConvertOperators.valueOf("$suppleOrderReportList.rewardOrgCurrencyAmt").convertToDecimal()).as("rewardOrgCurrencyAmt")
                        .sum(ConvertOperators.valueOf("$suppleOrderReportList.rebateOrgCurrencyAmt").convertToDecimal()).as("rebateOrgCurrencyAmt")
                        .sum(ConvertOperators.valueOf("$suppleOrderReportList.suppleOrderFinanceReportResp.rewardUnpaidOrgCurrencyAmt").convertToDecimal()).as("rewardUnpaidOrgCurrencyAmt")
                        .sum(ConvertOperators.valueOf("$suppleOrderReportList.suppleOrderFinanceReportResp.rebateUnpaidOrgCurrencyAmt").convertToDecimal()).as("rebateUnpaidOrgCurrencyAmt")
                        .sum(ConvertOperators.valueOf("$suppleOrderReportList.suppleOrderFinanceReportResp.supplyOrderUnpaidOrgCurrencyAmt").convertToDecimal()).as("supplierOrderUnpaidOrgCurrencyAmt"),
                Aggregation.sort(Sort.by(Sort.Direction.DESC, "supplierCode")),
                Aggregation.skip((long) (request.getCurrentPage() - 1) * request.getPageSize()),
                Aggregation.limit(request.getPageSize())
        );
        AggregationResults<PayableStatisticsDTO> aggregateResult = mongoTemplate.aggregate(aggregation, OrderReportResp.class, PayableStatisticsDTO.class);
        List<PayableStatisticsDTO> statisticsList = aggregateResult.getMappedResults();

        // 临时存放供应商编码
//        Set<String> supplierCodeSet = new HashSet<>();
        List<PayableStatisticsResp> statisticsRespList = new ArrayList<>();
        for (PayableStatisticsDTO payableStatisticsDTO : statisticsList) {
            PayableStatisticsResp payableStatisticsResp = new PayableStatisticsResp();
            BeanUtils.copyProperties(payableStatisticsDTO, payableStatisticsResp);
            //供应商应付总金额 = 供货单【“供货单应付CNY”】之和  -  【“供应商奖励应收CNY”】之和 + 【“供应商返佣应付CNY”】之和
            //payableStatisticsResp.setSupplierOrderPayableOrgCurrencyAmt(payableStatisticsDTO.getSupplierOrderPayableOrgCurrencyAmt().subtract(payableStatisticsDTO.getRewardOrgCurrencyAmt()).add(payableStatisticsDTO.getRebateOrgCurrencyAmt()));
            payableStatisticsResp.setSupplierOrderPayableOrgCurrencyAmt(payableStatisticsDTO.getSupplierOrderPayableOrgCurrencyAmt().setScale(2, RoundingMode.HALF_UP).toString());
            //供应商未付总金额 = 供货单【“供货单未付CNY”】之和  -  【“供应商奖励未收CNY”】之和 + 【“供应商返佣未付CNY”】之和
            //payableStatisticsResp.setSupplierOrderUnpaidOrgCurrencyAmt(payableStatisticsDTO.getSupplierOrderUnpaidOrgCurrencyAmt().subtract(payableStatisticsDTO.getRewardUnpaidOrgCurrencyAmt()).add(payableStatisticsDTO.getRebateUnpaidOrgCurrencyAmt()));
            payableStatisticsResp.setSupplierOrderUnpaidOrgCurrencyAmt(payableStatisticsDTO.getSupplierOrderUnpaidOrgCurrencyAmt().setScale(2, RoundingMode.HALF_UP).toString());
            payableStatisticsResp.setSupplierOrderPaidOrgCurrencyAmt(payableStatisticsDTO.getSupplierOrderPayableOrgCurrencyAmt().subtract(payableStatisticsDTO.getSupplierOrderUnpaidOrgCurrencyAmt()).setScale(2, RoundingMode.HALF_UP).toString());
            statisticsRespList.add(payableStatisticsResp);
//            supplierCodeSet.add(payableStatisticsResp.getSupplierCode());
        }

        //总页数默认为0
        int totalPages = 0;
        //总记录数不为0，则计算总页数
        if (totalCount != 0) {
            totalPages = (int) Math.ceil((double) totalCount / request.getPageSize());
        }
        PaginationSupportDTO<PayableStatisticsResp> paginationSupport = new PaginationSupportDTO<>();
        paginationSupport.setItemList(statisticsRespList);
        paginationSupport.setPageSize(request.getPageSize());
        paginationSupport.setTotalCount(totalCount);
        paginationSupport.setTotalPage(totalPages);
        paginationSupport.setCurrentPage(request.getCurrentPage());
        return paginationSupport;
    }

    /**
     * 应付报表条件组装
     *
     * @param request 请求参数
     * @return 返回查询条件
     */
    private Criteria conditionPayableReceivableSupplier(SalesDetailReq request) {
        Criteria criteria = new Criteria();

        //供应商编码
        if (StrUtilX.isNotEmpty(request.getSupplierCode())) {
            criteria.and("suppleOrderReportList.supplierCode").is(request.getSupplierCode());
        } else if (StrUtilX.isNotEmpty(request.getSupplierName())) { //供应商名称
            // 对 supplierName 进行转义处理
            String supplierName = Pattern.quote(request.getSupplierName());
            Pattern pattern = Pattern.compile(".*" + supplierName + ".*", Pattern.CASE_INSENSITIVE);
            criteria.and("suppleOrderReportList.supplierName").regex(pattern);
        }

        return criteria;
    }

    @Override
    public PayableAggregateDTO queryPayableAggregate(SalesDetailReq request) {
        PayableAggregateDTO payableAggregateDTO = new PayableAggregateDTO();
        PayableStatisticsDTO payableStatisticsDTO;
        try {
            //应付报表汇总查询条件
            Criteria criteria = conditionPayableReceivable(request);
            Aggregation aggregation = Aggregation.newAggregation(
                    Aggregation.match(criteria),
                    Aggregation.unwind("suppleOrderReportList"),
                    Aggregation.group()
                            .sum(ConvertOperators.valueOf("$suppleOrderReportList.payableOrgCurrencyAmt").convertToDecimal()).as("supplierOrderPayableOrgCurrencyAmt")
                            .sum(ConvertOperators.valueOf("$suppleOrderReportList.rewardOrgCurrencyAmt").convertToDecimal()).as("rewardOrgCurrencyAmt")
                            .sum(ConvertOperators.valueOf("$suppleOrderReportList.rebateOrgCurrencyAmt").convertToDecimal()).as("rebateOrgCurrencyAmt")
                            .sum(ConvertOperators.valueOf("$suppleOrderReportList.suppleOrderFinanceReportResp.rewardUnpaidOrgCurrencyAmt").convertToDecimal()).as("rewardUnpaidOrgCurrencyAmt")
                            .sum(ConvertOperators.valueOf("$suppleOrderReportList.suppleOrderFinanceReportResp.rebateUnpaidOrgCurrencyAmt").convertToDecimal()).as("rebateUnpaidOrgCurrencyAmt")
                            .sum(ConvertOperators.valueOf("$suppleOrderReportList.suppleOrderFinanceReportResp.supplyOrderUnpaidOrgCurrencyAmt").convertToDecimal()).as("supplierOrderUnpaidOrgCurrencyAmt")
            );
            payableStatisticsDTO = mongoTemplate.aggregate(aggregation, OrderReportResp.class, PayableStatisticsDTO.class).getUniqueMappedResult();

            if (null == payableStatisticsDTO) {
                return new PayableAggregateDTO();
            }

            BeanUtils.copyProperties(payableStatisticsDTO, payableAggregateDTO);
            //供应商应付总金额 = 供货单【“供货单应付CNY”】之和  -  【“供应商奖励应收CNY”】之和 + 【“供应商返佣应付CNY”】之和
            //payableAggregateDTO.setSupplierTotalPayableOrgCurrencyAmount(payableStatisticsDTO.getSupplierOrderPayableOrgCurrencyAmt().subtract(payableStatisticsDTO.getRewardOrgCurrencyAmt().add(payableStatisticsDTO.getRebateOrgCurrencyAmt())));
            payableAggregateDTO.setSupplierTotalPayableOrgCurrencyAmount(payableStatisticsDTO.getSupplierOrderPayableOrgCurrencyAmt());
            //供应商未付总金额 = 供货单【“供货单未付CNY”】之和  -  【“供应商奖励未收CNY”】之和 + 【“供应商返佣未付CNY”】之和
            //payableAggregateDTO.setSupplierTotalUnpaidOrgCurrencyAmt(payableStatisticsDTO.getSupplierOrderUnpaidOrgCurrencyAmt().subtract(payableStatisticsDTO.getRewardUnpaidOrgCurrencyAmt().add(payableStatisticsDTO.getRebateUnpaidOrgCurrencyAmt())));
            payableAggregateDTO.setSupplierTotalUnpaidOrgCurrencyAmt(payableStatisticsDTO.getSupplierOrderUnpaidOrgCurrencyAmt());
            payableAggregateDTO.setSupplierTotalOrderPaidOrgCurrencyAmt(payableAggregateDTO.getSupplierTotalPayableOrgCurrencyAmount().subtract(payableAggregateDTO.getSupplierTotalUnpaidOrgCurrencyAmt()));

            payableAggregateDTO.setSupplierTotalPayableOrgCurrencyAmount(payableAggregateDTO.getSupplierTotalPayableOrgCurrencyAmount().setScale(2, RoundingMode.CEILING));
            payableAggregateDTO.setSupplierTotalOrderPaidOrgCurrencyAmt(payableAggregateDTO.getSupplierTotalOrderPaidOrgCurrencyAmt().setScale(2, RoundingMode.CEILING));
            payableAggregateDTO.setSupplierTotalUnpaidOrgCurrencyAmt(payableAggregateDTO.getSupplierTotalUnpaidOrgCurrencyAmt().setScale(2, RoundingMode.CEILING));
        } catch (Exception e) {
            log.error("应付报表统计汇总数据异常：", e);
        }
        return payableAggregateDTO;
    }

    /**
     * 应付报表条件组装
     *
     * @param request 请求参数
     * @return 返回查询条件
     */
    private Criteria conditionPayableReceivable(SalesDetailReq request) {
        Criteria criteria = new Criteria();

        // 下单日期
        if (request.getDateQueryType().equals(DateQueryTypeEnum.DATE_QUERY_TYPE_0.type)) {
            request.setStartDate(request.getStartDate() + " 00:00:00");
            request.setEndDate(request.getEndDate() + " 23:59:59");
            criteria.and("createdDt").gte(request.getStartDate()).lte(request.getEndDate());
        }
        // 入住日期
        if (request.getDateQueryType().equals(DateQueryTypeEnum.DATE_QUERY_TYPE_1.type)) {
            criteria.and("checkInDate").gte(request.getStartDate()).lte(request.getEndDate());
        }
        // 离店日期
        if (request.getDateQueryType().equals(DateQueryTypeEnum.DATE_QUERY_TYPE_2.type)) {
            criteria.and("checkOutDate").gte(request.getStartDate()).lte(request.getEndDate());
        }

        // 客户编码
        if (StrUtilX.isNotEmpty(request.getAgentCode())) {
            criteria.and("agentCode").is(request.getAgentCode());
        }
//        else if (StrUtilX.isNotEmpty(request.getAgentName())) { // 客户名称
//            // 对 agentName 进行转义处理
//            String agentName = Pattern.quote(request.getAgentName());
//            Pattern pattern = Pattern.compile(".*" + agentName + ".*", Pattern.CASE_INSENSITIVE);
//            criteria.and("agentName").regex(pattern);
//        }

        //供应商编码
        if (StrUtilX.isNotEmpty(request.getSupplierCode())) {
            criteria.and("suppleOrderReportList.supplierCode").is(request.getSupplierCode());
        }
//        else if (StrUtilX.isNotEmpty(request.getSupplierName())) { //供应商名称
//            // 对 supplierName 进行转义处理
//            String supplierName = Pattern.quote(request.getSupplierName());
//            Pattern pattern = Pattern.compile(".*" + supplierName + ".*", Pattern.CASE_INSENSITIVE);
//            criteria.and("suppleOrderReportList.supplierName").regex(pattern);
//        }

        //订单结算方式
        if (request.getOrderSettlementType() != null) {
            criteria.and("orderSettlementType").is(request.getOrderSettlementType());
        }

        //客户经理
        if (StrUtilX.isNotEmpty(request.getAgentManagerName())) {
            Pattern pattern = Pattern.compile(".*" + request.getAgentManagerName() + ".*", Pattern.CASE_INSENSITIVE);
            criteria.and("agentManagerName").regex(pattern);
        }

        return criteria;
    }


    /**
     * 概况统计列表条件
     *
     * @param summaryReq
     * @return
     */
    private Query conditionSummary(SummaryReq summaryReq) {
        Query query = new Query();

        summaryReq.setStartDate(summaryReq.getStartDate() + " 00:00:00");
        summaryReq.setEndDate(summaryReq.getEndDate() + " 23:59:59");

        //客户编码
        if (StrUtilX.isNotEmpty(summaryReq.getAgentCode())) {
            query.addCriteria(Criteria.where("agentCode").is(summaryReq.getAgentCode()));
        }
        //下单时间
        if (summaryReq.getDateQueryType().equals(DateQueryTypeEnum.DATE_QUERY_TYPE_0.type)) {
            query.addCriteria(Criteria.where("createdDt").gte(summaryReq.getStartDate()).lte(summaryReq.getEndDate()));
        }
        //入住日期
        if (summaryReq.getDateQueryType().equals(DateQueryTypeEnum.DATE_QUERY_TYPE_1.type)) {
            query.addCriteria(Criteria.where("startDate").gte(summaryReq.getStartDate()).lte(summaryReq.getEndDate()));
        }
        //离店日期
        if (summaryReq.getDateQueryType().equals(DateQueryTypeEnum.DATE_QUERY_TYPE_2.type)) {
            query.addCriteria(Criteria.where("endDate").gte(summaryReq.getStartDate()).lte(summaryReq.getEndDate()));
        }
        //城市名称
        if (null != summaryReq.getCityName()) {
            query.addCriteria(Criteria.where("endDate").gte(summaryReq.getStartDate()).lte(summaryReq.getEndDate()));
        }
        //订单确认状态
        if (null != summaryReq.getOrderConfirmationStatus()) {
            query.addCriteria(Criteria.where("orderConfirmationStatus").is(summaryReq.getOrderConfirmationStatus()));
        }
        //是否协议订单
        if (null != summaryReq.getProductLabel()) {
            query.addCriteria(Criteria.where("productLabel").is(summaryReq.getProductLabel()));
        }
        return query;
    }
}
