package com.tiangong.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.mapper.StatisticsMapper;
import com.tiangong.service.StatisticsService;
import com.tiangong.statistics.dto.*;
import com.tiangong.util.CommonTgUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/7/4 17:55
 * @Description:
 */
@Slf4j
@Service
public class StatisticsServiceImpl implements StatisticsService {

    @Autowired
    private StatisticsMapper statisticsMapper;

    @Override
    public PaginationSupportDTO<SalesDetailStatisticsDTO> querySalesDetailStatistics(QuerySaleStatisticsDTO request) {
        PageHelper.startPage(request.getCurrentPage(), request.getPageSize());
        List<SalesDetailStatisticsDTO> list= statisticsMapper.querySalesDetailStatistics(request);
        for (int i = 0; i <list.size() ; i++) {
            list.get(i).setCommission(CommonTgUtils.setScale(list.get(i).getCommission(),2,BigDecimal.ROUND_UP));
            list.get(i).setPayableAmt(CommonTgUtils.setScale(list.get(i).getPayableAmt(),2,BigDecimal.ROUND_UP));
            list.get(i).setPaidAmt(CommonTgUtils.setScale(list.get(i).getPaidAmt(),2,BigDecimal.ROUND_UP));
            list.get(i).setUnpaidAmt(CommonTgUtils.setScale(list.get(i).getUnpaidAmt(),2,BigDecimal.ROUND_UP));
            list.get(i).setReceivableAmt(CommonTgUtils.setScale(list.get(i).getReceivableAmt(),2,BigDecimal.ROUND_UP));
            list.get(i).setReceivedAmt(CommonTgUtils.setScale(list.get(i).getReceivedAmt(),2,BigDecimal.ROUND_UP));
            list.get(i).setUnreceivedAmt(CommonTgUtils.setScale(list.get(i).getUnreceivedAmt(),2,BigDecimal.ROUND_UP));
            list.get(i).setProfit(CommonTgUtils.setScale(list.get(i).getProfit(),2,BigDecimal.ROUND_UP));
            list.get(i).setRewardOrgCurrencyAmt(CommonTgUtils.setScale(list.get(i).getRewardOrgCurrencyAmt(),2,BigDecimal.ROUND_UP));
            list.get(i).setRebateOrgCurrencyAmt(CommonTgUtils.setScale(list.get(i).getRebateOrgCurrencyAmt(),2,BigDecimal.ROUND_UP));
            if(request.getOrderReceivableType() != null && request.getOrderReceivableType() == 0 && list.get(i).getSupplyOrderConfirmationStatus() == 2){
                list.remove(i);
                i--;
                continue;
            }else if (list.get(i).getSupplyOrderConfirmationStatus() == 2){
//                list.get(i).setProfit(new BigDecimal(0).subtract(list.get(i).getPayableAmt()));
//                list.get(i).setReceivableAmt(new BigDecimal(0));
//                list.get(i).setUnreceivedAmt(new BigDecimal(0));
//                list.get(i).setReceivedAmt(new BigDecimal(0));
                continue;
            }
            //查询是否有供应商确认单
            String supplyCode =statisticsMapper.queryConfirmSupplyByOrderId(list.get(i).getOrderId());
            if(StringUtils.isNotEmpty(supplyCode)){
                if(request.getOrderReceivableType() != null && request.getOrderReceivableType() == 0 && !supplyCode.equals(list.get(i).getSupplyOrderCode())){
                    list.remove(i);
                    i--;
                    continue;
                }else if(!supplyCode.equals(list.get(i).getSupplyOrderCode())){
                    list.get(i).setProfit(new BigDecimal(0).subtract(list.get(i).getPayableAmt()));
                    list.get(i).setReceivableAmt(new BigDecimal(0));
                    list.get(i).setUnreceivedAmt(new BigDecimal(0));
                    list.get(i).setReceivedAmt(new BigDecimal(0));
                }else{
                    if (list.get(i).getCommission() != null){
                        list.get(i).setReceivableAmt(list.get(i).getReceivableAmt());
                    }
                    list.get(i).setProfit(list.get(i).getReceivableAmt().subtract(list.get(i).getPayableAmt()));
                }
            } else {
                if (list.get(i).getCommission() != null){
                    list.get(i).setReceivableAmt(list.get(i).getReceivableAmt());
                }
                list.get(i).setProfit(list.get(i).getReceivableAmt().subtract(list.get(i).getPayableAmt()));
            }
        }
        PageInfo<SalesDetailStatisticsDTO> page = new PageInfo<>(list);
        PaginationSupportDTO<SalesDetailStatisticsDTO> paginationSupport= new PaginationSupportDTO<>();
        paginationSupport.setItemList(list);
        paginationSupport.setPageSize(page.getPageSize());
        paginationSupport.setTotalCount(page.getTotal());
        paginationSupport.setTotalPage(page.getPages());
        paginationSupport.setCurrentPage(page.getPageNum());
        return paginationSupport;
    }

    @Override
    public SaleStatisticsDTO querySalesStatistics(QuerySaleStatisticsDTO request) {
        BigDecimal bg = new BigDecimal(100);
        SaleStatisticsDTO  saleStatisticsDTO =   statisticsMapper.querySalesStatistics(request);
        if(null != saleStatisticsDTO && null != saleStatisticsDTO.getProfitRate()){
            BigDecimal profitRate = new BigDecimal(saleStatisticsDTO.getProfitRate()).setScale(2,BigDecimal.ROUND_UP);
            saleStatisticsDTO.setProfitRate(profitRate.multiply(bg).stripTrailingZeros().toPlainString()+"%");
        }else {
            saleStatisticsDTO = new SaleStatisticsDTO();
            saleStatisticsDTO.setProfitRate("0%");
            saleStatisticsDTO.setSaleNightQty(new BigDecimal(0));
            saleStatisticsDTO.setSaleAmt(BigDecimal.ZERO);
            saleStatisticsDTO.setUnreceivedAmt(BigDecimal.ZERO);
            saleStatisticsDTO.setProfit(BigDecimal.ZERO);
        }
        return saleStatisticsDTO;
    }

    @Override
    public PaginationSupportDTO<OrderProfitStatisticsDTO> queryOrderProfitStatistics(QueryOrderStatisticsDTO request) {
        PageHelper.startPage(request.getCurrentPage(), request.getPageSize());
        List<OrderProfitStatisticsDTO> list = statisticsMapper.queryOrderProfitStatistics(request);

        PageInfo<OrderProfitStatisticsDTO> page = new PageInfo<>(list);
        PaginationSupportDTO<OrderProfitStatisticsDTO> paginationSupport= new PaginationSupportDTO<>();
        paginationSupport.setItemList(list);
        paginationSupport.setPageSize(page.getPageSize());
        paginationSupport.setTotalCount(page.getTotal());
        paginationSupport.setTotalPage(page.getPages());
        paginationSupport.setCurrentPage(page.getPageNum());
        return paginationSupport;
    }

    @Override
    public OrderStatisticsDTO queryOrderStatistics(QueryOrderStatisticsDTO request) {
        OrderStatisticsDTO orderStatisticsDTO = statisticsMapper.queryOrderStatistics(request);
        BigDecimal bg = new BigDecimal(100);
        if(null != orderStatisticsDTO && null != orderStatisticsDTO.getProfitRate()){
            BigDecimal profitRate = new BigDecimal(orderStatisticsDTO.getProfitRate()).setScale(2,BigDecimal.ROUND_UP);
            orderStatisticsDTO.setProfitRate(profitRate.multiply(bg).stripTrailingZeros().toPlainString()+"%");
        }else {
            orderStatisticsDTO = new OrderStatisticsDTO();
            orderStatisticsDTO.setNightQty(new BigDecimal(0));
            orderStatisticsDTO.setProfitRate("0%");
            orderStatisticsDTO.setProfit(BigDecimal.ZERO);
            orderStatisticsDTO.setOrderTotalAmt(BigDecimal.ZERO);
            orderStatisticsDTO.setSupplyOrderTotalAmt(BigDecimal.ZERO);
        }
        return orderStatisticsDTO;
    }

}
