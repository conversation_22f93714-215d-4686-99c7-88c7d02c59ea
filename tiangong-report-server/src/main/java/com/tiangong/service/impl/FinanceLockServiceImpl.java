package com.tiangong.service.impl;

import com.tiangong.common.Response;
import com.tiangong.domain.dto.FinanceLockSupplyOrderDTO;
import com.tiangong.enums.ErrorCodeEnum;
import com.tiangong.enums.ResultCodeEnum;
import com.tiangong.mapper.FinanceLockMapper;
import com.tiangong.service.FinanceLockService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2023/7/3 14:59
 * @Description:
 */
@Slf4j
@Service
public class FinanceLockServiceImpl implements FinanceLockService {

    @Autowired
    private FinanceLockMapper financeLockMapper;

    @Override
    public Response lockSupplyOrder(FinanceLockSupplyOrderDTO request) {
        Response response = new Response();
        Integer canLock = financeLockMapper.checkSupplyOrderCanLock(request.getSupplyOrderId());
        if (canLock != null && request.getLockStatus() == 0) {
            return new Response(ResultCodeEnum.FAILURE.code, ErrorCodeEnum.CONFIRMED_NON_STATEMENT_BILLS_CANNOT_BE_UNLOCKED.errorCode, ErrorCodeEnum.CONFIRMED_NON_STATEMENT_BILLS_CANNOT_BE_UNLOCKED.errorDesc);
        }

        financeLockMapper.lockSupplyOrder(request);
        response.setResult(ResultCodeEnum.SUCCESS.code);
        return response;
    }

}
