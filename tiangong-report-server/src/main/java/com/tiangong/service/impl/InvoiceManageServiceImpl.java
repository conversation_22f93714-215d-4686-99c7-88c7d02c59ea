package com.tiangong.service.impl;


import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.tiangong.domain.req.SupplierInvoiceBillItemExportReq;
import com.tiangong.domain.req.SupplierInvoiceReq;
import com.tiangong.domain.resp.SupplierInvoiceBillItemExportResp;
import com.tiangong.domain.resp.SupplierInvoiceResponse;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.mapper.InvoiceMangeMapper;
import com.tiangong.order.remote.response.GuestDTO;
import com.tiangong.service.InvoiceManageService;
import com.tiangong.util.CollUtilX;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: zhiling
 * @date: 2024/2/19 10:57
 * @description: 发票管理
 */
@Slf4j
@Service
public class InvoiceManageServiceImpl implements InvoiceManageService {

    @Autowired
    private InvoiceMangeMapper invoiceMangeMapper;

    @Override
    public PaginationSupportDTO<SupplierInvoiceResponse> queryInvoiceList(SupplierInvoiceReq request) {
        PageHelper.startPage(request.getCurrentPage(),request.getPageSize());
        List<SupplierInvoiceResponse> list = invoiceMangeMapper.queryInvoiceList(request);
        PageInfo<SupplierInvoiceResponse> page = new PageInfo<>(list);
        PaginationSupportDTO<SupplierInvoiceResponse> paginationSupport= new PaginationSupportDTO<>();
        paginationSupport.setItemList(list);
        paginationSupport.setPageSize(page.getPageSize());
        paginationSupport.setTotalCount(page.getTotal());
        paginationSupport.setTotalPage(page.getPages());
        paginationSupport.setCurrentPage(page.getPageNum());
        return paginationSupport;
    }

    @Override
    public List<SupplierInvoiceBillItemExportResp> querySupplierInvoiceBillItem(SupplierInvoiceBillItemExportReq request) {
        PageHelper.startPage(request.getCurrentPage(),request.getPageSize());
        List<SupplierInvoiceBillItemExportResp> list = invoiceMangeMapper.querySupplierInvoiceBillItemList(request);
        if (CollUtilX.isNotEmpty(list)) {
            // 设置入住人信息
            list = list.stream().peek(item -> {
                if (StrUtilX.isNotEmpty(item.getRoomNumbers()) && CollUtilX.isNotEmpty(item.getGuests())) {
                    Map<Integer, String> guestMap = new HashMap<>();
                    for (GuestDTO guest : item.getGuests()) {
                        if (guestMap.containsKey(guest.getRoomNumber())) {
                            String name = guestMap.get(guest.getRoomNumber()) + "," + guest.getName();
                            guestMap.put(guest.getRoomNumber(), name);
                        } else {
                            guestMap.put(guest.getRoomNumber(), guest.getName());
                        }
                    }
                    List<String> roomNumbers = StrUtilX.stringToList(item.getRoomNumbers(), ",");
                    StringBuilder sb = new StringBuilder();
                    for (String roomNumber : roomNumbers) {
                        if (StrUtilX.isNotEmpty(sb.toString())) {
                            sb.append(",");
                        }
                        sb.append(guestMap.get(Integer.parseInt(roomNumber)));
                    }
                    item.setGuest(sb.toString());
                }
            }).collect(Collectors.toList());
        }
        return list;
    }

    @Override
    public List<SupplierInvoiceBillItemExportResp> queryAgentInvoiceBillItem(SupplierInvoiceBillItemExportReq request) {
        PageHelper.startPage(request.getCurrentPage(),request.getPageSize());
        return invoiceMangeMapper.queryAgentInvoiceBillItemList(request);
    }

}
