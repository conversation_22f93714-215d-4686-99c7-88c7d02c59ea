package com.tiangong.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tiangong.cloud.common.constant.DynamicTableNameConstant;
import com.tiangong.cloud.common.domain.LoginUser;
import com.tiangong.cloud.common.enums.result.ResultEnum;
import com.tiangong.cloud.commonbean.config.page.MybatisPlusConfig;
import com.tiangong.cloud.commonbean.config.security.TokenManager;
import com.tiangong.common.Response;
import com.tiangong.constant.SignsConstant;
import com.tiangong.domain.dto.*;
import com.tiangong.domain.entity.SupplierImportStatementAnnexEntity;
import com.tiangong.domain.entity.SupplierImportStatementEntity;
import com.tiangong.domain.entity.SupplyAutoReconciliationEntity;
import com.tiangong.domain.po.SupplierStatementPO;
import com.tiangong.domain.req.SupplierImportStatementReq;
import com.tiangong.domain.resp.SupplierImportAndReconciliationResp;
import com.tiangong.enums.ErrorCodeEnum;
import com.tiangong.enums.SettlementCurrencyEnum;
import com.tiangong.enums.StatementTypeEnum;
import com.tiangong.exception.SysException;
import com.tiangong.finance.OrgDTO;
import com.tiangong.initializer.CommonInitializer;
import com.tiangong.keys.RedisKey;
import com.tiangong.mapper.*;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.service.SupplierImportStatementService;
import com.tiangong.service.SupplierStatementService;
import com.tiangong.util.CollUtilX;
import com.tiangong.util.DateUtilX;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.multipart.MultipartFile;
import tk.mybatis.mapper.entity.Example;

import javax.servlet.http.HttpServletRequest;
import java.io.BufferedInputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/7/3 9:43
 * @Description:
 */
@Slf4j
@Service
public class SupplierImportStatementServiceImpl extends ServiceImpl<SupplierImportStatementMapper, SupplierImportStatementEntity> implements SupplierImportStatementService {

    /**
     * 批量插入的数量
     */
    private final Integer batchInsertCount = 2000;

    private final Integer batchQueryCount = 2000;

    private static final ThreadPoolExecutor executor = new ThreadPoolExecutor(
            4,
            8,
            30,
            TimeUnit.SECONDS,
            new LinkedBlockingDeque<>(100000),
            Executors.defaultThreadFactory(),
            new ThreadPoolExecutor.AbortPolicy()
    );


    @Autowired
    private SupplierStatementService supplierStatementService;

    @Autowired
    private SupplierImportStatementMapper supplierImportStatementMapper;

    @Autowired
    private SupplierImportStatementAnnexMapper supplierImportStatementAnnexMapper;

    @Autowired
    private SupplierStatementMapper supplierStatementMapper;

    @Autowired
    private SupplierStatementOrderMapper supplierStatementOrderMapper;

    @Autowired
    private SupplyAutoReconciliationMapper supplyAutoReconciliationMapper;

    @Autowired
    HttpServletRequest request;

    /**
     * 导入供应商的账单
     */
    @Override
    @Transactional
    public Response<Object> supplierStatementImport(MultipartFile file, SupplierImportStatementReq supplierImportStatementReq) throws IOException {
        LoginUser loginUser = TokenManager.getUser(request);
        return importStatement(file, supplierImportStatementReq, "", loginUser);
    }


    /**
     * 导入供应商的账单和对比账单
     */
    @Override
    @Transactional
    public Response<Object> supplierImportAndReconciliation(MultipartFile file, SupplierImportStatementReq supplierImportStatementReq) throws IOException {
        Integer statementId = null;
        String statementCode = null;
        try {
            LoginUser loginUser = TokenManager.getUser(request);

            //查询未出账供应商列表,如果未出账供应商列表不包括当前的供应商，则直接返回给前端"该供应商不存在未出账"
            QueryUncheckOutSupplierListDTO queryUncheckOutSupplierListDTO = new QueryUncheckOutSupplierListDTO();
            BeanUtils.copyProperties(supplierImportStatementReq, queryUncheckOutSupplierListDTO);
            queryUncheckOutSupplierListDTO.setCompanyCode(loginUser.getCompanyCode());
            List<UncheckOutSupplierDTO> uncheckOutSupplierDTOList = supplierStatementMapper.queryUncheckOutSupplierList(queryUncheckOutSupplierListDTO);
            Set<String> uncheckOutSupplierDTOSet = uncheckOutSupplierDTOList.stream().map(UncheckOutSupplierDTO::getSupplierCode).collect(Collectors.toSet());
            if (CollUtilX.isEmpty(uncheckOutSupplierDTOSet) || !uncheckOutSupplierDTOSet.contains(supplierImportStatementReq.getSupplierCode())) {
                return Response.error(ErrorCodeEnum.FAIL.errorCode, "供应商没有可出账的供货单!");
            }

            //先出账生成账单
            CreateSupplierStatementDTO createSupplierStatementDTO = new CreateSupplierStatementDTO();
            BeanUtils.copyProperties(supplierImportStatementReq, createSupplierStatementDTO);
            createSupplierStatementDTO.setCompanyCode(loginUser.getCompanyCode());
            createSupplierStatementDTO.setOperator(loginUser.getUserName());
            createSupplierStatementDTO.setStatementType(StatementTypeEnum.SUPPLY_ORDER_AMT.key);
            createSupplierStatementDTO.setStatementName(supplierImportStatementReq.getSupplierStatementName());
            statementCode = supplierStatementService.createStatement(createSupplierStatementDTO);
            List<StatementSupplyOrderDTO> queryStatementOrderList = new ArrayList<>();
            if (StrUtilX.isNotEmpty(statementCode)) {
                boolean isExist = RedisTemplateX.setIfAbsent(StrUtilX.concat(RedisKey.SUPPLY_AUTO_RECONCILIATION, statementCode), "doing", 6 * 60 * 60 * 1000l);
                if (!isExist) {
                    return Response.error(ErrorCodeEnum.FAIL.errorCode, "账单比对中，请稍后!");
                }
                //查询账单Id
                Example supplierStatementExample = new Example(SupplierStatementPO.class);
                supplierStatementExample.createCriteria().andEqualTo("statementCode", statementCode);
                SupplierStatementPO supplierStatementPO = supplierStatementMapper.selectOneByExample(supplierStatementExample);

                //查询对应账单
                QueryStatementSupplyOrderListPageDTO queryStatementSupplyOrderListPageDTO = new QueryStatementSupplyOrderListPageDTO();
                queryStatementSupplyOrderListPageDTO.setStatementId(supplierStatementPO.getId());
                statementId = supplierStatementPO.getId();
                queryStatementSupplyOrderListPageDTO.setDateQueryType(supplierImportStatementReq.getDateQueryType());
                queryStatementSupplyOrderListPageDTO.setPageSize(batchQueryCount);
                Integer indexNum = 0;
                while (true) {
                    queryStatementSupplyOrderListPageDTO.setCurrentPage(indexNum * batchQueryCount);
                    List<StatementSupplyOrderDTO> list = supplierStatementOrderMapper.queryStatementOrderListPage(queryStatementSupplyOrderListPageDTO);
                    if (CollUtilX.isNotEmpty(list)) {
                        queryStatementOrderList.addAll(list);
                    } else {
                        break;
                    }
                    indexNum++;
                }

                if (CollUtilX.isEmpty(queryStatementOrderList)) {
                    //添加所有订单到账单
                    AddStatementSupplyOrderListDTO addStatementSupplyOrderListDTO = new AddStatementSupplyOrderListDTO();
                    addStatementSupplyOrderListDTO.setStatementId(supplierStatementPO.getId());
                    addStatementSupplyOrderListDTO.setStartDate(supplierImportStatementReq.getStartDate());
                    addStatementSupplyOrderListDTO.setEndDate(supplierImportStatementReq.getEndDate());
                    addStatementSupplyOrderListDTO.setDateQueryType(supplierImportStatementReq.getDateQueryType());
                    addStatementSupplyOrderListDTO.setOperator(loginUser.getFullUserName());
                    addStatementSupplyOrderListDTO.setCompanyCode(loginUser.getCompanyCode());
                    supplierStatementService.addStatementOrderList(addStatementSupplyOrderListDTO);
//                    if (supplierStatementResponse.getResult() == 0) {
//                        RedisTemplateX.delete(StrUtilX.concat(RedisKey.SUPPLY_AUTO_RECONCILIATION, statementCode));
//                        log.error("supplierImportAndReconciliation--addStatementOrderList--error");
//                        TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
//                        return Response.error(ErrorCodeEnum.FAIL.errorCode, "添加所有订单到账单失败!");
//                    }

                    indexNum = 0;
                    while (true) {
                        queryStatementSupplyOrderListPageDTO.setCurrentPage(indexNum * batchQueryCount);
                        List<StatementSupplyOrderDTO> list = supplierStatementOrderMapper.queryStatementOrderListPage(queryStatementSupplyOrderListPageDTO);
                        if (CollUtilX.isNotEmpty(list)) {
                            queryStatementOrderList.addAll(list);
                        } else {
                            break;
                        }
                        indexNum++;
                    }
                }
            } else {
                RedisTemplateX.delete(StrUtilX.concat(RedisKey.SUPPLY_AUTO_RECONCILIATION, statementCode));
                log.error("supplierImportAndReconciliation--createStatement--error");
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return Response.error(ErrorCodeEnum.FAIL.errorCode, "生成账单失败!");
            }

            if (CollUtilX.isEmpty(queryStatementOrderList)) {
                RedisTemplateX.delete(StrUtilX.concat(RedisKey.SUPPLY_AUTO_RECONCILIATION, statementCode));
                log.error("supplierImportAndReconciliation--queryStatementOrderList is null");
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return Response.error(ErrorCodeEnum.FAIL.errorCode, "此账单不包含任何订单!");
            }

            //提取账单 map<我方供货单号,<供应商订单号,ComparativeAmountAndCurrencyDTO>>
            Map<String, Map<String, ComparativeAmountAndCurrencyDTO>> systemBillMap = new HashMap<>();
            for (StatementSupplyOrderDTO statementSupplyOrderDTO : queryStatementOrderList) {
                systemBillMap.put(statementSupplyOrderDTO.getSupplyOrderCode(), new HashMap<String, ComparativeAmountAndCurrencyDTO>() {{
                    put(statementSupplyOrderDTO.getSupplierOrderCode(), ComparativeAmountAndCurrencyDTO.builder().comparativeAmount(statementSupplyOrderDTO.getPayableAmt())
                            //.currency(SettlementCurrencyEnum.getCodeByKey(statementSupplyOrderDTO.getCurrency().toString()))
                            .currency(SettlementCurrencyEnum.getCodeByKey(String.valueOf(statementSupplyOrderDTO.getCurrency())))
                            .build());
                }});
            }

            //导入供应商账单并且进行对比
            return importAndReconciliation(file, supplierImportStatementReq, statementId, statementCode, loginUser, systemBillMap);
        } catch (Exception e) {
            RedisTemplateX.delete(StrUtilX.concat(RedisKey.SUPPLY_AUTO_RECONCILIATION, statementCode));
            log.error("supplierImportAndReconciliation--error:", e);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return Response.error(ErrorCodeEnum.FAIL.errorCode, "对账失败!");
        }
    }

    private Response<Object> importStatement(MultipartFile file, SupplierImportStatementReq supplierImportStatementReq, String statementCode, LoginUser loginUser) throws IOException {
        LocalDateTime startTime = LocalDateTime.now();
        SupplierImportStatementEntity entity = new SupplierImportStatementEntity();
        BeanUtils.copyProperties(supplierImportStatementReq, entity);
        if (StrUtilX.isNotEmpty(statementCode)) {
            entity.setStatementCode(statementCode);
        }
        entity.setStatementStatus(3);
        entity.setStartDate(DateUtilX.stringToDate(supplierImportStatementReq.getStartDate()));
        entity.setEndDate(DateUtilX.stringToDate(supplierImportStatementReq.getEndDate()));
        entity.setSettlementDate(DateUtilX.stringToDate(supplierImportStatementReq.getSettlementDate()));
        entity.setDeleted(0);
        entity.setCreatedDt(new Date());
        entity.setCreatedBy(loginUser.getFullUserName());
        entity.setUpdatedDt(new Date());
        entity.setUpdatedBy(loginUser.getFullUserName());
        //供应商账单编号规则：G+年月日+四位随机数
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyyMMdd");
        String supplierStatementCode = "G" + dtf.format(startTime) + (System.currentTimeMillis() / 1000) % 10000 + "000000";
        supplierStatementCode = supplierStatementCode.substring(0, 13);
        //TODO 查询是否已存在对应的供应商账单编号

        entity.setSupplierStatementCode(supplierStatementCode);

        int insert = supplierImportStatementMapper.insert(entity);
        if (insert != 1) {
            throw new SysException(ResultEnum.E_1001.getCode(), ResultEnum.E_1001.getMessage());
        }

        String filename = file.getOriginalFilename();
        if (StrUtilX.isNotEmpty(filename)) {
            String suffix = filename.substring(filename.lastIndexOf(".") + 1);
            List<String> list = Arrays.asList("xls", "xlsx");
            if (list.contains(suffix)) {
                BufferedInputStream inputStream = new BufferedInputStream(file.getInputStream());

                Workbook workbook = null;
                if (suffix.equals("xls")) {
                    workbook = new HSSFWorkbook(inputStream);
                } else if (suffix.equals("xlsx")) {
                    workbook = new XSSFWorkbook(inputStream);
                }

                //得到第一个shell
                Sheet sheet = workbook.getSheetAt(0);

                //Excel的行数  物理行数
                int totalRows = sheet.getLastRowNum() + 1;
                if (totalRows == 1) {//没有数据
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    return Response.error(ErrorCodeEnum.FAIL.errorCode, "此excel没有数据!");
                }
                //列数
                int totalCells = 0;

                //得到Excel的列数(前提是有行数)
                if (totalRows >= 1 && sheet.getRow(0) != null) {
                    totalCells = sheet.getRow(1).getPhysicalNumberOfCells();
                }

                if (sheet.getRow(0).getPhysicalNumberOfCells() != 3) {
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    return Response.error(ErrorCodeEnum.FAIL.errorCode, "导入的excel模板有问题!");
                } else {
                    Row rowTop = sheet.getRow(0);
                    for (int i = 0; i < 3; i++) {
                        Cell cellTop = rowTop.getCell(i);
                        if (cellTop == null) {
                            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                            return Response.error(ErrorCodeEnum.FAIL.errorCode, "导入的excel模板有问题!");
                        }
                        cellTop.setCellType(Cell.CELL_TYPE_STRING);
                        String topTitle = cellTop.getStringCellValue();
                        if (i == 0 && !topTitle.equals("供应商订单号")) {
                            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                            return Response.error(ErrorCodeEnum.FAIL.errorCode, "导入的excel模板有问题!");
                        } else if (i == 1 && !topTitle.equals("我方供货单号")) {
                            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                            return Response.error(ErrorCodeEnum.FAIL.errorCode, "导入的excel模板有问题!");
                        } else if (i == 2 && !topTitle.equals("要收金额")) {
                            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                            return Response.error(ErrorCodeEnum.FAIL.errorCode, "导入的excel模板有问题!");
                        }
                    }
                }

                // 获取商家编码
                OrgDTO orgDTO = CommonInitializer.getOrgInfo();
                // 获取币种
                String currencyStr = SettlementCurrencyEnum.getCodeByKey(String.valueOf(orgDTO.getOrgCurrency()));
                if (StrUtilX.isNotEmpty(currencyStr)) {
                    throw new SysException(ErrorCodeEnum.EXCHANGE_COIN_IS_NOT_ALREADY);
                }

                CompletableFuture<Void> completableFuture = CompletableFuture.runAsync(() -> {
                    try {
                        Integer count = 0;
                        BigDecimal supplierTotalReceivables = new BigDecimal(0);

                        List<SupplierImportStatementAnnexEntity> annexList = new ArrayList<>();
                        SupplierImportStatementAnnexEntity supplierImportStatementAnnexEntity;
                        //循环Excel行数,从第2行开始。标题不入库
                        for (int r = 1; r < totalRows; r++) {
                            Row row = sheet.getRow(r);
                            if (row == null) continue;
                            supplierImportStatementAnnexEntity = new SupplierImportStatementAnnexEntity();
                            //循环Excel的列
                            for (int c = 0; c < 3; c++) {
                                Cell cell = row.getCell(c);
                                switch (c) {
                                    case 0:
                                        if (cell == null) {
                                            continue;
                                        }
                                        cell.setCellType(Cell.CELL_TYPE_STRING);
                                        String supplierOrderCode = cell.getStringCellValue().replaceAll(" ", "");
                                        if (StrUtilX.isNotEmpty(supplierOrderCode)) {
                                            supplierImportStatementAnnexEntity.setSupplierOrderCode(supplierOrderCode);
                                        }
                                        break;
                                    case 1:
                                        if (cell == null) {
                                            continue;
                                        }
                                        cell.setCellType(Cell.CELL_TYPE_STRING);
                                        String supplyOrderCode = cell.getStringCellValue().replaceAll(" ", "");
                                        if (StrUtilX.isNotEmpty(supplyOrderCode)) {
                                            supplierImportStatementAnnexEntity.setSupplyOrderCode(supplyOrderCode);
                                        }
                                        break;
                                    case 2:
                                        if (cell == null) {
                                            supplierImportStatementAnnexEntity.setReceivableAmt(new BigDecimal(0));
                                            supplierImportStatementAnnexEntity.setCurrency(currencyStr);
                                            continue;
                                        }
                                        cell.setCellType(Cell.CELL_TYPE_STRING);
                                        String receivableAmountCurrency = cell.getStringCellValue();
                                        receivableAmountCurrency = receivableAmountCurrency.replaceAll(" ", "");

                                        String regex = "[0-9.]+";
                                        Pattern pattern = Pattern.compile(regex);
                                        Matcher matcher = pattern.matcher(receivableAmountCurrency);
                                        Integer flage = 0;
                                        StringBuilder sb = new StringBuilder();
                                        while (matcher.find()) {//将数字和小数点提取出来
                                            sb.append(matcher.group());
                                            flage++;
                                            if (flage == 100) {//防止出现死循环
                                                break;
                                            }
                                        }

                                        String strTmp = receivableAmountCurrency.replaceAll("[0-9.]", "");
                                        try {//转数字
                                            supplierTotalReceivables = supplierTotalReceivables.add(new BigDecimal(sb.toString()));
                                            supplierImportStatementAnnexEntity.setReceivableAmt(new BigDecimal(sb.toString()));
                                            if (StrUtilX.isNotEmpty(strTmp)) {
                                                supplierImportStatementAnnexEntity.setCurrency(strTmp);
                                            } else {
                                                supplierImportStatementAnnexEntity.setCurrency(currencyStr);
                                            }
                                        } catch (Exception e) {
                                            supplierImportStatementAnnexEntity.setReceivableAmt(SignsConstant.MONEY_SIGN);
                                            if (StrUtilX.isNotEmpty(receivableAmountCurrency)) {
                                                supplierImportStatementAnnexEntity.setCurrency(receivableAmountCurrency);
                                            } else {
                                                supplierImportStatementAnnexEntity.setCurrency(currencyStr);
                                            }
                                        }
                                        break;
                                }
                            }
                            if (StrUtilX.isNotEmpty(supplierImportStatementAnnexEntity.getSupplyOrderCode())) {
                                supplierImportStatementAnnexEntity.setSupplierImportStatementId(entity.getId());
                                supplierImportStatementAnnexEntity.setDeleted(0);
                                supplierImportStatementAnnexEntity.setCreatedBy(loginUser.getFullUserName());
                                supplierImportStatementAnnexEntity.setCreatedDt(new Date());
                                supplierImportStatementAnnexEntity.setUpdatedBy(loginUser.getFullUserName());
                                supplierImportStatementAnnexEntity.setUpdatedDt(new Date());
                                annexList.add(supplierImportStatementAnnexEntity);
                            }
                            //每batchInsertCount条就插入
                            if (annexList.size() >= batchInsertCount) {
                                MybatisPlusConfig.dynamicTableNameSuffix.set(String.valueOf(entity.getId() % DynamicTableNameConstant.SUPPLIER_IMPORT_STATEMENT_ANNEX_DIVIDE_TABLE_NUMBER));
                                supplierImportStatementAnnexMapper.insertBatchSomeColumn(annexList);
                                count++;
                                annexList = new ArrayList<>();
                            }
                        }
                        if (annexList.size() > 0) {
                            MybatisPlusConfig.dynamicTableNameSuffix.set(String.valueOf(entity.getId() % DynamicTableNameConstant.SUPPLIER_IMPORT_STATEMENT_ANNEX_DIVIDE_TABLE_NUMBER));
                            supplierImportStatementAnnexMapper.insertBatchSomeColumn(annexList);
                            count++;
                        }

                        //更新总的要收金额
                        int updateRow = supplierImportStatementMapper.update(null, new UpdateWrapper<SupplierImportStatementEntity>()
                                .eq("id", entity.getId()).set("supplier_total_receivables", supplierTotalReceivables).set("currency", currencyStr).set("statement_status", 0)
                                .set("updated_by", loginUser.getFullUserName()).set("updated_dt", new Date()));
                        if (updateRow != 1) {
                            throw new SysException(ResultEnum.E_1001.getCode(), ResultEnum.E_1001.getMessage());
                        }
                    } catch (Exception e) {
                        //异常的话把对应的已插入的附件都删除掉
                        MybatisPlusConfig.dynamicTableNameSuffix.set(String.valueOf(entity.getId() % DynamicTableNameConstant.SUPPLIER_IMPORT_STATEMENT_ANNEX_DIVIDE_TABLE_NUMBER));
                        supplierImportStatementAnnexMapper.delete(new QueryWrapper<SupplierImportStatementAnnexEntity>().lambda().eq(SupplierImportStatementAnnexEntity::getSupplierImportStatementId, entity.getId()));
                        //把导入记录改成导入失败
                        int updateRow = supplierImportStatementMapper.update(null, new UpdateWrapper<SupplierImportStatementEntity>()
                                .eq("id", entity.getId()).set("statement_status", 4)
                                .set("updated_by", loginUser.getFullUserName()).set("updated_dt", new Date()));
                    }
                }, executor);
            } else {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return Response.error(ErrorCodeEnum.FAIL.errorCode, "非法文件上传!");
            }
        } else {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return Response.error(ErrorCodeEnum.FAIL.errorCode, "文件不能为空");
        }
        return Response.success("导入中");
    }

    private Response<Object> importAndReconciliation(MultipartFile file, SupplierImportStatementReq supplierImportStatementReq, Integer statementId, String statementCode, LoginUser loginUser, Map<String, Map<String, ComparativeAmountAndCurrencyDTO>> systemBillMap) throws IOException {
        LocalDateTime startTime = LocalDateTime.now();
        SupplierImportStatementEntity entity = new SupplierImportStatementEntity();
        BeanUtils.copyProperties(supplierImportStatementReq, entity);
        if (statementId != null) {
            entity.setStatementId(statementId);
        }
        if (StrUtilX.isNotEmpty(statementCode)) {
            entity.setStatementCode(statementCode);
        }
        entity.setStatementStatus(3);
        entity.setStartDate(DateUtilX.stringToDate(supplierImportStatementReq.getStartDate()));
        entity.setEndDate(DateUtilX.stringToDate(supplierImportStatementReq.getEndDate()));
        entity.setSettlementDate(DateUtilX.stringToDate(supplierImportStatementReq.getSettlementDate()));
        entity.setDeleted(0);
        entity.setCreatedDt(new Date());
        entity.setCreatedBy(loginUser.getFullUserName());
        entity.setUpdatedDt(new Date());
        entity.setUpdatedBy(loginUser.getFullUserName());
        //供应商账单编号规则：G+年月日+四位随机数
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyyMMdd");
        String supplierStatementCode = "G" + dtf.format(startTime) + (System.currentTimeMillis() / 1000) % 10000 + "000000";
        supplierStatementCode = supplierStatementCode.substring(0, 13);
        entity.setSupplierStatementCode(supplierStatementCode);

        int insert = supplierImportStatementMapper.insert(entity);
        if (insert != 1) {
            RedisTemplateX.delete(StrUtilX.concat(RedisKey.SUPPLY_AUTO_RECONCILIATION, statementCode));
            throw new SysException(ResultEnum.E_1001.getCode(), ResultEnum.E_1001.getMessage());
        }

        //Map<String, Map<String, ComparativeAmountAndCurrencyDTO>> comparedBillsMap = new HashMap<>();
        Set<String> comparedBillsSet = new HashSet<>();

        String filename = file.getOriginalFilename();
        if (StrUtilX.isNotEmpty(filename)) {
            String suffix = filename.substring(filename.lastIndexOf(".") + 1);
            List<String> list = Arrays.asList("xls", "xlsx");
            if (list.contains(suffix)) {
                BufferedInputStream inputStream = new BufferedInputStream(file.getInputStream());

                Workbook workbook = null;
                if (suffix.equals("xls")) {
                    workbook = new HSSFWorkbook(inputStream);
                } else if (suffix.equals("xlsx")) {
                    workbook = new XSSFWorkbook(inputStream);
                }

                //得到第一个shell
                Sheet sheet = workbook.getSheetAt(0);

                //Excel的行数  物理行数
                int totalRows = sheet.getLastRowNum() + 1;
                if (totalRows == 1) {//没有数据
                    RedisTemplateX.delete(StrUtilX.concat(RedisKey.SUPPLY_AUTO_RECONCILIATION, statementCode));
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    return Response.error(ErrorCodeEnum.FAIL.errorCode, "此excel没有数据!");
                }
                //列数
                int totalCells = 0;

                //得到Excel的列数(前提是有行数)
                if (totalRows >= 1 && sheet.getRow(0) != null) {
                    totalCells = sheet.getRow(1).getPhysicalNumberOfCells();
                }

                if (sheet.getRow(0).getPhysicalNumberOfCells() != 3) {
                    RedisTemplateX.delete(StrUtilX.concat(RedisKey.SUPPLY_AUTO_RECONCILIATION, statementCode));
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    return Response.error(ErrorCodeEnum.FAIL.errorCode, "导入的excel模板有问题!");
                } else {
                    Row rowTop = sheet.getRow(0);
                    for (int i = 0; i < 3; i++) {
                        Cell cellTop = rowTop.getCell(i);
                        if (cellTop == null) {
                            RedisTemplateX.delete(StrUtilX.concat(RedisKey.SUPPLY_AUTO_RECONCILIATION, statementCode));
                            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                            return Response.error(ErrorCodeEnum.FAIL.errorCode, "导入的excel模板有问题!");
                        }
                        cellTop.setCellType(Cell.CELL_TYPE_STRING);
                        String topTitle = cellTop.getStringCellValue();
                        if (i == 0 && !topTitle.equals("供应商订单号")) {
                            RedisTemplateX.delete(StrUtilX.concat(RedisKey.SUPPLY_AUTO_RECONCILIATION, statementCode));
                            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                            return Response.error(ErrorCodeEnum.FAIL.errorCode, "导入的excel模板有问题!");
                        } else if (i == 1 && !topTitle.equals("我方供货单号")) {
                            RedisTemplateX.delete(StrUtilX.concat(RedisKey.SUPPLY_AUTO_RECONCILIATION, statementCode));
                            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                            return Response.error(ErrorCodeEnum.FAIL.errorCode, "导入的excel模板有问题!");
                        } else if (i == 2 && !topTitle.equals("要收金额")) {
                            RedisTemplateX.delete(StrUtilX.concat(RedisKey.SUPPLY_AUTO_RECONCILIATION, statementCode));
                            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                            return Response.error(ErrorCodeEnum.FAIL.errorCode, "导入的excel模板有问题!");
                        }
                    }
                }

                String supplierStatementCodeTmp = supplierStatementCode;
                CompletableFuture<Void> completableFuture = CompletableFuture.runAsync(() -> {
                    try {
                        Integer count = 0;

                        BigDecimal supplierTotalReceivables = new BigDecimal(0);
                        String currencyStr = "CNY";

                        List<SupplierImportStatementAnnexEntity> annexList = new ArrayList<>();
                        List<SupplyAutoReconciliationEntity> autoReconciliationList = new ArrayList<>();
                        SupplierImportStatementAnnexEntity supplierImportStatementAnnexEntity;
                        //循环Excel行数,从第2行开始。标题不入库
                        for (int r = 1; r < totalRows; r++) {
                            Row row = sheet.getRow(r);
                            if (row == null) continue;
                            supplierImportStatementAnnexEntity = new SupplierImportStatementAnnexEntity();
                            //循环Excel的列
                            for (int c = 0; c < 3; c++) {
                                Cell cell = row.getCell(c);
                                switch (c) {
                                    case 0:
                                        if (cell == null) {
                                            continue;
                                        }
                                        cell.setCellType(Cell.CELL_TYPE_STRING);
                                        String supplierOrderCode = cell.getStringCellValue().replaceAll(" ", "");
                                        if (StrUtilX.isNotEmpty(supplierOrderCode)) {
                                            supplierImportStatementAnnexEntity.setSupplierOrderCode(supplierOrderCode);
                                        }
                                        break;
                                    case 1:
                                        if (cell == null) {
                                            continue;
                                        }
                                        cell.setCellType(Cell.CELL_TYPE_STRING);
                                        String supplyOrderCode = cell.getStringCellValue().replaceAll(" ", "");
                                        if (StrUtilX.isNotEmpty(supplyOrderCode)) {
                                            supplierImportStatementAnnexEntity.setSupplyOrderCode(supplyOrderCode);
                                        }
                                        break;
                                    case 2:
                                        if (cell == null) {
                                            continue;
                                        }
                                        cell.setCellType(Cell.CELL_TYPE_STRING);
                                        String receivableAmountCurrency = cell.getStringCellValue();
                                        receivableAmountCurrency = receivableAmountCurrency.replaceAll(" ", "");

                                        String regex = "[0-9.]+";
                                        Pattern pattern = Pattern.compile(regex);
                                        Matcher matcher = pattern.matcher(receivableAmountCurrency);
                                        Integer flage = 0;
                                        StringBuilder sb = new StringBuilder();
                                        while (matcher.find()) {//将数字和小数点提取出来
                                            sb.append(matcher.group());
                                            flage++;
                                            if (flage == 100) {//防止出现死循环
                                                break;
                                            }
                                        }

                                        String strTmp = receivableAmountCurrency.replaceAll("[0-9.]", "");
                                        try {//转数字
                                            supplierTotalReceivables = supplierTotalReceivables.add(new BigDecimal(sb.toString()));
                                            supplierImportStatementAnnexEntity.setReceivableAmt(new BigDecimal(sb.toString()));
                                            if (StrUtilX.isNotEmpty(strTmp)) {
                                                supplierImportStatementAnnexEntity.setCurrency(strTmp);
                                            } else {
                                                supplierImportStatementAnnexEntity.setCurrency("CNY");
                                            }
                                        } catch (Exception e) {
                                            supplierImportStatementAnnexEntity.setReceivableAmt(SignsConstant.MONEY_SIGN);
                                            if (StrUtilX.isNotEmpty(receivableAmountCurrency)) {
                                                supplierImportStatementAnnexEntity.setCurrency(receivableAmountCurrency);
                                            } else {
                                                supplierImportStatementAnnexEntity.setCurrency("CNY");
                                            }
                                        }
                                        break;
                                }
                            }
                            if (StrUtilX.isNotEmpty(supplierImportStatementAnnexEntity.getSupplyOrderCode())) {
                                supplierImportStatementAnnexEntity.setSupplierImportStatementId(entity.getId());
                                supplierImportStatementAnnexEntity.setDeleted(0);
                                supplierImportStatementAnnexEntity.setCreatedBy(loginUser.getFullUserName());
                                supplierImportStatementAnnexEntity.setCreatedDt(new Date());
                                supplierImportStatementAnnexEntity.setUpdatedBy(loginUser.getFullUserName());
                                supplierImportStatementAnnexEntity.setUpdatedDt(new Date());
                                annexList.add(supplierImportStatementAnnexEntity);
                                //对比账单并且将对比结果写入f_supply_auto_reconciliation_result
                                //0:一致  1:金额不对  2:供应商少单  3:我方少单
                                Map<String, ComparativeAmountAndCurrencyDTO> comparativeAmountAndCurrencyDTOMap = systemBillMap.get(supplierImportStatementAnnexEntity.getSupplyOrderCode());
                                if (comparativeAmountAndCurrencyDTOMap == null) {//3:我方少单
                                    //放到集合，到达一定量之后再批量插入
                                    autoReconciliationList.add(SupplyAutoReconciliationEntity.builder().reconciliationResultType(3)
                                            .statementId(statementId)
                                            .statementCode(statementCode).supplyOrderCode(supplierImportStatementAnnexEntity.getSupplyOrderCode())
                                            .supplierStatementId(entity.getId())
                                            .supplierStatementCode(supplierStatementCodeTmp).supplierOrderCode(supplierImportStatementAnnexEntity.getSupplierOrderCode())
                                            .supplierReceivedAmt(supplierImportStatementAnnexEntity.getReceivableAmt()).supplierReceivedAmtCurrency(supplierImportStatementAnnexEntity.getCurrency())
                                            .createdBy(loginUser.getFullUserName()).createdDt(new Date())
                                            .updatedBy(loginUser.getFullUserName()).updatedDt(new Date()).build());
                                } else {//0:一致 1:金额不对
                                    //comparedBillsMap.put(supplierImportStatementAnnexEntity.getSupplyOrderCode(), comparativeAmountAndCurrencyDTOMap);
                                    comparedBillsSet.add(supplierImportStatementAnnexEntity.getSupplyOrderCode());
                                    for (Map.Entry<String, ComparativeAmountAndCurrencyDTO> comparativeAmountAndCurrencyDTOEntry : comparativeAmountAndCurrencyDTOMap.entrySet()) {
                                        //1:金额不对
                                        Integer reconciliationResultTypeTmp = 1;
                                        BigDecimal supplyPaidAmt = comparativeAmountAndCurrencyDTOEntry.getValue().getComparativeAmount();
                                        if (supplyPaidAmt.compareTo(supplierImportStatementAnnexEntity.getReceivableAmt()) == 0) {//0:一致
                                            reconciliationResultTypeTmp = 0;
                                        }
                                        autoReconciliationList.add(SupplyAutoReconciliationEntity.builder().reconciliationResultType(reconciliationResultTypeTmp)
                                                .statementId(statementId)
                                                .statementCode(statementCode).supplyOrderCode(supplierImportStatementAnnexEntity.getSupplyOrderCode())
                                                .supplyPaidAmt(supplyPaidAmt).supplyPaidAmtCurrency(comparativeAmountAndCurrencyDTOEntry.getValue().getCurrency())
                                                .supplierStatementId(entity.getId())
                                                .supplierStatementCode(supplierStatementCodeTmp).supplierOrderCode(supplierImportStatementAnnexEntity.getSupplierOrderCode())
                                                .supplierReceivedAmt(supplierImportStatementAnnexEntity.getReceivableAmt()).supplierReceivedAmtCurrency(supplierImportStatementAnnexEntity.getCurrency())
                                                .createdBy(loginUser.getFullUserName()).createdDt(new Date())
                                                .updatedBy(loginUser.getFullUserName()).updatedDt(new Date()).build());
                                    }
                                }
                            }
                            //每batchInsertCount条就插入
                            if (annexList.size() >= batchInsertCount) {
                                MybatisPlusConfig.dynamicTableNameSuffix.set(String.valueOf(entity.getId() % DynamicTableNameConstant.SUPPLIER_IMPORT_STATEMENT_ANNEX_DIVIDE_TABLE_NUMBER));
                                supplierImportStatementAnnexMapper.insertBatchSomeColumn(annexList);
                                count++;
                                annexList = new ArrayList<>();
                            }
                            if (autoReconciliationList.size() >= batchInsertCount) {
                                MybatisPlusConfig.dynamicTableNameSuffix.set(String.valueOf(statementId % DynamicTableNameConstant.SUPPLY_AUTO_RECONCILIATION_DIVIDE_TABLE_NUMBER));
                                supplyAutoReconciliationMapper.insertBatchSomeColumn(autoReconciliationList);
                                autoReconciliationList = new ArrayList<>();
                            }
                        }
                        if (annexList.size() > 0) {
                            MybatisPlusConfig.dynamicTableNameSuffix.set(String.valueOf(entity.getId() % DynamicTableNameConstant.SUPPLIER_IMPORT_STATEMENT_ANNEX_DIVIDE_TABLE_NUMBER));
                            supplierImportStatementAnnexMapper.insertBatchSomeColumn(annexList);
                            currencyStr = annexList.get(0).getCurrency();
                            count++;
                        }

                        //2:供应商少单 systemBillMap - comparedBillsSet
                        Set<String> systemBillSet = systemBillMap.keySet();
                        //Set<String> comparedBillsSet = comparedBillsMap.keySet();
                        systemBillSet.removeAll(comparedBillsSet);

                        for (Map.Entry<String, Map<String, ComparativeAmountAndCurrencyDTO>> systemBillMapEntry : systemBillMap.entrySet()) {
                            String supplyOrderCode = systemBillMapEntry.getKey();
                            String supplierOrderCode = null;
                            ComparativeAmountAndCurrencyDTO comparativeAmountAndCurrencyDTO = new ComparativeAmountAndCurrencyDTO();
                            for (Map.Entry<String, ComparativeAmountAndCurrencyDTO> tmpEntry : systemBillMapEntry.getValue().entrySet()) {
                                supplierOrderCode = tmpEntry.getKey();
                                comparativeAmountAndCurrencyDTO = tmpEntry.getValue();
                            }
                            autoReconciliationList.add(SupplyAutoReconciliationEntity.builder().reconciliationResultType(2)
                                    .statementId(statementId)
                                    .statementCode(statementCode).supplyOrderCode(supplyOrderCode)
                                    .supplyPaidAmt(comparativeAmountAndCurrencyDTO.getComparativeAmount()).supplyPaidAmtCurrency(comparativeAmountAndCurrencyDTO.getCurrency())
                                    .supplierOrderCode(supplierOrderCode)
                                    .createdBy(loginUser.getFullUserName()).createdDt(new Date())
                                    .updatedBy(loginUser.getFullUserName()).updatedDt(new Date()).build());
                            if (autoReconciliationList.size() >= batchInsertCount) {
                                MybatisPlusConfig.dynamicTableNameSuffix.set(String.valueOf(statementId % DynamicTableNameConstant.SUPPLY_AUTO_RECONCILIATION_DIVIDE_TABLE_NUMBER));
                                supplyAutoReconciliationMapper.insertBatchSomeColumn(autoReconciliationList);
                                autoReconciliationList = new ArrayList<>();
                            }
                        }
                        if (autoReconciliationList.size() > 0) {
                            MybatisPlusConfig.dynamicTableNameSuffix.set(String.valueOf(statementId % DynamicTableNameConstant.SUPPLY_AUTO_RECONCILIATION_DIVIDE_TABLE_NUMBER));
                            supplyAutoReconciliationMapper.insertBatchSomeColumn(autoReconciliationList);
                        }

                        //更新总的要收金额
                        int updateRow = supplierImportStatementMapper.update(null, new UpdateWrapper<SupplierImportStatementEntity>()
                                .eq("id", entity.getId()).set("supplier_total_receivables", supplierTotalReceivables).set("currency", currencyStr).set("statement_status", 1)
                                .set("updated_by", loginUser.getFullUserName()).set("updated_dt", new Date()));
                        if (updateRow != 1) {
                            RedisTemplateX.delete(StrUtilX.concat(RedisKey.SUPPLY_AUTO_RECONCILIATION, statementCode));
                            throw new SysException(ResultEnum.E_1001.getCode(), ResultEnum.E_1001.getMessage());
                        }
                        RedisTemplateX.delete(StrUtilX.concat(RedisKey.SUPPLY_AUTO_RECONCILIATION, statementCode));
                    } catch (Exception e) {
                        MybatisPlusConfig.dynamicTableNameSuffix.set(String.valueOf(entity.getId() % DynamicTableNameConstant.SUPPLIER_IMPORT_STATEMENT_ANNEX_DIVIDE_TABLE_NUMBER));
                        supplierImportStatementAnnexMapper.delete(new QueryWrapper<SupplierImportStatementAnnexEntity>().lambda().eq(SupplierImportStatementAnnexEntity::getSupplierImportStatementId, entity.getId()));

                        MybatisPlusConfig.dynamicTableNameSuffix.set(String.valueOf(statementId % DynamicTableNameConstant.SUPPLY_AUTO_RECONCILIATION_DIVIDE_TABLE_NUMBER));
                        supplyAutoReconciliationMapper.delete(new QueryWrapper<SupplyAutoReconciliationEntity>().lambda().eq(SupplyAutoReconciliationEntity::getStatementId, statementId));

                        //更新总的要收金额
                        int updateRow = supplierImportStatementMapper.update(null, new UpdateWrapper<SupplierImportStatementEntity>()
                                .eq("id", entity.getId()).set("statement_status", 4)
                                .set("updated_by", loginUser.getFullUserName()).set("updated_dt", new Date()));
                        RedisTemplateX.delete(StrUtilX.concat(RedisKey.SUPPLY_AUTO_RECONCILIATION, statementCode));
                    }
                }, executor);
            } else {
                RedisTemplateX.delete(StrUtilX.concat(RedisKey.SUPPLY_AUTO_RECONCILIATION, statementCode));
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return Response.error(ErrorCodeEnum.FAIL.errorCode, "非法文件上传!");
            }
        } else {
            RedisTemplateX.delete(StrUtilX.concat(RedisKey.SUPPLY_AUTO_RECONCILIATION, statementCode));
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return Response.error(ErrorCodeEnum.FAIL.errorCode, "文件不能为空");
        }

        //stringRedisTemplate.delete(StrUtilX.concat(RedisKey.SUPPLY_AUTO_RECONCILIATION, statementCode));
        return Response.success(SupplierImportAndReconciliationResp.builder().statementId(statementId).statementCode(statementCode).build());
    }

}
