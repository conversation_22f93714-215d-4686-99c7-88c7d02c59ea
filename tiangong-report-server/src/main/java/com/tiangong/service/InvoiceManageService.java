package com.tiangong.service;

import com.tiangong.domain.req.SupplierInvoiceBillItemExportReq;
import com.tiangong.domain.req.SupplierInvoiceReq;
import com.tiangong.domain.resp.SupplierInvoiceBillItemExportResp;
import com.tiangong.domain.resp.SupplierInvoiceResponse;
import com.tiangong.dto.common.PaginationSupportDTO;

import java.util.List;

/**
 * @author: zhiling
 * @date: 2024/2/19 10:55
 * @description: 发票管理
 */
public interface InvoiceManageService {


    /**
     * 查询发票列表
     *
     * @param request
     * @return
     */
    PaginationSupportDTO<SupplierInvoiceResponse> queryInvoiceList(SupplierInvoiceReq request);

    /**
     * 查询进项票单明细
     *
     * @param request
     * @return
     */
    List<SupplierInvoiceBillItemExportResp> querySupplierInvoiceBillItem(SupplierInvoiceBillItemExportReq request);

    /**
     * 查询销项票单明细
     *
     * @param request
     * @return
     */
    List<SupplierInvoiceBillItemExportResp> queryAgentInvoiceBillItem(SupplierInvoiceBillItemExportReq request);
}
