package com.tiangong.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tiangong.common.Response;
import com.tiangong.domain.entity.SupplierImportStatementEntity;
import com.tiangong.domain.req.SupplierImportStatementReq;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

/**
 * <AUTHOR>
 * @Date 2023/7/3 9:39
 * @Description:
 */
public interface SupplierImportStatementService extends IService<SupplierImportStatementEntity> {

    /**
     * 导入供应商的账单
     */
    Response<Object> supplierStatementImport(MultipartFile file, SupplierImportStatementReq supplierImportStatementReq) throws IOException;

    /**
     * 导入供应商的账单和对比账单
     */
    Response<Object> supplierImportAndReconciliation(MultipartFile file, SupplierImportStatementReq supplierImportStatementReq) throws IOException;

}
