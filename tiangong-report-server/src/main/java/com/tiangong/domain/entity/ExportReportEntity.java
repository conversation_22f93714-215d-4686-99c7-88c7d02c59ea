package com.tiangong.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2023/7/6 11:06
 * @Description:
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
@TableName("t_export_report")
public class ExportReportEntity extends Model<ExportReportEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    /**
     * 导出编号
     */
    private String exportCode;
    /**
     * 导出类型
     * 0:自动对账
     * 1:协议酒店映射明细
     * 2:审计日志
     * 3:销售明细表
     * 4订单利润表
     */
    private Integer exportType;
    /**
     * 导出状态
     * 0:导出中
     * 1:导出成功
     * 2:导出失败
     */
    private Integer exportStatus;
    /**
     * 文件路径
     */
    private String fileUrl;
    /**
     * 数据创建人
     */
    private String createdBy;
    /**
     * 数据创建时间
     */
    private Date createdDt;
    /**
     * 修改人
     */
    private String updatedBy;
    /**
     * 修改时间
     */
    private Date updatedDt;

}
