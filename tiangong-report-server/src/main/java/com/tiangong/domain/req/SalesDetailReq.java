package com.tiangong.domain.req;


import com.tiangong.dto.common.BaseRequest;
import com.tiangong.enums.SupplyOneLevelChannelTypeEnum;
import com.tiangong.enums.SupplyTwoLevelChannelTypeEnum;
import lombok.Data;

/**
 * 销售（利润）报表入参
 * <AUTHOR>
 */
@Data
public class SalesDetailReq extends BaseRequest {
    /**
     * 供货单确认状态：0待确认，1已确认，2已取消，3未发单
     */
    private Integer supplyOrderConfirmationStatus;

    /**
     * 酒店确认号
     */
    private String confirmationCode;

    /**
     * 钟点房标志 1是 0否
     */
    private Integer hourly;


    /**
     * 开始时间
     */
    private String startDate;

    /**
     * 开始时间
     */
    private String endDate;


    /**
     * 供货单应付(0:不等于0，1：等于0)
     */
    private Integer supplyOrderPayableType;

    /**
     * 客户编码
     */
    private String agentCode;

    /**
     * 客户名称
     */
    private String agentName;

    /**
     * 国家 /省份 / 城市的编码
     */
    private String countryProvinceCityCode;

    /**
     * 标记属于国，省，市，那种类型
     */
    private Integer dataType;

    /**
     * 采购经理ID
     */
    private Integer purchaseManagerId;


    /**
     * 归属人
     */
    private String orderOwnerName;

    /**
     * 客户渠道
     */
    private String channelCode;

    /**
     * 订单确认状态
     * @see com.tiangong.enums.OrderStatusEnum
     */
    private Integer orderConfirmationStatus;

    /**
     * 酒店ID
     */
    private Long hotelId;

    /**
     * 订单编号
     */
    private String orderCode;

    /**
     * 订单应收(0:不等于0，1：等于0)
     */
    private Integer orderReceivableType;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 销售经理ID
     */
    private Integer saleManagerId;

    /**
     * 运营商编码
     */
    private String companyCode;

    /**
     * 日期查询类型：0下单日期，1入住日期，2离店日期
     */
    private Integer dateQueryType;

    /**
     * 协议标签
     * @see com.tiangong.enums.LabelTypeEnum
     */
    private Integer supplierLabel;

    /**
     * 订单类型
     * @see com.tiangong.enums.PayMethodEnum
     */
    private Integer payMethod;

    /**
     * 产品标签类型（1：代结算  0：非代结算 未配置则为null）
     */
    private Integer productLabelType;

    /**
     * 是否亏损单 0-否 1-是
     */
    private Integer deficitStatus;

    /**
     * 订单结算方式
     */
    private Integer orderSettlementType;

    /**
     * 供货单号
     */
    private String supplyOrderCode;

    /**
     * 客户经理
     */
    private String agentManagerName;

    /**
     * 一级渠道类型
     * @see SupplyOneLevelChannelTypeEnum
     */
    private Integer oneLevelChannelType;

    /**
     * 二级渠道类型
     * @see SupplyTwoLevelChannelTypeEnum
     */
    private Integer twoLevelChannelType;

    /**
     * 三级渠道类型
     */
    private String threeLevelChannelType;

    /**
     * 订单来源
     * @see com.tiangong.enums.OrderSourceEnum
     */
    private Integer orderSource;

    /**
     * 是否手工订单
     */
    private Integer isManualOrder;

    /**
     * 客户类型
     */
    private String agentType;
}
