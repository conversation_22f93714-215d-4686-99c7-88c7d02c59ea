package com.tiangong.domain.resp;


import lombok.Data;

import java.math.BigDecimal;

/**
 * 应收报表
 */
@Data
public class PayableStatisticsResp {

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 间夜数
     */
    private Integer supplyOrderNightQty;

    /**
     * 供应商应付金额（商家币种）
     */
    private String supplierOrderPayableOrgCurrencyAmt;

    /**
     * 供应商已付金额(商家币种)
     */
    private String supplierOrderPaidOrgCurrencyAmt;

    /**
     * 供应商未付金额(商家币种)
     */
    private String supplierOrderUnpaidOrgCurrencyAmt;

}
