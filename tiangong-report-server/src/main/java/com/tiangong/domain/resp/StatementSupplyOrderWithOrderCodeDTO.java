package com.tiangong.domain.resp;

import com.tiangong.order.remote.response.GuestDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class StatementSupplyOrderWithOrderCodeDTO implements Serializable {

    /**
     * 账单明细id
     */
    private Integer statementOrderId;

    /**
     * 供货单编码
     */
    private String supplyOrderCode;

    /**
     * 创建时间
     */
    private String createdDt;

    /**
     * 酒店名称
     */
    private String hotelName;

    /**
     * 房型名称
     */
    private String roomName;

    /**
     * 入住日期
     */
    private String startDate;

    /**
     * 离店日期
     */
    private String endDate;

    /**
     * 入住人
     */
    private String guest;

    /**
     * 入住人信息
     */
    private List<GuestDTO> guests;

    /**
     * 间数
     */
    private String roomQty;

    /**
     * 应付
     */
    private BigDecimal payableAmt;

    /**
     * 已付
     */
    private BigDecimal paidAmt;

    /**
     * 未付
     */
    private BigDecimal unpaidAmt;

    /**
     * 要付
     */
    private BigDecimal payAmt;

    /**
     * 币种
     */
    private String currency;

    /**
     * 兑换商家币种的汇率
     */
    private String saleRate;

    /**
     * 确认状态：0未确认，1确认，2已取消，3已完成
     * @see com.tiangong.order.enums.ConfirmationStatusEnum
     */
    private Integer confirmationStatus;
    /**
     * 确认信息
     */
    private String confirmationStatusStr;

    /**
     * 供应商订单编码
     */
    private String supplierOrderCode;

    /**
     * 确认号
     */
    private String confirmationCode;

    private String orderCode;

    /**
     * 佣金
     */
    private BigDecimal commission;

    /**
     * 附加费
     */
    private BigDecimal additionalCharges;

    /**
     * 退订费
     */
    private BigDecimal refundFee;

    /**
     * 房费
     */
    private BigDecimal sumBasePrice;

    /**
     * 应付(商家币种)
     */
    private BigDecimal payableOrgCurrencyAmt;

    /**
     * 已付(商家币种)
     */
    private BigDecimal paidOrgCurrencyAmt;

    /**
     * 未付(商家币种)
     */
    private BigDecimal unpaidOrgCurrencyAmt;

    /**
     * 要付(商家币种)
     */
    private BigDecimal payOrgCurrencyAmt;

    /**
     * 房间号（多个用逗号隔开）
     */
    private String roomNumbers;

}
