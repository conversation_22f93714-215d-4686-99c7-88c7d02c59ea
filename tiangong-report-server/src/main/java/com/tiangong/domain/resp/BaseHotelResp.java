package com.tiangong.domain.resp;

import lombok.Data;

import java.io.Serializable;

@Data
public class BaseHotelResp implements Serializable {

    /**
     * 酒店ID
     */
    private String hotelId;

    /**
     * 酒店名称
     */
    private String hotelName;

    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 来源供应商类型
     */
    private String sourceSupplierCode;

    /**
     * 来源供应商ID
     */
    private String sourceHotelId;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private String createdDt;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private String updatedDt;

}
