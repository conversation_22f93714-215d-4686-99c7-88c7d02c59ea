package com.tiangong.domain.resp;


import lombok.Data;

import java.math.BigDecimal;

/**
 * 应收报表
 */
@Data
public class PayableStatisticsDTO {

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 间夜数
     */
    private Integer supplyOrderNightQty;

    /**
     * 应付金额（商家币种）
     */
    private BigDecimal supplierOrderPayableOrgCurrencyAmt;

    /**
     * 供货单已付金额(商家币种)
     */
    private BigDecimal supplierOrderPaidOrgCurrencyAmt;

    /**
     * 供货单未付金额(商家币种)
     */
    private BigDecimal supplierOrderUnpaidOrgCurrencyAmt;

    /**
     * 供应商奖励商家币种
     */
    private BigDecimal rewardOrgCurrencyAmt;

    /**
     * 供应商返佣商家币种
     */
    private BigDecimal rebateOrgCurrencyAmt;

    /**
     * 奖励未收金额(商家币种)
     */
    private BigDecimal rewardUnpaidOrgCurrencyAmt;

    /**
     * 返佣未付金额(商家币种)
     */
    private BigDecimal rebateUnpaidOrgCurrencyAmt;

}
