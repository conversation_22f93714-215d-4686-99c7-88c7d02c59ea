package com.tiangong.server;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.opencsv.CSVWriter;
import com.tiangong.cloud.common.constant.HttpConstant;
import com.tiangong.cloud.common.domain.LoginUser;
import com.tiangong.cloud.commonbean.config.security.TokenManager;
import com.tiangong.common.Response;
import com.tiangong.dis.enums.DhubReturnCodeEnum;
import com.tiangong.domain.dto.ExportOrderCancelReasonDTO;
import com.tiangong.domain.dto.ExportOrderRemarkDTO;
import com.tiangong.domain.entity.ExportReportEntity;
import com.tiangong.domain.req.ExportOrderCancelReasonReq;
import com.tiangong.domain.req.ExportOrderRemarkReq;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.enums.LanguageTypeEnum;
import com.tiangong.enums.ReportConstants;
import com.tiangong.enums.ReportStatus;
import com.tiangong.enums.ReportType;
import com.tiangong.mapper.ExportReportMapper;
import com.tiangong.service.OrderRemarkService;
import com.tiangong.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.util.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.util.StopWatch;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.*;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.zip.ZipOutputStream;

/**
 * <AUTHOR>
 * @Date 2024/7/10 下午2:31
 * @Description:
 */

@RestController
@Slf4j
@RequestMapping(value = "/report/order")
public class OrderRemarkServer {

    @Autowired
    private GenerateExportCode generateExportCode;

    @Autowired
    private ExportReportMapper exportReportMapper;

    @Autowired
    private FileUpUtil fileUpUtil;

    @Autowired
    HttpServletRequest httpServletRequest;

    @Autowired
    private OrderRemarkService orderRemarkService;

    //循环次数
    public final Integer NUMBER_OF_CYCLES = 99999;

    /**
     * 导出订单备注
     */
    @PostMapping("/orderRemark")
    public Response<Object> exportOrderRemark(@RequestBody ExportOrderRemarkReq request) {
        if (StrUtilX.isEmpty(request.getStartDate()) || StrUtilX.isEmpty(request.getEndDate())) {
            return Response.failure(DhubReturnCodeEnum.INVALID_INPUTPARAM.code, DhubReturnCodeEnum.INVALID_INPUTPARAM.description, "时间范围错误!");
        }
        String filePrefix = request.getStartDate() + "_" + request.getEndDate();

        LoginUser loginUser = TokenManager.getUser(httpServletRequest);
        String language = httpServletRequest.getHeader(HttpConstant.Language);

        String exportCode = generateExportCode.getExportCode(ReportType.REPORT_ORDER_REMARK);
        //String saveAddress = saveFileProperties.getSaveAddress() + exportCode + ".zip";
        ExportReportEntity exportReport = ExportReportEntity.builder().exportCode(exportCode)
                .exportType(ReportType.REPORT_ORDER_REMARK.key)
                .exportStatus(ReportStatus.EXPORTING.key)
                //.fileUrl(saveAddress)
                .createdBy(loginUser.getFullUserName())
                .createdDt(new Date())
                .updatedBy(loginUser.getFullUserName())
                .updatedDt(new Date())
                .build();
        exportReportMapper.insert(exportReport);

        CompletableFuture<Void> completableFuture = CompletableFuture.runAsync(() -> {
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ZipOutputStream zipOut = new ZipOutputStream(baos);
            StopWatch stopWatch = new StopWatch();
            stopWatch.start("1.查询数据");

            try {
                int pageNum = 0; //文件数
                boolean hasMoreData = true;

                for (int countNum = 0; countNum < NUMBER_OF_CYCLES; countNum++) {
                    if (!hasMoreData) {
                        break;
                    }
                    // 文件名称
                    String csvFileName = filePrefix + ReportConstants.EXPORT_ORDER_REMARK_FILE_NAME + pageNum + ".csv";
                    String zipEntryName = filePrefix + ReportConstants.EXPORT_ORDER_REMARK_FILE_NAME + pageNum + ".csv";

                    // 默认中文
                    String[] EXPORT_ORDER_REMARK_TABLE_HEADER = ReportConstants.EXPORT_ORDER_REMARK_TABLE_HEADER;
                    // 判断语言
                    if (StrUtilX.isNotEmpty(language) && language.equals(LanguageTypeEnum.en_US.getValue())) {
                        csvFileName = filePrefix + ReportConstants.EXPORT_ORDER_REMARK_FILE_NAME_EN_US + pageNum + ".csv";
                        zipEntryName = filePrefix + ReportConstants.EXPORT_ORDER_REMARK_FILE_NAME_EN_US + pageNum + ".csv";
                        EXPORT_ORDER_REMARK_TABLE_HEADER = ReportConstants.EXPORT_ORDER_REMARK_TABLE_HEADER_EN_US;
                    }

                    CSVWriter csvWriter = new CSVWriter(new FileWriter(csvFileName));

                    csvWriter.writeNext(EXPORT_ORDER_REMARK_TABLE_HEADER);
                    for (int i = 1; i <= 10; i++) {
                        if (!hasMoreData) {
                            break;
                        }
                        int num = pageNum * 10 + i; //页数
                        request.setCurrentPage(num);
                        request.setPageSize(5000);

                        PaginationSupportDTO<ExportOrderRemarkDTO> exportOrderRemarkDTOS = orderRemarkService.queryOrderRemarkInfos(request);
                        List<ExportOrderRemarkDTO> exportOrderRemarkDTOList = exportOrderRemarkDTOS.getItemList();

                        if (CollUtilX.isEmpty(exportOrderRemarkDTOList) || exportOrderRemarkDTOList.size() < 5000) {
                            hasMoreData = false;
                        }

                        if (CollUtilX.isNotEmpty(exportOrderRemarkDTOList)) {
                            for (ExportOrderRemarkDTO exportOrderRemarkDTO : exportOrderRemarkDTOList) {
                                String[] info = {exportOrderRemarkDTO.getOrderCode(), exportOrderRemarkDTO.getOrderConfirmationStatusDesc(), exportOrderRemarkDTO.getSupplyOrderCode(),
                                        exportOrderRemarkDTO.getAgentCode(), exportOrderRemarkDTO.getAgentName(), exportOrderRemarkDTO.getSupplierCode(),
                                        exportOrderRemarkDTO.getSupplierName(), exportOrderRemarkDTO.getContactName(), exportOrderRemarkDTO.getOrderCreatedDt(),
                                        exportOrderRemarkDTO.getStartDate(), exportOrderRemarkDTO.getEndDate(), exportOrderRemarkDTO.getConfirmationCreatedDt(),
                                        exportOrderRemarkDTO.getRemarkTypeDesc(), exportOrderRemarkDTO.getRemark(), exportOrderRemarkDTO.getOrderRemarkCreatedBy(),
                                        exportOrderRemarkDTO.getOrderRemarkCreatedDt()
                                };
                                csvWriter.writeNext(info);
                            }
                        }
                        csvWriter.flush();
                    }
                    pageNum++;
                    // Csv文件压缩成Zip
                    CsvUtils.setZipByCsv(csvFileName, zipEntryName, zipOut);
                }
                zipOut.finish();

                InputStream inputStream = null;
                try {
                    byte[] bytes = baos.toByteArray();

                    inputStream = new ByteArrayInputStream(bytes);

                    MultipartFile multipartFile = new MockMultipartFile("file", exportCode + ".zip", "zip", inputStream);

                    String fileSuffixName = "jiali_tiangong/" + multipartFile.getOriginalFilename();
                    String fileUrl = fileUpUtil.uploadFile2(multipartFile, fileSuffixName);
                    log.info("导出订单备注表 fileUrl=" + fileUrl);

                    stopWatch.stop();
                    log.info("导出订单备注表耗时情况:{}", stopWatch.prettyPrint());
                    exportReportMapper.update(null, new UpdateWrapper<ExportReportEntity>().lambda()
                            .eq(ExportReportEntity::getId, exportReport.getId())
                            .set(ExportReportEntity::getFileUrl, fileUrl)
                            .set(ExportReportEntity::getExportStatus, ReportStatus.EXPORT_SUCCESS.key)
                            .set(ExportReportEntity::getUpdatedBy, loginUser.getFullUserName())
                            .set(ExportReportEntity::getUpdatedDt, new Date()));
                } catch (IOException e) {
                    log.error("OrderRemark has error", e);
                    exportReportMapper.update(null, new UpdateWrapper<ExportReportEntity>().lambda()
                            .eq(ExportReportEntity::getId, exportReport.getId())
                            .set(ExportReportEntity::getExportStatus, ReportStatus.EXPORT_FAILED.key)
                            .set(ExportReportEntity::getUpdatedBy, loginUser.getFullUserName())
                            .set(ExportReportEntity::getUpdatedDt, new Date()));
                } finally {
                    if (null != inputStream) {
                        IOUtils.closeQuietly(inputStream);
                    }
                }

            } catch (Exception e) {
                log.error("导出订单备注异常", e);
                exportReportMapper.update(null, new UpdateWrapper<ExportReportEntity>().lambda()
                        .eq(ExportReportEntity::getId, exportReport.getId())
                        .set(ExportReportEntity::getExportStatus, ReportStatus.EXPORT_FAILED.key)
                        .set(ExportReportEntity::getUpdatedBy, loginUser.getFullUserName())
                        .set(ExportReportEntity::getUpdatedDt, new Date()));
            }
        });
        return Response.success("导出中,请到导出统计表中查看导出结果");
    }

    /**
     * 导出订单取消原因
     */
    @PostMapping("/exportOrderCancelReason")
    public Response<Object> exportOrderCancelReason(@RequestBody ExportOrderCancelReasonReq request) {
        if (StrUtilX.isEmpty(request.getStartDate()) || StrUtilX.isEmpty(request.getEndDate())) {
            return Response.failure(DhubReturnCodeEnum.INVALID_INPUTPARAM.code, DhubReturnCodeEnum.INVALID_INPUTPARAM.description, "时间范围错误!");
        }
        String filePrefix = request.getStartDate() + "_" + request.getEndDate();

        LoginUser loginUser = TokenManager.getUser(httpServletRequest);
        String language = httpServletRequest.getHeader(HttpConstant.Language);

        String exportCode = generateExportCode.getExportCode(ReportType.REPORT_ORDER_CANCEL_REASON);
        ExportReportEntity exportReport = ExportReportEntity.builder().exportCode(exportCode)
                .exportType(ReportType.REPORT_ORDER_CANCEL_REASON.key)
                .exportStatus(ReportStatus.EXPORTING.key)
                .createdBy(loginUser.getFullUserName())
                .createdDt(new Date())
                .updatedBy(loginUser.getFullUserName())
                .updatedDt(new Date())
                .build();
        exportReportMapper.insert(exportReport);

        CompletableFuture<Void> completableFuture = CompletableFuture.runAsync(() -> {
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ZipOutputStream zipOut = new ZipOutputStream(baos);
            StopWatch stopWatch = new StopWatch();
            stopWatch.start("1.查询数据");

            try {
                int pageNum = 0; //文件数
                boolean hasMoreData = true;

                for (int countNum = 0; countNum < NUMBER_OF_CYCLES; countNum++) {
                    if (!hasMoreData) {
                        break;
                    }
                    // 文件名称
                    String csvFileName = filePrefix + ReportConstants.EXPORT_ORDER_CANCEL_REASON_FILE_NAME + pageNum + ".csv";
                    String zipEntryName = filePrefix + ReportConstants.EXPORT_ORDER_CANCEL_REASON_FILE_NAME + pageNum + ".csv";

                    // 默认中文
                    String[] EXPORT_ORDER_CANCEL_REASON_TABLE_HEADER = ReportConstants.EXPORT_ORDER_CANCEL_REASON_TABLE_HEADER;
                    // 判断语言
                    if (StrUtilX.isNotEmpty(language) && language.equals(LanguageTypeEnum.en_US.getValue())) {
                        csvFileName = filePrefix + ReportConstants.EXPORT_ORDER_CANCEL_REASON_FILE_NAME_EN_US + pageNum + ".csv";
                        zipEntryName = filePrefix + ReportConstants.EXPORT_ORDER_CANCEL_REASON_FILE_NAME_EN_US + pageNum + ".csv";
                        EXPORT_ORDER_CANCEL_REASON_TABLE_HEADER = ReportConstants.EXPORT_ORDER_CANCEL_REASON_TABLE_HEADER_EN_US;
                    }

                    CSVWriter csvWriter = new CSVWriter(new FileWriter(csvFileName));

                    csvWriter.writeNext(EXPORT_ORDER_CANCEL_REASON_TABLE_HEADER);
                    for (int i = 1; i <= 10; i++) {
                        if (!hasMoreData) {
                            break;
                        }
                        int num = pageNum * 10 + i; //页数
                        request.setCurrentPage(num);
                        request.setPageSize(5000);

                        PaginationSupportDTO<ExportOrderCancelReasonDTO> exportOrderCancelReasonPage = orderRemarkService.queryOrderCancelReasonInfos(request);
                        List<ExportOrderCancelReasonDTO> exportOrderCancelReasonList = exportOrderCancelReasonPage.getItemList();

                        if (CollUtilX.isEmpty(exportOrderCancelReasonList) || exportOrderCancelReasonList.size() < 5000) {
                            hasMoreData = false;
                        }

                        if (CollUtilX.isNotEmpty(exportOrderCancelReasonList)) {
                            for (ExportOrderCancelReasonDTO dto : exportOrderCancelReasonList) {
                                String[] info = {dto.getOrderCode(), dto.getOrderConfirmationDurationDesc(), dto.getCancelReason(), dto.getCancelContent(),
                                        dto.getOrderConfirmationStatusDesc(), dto.getAgentName(), dto.getOrderCreatedDt(), dto.getCheckInDate(), dto.getCheckOutDate(),
                                        dto.getOrderNightQty(), dto.getHotelName(), dto.getCityName(), dto.getProvinceName(), dto.getCountryName(), dto.getIsCnOrder()};
                                csvWriter.writeNext(info);
                            }
                        }
                        csvWriter.flush();
                    }
                    pageNum++;
                    // Csv文件压缩成Zip
                    CsvUtils.setZipByCsv(csvFileName, zipEntryName, zipOut);
                }
                zipOut.finish();

                InputStream inputStream = null;
                try {
                    byte[] bytes = baos.toByteArray();

                    inputStream = new ByteArrayInputStream(bytes);

                    MultipartFile multipartFile = new MockMultipartFile("file", exportCode + ".zip", "zip", inputStream);

                    String fileSuffixName = "jiali_tiangong/" + multipartFile.getOriginalFilename();
                    String fileUrl = fileUpUtil.uploadFile2(multipartFile, fileSuffixName);
                    log.info("导出订单取消原因表 fileUrl=" + fileUrl);

                    stopWatch.stop();
                    log.info("导出订单取消原因表耗时情况:{}", stopWatch.prettyPrint());
                    exportReportMapper.update(null, new UpdateWrapper<ExportReportEntity>().lambda()
                            .eq(ExportReportEntity::getId, exportReport.getId())
                            .set(ExportReportEntity::getFileUrl, fileUrl)
                            .set(ExportReportEntity::getExportStatus, ReportStatus.EXPORT_SUCCESS.key)
                            .set(ExportReportEntity::getUpdatedBy, loginUser.getFullUserName())
                            .set(ExportReportEntity::getUpdatedDt, new Date()));
                } catch (IOException e) {
                    log.error("OrderCancelReason has error", e);
                    exportReportMapper.update(null, new UpdateWrapper<ExportReportEntity>().lambda()
                            .eq(ExportReportEntity::getId, exportReport.getId())
                            .set(ExportReportEntity::getExportStatus, ReportStatus.EXPORT_FAILED.key)
                            .set(ExportReportEntity::getUpdatedBy, loginUser.getFullUserName())
                            .set(ExportReportEntity::getUpdatedDt, new Date()));
                } finally {
                    if (null != inputStream) {
                        IOUtils.closeQuietly(inputStream);
                    }
                }

            } catch (Exception e) {
                log.error("导出订单取消原因异常", e);
                exportReportMapper.update(null, new UpdateWrapper<ExportReportEntity>().lambda()
                        .eq(ExportReportEntity::getId, exportReport.getId())
                        .set(ExportReportEntity::getExportStatus, ReportStatus.EXPORT_FAILED.key)
                        .set(ExportReportEntity::getUpdatedBy, loginUser.getFullUserName())
                        .set(ExportReportEntity::getUpdatedDt, new Date()));
            }
        });
        return Response.success("导出中,请到导出统计表中查看导出结果");
    }
}
