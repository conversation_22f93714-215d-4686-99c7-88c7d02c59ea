package com.tiangong.server;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.tiangong.cloud.common.domain.LoginUser;
import com.tiangong.cloud.commonbean.config.security.TokenManager;
import com.tiangong.common.Response;
import com.tiangong.domain.dto.OperateLogSearchDTO;
import com.tiangong.domain.dto.QueryOperateLogDTO;
import com.tiangong.domain.entity.ExportReportEntity;
import com.tiangong.domain.req.OperateLogSearchReq;
import com.tiangong.enums.OperationTypeEnum;
import com.tiangong.enums.ReportStatus;
import com.tiangong.enums.ReportType;
import com.tiangong.mapper.ExportReportMapper;
import com.tiangong.mapper.OperateLogMapper;
import com.tiangong.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.util.IOUtils;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @Date 2023/7/4 15:32
 * @Description:
 */
@RestController
@Slf4j
@RequestMapping(value = "/report")
public class AuditLogServer {

    @Autowired
    private GenerateExportCode generateExportCode;

    @Autowired
    private OperateLogMapper operateLogMapper;

    @Autowired
    private ExportReportMapper exportReportMapper;

    @Autowired
    private FileUpUtil fileUpUtil;

    @Autowired
    HttpServletRequest request;


    @RequestMapping(value = "/auditLog/exportAuditLog", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public Response exportAuditLog(@RequestBody OperateLogSearchReq req) {
        QueryOperateLogDTO queryOperateLogDTO = new QueryOperateLogDTO();
        queryOperateLogDTO.setOperateAccount(req.getOperateAccount());
        queryOperateLogDTO.setLogType(req.getLogType());
        if (req.getOperateStartDate() != null && req.getOperateEndDate() != null) {
            queryOperateLogDTO.setOperateStartDate(DateUtilX.dateToString(req.getOperateStartDate()));
            queryOperateLogDTO.setOperateEndDate(DateUtilX.dateToString(DateUtilX.addDate(req.getOperateEndDate(), 1)));
        }
        queryOperateLogDTO.setOperateResult(req.getOperateResult());
        queryOperateLogDTO.setOperationType(req.getOperationType());

        LoginUser loginUser = TokenManager.getUser(request);

        String exportCode = generateExportCode.getExportCode(ReportType.REPORT_LOG);
//        String saveAddress = saveFileProperties.getSaveAddress() + exportCode + ".xlsx";
        ExportReportEntity exportReport = ExportReportEntity.builder().exportCode(exportCode)
                .exportType(ReportType.REPORT_LOG.key)
                .exportStatus(ReportStatus.EXPORTING.key)
//                .fileUrl(saveAddress)
                .createdBy(loginUser.getFullUserName())
                .createdDt(new Date())
                .updatedBy(loginUser.getFullUserName())
                .updatedDt(new Date())
                .build();
        exportReportMapper.insert(exportReport);

        CompletableFuture<Void> completableFuture = CompletableFuture.runAsync(() -> {
            InputStream inputStream = null;
            try {

                SXSSFWorkbook workbook = new SXSSFWorkbook(5000);
                Row row = null;
                Sheet sheet = workbook.createSheet("审计日志");
                Row rowTop = sheet.createRow(0);
                rowTop.createCell(0).setCellValue("操作时间");
                rowTop.createCell(1).setCellValue("操作人");
                rowTop.createCell(2).setCellValue("IP地址");
                rowTop.createCell(3).setCellValue("事件类型");
                rowTop.createCell(4).setCellValue("操作类型");
                rowTop.createCell(5).setCellValue("操作内容");
                rowTop.createCell(6).setCellValue("操作结果");

                int currentPage = 0;
                long pageSize = 10000L;
                int breakNum = 10000;
                int rowNum = 1;
                queryOperateLogDTO.setPageSize(pageSize);
                while (currentPage < breakNum) {
                    queryOperateLogDTO.setCurrentPage(currentPage * pageSize);
                    List<OperateLogSearchDTO> operateLogSearchDTOS = operateLogMapper.queryListPage(queryOperateLogDTO);
                    if (CollUtilX.isNotEmpty(operateLogSearchDTOS)) {
                        for (OperateLogSearchDTO operateLogSearchDTO : operateLogSearchDTOS) {
                            row = sheet.createRow(rowNum++);
                            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                            String createdDt = operateLogSearchDTO.getCreatedDt().format(formatter);
                            row.createCell(0).setCellValue(createdDt);
                            row.createCell(1).setCellValue(operateLogSearchDTO.getCreatedBy());
                            row.createCell(2).setCellValue(operateLogSearchDTO.getRequestIp());
                            if (operateLogSearchDTO.getLogLevel() != null) {
                                row.createCell(3).setCellValue(operateLogSearchDTO.getLogLevel() == 0 ? "系统级事件" : "业务级事件");
                            }
                            String operationNameByOperationType = OperationTypeEnum.getOperationNameByOperationType(operateLogSearchDTO.getOperationType());
                            if (StrUtilX.isNotEmpty(operationNameByOperationType)) {
                                row.createCell(4).setCellValue(operationNameByOperationType);
                            }
                            if (StrUtilX.isNotEmpty(operateLogSearchDTO.getRequestParam())) {
                                row.createCell(5).setCellValue(operateLogSearchDTO.getLogName() + ": " + operateLogSearchDTO.getRequestParam());
                            } else {
                                row.createCell(5).setCellValue(operateLogSearchDTO.getLogName());
                            }
                            if (operateLogSearchDTO.getOperationResult() != null) {
                                row.createCell(6).setCellValue(operateLogSearchDTO.getOperationResult() == 0 ? "失败" : "成功");
                            }
                        }
                    } else {
                        break;
                    }
                    currentPage++;
                }
                sheet.setColumnWidth(0, 25 * 256);
                sheet.setColumnWidth(1, 25 * 256);
                sheet.setColumnWidth(2, 25 * 256);
                sheet.setColumnWidth(3, 25 * 256);
                sheet.setColumnWidth(4, 25 * 256);
                sheet.setColumnWidth(5, 25 * 256);
                sheet.setColumnWidth(6, 25 * 256);

                ByteArrayOutputStream outputTmp = new ByteArrayOutputStream();
                workbook.write(outputTmp);
                outputTmp.flush();
                byte[] byteArray = outputTmp.toByteArray();
                IOUtils.closeQuietly(outputTmp);
                inputStream = new ByteArrayInputStream(byteArray, 0, byteArray.length);
                MultipartFile multipartFile = new MockMultipartFile("file", exportCode + ".xlsx", "xlsx", inputStream);

                String fileSuffixName = "jiali_tiangong/" + multipartFile.getOriginalFilename();
                String fileUrl = fileUpUtil.uploadFile2(multipartFile, fileSuffixName);
                log.info("导出审计日志 fileUrl=" + fileUrl);

                exportReportMapper.update(null, new UpdateWrapper<ExportReportEntity>().lambda()
                        .eq(ExportReportEntity::getId, exportReport.getId())
                        .set(ExportReportEntity::getFileUrl, fileUrl)
                        .set(ExportReportEntity::getExportStatus, ReportStatus.EXPORT_SUCCESS.key)
                        .set(ExportReportEntity::getUpdatedBy, loginUser.getFullUserName())
                        .set(ExportReportEntity::getUpdatedDt, new Date()));
            } catch (IOException e) {
                log.error("exportAuditLog has error", e);
                exportReportMapper.update(null, new UpdateWrapper<ExportReportEntity>().lambda()
                        .eq(ExportReportEntity::getId, exportReport.getId())
                        .set(ExportReportEntity::getExportStatus, ReportStatus.EXPORT_FAILED.key)
                        .set(ExportReportEntity::getUpdatedBy, loginUser.getFullUserName())
                        .set(ExportReportEntity::getUpdatedDt, new Date()));
            } finally {
                if (null != inputStream) {
                    IOUtils.closeQuietly(inputStream);
                }
            }
        });
        return Response.success("导出中,请到导出统计表中查看导出结果");
    }

}
