package com.tiangong.supply.direct.enums;

import com.tiangong.enums.ErrorCodeEnum;

/**
 * <AUTHOR>
 * @Date 2020-2-5 15:36
 * @Description:
 */
public enum ReturnNoEnum {


    /************供应商共用错误码配置开始*****************/
    //供应商共用试预定
    _010000000("010000000", ErrorCodeEnum.SUCCESS.errorCode, "成功"),
    _010701033(BU.HOTEL.NO + APP.H_SYNC_SUPPLIER_COMMON.NO + BS.PRE_BOOKING.NO + ErrorCodeEnum.MERCHANT_NOT_CONFIGURED.errorNo, ErrorCodeEnum.MERCHANT_NOT_CONFIGURED.errorCode, "试预订失败，无该商家配置"),
    _010701004(BU.HOTEL.NO + APP.H_SYNC_SUPPLIER_COMMON.NO + BS.PRE_BOOKING.NO + ErrorCodeEnum.INVALID_INPUTPARAM.errorNo, ErrorCodeEnum.INVALID_INPUTPARAM.errorCode, "试预订失败，请求参数不正确"),
    _010701003(BU.HOTEL.NO + APP.H_SYNC_SUPPLIER_COMMON.NO + BS.PRE_BOOKING.NO + ErrorCodeEnum.DISSATISFY_QUOTA.errorNo, ErrorCodeEnum.DISSATISFY_QUOTA.errorCode, "试预订失败，酒店配额不足"),
    _010701009(BU.HOTEL.NO + APP.H_SYNC_SUPPLIER_COMMON.NO + BS.PRE_BOOKING.NO + ErrorCodeEnum.DISSATISFY_BOOKROOMSCLAUSE.errorNo, ErrorCodeEnum.DISSATISFY_BOOKROOMSCLAUSE.errorCode, "试预订失败，不满足间数条款"),
    _010701002(BU.HOTEL.NO + APP.H_SYNC_SUPPLIER_COMMON.NO + BS.PRE_BOOKING.NO + ErrorCodeEnum.PRICE_NEED_QUERY.errorNo, ErrorCodeEnum.PRICE_NEED_QUERY.errorCode, "试预订失败，价格待查"),
    _010701001(BU.HOTEL.NO + APP.H_SYNC_SUPPLIER_COMMON.NO + BS.PRE_BOOKING.NO + ErrorCodeEnum.FULL_ROOM.errorNo, ErrorCodeEnum.FULL_ROOM.errorCode, "试预订失败，满房"),
    _010701903(BU.HOTEL.NO + APP.H_SYNC_SUPPLIER_COMMON.NO + BS.PRE_BOOKING.NO + ErrorCodeEnum.CONNECT_TIME_OUT.errorNo, ErrorCodeEnum.CONNECT_TIME_OUT.errorCode, "试预订失败，连接超时"),
    _010701904(BU.HOTEL.NO + APP.H_SYNC_SUPPLIER_COMMON.NO + BS.PRE_BOOKING.NO + ErrorCodeEnum.READ_TIME_OUT.errorNo, ErrorCodeEnum.READ_TIME_OUT.errorCode, "试预订失败，响应超时"),
    _010701083(BU.HOTEL.NO + APP.H_SYNC_SUPPLIER_COMMON.NO + BS.PRE_BOOKING.NO + ErrorCodeEnum.SUPPLY_RETURN_RESULT_FORMAT_ERROR.errorNo, ErrorCodeEnum.SUPPLY_RETURN_RESULT_FORMAT_ERROR.errorCode, "试预订失败，返回格式不正确"),
    _010701084(BU.HOTEL.NO + APP.H_SYNC_SUPPLIER_COMMON.NO + BS.PRE_BOOKING.NO + ErrorCodeEnum.SUPPLY_RETURN_RESULT_FAIL.errorNo, ErrorCodeEnum.SUPPLY_RETURN_RESULT_FAIL.errorCode, "试预订失败，返回失败结果"),
    _010701900(BU.HOTEL.NO + APP.H_SYNC_SUPPLIER_COMMON.NO + BS.PRE_BOOKING.NO + ErrorCodeEnum.SYSTEM_EXCEPTION.errorNo, ErrorCodeEnum.SYSTEM_EXCEPTION.errorCode, "试预订失败，系统异常"),
    //供应商共用创建订单
    _010702033(BU.HOTEL.NO + APP.H_SYNC_SUPPLIER_COMMON.NO + BS.CREATE_ORDER.NO + ErrorCodeEnum.MERCHANT_NOT_CONFIGURED.errorNo, ErrorCodeEnum.MERCHANT_NOT_CONFIGURED.errorCode, "下单失败，无该商家配置"),
    _010702903(BU.HOTEL.NO + APP.H_SYNC_SUPPLIER_COMMON.NO + BS.CREATE_ORDER.NO + ErrorCodeEnum.CONNECT_TIME_OUT.errorNo, ErrorCodeEnum.CONNECT_TIME_OUT.errorCode, "下单失败，连接超时"),
    _010702904(BU.HOTEL.NO + APP.H_SYNC_SUPPLIER_COMMON.NO + BS.CREATE_ORDER.NO + ErrorCodeEnum.READ_TIME_OUT.errorNo, ErrorCodeEnum.READ_TIME_OUT.errorCode, "下单失败，响应超时"),
    _010702081(BU.HOTEL.NO + APP.H_SYNC_SUPPLIER_COMMON.NO + BS.CREATE_ORDER.NO + ErrorCodeEnum.SUPPLY_INTERFACE_EXCEPTION.errorNo, ErrorCodeEnum.SUPPLY_INTERFACE_EXCEPTION.errorCode, "下单失败，接口异常"),
    _010702083(BU.HOTEL.NO + APP.H_SYNC_SUPPLIER_COMMON.NO + BS.CREATE_ORDER.NO + ErrorCodeEnum.SUPPLY_RETURN_RESULT_FORMAT_ERROR.errorNo, ErrorCodeEnum.SUPPLY_RETURN_RESULT_FORMAT_ERROR.errorCode, "下单失败，返回格式不正确"),
    _010702084(BU.HOTEL.NO + APP.H_SYNC_SUPPLIER_COMMON.NO + BS.CREATE_ORDER.NO + ErrorCodeEnum.SUPPLY_RETURN_RESULT_FAIL.errorNo, ErrorCodeEnum.SUPPLY_RETURN_RESULT_FAIL.errorCode, "下单失败，返回订单状态不确认"),
    _010702085(BU.HOTEL.NO + APP.H_SYNC_SUPPLIER_COMMON.NO + BS.CREATE_ORDER.NO + ErrorCodeEnum.SUPPLY_NO_HAS_PRODUCT.errorNo, ErrorCodeEnum.SUPPLY_NO_HAS_PRODUCT.errorCode, "供应商产品不存在"),
    _010702900(BU.HOTEL.NO + APP.H_SYNC_SUPPLIER_COMMON.NO + BS.CREATE_ORDER.NO + ErrorCodeEnum.SYSTEM_EXCEPTION.errorNo, ErrorCodeEnum.SYSTEM_EXCEPTION.errorCode, "下单失败，系统异常"),
    //供应商共用取消订单
    _010703033(BU.HOTEL.NO + APP.H_SYNC_SUPPLIER_COMMON.NO + BS.CANCLE_ORDER.NO + ErrorCodeEnum.MERCHANT_NOT_CONFIGURED.errorNo, ErrorCodeEnum.MERCHANT_NOT_CONFIGURED.errorCode, "取消订单失败，无该商家配置"),
    _010703005(BU.HOTEL.NO + APP.H_SYNC_SUPPLIER_COMMON.NO + BS.CANCLE_ORDER.NO + ErrorCodeEnum.INVALID_INPUTPARAM.errorNo, ErrorCodeEnum.INVALID_INPUTPARAM.errorCode, "取消订单失败，请求参数不正确"),
    _010703903(BU.HOTEL.NO + APP.H_SYNC_SUPPLIER_COMMON.NO + BS.CANCLE_ORDER.NO + ErrorCodeEnum.CONNECT_TIME_OUT.errorNo, ErrorCodeEnum.CONNECT_TIME_OUT.errorCode, "取消订单失败，连接超时"),
    _010703904(BU.HOTEL.NO + APP.H_SYNC_SUPPLIER_COMMON.NO + BS.CANCLE_ORDER.NO + ErrorCodeEnum.READ_TIME_OUT.errorNo, ErrorCodeEnum.READ_TIME_OUT.errorCode, "接口访问超时"),
    _010703084(BU.HOTEL.NO + APP.H_SYNC_SUPPLIER_COMMON.NO + BS.CANCLE_ORDER.NO + ErrorCodeEnum.SUPPLY_RETURN_RESULT_FAIL.errorNo, ErrorCodeEnum.SUPPLY_RETURN_RESULT_FAIL.errorCode, "取消订单失败，返回取消失败结果"),
    _010703081(BU.HOTEL.NO + APP.H_SYNC_SUPPLIER_COMMON.NO + BS.CANCLE_ORDER.NO + ErrorCodeEnum.SUPPLY_INTERFACE_EXCEPTION.errorNo, ErrorCodeEnum.SUPPLY_INTERFACE_EXCEPTION.errorCode, "取消订单失败，供应商接口异常"),
    _010703082(BU.HOTEL.NO + APP.H_SYNC_SUPPLIER_COMMON.NO + BS.CANCLE_ORDER.NO + ErrorCodeEnum.SUPPLY_RETURN_RESULT_EMPTY.errorNo, ErrorCodeEnum.SUPPLY_RETURN_RESULT_EMPTY.errorCode, "取消订单失败，供应商返回结果为空"),
    _010703083(BU.HOTEL.NO + APP.H_SYNC_SUPPLIER_COMMON.NO + BS.CANCLE_ORDER.NO + ErrorCodeEnum.SUPPLY_RETURN_RESULT_FORMAT_ERROR.errorNo, ErrorCodeEnum.SUPPLY_RETURN_RESULT_FORMAT_ERROR.errorCode, "取消订单失败，供应商返回结果格式不正确"),
    _010703064(BU.HOTEL.NO + APP.H_SYNC_SUPPLIER_COMMON.NO + BS.CANCLE_ORDER.NO + ErrorCodeEnum.CANCEL_FAILED.errorNo, ErrorCodeEnum.CANCEL_FAILED.errorCode, "取消订单失败"),
    _010703057(BU.HOTEL.NO + APP.H_SYNC_SUPPLIER_COMMON.NO + BS.CANCLE_ORDER.NO + ErrorCodeEnum.DISSATISFY_CANCELCLAUSE.errorNo, ErrorCodeEnum.DISSATISFY_CANCELCLAUSE.errorCode, "取消订单失败,不满足取消条款或者合作商系统异常无法取消"),
    _010703900(BU.HOTEL.NO + APP.H_SYNC_SUPPLIER_COMMON.NO + BS.CANCLE_ORDER.NO + ErrorCodeEnum.SYSTEM_EXCEPTION.errorNo, ErrorCodeEnum.SYSTEM_EXCEPTION.errorCode, "取消订单失败，系统异常"),
    //供应商共用查询订单
    _010704033(BU.HOTEL.NO + APP.H_SYNC_SUPPLIER_COMMON.NO + BS.QUERY_ORDERSTATUS.NO + ErrorCodeEnum.MERCHANT_NOT_CONFIGURED.errorNo, ErrorCodeEnum.MERCHANT_NOT_CONFIGURED.errorCode, "查询订单失败，无该商家配置"),
    _010704081(BU.HOTEL.NO + APP.H_SYNC_SUPPLIER_COMMON.NO + BS.QUERY_ORDERSTATUS.NO + ErrorCodeEnum.SUPPLY_INTERFACE_EXCEPTION.errorNo, ErrorCodeEnum.SUPPLY_INTERFACE_EXCEPTION.errorCode, "查询订单失败，供应商接口异常"),
    _010704082(BU.HOTEL.NO + APP.H_SYNC_SUPPLIER_COMMON.NO + BS.QUERY_ORDERSTATUS.NO + ErrorCodeEnum.SUPPLY_RETURN_RESULT_EMPTY.errorNo, ErrorCodeEnum.SUPPLY_RETURN_RESULT_EMPTY.errorCode, "查询订单接口异常,返回结果为空"),
    _010704083(BU.HOTEL.NO + APP.H_SYNC_SUPPLIER_COMMON.NO + BS.QUERY_ORDERSTATUS.NO + ErrorCodeEnum.SUPPLY_RETURN_RESULT_FORMAT_ERROR.errorNo, ErrorCodeEnum.SUPPLY_RETURN_RESULT_FORMAT_ERROR.errorCode, "查询订单失败，返回格式不正确"),
    _010704084(BU.HOTEL.NO + APP.H_SYNC_SUPPLIER_COMMON.NO + BS.QUERY_ORDERSTATUS.NO + ErrorCodeEnum.SUPPLY_RETURN_RESULT_FAIL.errorNo, ErrorCodeEnum.SUPPLY_RETURN_RESULT_FAIL.errorCode, "查询订单接口,返回查询订单失败"),
    _010704900(BU.HOTEL.NO + APP.H_SYNC_SUPPLIER_COMMON.NO + BS.QUERY_ORDERSTATUS.NO + ErrorCodeEnum.SYSTEM_EXCEPTION.errorNo, ErrorCodeEnum.SYSTEM_EXCEPTION.errorCode, "查询订单失败，系统异常"),
    _010704903(BU.HOTEL.NO + APP.H_SYNC_SUPPLIER_COMMON.NO + BS.QUERY_ORDERSTATUS.NO + ErrorCodeEnum.CONNECT_TIME_OUT.errorNo, ErrorCodeEnum.CONNECT_TIME_OUT.errorCode, "查询订单失败，连接超时"),
    _010704904(BU.HOTEL.NO + APP.H_SYNC_SUPPLIER_COMMON.NO + BS.QUERY_ORDERSTATUS.NO + ErrorCodeEnum.READ_TIME_OUT.errorNo, ErrorCodeEnum.READ_TIME_OUT.errorCode, "接口访问超时"),

    /** 供应端订单类返回码公共配置调整    */

    /**
     * 应用场景必要参数非空检查
     */
    _011202005(BU.HOTEL.NO + APP.H_SYNC_COMMON_ORDER.NO + BS.CREATE_ORDER.NO + ErrorCodeEnum.INVALID_INPUTPARAM.errorNo, ErrorCodeEnum.INVALID_INPUTPARAM.errorCode, "发单接口无效的入参对象"),//下单返回码--无效的入参
    _011204005(BU.HOTEL.NO + APP.H_SYNC_COMMON_ORDER.NO + BS.QUERY_ORDERSTATUS.NO + ErrorCodeEnum.INVALID_INPUTPARAM.errorNo, ErrorCodeEnum.INVALID_INPUTPARAM.errorCode, "查询订单服务必要参数为空"),//查询订单返回码--必要参数缺失
    _011201005(BU.HOTEL.NO + APP.H_SYNC_COMMON_ORDER.NO + BS.PRE_BOOKING.NO + ErrorCodeEnum.INVALID_INPUTPARAM.errorNo, ErrorCodeEnum.INVALID_INPUTPARAM.errorCode, "试预定必要参数为空"),//试预定--必要参数缺失
    _011203005(BU.HOTEL.NO + APP.H_SYNC_COMMON_ORDER.NO + BS.CANCLE_ORDER.NO + ErrorCodeEnum.INVALID_INPUTPARAM.errorNo, ErrorCodeEnum.INVALID_INPUTPARAM.errorCode, "取消订单服务必要参数为空"),//取消订单--必要参数缺失

    /**
     * 商家与供应商公共匹配定义
     */
    _011201049(BU.HOTEL.NO + APP.H_SYNC_COMMON_ORDER.NO + BS.PRE_BOOKING.NO + ErrorCodeEnum.NOMATCH_SUPPLYANDMECHANT.errorNo, ErrorCodeEnum.NOMATCH_SUPPLYANDMECHANT.errorCode, "试预定检查缺少商家与供应商配置!"),//试预定返回码--缺少商家与供应商配置
    _011202049(BU.HOTEL.NO + APP.H_SYNC_COMMON_ORDER.NO + BS.CREATE_ORDER.NO + ErrorCodeEnum.NOMATCH_SUPPLYANDMECHANT.errorNo, ErrorCodeEnum.NOMATCH_SUPPLYANDMECHANT.errorCode, "新增订单检查缺少商家与供应商配置!"),//下单返回码--缺少商家与供应商配置
    _011204049(BU.HOTEL.NO + APP.H_SYNC_COMMON_ORDER.NO + BS.QUERY_ORDERSTATUS.NO + ErrorCodeEnum.NOMATCH_SUPPLYANDMECHANT.errorNo, ErrorCodeEnum.NOMATCH_SUPPLYANDMECHANT.errorCode, "查询订单检查缺少商家与供应商配置!"),//查询订单返回码--缺少商家与供应商配置
    _011203049(BU.HOTEL.NO + APP.H_SYNC_COMMON_ORDER.NO + BS.CANCLE_ORDER.NO + ErrorCodeEnum.NOMATCH_SUPPLYANDMECHANT.errorNo, ErrorCodeEnum.NOMATCH_SUPPLYANDMECHANT.errorCode, "取消订单检查缺少商家与供应商配置!"),//取消订单返回码--缺少商家与供应商配置
    _011205049(BU.HOTEL.NO + APP.H_SYNC_COMMON_ORDER.NO + BS.INVOICE_ORDER.NO + ErrorCodeEnum.NOMATCH_SUPPLYANDMECHANT.errorNo, ErrorCodeEnum.NOMATCH_SUPPLYANDMECHANT.errorCode, "订单开票检查缺少商家与供应商配置!"),


    /**
     * 映射关系检查
     */
    _011201031(BU.HOTEL.NO + APP.H_SYNC_COMMON_ORDER.NO + BS.PRE_BOOKING.NO + ErrorCodeEnum.PRICPLANMAPPING_ISEMPTY.errorNo, ErrorCodeEnum.PRICPLANMAPPING_ISEMPTY.errorCode, "试预定前置检查价格计划未映射!"),//试预定返回码--价格计划未映射
    _011201029(BU.HOTEL.NO + APP.H_SYNC_COMMON_ORDER.NO + BS.PRE_BOOKING.NO + ErrorCodeEnum.HOTELMAPPING_ISEMPTY.errorNo, ErrorCodeEnum.HOTELMAPPING_ISEMPTY.errorCode, "试预定前置检查酒店未映射!"),//试预定返回码--酒店未映射
    _011201030(BU.HOTEL.NO + APP.H_SYNC_COMMON_ORDER.NO + BS.PRE_BOOKING.NO + ErrorCodeEnum.ROOMTYPEMAPPING_ISEMPTY.errorNo, ErrorCodeEnum.ROOMTYPEMAPPING_ISEMPTY.errorCode, "试预定前置检查房型未映射!"),//试预定返回码--房型未映射
    _011202031(BU.HOTEL.NO + APP.H_SYNC_COMMON_ORDER.NO + BS.CREATE_ORDER.NO + ErrorCodeEnum.PRICPLANMAPPING_ISEMPTY.errorNo, ErrorCodeEnum.PRICPLANMAPPING_ISEMPTY.errorCode, "下单前置检查价格计划未映射!"),//下单返回码--价格计划未映射
    _011202029(BU.HOTEL.NO + APP.H_SYNC_COMMON_ORDER.NO + BS.CREATE_ORDER.NO + ErrorCodeEnum.HOTELMAPPING_ISEMPTY.errorNo, ErrorCodeEnum.HOTELMAPPING_ISEMPTY.errorCode, "下单检查酒店未映射!"),//下单返回码--酒店未映射
    _011202030(BU.HOTEL.NO + APP.H_SYNC_COMMON_ORDER.NO + BS.CREATE_ORDER.NO + ErrorCodeEnum.ROOMTYPEMAPPING_ISEMPTY.errorNo, ErrorCodeEnum.ROOMTYPEMAPPING_ISEMPTY.errorCode, "下单检查房型未映射!"),//下单返回码--房型未映射

    /**
     * 接口调用异常返回码定义
     */
    _011202901(BU.HOTEL.NO + APP.H_SYNC_COMMON_ORDER.NO + BS.CREATE_ORDER.NO + ErrorCodeEnum.OUTER_IF_EXCEPTION.errorNo, ErrorCodeEnum.OUTER_IF_EXCEPTION.errorCode, "调用供应商发单接口系统异常"),//下单--发单异常
    _011201901(BU.HOTEL.NO + APP.H_SYNC_COMMON_ORDER.NO + BS.PRE_BOOKING.NO + ErrorCodeEnum.OUTER_IF_EXCEPTION.errorNo, ErrorCodeEnum.OUTER_IF_EXCEPTION.errorCode, "调用供应商试预定接口出现系统异常"),//试预定--系统异常
    _011204901(BU.HOTEL.NO + APP.H_SYNC_COMMON_ORDER.NO + BS.QUERY_ORDERSTATUS.NO + ErrorCodeEnum.OUTER_IF_EXCEPTION.errorNo, ErrorCodeEnum.OUTER_IF_EXCEPTION.errorCode, "调用供应商查询订单接口出现系统异常"),//查询订单--系统异常
    _011203901(BU.HOTEL.NO + APP.H_SYNC_COMMON_ORDER.NO + BS.CANCLE_ORDER.NO + ErrorCodeEnum.OUTER_IF_EXCEPTION.errorNo, ErrorCodeEnum.OUTER_IF_EXCEPTION.errorCode, "调用供应商取消订单接口出现系统异常"),//取消订单--系统异常
    _011205901(BU.HOTEL.NO + APP.H_SYNC_COMMON_ORDER.NO + BS.INVOICE_ORDER.NO + ErrorCodeEnum.OUTER_IF_EXCEPTION.errorNo, ErrorCodeEnum.OUTER_IF_EXCEPTION.errorCode, "调用供应商开票接口出现系统异常"),//开票接口

    /**
     * 订单信息
     */
    _011204034(BU.HOTEL.NO + APP.H_SYNC_COMMON_ORDER.NO + BS.QUERY_ORDERSTATUS.NO + ErrorCodeEnum.SUPPLYORDER_NOT_EXIST.errorNo, ErrorCodeEnum.SUPPLYORDER_NOT_EXIST.errorCode, "订单查询返回供货单不存在"),//查询订单返回码--供货单信息不存在
    _011203034(BU.HOTEL.NO + APP.H_SYNC_COMMON_ORDER.NO + BS.CANCLE_ORDER.NO + ErrorCodeEnum.SUPPLYORDER_NOT_EXIST.errorNo, ErrorCodeEnum.SUPPLYORDER_NOT_EXIST.errorCode, "取消订单供货单信息不存在"),//取消订单--供货单信息不存在
    _011206217(BU.HOTEL.NO + APP.H_SYNC_COMMON_ORDER.NO + BS.SEND_SUPPLY_ORDER.NO + ErrorCodeEnum.SEND_SUPPLY_ORDER_TO_SUPPLIER_ERROR.errorNo, ErrorCodeEnum.SEND_SUPPLY_ORDER_TO_SUPPLIER_ERROR.errorCode, "上一次为发单异常");//上一次为发单异常


    public String returnNo;    // 返回编号

    public String returnCode; // 返回代码

    public String returnDesc; // 返回描述

    private ReturnNoEnum(String returnNo, String returnCode, String returnDesc) {
        this.returnNo = returnNo;
        this.returnCode = returnCode;
        this.returnDesc = returnDesc;
    }

    /**
     * 业务线
     */
    public enum BU {
        /**
         * 01 酒店业务线
         */
        HOTEL("01", "HOTEL", "酒店业务线"),
        /**
         * 02 旅游业务线
         */
        DMS("02", "DMS", "旅游业务线"),
        /**
         * 03 海外业务线
         */         //示例，可自行定义名称
        OVER_SEA("03", "OVER_SEA", "海外业务线"), //示例，可自行定义名称
        /**
         * 04 综合业务线
         */
        PLATFORM("04", "PLATFORM", "综合业务线");

        public String NO;
        public String name;
        public String desc;

        private BU(String nO, String name, String desc) {
            this.NO = nO;
            this.name = name;
            this.desc = desc;
        }
    }


    public enum APP {
        /***供应端应用01-30***/
        /**
         * 01  USP
         */
        H_USP("01", "USP", "分销API"),
        /**
         * 02  hotel-sync-common-server
         */
        H_SYNC_COMMON("02", "hotel-sync-common-server", "酒店同步公共服务"),
        /**
         * 03  hotel-sync-gone-server
         */
        H_SYNC_SU8("03", "hotel-sync-gone-server", "酒店数据对接同步服务-速8"),
        /**
         * 04  hotel-sync-data-server
         */
        H_SYNC_YL("04", "hotel-sync-yl-server", "酒店数据对接同步服务-盈利"),
        /**
         * 05  hotel-sync-gone-server
         */
        H_SYNC_JJDC("05", "hotel-sync-gone-server", "酒店数据对接同步服务-锦江"),
        /**
         * 06  hotel-sync-data-server
         */
        H_SYNC_SJL("06", "hotel-sync-sjl-server", "酒店数据对接同步服务-深捷"),
        /**
         * 07  hotel-sync-dds-server
         */
        H_SYNC_SUPPLIER_COMMON("07", "hotel-sync-server", "酒店数据对接同步服务-供应商共用"),
        /**
         * 08  hotel-sync-gone-server-君庭
         */
        H_SYNC_JUNT("08", "hotel-sync-gone-server", "酒店数据对接同步服务-君庭"),
        /**
         * 09  hotel-sync-gone-server-OYO
         */
        H_SYNC_OYO("09", "hotel-sync-gone-server", "酒店数据对接同步服务-OYO"),
        /**
         * 10  HSA-大都市
         */
        H_HSA_DDS("10", "HSA", "HSA-大都市"),
        /**
         * 11  hotel-sync-hz-server
         */
        H_SYNC_HZ("11", "hotel-sync-hz-server", "华住对接服务"),
        /**
         * 12   hotel-sync-server 增加订单类接口应用公共返回码
         */
        H_SYNC_COMMON_ORDER("12", "hotel-sync-server", "酒店订单公共服务"),
        /**
         * 13  hotel-sync-wyn-server 维也纳
         */
        H_SYNC_WYN("13", "hotel-sync-wyn-server", "酒店数据对接同步服务-维也纳"),
        /**
         * 14  HDP
         */
        H_HDP("14", "HDP", "分销API"),
        /**
         * 15  hotel-sync-data-server 开元集团
         */
        H_SYNC_KY("15", "hotel-sync-data-server", "酒店数据对接同步服务-开元"),
        /**
         * 16  hotel-sync-dch-server 东呈集团
         */
        H_SYNC_DCH("16", "hotel-sync-dch-server", "酒店数据对接同步服务-东呈"),
        /**
         * 17  hhotel-sync-gone-server 铂涛集团
         */
        H_SYNC_BT("17", "hotel-sync-gone-server", "酒店数据对接同步服务-铂涛"),
        /**
         * 19  hotel-sync-gone-server 云掌柜PMS
         */
        H_SYNC_YZHG("19", "hotel-sync-gone-server", "酒店数据对接同步服务-云掌柜"),
        /**
         * 19  hotel-sync-data-server 格林豪泰
         */
        H_SYNC_GLHT("20", "hotel-sync-data-server", "酒店数据对接同步服务-格林豪泰"),
        /**
         * 21  hotel-sync-data-server 雅阁集团
         */
        H_SYNC_YG("21", "hotel-sync-data-server", "酒店数据对接同步服务-雅阁集团"),
        /**
         * HSA海外直连供应商通用
         */
        H_HSA("18", "HSA", "海外直连"),
        /**
         * 22  hotel-sync-bld-server 新美大
         */
        H_SYNC_XMD("22", "hotel-sync-bld-server", "新美大"),
        H_SYNC_XW("23", "hotel-sync-bld-server", "广州喜玩(发现假期)"),
        H_SYNC_PY("24", "hotel-sync-bld-server", "广州票亿"),
        H_SYNC_ZBZ("25", "hotel-sync-data-server", "指北针"),
        H_SYNC_RJ("26", "hotel-sync-rj-server", "酒店数据对接同步服务-如家集团"),
        H_SYNC_HH("27", "hotel-sync-data-server", "酒店数据对接同步服务-海航"),
        H_SYNC_ZY("28", "hotel-sync-zy-server", "酒店数据对接同步服务-住友"),
        H_SYNC_SKY("29", "hotel-sync-sky-server", "酒店数据对接同步服务-尚客优"),
        H_SYNC_YD("30", "hotel-sync-yd-server", "酒店数据对接同步服务-亚朵"),
        H_SYNC_WH("34", "hotel-sync-wh-server", "酒店数据对接同步服务-WEHOTEL"),
        H_SYNC_CL("35", "hotel-sync-cl-server", "酒店数据对接同步服务-长隆"),

        /***hotelSAAS应用 31-60***/
        H_HOMS("31", "HOMS", "订单管理系统"),
        H_ORDER_SVR("32", "hotel-order-server", "订单服务"),
        H_GDP("33", "GDP", "GDP服务"),

        /*****mice应用******/
        M_PRODUCT_WEB("41", "mice-order-web", "MICE订单前端接口"),
        M_PRODUCT_SVR("42", "mice-order-server", "MICE库存服务"),
        M_ORDER_WEB("43", "mice-product-web", "MICE库存前端接口"),
        M_ORDER_SVR("44", "mice-product-server", "MICE订单服务"),
        M_CHANNEL_WEB("45", "mice-channel-web", "MICE的B2B网站前端接口"),
        M_MOBILE("46", "mice-mobile", "MICE移动化前端接口"),


        /***分销端应用61-90**/
        /**
         * "61", "HPMS", "携程服务"
         */
        H_HPMS("61", "HPMS", "携程服务"),
        /**
         * "62", "NHPMS", "携程服务"
         */
        H_NHPMS("62", "NHPMS", "携程服务"),
        /**
         * "63", "Hub", "库存API"
         */
        H_HUB("63", "Hub", "库存API"),
        /**
         * "64", "hotel-delivery-ctrip-server", "携程服务"
         */
        H_CTRIP_SVR("64", "hotel-delivery-ctrip-server", "携程服务"),
        /**
         * "65", "hotel-delivery-server", "去哪儿服务"
         */
        H_QUNAR_SVR("65", "hotel-delivery-server", "去哪儿服务"),
        /**
         *
         */
        H_QUNAR_SVR_QZ("66", "hotel-delivery-server-quartz", "去哪儿同步服务"),
        H_TAOBAO_SVR("67", "hotel-delivery-taobao-server", "淘宝服务"),
        /**
         *
         */
        H_ELONG_SVR("68", "hotel-delivery-elong-server", "艺龙服务"),
        /**
         *
         */
        H_INCR_SVR("69", "hotel-increment-server", "增量服务"),
        /**
         *
         */
        H_REDIS_SVR("70", "hotel-redis-server", "Redis服务"),
        /**
         *
         */
        H_XMD_SVR("71", "hotel-delivery-xmd-server", "新美大服务"),
        /**
         *
         */
        H_TN_SVR("71", "hotel-delivery-tuniu-server", "途牛服务"),
        /**
         *
         */
        H_JD_SVR("72", "hotel-delivery-jd-server", "京东服务"),
        /**
         *
         */
        H_HB_SVR("73", "hotel-delivery-hbgj-server", "航班管家服务"),

        /*** 综合应用 91~120 ******/
        P_FINANCE_SRV("91", "fangcang-finance-web", "财务服务");

        public String NO;
        public String name;
        public String desc;

        private APP(String nO, String name, String desc) {
            NO = nO;
            this.name = name;
            this.desc = desc;
        }
    }


    /**
     * 业务场景
     */
    public enum BS {
        /**
         * 01 PRE_BOOKING  试预定
         **/
        PRE_BOOKING("01", "PRE_BOOKING", "试预定"),
        /**
         * 02 CREATE_ORDER  下单
         **/
        CREATE_ORDER("02", "CREATE_ORDER", "下单"),
        /**
         * 03 CANCLE_ORDER  取消订单
         **/
        CANCLE_ORDER("03", "CANCLE_ORDER", "取消订单"),
        /**
         * 04 QUERY_ORDERSTATUS  查询订单状态
         **/
        QUERY_ORDERSTATUS("04", "QUERY_ORDERSTATUS", "查询订单状态"),
        /**
         * 05 WRITE_ORDERSTATUS 回写订单状态
         **/
        WRITE_ORDERSTATUS("05", "WRITE_ORDERSTATUS", "回写订单状态"),
        /**
         * 06 发单
         ******/
        SEND_SUPPLY_ORDER("06", "SEND_SUPPLY_ORDER", "发单到直连"),
        /**
         * 07 财务挂账
         ******/
        FINANCE_TRADE("07", "FINANCE_TRADE", "财务支付交易"),
        /**
         * 08 财务对账
         ******/
        FINANCE_BILL("08", "FINANCE_BILL", "财务账单"),
        /**
         * 09 财务工单
         ******/
        FINANCE_WORK_ORDER("09", "FINANCE_WORK_ORDER", "财务工单"),
        /**
         * 10 财务账户
         ******/
        FINANCE_ACCOUNT("10", "FINANCE_ACCOUNT", "财务账户"),
        /**
         * 11 财务发票
         ******/
        FINANCE_INVOICE("11", "FINANCE_INVOICE", "财务发票"),
        /**
         * 12 财务加锁
         ******/
        FINANCE_LOCK("12", "FINANCE_LOCK", "财务加锁"),
        /**
         * 13酒店查询
         **/
        HOTEL_SEARCH("13", "HOTEL_SEARCH", "酒店查询"),
        /**
         * 14MICE 订单查询
         **/
        MICE_ORDER_SEARCH("14", "MICE_ORDER_SEARCH", "订单查询"),
        /**
         * 15 MICE b2b_web
         **/
        MICE_B2B("15", "MICE_B2B", "mice_b2b_web"),
        /**
         * 16文件查询
         **/
        FILE_SEARCH("16", "FILE_SEARCH", "文件查询"),
        /**
         * 17文件查询
         **/
        FILE_OPERAT("17", "FILE_OPERAT", "文件操作"),
        /**
         * 18 购物车操作
         **/
        SHOP_CART_OPERAT("18", "SHOP_CART_OPERAT", "购物车操作"),
        /**
         * 19消息
         **/
        MSG_OPERAT("19", "MSG_OPERAT", "消息操作"),
        /**
         * 03 CANCLE_ORDER  支付订单
         **/
        PAY_ORDER("20", "PAY_ORDER", "支付订单"),


        /**
         * 03 CANCLE_ORDER  取消订单
         **/
        INVOICE_ORDER("21", "INVOICE_ORDER", "订单开票"),

        /**
         * 30~50
         **/
        GDP_HOTEL("30", "GDP_HOTEL", "GDP酒店查询");

        public String NO;
        public String name;
        public String desc;

        private BS(String nO, String name, String desc) {
            NO = nO;
            this.name = name;
            this.desc = desc;
        }
    }

}
