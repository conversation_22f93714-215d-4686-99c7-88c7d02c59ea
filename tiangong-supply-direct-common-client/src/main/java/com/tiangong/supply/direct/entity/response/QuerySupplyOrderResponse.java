package com.tiangong.supply.direct.entity.response;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
public class QuerySupplyOrderResponse {

    /**
     * MGS供货单号
     */
    private String supplyOrderCode;

    /**
     * 供应商订单编码
     */
    private String supplierOrderCode;

    /**
     * 供货结果
     * @see com.tiangong.enums.OrderStatusEnum
     */
    private Integer supplyResult;

    /**
     * 备注
     */
    private String reason;

    /**
     * 确认号
     **/
    private String confirmationNumber;

    /**
     * 退改费
     */
    private BigDecimal refundFee;

    /**
     * 退改费币种
     */
    private Integer refundFeeCurrency;

    /**
     * 拒绝原因
     */
    private String refusedReason;

    /**
     * 供应商返佣
     */
    private BigDecimal supplyShouldRackBack;

    /**
     * 供应奖励
     */
    private BigDecimal supplyReward;

}
