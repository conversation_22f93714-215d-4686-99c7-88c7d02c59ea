package com.tiangong.supply.direct.interfaces;

import com.tiangong.supply.direct.entity.dto.CreateOrderDto;
import com.tiangong.supply.direct.entity.dto.PreBookingDto;
import com.tiangong.supply.direct.entity.request.CancelOrderRequest;
import com.tiangong.supply.direct.entity.request.QuerySupplyOrderRequest;
import com.tiangong.supply.direct.entity.response.CancelOrderResponse;
import com.tiangong.supply.direct.entity.response.CreateSupplyOrderResponse;
import com.tiangong.supply.direct.entity.response.PreBookingResponse;
import com.tiangong.supply.direct.entity.response.QuerySupplyOrderResponse;

/**
 * 订单模块接口
 */

public interface SupplyDirectOrderService {

    /**
     * 试预订接口
     */
    PreBookingResponse proBooking(PreBookingDto request);

    /**
     * 创建订单
     */
    CreateSupplyOrderResponse createSupplyOrder(CreateOrderDto request);

    /**
     * 查询订单
     */
    QuerySupplyOrderResponse querySupplyOrder(QuerySupplyOrderRequest request);

    /**
     * 取消订单
     */
    CancelOrderResponse cancelSupplyOrder(CancelOrderRequest request);



}
