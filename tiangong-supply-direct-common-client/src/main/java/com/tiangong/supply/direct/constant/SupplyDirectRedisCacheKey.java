package com.tiangong.supply.direct.constant;

public interface SupplyDirectRedisCacheKey {

    /**
     * 直连供应商缓存前缀 SUPPLY:
     */
    String supply_prefix = "SUPPLY:";

    /**
     * 酒店映射 hotelid>hotel_mapping
     */
    String HOTEL_MAPPING = supply_prefix + "HOTEL_MAPPING_";

    /**
     * 房型映射 缓存前缀
     */
    String ROOM_MAPPING = supply_prefix + "ROOM_MAPPING_";

    /**
     * 供应商账号 缓存前缀
     */
    String SUPPLY_CONFIG = supply_prefix + "SUPPLY_CONFIG_";

    /**
     * 试预定缓存映射 钟点房没有checkout拼接
     */
    String PREBOOKING_CACHE = supply_prefix + "PREBOOKING_CACHE:";


}
