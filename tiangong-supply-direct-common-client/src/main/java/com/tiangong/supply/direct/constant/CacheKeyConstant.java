package com.tiangong.supply.direct.constant;

/**
 * <AUTHOR>
 * @Date 2019-12-21 10:07
 * @Description: redis缓存定义
 */
public class CacheKeyConstant {

    public static final String prefix = "SUPPLY:";

    /**
     * 需要同步的酒店ID列表 缓存前缀
     */
    public static final String SUPPLY_HOTEL_ID_SYNC_SET = prefix + "SUPPLY_HOTEL_ID_SYNC_";

    /**
     * 价格计划映射 缓存前缀
     */
    public static final String PRICE_PLAN_MAPPING_CACHE_KEY = prefix + "PRICE_PLAN_MAPPING_TMC_";

    /**
     * 酒店映射 缓存前缀
     */
    public static final String HOTEL_MAPPING = prefix + "HOTEL_MAPPING_";

    /**
     * 酒店Id 缓存前缀
     */
    public static final String SUPPLY_HOTEL = prefix + "HOTEL_";

    /**
     * 有价格酒店Id 缓存前缀
     */
    public static final String SUPPLY_HAS_PRICE_HOTEL = prefix + "HAS_PRICE_HOTEL_";

    /**
     * 酒店映射 缓存前缀(已本系统酒店Id作为key，实时查询时用)
     */
    public static final String HOTEL_MAPPING_TO_SUPPLY = prefix + "HOTEL_MAPPING_TO_SUPPLY_";

    /**
     * 房型映射 缓存前缀
     */
    public static final String ROOM_MAPPING = prefix + "ROOM_MAPPING_";

    /**
     * 订单状态
     */
    public static final String ORDER_STATUS = prefix + "ORDER_STATUS_CTRIP";

    /**
     * 携程同步天数
     */
    public static final String CTRIP_SYNC_DAYS = prefix + "CTRIP_SYNC_DAYS";

    /**
     * 携程同步增量天数
     */
    public static final String CTRIP_SYNC_INCREMENT_DAYS = prefix + "CTRIP_SYNC_INCREMENT_DAYS";

    /**
     * 携程是否过滤产品
     */
    public static final String CTRIP_PRODUCT_FILTER = prefix + "CTRIP_PRODUCT_FILTER";

    /**
     * 携程促销是否过滤产品
     */
    public static final String CTRIP_PROMOTION_PRODUCT_FILTER = prefix + "CTRIP_PROMOTION_PRODUCT_FILTER";

    /**
     * 携程按产品过滤 1:按产品 2：按酒店  默认按酒店
     */
    public static final String CTRIP_INCREMENT_FILTER_FLAG = prefix + "CTRIP_INCREMENT_FILTER_FLAG";

    /**
     * 携程促销按产品过滤 1:按产品 2：按酒店  默认按酒店
     */
    public static final String CTRIP_PROMOTION_INCREMENT_FILTER_FLAG = prefix + "CTRIP_PROMOTION_INCREMENT_FILTER_FLAG";

    /**
     * 携程同步日志
     */
    public static final String CTRIP_SYNC_LOG_FLAG = prefix + "CTRIP_SYNC_LOG_FLAG";

    /**
     * 促销增量时间列表
     */
    public static final String INCREMENT_TIME_LIST_PROMOTION  = prefix + "INCREMENT_TIME_LIST_PROMOTION";

    /**
     * 增量时间列表
     */
    public static final String INCREMENT_TIME_LIST  = prefix + "INCREMENT_TIME_LIST";

    /**
     * ctrip增量消费中
     */
    public static final String INCREMENT_COMSUMING_TH1  = prefix + "INCREMENT_COMSUMING_TH1";

    /**
     * ctrip-cache增量消费中
     */
    public static final String INCREMENT_COMSUMING_TH2  = prefix + "INCREMENT_COMSUMING_TH2";

    /**
     * ctrip-cache1增量消费中
     */
    public static final String INCREMENT_COMSUMING_TH3  = prefix + "INCREMENT_COMSUMING_TH3";


    /**
     * 增量池
     */
    public static final String INCREMENT_HOTEL_ROOM_DATE  = prefix + "INCREMENT_HOTEL_ROOM_DATE";

    /**
     * 增量池1
     */
    public static final String INCREMENT_HOTEL_ROOM_DATE_TH1  = prefix + "INCREMENT_HOTEL_ROOM_DATE_TH1";

    /**
     * 增量池2
     */
    public static final String INCREMENT_HOTEL_ROOM_DATE_TH2  = prefix + "INCREMENT_HOTEL_ROOM_DATE_TH2";

    /**
     * 增量池3
     */
    public static final String INCREMENT_HOTEL_ROOM_DATE_TH3  = prefix + "INCREMENT_HOTEL_ROOM_DATE_TH3";


    /**
     * 增量池(促销)
     */
    public static final String INCREMENT_HOTEL_ROOM_DATE_PROMOTION  = prefix + "INCREMENT_HOTEL_ROOM_DATE_PROMOTION";

    /**
     * 增量池1(促销)
     */
    public static final String INCREMENT_HOTEL_ROOM_DATE_TH1_PROMOTION  = prefix + "INCREMENT_HOTEL_ROOM_DATE_TH1_PROMOTION";

    /**
     * 增量池2(促销)
     */
    public static final String INCREMENT_HOTEL_ROOM_DATE_TH2_PROMOTION  = prefix + "INCREMENT_HOTEL_ROOM_DATE_TH2_PROMOTION";

    /**
     * 增量池3(促销)
     */
    public static final String INCREMENT_HOTEL_ROOM_DATE_TH3_PROMOTION  = prefix + "INCREMENT_HOTEL_ROOM_DATE_TH3_PROMOTION";

    /**
     * ctrip增量消费中(促销)
     */
    public static final String INCREMENT_COMSUMING_TH1_PROMOTION  = prefix + "INCREMENT_COMSUMING_TH1_PROMOTION";

    /**
     * ctrip-cache增量消费中(促销)
     */
    public static final String INCREMENT_COMSUMING_TH2_PROMOTION  = prefix + "INCREMENT_COMSUMING_TH2_PROMOTION";

    /**
     * ctrip-cache1增量消费中(促销)
     */
    public static final String INCREMENT_COMSUMING_TH3_PROMOTION  = prefix + "INCREMENT_COMSUMING_TH3_PROMOTION";


    /**
     * 需要同步的酒店ID
     */
    public static final String NEED_SYNC_HOTEL_ID = prefix + "NEED_SYNC_HOTEL_ID";

    /**
     * 酒店产品
     */
    public static final String HOTEL_PRODUCTS = prefix + "HOTEL_PRODUCTS_";


    /**
     * 需要删除的产品ID
     */
    public static final String NEED_CLEAN_PRODUCT  =  "NEED_CLEAN_PRODUCT";

    /**
     * 超时记录
     */
    public static final String REQUEST_PRODUCT_TIME_OUT  = prefix + "REQUEST_PRODUCT_TIME_OUT";


    /**
     * 酒店下面的价格计划映射
     */
    public static final String HOTEL_PRICEPLAN_MAPPING = prefix + "HOTEL_PRICEPLAN_MAPPING";

    /**
     * 实时查询酒店价格缓存
     */
    public static final String HOTEL_PRODUCT_PRICE_CACHE = prefix + "HOTELPRICE:";

    /**
     * 实时查询酒店价格缓存
     */
    public static final String HOTEL_PRODUCT_PRICE_CACHE_FORORDER = prefix + "HOTELPRICEFORORDER:";

    /**
     * 价格计划 增量 缓存 定时任务消费
     */
    public static final String PLAN_INCREMENT_MAPPING = prefix + "PLAN_INCREMENT_MAPPING_";

    /**
     * 增量最大时间
     */
    public static final String INCREMENT_LASTEST_TIME = prefix + "INCREMENT_LASTEST_TIME_";

    /**
     * 增量数据
     */
    public static final String INCREMENT_DATA = prefix + "INCREMENT_DATA_";

    /**
     * 热门酒店临时Key
     */
    public static final String HOT_HOTEL_TEMP_KEY = prefix + "HOT_HOTEL_TEMP_";

    /**
     * 热门酒店Key
     */
    public static final String HOT_HOTEL_KEY = prefix + "HOT_HOTEL_";

    /**
     * 过滤产品名称
     */
    public static final String FILTER_RATEPLAN_NAME_LIST = prefix + "FILTER_RATEPLAN_NAME_LIST_";

}
