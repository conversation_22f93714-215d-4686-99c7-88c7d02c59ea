package com.tiangong.supply.direct.entity.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;


@Data
@ToString(callSuper = true)
//@JsonInclude(JsonInclude.Include.NON_DEFAULT)
public class CreateOrderInvoiceRequest {

    /**
     * MGS供货单号
     */
    @NotNull(message = "EMPTY_PARAM_ORDERCODE")
    private String supplyOrderCode;

    /**
     * 供应商订单编码
     */
    @NotNull(message = "EMPTY_PARAM_SUPPLYORDERCODE")
    private String supplierOrderCode;


    /**
     * 开票金额
     */
    @NotNull(message = "EMPTY_PARAM_AMOUNT")
    private BigDecimal invoiceAmount;

    /**
     * 发票类型(0 普通发票, 1 专用发票)
     */
    @NotNull(message = "EMPTY_PARAM_INVOICETYPE")
    private Integer invoiceType;

    /**
     * 发票名词
     */
    private String invoiceName;

    /**
     * 发票抬头
     */
    @NotNull(message = "EMPTY_PARAM_INVOICEHEADER")
    private String invoiceTitle;

    /**
     * 税号
     */
    private String taxNumber;

    /**
     * 开户行
     */
    private String accountBank;
    /**
     * 公司电话
     */
    private String companyPhone;
    /**
     * 注册地址
     */
    private String registerAddr;
    /**
     * 银行账号
     */
    private String accountNo;
    /**
     * 发票备注
     */
    private String invoiceRemark;
    /**
     * 申请日期
     */
    private Date applyDate;
    /**
     * 取票方式 1自取纸制发票，2-邮寄纸制发票，3-电子发票；
     */
    @NotNull(message = "EMPTY_PARAM_TICKETTYPE")
    private Integer ticketType;
    /**
     * 发送方式(0 未发送, 1 已发送)
     */
    private Integer sendStatus;
    /**
     * 收件人
     */
    private String receivedName;
    /**
     * 收件人电话
     */
    private String receivedPhone;
    /**
     * 收件人地址
     */
    private String receivedAddr;
    /**
     * 快递单号
     */
    private String trackingNo;
    /**
     * 邮箱地址
     */
    private String email;

    /**
     * 抬头类型 (0 企业单位, 1 个人/非企业单位)
     */
    private Integer invoiceTitleType;

    /**
     * 供应商编码
     */
    @NotNull(message = "EMPTY_PARAM_SUPPLYCODE")
    private String supplyCode;
}