package com.tiangong.supply.direct.entity.response;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class CancelOrderResponse {

    private static final long serialVersionUID = 1L;
    /**
     * MGS供货单号
     */
    private String supplyOrderCode;

    /**
     * 取消失败原因
     */
    private String reason;

    /**
     * 订单确认状态
     * @see com.tiangong.enums.OrderStatusEnum
     */
    private Integer orderConfirmationStatus;

    /**
     * 供应商订单号
     */
    private String supplierOrderId;

    /**
     * 退改费
     */
    private BigDecimal refundFee;

    /**
     * 退改费币种
     */
    private Integer refundFeeCurrency;
}
