package com.tiangong.supply.direct.remote;


import com.tiangong.annotations.SlsLog;
import com.tiangong.common.Response;
import com.tiangong.order.remote.dto.InBlankCheckInfoDTO;
import com.tiangong.order.remote.dto.InvoiceNotifyRequest;
import com.tiangong.order.remote.dto.OrderCheckDetailInfoDTO;
import com.tiangong.order.remote.response.SupplyOrderDTO;
import com.tiangong.supply.direct.entity.response.QuerySupplyOrderResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(name = "tiangong-supply-direct-common-server", contextId = "SupplyOrderRelatedNotifyRemote")
public interface SupplyOrderDetailRemote {

    /**
     * 订单状态通知
     */
    @PostMapping("/supply/direct/order/status/notify")
    Response<Object> orderStatusNotify(@RequestBody QuerySupplyOrderResponse request);

    /**
     * 开票推送接口
     */
    @PostMapping("/supply/direct/invoice/status/notify")
    Response<Object> invoiceStatusNotify(@RequestBody InvoiceNotifyRequest request);

    /**
     * 订单入住明细
     */
    @PostMapping("/supply/direct/order/check/detail")
    Response<Object> orderCheckDetail(@RequestBody OrderCheckDetailInfoDTO orderCheckDetailInfoDTO);

    /**
     * 获取需要没有填写入住明细的供货单
     */
    @PostMapping("/supply/direct/order/queryInBlankCheckInfoSupplyOrderCodeList")
    Response<List<SupplyOrderDTO>> queryInBlankCheckInfoSupplyOrderCodeList(@RequestBody InBlankCheckInfoDTO inBlankCheckInfoDTO);

}
