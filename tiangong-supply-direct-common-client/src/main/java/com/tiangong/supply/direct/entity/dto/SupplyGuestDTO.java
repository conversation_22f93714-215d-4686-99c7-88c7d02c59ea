package com.tiangong.supply.direct.entity.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2020-1-9 17:08
 * @Description:
 */
@Data
public class SupplyGuestDTO {

    /**
     * 供货项入住人id
     */
    private Long id;

    /**
     * 供货单ID
     */
    private Long supplyOrderId;

    /**
     * 关联入住人ID
     */
    private Long guestId;

    /**
     * 宾客姓名
     */
    private String guestName;

    /**
     * 国籍
     */
    private Integer nationality;

    /**
     * 性别
     */
    private Integer sex;

    /**
     * 手机
     */
    private String phone;

    private String countryCode;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 证件类别
     */
    private Integer idType;

    /**
     * 证件号码
     */
    private String idnumber;

    /**
     * 客人年龄
     */
    private Integer age;

    /**
     * 姓
     */
    private String firstName;

    /**
     * 名
     */
    private String lastName;


    /**
     * 华住需要入住人是否获得积分 0否 1是
     */
    private Integer earnPoints;

    /**
     * 房间序号
     */
    private Integer roomIndex;
}
