package com.tiangong.supply.direct.entity.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.tiangong.dto.product.HourlyRoomInfo;
import com.tiangong.dto.product.response.TipInfosDTO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class ProductDetail {

    /**
     * 价格计划id
     * 为sp_hotel_id+_+sp_room_id+_+sp_ratePlanId/productId+_+supplyCode+_+protocolFlag(1/0)#SpPricePlanName
     * 示例：561794_1702770_85232030_S10267587_1#单早
     */
    private String ratePlanId;

    /**
     * 价格计划名称
     */
    private String ratePlanName;

//    /**
//     * 床型编码
//     */
//    private String bedType;

    /**
     * 床型信息
     */
    private List<BedTypesDetailDto> bedTypeDetails;

    /**
     * 窗型编码 枚举值见【窗型表】
     */
    private Integer windowType;

    /**
     * 最大入住人
     */
    private Integer maxGuestPerson;

    /**
     * 最小入住人
     */
    private Integer minGuestPerson;

    /**
     * 是否立即确认 是否立即确认
     * 0 否
     * 1 是
     * 空 不确定
     */
    private Integer immediateConfirm;

    /**
     * 是否钟点房	Integer	1是0否 空代表否
     */
    private int hourlyRoom;

    /**
     * 钟点房对象
     */
    private HourlyRoomInfo hourlyRoomInfo;

    /**
     * 均价
     */
    private BigDecimal avgPrice;

    /**
     * 是否可加床
     * 0 否
     * 1 是
     * 空 不确定
     */
    private Integer addBed;

    /**
     * 加床费用
     */
    private BigDecimal addBedPrice;

    /**
     * 加床费用描述
     */
    private String addBedPriceDesc;


    /**
     * 订单是否需要证件信息
     * 0 不需要
     * 1 需要
     * 空 默认为不需要
     */
    private Integer certificateFlag;


    /**
     * 宽带 1：收费，2：免费，3:无，4：未设置
     */
    private Integer broadNet = 3;

    /***/
    private String supplyCode;

    /**
     * 预订状态
     */
    private Integer bookType;

    /**
     * 发票模式
     */
    private Integer invoiceModel;

    /**
     * 发票类型
     */
    private Integer invoiceType;

    /**
     * 提示信息
     */
//    private String reminder;

//    /**
//     * 售卖信息列表
//     */
//    private List<PriceItem> priceItems;

    /**
     * 每间售卖信息列表
     */
    private List<RoomItemDetail> roomItemDetails;

    /**
     * 是否到店付：1-是，0或其它-否， 默认0
     */
    private Integer payAtHotelFlag = 0;

    /**
     * 产品标签
     * 0或空-无标签
     * 1-协议价格标签
     * 2-平台自签标签
     * 3-团房标签
     */
    private Integer productLabel = 0;

    /**
     * 产品小类标签
     */
    private Integer smallProductLabel = 0;

    /**
     *  产品标签类型（1：代结算  0：非代结算 未配置则为null）
     */
    private Integer labelType;

    /**
     * 供应商编码
     */
    @JsonIgnore
    private String supplyClass;

    /**
     * 礼包信息
     */
//    private List<String> giftPacks;

    /**
     * 提示信息
     */
    private List<TipInfosDTO> tips;
}
