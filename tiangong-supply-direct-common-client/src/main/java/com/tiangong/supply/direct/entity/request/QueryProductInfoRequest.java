package com.tiangong.supply.direct.entity.request;

import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * 查询产品参数 通用对象
 */
@Data
public class QueryProductInfoRequest {

    /**
     * 供应商编码集合
     */
    private Set<String> supplyCodes;

    /**
     * 供应商类型
     */
    private String supplyClass;

    /**
     * 入住时间
     */
    private String checkInDate;

    /**
     * 离店时间
     */
    private String checkOutDate;

    /**
     * 供应商酒店id 多酒店根本不存在
     */
    private String spHotelId;

    /**
     * 房型id
     */
    private Integer spRoomId;

    /**
     * 间数
     */
    private Integer roomQty;

    /**
     * 入住总人数
     */
    private Integer guestQuantity;

    /**
     * 只查钟点房	Integer	否	1是0否 默认0 全查
     */
    private int onlyHourRoom;

    /**
     * 供应商类型 1:国内 2:海外
     */
    private String supplyType;

    /**
     * 供应商产品id
     */
    private String spProductId;

    /**
     * 房间人数信息
     */
    private List<RoomGuestNumber> roomGuestNumbers;

    /**
     * 登陆账号
     */
    private String userAccount;

    /**
     * 排查问题
     */
    private String requestId;

    /**
     * 币种
     */
    private Integer currency;

    /**
     * 语言
     */
    private String language;

    /**
     * 国籍
     */
    private String nationality;

    /**
     * 发起者
     */
    private String sponsor;
}
