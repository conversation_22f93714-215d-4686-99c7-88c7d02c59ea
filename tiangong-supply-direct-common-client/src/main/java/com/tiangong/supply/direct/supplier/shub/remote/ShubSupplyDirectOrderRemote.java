package com.tiangong.supply.direct.supplier.shub.remote;

import com.tiangong.supply.direct.annotations.SuppyDirectClass;
import com.tiangong.supply.direct.entity.dto.CreateOrderDto;
import com.tiangong.supply.direct.entity.dto.PreBookingDto;
import com.tiangong.supply.direct.entity.request.CancelOrderRequest;
import com.tiangong.supply.direct.entity.request.CreateOrderInvoiceRequest;
import com.tiangong.supply.direct.entity.request.QuerySupplyOrderRequest;
import com.tiangong.supply.direct.entity.response.*;
import com.tiangong.supply.direct.interfaces.BaseSupplyDirectOrderRemote;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

@SuppyDirectClass(value = "SHUB")
@FeignClient(name = "tiangong-supply-direct-shub-server")
public interface ShubSupplyDirectOrderRemote extends BaseSupplyDirectOrderRemote {

    /**
     * 试预订接口
     */
    @RequestMapping(value = "/shub/pre/booking", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    PreBookingResponse preBooking(@RequestBody PreBookingDto preBookingDto);

    /**
     * 创建订单
     */
    @RequestMapping(value = "/shub/create/order", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    CreateSupplyOrderResponse createSupplyOrder(@RequestBody CreateOrderDto createOrderDto);

    /**
     * 查询订单
     */
    @RequestMapping(value = "/shub/query/order", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    QuerySupplyOrderResponse querySupplyOrder(@RequestBody QuerySupplyOrderRequest request);

    /**
     * 取消订单
     */
    @RequestMapping(value = "/shub/cancel/order", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    CancelOrderResponse cancelSupplyOrder(@RequestBody CancelOrderRequest request);

    /**
     * 开票接口
     */
    @RequestMapping(value = "/shub/createInvoice", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    CreateInvoiceResponse createInvoice(@RequestBody CreateOrderInvoiceRequest request);
}
