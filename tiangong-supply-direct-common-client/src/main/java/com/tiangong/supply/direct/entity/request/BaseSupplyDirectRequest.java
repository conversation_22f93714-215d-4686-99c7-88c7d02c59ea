package com.tiangong.supply.direct.entity.request;

import lombok.Data;

import java.util.Set;

@Data
public class BaseSupplyDirectRequest {

    /**
     * 供应商编码集合
     */
    private Set<String> supplyCodes;


    /**
     * 供应商类型
     */
    private String supplyClass;

    /**
     * 供应商酒店Id列表
     */
    private Set<String> spHotelIds;

    /**
     * 供应商酒店id
     */
    private String spHotelId;

    /**
     * 入住日期
     */
    private String checkInDate;

    /**
     * 离店日期
     */
    private String checkOutDate;

    /**
     * 间数
     */
    private Integer roomQty;

    /**
     * 入住总人数
     */
    private Integer guestQuantity;


    /**
     * 携程联登用户id
     */
    private String userId;


}
