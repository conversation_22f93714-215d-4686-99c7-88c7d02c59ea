package com.tiangong.supply.direct.entity.dto;


import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
public class ProductDetailDTO {

    /**
     * 酒店id
     */
    private Long hotelId;

    /**
     * 房型列表
     */
    private List<RoomItem> roomItems;

    /**
     * 钟点房房型列表
     */
    private List<RoomItem> hourlyRoomItems;

    public ProductDetailDTO (Long hotelId, List<RoomItem> roomItems) {
        this.hotelId = hotelId;
        this.roomItems = roomItems;
    }
}
