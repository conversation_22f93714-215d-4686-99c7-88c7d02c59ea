package com.tiangong.supply.direct.entity.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 供应商 底价通用封装对象 以isHourly区分钟点房
 */
@Data
public class SupplyHotelLowestPriceRequest implements Serializable {

    private Integer id;
    /**
     * 分销商编码
     */
    private String supplyCode;
    /**
     * 城市编码 必填 如果没有封装一定要跳过
     */
    private String cityCode;
    /**
     * 酒店id
     */
    private Long hotelId;
    /**
     * 供应商酒店id
     */
    private String spHotelId;
    /**
     * 售卖日期
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date saleDate;
    /**
     * 最低价格
     */
    private BigDecimal lowestPrice;
    /**
     * 删除状态 无效属性
     */
    private Integer deleted;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private Date createdDt;
    /**
     * 更新人
     */
    private String updatedBy;
    /**
     * 更新时间
     */
    private Date updatedDt;

    /**
     * protocolFlag	协议酒店	Integer	否	传入1则只查询协议酒店，不传或者传其他值则查询全部 只有1和0种状态
     */
    private int protocolFlag;
    /**
     * cancelFlag	可取消	Integer	否	传入1则只查询有可取消产品酒店，不传或者传其他值则查询全部 只有1和0种状态
     */
    private int cancelFlag;
    /**
     * breakfastFlag	含早	Integer	否	传入1则只查询有含早餐产品酒店，不传或者传其他值则查询全部 只有1和0种状态
     */
    private int breakfastFlag;
    /**
     * instantConfirmFlag	即时确认	Integer	否	传入1则只查询有即时确认产品酒店，不传或者传其他值则查询全部 只有1和0种状态
     */
    private int instantConfirmFlag;

    /**
     * 需转换成内部通用房型编码 多个用逗号隔开 补齐分销商低价数据使用
     */
    private String bedType;

    /**
     * 最早到店时间     Integer	如 10:00，该值为 600
     */
    private Integer earliestArriveTime;
    /**
     * 最晚离店时间	Integer	如 18:00，该值为 1080
     */
    private Integer latestLeaveTime;
    /**
     * 连住时间	Integer	如 3小时，该值为3
     */
    private Integer duration;
    /**
     * 是否钟点房 必填 否者默认日历房
     */
    private boolean isHourly;

}
