package com.tiangong.supply.direct.remote;

import com.tiangong.supply.direct.entity.request.*;
import com.tiangong.supply.direct.entity.response.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

@FeignClient(name = "tiangong-supply-direct-common-server", contextId = "SupplyDirectOrderRemote")
public interface SupplyDirectOrderRemote {

    /**
     * 试预订接口
     */
    @RequestMapping(value = "/supply/direct/pre/booking", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    PreBookingResponse preBooking(@RequestBody PreBookingRequest request);

    /**
     * 创建订单
     */
    @RequestMapping(value = "/supply/direct/create/order", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    CreateSupplyOrderResponse createSupplyOrder(@RequestBody CreateSupplyOrderRequest request);

    /**
     * 查询订单
     */
    @RequestMapping(value = "/supply/direct/query/order", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    QuerySupplyOrderResponse querySupplyOrder(@RequestBody QuerySupplyOrderRequest request);

    /**
     * 取消订单
     */
    @RequestMapping(value = "/supply/direct/cancel/order", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    CancelOrderResponse cancelSupplyOrder(@RequestBody CancelOrderRequest request);

    /**
     * 开票接口
     */
    @RequestMapping(value = "/supply/direct/createInvoice", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    CreateInvoiceResponse createInvoice(@RequestBody CreateOrderInvoiceRequest request);


}
