package com.tiangong.supply.direct.entity.response;

import com.tiangong.dto.product.HourlyRoomInfo;
import com.tiangong.dto.product.response.TipInfosDTO;
import com.tiangong.supply.direct.entity.dto.BedTypesDetailDto;
import com.tiangong.supply.direct.entity.dto.CancelRestriction;
import com.tiangong.supply.direct.entity.dto.OrderRoomDetailDto;
import lombok.Data;

import java.util.List;

@Data
public class PreBookingResponse {

    /**
     * MGS酒店ID
     */
    private String hotelId;

    /**
     * MGS房型ID
     */
    private String roomId;

    /**
     * MGS价格计划ID
     */
    private String pricePlanId;

    /**
     * 支付类型
     * @see com.tiangong.enums.PayMethodEnum
     */
    private Integer payMethod;

    /**
     * 供应商编码
     */
    private String supplyCode;

    /**
     * 入住日期
     */
    private String checkInDate;

    /**
     * 离店日期
     */
    private String checkOutDate;

    /**
     * 是否可订
     * 1:可定
     * 0:不可订
     */
    private Integer canBook;

    /**
     * 是否即时确认(1:即时确认 0：非即时确认)
     */
    private Integer canImmediate;

    /**
     * 房间费用明细列表
     */
    private List<OrderRoomDetailDto> orderRoomDetails;

    /**
     * 供应商产品名称
     */
    private String spProductName;

    /**
     * 评价 产品特殊说明
     */
    private String rateComments;


    /**
     * 阶段取消条款
     */
    private List<CancelRestriction> cancelRestrictions;

    /**
     * 发票模式
     * 0 不开发票
     * 1 商家开发票
     * 2. 酒店前台自取
     */
    private Integer invoiceModel;

    /**
     * 发票类型
     * 0 或 null 未确定
     * 1 普票
     * 2 专票
     */
    private Integer invoiceType;

    /**
     * 担保条款类型
     * 为空 未设置担保条款
     * 0 未设置担保条款
     * 1 按到店时间
     * 2 按预订间数
     * 3 预订即保（预订就必须担保）
     */
    private Integer guaranteeType;

    /**
     * guaranteeType
     * 为1时：时间点，如1700，表示17点
     * 为2时：间数，如：2
     * 为3时：无
     */
    private String guaranteeCondition;

    /**
     * 担保费用类型
     * 1 全额
     * 2 首晚
     */
    private Integer guaranteeFeeType;

    /**
     * 担保描述
     */
    private String guaranteeDesc;

    /**
     * 酒店名称
     */
    private String hotelName;

    /**
     * 房型名称
     */
    private String roomName;

    /**
     * 是否钟点房	Integer	1是0否 空代表否
     */
    private int hourlyRoom;

    /**
     * 钟点房对象
     */
    private HourlyRoomInfo hourlyRoomInfo;

    /**
     * 是否需要填写身份证	Integer	1是0否 空代表否
     */
    private int hasIdCard;

    /**
     * 床型详细信息
     */
    private List<BedTypesDetailDto> bedTypeDetails;

    /**
     * 提示信息
     */
    private List<TipInfosDTO> tips;
}
