package com.tiangong.supply.direct.constant;

public enum SupplyDirectActiveEnum {

    INVALID(0, "无效"),
    ACTIVE(1, "有效"),
    DELETE(2, "删除");
    private Integer value;
    private String desc;

    SupplyDirectActiveEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static String getDesc(int val) {
        for (SupplyDirectActiveEnum e : SupplyDirectActiveEnum.values()) {
            if (e.getValue() == val)
                return e.getDesc();
        }
        return null;
    }

    public static SupplyDirectActiveEnum getActive(Integer value) {
        for (SupplyDirectActiveEnum temp : SupplyDirectActiveEnum.values()) {
            if (temp.getValue() == value) {
                return temp;
            }
        }
        return null;
    }

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

}
