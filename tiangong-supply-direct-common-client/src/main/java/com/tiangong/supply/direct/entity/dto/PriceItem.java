package com.tiangong.supply.direct.entity.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class PriceItem {

    /** 售卖日期 */
    private String saleDate;

    /**
     * 每间 价格明细
     */
    private TaxDetailDto taxDetail;

    /** 售卖价格 */
    private BigDecimal salePrice;

    /**
     * 底价
     */
    private BigDecimal basePrice;

    /**
     * 底价币种
     */
    private Integer baseCurrency;

    /** 早餐类型 */
    private Integer breakfastType;

    /** 早餐数量 */
    private Integer breakfastNum = 0;

    /** 房态 */
    private Integer roomStatus;

    /**
     * 是否可超
     */
    private Boolean overDraft;

    /** 剩余配额数 **/
    private Integer quotaNum;

    /** 预订条款类型 */
    private Integer bookRestrictionType = 0;

    /** 提前预定的天数 */
    private Integer bookRestrictionDay;

    /** 提前预定天数 Day 天之中的 time 点 */
    private String bookRestrictionTime;

    /** 入住条款类型 */
    private Integer occupancyRestrictionType = 0;

    /** 需要入住的天数 */
    private Integer occupancyRestrictionDay;

    /** 最少预订房间数 */
    private Integer minRoomCount;

    /** 最多预订房间数 */
    private Integer maxRoomCount;

    /**
     * 阶段取消条款
     */
    private List<CancelRestriction> cancelRestrictions;


    //担保条款类型
    private Integer guaranteeType = 0;

    //担保条件
    private String guaranteeCondition;

    //担保费用类型
    private Integer guaranteeFeeType;
}
