package com.tiangong.supply.direct.entity.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 房间售卖信息
 */
@Data
public class RoomItemDetail {

    /**
     * 房间序号，多间房多个入住人使用
     */
    private Integer roomIndex;

    /**
     * 成人数
     */
    private Integer adultNum;

    /**
     * 儿童年龄 多个儿童年龄用逗号分隔 例如：1,2,3
     */
    private String childAges;

    /**
     * 每间 价格明细
     */
    private TaxDetailDto taxDetail;

    /**
     * 到店另付币种(酒店当地币种)
     */
    private Integer payInStoreCurrency;

    /**
     * 到店另付费用(酒店当地币种金额)
     */
    private BigDecimal payInStorePrice;

    /**
     * 到店另付币种(供应商币种)
     */
    private Integer supplyPayInStoreCurrency;

    /**
     * 到店另付费用(供应商币种金额)
     */
    private BigDecimal supplyPayInStorePrice;

    /**
     * 售卖信息列表
     */
    private List<PriceItem> priceItems;
}