//package com.tiangong.supply.direct.enums;
//
//import java.util.Objects;
//
///**
// * 直连供应商类型定义枚举
// */
//public enum SupplierDirectClass {
//
//    CTRIP("CTRIP"),
//    TCSL("TCSL"),
//    HSJL("HSJL"),
//    CTRIP_UNION("CTRIP_UNION"),
//    IREVE("IREVE"),
//    SHUB("SHUB");
//
//    public String supplierDirectClass;
//
//    public String getSupplierDirectClass() {
//        return supplierDirectClass;
//    }
//
//    public void setSupplierDirectClass(String supplierDirectClass) {
//        this.supplierDirectClass = supplierDirectClass;
//    }
//
//    SupplierDirectClass(String supplierDirectClass) {
//        this.supplierDirectClass = supplierDirectClass;
//    }
//
//    public static SupplierDirectClass getSupplyDirectClass(String supplyDirectClass) {
//        for (SupplierDirectClass supplierClass : SupplierDirectClass.values()) {
//            if (Objects.equals(supplierClass.name(), supplyDirectClass)) {
//                return supplierClass;
//            }
//        }
//        return null;
//    }
//
//}
