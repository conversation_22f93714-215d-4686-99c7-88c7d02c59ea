package com.tiangong.supply.direct.entity.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.tiangong.dto.product.HourlyRoomDetail;
import com.tiangong.supply.direct.entity.dto.SupplyDetailsDTO;
import com.tiangong.supply.direct.entity.dto.SupplyGuestDTO;
import lombok.Data;

import javax.persistence.Column;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
//@JsonInclude(JsonInclude.Include.NON_DEFAULT)
public class CreateSupplyOrderRequest {

    /**
     * 供应商类型
     */
    @NotNull(message = "EMPTY_PARAM_SUPPLYCODE")
    private String supplyCode;


    /**
     * 供货单编码（作为MGS订单号）
     */
    @NotNull(message = "EMPTY_PARAM_ORDERCODE")
    private String supplyOrderCode;

    /**
     * 酒店ID
     */
    @NotNull(message = "EMPTY_PARAM_HOTELID")
    private String hotelId;

    /**
     * 房型ID
     */
    @NotNull(message = "EMPTY_PARAM_ROOMID")
    private String roomId;

    /**
     * 供应商产品id
     */
    @NotNull(message = "EMPTY_PARAM_SPPRODUCTID")
    private String spProductId;


    /**
     * 价格计划名称
     */
    private String pricePlanName;

    /**
     * 床型
     */
    private String bedType;

    /**
     * 到店另付币种
     */
    private Integer payInStoreCurrency;

    /**
     * 到店另付费用
     */
    private BigDecimal payInStorePrice;

    /**
     * 房间数量
     */
    @NotNull(message = "EMPTY_PARAM_ROOMNUM")
    private int roomNum;

    /**
     * 入住日期
     */
    @NotNull(message = "EMPTY_PARAM_CHECKINDATE")
    private Date checkInDate;

    /**
     * 离店日期
     */
    @NotNull(message = "EMPTY_PARAM_CHECKOUTDATE")
    private Date checkOutDate;

    /**
     * 最早到店时间
     */
    private String arrivalEarlyTime;

    /**
     * 最晚到店时间
     */
    private String arrivalLateTime;

    /**
     * 供货单总价
     */
    @NotNull(message = "EMPTY_PARAM_ORDERTOTALAMOUNT")
    private BigDecimal orderSum;

    /**
     * 供货单入住人
     */
    @Valid
    @Size(min = 1,message = "EMPTY_PARAM_SUPPLYGUESTS")
    private List<SupplyGuestDTO> supplyGuests;


    /**
     * 供货明细
     */
    @Valid
    @Size(min = 1,message = "EMPTY_PARAM_SUPPLYDETAILS")
    private List<SupplyDetailsDTO> supplyDetails;

    /**
     * 发单人
     */
    private String supplyRequestUser;

    /**
     * 备注信息
     */
    private String remark;

    /**
     * 0 tiangong 产品， 1 供应商产品
     */
    private Integer isSpProduct;

    /**
     * 供应商酒店id
     */
    private String spHotelId;

    /**
     * 供应商房型id
     */
    private String spRoomId;


    /**
     * 每间房成人数量(入住人数)
     */
    private Integer numberOfAdults;

    /**
     * 每间房儿童数量
     */
    private Integer numberOfChildren;

    /**
     * 儿童年龄,多个,号隔开
     */
    private String childrenAges;

    /**
     * 支付方式（0:预付 1:现付）
     * @see com.tiangong.enums.PayMethodEnum
     */
    private Integer payMethod;

    /**
     * 是否担保
     * 订单是否担保
     * 1 担保
     * 0 未担保
     * 默认为未担保
     */
    private Integer guaranteeFlag;

    /**
     * 是否钟点房	Integer	1是0否 空代表否
     */
    private int hourlyRoom;

    /**
     * 钟点房日期对象
     */
    private HourlyRoomDetail hourlyRoomDetail;

    /**
     * 房间人数信息
     */
    private List<RoomGuestNumber> roomGuestNumbers;

    /**
     * 是否VIP订单 1-VIP订单 0-非VIP订单
     */
    private String isVipOrder;

    /**
     * 出行类型，1-因公，2-因私
     */
    private Integer travelType;

    /**
     * 登陆账号
     */
    private String userAccount;

    /**
     * 排查问题
     */
    private String requestId;

    /**
     * 国籍
     */
    private String nationality;

}
