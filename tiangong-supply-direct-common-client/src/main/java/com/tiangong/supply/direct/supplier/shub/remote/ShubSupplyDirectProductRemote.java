package com.tiangong.supply.direct.supplier.shub.remote;

import com.tiangong.common.Response;
import com.tiangong.supply.direct.annotations.SuppyDirectClass;
import com.tiangong.supply.direct.entity.dto.ProductMiddleDto;
import com.tiangong.supply.direct.entity.request.QueryProductInfoRequest;
import com.tiangong.supply.direct.interfaces.BaseSupplyDirectProductRemote;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

/**
 * 供应商直连产品
 **/
@SuppyDirectClass(value = "SHUB")
@FeignClient(name = "tiangong-supply-direct-shub-server")
public interface ShubSupplyDirectProductRemote extends BaseSupplyDirectProductRemote {

    /**
     * 查询产品基础信息
     */
    @RequestMapping(value = "/shub/query/product", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    Response<List<ProductMiddleDto>> queryProductList(@RequestBody QueryProductInfoRequest baseSupplyDirectRequest);

}
