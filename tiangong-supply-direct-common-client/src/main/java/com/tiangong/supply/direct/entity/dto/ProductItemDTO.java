package com.tiangong.supply.direct.entity.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2019/7/17 9:34
 * 订单所需产品列表
 **/
@Data
public class ProductItemDTO {
    
    /**
     * 产品Id
     */
    private  Integer productId;
    /**
     * 产品名称
     */
    private  String productName;
    /**
     * 房型Id
     */
    private  Integer roomId;
    /**
     * 房型名称
     */
    private  String roomName;
    /**
     * 床型
     */
    private  String bedTypes;
    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 采购类型：0自签协议房，1自签包房，2手工录入
     * @see com.tiangong.enums.PurchaseTypeEnum
     */
    private Integer purchaseType;
    /**
     * 间数
     */
    private  Integer roomQty;
    /**
     * 底价总和，计算逻辑：sum（每日价格）*roomQty
     */
    private BigDecimal totalAmt;
    /**
     * 早餐数
     */
    private Integer breakfastQty;
    /**
     * 币种
     */
    private Integer currency;

    /**
     * 供应商产品ID
     */
    private String spProductId;

    /**
     * 0 mgs产品, 1,供应商产品
     */
    private Integer isSpProduct;

}
