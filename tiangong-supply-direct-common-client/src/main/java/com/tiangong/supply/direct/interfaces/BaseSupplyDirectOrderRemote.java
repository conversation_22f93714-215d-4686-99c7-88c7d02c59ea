package com.tiangong.supply.direct.interfaces;

import com.tiangong.supply.direct.entity.dto.CreateOrderDto;
import com.tiangong.supply.direct.entity.dto.PreBookingDto;
import com.tiangong.supply.direct.entity.request.CancelOrderRequest;
import com.tiangong.supply.direct.entity.request.CreateOrderInvoiceRequest;
import com.tiangong.supply.direct.entity.request.QuerySupplyOrderRequest;
import com.tiangong.supply.direct.entity.response.*;

/**
 * 订单父接口,供应商feign接口需要继承此接口
 * 并增加一个@SupplyDirectClass("供应商编码")
 */

public interface BaseSupplyDirectOrderRemote {

    /**
     * 试预订接口
     */
    PreBookingResponse preBooking(PreBookingDto preBookingDto);

    /**
     * 创建订单
     */
    CreateSupplyOrderResponse createSupplyOrder(CreateOrderDto createOrderDto);

    /**
     * 查询订单
     */
    QuerySupplyOrderResponse querySupplyOrder(QuerySupplyOrderRequest request);

    /**
     * 取消订单
     */
    CancelOrderResponse cancelSupplyOrder(CancelOrderRequest request);

    /**
     * 开票接口
     */
    CreateInvoiceResponse createInvoice(CreateOrderInvoiceRequest request);

}
