package com.tiangong.supply.direct.interfaces;

import com.tiangong.common.Response;
import com.tiangong.supply.direct.entity.dto.ProductMiddleDto;
import com.tiangong.supply.direct.entity.request.QueryProductInfoRequest;

import java.util.List;

/**
 * 查询产品父接口
 * 订单父接口,供应商feign接口需要继承此接口
 * 并增加一个@SupplyDirectClass("供应商编码")
 */
public interface BaseSupplyDirectProductRemote {

    /**
     * 查询产品基础信息
     */
    Response<List<ProductMiddleDto>> queryProductList(QueryProductInfoRequest queryProductInfoRequest);
}

