package com.tiangong.supply.direct.annotations;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 供应商对接,在feign接口上增加此注解
 * 通用模块根据注解中的value来实现动态路由
 */
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface SuppyDirectClass {

    String value() default "";
}
