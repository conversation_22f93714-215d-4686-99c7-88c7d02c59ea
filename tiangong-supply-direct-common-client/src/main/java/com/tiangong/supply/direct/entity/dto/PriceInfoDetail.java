package com.tiangong.supply.direct.entity.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2020-1-9 15:31
 * @Description:
 */
@Data
//@JsonInclude(JsonInclude.Include.NON_DEFAULT)
public class PriceInfoDetail {

    /**
     * 日期
     */
    private String date;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 价格明细
     * 主要用于海外展示
     */
    private TaxDetailDto taxDetail;

    /**
     * 币种
     */
    private Integer currency;

    /**
     * 是否待查(1:待查 0：非待查)
     */
    private Integer needQuery;

    /**
     * 早餐类型 1:中早 2:西早 3:自助早
     */
    private Integer breakfastType;

    /**
     * 早餐数量
     */
    private Integer breakfastNum;

    /**
     * 每日是否可定订 1:可定 0：不可订
     */
    private Integer canBook;

    /**
     * 可售房间数量
     */
    private Integer quotaNum;

    /**
     * 房态 1：有房 2：待查 3：满房
     */
    private Integer roomStatus ;

    /**
     * 是否可超(1：可超 0：不可超)
     */
    private Integer canOverDraft;

}
