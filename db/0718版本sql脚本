DROP TABLE IF EXISTS `tiangong<PERSON>ali`.`t_hotel_heat`;
CREATE TABLE `tiangongjiali`.`t_hotel_heat`  (
  `hotel_id` BIGINT NOT NULL COMMENT '酒店id',
	`heat_score` BIGINT NOT NULL COMMENT '热度分数',
	`bookable_score` BIGINT NOT NULL COMMENT '可订分数',
	`grade_score` BIGINT NOT NULL COMMENT '评分分数',
	`group_score` BIGINT NOT NULL COMMENT '所属集团分数',
	`city_avg_price_score` BIGINT NOT NULL COMMENT '城市平均房价分数',
	`room_night_score` BIGINT NOT NULL COMMENT '预订间夜分数',
  `created_dt` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `created_by` varchar(128) NULL DEFAULT NULL COMMENT '创建人',
	`updated_dt` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `updated_by` varchar(128) NULL DEFAULT NULL COMMENT '修改人',
  PRIMARY KEY (`hotel_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '酒店热度表' ROW_FORMAT = Dynamic;