DROP TABLE IF EXISTS file_log;
DROP TABLE IF EXISTS s_auth_menu;
DROP TABLE IF EXISTS s_auth_role;
DROP TABLE IF EXISTS s_auth_role_menu;
DROP TABLE IF EXISTS s_auth_user;
DROP TABLE IF EXISTS s_auth_user_role;
DROP TABLE IF EXISTS query;
DROP TABLE IF EXISTS f_room_charter;
DROP TABLE IF EXISTS f_room_charter_credit_record;
DROP TABLE IF EXISTS f_room_charter_statement;
DROP TABLE IF EXISTS f_room_charter_statement_order;
DROP TABLE IF EXISTS f_room_charter_used_record;

DROP TABLE IF EXISTS t_hotel_contract_hotel;
DROP TABLE IF EXISTS t_hotel_contract_import;
DROP TABLE IF EXISTS t_hotel_contract_product;

DROP TABLE IF EXISTS t_dis_access_config;
DROP TABLE IF EXISTS t_dis_agent_config;
DROP TABLE IF EXISTS t_dis_channel_config;
DROP TABLE IF EXISTS t_dis_elong_config;
DROP TABLE IF EXISTS t_dis_hotel;
DROP TABLE IF EXISTS t_dis_hotel_record;
DROP TABLE IF EXISTS t_dis_interface_log;
DROP TABLE IF EXISTS t_dis_ketan_mapping_product;
DROP TABLE IF EXISTS t_dis_mapping_city;
DROP TABLE IF EXISTS t_dis_mapping_common_hotel;
DROP TABLE IF EXISTS t_dis_mapping_common_room;
DROP TABLE IF EXISTS t_dis_mapping_hotel;
DROP TABLE IF EXISTS t_dis_mapping_hotel_log;
DROP TABLE IF EXISTS t_dis_mapping_ketan_product;
DROP TABLE IF EXISTS t_dis_mapping_os_hotel;
DROP TABLE IF EXISTS t_dis_mapping_os_room;
DROP TABLE IF EXISTS t_dis_mapping_product;
DROP TABLE IF EXISTS t_dis_mapping_room;
DROP TABLE IF EXISTS t_dis_room;
DROP TABLE IF EXISTS t_dis_shop_config;
DROP TABLE IF EXISTS t_dis_shop_field_config;

DROP TABLE IF EXISTS t_pro_idle_hotel;
DROP TABLE IF EXISTS t_pro_idle_hotel_log;

DROP TABLE IF EXISTS t_supply_area;
DROP TABLE IF EXISTS t_supply_channel_config;
DROP TABLE IF EXISTS t_supply_config;
DROP TABLE IF EXISTS t_supply_config_template;
DROP TABLE IF EXISTS t_supply_hotel_lowest_price;
DROP TABLE IF EXISTS t_supply_hotel_lowest_price_hourly;
DROP TABLE IF EXISTS t_supply_hotel_mapping;
DROP TABLE IF EXISTS t_supply_info;
DROP TABLE IF EXISTS t_supply_photo;
DROP TABLE IF EXISTS t_supply_priceplan_mapping;
DROP TABLE IF EXISTS t_supply_sale_config;
DROP TABLE IF EXISTS t_supply_sale_time;

DROP TABLE IF EXISTS t_org_subsidiary;
DROP TABLE IF EXISTS t_pro_nocached_sale_increase;
DROP TABLE IF EXISTS t_pro_nocached_sale_log;
DROP TABLE IF EXISTS t_pro_nocached_sale_status;






ALTER TABLE o_order
DROP COLUMN shop_id,
DROP COLUMN shop_alias,
DROP COLUMN is_synced_to_feizhu;

ALTER TABLE t_pro_dayincrease
DROP COLUMN ctrip_adjustment_type,
DROP COLUMN ctrip_modified_amt,
DROP COLUMN ctrip_mini_add_radiation,
DROP COLUMN meituan_adjustment_type,
DROP COLUMN meituan_modified_amt,
DROP COLUMN meituan_mini_add_radiation,
DROP COLUMN feizhu_adjustment_type,
DROP COLUMN feizhu_modified_amt,
DROP COLUMN feizhu_mini_add_radiation,
DROP COLUMN feizhu_shop1_adjustment_type,
DROP COLUMN feizhu_shop1_modified_amt,
DROP COLUMN feizhu_shop1_mini_add_radiation,
DROP COLUMN feizhu_shop2_adjustment_type,
DROP COLUMN feizhu_shop2_modified_amt,
DROP COLUMN feizhu_shop2_mini_add_radiation,
DROP COLUMN feizhu_shop3_adjustment_type,
DROP COLUMN feizhu_shop3_modified_amt,
DROP COLUMN feizhu_shop3_mini_add_radiation,
DROP COLUMN feizhu_shop4_adjustment_type,
DROP COLUMN feizhu_shop4_modified_amt,
DROP COLUMN feizhu_shop4_mini_add_radiation,
DROP COLUMN tcyl_adjustment_type,
DROP COLUMN tcyl_modified_amt,
DROP COLUMN tcyl_mini_add_radiation,
DROP COLUMN b2c_adjustment_type,
DROP COLUMN b2c_modified_amt,
DROP COLUMN b2c_mini_add_radiation,
DROP COLUMN qunar_adjustment_type,
DROP COLUMN qunar_modified_amt,
DROP COLUMN qunar_mini_add_radiation,
DROP COLUMN gaode_adjustment_type,
DROP COLUMN gaode_modified_amt,
DROP COLUMN gaode_mini_add_radiation;
ALTER TABLE t_pro_hotel_sale_info
DROP COLUMN b2c_sale_count,
DROP COLUMN ctrip_sale_count,
DROP COLUMN meituan_sale_count,
DROP COLUMN feizhu_sale_count,
DROP COLUMN tcyl_sale_count,
DROP COLUMN qunar_sale_count,
DROP COLUMN feizhu_shop1_sale_count,
DROP COLUMN feizhu_shop2_sale_count,
DROP COLUMN feizhu_shop3_sale_count,
DROP COLUMN feizhu_shop4_sale_count;

ALTER TABLE t_pro_sale_status
DROP COLUMN b2c_sale_status,
DROP COLUMN ctrip_sale_status,
DROP COLUMN meituan_sale_status,
DROP COLUMN feizhu_sale_status,
DROP COLUMN tcyl_sale_status,
DROP COLUMN qunar_sale_status,
DROP COLUMN gaode_sale_status,
DROP COLUMN feizhu_shop1_sale_status,
DROP COLUMN feizhu_shop2_sale_status,
DROP COLUMN feizhu_shop3_sale_status,
DROP COLUMN feizhu_shop4_sale_status;

ALTER TABLE o_order_prebooking_log
DROP COLUMN shop_id;

ALTER TABLE t_dis_channel_agent
DROP COLUMN shop_id;

ALTER TABLE t_pro_increase
DROP COLUMN shop_id,
DROP COLUMN shop_alias;

ALTER TABLE t_pro_sale_log
DROP COLUMN shop_id;