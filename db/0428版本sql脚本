-- 新增供应商配置信息表字段
ALTER TABLE `tiangongjiali`.t_exchangerate ADD COLUMN adjustment_type int DEFAULT 3 COMMENT '增量类型0加数值 1减数值 2加百分比 3减百分比 4等于';
ALTER TABLE `tiangongjiali`.t_exchangerate ADD COLUMN modified_amt decimal(10,2) DEFAULT 0 COMMENT '调整金额';
ALTER TABLE `tiangongjiali`.t_exchangerate ADD COLUMN exchange_rate decimal(20,8) DEFAULT NULL COMMENT '汇率';
ALTER TABLE `tiangongjiali`.t_exchangerate ADD COLUMN auto_sync int DEFAULT 1 COMMENT '是否自动同步：0否1是';


update `tiangongjiali`.t_exchangerate set exchange_rate = rate;

ALTER TABLE `tiangongjiali`.t_org_organization ADD COLUMN org_currency varchar(10) DEFAULT NULL COMMENT '商户币种';

update `tiangongjiali`.t_org_organization set org_currency = '0' where org_code = 'SC10049';

ALTER TABLE `tiangongjiali`.t_org_bank ADD COLUMN bank_type int DEFAULT 1 COMMENT '0网商银行 1其他';

ALTER TABLE `tiangongjiali`.`t_exchangerate`
MODIFY COLUMN `rate` decimal(20,8) DEFAULT NULL COMMENT '最终汇率';

ALTER TABLE `tiangongjiali`.`o_order`
MODIFY COLUMN `sale_rate` decimal(20,8) DEFAULT NULL COMMENT '汇率';

ALTER TABLE `tiangongjiali`.`o_supply_order`
MODIFY COLUMN `rate` decimal(20,8) DEFAULT NULL COMMENT '汇率';

ALTER TABLE `tiangongjiali`.`o_supply_order`
MODIFY COLUMN `supplier_to_agent_rate` decimal(30,18) DEFAULT NULL COMMENT '供应商对分销商汇率';


ALTER TABLE `tiangongjiali`.t_org_company_agent ADD COLUMN domestic_or_overseas int DEFAULT 1 COMMENT '国内全国标识：1国内 2全国';

DROP TABLE IF EXISTS `tiangongjiali`.`t_baseinfo_hot_city_click`;
CREATE TABLE `tiangongjiali`.`t_baseinfo_hot_city_click`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `city_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `city_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `hot_num` int NULL DEFAULT NULL,
  `latitude` decimal(13, 6) NULL DEFAULT NULL COMMENT '纬度',
  `longitude` decimal(13, 6) NULL DEFAULT NULL COMMENT '经度',
  `time_zone` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;


ALTER TABLE `tiangongjiali`.o_order ADD COLUMN bed_type_diff int DEFAULT 0 COMMENT '床型是否相同(基础信息和供应商)：0相同1不相同 空相同';

DROP TABLE IF EXISTS `tiangongjiali`.`f_statement_amt_log`;
CREATE TABLE `tiangongjiali`.`f_statement_amt_log`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `type` int NULL DEFAULT NULL COMMENT '0:客户账单管理 1：供应商账单管理 2：供应商奖励账单 3：供应商返佣账单',
  `statement_id` int NULL DEFAULT NULL COMMENT '账单id',
  `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '内容',
  `created_by` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `created_dt` datetime NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 21 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

ALTER TABLE `tiangongjiali`.o_supply_order ADD COLUMN is_check_in_detail_abnormal int DEFAULT 0 COMMENT '入住明显是否异常：0否1是';


ALTER TABLE tiangongjiali.o_order add org_to_agent_rate DECIMAL(20, 8) COMMENT '商家转客户汇率';
ALTER TABLE tiangongjiali.o_supply_order add org_to_agent_rate DECIMAL(20, 8) COMMENT '商家转客户汇率';
ALTER TABLE tiangongjiali.o_order add base_price DECIMAL(14, 2) COMMENT '底价（现付产品使用）';
ALTER TABLE tiangongjiali.o_order add base_currency INT(2) COMMENT '底价币种（现付产品使用）';

DROP TABLE IF EXISTS `tiangongjiali`.`o_supply_order_amt_log`;
CREATE TABLE `tiangongjiali`.`o_supply_order_amt_log`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `order_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `supply_order_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `created_dt` date NULL DEFAULT NULL,
  `status` tinyint NULL DEFAULT 0 COMMENT '是否已处理：0否 1是',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;



-- 新增菜单，修改菜单顺序
INSERT INTO `t_auth_menu` ( `menu_name`, `menu_code`, `menu_level`, `menu_rank`, `parent_code`, `back_end_url`, `type`, `active`, `created_by`, `created_dt`, `modified_by`, `modified_dt`, `front_end_url`, `open_type`) VALUES ('应收报表', 'M000571', 2, 3, 'M000005', NULL, 0, 1, 'system', NOW(), NULL, NULL, '/receivableStatistics', NULL);
INSERT INTO `t_auth_menu` ( `menu_name`, `menu_code`, `menu_level`, `menu_rank`, `parent_code`, `back_end_url`, `type`, `active`, `created_by`, `created_dt`, `modified_by`, `modified_dt`, `front_end_url`, `open_type`) VALUES ('应付报表', 'M000572', 2, 4, 'M000005', NULL, 0, 1, 'system', NOW(), NULL, NULL, '/payableStatistics', NULL);
UPDATE t_auth_menu SET menu_rank = 5 WHERE menu_code = 'M000116';
UPDATE t_auth_menu SET menu_rank = 6 WHERE menu_code = 'M000508';

-- 删除接口频率重复数据
DELETE from t_org_agent_api_invoke_config where partner_code in (
select a.partner_code from(
select count(1) as num, partner_code, method_code from t_org_agent_api_invoke_config GROUP BY partner_code,method_code
    ) a where a.num > 1
) and method_code in (
select a.method_code from(
select count(1) as num, partner_code, method_code from t_org_agent_api_invoke_config GROUP BY partner_code,method_code
    ) a where a.num > 1
);

-- 担保条款
ALTER TABLE tiangongjiali.o_order_restrict MODIFY COLUMN guarantee_type tinyint(2) COMMENT '担保条款类型 为空或0：未设置担保条款 1：按到店时间 2：按预订间数 3：预订即保（预订就必须担保）';
ALTER TABLE tiangongjiali.o_order_restrict ADD COLUMN guarantee_condition VARCHAR(64) COMMENT '担保条件 guaranteeType为1时：时间点，如1700，表示17点 为2时：间数，如：2 为3时：无';
ALTER TABLE tiangongjiali.o_order_restrict MODIFY COLUMN guarantee_fee_type tinyint(2) COMMENT '担保费用类型 1:全额 2:首晚';

ALTER TABLE tiangongjiali.f_agent_statement_order MODIFY COLUMN product_name VARCHAR(128);
)

DROP TABLE IF EXISTS `sys_config_log_code`;
CREATE TABLE `sys_config_log_code`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `log_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '日志编码',
  `log_content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '日志内容',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uni_code`(`log_code` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 36 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_config_log_code
-- ----------------------------
INSERT INTO `sys_config_log_code` VALUES (1, 'O000001', '您的订单号');
INSERT INTO `sys_config_log_code` VALUES (2, 'O000002', '入住');
INSERT INTO `sys_config_log_code` VALUES (3, 'O000003', '离店的');
INSERT INTO `sys_config_log_code` VALUES (4, 'O000004', '的');
INSERT INTO `sys_config_log_code` VALUES (5, 'O000005', '间');
INSERT INTO `sys_config_log_code` VALUES (6, 'O000006', '已为您预订成功！真诚祝您入住愉快！');
INSERT INTO `sys_config_log_code` VALUES (7, 'O000007', '酒店地址');
INSERT INTO `sys_config_log_code` VALUES (8, 'O000008', '酒店电话');
INSERT INTO `sys_config_log_code` VALUES (9, 'O000009', '已为您取消！欢迎再次预订！');
INSERT INTO `sys_config_log_code` VALUES (10, 'O000010', '客户');
INSERT INTO `sys_config_log_code` VALUES (11, 'O000011', '运营商');
INSERT INTO `sys_config_log_code` VALUES (12, 'O000012', '内部备注');
INSERT INTO `sys_config_log_code` VALUES (13, 'O000013', '入住明细不符');
INSERT INTO `sys_config_log_code` VALUES (14, 'O000014', '供货单');
INSERT INTO `sys_config_log_code` VALUES (15, 'O000015', '入住明细总金额为');
INSERT INTO `sys_config_log_code` VALUES (16, 'O000016', '与供货单房费金额不符,请查看是否已处理');
INSERT INTO `sys_config_log_code` VALUES (17, 'O000017', '在线退款通知');
INSERT INTO `sys_config_log_code` VALUES (18, 'O000018', '添加了');
INSERT INTO `sys_config_log_code` VALUES (19, 'O000019', '更新了');
INSERT INTO `sys_config_log_code` VALUES (20, 'O000020', '删除了');
INSERT INTO `sys_config_log_code` VALUES (21, 'O000021', '供货单');
INSERT INTO `sys_config_log_code` VALUES (22, 'O000022', '订单');
INSERT INTO `sys_config_log_code` VALUES (23, 'O000023', '账单金额由');
INSERT INTO `sys_config_log_code` VALUES (24, 'O000024', '变更为');
INSERT INTO `sys_config_log_code` VALUES (25, 'O000025', '账单款');
INSERT INTO `sys_config_log_code` VALUES (26, 'O000026', 'system(自助结算)');
INSERT INTO `sys_config_log_code` VALUES (27, 'O000027', '订单通工单处理成功');
INSERT INTO `sys_config_log_code` VALUES (28, 'O000028', '天宫工单处理成功');
INSERT INTO `sys_config_log_code` VALUES (29, 'O000029', '合计');
INSERT INTO `sys_config_log_code` VALUES (30, 'O000030', '加幅为');
INSERT INTO `sys_config_log_code` VALUES (31, 'O000031', '创建了汇率,汇率为');
INSERT INTO `sys_config_log_code` VALUES (32, 'O000032', '最终汇率为');
INSERT INTO `sys_config_log_code` VALUES (33, 'O000033', '修改汇率由');
INSERT INTO `sys_config_log_code` VALUES (34, 'O000034', '修改加幅由');
INSERT INTO `sys_config_log_code` VALUES (35, 'O000035', '修改为');

CREATE INDEX index_roomtype_id ON t_baseinfo_room_en_us (roomtype_id);
CREATE INDEX index_roomtype_id ON t_baseinfo_room_zh_cn (roomtype_id);

ALTER TABLE `tiangongjiali`.t_baseinfo_hotel_zh_cn ADD COLUMN lat_google decimal(13,6) DEFAULT NULL COMMENT '谷歌纬度';
ALTER TABLE `tiangongjiali`.t_baseinfo_hotel_zh_cn ADD COLUMN lng_google decimal(13,6) DEFAULT NULL COMMENT '谷歌经度';
ALTER TABLE `tiangongjiali`.t_baseinfo_hotel_en_us ADD COLUMN lat_google decimal(13,6) DEFAULT NULL COMMENT '谷歌纬度';
ALTER TABLE `tiangongjiali`.t_baseinfo_hotel_en_us ADD COLUMN lng_google decimal(13,6) DEFAULT NULL COMMENT '谷歌经度';

update t_org_supplier_label_config set label_type = 8 where label_type = 1;
update t_org_supplier_label_config set label_type = 9 where label_type = 2;
update t_org_supplier_label_config set label_type = 1 where label_type = 3;
update t_org_supplier_label_config set label_type = 2 where label_type = 8;
update t_org_supplier_label_config set label_type = 3 where label_type = 9;