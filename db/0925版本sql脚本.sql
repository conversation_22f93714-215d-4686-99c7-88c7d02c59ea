INSERT INTO `t_org_agent_api_invoke_config` VALUES (NULL, NULL, 'getAddress', '获取目的地接口', 100, 1, 'system', '2024-9-25 00:00:00', 'system', '2024-9-25 00:00:00', 0);
INSERT INTO `t_org_agent_api_invoke_config` VALUES (NULL, NULL, 'getHotelSearch', '获取酒店过滤器接口', 100, 1, 'system', '2024-9-25 00:00:00', 'system', '2024-9-25 00:00:00', 0);
INSERT INTO `t_org_agent_api_invoke_config` VALUES (NULL, NULL, 'findHotelList', '获取酒店列表接口', 100, 1, 'system', '2024-9-25 00:00:00', 'system', '2024-9-25 00:00:00', 0);


DROP TABLE IF EXISTS `tiangongjiali`.`t_order_change_word_order_task`;
CREATE TABLE `tiangongjiali`.`t_order_change_word_order_task` (
  `task_order_code` varchar(32) NOT NULL COMMENT '任务单号',
	`order_code` varchar(32) NOT NULL COMMENT '订单编码',
	`task_type` int(2) NOT NULL COMMENT '任务类型(1退房任务)',
	`refund_type` int(2) DEFAULT NULL COMMENT '退款类型(0整单取消 1部分取消)',
	`status` tinyint(2) NOT NULL COMMENT '状态(0待处理 1跟进中 2已完成)',
	`follow_up` varchar(128) DEFAULT NULL COMMENT '跟进人',
	`change_reason` varchar(2000) DEFAULT NULL COMMENT '退改原因',
	`refund_dt` datetime NULL DEFAULT NULL COMMENT '退改完成时间',
	`remark` varchar(2000) DEFAULT NULL COMMENT '备注',
	`handle_result` tinyint(2) DEFAULT NULL COMMENT '处理结果(1同意 2拒绝)',
	`agent_refund` decimal(12, 2) DEFAULT NULL COMMENT '我司退客户金额',
	`supply_refund` decimal(12, 2) DEFAULT NULL COMMENT '供应商退我司金额',
  `created_dt` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `created_by` varchar(128) NULL DEFAULT NULL COMMENT '创建人',
	`updated_dt` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `updated_by` varchar(128) NULL DEFAULT NULL COMMENT '修改人'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '退改工单任务表' ROW_FORMAT = Dynamic;
ALTER TABLE `tiangongjiali`.`t_order_change_word_order_task` ADD UNIQUE `idx_uni_order_code` (`order_code`);

DROP TABLE IF EXISTS `tiangongjiali`.`t_order_change_word_order_task_item`;
CREATE TABLE `tiangongjiali`.`t_order_change_word_order_task_item` (
  `item_id` int NOT NULL AUTO_INCREMENT COMMENT '明细id',
  `task_order_code` varchar(32) NOT NULL COMMENT '任务单号',
	`room_index` varchar(32) DEFAULT NULL COMMENT '房间序号',
	`change_date` varchar(256) DEFAULT NULL COMMENT '退改日期，多个用逗号隔开',
	`guest_name` varchar(256) DEFAULT NULL COMMENT '入住人，多个用逗号隔开',
	`agent_guest_name` varchar(256) DEFAULT NULL COMMENT '下游传入的入住人，多个用逗号隔开',
  `created_dt` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `created_by` varchar(128) NULL DEFAULT NULL COMMENT '创建人',
	`updated_dt` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `updated_by` varchar(128) NULL DEFAULT NULL COMMENT '修改人',
  PRIMARY KEY (`item_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '退改工单任务明细表' ROW_FORMAT = Dynamic;

DROP TABLE IF EXISTS `tiangongjiali`.`t_order_change_word_order_task_price_item`;
CREATE TABLE `tiangongjiali`.`t_order_change_word_order_task_price_item` (
	`id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
  `item_id` int NOT NULL COMMENT '明细id',
	`sale_date` varchar(32) NOT NULL COMMENT '售卖日期',
	`room_price` decimal(12, 2) DEFAULT NULL COMMENT '房费',
	`refund_price` decimal(12, 2) DEFAULT NULL COMMENT '退订费',
  `created_dt` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `created_by` varchar(128) NULL DEFAULT NULL COMMENT '创建人',
	`updated_dt` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `updated_by` varchar(128) NULL DEFAULT NULL COMMENT '修改人',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '退改工单任务价格价格明细表' ROW_FORMAT = Dynamic;

ALTER TABLE `tiangongjiali`.`t_org_order_automatic` MODIFY COLUMN created_dt DATETIME DEFAULT NULL COMMENT '创建时间';
ALTER TABLE `tiangongjiali`.`t_org_order_automatic` MODIFY COLUMN modified_by VARCHAR(64) DEFAULT NULL COMMENT '修改人';
ALTER TABLE `tiangongjiali`.`t_org_order_automatic` MODIFY COLUMN modified_dt DATETIME DEFAULT NULL COMMENT '修改时间';
# 退改通知推送配置接口
ALTER TABLE `tiangongjiali`.`t_org_agent_api_config` ADD   `order_checkout_notify_url` varchar(255) DEFAULT NULL COMMENT '退改通知推送接口'  AFTER `warranties_notify_url`;