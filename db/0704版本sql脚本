#有执行顺序，先执行
update `tiangongjiali`.o_order_remark t set t.remark_type = 1 where t.remark_type = 2;
#后执行
update `tiangongjiali`.o_order_remark t set t.remark_type = 2 where t.remark_type = 4;

#新增最低加幅金额字段
ALTER TABLE `tiangongjiali`.`t_pro_org_increase` ADD COLUMN `lowest_increase_amt` decimal(12, 2) DEFAULT NULL COMMENT '最低加幅金额';
ALTER TABLE `tiangongjiali`.`t_org_company_supplier` ADD COLUMN `lowest_increase_amt` decimal(12, 2) DEFAULT NULL COMMENT '最低加幅金额';
ALTER TABLE `tiangongjiali`.`t_org_supplier_increase_log` ADD COLUMN `lowest_increase_amt` decimal(12, 2) DEFAULT NULL COMMENT '最低加幅金额';

#创建酒店标签配置表
DROP TABLE IF EXISTS `tiangongjiali`.`t_hotel_label_config`;
CREATE TABLE `tiangongjiali`.`t_hotel_label_config`  (
  `hotel_label_config_id` int NOT NULL AUTO_INCREMENT,
  `label_code` varchar(32) DEFAULT NULL COMMENT '标签编码',
  `label_name` varchar(128) NOT NULL COMMENT '标签名称',
  `recommend_score` int(10) NOT NULL COMMENT '标签推荐分值',
	`deleted` tinyint(1) NOT NULL COMMENT '删除状态',
  `created_dt` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `created_by` varchar(128) NULL DEFAULT NULL COMMENT '创建人',
	`updated_dt` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `updated_by` varchar(128) NULL DEFAULT NULL COMMENT '修改人',
  PRIMARY KEY (`hotel_label_config_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '酒店标签配置表' ROW_FORMAT = Dynamic;

#创建酒店标签配置日志表
DROP TABLE IF EXISTS `tiangongjiali`.`t_hotel_label_config_log`;
CREATE TABLE `tiangongjiali`.`t_hotel_label_config_log`  (
  `id` int NOT NULL AUTO_INCREMENT,
	`hotel_label_config_id` int NOT NULL COMMENT '酒店标签配置id',
  `content` varchar(256) DEFAULT NULL COMMENT '操作内容',
  `created_dt` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `created_by` varchar(128) NULL DEFAULT NULL COMMENT '创建人',
	`updated_dt` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `updated_by` varchar(128) NULL DEFAULT NULL COMMENT '修改人',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '酒店标签配置日志表' ROW_FORMAT = Dynamic;

#创建酒店推荐标签关联表
DROP TABLE IF EXISTS `tiangongjiali`.`t_hotel_recommend_label_rel`;
CREATE TABLE `tiangongjiali`.`t_hotel_recommend_label_rel`  (
  `id` int NOT NULL AUTO_INCREMENT,
	`hotel_recommend_id` int NOT NULL COMMENT '酒店推荐id',
  `hotel_label_config_id` int NOT NULL COMMENT '酒店标签配置id',
  `created_dt` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `created_by` varchar(128) NULL DEFAULT NULL COMMENT '创建人',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '酒店推荐标签关联表' ROW_FORMAT = Dynamic;

#修改描述
ALTER TABLE `tiangongjiali`.t_hotel_recommend MODIFY COLUMN agent_code varchar(32) not null COMMENT '客户编码(当为0时，适用所有客户)';

INSERT INTO `tiangongjiali`.`t_auth_menu` (`menu_name`, `menu_code`, `menu_level`, `menu_rank`, `parent_code`, `back_end_url`, `type`, `active`, `created_by`, `created_dt`, `modified_by`, `modified_dt`, `front_end_url`, `open_type`)
VALUES ('酒店标签管理', 'M000573', 2, 16, 'M000010', NULL, 0, 1, NULL, NULL, NULL, NULL, '/hoteltag', NULL);

INSERT INTO `tiangongjiali`.`t_auth_role_menu` (`role_id`, `menu_id`, `created_by`, `create_dt`, `modified_by`, `modified_dt`)
VALUES (1, (select t.id from t_auth_menu t where t.menu_code = 'M000573'), 'xinpeng', SYSDATE(), NULL, NULL);
INSERT INTO `tiangongjiali`.`t_auth_role_menu` (`role_id`, `menu_id`, `created_by`, `create_dt`, `modified_by`, `modified_dt`)
VALUES (2, (select t.id from t_auth_menu t where t.menu_code = 'M000573'), 'xinpeng', SYSDATE(), NULL, NULL);

