-- 需要新增定时任务（InitFacilityTask）
-- 需要新增concat.yml中腾讯云日志的配置
-- 基础信息需要发包支持拉取设施

DROP TABLE IF EXISTS `t_facility`;
CREATE TABLE `t_facility`  (
                               `id` int NOT NULL AUTO_INCREMENT,
                               `facility_id` int NULL DEFAULT NULL COMMENT '设施id',
                               `facility_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '设施名称',
                               `facility_type` tinyint NULL DEFAULT NULL COMMENT '设施类型：1酒店 2房型',
                               `language` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '语言',
                               `created_by` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT '' COMMENT '创建人',
                               `created_dt` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                               `modified_by` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT '' COMMENT '修改人',
                               `modified_dt` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
                               `deleted` tinyint NULL DEFAULT 0 COMMENT '是否删除(0 否，1 是)',
                               PRIMARY KEY (`id`) USING BTREE,
                               INDEX `idx_facility_id`(`facility_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2806 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;

DROP TABLE IF EXISTS `t_facility_config`;
CREATE TABLE `t_facility_config`  (
                                      `id` int NOT NULL AUTO_INCREMENT,
                                      `facility_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '设施编码',
                                      `facility_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '设施名称',
                                      `facility_id_list` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '包含的设施id',
                                      `facility_type` tinyint NULL DEFAULT NULL COMMENT '设施类型：1酒店 2房型',
                                      `type_score` int NULL DEFAULT NULL COMMENT '分类分数',
                                      `created_by` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT '' COMMENT '创建人',
                                      `created_dt` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                      `modified_by` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT '' COMMENT '修改人',
                                      `modified_dt` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
                                      `deleted` tinyint NULL DEFAULT 0 COMMENT '是否删除(0 否，1 是)',
                                      PRIMARY KEY (`id`) USING BTREE,
                                      INDEX `idx_facility_code`(`facility_code` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 39 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_facility_config
-- ----------------------------
INSERT INTO `t_facility_config` VALUES (1, 'FC1001', '游泳池', NULL, 1, 99, '', '2024-10-16 15:19:50', '', '2024-10-16 15:19:50', 0);
INSERT INTO `t_facility_config` VALUES (2, 'FC1002', '健身中心', NULL, 1, 99, '', '2024-10-16 15:19:59', '', '2024-10-16 15:19:59', 0);
INSERT INTO `t_facility_config` VALUES (3, 'FC1003', 'SPA及健康中心', NULL, 1, 99, '', '2024-10-16 15:20:03', '', '2024-10-16 15:20:03', 0);
INSERT INTO `t_facility_config` VALUES (4, 'FC1004', '餐厅', NULL, 1, 99, '', '2024-10-16 15:20:09', '', '2024-10-16 15:20:09', 0);
INSERT INTO `t_facility_config` VALUES (5, 'FC1005', '24小时前台', NULL, 1, 99, '', '2024-10-16 15:20:13', '', '2024-10-16 15:20:13', 0);
INSERT INTO `t_facility_config` VALUES (6, 'FC1006', '酒吧', NULL, 1, 99, '', '2024-10-16 15:20:22', '', '2024-10-16 15:20:22', 0);
INSERT INTO `t_facility_config` VALUES (7, 'FC1007', '免费早餐', NULL, 1, 99, '', '2024-10-16 15:20:28', '', '2024-10-16 15:20:28', 0);
INSERT INTO `t_facility_config` VALUES (8, 'FC1008', '停车场', NULL, 1, 99, '', '2024-10-16 15:20:37', '', '2024-10-16 15:20:37', 0);
INSERT INTO `t_facility_config` VALUES (9, 'FC1009', '免费WiFi', NULL, 1, 99, '', '2024-10-16 15:20:44', '', '2024-10-16 15:20:44', 0);
INSERT INTO `t_facility_config` VALUES (10, 'FC1010', '宠物友好', NULL, 1, 99, '', '2024-10-16 15:20:49', '', '2024-10-16 15:20:49', 0);
INSERT INTO `t_facility_config` VALUES (11, 'FC1011', '充电车位', NULL, 1, 99, '', '2024-10-16 15:20:54', '', '2024-10-16 15:20:54', 0);
INSERT INTO `t_facility_config` VALUES (12, 'FC1012', '货币兑换', NULL, 1, 99, '', '2024-10-16 15:20:59', '', '2024-10-16 15:20:59', 0);
INSERT INTO `t_facility_config` VALUES (13, 'FC1013', '会议室', NULL, 1, 99, '', '2024-10-16 15:21:03', '', '2024-10-16 15:21:03', 0);
INSERT INTO `t_facility_config` VALUES (14, 'FC1014', '洗衣设施', NULL, 1, 99, '', '2024-10-16 15:21:10', '', '2024-10-16 15:21:10', 0);
INSERT INTO `t_facility_config` VALUES (15, 'FC1015', '接送服务', NULL, 1, 99, '', '2024-10-16 15:21:16', '', '2024-10-16 15:21:16', 0);
INSERT INTO `t_facility_config` VALUES (16, 'FC1016', '行李寄存', NULL, 1, 99, '', '2024-10-16 15:23:28', '', '2024-10-16 15:23:28', 0);
INSERT INTO `t_facility_config` VALUES (17, 'FC2001', '浴缸', NULL, 2, 99, '', '2024-10-16 15:26:08', '', '2024-10-16 15:26:08', 0);
INSERT INTO `t_facility_config` VALUES (18, 'FC2002', '淋浴', NULL, 2, 99, '', '2024-10-16 15:26:12', '', '2024-10-16 15:26:12', 0);
INSERT INTO `t_facility_config` VALUES (19, 'FC2003', '洗衣机', NULL, 2, 99, '', '2024-10-16 15:26:54', '', '2024-10-16 15:26:54', 0);
INSERT INTO `t_facility_config` VALUES (20, 'FC2004', '厨房', NULL, 2, 99, '', '2024-10-16 15:26:59', '', '2024-10-16 15:26:59', 0);
INSERT INTO `t_facility_config` VALUES (21, 'FC2005', '冰箱', NULL, 2, 99, '', '2024-10-16 15:27:02', '', '2024-10-16 15:27:02', 0);
INSERT INTO `t_facility_config` VALUES (22, 'FC2006', '空调', NULL, 2, 99, '', '2024-10-16 15:27:06', '', '2024-10-16 15:27:06', 0);
INSERT INTO `t_facility_config` VALUES (23, 'FC2007', '吹风机', NULL, 2, 99, '', '2024-10-16 15:27:09', '', '2024-10-16 15:27:09', 0);
INSERT INTO `t_facility_config` VALUES (24, 'FC2008', '阳台', NULL, 2, 99, '', '2024-10-16 15:27:20', '', '2024-10-16 15:27:20', 0);
INSERT INTO `t_facility_config` VALUES (25, 'FC2009', '智能马桶', NULL, 2, 99, '', '2024-10-16 15:27:24', '', '2024-10-16 15:27:24', 0);
INSERT INTO `t_facility_config` VALUES (26, 'FC2010', '私人泳池', NULL, 2, 99, '', '2024-10-16 15:27:28', '', '2024-10-16 15:27:28', 0);
INSERT INTO `t_facility_config` VALUES (27, 'FC2011', '电脑', NULL, 2, 99, '', '2024-10-16 15:27:33', '', '2024-10-16 15:27:33', 0);
INSERT INTO `t_facility_config` VALUES (28, 'FC2012', '吸烟室', NULL, 2, 99, '', '2024-10-16 15:27:37', '', '2024-10-16 15:27:37', 0);
INSERT INTO `t_facility_config` VALUES (29, 'FC2013', '私人浴室', NULL, 2, 99, '', '2024-10-16 15:27:41', '', '2024-10-16 15:27:41', 0);
INSERT INTO `t_facility_config` VALUES (30, 'FC2014', '无障碍客房', NULL, 2, 99, '', '2024-10-16 15:27:46', '', '2024-10-16 15:27:46', 0);
INSERT INTO `t_facility_config` VALUES (31, 'FC2015', '禁烟房', NULL, 2, 99, '', '2024-10-16 15:27:49', '', '2024-10-16 15:27:49', 0);
INSERT INTO `t_facility_config` VALUES (32, 'FC2016', '电视', NULL, 2, 99, '', '2024-10-16 15:28:20', '', '2024-10-16 15:28:20', 0);
INSERT INTO `t_facility_config` VALUES (33, 'FC2017', '咖啡/泡茶设备', NULL, 2, 99, '', '2024-10-16 15:28:51', '', '2024-10-16 15:28:51', 0);
INSERT INTO `t_facility_config` VALUES (34, 'FC2018', '电热水壶', NULL, 2, 99, '', '2024-10-16 15:28:57', '', '2024-10-16 15:28:57', 0);
INSERT INTO `t_facility_config` VALUES (35, 'FC2019', '可加床', NULL, 2, 99, '', '2024-10-16 15:29:02', '', '2024-10-16 15:29:02', 0);
INSERT INTO `t_facility_config` VALUES (36, 'FC2020', '可加婴儿床', NULL, 2, 99, '', '2024-10-16 15:29:07', '', '2024-10-16 15:29:07', 0);
INSERT INTO `t_facility_config` VALUES (37, 'FC2021', '卫生纸', NULL, 2, 99, '', '2024-10-16 15:29:12', '', '2024-10-16 15:29:12', 0);
INSERT INTO `t_facility_config` VALUES (38, 'FC2022', '庭院', NULL, 2, 99, '', '2024-10-16 15:29:16', '', '2024-10-16 15:29:16', 0);

SET FOREIGN_KEY_CHECKS = 1;