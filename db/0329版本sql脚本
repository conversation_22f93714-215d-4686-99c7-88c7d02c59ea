

-- 新增供应商配置信息表字段
ALTER TABLE t_org_company_supplier ADD COLUMN invoice_id int DEFAULT NULL COMMENT '我方合作主体';
ALTER TABLE t_org_company_supplier ADD COLUMN channel int DEFAULT NULL COMMENT '通道商（0：Amadeus 1订单通 2畅联 3绿云 4住哲 5Sabre 6其他）';
ALTER TABLE t_org_company_supplier ADD COLUMN enable_protocol int DEFAULT NULL COMMENT '是否协议托管：0否 1是';
ALTER TABLE t_org_company_supplier ADD COLUMN enable_settled int DEFAULT NULL COMMENT '是否待结算：0否 1是';
ALTER TABLE t_org_company_supplier ADD COLUMN remark varchar(512) DEFAULT NULL COMMENT '描述';



ALTER TABLE t_org_company_agent ADD COLUMN invoice_id int DEFAULT NULL COMMENT '我方合作主体';
ALTER TABLE t_org_company_agent ADD COLUMN settled_type int DEFAULT NULL COMMENT '结算模式：0挂账 1预存';
ALTER TABLE t_org_company_agent ADD COLUMN remark varchar(512) DEFAULT NULL COMMENT '描述';




-- 新增联系人表字段
ALTER TABLE t_org_contact ADD COLUMN contact_type int DEFAULT NULL COMMENT '类型：0供应商 1我方';




ALTER TABLE o_order ADD COLUMN remaining_balance decimal(15,2) DEFAULT NULL COMMENT '剩余额度';


CREATE TABLE `t_baseinfo_facility`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `parking_ids` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '停车场',
  `meeting_ids` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '会议室',
  `swimming_ids` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '泳池',
  `restaurant_ids` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '餐厅',
  `gym_ids` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '健身房',
  `robot_ids` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '机器人',
  `laundry_room_ids` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '洗衣服',
  `luggage_ids` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '行李寄存',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '设施配置表' ROW_FORMAT = Dynamic;


DROP TABLE IF EXISTS `s_sys_config`;
CREATE TABLE `s_sys_config`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `str_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'key值',
  `str_value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'value值',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '描述',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of s_sys_config
-- ----------------------------
INSERT INTO `s_sys_config` VALUES (1, 'travelNoticePolicy', 'instructions,specialInstructions,mandatoryFees,optionalFees', '行程须知政策');
INSERT INTO `s_sys_config` VALUES (2, 'enableSendMsg', '0', '是否发送短信：0否1是');

-- ----------------------------
-- Table structure for s_tips
-- ----------------------------
DROP TABLE IF EXISTS `s_tips`;
CREATE TABLE `s_tips`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `type` tinyint NULL DEFAULT NULL COMMENT '说明类型：0税费说明 1订单填写说明',
  `supplier_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '供应商编码',
  `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '说明内容',
  `active` tinyint NULL DEFAULT 1 COMMENT '启用状态：0否 1是',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '提示说明' ROW_FORMAT = Dynamic;

INSERT INTO `t_org_agent_api_invoke_config` VALUES (104, NULL, 'queryHotCityList', '查询热门城市', 100, 1, NULL, NULL, NULL, NULL, 0);
INSERT INTO `t_org_agent_api_invoke_config` VALUES (105, NULL, 'queryHotelAllList', '查询酒店列表信息', 100, 1, NULL, NULL, NULL, NULL, 0);
INSERT INTO `t_org_agent_api_invoke_config` VALUES (106, NULL, 'queryCityAndHotelByKeyword', '查询城市和酒店信息', 100, 1, NULL, NULL, NULL, NULL, 0);
INSERT INTO `t_org_agent_api_invoke_config` VALUES (107, NULL, 'queryDistrictAndBusiness', '行政区商业区筛选条件', 100, 1, NULL, NULL, NULL, NULL, 0);



#入住明细推送sql脚本
CREATE TABLE `o_order_check_push_config` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增长主键id',
  `channel_code` int DEFAULT NULL COMMENT '渠道编码',
  `channel_name` varchar(32) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '渠道名称',
  `agent_code` varchar(512) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '分销商编码',
  `merchant_code` varchar(32) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '商家编码 天宫商家编码',
  `order_type` tinyint(1) DEFAULT NULL COMMENT '订单类型 [''预付'',''现付'',''预付+现付'']',
  `public_key` varchar(1024) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '验签公钥',
  `private_key` varchar(2048) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '验签私钥',
  `secret_key` varchar(256) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '加解/秘密钥',
  `iv_parameter` varchar(256) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '平安向量值',
  `order_check_detail_notify_url` varchar(128) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '入住明细推送接口',
  `is_active` tinyint(1) DEFAULT NULL COMMENT '是否有效 [''无效'',''有效'']',
  `created_by` varchar(64) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人 下单人',
  `created_dt` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 下单时间',
  `updated_by` varchar(64) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '更新人',
  `updated_dt` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='入住明细推送配置表';



CREATE TABLE `o_order_check_push_info` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '入住明细推送信息ID 主键id',
  `order_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '订单编码 天宫订单编码',
  `check_in_date` date DEFAULT NULL COMMENT '入住日期',
  `check_out_date` date DEFAULT NULL COMMENT '离店日期',
  `channel_order_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '分销商订单编码',
  `agent_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '分销商编码',
  `agent_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '分销商名称',
  `hotel_name` varchar(400) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '酒店名称 国内酒店传中文酒店名称，否则传英文酒店名称',
  `guest` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '所有入住人',
  `push_status` tinyint(1) DEFAULT NULL COMMENT '推送状态 [''推送失败'',''入住明细异常'',''未填写入住明细'',''新建'',''已推送'']',
  `room_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '房型名称',
  `rate_plan_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '价格计划名称',
  `channel_code` int DEFAULT NULL COMMENT '渠道编码',
  `pay_method` tinyint(1) DEFAULT NULL COMMENT '付款方式 [''预付'',''现付'']',
  `error_message` varchar(256) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '错误信息',
  `created_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人 下单人',
  `created_dt` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 下单时间',
  `updated_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '更新人',
  `updated_dt` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='入住明细推送表';


CREATE TABLE `o_order_check_push_relation` (
  `order_id` bigint NOT NULL COMMENT '订单id',
  `order_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '订单编码 天宫订单编码',
  `push_dt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '推送时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='入住明细推送状态关联表';

INSERT INTO `tiangongjiali`.`o_order_check_push_config` (`id`, `channel_code`, `channel_name`, `agent_code`, `merchant_code`, `order_type`, `public_key`, `private_key`, `secret_key`, `iv_parameter`, `order_check_detail_notify_url`, `is_active`, `created_by`, `created_dt`, `updated_by`, `updated_dt`) VALUES (1, 1, '平安', '', 'SC10049', 2, 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAhsjLOouV7HOoxKEuGaQKTlWk/QKa89yzeK4YPBCNl818/D9yTm4rOTjHkwdjCioRxHMBfNT0TPzls31y/1D3810mL6urm5EfCckH07uwjiRp+EfewM00VRP+GFMfEZ7i6EewzNnIz0do1aJX7j3hPM1cqCH5Z+vZePTTXvzlecfbAifUPi8gx6iSn7NxxVf/90jjQ/Qhs+O3Ag64zpO66t4aW0XmdIMvf7M2PFomTtNNRazMnJRSyiyKI1GelEJ0lzs+x43c5mmEbDue4t2reB1RVvUnu9E7Za3m5nDl0JbC+hORUlggdGFvO5Q7ofsbrDgZIIrTEsIsvTibkxk8uQIDAQAB','MIICdQIBADANBgkqhkiG9w0BAQEFAASCAl8wggJbAgEAAoGBAKaxLyuw+aN1vVSlbOtWEY4dRpT27I6mOQ3w38nNfNaEp4vA1nd256IQFfjHA6YtWUqxy7siBLe0lrYmiw9GyXqCVRlR6sBlKz/mzSu0ZX+SIV3/AEOaQWdpWKqhGak6B7zgQm3ghE9oERAtRrWZcuquTzlFoNtas6KC9kuO9lmVAgMBAAECgYBHxFFCxlc0gSSapP5X9B5qyT/yMr12TSqTTfmLsOTmlwZrrQKpPJBNvg83pLd1fGDNP+8vCl5tmVqp4b5NPXTQj0spQAImw7yxCsrXT+KakZ6r5NxxD0g/n9AqgzALSSPvx0zE2y9BaJPlS9ObK3jW9ZJXGNhkfafIn6St0w5IoQJBAONPxFBze54icLL9+CDsoTfy4QH6/6j4ujqSfUCWF7wnis1g07AmW6eqdo00syRWTNyrwwxeCslSU0TkQPXFDhcCQQC7uttmiwW42XEju7rrIouRxNR9x5a50ZPsXNacJ2bf8T6HVeo/knXk7zhwcqBoAq6sLIdTCFZIimg3WECHqa0zAkArU+0LmLebcN0igR5myDQCwhz5j6pjvuVgfMO7J+DmUHM4qpgvH2ChGPD4AW+UIY8NEQBgvxw3O6X7n7ZyIlmJAkAPQxGzNHoynPrlvM1lcFmIcEgWMbO/R5qPDL0l1M44RpSezA1PPcj347ri6W7BBfbNaJFX3sWlABuY5EPaw6ChAkB2Uw+N3pz9LCK1/88FLjQh+MCDAWsl/MqA+YwsWU6PUFLj/ur4CBSU3ntsOiuEovhNUuN8eYi5dYpmVmuvNWkH', 'https://ckcgw.yqb.com/hotel/fangcang/hotelCheckStagePush/request', 1, 'system', NOW(), 'system', 'NOW()');
INSERT INTO `tiangongjiali`.`t_auth_menu` (`menu_name`, `menu_code`, `menu_level`, `menu_rank`, `parent_code`, `back_end_url`, `type`, `active`, `created_by`, `created_dt`, `modified_by`, `modified_dt`, `front_end_url`, `open_type`) VALUES ('入住明细推送监控', 'M000570', 2, 4, 'M000003', NULL, 0, 1, 'system', NOW(), NULL, NULL, '/checkInDetailsPush', NULL);


-- 自助结算模块
ALTER TABLE `tiangongjiali`.`f_settle_order`
ADD COLUMN `hotel_id` int(16) NULL COMMENT '酒店id' AFTER `updated_dt`,
ADD COLUMN `confirm_no` varchar(255) NULL COMMENT '确认号' AFTER `hotel_id`;

#补全数据
UPDATE f_settle_order
SET confirm_no = (
  SELECT confirmation_code
  FROM o_supply_order
  WHERE f_settle_order.supply_order_code = o_supply_order.supply_order_code
),
hotel_id = (
  SELECT hotel_id
  FROM o_supply_order
  WHERE f_settle_order.supply_order_code = o_supply_order.supply_order_code
)

-- 订单退款通知
INSERT INTO `sequence` (`id`, `seq_name`, `current_val`, `increment_val`) VALUES (15, 'seq_order_refund_task_code', 10000001, 1);
INSERT INTO `sequence` (`id`, `seq_name`, `current_val`, `increment_val`) VALUES (16, 'seq_order_deduction_task_code', 10000001, 1);

INSERT INTO `t_auth_menu` (`id`, `menu_name`, `menu_code`, `menu_level`, `menu_rank`, `parent_code`, `back_end_url`, `type`, `active`, `created_by`, `created_dt`, `modified_by`, `modified_dt`, `front_end_url`, `open_type`) VALUES (563,'在线退款通知', 'M000568', 2, 5, 'M000003', NULL, 0, 0, 'system', NOW(), NULL, NULL, '/orderRefund', NULL);
INSERT INTO `t_auth_menu` (`id`,  `menu_name`, `menu_code`, `menu_level`, `menu_rank`, `parent_code`, `back_end_url`, `type`, `active`, `created_by`, `created_dt`, `modified_by`, `modified_dt`, `front_end_url`, `open_type`) VALUES (564,'平安担保扣款通知', 'M000569', 2, 6, 'M000003', NULL, 0, 0, 'system', NOW(), NULL, NULL, '/orderDeduction', NULL);

CREATE TABLE `o_order_refund_task` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `task_code` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '任务编码',
  `order_id` bigint NOT NULL COMMENT '订单id',
  `order_code` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '订单编码',
  `refund_amount` decimal(16,2) NOT NULL COMMENT '退款金额',
  `agent_code` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '客户编码',
  `currency` int NOT NULL COMMENT '币种',
  `refund_state` tinyint(1) NOT NULL COMMENT '退款状态 0-新建 1-通知成功 2-已取消 3-退款中',
  `task_type` tinyint(1) NOT NULL COMMENT '任务类型 0-在线退款通知 1-平安担保扣款通知',
  `daily_refund_info` varchar(5000) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT '退款明细',
  `refund_type` tinyint(1) DEFAULT NULL COMMENT '退款类型 0-普通退款（平安）1-罚金退款（平安） 3-退款',
  `refund_task_type` tinyint(1) NOT NULL COMMENT '退款任务类型 0-人工 1-自动',
  `is_push` tinyint(1) NOT NULL COMMENT '是否推送 0-否 1-是',
  `deleted` tinyint(1) DEFAULT NULL COMMENT '删除状态',
  `created_by` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT '创建人',
  `created_dt` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_by` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT '更新人',
  `updated_dt` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='订单退款任务表';

CREATE TABLE `o_order_refund_task_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `refund_task_id` bigint NOT NULL COMMENT '退款任务ID',
  `content` varchar(2000) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '操作内容',
  `deleted` tinyint(1) DEFAULT NULL COMMENT '删除状态',
  `created_by` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '创建人',
  `created_dt` datetime NOT NULL COMMENT '创建时间',
  `updated_by` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT '更新人',
  `updated_dt` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=37 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='订单退款任务日志表';


-- 新增字段
ALTER TABLE tiangongjiali.o_order ADD COLUMN settled_type int DEFAULT NULL COMMENT '结算模式：0挂账 1预存';
ALTER TABLE tiangongjiali.o_supply_order add room_numbers VARCHAR(32) COMMENT '房间号（多个用逗号隔开）';
ALTER TABLE tiangongjiali.o_supply_order add e_cancel_supply_order TINYINT(2) COMMENT '异常取消供货单标识 0否 1是';
ALTER TABLE tiangongjiali.o_supply_order add e_cancel_supply_order_context VARCHAR(256) COMMENT '异常取消供货单描述';
ALTER TABLE tiangongjiali.o_supply_product add cancellation_term VARCHAR(255) COMMENT '取消条款';
-- 删除字段
ALTER TABLE tiangongjiali.o_order DROP COLUMN e_cancel_supply_order;
ALTER TABLE tiangongjiali.o_order DROP COLUMN e_cancel_supply_order_context;
-- 修改字段
ALTER TABLE tiangongjiali.o_supply_product_price MODIFY COLUMN pay_at_hotel_currency INT(2);

-- 删除英文房型重复数据
CREATE TEMPORARY TABLE temp_table AS
SELECT hotel_id, roomtype_id, MAX(created_dt) AS max_created_dt
FROM t_baseinfo_room_en_us
GROUP BY hotel_id, roomtype_id;

DELETE t1
FROM t_baseinfo_room_en_us t1
JOIN temp_table t2 ON t1.hotel_id = t2.hotel_id
                  AND t1.roomtype_id = t2.roomtype_id
                  AND t1.created_dt < t2.max_created_dt;

DROP TABLE temp_table;


-- 删除中文房型重复数据
CREATE TEMPORARY TABLE temp_table AS
SELECT hotel_id, roomtype_id, MAX(created_dt) AS max_created_dt
FROM t_baseinfo_room_zh_cn
GROUP BY hotel_id, roomtype_id;

DELETE t1
FROM t_baseinfo_room_zh_cn t1
JOIN temp_table t2 ON t1.hotel_id = t2.hotel_id
AND t1.roomtype_id = t2.roomtype_id
AND t1.created_dt < t2.max_created_dt;

DROP TABLE temp_table;

-- 修改字段名称

ALTER TABLE t_baseinfo_areadata_zh_cn
    CHANGE longtitude longitude DECIMAL(13,6);

		ALTER TABLE t_baseinfo_areadata_en_us
    CHANGE longtitude longitude DECIMAL(13,6);

		ALTER TABLE t_baseinfo_hotel_en_us
    CHANGE longtitude longitude DECIMAL(13,6);

		ALTER TABLE t_baseinfo_hotel_zh_cn
    CHANGE longtitude longitude DECIMAL(13,6);

-- 删除超管多个角色身份
    delete from t_auth_user_role where user_id = 1 and role_id > 1;
    delete from t_auth_role_menu where menu_id > 108998;


CREATE TABLE `t_baseinfo_hot_city`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `city_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '城市编码',
  `city_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '城市名称',
  `city_en_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '城市英文名称',
  `supply_type` tinyint NULL DEFAULT NULL COMMENT '国内海外标识：1国内 2海外',
  `area_type` int NULL DEFAULT NULL COMMENT '区域类型',
  `parent_area_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '父级id',
  `latitude` decimal(13, 6) NULL DEFAULT NULL COMMENT '纬度',
  `longitude` decimal(13, 6) NULL DEFAULT NULL COMMENT '经度',
  `time_zone` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '时区',
  `update_time` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '热门城市' ROW_FORMAT = Dynamic;

INSERT INTO `t_auth_role_menu` (role_id,menu_id,created_by,create_dt, modified_by,modified_dt) VALUES (1, 561, NULL, NULL, NULL, NULL);
INSERT INTO `t_auth_role_menu` (role_id,menu_id,created_by,create_dt, modified_by,modified_dt) VALUES (1, 562, NULL, NULL, NULL, NULL);
INSERT INTO `t_auth_role_menu` (role_id,menu_id,created_by,create_dt, modified_by,modified_dt) VALUES (1, 563, NULL, NULL, NULL, NULL);
INSERT INTO `t_auth_role_menu` (role_id,menu_id,created_by,create_dt, modified_by,modified_dt) VALUES (1, 564, NULL, NULL, NULL, NULL);
INSERT INTO `t_auth_role_menu` (role_id,menu_id,created_by,create_dt, modified_by,modified_dt) VALUES (1, 565, NULL, NULL, NULL, NULL);

INSERT INTO `t_auth_role_menu` (role_id,menu_id,created_by,create_dt, modified_by,modified_dt) VALUES (2, 561, NULL, NULL, NULL, NULL);
INSERT INTO `t_auth_role_menu` (role_id,menu_id,created_by,create_dt, modified_by,modified_dt) VALUES (2, 562, NULL, NULL, NULL, NULL);
INSERT INTO `t_auth_role_menu` (role_id,menu_id,created_by,create_dt, modified_by,modified_dt) VALUES (2, 563, NULL, NULL, NULL, NULL);
INSERT INTO `t_auth_role_menu` (role_id,menu_id,created_by,create_dt, modified_by,modified_dt) VALUES (2, 564, NULL, NULL, NULL, NULL);
INSERT INTO `t_auth_role_menu` (role_id,menu_id,created_by,create_dt, modified_by,modified_dt) VALUES (2, 565, NULL, NULL, NULL, NULL);

ALTER TABLE `tiangongjiali`.`o_order`
MODIFY COLUMN `voucher_url` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '订单凭证地址' AFTER `invoice_model`;