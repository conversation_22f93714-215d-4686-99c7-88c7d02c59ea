DROP TABLE IF EXISTS `tiangongjiali`.`f_financing_party_type`;
CREATE TABLE `tiangongjiali`.`f_financing_party_type` (
  `financing_party_type_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `financing_party_name` varchar(64) NOT NULL COMMENT '融资方名称',
  `deleted` tinyint(2) NULL DEFAULT 0 COMMENT '是否删除(0否 1是)',
  `created_dt` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `created_by` varchar(128) NULL DEFAULT NULL COMMENT '创建人',
  `updated_dt` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `updated_by` varchar(128) NULL DEFAULT NULL COMMENT '修改人',
  PRIMARY KEY (`financing_party_type_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '融资方类型表' ROW_FORMAT = Dynamic;

DROP TABLE IF EXISTS `tiangongjiali`.`f_financing_party_config_extend`;
CREATE TABLE `tiangongjiali`.`f_financing_party_config_extend` (
   `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
   `financing_party_type_id` bigint NOT NULL COMMENT '融资方类型id',
   `field_name` varchar(64) DEFAULT NULL COMMENT '字段名',
   `field_value` text DEFAULT NULL COMMENT '字段值',
   `deleted` tinyint(2) NULL DEFAULT 0 COMMENT '是否删除(0否 1是)',
   `created_dt` datetime NULL DEFAULT NULL COMMENT '创建时间',
   `created_by` varchar(128) NULL DEFAULT NULL COMMENT '创建人',
   `updated_dt` datetime NULL DEFAULT NULL COMMENT '修改时间',
   `updated_by` varchar(128) NULL DEFAULT NULL COMMENT '修改人',
   PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '融资方配置扩展表' ROW_FORMAT = Dynamic;
CREATE INDEX idx_financing_party_type_id ON `tiangongjiali`.`f_financing_party_config_extend` (financing_party_type_id);

DROP TABLE IF EXISTS `tiangongjiali`.`f_financing_config`;
CREATE TABLE `tiangongjiali`.`f_financing_config` (
  `financing_config_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `financing_party_type_id` bigint NOT NULL COMMENT '融资方类型id',
  `push_type`  tinyint(2) NOT NULL COMMENT '推送类型(1客户 2供应商)',
  `agent_code` varchar(32) DEFAULT NULL COMMENT '客户编码',
  `supply_code` varchar(32) DEFAULT NULL COMMENT '供应商编码',
  `exclude_supply_code` text DEFAULT NULL COMMENT '不包含供应商编码',
  `channel_code` varchar(128) NOT NULL COMMENT '渠道编码',
  `company_group_type` varchar(128) NOT NULL COMMENT '核心企业集团类型',
  `company_name` varchar(256) DEFAULT NULL COMMENT '核心企业名称',
  `unify_code` varchar(256) DEFAULT NULL COMMENT '核心企业社会信用代码',
  `order_source_code` varchar(32) NOT NULL COMMENT '订单来源编码(0直连 1差旅壹号 2胜意 3华为慧通)',
  `is_active` tinyint(2) NULL DEFAULT 1 COMMENT '是否有效(0否 1是)',
  `deleted` tinyint(2) NULL DEFAULT 0 COMMENT '是否删除(0否 1是)',
  `created_dt` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `created_by` varchar(128) NULL DEFAULT NULL COMMENT '创建人',
  `updated_dt` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `updated_by` varchar(128) NULL DEFAULT NULL COMMENT '修改人',
  PRIMARY KEY (`financing_config_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '融资信息配置表' ROW_FORMAT = Dynamic;
CREATE INDEX idx_financing_party_type_id ON `tiangongjiali`.`f_financing_config` (financing_party_type_id);

DROP TABLE IF EXISTS `tiangongjiali`.`f_financing_push_task`;
CREATE TABLE `tiangongjiali`.`f_financing_push_task` (
 `task_id` varchar(32) NOT NULL COMMENT '任务id',
 `financing_config_id` bigint NOT NULL COMMENT '融资方信息配置id',
 `code` varchar(32) NOT NULL COMMENT '编码（订单编码/账单编码）',
 `data_created_dt` datetime NULL DEFAULT NULL COMMENT '数据创建时间',
 `data_type` tinyint(2) NOT NULL COMMENT '数据类型（1离店订单 2金额变动订单 3账单）',
 `hotel_id` int(4) DEFAULT NULL COMMENT '酒店id',
 `hotel_name` varchar(256) DEFAULT NULL COMMENT '酒店名称',
 `check_in_date` date DEFAULT NULL COMMENT '入住日期',
 `check_out_date` date DEFAULT NULL COMMENT '离店日期',
 `room_qty` int(2) DEFAULT NULL COMMENT '间数',
 `night_qty` int(2) DEFAULT NULL COMMENT '晚数',
 `amount` decimal(14,2) DEFAULT NULL COMMENT '金额',
 `currency` int(4) DEFAULT NULL COMMENT '币种',
 `agent_code` varchar(32) DEFAULT NULL COMMENT '客户编码',
 `agent_name` varchar(128) DEFAULT NULL COMMENT '客户名称',
 `fail_count` int(2) DEFAULT 0 COMMENT '推送失败次数',
 `push_status` tinyint(2) DEFAULT NULL COMMENT '推送状态(0待推送 1推送中 2已推送 3推送失败)',
 `push_flag` tinyint(2) DEFAULT NULL COMMENT '推送标识(0未推送 1推送成功)',
 `created_dt` datetime NULL DEFAULT NULL COMMENT '创建时间',
 `created_by` varchar(128) NULL DEFAULT NULL COMMENT '创建人',
 `updated_dt` datetime NULL DEFAULT NULL COMMENT '修改时间',
 `updated_by` varchar(128) NULL DEFAULT NULL COMMENT '修改人',
 PRIMARY KEY (`task_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '融资信息推送任务表' ROW_FORMAT = Dynamic;
CREATE INDEX idx_financing_config_id ON `tiangongjiali`.`f_financing_push_task` (financing_config_id);

INSERT INTO `tiangongjiali`.`sequence` (`seq_name`, `current_val`, `increment_val`) VALUES ('seq_financing_code', 1000001, 1);

ALTER TABLE tiangongjiali.t_org_company_agent MODIFY COLUMN settlement_currency INT(4) DEFAULT NULL COMMENT '结算币种类型';
ALTER TABLE tiangongjiali.o_order_product_price MODIFY COLUMN currency INT(4) DEFAULT NULL COMMENT '币种';