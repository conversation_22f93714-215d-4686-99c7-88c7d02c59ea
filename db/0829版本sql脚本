DROP TABLE IF EXISTS `tiangongjiali`.`t_region_mainly_popularize_price`;
CREATE TABLE `tiangongjiali`.`t_region_mainly_popularize_price` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
	`region_name` varchar(128) NULL DEFAULT NULL COMMENT '区域名称',
	`region_code` varchar(128) NOT NULL COMMENT '区域编码',
	`region_type` int NOT NULL COMMENT '区域类型（1国家 3城市）',
	`start_price` decimal(12, 2) NOT NULL COMMENT '推荐最低价',
	`end_price` decimal(12, 2) NOT NULL COMMENT '推荐最高价',
  `created_dt` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `created_by` varchar(128) NULL DEFAULT NULL COMMENT '创建人',
	`updated_dt` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `updated_by` varchar(128) NULL DEFAULT NULL COMMENT '修改人',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '区域主推价格表' ROW_FORMAT = Dynamic;

ALTER TABLE tiangongjiali.t_hotel_avg_price add country_code VARCHAR(32) COMMENT '国家编码';

ALTER TABLE `tiangongjiali`.`o_order` MODIFY pay_at_hotel_currency int(2) COMMENT '到店另付费币种';
ALTER TABLE `tiangongjiali`.`o_order_product_price` MODIFY pay_at_hotel_currency int(2) COMMENT '到店另付费币种';