
INSERT INTO `tiangongjiali`.`sequence` (`seq_name`, `current_val`, `increment_val`) VALUES ('seq_settle_job_code', 1000101, 1);
INSERT INTO `tiangongjiali`.`sequence` (`seq_name`, `current_val`, `increment_val`) VALUES ('seq_settle_pay_code', 80000101, 1);


-- ----------------------------
-- Table structure for f_settle_hotel_config
-- ----------------------------
DROP TABLE IF EXISTS `f_settle_hotel_config`;
CREATE TABLE `f_settle_hotel_config` (
                                         `hid` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
                                         `merchant_code` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '商家编码 ,天宫商家编码',
                                         `supplier_code` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '供应商编码 天宫供应商编码',
                                         `supplier_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '供应商名称',
                                         `city_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '城市名称',
                                         `hotel_id` bigint NOT NULL COMMENT '酒店id 通过酒店名称检索到酒店id',
                                         `hotel_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '酒店名称',
                                         `ddt_merchant_code` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '订单通商家编码',
                                         `ddt_customer_code` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '订单通客户编码 订单通客户编码',
                                         `settle_time_type` tinyint(1) NOT NULL COMMENT '结算时间类型 [1:下单后 2:入住当日之后 3:离店当日之后 4:离店周结 5:离店半月结 6离店月结]',
                                         `is_check_detail` tinyint(1) NOT NULL COMMENT '是否审核入住明细 [0:否 1:是]',
                                         `invoice_limit` decimal(10,2) NOT NULL COMMENT '已收款待开票额度(自助结算金额上限) 自助结算金额上限',
                                         `account_num` varchar(64) COLLATE utf8mb4_bin NOT NULL COMMENT '酒店收款帐号 订单通收款账号，可为空,如果不为空,收款的时候必须匹配上，否则收款失败',
                                         `register_name` varchar(64) COLLATE utf8mb4_bin NOT NULL COMMENT '酒店收款账户 订单通收款账户，可为空,如果不为空,收款的时候必须匹配上，否则收款失败',
                                         `pub_payment_order_email_url` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '公付订单提醒邮箱 公付邮箱提醒',
                                         `is_sync` tinyint(1) NOT NULL COMMENT '是否同步订单联系人信息到ireve [0:不同步 1:同步]',
                                         `is_real_time` tinyint(1) NOT NULL COMMENT '是否立即到账 [0:不是 1:是]',
                                         `deleted` tinyint(1) NOT NULL COMMENT '删除状态',
                                         `created_by` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建人',
                                         `created_dt` datetime NOT NULL COMMENT '创建时间',
                                         `updated_by` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '更新人',
                                         `updated_dt` datetime NOT NULL COMMENT '更新时间',
                                         PRIMARY KEY (`hid`)
) ENGINE=InnoDB AUTO_INCREMENT=31 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='自助结算酒店配置表';

-- ----------------------------
-- Table structure for f_settle_hotel_config_attach
-- ----------------------------
DROP TABLE IF EXISTS `f_settle_hotel_config_attach`;
CREATE TABLE `f_settle_hotel_config_attach` (
                                                `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增长主键id',
                                                `hid` bigint NOT NULL COMMENT '自助结算配置id 外健id',
                                                `payer_account_id` bigint NOT NULL COMMENT '付款主体id 银行账号id值',
                                                `passage_type` tinyint(1) NOT NULL COMMENT '付款类型 8网商银行',
                                                `created_by` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建人',
                                                `created_dt` datetime NOT NULL COMMENT '创建时间',
                                                `updated_by` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '更新人',
                                                `updated_dt` datetime NOT NULL COMMENT '更新时间',
                                                PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=31 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='自助结算酒店配置附加项表';

-- ----------------------------
-- Table structure for f_settle_hotel_config_log
-- ----------------------------
DROP TABLE IF EXISTS `f_settle_hotel_config_log`;
CREATE TABLE `f_settle_hotel_config_log` (
                                             `log_id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志id',
                                             `supplier_code` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '供应商编码 天宫供应商编码',
                                             `hotel_id` bigint NOT NULL COMMENT '酒店id 通过酒店名称检索到酒店id',
                                             `ddt_customer_code` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '订单通客户编码 订单通客户编码',
                                             `content` text COLLATE utf8mb4_bin NOT NULL COMMENT '删除信息',
                                             `created_by` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建人 下单人',
                                             `created_dt` datetime NOT NULL COMMENT '创建时间',
                                             `updated_by` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '更新人',
                                             `updated_dt` datetime NOT NULL COMMENT '更新时间',
                                             PRIMARY KEY (`log_id`)
) ENGINE=InnoDB AUTO_INCREMENT=28 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='自助结算配置日志表';


-- ----------------------------
-- Table structure for f_settle_order
-- ----------------------------
DROP TABLE IF EXISTS `f_settle_order`;
CREATE TABLE `f_settle_order` (
                                  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增长主键id',
                                  `merchant_code` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '商家编码 天宫商家编码',
                                  `supplier_code` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '供应商编码 天宫供应商编码',
                                  `check_in_date` date NOT NULL COMMENT '入住日期',
                                  `check_out_date` date NOT NULL COMMENT '离店日期',
                                  `order_code` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '订单编码 天宫订单编码',
                                  `order_dt` datetime NOT NULL COMMENT '订单创建时间',
                                  `supply_order_code` varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT '供货单编码 天宫供货单编码',
                                  `tg_supply_order_dt` datetime NOT NULL COMMENT '天宫供货单创建时间 天宫供货单创建时间',
                                  `ddt_merchant_code` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '订单通商家编码',
                                  `ddt_customer_code` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '订单通客户编码 订单通客户编码',
                                  `ddt_order_code` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '订单通订单编码 订单通订单编码',
                                  `ddt_order_dt` datetime NOT NULL COMMENT '订单通订单创建时间 订单通订单创建时间',
                                  `settle_dt` datetime NOT NULL COMMENT '结算时间 结算时间',
                                  `settle_type` tinyint(1) NOT NULL COMMENT '结算时间控制类型 [1:下单后 2:入住当日之后 3:离店当日之后 4:离店周结 5:离店半月结 6:离店月结]',
                                  `payer_account_id` bigint NOT NULL COMMENT '付款主体id 银行账号id值',
                                  `pub_payment_order_email_url` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '公付邮箱地址',
                                  `send_status` tinyint(1) NOT NULL COMMENT '公付邮件发送状态 [0:无需发送 1:需要发送  2:发送成功 3:发送失败 4:首次失败 5:二次发送失败]',
                                  `send_dt` datetime DEFAULT NULL COMMENT '公付邮件发送时间',
                                  `created_by` varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT '创建人',
                                  `created_dt` datetime NOT NULL COMMENT '创建时间',
                                  `updated_by` varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT '更新人',
                                  `updated_dt` datetime NOT NULL COMMENT '更新时间',
                                  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=31 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='结算单表';


-- ----------------------------
-- Table structure for f_settle_service_job
-- ----------------------------
DROP TABLE IF EXISTS `f_settle_service_job`;
CREATE TABLE `f_settle_service_job` (
                                        `job_id` bigint NOT NULL AUTO_INCREMENT COMMENT '任务id',
                                        `hotel_id` bigint DEFAULT NULL COMMENT '酒店id',
                                        `hotel_name` varchar(255) COLLATE utf8mb4_bin NOT NULL COMMENT '酒店名称',
                                        `task_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '任务编码',
                                        `business_code` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '业务订单号 （订单编码/账单编码）',
                                        `transfer_status` tinyint(1) NOT NULL COMMENT '转账任务状态 [0:收(付)款中 1:收(付)款成功 2:收(付)款失败 3等待收(付)款]',
                                        `searial_no` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '支付凭证 支付流水号',
                                        `biz_no` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '商户业务流水号',
                                        `order_type` tinyint(1) NOT NULL COMMENT '订单类型 [0:单结结算 1:周期结结算]',
                                        `ddt_status` tinyint(1) NOT NULL COMMENT '订单通状态 [0:未处理 1:处理中 2处理成功]',
                                        `tg_bill_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '天宫账单编码 天宫账单编码',
                                        `tg_status` tinyint(1) NOT NULL COMMENT '天宫状态 [0:未处理 1:处理中 2:处理成功]',
                                        `progress_status` tinyint(1) NOT NULL COMMENT '任务状态 [0:未完成 1已完成]',
                                        `payment_amt` decimal(12,0) NOT NULL COMMENT '付款金额',
                                        `payment_dt` datetime NOT NULL COMMENT '付款时间',
                                        `hid` bigint NOT NULL COMMENT '配置id',
                                        `created_by` varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT '创建人 下单人',
                                        `created_dt` datetime NOT NULL COMMENT '创建时间 下单时间',
                                        `updated_by` varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT '更新人',
                                        `updated_dt` datetime NOT NULL COMMENT '更新时间',
                                        PRIMARY KEY (`job_id`)
) ENGINE=InnoDB AUTO_INCREMENT=40 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='付款任务表';


-- ----------------------------
-- Table structure for f_settle_service_job_log
-- ----------------------------
DROP TABLE IF EXISTS `f_settle_service_job_log`;
CREATE TABLE `f_settle_service_job_log` (
                                            `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增长主键id',
                                            `job_id` bigint NOT NULL COMMENT '付款任务id 付款任务id外健',
                                            `log_type` tinyint(1) NOT NULL COMMENT '日志类型 [0:网商银行支付 1:天宫工单处理 2:订单通工单处理]',
                                            `content` varchar(256) COLLATE utf8mb4_bin NOT NULL COMMENT '操作内容',
                                            `created_by` varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT '创建人',
                                            `created_dt` datetime NOT NULL COMMENT '创建时间',
                                            `updated_by` varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT '更新人',
                                            `updated_dt` datetime NOT NULL COMMENT '更新时间',
                                            PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=74 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='付款任务日志表';



-- ----------------------------
-- Table structure for f_settle_task
-- ----------------------------
DROP TABLE IF EXISTS `f_settle_task`;
CREATE TABLE `f_settle_task` (
                                 `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增长主键id',
                                 `order_id` bigint NOT NULL COMMENT '订单id',
                                 `supply_order_id` bigint NOT NULL COMMENT '供货单ID',
                                 `confirm_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '确认订单号',
                                 `task_type` tinyint(1) NOT NULL COMMENT '任务类型 [0:saas供货单结果【是】(下结算单到ireve) 1:ireve供货单结果【否】(取消ireve订单)]',
                                 `result_type` tinyint(1) NOT NULL COMMENT '完成类型 [0:未开始 1:进行中 2:已完成 3:失败]',
                                 `order_type` tinyint(1) NOT NULL COMMENT '订单类型 [0:saas订单 1:ireve订单]',
                                 `error_count` int DEFAULT NULL COMMENT '失败次数',
                                 `supply_result` tinyint(1) NOT NULL COMMENT '供货结果 [1:已确认 2:已取消]',
                                 `created_by` varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT '创建人',
                                 `created_dt` datetime NOT NULL COMMENT '创建时间',
                                 `updated_by` varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT '更新人',
                                 `updated_dt` datetime NOT NULL COMMENT '更新时间',
                                 `hid` bigint NOT NULL COMMENT '自助结算配置id',
                                 PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=32 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='结算单任务表';


-- ----------------------------
-- Table structure for f_sys_ddt_task
-- ----------------------------
DROP TABLE IF EXISTS `f_sys_ddt_task`;
CREATE TABLE `f_sys_ddt_task` (
                                  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
                                  `business_code` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '业务订单号 （订单编码/账单编码）',
                                  `task_type` tinyint(1) NOT NULL COMMENT '任务类型 [1:创建结算单, 2:取消订单, 3:工单处理, 4:支付失败同步]',
                                  `result_type` tinyint(1) NOT NULL COMMENT '完成类型 [0:未开始, 1:已完成, 2:失败]',
                                  `request` text COLLATE utf8mb4_bin NOT NULL COMMENT '请求参数',
                                  `created_by` varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT '创建人',
                                  `created_dt` datetime NOT NULL COMMENT '创建时间',
                                  `updated_by` varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT '更新人',
                                  `updated_dt` datetime NOT NULL COMMENT '更新时间',
                                  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=45 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='同步任务表';

-- ----------------------------
-- Table structure for f_year_workday
-- ----------------------------
DROP TABLE IF EXISTS `f_year_workday`;
CREATE TABLE `f_year_workday` (
                                  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增长主键id',
                                  `calendar` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '日期 例如：20231101',
                                  `year` varchar(16) COLLATE utf8mb4_bin NOT NULL COMMENT '年份 例如：2023',
                                  `year_day` int NOT NULL COMMENT '一年中第几天',
                                  `work_day` tinyint(1) NOT NULL COMMENT '是否是工作日 0-不是 1-是',
                                  `created_by` varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT '创建人',
                                  `created_dt` date NOT NULL COMMENT '创建时间',
                                  `updated_by` varchar(20) COLLATE utf8mb4_bin NOT NULL COMMENT '更新人',
                                  `updated_dt` date NOT NULL COMMENT '更新时间',
                                  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=367 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='工作日表';
