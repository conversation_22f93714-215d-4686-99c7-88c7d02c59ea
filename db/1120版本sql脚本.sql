DROP TABLE IF EXISTS `tiangongjiali`.`f_protocol_order_settlement_cost`;
CREATE TABLE `tiangongjiali`.`f_protocol_order_settlement_cost` (
    `id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
    `pay_serial_no` varchar(32) NOT NULL COMMENT '支付流水号',
    `swiping_card_amount` decimal(12, 2) NOT NULL COMMENT '刷卡金额',
    `swiping_card_amount_currency` int(2) NOT NULL COMMENT '刷卡金额币种',
    `swiping_card_rate` decimal(14, 8) NOT NULL COMMENT '刷卡汇率',
    `swiping_card_dt` datetime NOT NULL COMMENT '刷卡时间',
    `settle_amount` decimal(12, 2) NOT NULL COMMENT '结算金额',
    `settle_amount_currency` int(2) NOT NULL COMMENT '结算金额币种',
    `supply_order_code` varchar(32) DEFAULT NULL COMMENT '供货单编码',
    `order_code` varchar(32) DEFAULT NULL COMMENT '订单编码',
    `channel_order_code` varchar(256) DEFAULT NULL COMMENT '客户单号',
    `import_by` varchar(128) NULL DEFAULT NULL COMMENT '导入人',
    `import_dt` datetime NULL DEFAULT NULL COMMENT '导入时间',
    `matching_status` tinyint(2) DEFAULT NULL COMMENT '匹配状态(0新建 1匹配成功 2匹配失败)',
    `fail_desc` varchar(256) DEFAULT NULL COMMENT '失败描述',
    `push_status` tinyint(2) DEFAULT NULL COMMENT '推送状态(0未推送 1推送成功 2推送失败)',
    `agent_code` varchar(32) DEFAULT NULL COMMENT '客户编码',
    `deleted` tinyint(2) NULL DEFAULT 0 COMMENT '是否删除(0否 1是)',
    `created_dt` datetime NULL DEFAULT NULL COMMENT '创建时间',
    `created_by` varchar(128) NULL DEFAULT NULL COMMENT '创建人',
    `updated_dt` datetime NULL DEFAULT NULL COMMENT '修改时间',
    `updated_by` varchar(128) NULL DEFAULT NULL COMMENT '修改人',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '协议订单结算信息' ROW_FORMAT = Dynamic;

ALTER TABLE `tiangongjiali`.`t_org_company_agent` ADD COLUMN settlement_strategy tinyint(2) DEFAULT 0 COMMENT '结算策略(0按下单 1按结算成本)';
ALTER TABLE `tiangongjiali`.`t_org_company_agent` ADD COLUMN push_settle_cost_switch tinyint(2) DEFAULT NULL COMMENT '推送结算成本(0关 1开)';
ALTER TABLE `tiangongjiali`.`t_org_company_agent` ADD COLUMN push_settle_cost_url varchar(256) DEFAULT NULL COMMENT '推送结算成本地址';

ALTER TABLE `tiangongjiali`.`f_agent_statement` ADD COLUMN settlement_strategy tinyint(2) DEFAULT 0 COMMENT '结算策略(0按下单 1按结算成本)';
ALTER TABLE `tiangongjiali`.`f_agent_statement` ADD COLUMN settlement_cost_error tinyint(2) DEFAULT 0 COMMENT '结算成本异常标识(0成功 1异常)';
ALTER TABLE `tiangongjiali`.`f_agent_statement` ADD COLUMN repair_rate_difference tinyint(2) DEFAULT 0 COMMENT '补汇差状态 0新建 1待处理 2处理成功 3处理异常';

ALTER TABLE `tiangongjiali`.`f_agent_statement_order` ADD COLUMN swiping_card_amount decimal(12, 2) DEFAULT NULL COMMENT '刷卡金额';
ALTER TABLE `tiangongjiali`.`f_agent_statement_order` ADD COLUMN swiping_card_amount_currency int(2) DEFAULT NULL COMMENT '刷卡金额币种';
ALTER TABLE `tiangongjiali`.`f_agent_statement_order` ADD COLUMN swiping_card_rate decimal(14, 8) DEFAULT NULL COMMENT '刷卡汇率';
ALTER TABLE `tiangongjiali`.`f_agent_statement_order` ADD COLUMN swiping_card_dt datetime DEFAULT NULL COMMENT '刷卡时间';
ALTER TABLE `tiangongjiali`.`f_agent_statement_order` ADD COLUMN settle_amount decimal(12, 2) DEFAULT NULL COMMENT '结算金额';
ALTER TABLE `tiangongjiali`.`f_agent_statement_order` ADD COLUMN settle_amount_currency int(2) DEFAULT NULL COMMENT '结算金额币种';
ALTER TABLE `tiangongjiali`.`f_agent_statement_order` ADD COLUMN collection_rate decimal(14, 8) DEFAULT NULL COMMENT '收款汇率(结算金额与成本收款金额的汇率)';
ALTER TABLE `tiangongjiali`.`f_agent_statement_order` ADD COLUMN collection_amount decimal(12, 2) DEFAULT NULL COMMENT '成本收款金额';
ALTER TABLE `tiangongjiali`.`f_agent_statement_order` ADD COLUMN rate_difference_value decimal(12, 2) DEFAULT NULL COMMENT '汇差';
ALTER TABLE `tiangongjiali`.`f_agent_statement_order` ADD COLUMN settlement_cost_error_desc varchar(256) DEFAULT NULL COMMENT '结算成本错误描述';

INSERT INTO `tiangongjiali`.`t_auth_menu` (`menu_name`, `menu_code`, `menu_level`, `menu_rank`, `parent_code`, `back_end_url`, `type`, `active`, `created_by`, `created_dt`, `modified_by`, `modified_dt`, `front_end_url`, `open_type`) VALUES ('协议订单结算成本', 'M000580', 2, 12, 'M000004', NULL, 0, 1, 'system', SYSDATE(), NULL, NULL, '/orderSettlementCost', NULL);

INSERT INTO `tiangongjiali`.`t_auth_role_menu` (`role_id`, `menu_id`, `created_by`, `create_dt`, `modified_by`, `modified_dt`) VALUES (1, (select t.id from `tiangongjiali`.`t_auth_menu` t where t.menu_code = 'M000580'), NULL, NULL, NULL, NULL);
INSERT INTO `tiangongjiali`.`t_auth_role_menu` (`role_id`, `menu_id`, `created_by`, `create_dt`, `modified_by`, `modified_dt`) VALUES (2, (select t.id from `tiangongjiali`.`t_auth_menu` t where t.menu_code = 'M000580'), NULL, NULL, NULL, NULL);

ALTER TABLE tiangongjiali.t_org_supplier_label_config MODIFY COLUMN label_type int(2) DEFAULT NULL COMMENT '标签类型1协议 2平台自签 3团房 4固定协议价 5折扣协议价 6打包价';

CREATE INDEX idx_agent_id ON `tiangongjiali`.`t_org_supplier_label_config` (agent_id);
CREATE INDEX idx_supplier_id ON `tiangongjiali`.`t_org_supplier_label_config` (supplier_id);