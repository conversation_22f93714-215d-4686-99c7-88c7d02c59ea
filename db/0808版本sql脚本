DROP TABLE IF EXISTS `tiangongjiali`.`t_hotel_avg_price`;
CREATE TABLE `tiangongjiali`.`t_hotel_avg_price`  (
  `hotel_id` BIGINT NOT NULL COMMENT '酒店id',
	`avg_price` DECIMAL(12, 2) NOT NULL COMMENT '平均价',
	`city_code` varchar(32) NOT NULL COMMENT '城市编码',
  `created_dt` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `created_by` varchar(128) NULL DEFAULT NULL COMMENT '创建人',
	`updated_dt` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `updated_by` varchar(128) NULL DEFAULT NULL COMMENT '修改人',
	PRIMARY KEY (`hotel_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '酒店平均价表' ROW_FORMAT = Dynamic;
CREATE INDEX idx_city_code ON `tiangongjiali`.`t_hotel_avg_price` (city_code);

DROP TABLE IF EXISTS `tiangongjiali`.`t_city_avg_price`;
CREATE TABLE `tiangongjiali`.`t_city_avg_price`  (
  `city_code` varchar(32) NOT NULL COMMENT '城市编码',
	`avg_price` DECIMAL(12, 2) NOT NULL COMMENT '平均价',
	`calculate_date` date NOT NULL COMMENT '计算日期',
  `created_dt` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `created_by` varchar(128) NULL DEFAULT NULL COMMENT '创建人',
	`updated_dt` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `updated_by` varchar(128) NULL DEFAULT NULL COMMENT '修改人'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '城市平均价表' ROW_FORMAT = Dynamic;
CREATE INDEX idx_city_code ON `tiangongjiali`.`t_city_avg_price` (city_code);

DROP TABLE IF EXISTS `tiangongjiali`.`o_order_product_tip`;
CREATE TABLE `tiangongjiali`.`o_order_product_tip`  (
	`id` int NOT NULL AUTO_INCREMENT COMMENT '主键id',
	`order_code` varchar(32) NOT NULL COMMENT '订单编码',
	`product_id` varchar(128) NOT NULL COMMENT '产品id',
	`tip_type` tinyint NOT NULL COMMENT '提示信息类型（1：卖点信息 2：价格礼包 3：特别说明 4：提示信息 5：促销信息）',
	`title` varchar(128) DEFAULT NULL COMMENT '标题',
	`content` text DEFAULT NULL COMMENT '内容',
  `created_dt` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `created_by` varchar(128) NULL DEFAULT NULL COMMENT '创建人',
	`updated_dt` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `updated_by` varchar(128) NULL DEFAULT NULL COMMENT '修改人',
	PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单产品提示信息表' ROW_FORMAT = Dynamic;
CREATE INDEX idx_order_code ON `tiangongjiali`.`o_order_product_tip` (order_code);