

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for t_invoice
-- ----------------------------
DROP TABLE IF EXISTS `t_invoice`;
CREATE TABLE `t_invoice`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `type` tinyint NULL DEFAULT NULL COMMENT '1客户 2供应商 3公司维度',
  `org_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '供应商或客户编码',
  `company_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '公司名称',
  `taxpayer_identification_number` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '纳税人识别号',
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '地址',
  `phone` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '电话',
  `general_invoice_ratio` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '普票开票比例',
  `special_invoice_ratio` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '专票开票比例',
  `account_bank` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '开户银行',
  `account_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '银行账号',
  `created_by` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT '创建人',
  `created_dt` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `modified_by` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT '修改人',
  `modified_dt` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `deleted` tinyint NOT NULL DEFAULT 0 COMMENT '删除状态',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `inx_type`(`type`) USING BTREE,
  INDEX `inx_org_code`(`org_code`) USING BTREE,
  INDEX `inx_type_code`(`type`, `org_code`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- 银行卡流水记录表
DROP TABLE IF EXISTS `t_org_bank_log`;
CREATE TABLE `t_org_bank_log`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `bank_id` int NULL DEFAULT NULL,
  `change_type` int NULL DEFAULT NULL COMMENT '交易类型',
  `org_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '机构编码',
  `change_content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '交易描述',
  `income` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '收入',
  `expenditure` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '支出',
  `balance` decimal(10, 2) NULL DEFAULT NULL COMMENT '剩余额度',
  `change_date` datetime NULL DEFAULT NULL COMMENT '交易时间',
  `change_currency` int NULL DEFAULT NULL COMMENT '交易币种',
  `created_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `created_dt` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- 集团品牌表
-- ----------------------------
-- Table structure for t_baseinfo_group
-- ----------------------------
DROP TABLE IF EXISTS `t_baseinfo_group`;
CREATE TABLE `t_baseinfo_group`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `group_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '集团id',
  `group_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '集团名称',
  `brand_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '品牌id',
  `brand_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '品牌名称',
  `language` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '语言类型',
  `created_dt` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_language`(`language`) USING BTREE,
  INDEX `idx_group_id`(`group_id`) USING BTREE,
  INDEX `idx_brand_id`(`brand_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- 客户个人收付款银行卡表
-- ----------------------------
-- Table structure for t_org_agent_bank
-- ----------------------------
DROP TABLE IF EXISTS `t_org_agent_bank`;
CREATE TABLE `t_org_agent_bank`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `agent_id` int NOT NULL COMMENT '客户id',
  `bank_id` int NOT NULL COMMENT '银行卡id',
  `created_by` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT '创建人',
  `created_dt` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `modified_by` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT '修改人',
  `modified_dt` datetime NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_aid`(`agent_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- 供应商标签配置表
-- ----------------------------
-- Table structure for t_org_supplier_label_config
-- ----------------------------
DROP TABLE IF EXISTS `t_org_supplier_label_config`;
CREATE TABLE `t_org_supplier_label_config`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `agent_id` int NULL DEFAULT NULL COMMENT '客户id',
  `service_type` int NULL DEFAULT NULL COMMENT '业务类型1国内业务 2海外以为',
  `label_type` int NULL DEFAULT NULL COMMENT '标签类型1平台 2团房 3协议',
  `supplier_id` int NULL DEFAULT NULL COMMENT '供应商id',
  `created_by` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `created_dt` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `modified_by` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `modified_dt` datetime NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;


-- ----------------------------
-- Table structure for t_hotel_available
-- ----------------------------
DROP TABLE IF EXISTS `t_hotel_available`;
CREATE TABLE `t_hotel_available`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `hotel_id` int NULL DEFAULT NULL COMMENT '酒店id',
  `agent_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '客户编码',
  `available_type` tinyint NULL DEFAULT NULL COMMENT '0黑名单 1白名单',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_uni`(`hotel_id`, `agent_code`, `available_type`) USING BTREE,
  INDEX `idx_agent_code`(`agent_code`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

DROP TABLE IF EXISTS `t_org_msg`;
CREATE TABLE `t_org_msg`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `is_read` tinyint NULL DEFAULT NULL COMMENT '是否已读0否 1是',
  `msg_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '消息类型1在线支付结果通知 2公付订单提示通知',
  `msg_content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '消息内容',
  `created_dt` datetime NULL DEFAULT NULL COMMENT '通知时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;


SET FOREIGN_KEY_CHECKS = 1;


-- 新增供应商表字段
ALTER TABLE t_org_company_supplier ADD COLUMN supplier_form tinyint(2) COMMENT '供应商类别';
ALTER TABLE t_org_company_supplier ADD COLUMN quick_processing_switch tinyint(2) COMMENT '快速处理标签开关';
ALTER TABLE t_org_company_supplier ADD COLUMN hotel_id int COMMENT '快速处理标签开关';
ALTER TABLE t_org_company_supplier ADD COLUMN hotel_name varchar(255) COMMENT '快速处理标签开关';
ALTER TABLE t_org_company_supplier ADD COLUMN group_id int COMMENT '快速处理标签开关';
ALTER TABLE t_org_company_supplier ADD COLUMN group_name varchar(255) COMMENT '快速处理标签开关';

-- 新增银行卡表字段
ALTER TABLE t_org_bank ADD COLUMN account_type tinyint(2) COMMENT '账号类型：1企业账号 2个人账号';
ALTER TABLE t_org_bank ADD COLUMN bank_currency tinyint(2) COMMENT '银行币种';
ALTER TABLE t_org_bank ADD COLUMN company_invoice_id tinyint(2) COMMENT '公司发票主体id（t_invoice表）';
ALTER TABLE t_org_bank ADD COLUMN invoice_type tinyint(2) COMMENT '发票类型：1普票2专票';
ALTER TABLE t_org_bank ADD COLUMN invoice_ratio decimal(4,2) COMMENT '发票税点';
ALTER TABLE t_org_bank ADD COLUMN balance decimal(14,2) COMMENT '余额';

-- 新增合作商配置信息表字段
ALTER TABLE t_org_agent_api_config ADD COLUMN original_protocol_price_switch tinyint DEFAULT '1' COMMENT '原始协议价开关：0关 1开';
ALTER TABLE t_org_agent_api_config ADD COLUMN quote_switch tinyint DEFAULT '1' COMMENT '30天报价开关：0关 1开';
ALTER TABLE t_org_agent_api_config ADD COLUMN rating double(4,2) DEFAULT NULL COMMENT '评分';
ALTER TABLE t_org_agent_api_config ADD COLUMN data_encryption_switch tinyint DEFAULT '1' COMMENT '数据加密开关：0关 1开';
ALTER TABLE t_org_agent_api_config ADD COLUMN ops_secret_key varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'ops密钥';
ALTER TABLE t_org_agent_api_config ADD COLUMN customer_public_key varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户公钥';
ALTER TABLE t_org_agent_api_config ADD COLUMN tiangong_public_key varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '天宫公钥';
