package com.tiangong.dis.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.tiangong.common.Response;
import com.tiangong.dis.common.CommonUtils;
import com.tiangong.dis.convert.DisConvert;
import com.tiangong.dis.convert.OrderStatusMappingEnum;
import com.tiangong.dis.dto.QueryOrderInvoiceResponse;
import com.tiangong.dis.dto.ResponseResult;
import com.tiangong.dis.enums.DhubReturnCodeEnum;
import com.tiangong.dis.remote.OrderChangeWordOrderTaskRemote;
import com.tiangong.dis.service.HotelOrderService;
import com.tiangong.dto.common.CompanyDTO;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.dto.hotel.AddHotelAvailableReq;
import com.tiangong.dto.hotel.HotelAvailableDTO;
import com.tiangong.dto.order.CancelRestriction;
import com.tiangong.dto.order.CreditLineOrderDTO;
import com.tiangong.dto.order.OrderGuestDTO;
import com.tiangong.dto.order.OrderRoomGuestDTO;
import com.tiangong.dto.order.request.*;
import com.tiangong.dto.order.response.*;
import com.tiangong.dto.product.PriceInfoDetail;
import com.tiangong.dto.product.ProductMappingCache;
import com.tiangong.dto.product.request.ChildrenInfo;
import com.tiangong.dto.product.request.RoomGuestNumber;
import com.tiangong.enums.*;
import com.tiangong.exception.CustomException;
import com.tiangong.exception.SysException;
import com.tiangong.hotel.dto.BaseinfoRegionDTO;
import com.tiangong.hotel.remote.HotelRemote;
import com.tiangong.keys.RedisKey;
import com.tiangong.order.remote.BookingRemote;
import com.tiangong.order.remote.OrderQueryRemote;
import com.tiangong.order.remote.OrderRemote;
import com.tiangong.order.remote.SupplyOrderRemote;
import com.tiangong.order.remote.dto.OrderCheckDetailDTO;
import com.tiangong.order.remote.dto.OrderCheckInfoDTO;
import com.tiangong.order.remote.dto.PreBookDTO;
import com.tiangong.order.remote.request.*;
import com.tiangong.order.remote.response.*;
import com.tiangong.organization.remote.dto.AgentAccountConfig;
import com.tiangong.product.dto.ProductSalePriceDTO;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.util.CollUtilX;
import com.tiangong.util.DateUtilX;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;

import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toMap;

@Slf4j
@Service
public class HotelOrderServiceImpl implements HotelOrderService {

    @Autowired
    private BookingRemote bookingRemote;

    @Autowired
    private OrderRemote orderRemote;

    @Autowired
    private OrderQueryRemote orderQueryRemote;

    @Autowired
    private HotelRemote hotelRemote;

    @Autowired
    private OrderChangeWordOrderTaskRemote orderChangeWordOrderTaskRemote;

    @Autowired
    private SupplyOrderRemote supplyOrderRemote;

    @Override
    public ResponseResult<CheckBookingResponse> checkBooking(CheckBookingRequest request) {
        ResponseResult<CheckBookingResponse> checkBookingResponseResponse = new ResponseResult<>(DhubReturnCodeEnum.SUCCESS.no, DhubReturnCodeEnum.SUCCESS.description);

        //判断该酒店是否加入黑名单
        AddHotelAvailableReq req = new AddHotelAvailableReq();
        req.setAgentCode(request.getAgentCode());
        req.setHotelId(request.getHotelId());
        req.setAvailableType(0);
        Response<HotelAvailableDTO> hotelAvailableResp = hotelRemote.queryHotelAvailable(req);
        if (hotelAvailableResp.getResult() == ResultCodeEnum.SUCCESS.code) {
            HotelAvailableDTO model = hotelAvailableResp.getModel();
            if (model != null) {
                return new ResponseResult<>(DhubReturnCodeEnum.HOTEL_IS_NOT_AVAILABLE.no, DhubReturnCodeEnum.HOTEL_IS_NOT_AVAILABLE.code,DhubReturnCodeEnum.HOTEL_IS_NOT_AVAILABLE.description);
            }
        }

        CheckBookingResponse checkBookingResponse = new CheckBookingResponse();
        checkBookingResponse.setRatePlanId(request.getRatePlanId());
        String originalProductId = ProductMappingCache.getOriginalProductId(request.getRatePlanId());
        if (StrUtilX.isEmpty(originalProductId)) {
            checkBookingResponseResponse.setReturnCode(DhubReturnCodeEnum.INVALID_PRICEPLANID.no);
            checkBookingResponseResponse.setReturnCode(DhubReturnCodeEnum.INVALID_PRICEPLANID.code);
            checkBookingResponseResponse.setReturnMsg(DhubReturnCodeEnum.INVALID_PRICEPLANID.description);
            return checkBookingResponseResponse;
        }
        checkBookingResponse.setCanBook(0);
        checkBookingResponseResponse.setBussinessResponse(checkBookingResponse);

        PreBookDTO preBookDTO = convertPreBookDTO(request);
        //时间格式校验
        //this.checkCreateOrderParam(request);

        Response<Object> response = null;
        try {
            response = bookingRemote.checkPreBook(preBookDTO);
        } catch (SysException e) {
            log.error("试预订(可订检查)接口 SysException：", e);
            response = Response.error(e.getCode(), e.getMessage());
        } catch (CustomException e) {
            log.error("试预订(可订检查)接口 CustomException：", e);
            response = Response.error(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("试预订(可订检查)接口异常", e);
            response = Response.error(ErrorCodeEnum.UNKNOWN_EXCEPTION.errorCode, ErrorCodeEnum.UNKNOWN_EXCEPTION.errorDesc);
        }
        if (Objects.nonNull(response)) {
            passBookingResult(checkBookingResponseResponse, response, request);
        }
        return checkBookingResponseResponse;
    }

    /**
     * 校验创建订单参数
     */
    private void checkCreateOrderParam(CreateOrderRequest request, ResponseResult<CreateOrderResponse> createOrderResponseResponseResult) {
        // 校验入离日期
        try {
            // 入住日期
            Date checkInDate = DateUtilX.stringToDate(request.getCheckInDate());
            // 离店日期
            Date checkOutDate = DateUtilX.stringToDate(request.getCheckOutDate());

            // 校验入住日期、离店日期是否大于当前时间，离店日期是否大于入住日期
            CommonUtils.checkDateValidity(checkInDate, checkOutDate);

            // 每日日期
            List<Date> dateList = DateUtilX.getDateList(checkInDate, DateUtilX.getAddDay(checkOutDate, -1));

            // 价格日期
            Set<Date> priceDateSet = new HashSet<>();
            for (CreateOrderPriceItem priceItem : request.getPriceItems()) {
                Date date = DateUtilX.stringToDate(priceItem.getSaleDate());
                // 判断日期是否在入离日期范围内
                if (!dateList.contains(date)) {
                    createOrderResponseResponseResult.setReturnCode(DhubReturnCodeEnum.INVALID_DATE.no);
                    createOrderResponseResponseResult.setReturnCode(DhubReturnCodeEnum.INVALID_DATE.code);
                    createOrderResponseResponseResult.setReturnMsg(DhubReturnCodeEnum.INVALID_DATE.description);
                    return;
                    //throw new CustomException(ReturnCodeEnum.INVALID_DATE.no, ReturnCodeEnum.INVALID_DATE.description);
                }
                priceDateSet.add(date);
            }
            // 判断那些日期不存在
            for (Date date : dateList) {
                if (!priceDateSet.contains(date)) {
                    createOrderResponseResponseResult.setReturnCode(DhubReturnCodeEnum.NOT_EXIST_PRICE_DATE.no);
                    createOrderResponseResponseResult.setReturnCode(DhubReturnCodeEnum.NOT_EXIST_PRICE_DATE.code);
                    createOrderResponseResponseResult.setReturnMsg(DhubReturnCodeEnum.NOT_EXIST_PRICE_DATE.description);
                    return;
                    //throw new CustomException(ReturnCodeEnum.NOT_EXIST_PRICE_DATE.no, DateUtil.dateToString(date) + ReturnCodeEnum.NOT_EXIST_PRICE_DATE.description);
                }
            }
        } catch (Exception e) {
            createOrderResponseResponseResult.setReturnCode(DhubReturnCodeEnum.INVALID_DATE.no);
            createOrderResponseResponseResult.setReturnCode(DhubReturnCodeEnum.INVALID_DATE.code);
            createOrderResponseResponseResult.setReturnMsg(DhubReturnCodeEnum.INVALID_DATE.description);
            //throw new CustomException(ReturnCodeEnum.INVALID_DATE.no, ReturnCodeEnum.INVALID_DATE.description);
        }
    }

    @Override
    public ResponseResult<CreateOrderResponse> createOrder(CreateOrderRequest request) {

        //判断该酒店是否加入黑名单
        AddHotelAvailableReq req = new AddHotelAvailableReq();
        req.setAgentCode(request.getAgentCode());
        req.setHotelId(request.getHotelId());
        req.setAvailableType(0);
        Response<HotelAvailableDTO> hotelAvailableResp = hotelRemote.queryHotelAvailable(req);
        if (hotelAvailableResp.getResult() == ResultCodeEnum.SUCCESS.code) {
            HotelAvailableDTO model = hotelAvailableResp.getModel();
            if (model != null) {
                return new ResponseResult<>(DhubReturnCodeEnum.HOTEL_IS_NOT_AVAILABLE.no,DhubReturnCodeEnum.HOTEL_IS_NOT_AVAILABLE.code, DhubReturnCodeEnum.HOTEL_IS_NOT_AVAILABLE.description);
            }
        }

        //先参数校验
        SimpleDateFormat sdfTmp = new SimpleDateFormat("yyyy-MM-dd", Locale.CHINA);
        try {
            sdfTmp.parse(request.getCheckInDate());
            sdfTmp.parse(request.getCheckOutDate());
        } catch (Exception e) {
            throw new CustomException(DhubReturnCodeEnum.INVALID_DATE.no, DhubReturnCodeEnum.INVALID_DATE.code, DhubReturnCodeEnum.INVALID_DATE.description);
        }
        //钟点房数据离开日期一定是当天的下一天
        if (request.getHourlyRoom() == 1 && request.getHourlyRoomDetail() != null) {
            request.setCheckOutDate(DateUtilX.getNextDateStr(request.getCheckInDate()));
            //入住时间和小时房开始时间不一致
            if (!request.getCheckInDate().equals(request.getHourlyRoomDetail().getCheckInTime().substring(0, 10))) {
                throw new CustomException(DhubReturnCodeEnum.INVALID_DATE.no, DhubReturnCodeEnum.INVALID_DATE.code, DhubReturnCodeEnum.INVALID_DATE.description);
            }
            //小时房结束时间不对
            if (!request.getHourlyRoomDetail().getCheckOutTime().substring(0, 10).equals(request.getCheckInDate())
                    && !request.getHourlyRoomDetail().getCheckOutTime().substring(0, 10).equals(request.getCheckOutDate())
            ) {
                throw new CustomException(DhubReturnCodeEnum.INVALID_DATE.no, DhubReturnCodeEnum.INVALID_DATE.code, DhubReturnCodeEnum.INVALID_DATE.description);
            }
            try {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                if (sdf.parse(request.getHourlyRoomDetail().getCheckInTime()).before(new Date())) {
                    throw new CustomException(DhubReturnCodeEnum.INVALID_INPUTPARAM.no, DhubReturnCodeEnum.INVALID_INPUTPARAM.code, DhubReturnCodeEnum.INVALID_INPUTPARAM.description);
                }
                if (sdf.parse(request.getHourlyRoomDetail().getCheckOutTime()).before(sdf.parse(request.getHourlyRoomDetail().getCheckInTime()))) {
                    throw new CustomException(DhubReturnCodeEnum.INVALID_INPUTPARAM.no, DhubReturnCodeEnum.INVALID_INPUTPARAM.code, DhubReturnCodeEnum.INVALID_INPUTPARAM.description);
                }
                if (sdf.parse(request.getHourlyRoomDetail().getCheckOutTime()).getTime() - (sdf.parse(request.getHourlyRoomDetail().getCheckInTime())).getTime()
                        > 24 * 60 * 60 * 1000) {
                    throw new CustomException(DhubReturnCodeEnum.INVALID_INPUTPARAM.no, DhubReturnCodeEnum.INVALID_INPUTPARAM.code, DhubReturnCodeEnum.INVALID_INPUTPARAM.description);
                }
                sdf = new SimpleDateFormat("yyyy-MM-dd");
                if (sdf.parse(request.getCheckOutDate()).before(new Date())) {
                    throw new CustomException(DhubReturnCodeEnum.INVALID_DATE.no, DhubReturnCodeEnum.INVALID_DATE.code, DhubReturnCodeEnum.INVALID_DATE.description);
                }

            } catch (Exception e) {
                throw new CustomException(DhubReturnCodeEnum.INVALID_INPUTPARAM.no, DhubReturnCodeEnum.INVALID_INPUTPARAM.code, DhubReturnCodeEnum.INVALID_INPUTPARAM.description);
            }
        }
        ResponseResult<CreateOrderResponse> createOrderResponseResponseResult = new ResponseResult<>(DhubReturnCodeEnum.SUCCESS.no, DhubReturnCodeEnum.SUCCESS.description);
//        if (checkOrderCreationStatus(request, createOrderResponseResponseResult)) {
//            return createOrderResponseResponseResult;
//        }
        if (checkOrderAmount(request, createOrderResponseResponseResult)) {
            return createOrderResponseResponseResult;
        }
        //先试预定
        if (!checkPreBooking(request, createOrderResponseResponseResult)) {
            return createOrderResponseResponseResult;
        }
        CreateOrderResponse createOrderResponse = new CreateOrderResponse();
        createOrderResponse.setCoOrderCode(request.getCoOrderCode());
        createOrderResponse.setResult(2);
        createOrderResponseResponseResult.setBussinessResponse(createOrderResponse);
        AddManualOrderDTO addManualOrderDTO = convertOrderDTO(request);
        if (request.getHourlyRoom() == 1 && request.getHourlyRoomDetail() != null) {
            addManualOrderDTO.setHourlyTime(request.getHourlyRoomDetail().getCheckInTime() + "--" + request.getHourlyRoomDetail().getCheckOutTime());
        }
        if (StrUtilX.isNotEmpty(request.getArriveTime())) {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
                sdf.parse(request.getArriveTime());
                addManualOrderDTO.setArrivalTime(request.getArriveTime());
            } catch (Exception e) {
                log.error("创建订单异常", e);
                throw new CustomException(DhubReturnCodeEnum.INVALID_INPUTPARAM.no, DhubReturnCodeEnum.INVALID_INPUTPARAM.code, DhubReturnCodeEnum.INVALID_INPUTPARAM.description);
            }
        }
        Response<String> response = bookingRemote.addOTAOrder(addManualOrderDTO);
        if (Objects.nonNull(response)) {
            passCreateOrderResult(createOrderResponseResponseResult, response, request);
        }

        return createOrderResponseResponseResult;
    }

    @Override
    public ResponseResult<CancelOrderResponse> cancelOrder(CancelOrderRequest request) {
        ResponseResult<CancelOrderResponse> cancelOrderResponseResult = new ResponseResult<>(DhubReturnCodeEnum.SUCCESS.no, DhubReturnCodeEnum.SUCCESS.description);
        CancelOtaOrderDTO dto = new CancelOtaOrderDTO();
        dto.setOrderId(request.getFcOrderCode());
        dto.setChannelOrderCode(request.getCoOrderCode());
        dto.setCancelReason(request.getCancelReason());
        dto.setChannelCode(request.getChannelCode());
        dto.setRequestId(request.getRequestId());
        dto.setAgentCode(request.getAgentCode());
        Response<OtaOrderDTO> response = bookingRemote.cancelOtaOrder(dto);
        if (response.getResult() != 1) {
            if (Objects.equals(response.getFailCode(), ErrorCodeEnum.ORDER_NOT_EXISTS.errorCode)) {
                cancelOrderResponseResult.setReturnCode(DhubReturnCodeEnum.CANCEL_THEORDERUNEXIST.no);
                cancelOrderResponseResult.setReturnCode(DhubReturnCodeEnum.CANCEL_THEORDERUNEXIST.code);
                cancelOrderResponseResult.setReturnMsg(DhubReturnCodeEnum.CANCEL_THEORDERUNEXIST.description);
            } else {
                cancelOrderResponseResult.setReturnCode(response.getFailCode());
                cancelOrderResponseResult.setReturnMsg(response.getFailReason());
            }
        } else {
            CancelOrderResponse bussinessResponse = new CancelOrderResponse();
            OtaOrderDTO otaOrderDTO = JSON.parseObject(JSON.toJSONString(response.getModel()), OtaOrderDTO.class);
            bussinessResponse.setFcOrderCode(otaOrderDTO.getOrderCode());
            bussinessResponse.setCoOrderCode(otaOrderDTO.getChannelOrderCode());
            if (Objects.equals(CancelOrderEnum.CANCEL_SUCCESS.key, otaOrderDTO.getCancelStatus())) {
                bussinessResponse.setCancelResult(otaOrderDTO.getCancelStatus());
            } else if (Objects.equals(CancelOrderEnum.ASYNC_CANCEL.key, otaOrderDTO.getCancelStatus())) {
                bussinessResponse.setCancelResult(3);
            } else {
                bussinessResponse.setCancelResult(otaOrderDTO.getCancelStatus());
            }
            cancelOrderResponseResult.setBussinessResponse(bussinessResponse);
        }

        return cancelOrderResponseResult;
    }

    @Override
    public ResponseResult<OrderDetailResponse> queryAPIOrderDetail(OrderDetailRequest request) {
        ResponseResult<OrderDetailResponse> orderDetailResponseResponseResult = new ResponseResult<>(DhubReturnCodeEnum.SUCCESS.no, DhubReturnCodeEnum.SUCCESS.description);
        OrderCodeDTO orderCodeDTO = new OrderCodeDTO();
        orderCodeDTO.setOrderCode(request.getFcOrderCode());
        orderCodeDTO.setCoOrderCode(request.getCoOrderCode());
        orderCodeDTO.setRequestId(request.getRequestId());
        orderCodeDTO.setAgentCode(request.getAgentCode());
        orderCodeDTO.setChannelCode(request.getChannelCode());
        Response<OrderDTO> response = orderQueryRemote.queryOrderDetail(orderCodeDTO);
        if (response.getResult() != 1) {
            orderDetailResponseResponseResult.setReturnCode(response.getFailCode());
            orderDetailResponseResponseResult.setReturnMsg(response.getFailReason());
        } else {
            OrderDTO orderDTO = JSON.parseObject(JSON.toJSONString(response.getModel()), OrderDTO.class);
            OrderDetailResponse orderDetailResponse = JSON.parseObject(JSON.toJSONString(response.getModel()), OrderDetailResponse.class);
            convertToAPIOrder(orderDetailResponse, orderDTO);
            orderDetailResponseResponseResult.setBussinessResponse(orderDetailResponse);
            if (!response.getResult().equals(1)) {
                orderDetailResponseResponseResult.setReturnCode(response.getFailCode());
                orderDetailResponseResponseResult.setReturnMsg(response.getFailReason());
            }
        }
        return orderDetailResponseResponseResult;
    }

    @Override
    public Response<PaginationSupportDTO<OrderSimpleDTO>> queryOrderList(QueryOrderListDTO request) {
        return orderQueryRemote.queryOrderList(request);
    }

    /**
     * 订单详情查询-b2b
     */
    @Override
    public OrderDTO queryB2BOrderDetail(OrderDetailRequest request) {
        OrderCodeDTO orderCodeDTO = new OrderCodeDTO();
        orderCodeDTO.setOrderCode(request.getFcOrderCode());
        orderCodeDTO.setLanguage(request.getLanguage());
        orderCodeDTO.setRequestId(request.getRequestId());
        Response<OrderDTO> response = orderQueryRemote.queryOrderDetail(orderCodeDTO);
        return response.getModel();
    }

    @Override
    public ResponseResult<PayOrderResponse> payOrder(PayOrderRequest request) {
        ResponseResult<PayOrderResponse> payOrderResponseResponseResult = new ResponseResult<>(DhubReturnCodeEnum.SUCCESS.no, DhubReturnCodeEnum.SUCCESS.description);
        PayOrderResponse payOrderResponse = new PayOrderResponse();
        payOrderResponse.setCoOrderCode(request.getCoOrderCode());
        payOrderResponse.setFcOrderCode(request.getFcOrderCode());
        UpdatePayStatusAndDeductRefundCreditLineDTO updatePayStatusAndDeductRefundCreditLineDTO = new UpdatePayStatusAndDeductRefundCreditLineDTO();
        updatePayStatusAndDeductRefundCreditLineDTO.setOrderCode(request.getCoOrderCode());
        updatePayStatusAndDeductRefundCreditLineDTO.setFcOrderCode(request.getFcOrderCode());
        updatePayStatusAndDeductRefundCreditLineDTO.setAgentCode(request.getAgentCode());
        updatePayStatusAndDeductRefundCreditLineDTO.setPayAmount(request.getPayAmount());
        updatePayStatusAndDeductRefundCreditLineDTO.setChannelCode(request.getChannelCode());
        Response<CreditLineOrderDTO> response = bookingRemote.updatePayStatusAndDeductRefundCreditLineByOrderCode(updatePayStatusAndDeductRefundCreditLineDTO);
        if (response.isError()) {
            if (Objects.equals(response.getFailCode(), ErrorCodeEnum.ORDER_NOT_EXISTS.errorCode)) {
                return new ResponseResult<>(DhubReturnCodeEnum.CANCEL_THEORDERUNEXIST.no,DhubReturnCodeEnum.CANCEL_THEORDERUNEXIST.code, DhubReturnCodeEnum.CANCEL_THEORDERUNEXIST.description);
            } else {
                return new ResponseResult<>(response.getFailCode(), response.getFailReason());
            }
        }

        // 有支付方式或是混合支付，存储支付明细
        if (request.getIsMixedPay() != null || request.getPayMethod() != null) {
            AddOrderPayDTO addOrderPayDTO = new AddOrderPayDTO();
            addOrderPayDTO.setOrderCode(request.getCoOrderCode());
            addOrderPayDTO.setFcOrderCode(request.getFcOrderCode());
            addOrderPayDTO.setAgentCode(request.getAgentCode());
            addOrderPayDTO.setPayType(0);
            List<AddOrderPayDetailDTO> payDetailList = new ArrayList<>();

            // 混合支付
            if (request.getIsMixedPay() != null && request.getIsMixedPay() == 1) {
                for (MixedPayInfo mixedPayInfo : request.getMixedPayList()) {
                    AddOrderPayDetailDTO addOrderPayDetailDTO = new AddOrderPayDetailDTO();
                    addOrderPayDetailDTO.setOrderCode(request.getFcOrderCode());
                    addOrderPayDetailDTO.setPayType(0); // 付款类型 0-收
                    addOrderPayDetailDTO.setPartnerPayMethod(mixedPayInfo.getPayMethod());
                    // 1-企业支付 2-个人支付
                    if (mixedPayInfo.getPayMethod() == 2) {
                        addOrderPayDetailDTO.setPersonPayAmount(mixedPayInfo.getPayAmount());
                    } else {
                        addOrderPayDetailDTO.setCompanyPayAmount(mixedPayInfo.getPayAmount());
                    }
                    addOrderPayDetailDTO.setPaySerialNo(mixedPayInfo.getPaySerialNo());
                    addOrderPayDetailDTO.setPayAccount(mixedPayInfo.getPayAccount());
                    addOrderPayDetailDTO.setPartnerCode(request.getPartnerCode());
                    payDetailList.add(addOrderPayDetailDTO);
                }
                addOrderPayDTO.setIsMixedPay(1);
            } else if (request.getPayMethod() != null) {
                // 单个支付
                AddOrderPayDetailDTO addOrderPayDetailDTO = new AddOrderPayDetailDTO();
                addOrderPayDetailDTO.setOrderCode(request.getFcOrderCode());
                addOrderPayDetailDTO.setPayType(0); // 付款类型 0-收
                addOrderPayDetailDTO.setPartnerPayMethod(request.getPayMethod());
                // 1-企业支付 2-个人支付
                if (request.getPayMethod() == 2) {
                    addOrderPayDetailDTO.setPersonPayAmount(request.getPayAmount());
                } else {
                    addOrderPayDetailDTO.setCompanyPayAmount(request.getPayAmount());
                }
                addOrderPayDetailDTO.setPaySerialNo(request.getPaySerialNo());
                addOrderPayDetailDTO.setPayAccount(request.getPayAccount());
                addOrderPayDetailDTO.setPartnerCode(request.getPartnerCode());
                payDetailList.add(addOrderPayDetailDTO);
            }
            addOrderPayDTO.setPayDetailList(payDetailList);
            addOrderPayDTO.setChannelCode(request.getChannelCode());
            Response<Object> payDetailResponse = orderRemote.addOrderPayDetailList(addOrderPayDTO);
            if (payDetailResponse.isError()) {
                return new ResponseResult<>(response.getFailCode(), response.getFailReason());
            }
        }

        payOrderResponse.setPayStatus(1);
        payOrderResponse.setMessage("支付成功");
        try {
            CreditLineOrderDTO creditLineOrderDTO = response.getModel();
            if (null != creditLineOrderDTO) {
                payOrderResponse.setCoOrderCode(creditLineOrderDTO.getCoOrderCode());
                payOrderResponse.setFcOrderCode(creditLineOrderDTO.getFcOrderCode());
            }
        } catch (Exception e) {
            log.error("数据转换异常：{}", response);
        }
        //查询分销商--从缓存里面获取
        String agent = (String) RedisTemplateX.hashGet(RedisKey.AGENT_ACCOUNT_CONFIG, request.getAgentCode());
        AgentAccountConfig agentAccountConfig = JSON.parseObject(agent, AgentAccountConfig.class);
        if (Objects.nonNull(agentAccountConfig)) {
            payOrderResponse.setCreditBalance(agentAccountConfig.getBalance().setScale(2, RoundingMode.DOWN));
        }
        payOrderResponseResponseResult.setBussinessResponse(payOrderResponse);
        return payOrderResponseResponseResult;
    }

    @Override
    public ResponseResult<OrderRefundNoticeResponse> refundNoticeOrder(OrderRefundNoticeRequest request) {
        ResponseResult<OrderRefundNoticeResponse> refundNoticeOrderResult = new ResponseResult<>(DhubReturnCodeEnum.SUCCESS.no, DhubReturnCodeEnum.SUCCESS.description);
        OrderRefundNoticeResponse refundNoticeResponse = new OrderRefundNoticeResponse();

        // 新增支付明细
        if (request.getIsMixedRefund() != null || request.getRefundMethod() != null) {

            AddOrderPayDTO addOrderPayDTO = new AddOrderPayDTO();
            addOrderPayDTO.setOrderCode(request.getCoOrderCode());
            addOrderPayDTO.setFcOrderCode(request.getFcOrderCode());
            addOrderPayDTO.setAgentCode(request.getAgentCode());
            addOrderPayDTO.setPayType(1);
            List<AddOrderPayDetailDTO> payDetailList = new ArrayList<>();

            // 混合支付
            if (request.getIsMixedRefund() != null && request.getIsMixedRefund() == 1) {
                for (MixedRefundInfo mixedRefundInfo : request.getMixedRefundList()) {
                    AddOrderPayDetailDTO addOrderPayDetailDTO = new AddOrderPayDetailDTO();
                    addOrderPayDetailDTO.setOrderCode(request.getFcOrderCode());
                    addOrderPayDetailDTO.setPayType(1); // 付款类型 1-付
                    addOrderPayDetailDTO.setPartnerPayMethod(mixedRefundInfo.getRefundMethod());
                    // 1-企业支付 2-个人支付
                    if (mixedRefundInfo.getRefundMethod() == 2) {
                        addOrderPayDetailDTO.setPersonPayAmount(mixedRefundInfo.getRefundAmount());
                    } else {
                        addOrderPayDetailDTO.setCompanyPayAmount(mixedRefundInfo.getRefundAmount());
                    }
                    addOrderPayDetailDTO.setPaySerialNo(mixedRefundInfo.getRefundSerialNo());
                    addOrderPayDetailDTO.setPartnerCode(request.getPartnerCode());
                    payDetailList.add(addOrderPayDetailDTO);
                }
            } else if (request.getRefundMethod() != null) {
                // 单个支付
                AddOrderPayDetailDTO addOrderPayDetailDTO = new AddOrderPayDetailDTO();
                addOrderPayDetailDTO.setOrderCode(request.getFcOrderCode());
                addOrderPayDetailDTO.setPayType(1); // 付款类型 1-付
                addOrderPayDetailDTO.setPartnerPayMethod(request.getRefundMethod());
                // 1-企业支付 2-个人支付
                if (request.getRefundMethod() == 2) {
                    addOrderPayDetailDTO.setPersonPayAmount(request.getRefundAmount());
                } else {
                    addOrderPayDetailDTO.setCompanyPayAmount(request.getRefundAmount());
                }
                addOrderPayDetailDTO.setPaySerialNo(request.getRefundSerialNo());
                addOrderPayDetailDTO.setPartnerCode(request.getPartnerCode());
                payDetailList.add(addOrderPayDetailDTO);
            }
            addOrderPayDTO.setPayDetailList(payDetailList);
            addOrderPayDTO.setChannelCode(request.getChannelCode());
            Response<Object> response = orderRemote.addOrderPayDetailList(addOrderPayDTO);
            if (response.isError()) {
                if (Objects.equals(response.getFailCode(), ErrorCodeEnum.ORDER_NOT_EXISTS.errorCode)) {
                    return new ResponseResult<>(DhubReturnCodeEnum.CANCEL_THEORDERUNEXIST.no, DhubReturnCodeEnum.CANCEL_THEORDERUNEXIST.code, DhubReturnCodeEnum.CANCEL_THEORDERUNEXIST.description);
                } else {
                    return new ResponseResult<>(response.getFailCode(), response.getFailReason());
                }
            }
        }
        refundNoticeResponse.setRefundStatus(1);
        refundNoticeResponse.setMessage("退款成功");
        refundNoticeOrderResult.setBussinessResponse(refundNoticeResponse);
        return refundNoticeOrderResult;
    }

    @Override
    public ResponseResult<CreditBalanceResponse> queryCreditBalance(QueryCreditBalanceRequest request) {
        ResponseResult<CreditBalanceResponse> creditBalanceResponseResponseResult = new ResponseResult<>(DhubReturnCodeEnum.SUCCESS.no, DhubReturnCodeEnum.SUCCESS.description);
        CreditBalanceResponse creditBalanceResponse = new CreditBalanceResponse();
        //查询分销商--从缓存里面获取
        String agent = (String) RedisTemplateX.hashGet(RedisKey.AGENT_ACCOUNT_CONFIG, request.getAgentCode());
        AgentAccountConfig agentAccountConfig = JSON.parseObject(agent, AgentAccountConfig.class);
        if (Objects.nonNull(agentAccountConfig)) {
            creditBalanceResponse.setCreditBalance(agentAccountConfig.getBalance());
        }
        creditBalanceResponseResponseResult.setBussinessResponse(creditBalanceResponse);
        return creditBalanceResponseResponseResult;
    }

    @Override
    public ResponseResult<CreateInvoiceResponse> createInvoice(CreateInvoiceRequest request) {
        ResponseResult<CreateInvoiceResponse> createInvoiceResponseResponseResult = new ResponseResult<>(DhubReturnCodeEnum.SUCCESS.no, DhubReturnCodeEnum.SUCCESS.description);
        try {
            //校验区号
            Response<List<BaseinfoRegionDTO>> region = hotelRemote.getRegion();
            List<String> codeList = region.getModel().stream().map(BaseinfoRegionDTO::getCountryCode).collect(toList());
            if (StrUtil.isNotEmpty(request.getTelephoneAreaCode())) {
                if (!codeList.contains(request.getTelephoneAreaCode())) {
                    createInvoiceResponseResponseResult.setReturnMsg(ErrorCodeEnum.INVOICE_CONTACT_AREA_CODE_NOT_EXIST.getErrorDesc());
                    createInvoiceResponseResponseResult.setReturnCode(ErrorCodeEnum.INVOICE_CONTACT_AREA_CODE_NOT_EXIST.errorNo);
                    createInvoiceResponseResponseResult.setFailCode(ErrorCodeEnum.INVOICE_CONTACT_AREA_CODE_NOT_EXIST.errorCode);
                    return createInvoiceResponseResponseResult;
                }
            }
            if (StrUtil.isNotEmpty(request.getCompanyTelAreaCode())) {
                if (!codeList.contains(request.getCompanyTelAreaCode())) {
                    createInvoiceResponseResponseResult.setReturnMsg(ErrorCodeEnum.INVOICE_COMPANY_AREA_CODE_NOT_EXIST.getErrorDesc());
                    createInvoiceResponseResponseResult.setReturnCode(ErrorCodeEnum.INVOICE_COMPANY_AREA_CODE_NOT_EXIST.errorNo);
                    createInvoiceResponseResponseResult.setFailCode(ErrorCodeEnum.INVOICE_COMPANY_AREA_CODE_NOT_EXIST.errorCode);
                    return createInvoiceResponseResponseResult;
                }
            }
            OrderInvoiceDTO orderInvoiceDTO = convertInvoice(request);
            if (Objects.nonNull(orderInvoiceDTO)) {
                Response<OrderInvoiceDTO> response = orderRemote.applyOrderInvoice(orderInvoiceDTO);
                if (Objects.nonNull(response) && !response.isSuccess()) {
                    log.info("发票申请失败{}", response);
                    createInvoiceResponseResponseResult.setReturnMsg(response.getFailReason());
                    createInvoiceResponseResponseResult.setReturnCode(response.getFailCode());
                    return createInvoiceResponseResponseResult;
                }
                OrderInvoiceDTO model = response.getModel();
                CreateInvoiceResponse createInvoiceResponse = new CreateInvoiceResponse();
                createInvoiceResponse.setInvoiceId(String.valueOf(model.getId()));
                createInvoiceResponse.setApplyId(request.getApplyId());
                createInvoiceResponse.setSortNum(1);
                createInvoiceResponseResponseResult.setBussinessResponse(createInvoiceResponse);
                return createInvoiceResponseResponseResult;
            }

        } catch (Exception e) {
            createInvoiceResponseResponseResult.setFailCode(ErrorCodeEnum.UNKNOWN_EXCEPTION.errorCode);
            createInvoiceResponseResponseResult.setReturnCode(ErrorCodeEnum.UNKNOWN_EXCEPTION.errorCode);
            createInvoiceResponseResponseResult.setReturnMsg(e.getMessage());
            log.error("dhub发票申请异常,requestId:" + request.getRequestId(), e);
        }
        return createInvoiceResponseResponseResult;
    }

    @Override
    public ResponseResult<QueryOrderInvoiceResponse> queryOrderInvoice(QueryOrderInvoiceDTO request) {
        log.info("根据订单号查询发票详情，参数：{}", request);

        // 参数校验
        if (request == null || StringUtils.isEmpty(request.getOrderCode())) {
            log.error("查询发票详情失败：订单号为空");
            throw new SysException("订单号不能为空");
        }

        // 构建查询参数
        QueryOrderInvoiceDTO queryOrderInvoiceDTO = new QueryOrderInvoiceDTO();
        queryOrderInvoiceDTO.setOrderCode(request.getOrderCode());

        // 调用订单服务查询发票信息
        Response<OrderInvoiceDTO> response = orderQueryRemote.queryOrderInvoice(queryOrderInvoiceDTO);

        // 处理响应结果
        if (response == null || !response.isSuccess()) {
            String errorMsg = response != null ? response.getFailReason() : "查询发票详情失败";
            log.error("查询发票详情失败：{}", errorMsg);
            throw new SysException(errorMsg);
        }

        // 转换响应结果
        OrderInvoiceDTO orderInvoiceDTO = response.getModel();
        QueryOrderInvoiceResponse result = new QueryOrderInvoiceResponse();

        if (orderInvoiceDTO != null) {
            // 发票存在
            result.setId(orderInvoiceDTO.getId());
            result.setOrderCode(orderInvoiceDTO.getOrderCode());
            result.setInvoiceStatus(orderInvoiceDTO.getInvoiceStatus());
            result.setInvoiceDate(orderInvoiceDTO.getInvoiceDate());
            result.setInvoiceAmount(orderInvoiceDTO.getInvoiceAmount());
            result.setInvoiceType(orderInvoiceDTO.getInvoiceType());
            result.setInvoiceName(orderInvoiceDTO.getInvoiceName());
            result.setInvoiceTitle(orderInvoiceDTO.getInvoiceTitle());
            result.setTaxNumber(orderInvoiceDTO.getTaxNumber());
            result.setInvoiceNo(orderInvoiceDTO.getInvoiceNo());
        }
        return new ResponseResult<>(DhubReturnCodeEnum.SUCCESS.no, DhubReturnCodeEnum.SUCCESS.description, result);
    }

    @Override
    public ResponseResult<OrderCheckDetailInfoResponse> queryOrderCheckDetailInfo(OrderCheckInfoRequest request) {
        ResponseResult<OrderCheckDetailInfoResponse> orderCheckDetailInfoResponseResponseResult = new ResponseResult<>(DhubReturnCodeEnum.SUCCESS.no, DhubReturnCodeEnum.SUCCESS.description);
        OrderCheckDetailInfoResponse orderCheckDetailInfoResponse = new OrderCheckDetailInfoResponse();
        orderCheckDetailInfoResponseResponseResult.setBussinessResponse(orderCheckDetailInfoResponse);
        Response<List<OrderCheckInfoDTO>> response = orderRemote.queryOrderCheckDetailInfoDhub(request);
        if (response.isError()) {
            if (Objects.equals(response.getFailCode(), ErrorCodeEnum.ORDER_NOT_EXISTS.errorCode)) {
                return new ResponseResult<>(DhubReturnCodeEnum.CANCEL_THEORDERUNEXIST.no,DhubReturnCodeEnum.CANCEL_THEORDERUNEXIST.code, DhubReturnCodeEnum.CANCEL_THEORDERUNEXIST.description);
            } else {
                return new ResponseResult<>(response.getFailCode(), response.getFailReason());
            }
        }
        passOrderCheckDetailResult(orderCheckDetailInfoResponseResponseResult, response);

        return orderCheckDetailInfoResponseResponseResult;

    }

    @Override
    public Response<OrderStatisticsDTOB2BResponse> queryOrderStatisticsB2B(QueryOrderStatisticsDTOB2BRequest request) {
        return orderQueryRemote.queryOrderStatisticsB2B(request);
    }

    @Override
    public ResponseResult<CheckoutApplyInfoResponse> checkoutApply(CheckoutApplyInfoRequest request) {
        return orderChangeWordOrderTaskRemote.addOrderChangeWordOrderTask(request);
    }

    @Override
    public ResponseResult<CheckoutDetailInfoResponse> queryCheckoutDetail(CheckoutDetailInfoRequest request) {
        return orderChangeWordOrderTaskRemote.queryCheckoutApplyDetail(request);
    }

    @Override
    public Response<SupplyOrderDTO> querySupplyOrderInfo(SupplyOrderIdDTO request) {
        return supplyOrderRemote.querySupplyOrderInfo(request);
    }

    /**
     * 组装预定返回信息
     */
    private void passBookingResult(ResponseResult<CheckBookingResponse> checkBookingResponseResponse, Response<Object> response, CheckBookingRequest request) {
        if (response.isSuccess()) {
            CheckBookingResponse checkBookingResponse = checkBookingResponseResponse.getBussinessResponse();
            if (Objects.nonNull(response.getModel())) {
                ProductSalePriceDTO productSalePriceDTO = JSON.parseObject(JSONUtil.toJsonStr(response.getModel()), new TypeReference<ProductSalePriceDTO>() {
                });
                List<CancelRestriction> cancelRestrictions = productSalePriceDTO.getCancelRestrictions();
                if (CollUtilX.isNotEmpty(cancelRestrictions)) {
                    List<CancelRestriction> list = cancelRestrictions.stream().map(e -> {
                                CancelRestriction cancelRestriction = new CancelRestriction();
                                cancelRestriction.setCancelRestrictionType(e.getCancelRestrictionType());
                                cancelRestriction.setCancelRestrictionDay(e.getCancelRestrictionDay());
                                cancelRestriction.setCancelRestrictionTime(e.getCancelRestrictionTime());
                                cancelRestriction.setCancellationPrice(e.getCancellationPrice());
                                cancelRestriction.setCancelRestrictionDate(e.getCancelRestrictionDate());
                                return cancelRestriction;
                            }

                    ).collect(toList());
                    checkBookingResponse.setCancelRestrictions(list);
                }
                checkBookingResponse.setTotalSalePrice(productSalePriceDTO.getTotalSalePrice());
                checkBookingResponse.setPriceItems(productSalePriceDTO.getPriceList());
                checkBookingResponse.setCanBook(productSalePriceDTO.getCanBook());
                checkBookingResponse.setOrderRoomDetails(productSalePriceDTO.getRoomItemDetails());
                checkBookingResponse.setTaxDetailDto(productSalePriceDTO.getTaxDetail());
                checkBookingResponse.setHourlyRoom(productSalePriceDTO.getHourlyRoom());
                checkBookingResponse.setHourlyRoomInfo(productSalePriceDTO.getHourlyRoomInfo());
                checkBookingResponse.setInvoiceModel(productSalePriceDTO.getInvoiceModel());
                checkBookingResponse.setInvoiceType(productSalePriceDTO.getInvoiceType());
                checkBookingResponse.setGuaranteeType(productSalePriceDTO.getGuaranteeType());
                checkBookingResponse.setGuaranteeFeeType(productSalePriceDTO.getGuaranteeFeeType());
                checkBookingResponse.setGuaranteeCondition(productSalePriceDTO.getGuaranteeCondition());
                checkBookingResponse.setGuaranteeDesc(productSalePriceDTO.getGuaranteeDesc());

                //校验配额
                if (productSalePriceDTO.getPriceList() != null) {
                    for (PriceInfoDetail priceInfoDetail : productSalePriceDTO.getPriceList()) {
                        if (priceInfoDetail.getRoomStatus() != null
                                && priceInfoDetail.getRoomStatus() == RoomStateEnum.HAVA_ROOM.key
                                && (priceInfoDetail.getCanOverDraft() == null || priceInfoDetail.getCanOverDraft() == 0)
                                && priceInfoDetail.getQuotaNum() < request.getRoomNum()) {
                            response.setResult(ResultCodeEnum.FAILURE.code);
                            checkBookingResponseResponse.setReturnCode(DhubReturnCodeEnum.DISSATISFY_QUOTA.no);
                            checkBookingResponseResponse.setFailCode(DhubReturnCodeEnum.DISSATISFY_QUOTA.code);
                            checkBookingResponseResponse.setReturnMsg(priceInfoDetail.getDate() + "," + DhubReturnCodeEnum.DISSATISFY_QUOTA.description);
                            return;
                        }
                    }
                }
            }
        } else {
            if (Objects.equals(response.getFailCode(), ErrorCodeEnum.PRODUCT_DISABLE.errorCode)) {
                checkBookingResponseResponse.setReturnCode(DhubReturnCodeEnum.INVALID_SALESTATE.no);
                checkBookingResponseResponse.setFailCode(DhubReturnCodeEnum.INVALID_SALESTATE.code);
                checkBookingResponseResponse.setReturnMsg(DhubReturnCodeEnum.INVALID_SALESTATE.description);
            } else if (Objects.equals(response.getFailCode(), ErrorCodeEnum.ERROR_RESTRICT.errorCode)) {
                checkBookingResponseResponse.setReturnCode(DhubReturnCodeEnum.DISSATISFY_BOOKINGCLAUSE.no);
                checkBookingResponseResponse.setFailCode(DhubReturnCodeEnum.DISSATISFY_BOOKINGCLAUSE.code);
                checkBookingResponseResponse.setReturnMsg(DhubReturnCodeEnum.DISSATISFY_BOOKINGCLAUSE.description);
            } else if (Objects.equals(response.getFailCode(), ErrorCodeEnum.NO_SALE_PRICE.errorCode)) {
                checkBookingResponseResponse.setReturnCode(DhubReturnCodeEnum.INVALID_PRICE.no);
                checkBookingResponseResponse.setFailCode(DhubReturnCodeEnum.INVALID_PRICE.code);
                checkBookingResponseResponse.setReturnMsg(DhubReturnCodeEnum.INVALID_PRICE.description);
            } else if (Objects.equals(response.getFailCode(), ErrorCodeEnum.INSUFFICIENT_AMOUNT.errorCode)) {
                checkBookingResponseResponse.setReturnCode(DhubReturnCodeEnum.CREDIT_BALANCE_NOT_ENOUGH.no);
                checkBookingResponseResponse.setFailCode(DhubReturnCodeEnum.CREDIT_BALANCE_NOT_ENOUGH.code);
                checkBookingResponseResponse.setReturnMsg(DhubReturnCodeEnum.CREDIT_BALANCE_NOT_ENOUGH.description);
            } else if (Objects.equals(response.getFailCode(), ErrorCodeEnum.QUOTA_ENOUGH.errorCode)) {
                checkBookingResponseResponse.setReturnCode(DhubReturnCodeEnum.DISSATISFY_QUOTA.no);
                checkBookingResponseResponse.setFailCode(DhubReturnCodeEnum.DISSATISFY_QUOTA.code);
                checkBookingResponseResponse.setReturnMsg(DhubReturnCodeEnum.DISSATISFY_QUOTA.description);
            } else if (Objects.equals(response.getFailCode(), ErrorCodeEnum.CLOSE_ROOM.errorCode)) {
                checkBookingResponseResponse.setReturnCode(DhubReturnCodeEnum.FULL_ROOM.no);
                checkBookingResponseResponse.setFailCode(DhubReturnCodeEnum.FULL_ROOM.code);
                checkBookingResponseResponse.setReturnMsg(DhubReturnCodeEnum.FULL_ROOM.description);
            } else if (Objects.equals(response.getFailCode(), ErrorCodeEnum.PRICE_NOT_ACCURATE.errorCode)) {
                checkBookingResponseResponse.setReturnCode(DhubReturnCodeEnum.INVALID_PRICE.no);
                checkBookingResponseResponse.setFailCode(DhubReturnCodeEnum.INVALID_PRICE.code);
                checkBookingResponseResponse.setReturnMsg(DhubReturnCodeEnum.INVALID_PRICE.description);
            } else if (Objects.equals(response.getFailCode(), ErrorCodeEnum.PRICE_AND_QUOTA_ERROR.errorCode)) {
                checkBookingResponseResponse.setReturnCode(DhubReturnCodeEnum.INVALID_PRICE.no);
                checkBookingResponseResponse.setFailCode(DhubReturnCodeEnum.INVALID_PRICE.code);
                checkBookingResponseResponse.setReturnMsg(DhubReturnCodeEnum.INVALID_PRICE.description);
            } else if (Objects.equals(response.getFailCode(), ErrorCodeEnum.SUPPLY_RETURN_RESULT_FAIL.errorCode)) {
                checkBookingResponseResponse.setReturnCode(DhubReturnCodeEnum.SUPPLY_NOHAVE_RESULT_INFO.no);
                checkBookingResponseResponse.setFailCode(DhubReturnCodeEnum.SUPPLY_NOHAVE_RESULT_INFO.code);
                checkBookingResponseResponse.setReturnMsg(DhubReturnCodeEnum.SUPPLY_NOHAVE_RESULT_INFO.description);
            } else if (Objects.equals(response.getFailCode(), ErrorCodeEnum.DISSATISFY_QUOTA.errorCode)) {
                checkBookingResponseResponse.setReturnCode(DhubReturnCodeEnum.DISSATISFY_QUOTA.no);
                checkBookingResponseResponse.setFailCode(DhubReturnCodeEnum.DISSATISFY_QUOTA.code);
                checkBookingResponseResponse.setReturnMsg(DhubReturnCodeEnum.DISSATISFY_QUOTA.description);
            } else if (Objects.equals(response.getFailCode(), ErrorCodeEnum.DISSATISFY_BOOKROOMSCLAUSE.errorCode)) {
                checkBookingResponseResponse.setReturnCode(DhubReturnCodeEnum.DISSATISFY_BOOKROOMSCLAUSE.no);
                checkBookingResponseResponse.setFailCode(DhubReturnCodeEnum.DISSATISFY_BOOKROOMSCLAUSE.code);
                checkBookingResponseResponse.setReturnMsg(DhubReturnCodeEnum.DISSATISFY_BOOKROOMSCLAUSE.description);
            } else if (Objects.equals(response.getFailCode(), ErrorCodeEnum.PRICE_NEED_QUERY.errorCode)) {
                checkBookingResponseResponse.setReturnCode(DhubReturnCodeEnum.PRICE_NEED_QUERY.no);
                checkBookingResponseResponse.setFailCode(DhubReturnCodeEnum.PRICE_NEED_QUERY.code);
                checkBookingResponseResponse.setReturnMsg(DhubReturnCodeEnum.PRICE_NEED_QUERY.description);
            } else if (Objects.equals(response.getFailCode(), ErrorCodeEnum.FULL_ROOM.errorCode)) {
                checkBookingResponseResponse.setReturnCode(DhubReturnCodeEnum.FULL_ROOM.no);
                checkBookingResponseResponse.setFailCode(DhubReturnCodeEnum.FULL_ROOM.code);
                checkBookingResponseResponse.setReturnMsg(DhubReturnCodeEnum.FULL_ROOM.description);
            } else if (Objects.equals(response.getFailCode(), ErrorCodeEnum.CONNECT_TIME_OUT.errorCode)) {
                checkBookingResponseResponse.setReturnCode(DhubReturnCodeEnum.CONNECT_TIME_OUT.no);
                checkBookingResponseResponse.setFailCode(DhubReturnCodeEnum.CONNECT_TIME_OUT.code);
                checkBookingResponseResponse.setReturnMsg(DhubReturnCodeEnum.CONNECT_TIME_OUT.description);
            } else if (Objects.equals(response.getFailCode(), ErrorCodeEnum.NOMATCH_SUPPLYANDMECHANT.errorCode)) {
                checkBookingResponseResponse.setReturnCode(DhubReturnCodeEnum.NOMATCH_SUPPLYANDMECHANT.no);
                checkBookingResponseResponse.setFailCode(DhubReturnCodeEnum.NOMATCH_SUPPLYANDMECHANT.code);
                checkBookingResponseResponse.setReturnMsg(DhubReturnCodeEnum.NOMATCH_SUPPLYANDMECHANT.description);
            } else if (Objects.equals(response.getFailCode(), ErrorCodeEnum.HOTELMAPPING_ISEMPTY.errorCode)) {
                checkBookingResponseResponse.setReturnCode(DhubReturnCodeEnum.HOTEL_NOT_MAPPED.no);
                checkBookingResponseResponse.setFailCode(DhubReturnCodeEnum.HOTEL_NOT_MAPPED.code);
                checkBookingResponseResponse.setReturnMsg(DhubReturnCodeEnum.HOTEL_NOT_MAPPED.description);
            } else if (Objects.equals(response.getFailCode(), ErrorCodeEnum.ROOMTYPEMAPPING_ISEMPTY.errorCode) ||
                    Objects.equals(response.getFailCode(), ErrorCodeEnum.SUPPLY_NOT_EXIST.errorCode)) {
                checkBookingResponseResponse.setReturnCode(DhubReturnCodeEnum.CO_CHECKPRAM_ERROR.no);
                checkBookingResponseResponse.setFailCode(DhubReturnCodeEnum.CO_CHECKPRAM_ERROR.code);
                checkBookingResponseResponse.setReturnMsg(DhubReturnCodeEnum.CO_CHECKPRAM_ERROR.description);
            } else if (Objects.equals(response.getFailCode(), ErrorCodeEnum.SUPPLY_NO_HAS_PRODUCT.errorCode)) {
                checkBookingResponseResponse.setReturnCode(DhubReturnCodeEnum.INVALID_PRICEPLANID.no);
                checkBookingResponseResponse.setFailCode(DhubReturnCodeEnum.INVALID_PRICEPLANID.code);
                checkBookingResponseResponse.setReturnMsg(DhubReturnCodeEnum.INVALID_PRICEPLANID.description);
            } else if (Objects.equals(response.getFailCode(), ErrorCodeEnum.CHECK_DATE_ERROR.errorCode)) {
                checkBookingResponseResponse.setReturnCode(DhubReturnCodeEnum.INVALID_DATE.no);
                checkBookingResponseResponse.setFailCode(DhubReturnCodeEnum.INVALID_DATE.code);
                checkBookingResponseResponse.setReturnMsg(DhubReturnCodeEnum.INVALID_DATE.description);
            } else {
                //返回上游的异常信息
                if (StrUtilX.isNotEmpty(response.getFailCode())) {
                    checkBookingResponseResponse.setReturnCode(response.getFailCode());
                } else {
                    checkBookingResponseResponse.setReturnCode("999");
                }
                checkBookingResponseResponse.setReturnMsg(response.getFailReason());
            }
        }
    }

    /**
     * 订单金额校验
     */
    private Boolean checkOrderAmount(CreateOrderRequest request, ResponseResult<CreateOrderResponse> createOrderResponseResponseResult) {
        long day = DateUtilX.getDay(DateUtilX.stringToDate(request.getCheckInDate()), DateUtilX.stringToDate(request.getCheckOutDate()));
        if (!Objects.equals((int) day, request.getPriceItems().size())) {
            createOrderResponseResponseResult.setReturnCode(DhubReturnCodeEnum.NO_ROOMPRICE.no);
            createOrderResponseResponseResult.setReturnMsg(DhubReturnCodeEnum.NO_ROOMPRICE.description);
            return true;
        }
        return false;
    }

    /**
     * 下单前的试预定检查
     */
    private Boolean checkPreBooking(CreateOrderRequest request, ResponseResult<CreateOrderResponse> createOrderResponseResponseResult) {
        //校验时间
        checkCreateOrderParam(request, createOrderResponseResponseResult);
        if (!Objects.equals(createOrderResponseResponseResult.getReturnCode(), DhubReturnCodeEnum.SUCCESS.no)) {
            return false;
        }
        ResponseResult<CheckBookingResponse> checkBookingResponseResponseResult = checkBooking(convertCheckBookingRequest(request));
        if (Objects.equals(checkBookingResponseResponseResult.getReturnCode(), DhubReturnCodeEnum.SUCCESS.no)) {
            CheckBookingResponse bussinessResponse = checkBookingResponseResponseResult.getBussinessResponse();

            //试预定通过
            if (bussinessResponse.getCanBook() == null || bussinessResponse.getCanBook() != 1) {
                return false;
            }

            //校验价格-----
            //格式化试预定返回的数据
            if (bussinessResponse.getPriceItems() != null && bussinessResponse.getPriceItems().size() > 0) {
                Map<String, PriceInfoDetail> bookOrder = bussinessResponse.getPriceItems().stream().collect(toMap(PriceInfoDetail::getDate, t -> (t), (s1, s2) -> s2));
                //格式化下单的数据
                Map<String, CreateOrderPriceItem> createOrder = request.getPriceItems().stream().collect(toMap(CreateOrderPriceItem::getSaleDate, t -> (t), (s1, s2) -> s2));
                //BigDecimal totalPrice = BigDecimal.ZERO;
                for (String book : bookOrder.keySet()) {
                    //试预定返回的价格明细
                    PriceInfoDetail priceInfoDetail = bookOrder.get(book);
                    //下单传递的价格明细
                    CreateOrderPriceItem createOrderPriceItem = createOrder.get(book);
                    if (createOrderPriceItem == null) {
                        createOrderResponseResponseResult.setReturnCode(DhubReturnCodeEnum.CHARGE_PRICE.code);
                        createOrderResponseResponseResult.setReturnMsg(DhubReturnCodeEnum.CHARGE_PRICE.description);
                        return false;
                    }
                    if (priceInfoDetail.getSalePrice().compareTo(createOrderPriceItem.getSalePrice()) != 0) {
                        createOrderResponseResponseResult.setReturnCode(DhubReturnCodeEnum.CHARGE_PRICE.code);
                        createOrderResponseResponseResult.setReturnMsg(DhubReturnCodeEnum.CHARGE_PRICE.description);
                        return false;
                    }
                    //校验dhub的早餐数量，b2b的不校验
                    if (ChannelEnum.DHUB.key.equals(request.getChannelCode())) {
                        if (priceInfoDetail.getBreakfastNum().intValue() != createOrderPriceItem.getBreakfastNum()) {
                            createOrderResponseResponseResult.setReturnCode(DhubReturnCodeEnum.INVALID_BREAKFAST_NUM.code);
                            createOrderResponseResponseResult.setReturnMsg(DhubReturnCodeEnum.INVALID_BREAKFAST_NUM.description);
                            return false;
                        }
                    }
                    // 试预订已经做了校验配额，这里不再做处理
//                    if (priceInfoDetail.getRoomStatus() != null && priceInfoDetail.getRoomStatus() == RoomStateEnum.HAVA_ROOM.key &&
//                            (priceInfoDetail.getCanOverDraft() == null || priceInfoDetail.getCanOverDraft() == 0) &&
//                            priceInfoDetail.getQuotaNum() == null || priceInfoDetail.getQuotaNum().compareTo(request.getRoomNum()) < 0){
//                        //response.setResult(ResultCodeEnum.FAILURE.code);
//                        createOrderResponseResponseResult.setReturnCode(ReturnCodeEnum.DISSATISFY_QUOTA.no);
//                        createOrderResponseResponseResult.setReturnMsg( priceInfoDetail.getDate() +","+ReturnCodeEnum.DISSATISFY_QUOTA.description);
//                        return false;
//                    }

                    //totalPrice = totalPrice.add(priceInfoDetail.getSalePrice());
                }
                //比对总价
                if (bussinessResponse.getTotalSalePrice().compareTo(request.getTotalAmount()) != 0) {
                    createOrderResponseResponseResult.setReturnCode(DhubReturnCodeEnum.CHARGE_PRICE.code);
                    createOrderResponseResponseResult.setReturnMsg(DhubReturnCodeEnum.CHARGE_PRICE.description);
                    return false;
                }
            }

        } else {
            createOrderResponseResponseResult.setReturnCode(checkBookingResponseResponseResult.getReturnCode());
            createOrderResponseResponseResult.setReturnMsg(checkBookingResponseResponseResult.getReturnMsg());
            return false;
        }
        return true;
    }

    /**
     * 组装订单参数
     */
    private AddManualOrderDTO convertOrderDTO(CreateOrderRequest request) {
        AddManualOrderDTO addManualOrderDTO = new AddManualOrderDTO();
        addManualOrderDTO.setChannelCode(request.getChannelCode());
        addManualOrderDTO.setAgentCode(request.getAgentCode());
        addManualOrderDTO.setPartnerCode(request.getPartnerCode());
        addManualOrderDTO.setCompanyCode(CompanyDTO.COMPANY_CODE);
        addManualOrderDTO.setHotelId(request.getHotelId());
        addManualOrderDTO.setRoomId(request.getRoomId().intValue());
        addManualOrderDTO.setProductId(ProductMappingCache.getOriginalProductId(request.getRatePlanId()));
        addManualOrderDTO.setConversionProductId(request.getRatePlanId());
        addManualOrderDTO.setStartDate(request.getCheckInDate());
        addManualOrderDTO.setEndDate(request.getCheckOutDate());
        addManualOrderDTO.setRoomQty(request.getRoomNum());
        addManualOrderDTO.setOrderAmt(request.getTotalAmount());
        addManualOrderDTO.setChannelOrderCode(request.getCoOrderCode());
        addManualOrderDTO.setContactName(request.getLinkMan());
        addManualOrderDTO.setContactPhone(request.getLinkPhone());
        addManualOrderDTO.setContactCountryCode(request.getLinkCountryCode());
        addManualOrderDTO.setContactEmail(request.getEmail());
        addManualOrderDTO.setSpecialRequest(request.getSpecialDemand());
        if (!StringUtils.isEmpty(request.getRemark())) {
            addManualOrderDTO.setSpecialRequest(request.getRemark() + (StringUtils.isEmpty(addManualOrderDTO.getSpecialRequest()) ? "" : "," + addManualOrderDTO.getSpecialRequest()));
        }
        addManualOrderDTO.setSupplierCode(request.getSupplyCode());
        addManualOrderDTO.setAdultQty(request.getGuestQuantity());
        addManualOrderDTO.setPayMethod(request.getPayAtHotelFlag());
        addManualOrderDTO.setGuaranteeFlag(request.getGuaranteeFlag());
        addManualOrderDTO.setLanguage(request.getLanguage());
        addManualOrderDTO.setOperator(request.getCreatedBy());
        addManualOrderDTO.setBedInfos(request.getBedInfos());
        addManualOrderDTO.setBedInfoDesc(request.getBedInfoDesc());
        addManualOrderDTO.setSupplyType(request.getSupplyType());
        addManualOrderDTO.setIsSubstituted(request.getIsSubstituted());
        addManualOrderDTO.setUserAccount(request.getUserAccount());
        addManualOrderDTO.setChannelName(request.getChannelName());

        addManualOrderDTO.setPayStatus(request.getPayStatus());
        addManualOrderDTO.setIsVipOrder(request.getIsVipOrder());
        addManualOrderDTO.setTravelType(request.getTravelType());
        addManualOrderDTO.setSdistributorName(request.getSdistributorName());

        //每日价格
        List<PriceRequestDTO> priceRequestDTOS = new ArrayList<>();
        for (CreateOrderPriceItem createOrderPriceItem : request.getPriceItems()) {
            PriceRequestDTO priceRequestDTO = new PriceRequestDTO();
            priceRequestDTO.setSalePrice(createOrderPriceItem.getSalePrice());
            priceRequestDTO.setSaleDate(createOrderPriceItem.getSaleDate());
            priceRequestDTO.setBreakfastType(createOrderPriceItem.getBreakfastType());
            priceRequestDTO.setBreakfastNum(createOrderPriceItem.getBreakfastNum());
            priceRequestDTOS.add(priceRequestDTO);
        }
        addManualOrderDTO.setPriceList(priceRequestDTOS);
        addManualOrderDTO.setRoomGuestList(request.getRoomGuestList());//每间房入住人
        addManualOrderDTO.setQuickProcessingSwitch(request.getQuickProcessingSwitch());
        Object supplierLabel = RedisTemplateX.hGet(RedisKey.AGENT_SUPPLIER_LABEL, request.getAgentCode() + "_" + request.getSupplyCode());
        if (Objects.isNull(supplierLabel)) {
            addManualOrderDTO.setSupplierLabel(request.getSupplierLabel());
        } else {
            addManualOrderDTO.setSupplierLabel(Integer.parseInt(supplierLabel.toString()));
        }
        addManualOrderDTO.setAllBedType(request.getAllBedType());
        addManualOrderDTO.setGiftPacks(request.getGiftPacks());
        addManualOrderDTO.setBedTypeDiff(request.getBedTypeDiff());
        addManualOrderDTO.setRequestId(request.getRequestId());
        addManualOrderDTO.setOrderTrafficJson(request.getOrderTrafficJson());
        addManualOrderDTO.setNationality(request.getNationality());
        addManualOrderDTO.setServiceCharge(request.getServiceCharge());
        return addManualOrderDTO;
    }


    /**
     * 试预订入参
     */
    private PreBookDTO convertPreBookDTO(CheckBookingRequest request) {
        PreBookDTO preBookDTO = new PreBookDTO();
        preBookDTO.setRequestId(request.getRequestId());
        preBookDTO.setAgentCode(request.getAgentCode());
        preBookDTO.setChannelCode(ChannelEnum.B2B.key);
        preBookDTO.setHotelId(request.getHotelId());
        preBookDTO.setRoomId(request.getRoomId().intValue());
        preBookDTO.setProductId(request.getRatePlanId());
        preBookDTO.setCheckInDate(request.getCheckInDate());
        preBookDTO.setCheckOutDate(request.getCheckOutDate());
        preBookDTO.setRoomQty(request.getRoomNum());
        preBookDTO.setSupplyCode(request.getSupplyCode());
        preBookDTO.setGuestQuantity(request.getGuestQuantity());
        preBookDTO.setRoomGuestNumbers(request.getRoomGuestNumbers());
        preBookDTO.setSupplyType(request.getSupplyType());
        preBookDTO.setUserAccount(request.getUserAccount());
        preBookDTO.setRequestId(request.getRequestId());
        preBookDTO.setNationality(request.getNationality());
        preBookDTO.setLanguage(request.getLanguage());
        return preBookDTO;
    }

    /**
     * 转换下单请求到试预订请求
     */
    private CheckBookingRequest convertCheckBookingRequest(CreateOrderRequest request) {
        CheckBookingRequest checkBookingRequest = new CheckBookingRequest();
        checkBookingRequest.setAgentCode(request.getAgentCode());
        checkBookingRequest.setHotelId(request.getHotelId());
        checkBookingRequest.setRoomId(request.getRoomId());
        checkBookingRequest.setRatePlanId(request.getRatePlanId());
        checkBookingRequest.setCheckInDate(request.getCheckInDate());
        checkBookingRequest.setCheckOutDate(request.getCheckOutDate());
        checkBookingRequest.setSupplyCode(request.getSupplyCode());
        checkBookingRequest.setRoomNum(request.getRoomNum());
        checkBookingRequest.setSupplyType(request.getSupplyType());
        checkBookingRequest.setRequestId(request.getRequestId());
        if (CollUtilX.isNotEmpty(request.getRoomGuestList())) {
            List<RoomGuestNumber> roomGuestNumbers = new ArrayList<>();
            int i = 1;
            for (OrderRoomGuestDTO orderRoomGuestDTO : request.getRoomGuestList()) {
                RoomGuestNumber roomGuestNumber = new RoomGuestNumber();
                roomGuestNumber.setRoomIndex(i++);
                roomGuestNumber.setAdultNum(orderRoomGuestDTO.getAdultQty());
                if (StrUtilX.isNotEmpty(orderRoomGuestDTO.getChildrenAge())) {
                    String[] ageArr = orderRoomGuestDTO.getChildrenAge().split(",");
                    List<ChildrenInfo> childrenInfos = new ArrayList<>();
                    for (String age : ageArr) {
                        ChildrenInfo childrenInfo = new ChildrenInfo();
                        childrenInfo.setChildrenAge(Integer.valueOf(age));
                        childrenInfos.add(childrenInfo);
                    }
                    roomGuestNumber.setChildrenInfos(childrenInfos);
                }
                roomGuestNumbers.add(roomGuestNumber);
            }

            checkBookingRequest.setGuestQuantity(roomGuestNumbers.size());
            checkBookingRequest.setRoomGuestNumbers(roomGuestNumbers);
            checkBookingRequest.setNationality(request.getNationality());
        }

        return checkBookingRequest;
    }

    /**
     * 组装创建订单返回信息
     */
    private void passCreateOrderResult(ResponseResult<CreateOrderResponse> createOrderResponseResponseResult, Response<String> response, CreateOrderRequest request) {
        CreateOrderResponse createOrderResponse = createOrderResponseResponseResult.getBussinessResponse();
        createOrderResponse.setResult(1);
        createOrderResponse.setFcOrderCode(JSON.parseObject(JSON.toJSONString(response.getModel()), String.class));
        //fixme 支付状态
        // if payStatus = 1,异步更新订单支付状态为已支付
//        if (Objects.equals(request.getPayStatus(), PayStatusEnum.SUCCESS.getType())) {
//            CompletableFuture.runAsync(() -> {
//                PayOrderRequest payOrderRequest = new PayOrderRequest();
//                payOrderRequest.setCoOrderCode(createOrderResponse.getCoOrderCode());
//                payOrderRequest.setFcOrderCode(createOrderResponse.getFcOrderCode());
//                orderService.updatePayStatus(payOrderRequest);
//            });
//        }
        RedisTemplateX.setIfAbsent(StrUtilX.concat(RedisKey.PREVENT_DUPLICATE_ORDER_CREATION_SIGN, request.getCoOrderCode()), JSON.toJSONString(createOrderResponse), 20 * 60 * 1000L);
        if (!response.getResult().equals(1)) {
            if (Objects.equals(response.getFailCode(), ErrorCodeEnum.EXIST_ORDER.errorCode)) {
                createOrderResponseResponseResult.setReturnCode(DhubReturnCodeEnum.ORDER_HAVE_EXISTED.code);
                createOrderResponseResponseResult.setFailCode(DhubReturnCodeEnum.ORDER_HAVE_EXISTED.code);
                createOrderResponseResponseResult.setReturnMsg(DhubReturnCodeEnum.ORDER_HAVE_EXISTED.description);
            } else if (Objects.equals(response.getFailCode(), ErrorCodeEnum.INSUFFICIENT_AMOUNT.errorCode)) {
                createOrderResponseResponseResult.setReturnCode(DhubReturnCodeEnum.CREDIT_BALANCE_NOT_ENOUGH.code);
                createOrderResponseResponseResult.setFailCode(DhubReturnCodeEnum.CREDIT_BALANCE_NOT_ENOUGH.code);
                createOrderResponseResponseResult.setReturnMsg(DhubReturnCodeEnum.CREDIT_BALANCE_NOT_ENOUGH.description);
            } else if (Objects.equals(response.getFailCode(), ErrorCodeEnum.SUPPLY_NO_HAS_PRODUCT.errorCode)) {
                createOrderResponseResponseResult.setReturnCode(DhubReturnCodeEnum.INVALID_PRICEPLANID.code);
                createOrderResponseResponseResult.setFailCode(DhubReturnCodeEnum.INVALID_PRICEPLANID.code);
                createOrderResponseResponseResult.setReturnMsg(DhubReturnCodeEnum.INVALID_PRICEPLANID.description);
            } else if (Objects.equals(response.getFailCode(), ErrorCodeEnum.CHECK_DATE_ERROR.errorCode)) {
                createOrderResponseResponseResult.setReturnCode(DhubReturnCodeEnum.INVALID_DATE.code);
                createOrderResponseResponseResult.setFailCode(DhubReturnCodeEnum.INVALID_DATE.code);
                createOrderResponseResponseResult.setReturnMsg(DhubReturnCodeEnum.INVALID_DATE.description);
            } else {
                //返回上游的异常信息
                createOrderResponseResponseResult.setReturnCode(response.getFailCode());
                createOrderResponseResponseResult.setReturnMsg(response.getFailReason());
            }
        }
    }

    /**
     * 转换订单为api订单
     */
    private void convertToAPIOrder(OrderDetailResponse orderDetailResponse, OrderDTO orderDTO) {
        orderDetailResponse.setFcOrderCode(orderDTO.getOrderCode());
        orderDetailResponse.setCoOrderCode(orderDTO.getChannelOrderCode());
        orderDetailResponse.setOrderStatus(OrderStatusMappingEnum.getOrderStatus(orderDTO.getOrderConfirmationStatus()));
//        if (orderDTO.getPayStatus() != null ) {
//            orderDetailResponse.setChannelState(String.valueOf(orderDTO.getPayStatus() ));
//        }
        orderDetailResponse.setHotelId(orderDTO.getHotelId());
        orderDetailResponse.setHotelName(orderDTO.getHotelName());
        orderDetailResponse.setRoomId(Long.valueOf(orderDTO.getRoomId()));
        orderDetailResponse.setRoomName(orderDTO.getRoomName());
        orderDetailResponse.setRatePlanId(orderDTO.getConversionProductId());
        orderDetailResponse.setRatePlanName(orderDTO.getProductName());
        orderDetailResponse.setBedType(orderDTO.getBedType());
        orderDetailResponse.setCheckInDate(orderDTO.getStartDate());
        orderDetailResponse.setCheckOutDate(orderDTO.getEndDate());
        orderDetailResponse.setCheckInTime(orderDTO.getStartTime());
        orderDetailResponse.setCheckOutTime(orderDTO.getEndTime());
        orderDetailResponse.setArriveTime(orderDTO.getEarliestArriveTime());
        orderDetailResponse.setLatestArriveTime(orderDTO.getLatestArriveTime());
        orderDetailResponse.setRoomNum(orderDTO.getRoomQty());
        orderDetailResponse.setPayAtHotelCurrency(orderDTO.getPayAtHotelCurrency());
        orderDetailResponse.setPayAtHotelCurrencyCode(orderDTO.getPayAtHotelCurrencyCode());
        orderDetailResponse.setPayAtHotelFee(orderDTO.getPayAtHotelFee());
        orderDetailResponse.setFeeList(orderDTO.getFeeList());
        orderDetailResponse.setRoomGuestDTOS(orderDTO.getRoomGuestList());
        if (orderDTO.getPayMethod() != null && orderDTO.getPayMethod().equals(PayMethodEnum.PAY.key)) {
//            if (orderDTO.getSalePrice() != null) {
//                orderDetailResponse.setTotalBaseAmount(orderDTO.getSalePrice().doubleValue());
//            }
            if (orderDTO.getBasePrice() != null) {
                orderDetailResponse.setTotalBaseAmount(orderDTO.getBasePrice().doubleValue());
            }
            orderDetailResponse.setBaseCurrency(orderDTO.getBaseCurrency());
        }

        if (orderDTO.getSalePrice() != null) {
            orderDetailResponse.setTotalAmount(orderDTO.getSalePrice().doubleValue());
        }
        orderDetailResponse.setCurrency(orderDTO.getSaleCurrency());
        orderDetailResponse.setHourlyRoom(orderDTO.getHourly());
        orderDetailResponse.setLinkMan(orderDTO.getContactName());
        orderDetailResponse.setLinkPhone(orderDTO.getContactPhone());
        orderDetailResponse.setEmail(orderDTO.getContactEmail());
        if (orderDTO.getCreatedDt() != null) {
            orderDetailResponse.setCreateTime(orderDTO.getCreatedDt());
        }

        List<OrderPriceItem> orderPriceItemList = new ArrayList<>();
        for (PriceResponseDTO priceResponseDTO : orderDTO.getSalePriceList()) {
            OrderPriceItem orderPriceItem = new OrderPriceItem();
            orderPriceItem.setSalePrice(priceResponseDTO.getSalePrice());
            orderPriceItem.setSaleDate(priceResponseDTO.getSaleDate());
            orderPriceItem.setBreakfastNum(priceResponseDTO.getBreakfastNum());
            orderPriceItem.setCurrency(priceResponseDTO.getCurrency());
            orderPriceItemList.add(orderPriceItem);
        }
        orderDetailResponse.setPriceItems(orderPriceItemList);

        List<GuestInfo> guestInfoList = new ArrayList<>();
        for (OrderGuestDTO orderGuestDTO : orderDTO.getGuestList()) {
            GuestInfo guestInfo = new GuestInfo();
            guestInfo.setGuestName(orderGuestDTO.getName());
            guestInfo.setFirstName(orderGuestDTO.getFirstName());
            guestInfo.setLastName(orderGuestDTO.getLastName());
            guestInfo.setEarnPoints(orderGuestDTO.getEarnPoints());
            guestInfo.setIdCardNo(orderGuestDTO.getIdCardNo());
            guestInfo.setIdCardType(orderGuestDTO.getIdCardType());
            guestInfo.setMobileNo(orderGuestDTO.getMobileNo());
            guestInfo.setNationality(orderGuestDTO.getNationality());
            guestInfo.setRoomNumber(orderGuestDTO.getRoomNumber().toString());
            guestInfo.setMembershipCardNumber(orderGuestDTO.getMembershipCardNumber());
            guestInfoList.add(guestInfo);
        }
        orderDetailResponse.setGuestInfos(guestInfoList);

        if (StringUtils.isEmpty(orderDTO.getConfirmationCode())) {
            orderDetailResponse.setHotelConfirmNo("");
        } else {
            orderDetailResponse.setHotelConfirmNo(orderDTO.getConfirmationCode());
        }
        if (orderDTO.getPayStatus() == null || orderDTO.getPayStatus().intValue() == PayStatusEnum.NONE.getNo()) {
            orderDetailResponse.setChannelState("not-paid");
        } else if (orderDTO.getPayStatus().intValue() == PayStatusEnum.PAY.getNo()) {
            orderDetailResponse.setChannelState("paid");
        }
        orderDetailResponse.setQuickProcessingSwitch(orderDTO.getQuickProcessingSwitch());
        orderDetailResponse.setSupplierLabel(orderDTO.getSupplierLabel());
        orderDetailResponse.setBedTypeDiff(orderDTO.getBedTypeDiff());
        orderDetailResponse.setPenaltiesValue(orderDTO.getRefundFee());
        orderDetailResponse.setServiceCharge(orderDTO.getServiceCharge());
    }

    /**
     * 组装入住详情信息
     */
    private void passOrderCheckDetailResult(ResponseResult<OrderCheckDetailInfoResponse> responseResult, Response<List<OrderCheckInfoDTO>> response) {
        OrderCheckDetailInfoResponse bussinessResponse = responseResult.getBussinessResponse();
        List<OrderCheckInfoDTO> orderCheckInfoDTOS = response.getModel();

        OrderDTO orderDTO = null;
        if (StrUtilX.isNotEmpty(responseResult.getBussinessResponse().getFcOrderCode())) {
            Response<OrderDTO> queryOrderBasicInfoByOrderCode = orderRemote.queryOrderBasicInfoByOrderCode(responseResult.getBussinessResponse().getFcOrderCode());
            orderDTO = queryOrderBasicInfoByOrderCode.getModel();
        }

        BigDecimal sumAdditionalCharges = new BigDecimal(0);
        if (CollUtilX.isNotEmpty(orderCheckInfoDTOS)) {
            for (OrderCheckInfoDTO orderCheckInfoDTO : orderCheckInfoDTOS) {
                bussinessResponse.setCoOrderCode(orderCheckInfoDTO.getCoOrderCode());
                bussinessResponse.setFcOrderCode(orderCheckInfoDTO.getOrderCode());
                if (CollUtilX.isNotEmpty(orderCheckInfoDTO.getCheckInDetailList())) {
                    BigDecimal roomAdditionalCharges = new BigDecimal(0);
                    String currency = SettlementCurrencyEnum.getCodeByKey(String.valueOf(orderCheckInfoDTO.getCurrency()));
                    for (OrderCheckDetailDTO orderCheckDetailDTO : orderCheckInfoDTO.getCheckInDetailList()) {
                        orderCheckDetailDTO.setCurrency(currency);
                        roomAdditionalCharges = roomAdditionalCharges.add(orderCheckDetailDTO.getSalePrice() != null ? orderCheckDetailDTO.getSalePrice() : new BigDecimal(0))
                                .add(orderCheckDetailDTO.getRefundPrice() != null ? orderCheckDetailDTO.getRefundPrice() : new BigDecimal(0));
                    }
                    sumAdditionalCharges = sumAdditionalCharges.add(roomAdditionalCharges);
                    orderCheckInfoDTO.setRoomAdditionalCharges(roomAdditionalCharges);
                }
            }
            List<OrderCheckDetails> orderCheckDetails = DisConvert.INSTANCE.orderCheckDetailsConvert(orderCheckInfoDTOS);
            bussinessResponse.setCheckDetails(orderCheckDetails);
        }
        bussinessResponse.setOrderAdditionalCharges(sumAdditionalCharges);
        if (orderDTO != null && orderDTO.getOrderAmt() != null) {
            bussinessResponse.setOrderSalePrice(orderDTO.getOrderAmt());
        }
    }

    /**
     * 转换对象
     */
    private OrderInvoiceDTO convertInvoice(CreateInvoiceRequest request) {
        if (request == null) {
            return null;
        }
        OrderInvoiceDTO orderInvoice = new OrderInvoiceDTO();
        orderInvoice.setOrderCode(request.getOrderCode());
        orderInvoice.setInvoiceType(request.getInvoiceTypeInteger());
        orderInvoice.setInvoiceTitle(request.getInvoiceHeader());
        orderInvoice.setInvoiceName(request.getCostNameInteger());
        orderInvoice.setInvoiceAmount(request.getAmount());
        orderInvoice.setTaxNumber(request.getCompanyTax());
        orderInvoice.setRegisterAddr(request.getRegisterAddress());
        if (StrUtil.isNotEmpty(request.getCompanyTel())) {
            if (StrUtil.isEmpty(request.getCompanyTelAreaCode())) {
                orderInvoice.setCompanyPhone(request.getCompanyTel());
            } else {
                orderInvoice.setCompanyPhone(request.getCompanyTelAreaCode() + "-" + request.getCompanyTel());
            }
        }
        orderInvoice.setAccountNo(request.getBankAccount());
        orderInvoice.setAccountBank(request.getBank());
        orderInvoice.setTicketType(TicketTypeEnum.getCode(request.getGetMethod()));
        orderInvoice.setReceivedName(request.getReceiveName());
        if (StrUtil.isNotEmpty(request.getTelePhone())) {
            if (StrUtil.isEmpty(request.getTelephoneAreaCode())) {
                orderInvoice.setReceivedPhone(request.getTelePhone());
            } else {
                orderInvoice.setReceivedPhone(request.getTelephoneAreaCode() + "-" + request.getTelePhone());
            }
        }
        orderInvoice.setReceivedAddr(request.getAddress());
        orderInvoice.setEmail(request.getEmail());
        orderInvoice.setInvoiceStatus(0);
        orderInvoice.setSendStatus(0);
        orderInvoice.setInvoiceRemark(request.getRemark());
        orderInvoice.setApplyId(request.getApplyId());
        orderInvoice.setSortNum(request.getSortNum());
        orderInvoice.setSpecs(request.getSpecs());
        orderInvoice.setPerUnit(request.getPerUnit());
        orderInvoice.setCountNum(request.getCountNum());
        orderInvoice.setInvoiceTaxRate(request.getInvoiceTaxRate());
        orderInvoice.setTrackingNo(request.getTrackingNo());
        orderInvoice.setApplyDate(LocalDate.now().toString());
        orderInvoice.setOperator("system");
        orderInvoice.setInvoiceTitle(request.getInvoiceHeader());
        orderInvoice.setInvoiceTitleType(request.getInvoiceTitleType());
        orderInvoice.setCreatedBy("system");
        orderInvoice.setCreatedDt(DateUtilX.dateToString(new Date(), "yyyy-MM-dd HH:mm:ss"));
        return orderInvoice;
    }
}
