package com.tiangong.settle.paymentTimeConfig.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tiangong.cloud.common.domain.LoginUser;
import com.tiangong.cloud.common.enums.result.ResultEnum;
import com.tiangong.cloud.commonbean.config.security.TokenManager;
import com.tiangong.exception.SysException;
import com.tiangong.settle.paymentTimeConfig.domain.entity.OnlineCollectionConfigEntity;
import com.tiangong.settle.paymentTimeConfig.mapper.OnlineCollectionConfigMapper;
import com.tiangong.settle.paymentTimeConfig.service.OnlineCollectionConfigService;
import com.tiangong.settle.req.OnlineCollectionConfigReq;
import com.tiangong.settle.resp.OnlineCollectionConfigResp;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 延时付款配置表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-19 15:38:33
 */
@Service
public class OnlineCollectionConfigServiceImpl extends ServiceImpl<OnlineCollectionConfigMapper, OnlineCollectionConfigEntity> implements OnlineCollectionConfigService {

    @Autowired
    private OnlineCollectionConfigMapper onlineCollectionConfigMapper;

    @Autowired
    HttpServletRequest request;

    /**
     * 延时付款配置表新增
     */
    @Override
    @Transactional
    public int onlineCollectionConfigAdd(List<OnlineCollectionConfigReq> req) {
        LoginUser loginUser = TokenManager.getUser(request);

        int insert = 0;
        for (OnlineCollectionConfigReq onlineCollectionConfigReq : req) {
            OnlineCollectionConfigEntity entity = new OnlineCollectionConfigEntity();
            BeanUtils.copyProperties(onlineCollectionConfigReq, entity);
            //saas商家编码
            String merchantCode = loginUser.getCompanyCode();
            entity.setMerchantCode(merchantCode);

            entity.setCreatedDt(new Date());
            entity.setCreatedBy(loginUser.getUserName());
            entity.setUpdatedDt(new Date());
            entity.setUpdatedBy(loginUser.getUserName());
            insert = onlineCollectionConfigMapper.insert(entity);
            if (insert != 1) {
                throw new SysException(ResultEnum.E_1001.getCode(), ResultEnum.E_1001.getMessage());
            }
        }
        return insert;
    }

    /**
     * 延时付款配置表编辑
     */
    @Override
    @Transactional
    public int onlineCollectionConfigEdit(List<OnlineCollectionConfigReq> req) {
        LoginUser loginUser = TokenManager.getUser(request);
        try {
            for (OnlineCollectionConfigReq onlineCollectionConfigReq : req) {
                OnlineCollectionConfigEntity entity = new OnlineCollectionConfigEntity();
                BeanUtils.copyProperties(onlineCollectionConfigReq, entity);
                entity.setUpdatedDt(new Date());
                entity.setUpdatedBy(loginUser.getUserName());
                onlineCollectionConfigMapper.updateById(entity);
            }
        }catch (Exception e){
            throw new SysException(ResultEnum.E_1003.getCode(), ResultEnum.E_1003.getMessage());
        }
        return 1;
    }


    /**
     * 延时付款配置表列表（全部）
     */
    @Override
    public List<OnlineCollectionConfigResp> onlineCollectionConfigList(OnlineCollectionConfigReq req) {
        LoginUser loginUser = TokenManager.getUser(request);
        //saas商家编码
        String merchantCode = loginUser.getCompanyCode();

        QueryWrapper<OnlineCollectionConfigEntity> query = new QueryWrapper<>();
        query.eq("merchant_code",merchantCode);

        List<OnlineCollectionConfigEntity> list = onlineCollectionConfigMapper.selectList(query);
        return list.stream().map((item) -> {
                OnlineCollectionConfigResp resp = new OnlineCollectionConfigResp();
            BeanUtils.copyProperties(item, resp);
            return resp;
        }).collect(Collectors.toList());
    }


    /**
     * 延时付款配置表详情
     */
    @Override
    public List<OnlineCollectionConfigResp> onlineCollectionConfigInfo(OnlineCollectionConfigReq req) {
        QueryWrapper<OnlineCollectionConfigEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("merchant_code",req.getMerchantCode());
        List<OnlineCollectionConfigEntity> list = onlineCollectionConfigMapper.selectList(queryWrapper);
        List<OnlineCollectionConfigResp> collect = list.stream().map((item) -> {
            OnlineCollectionConfigResp resp = new OnlineCollectionConfigResp();
            BeanUtils.copyProperties(item, resp);
            return resp;
        }).collect(Collectors.toList());
        return collect;
    }
}