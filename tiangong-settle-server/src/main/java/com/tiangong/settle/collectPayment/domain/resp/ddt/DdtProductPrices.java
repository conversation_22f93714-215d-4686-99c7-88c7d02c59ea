package com.tiangong.settle.collectPayment.domain.resp.ddt;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

@Data
public class DdtProductPrices {
    private String date;

    private String price;

    public String getDate() {
        // 进行日期格式化
        if (date != null) {
            LocalDate date1 = LocalDate.parse(date, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            return date1.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        }
        return null;
    }
}
