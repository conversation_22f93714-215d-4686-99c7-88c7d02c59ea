package com.tiangong.product.dto;

import com.tiangong.dto.order.CancelRestriction;
import com.tiangong.dto.product.HourlyRoomInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @Auther: Owen
 * @Date: 2019/6/19 01:45
 * @Description: 酒店售价详情
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProductSalePriceResultDTO implements Serializable {

    /**
     * 酒店Id
     */
    private Long hotelId;

    /**
     * 酒店名称
     */
    private String hotelName;

    /**
     * 房型Id
     */
    private Integer roomId;

    /**
     * 房型名称
     */
    private String roomName;

    /**
     * 产品Id
     */
    private String productId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 早餐数
     */
    private Integer breakfastQty;

    /**
     * 产品币种
     */
    private Integer currency;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 产品供货方式（0 运营商自建， 1供应商EBK录入， 2 api对接）
     */
    private Integer supplyType;

    /**
     * 价格明细
     */
    private List<ProductSalePriceItemDTO> priceList;

    /**
     * 取消条款类型(海外产品不落地直连)
     */
    private Integer cancelRestrictType;

    private Integer cancelRestrictDays;

    private String cancelRestrictTime;

    /**
     * 阶段取消条款
     */
    private List<CancelRestriction> cancelRestrictions;

    /**
     * 发票模式
     */
    private Integer invoiceModel;

    /**
     * 发票类型
     */
    private Integer invoiceType;

    /**
     * 担保条款类型
     */
    private Integer guaranteeType;

    /**
     * 担保条件
     */
    private String guaranteeCondition;

    /**
     * 担保费用类型
     */
    private Integer guaranteeFeeType;

    /**
     * 担保描述
     */
    private String guaranteeDesc;

    /**
     * 床型
     */
    private String bedType;

    /**
     * 是否钟点房1是0否 默认日历房
     */
    private int hourly;

    /**
     * 钟点房对象
     */
    private HourlyRoomInfo hourlyRoomInfo;

    /**
     * 钟点房时间选择范围 隐藏传值使用
     */
    private List<String> hourlyTimeList;

    /**
     * 钟点房时间选择范围 展示使用
     */
    private List<String> hourlyTimeShowList;

    /**
     * 是否需要填写身份证	Integer	1是0否 空代表否
     */
    private int hasIdCard;
}
