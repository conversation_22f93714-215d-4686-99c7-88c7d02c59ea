package com.tiangong.product.dto;

import lombok.Data;

import java.util.Date;

/**
 * @program: tiangong
 * @ClassName CancelPenaltiesDTO
 * @description: 取消罚金
 * @author: 湫
 * @create: 2024/09/05/ 10:54
 * @Version 1.0
 **/
@Data
public class CancelPenaltiesDTO {

    /**
     * 罚金类型
     * 1. *晚
     * 2. 固定金额
     * 3. 百分比
     * 4. 首晚百分比
     * @see com.tiangong.enums.PenaltiesTypeEnum
     */
    private Integer penaltiesType;

    /**
     * 开始时间
     */
    private Date startDate;

    /**
     * 结束时间
     */
    private Date endData;

    /**
     * 罚金值
     */
    private Double penaltiesValue;

}
