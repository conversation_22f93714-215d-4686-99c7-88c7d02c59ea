package com.tiangong.product.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 税费配置 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class ProTaxDetailBaseVO implements Serializable {

    /** 自增长主键id */
    private Long id;

    /** 税费规则id */
    private Long taxRuleConfigId;

    /** 税费说明 */
    private String taxDesc;

    /** 税费标准 [1:/间、2:/晚] */
    private Integer taxStandard;

    /** 税费加辐规则 [1:加固定值、2:加百分比] */
    private Integer taxIncreaseType;

    /** 税费加辐值 */
    private BigDecimal taxIncreaseValue;

}
