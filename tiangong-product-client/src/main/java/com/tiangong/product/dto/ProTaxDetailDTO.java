package com.tiangong.product.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @program: tiangong
 * @ClassName TaxDetailDTO
 * @description:
 * @author: 湫
 * @create: 2024/09/07/ 17:56
 * @Version 1.0
 **/

@Data
public class ProTaxDetailDTO {
    /**
     * 税费类型
     * com.tiangong.enums.TaxTypeEnum
     */
    private Integer taxType;

    /**
     * 税费说明
     *
     */
    private String taxDesc;

    /**
     * 税费标准 [1:/间、2:/晚]
     * com.tiangong.enums.TaxStandardsEnum
     */
    private Integer taxStandard;

    /**
     * 税费加辐规则 [1:加固定值、2:加百分比]
     * com.tiangong.enums.TaxIncreaseEnum
     */
    private Integer taxIncreaseType;

    /** 税费加辐值 */
    private BigDecimal taxIncreaseValue;

}
