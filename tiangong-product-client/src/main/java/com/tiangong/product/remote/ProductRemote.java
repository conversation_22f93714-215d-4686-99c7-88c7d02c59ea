package com.tiangong.product.remote;

import com.tiangong.common.Response;
import com.tiangong.product.dto.ProductDTO;
import com.tiangong.product.dto.QueryProductRestrictDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

/**
 * @Auther: Owen
 * @Date: 2019/4/24 12:06
 * @Description: 产品
 */
@FeignClient(value = "tiangong-product-server")
public interface ProductRemote {

    /**
     * 查询产品售价列表
     */
    @PostMapping("/product/queryProduct")
    Response<ProductDTO> queryProduct(@RequestBody Map<String, String> requestMap);

    /**
     * 查询产品条款信息
     */
    @PostMapping("/product/queryProductRestrictList")
    Response<List<ProductDTO>> queryProductRestrictList(@RequestBody QueryProductRestrictDTO queryProductRestrictDTO);

    /**
     * 根据id查询产品
     */
    @PostMapping("/product/queryProductById")
    Response<ProductDTO> queryProductById(@RequestBody Map<String, String> requestMap);
}
