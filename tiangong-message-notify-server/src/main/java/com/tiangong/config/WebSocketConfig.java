package com.tiangong.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2024/7/12 下午5:25
 * @Description:
 */

@Configuration
@EnableWebSocket
public class WebSocketConfig implements WebSocketConfigurer {

    @Resource
    private OrderNotifyHandler orderNotifyHandler;

    @Resource
    private WebSocketInterceptor webSocketInterceptor;

    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        registry.addHandler(orderNotifyHandler, "/webSocket/orderNotify")
                .addInterceptors(webSocketInterceptor)
                .setAllowedOrigins("*");
    }

}
