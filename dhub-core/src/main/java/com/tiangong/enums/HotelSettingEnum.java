package com.tiangong.enums;

import com.tiangong.util.CollUtilX;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

@Getter
@AllArgsConstructor
public enum HotelSettingEnum {

    HOTELNAME("hotelName", "hotelName", "酒店名称"),
    INTRODUCE("introduce", "introduce", "简介"),
    GROUP("group", "group", "集团"),
    BRAND("brand", "brand", "品牌"),
    GEOINFO("geoInfo", "geoInfo", "经纬度"),
    LOCATION("location", "location", "位置"),
    DISTRICT("district", "district", "行政区"),
    BUSINESSZONE("businesszone", "businesszone", "商业区"),
    HOTELSTAR("hotelStar", "hotelStar", "酒店星级"),
    INOUTPOLICY("inOutPolicy", "inOutPolicy", "入离政策"),
    ADDBEDCHARGE("addBedCharge", "addBedCharge", "收费标准"),
    TEL("tel", "tel", "电话"),
    INFORM("importantNotices", "inform", "通知"),
    FACILITIES("hotelFacilityNew", "facilities", "设施集合"),
    IMAGES("images", "images", "图片集合"),
    PARKINGS("parking", "parkings", "停车场集合"),
    CHARGINGPARKING("chargingParking", "parkings", "停车场集合"),
    ROOMLIST("roomList", "roomList", "房型集合"),
    MEETINGS("hotelMeetingInfos", "meetings", "会议室集合"),
    RATING("comment", "rating", "酒店评分"),
    CERTIFICATES("hotelCertificates", "certificates", "资质"),
    VIDEOINFOS("hotelVideoInfos", "videoInfos", "视频"),
    BREAKFAST("breakfast", "breakfast", "早餐"),
    ADDBEDPOLICY("hotelStructuredPolicies.extraBedPolicy", "addBedPolicy", "加床政策"),
    CHILDPOLICY("hotelStructuredPolicies.childPolicy", "childPolicy", "儿童政策"),
    PETPOLICY("hotelStructuredPolicies.petPolicy", "petPolicy", "宠物政策"),
    HOTELTEXTPOLICY("hotelTextPolicies", "hotelTextPolicy", "酒店文本政策");

    public final String dhubKey;
    public final String key;
    public final String value;

    public static List<String> getAllKey() {
        List<String> allSettings = new ArrayList<>();
        for (HotelSettingEnum hotelSettingEnum : HotelSettingEnum.values()) {
            allSettings.add(hotelSettingEnum.key);
        }
        return allSettings;
    }

    /**
     * "settings": [
     * "hotelFacilityNew",     //酒店设施（含房型设施）
     * "breakfast",                //酒店政策：早餐政策
     * "importantNotices",     //酒店政策：重要通知
     * "parking",     //酒店政策：停车场
     * "chargingParking",      //酒店政策：充电车位
     * "hotelCertificates",    //酒店资质，可能没有
     * "comment",       //酒店评分
     * "hotelMeetingInfos",       //酒店会议室信息
     * "hotelVideoInfos",   //酒店视频信息
     * "hotelTextPolicies",   //酒店文本政策”
     * "hotelStructuredPolicies.childPolicy",       //儿童政策
     * "hotelStructuredPolicies.extraBedPolicy",  //加床政策
     * "hotelStructuredPolicies.petPolicy"   //宠物政策
     * ]
     *
     * @return
     */
    public static List<String> getBaseKey() {
        List<String> allSettings = new ArrayList<>();
        for (HotelSettingEnum hotelSettingEnum : HotelSettingEnum.values()) {
            if (hotelSettingEnum.dhubKey.equals("hotelFacilityNew")
                    || hotelSettingEnum.dhubKey.equals("breakfast")
                    || hotelSettingEnum.dhubKey.equals("importantNotices")
                    || hotelSettingEnum.dhubKey.equals("parking")
                    || hotelSettingEnum.dhubKey.equals("chargingParking")
                    || hotelSettingEnum.dhubKey.equals("hotelCertificates")
                    || hotelSettingEnum.dhubKey.equals("comment")
                    || hotelSettingEnum.dhubKey.equals("hotelMeetingInfos")
                    || hotelSettingEnum.dhubKey.equals("hotelVideoInfos")
                    || hotelSettingEnum.dhubKey.equals("hotelTextPolicies")
                    || hotelSettingEnum.dhubKey.equals("hotelStructuredPolicies.childPolicy")
                    || hotelSettingEnum.dhubKey.equals("hotelStructuredPolicies.extraBedPolicy")
                    || hotelSettingEnum.dhubKey.equals("hotelStructuredPolicies.petPolicy")
            ) {
                continue;
            }
            allSettings.add(hotelSettingEnum.key);
        }
        return allSettings;
    }

    public static List<String> getSettingsKeys(List<String> dhunKeys) {
        List<String> allSettings = new ArrayList<>();
        if (CollUtilX.isEmpty(dhunKeys)) return allSettings;
        for (String dhubKey : dhunKeys) {
            for (HotelSettingEnum hotelSettingEnum : HotelSettingEnum.values()) {
                if (dhubKey.equals(hotelSettingEnum.dhubKey)) {
                    allSettings.add(hotelSettingEnum.key);
                    break;
                }
            }
        }
        return allSettings;
    }

    public static String getKey(String dhubKey) {
        for (HotelSettingEnum dhubSettingEnum : HotelSettingEnum.values()) {
            if (dhubSettingEnum.dhubKey.equals(dhubKey)) {
                return dhubSettingEnum.key;
            }
        }
        return null;
    }
}
