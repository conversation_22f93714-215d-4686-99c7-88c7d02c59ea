package com.tiangong.service.base;

import com.tiangong.entity.request.basic.DistrictOrBusinessReq;
import com.tiangong.entity.request.basic.SearchCityAndHotelReq;
import com.tiangong.entity.request.hotel.*;
import com.tiangong.entity.response.basic.DistrictAndBusinessResponse;
import com.tiangong.entity.response.basic.HotelAndCityResponse;
import com.tiangong.entity.response.hotel.*;

public interface HotelInfoService {

    /**
     * 酒店列表查询接口
     */
    HotelIdListInfoResponse queryHotelList(HotelIdListInfoRequest request);
    /**
     * 酒店基本信息查询
     */
    HotelInfoResponse queryHotelInfo(HotelInfoRequest request);

    /**
     * 酒店增量查询
     */
    IncrementResponse queryHotelIncrement(HotelIncrementRequest request);

    /**
     * 酒店图片查询
     */
    HotelImageResponse queryHotelImage(HotelImageRequest request);

    /**
     * 酒店列表查询
     */
//    HotelListResponse queryHotelAllList(HotelPriceRequest request);


    /**
     * 查询行政区商业区，星级等筛选条件
     */
//    DistrictAndBusinessResponse queryDistrictAndBusiness(DistrictOrBusinessReq req);

    /**
     * 查询城市和酒店接口
     */
//    HotelAndCityResponse queryCityAndHotelByKeyword(SearchCityAndHotelReq request);

    /**
     * 根据酒店Id列表查询酒店主要信息
     */
//    HotelMainInfoResponse queryHotelMainInfoByHotelIds(HotelMainInfoRequest request);;

    /**
     * 查询酒店列表---新
     */
    HotelListPageResponse findHotelList(HotelListPageRequest request);
}
