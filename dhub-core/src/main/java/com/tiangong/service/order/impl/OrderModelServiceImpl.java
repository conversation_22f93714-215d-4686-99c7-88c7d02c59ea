package com.tiangong.service.order.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.tiangong.common.Response;
import com.tiangong.config.DhubConfig;
import com.tiangong.config.agentConfig.AgentConfig;
import com.tiangong.config.exception.CustomException;
import com.tiangong.constanct.MqConstants;
import com.tiangong.constant.Constant;
import com.tiangong.convert.DTOConvert;
import com.tiangong.dis.dto.SaveOrderLogDTO;
import com.tiangong.dis.enums.DhubReturnCodeEnum;
import com.tiangong.dis.remote.HotelOrderRemote;
import com.tiangong.dis.remote.OrderChangeWordOrderTaskRemote;
import com.tiangong.dto.hotel.CountryCodeReq;
import com.tiangong.dto.order.OrderGuestDTO;
import com.tiangong.dto.order.OrderRoomGuestDTO;
import com.tiangong.dto.order.request.*;
import com.tiangong.dto.order.response.*;
import com.tiangong.dto.product.HourlyRoomDetail;
import com.tiangong.dto.product.PriceInfoDetail;
import com.tiangong.dto.product.request.ChildrenInfo;
import com.tiangong.dto.product.request.RoomGuestNumber;
import com.tiangong.dto.product.response.BedInfoDto;
import com.tiangong.entity.base.AgentAccountConfig;
import com.tiangong.entity.base.Header;
import com.tiangong.entity.base.Request;
import com.tiangong.entity.base.ResponseResult;
import com.tiangong.entity.request.order.*;
import com.tiangong.entity.request.order.CheckoutRoomInfo;
import com.tiangong.entity.request.order.OrderCheckDetailInfoRequest;
import com.tiangong.entity.request.order.OrderStatusPushRequest;
import com.tiangong.entity.response.order.*;
import com.tiangong.entity.response.order.OrderStatusPushResponse;
import com.tiangong.entity.vo.order.*;
import com.tiangong.entity.vo.order.GuestInfo;
import com.tiangong.entity.vo.order.MixedPayInfo;
import com.tiangong.entity.vo.order.MixedRefundInfo;
import com.tiangong.entity.vo.order.OrderCheckDetailPriceItem;
import com.tiangong.entity.vo.order.OrderCheckDetails;
import com.tiangong.entity.vo.order.OrderPriceItem;
import com.tiangong.enums.*;
import com.tiangong.service.order.OrderModelService;
import com.tiangong.util.*;
import com.tiangong.utils.CommonUtil;
import com.tiangong.utils.CommonUtils;
import com.tiangong.utils.CreateTokenUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.config.RequestConfig;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Pattern;

@Slf4j
@Service
@RequiredArgsConstructor
public class OrderModelServiceImpl implements OrderModelService {

    private final HotelOrderRemote hotelOrderRemote;

    private final SlsLoggerUtil slsLoggerUtil;

    private final CreateTokenUtil createTokenUtil;

    private final HttpServletRequest httpServletRequest;

    private final DhubConfig dhubConfig;

    private final OrderChangeWordOrderTaskRemote orderChangeWordOrderTaskRemote;

    @Resource
    private RocketMQTemplate rocketMQTemplate;

    /**
     * 请求上下文中获取国内海外标识
     * 在 RequestValidateIntercept 设置
     * @return
     */
    private Integer getDomesticOrOverseas() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            HttpServletRequest request = attributes.getRequest();
            return (Integer) request.getAttribute(Constant.DOMESTICOROVERSEAS);
        }
        return null;
    }


    /**
     * 试预订(可订检查)接口
     */
    @Override
    public ResponseResult<CheckBookingInfoResponse> checkBooking(CheckBookingInfoRequest request) {
        ResponseResult<CheckBookingInfoResponse> checkBookingResponseResponse = new ResponseResult<>(DhubReturnCodeEnum.SUCCESS.no, DhubReturnCodeEnum.SUCCESS.code);
        //房间数量校验
        CommonUtil.checkRoomNum(request.getRoomNum());
        //校验国籍
        if (StringUtils.isNotEmpty(request.getNationality())) {
            String nameByCode = NationalEnum.getNameByCode(request.getNationality());
            if (StringUtils.isEmpty(nameByCode)) {
                throw new CustomException(DhubReturnCodeEnum.NATIONALITY_CODE_ERROR.no, DhubReturnCodeEnum.NATIONALITY_CODE_ERROR.code, DhubReturnCodeEnum.NATIONALITY_CODE_ERROR.code);
            }
        }
        CheckBookingRequest checkBookingRequest = DTOConvert.INSTANCE.convert(request);

        CountryCodeReq req = new CountryCodeReq();
        req.setHotelId(request.getHotelId());


        // 获取客户类型
        Integer domesticOrOverseas = request.getDomesticOrOverseas() != null ? request.getDomesticOrOverseas() : getDomesticOrOverseas();

        checkBookingRequest.setSupplyType(String.valueOf(domesticOrOverseas));
        checkBookingRequest.setSource(Constant.ACTIVE_1);
        //如果是海外，房间客人数量信息必传
        if (domesticOrOverseas == 2) {
            RoomGuestNumberDto roomGuestNumberDto = request.getRoomGuestNumber();
            //校验房间人数信息
            checkRoom(roomGuestNumberDto);

            //组装天宫格式
            List<RoomGuestNumber> roomGuestNumbers = new ArrayList<>();
            // int guestCount = 0;
            for (int i = 0; i < request.getRoomNum(); i++) {
                RoomGuestNumber dto = new RoomGuestNumber();
                dto.setRoomIndex(i + 1);
                if (StrUtilX.isNotEmpty(roomGuestNumberDto.getChildrenAges())) {
                    List<ChildrenInfo> childrenInfos = new ArrayList<>();
                    ChildrenInfo childrenInfo;
                    for (String age : roomGuestNumberDto.getChildrenAges().split(",")) {
                        childrenInfo = new ChildrenInfo();
                        childrenInfo.setChildrenAge(Integer.valueOf(age));
                        childrenInfos.add(childrenInfo);
                    }
                    dto.setChildrenInfos(childrenInfos);
                }
                dto.setAdultNum(roomGuestNumberDto.getNumberOfAdults());
                roomGuestNumbers.add(dto);
            }
            checkBookingRequest.setRoomGuestNumbers(roomGuestNumbers);
        } else {
            if (domesticOrOverseas == 1) {
                List<RoomGuestNumber> roomGuestNumbers = new ArrayList<>();
                for (int i = 0; i < request.getRoomNum(); i++) {
                    RoomGuestNumber roomGuestNumber = new RoomGuestNumber();
                    roomGuestNumber.setRoomIndex(i + 1);
                    if (request.getRoomGuestNumber() != null && request.getRoomGuestNumber().getNumberOfAdults() != null) {
                        roomGuestNumber.setAdultNum(request.getRoomGuestNumber().getNumberOfAdults());
                    } else {
                        roomGuestNumber.setAdultNum(2);
                    }
                    roomGuestNumbers.add(roomGuestNumber);
                }
                checkBookingRequest.setRoomGuestNumbers(roomGuestNumbers);
            }
        }

        if(dhubConfig.getExpedia()){
            createTokenUtil.expediaTokenAndIp(httpServletRequest,request.getPartnerCode());
        }

        checkBookingRequest.setUserAccount(request.getPartnerCode());
        request.setRequestId(UUID.randomUUID().toString());
        checkBookingRequest.setRequestId(request.getRequestId());
        checkBookingRequest.setNationality(request.getNationality());
        checkBookingRequest.setLanguage(request.getLanguage());
        com.tiangong.dis.dto.ResponseResult<CheckBookingResponse> checkBookingResponseResponseResult = hotelOrderRemote.checkBooking(checkBookingRequest);
        CommonUtil.checkResponseResult(checkBookingResponseResponseResult);
        if (!DhubReturnCodeEnum.SUCCESS.no.equals(checkBookingResponseResponseResult.getReturnCode())) {
            if (StrUtil.isNotBlank(checkBookingResponseResponseResult.getFailCode())) {
                DhubReturnCodeEnum errorCodeByEnum = DhubReturnCodeEnum.getEnumByErrorCode(checkBookingResponseResponseResult.getFailCode());
                throw new CustomException(errorCodeByEnum.getNo(), errorCodeByEnum.getCode(), errorCodeByEnum.getCode());
            }else {
                DhubReturnCodeEnum errorCodeByEnum = DhubReturnCodeEnum.getEnumByErrorCode(checkBookingResponseResponseResult.getReturnCode());
                throw new CustomException(errorCodeByEnum.getNo(), errorCodeByEnum.getCode(), errorCodeByEnum.getCode());
            }
        }
        if (checkBookingResponseResponseResult.getBussinessResponse() != null) {
            CheckBookingInfoResponse checkBookingInfoResponse = convertCheckBooking(domesticOrOverseas, checkBookingResponseResponseResult.getBussinessResponse());
            checkBookingResponseResponse.setBussinessResponse(checkBookingInfoResponse);
        }
        return checkBookingResponseResponse;
    }

    public void checkRoom(RoomGuestNumberDto roomGuestNumberDto) {
        if (Objects.isNull(roomGuestNumberDto)) {
            throw new CustomException(DhubReturnCodeEnum.EMPTY_PARAM_ROOM_GUEST_NUMBER.no, DhubReturnCodeEnum.EMPTY_PARAM_ROOM_GUEST_NUMBER.code, DhubReturnCodeEnum.EMPTY_PARAM_ROOM_GUEST_NUMBER.code);
        }

        if (roomGuestNumberDto.getNumberOfAdults() == null) {
            throw new CustomException(DhubReturnCodeEnum.EMPTY_PARAM_NUMBER_OF_ADULTS.no, DhubReturnCodeEnum.EMPTY_PARAM_NUMBER_OF_ADULTS.code, DhubReturnCodeEnum.EMPTY_PARAM_NUMBER_OF_ADULTS.code);
        }
        if (roomGuestNumberDto.getNumberOfChildren() != null && roomGuestNumberDto.getNumberOfChildren() > 0) {
            String childrenAges = roomGuestNumberDto.getChildrenAges();
            if (StrUtilX.isEmpty(childrenAges)) {
                throw new CustomException(DhubReturnCodeEnum.EMPTY_PARAM_CHILDREN_AGES.no, DhubReturnCodeEnum.EMPTY_PARAM_CHILDREN_AGES.code, DhubReturnCodeEnum.EMPTY_PARAM_CHILDREN_AGES.code);
            }
            if (!Pattern.compile("^\\d+(,\\d+)*$").matcher(childrenAges).matches()) {
                throw new CustomException(DhubReturnCodeEnum.EMPTY_PARAM_CHILDREN_AGES_TYPE_ERROR.no, DhubReturnCodeEnum.EMPTY_PARAM_CHILDREN_AGES_TYPE_ERROR.code, DhubReturnCodeEnum.EMPTY_PARAM_CHILDREN_AGES_TYPE_ERROR.code);
            }
            //校验儿童数量和年龄数量是否一致
            String[] agesNumber = childrenAges.split(",");
            if (agesNumber.length != roomGuestNumberDto.getNumberOfChildren()) {
                throw new CustomException(DhubReturnCodeEnum.EMPTY_PARAM_CHILDREN_AGES_AND_NUM_ERROR.no, DhubReturnCodeEnum.EMPTY_PARAM_CHILDREN_AGES_AND_NUM_ERROR.code, DhubReturnCodeEnum.EMPTY_PARAM_CHILDREN_AGES_AND_NUM_ERROR.code);
            }
            for (String age : agesNumber) {
                //校验儿童年龄范围
                int childAge = Integer.parseInt(age);
                if (childAge < 0 || childAge > 17) {
                    throw new CustomException(DhubReturnCodeEnum.CHILDREN_AGE_ERROR.no, DhubReturnCodeEnum.CHILDREN_AGE_ERROR.code, DhubReturnCodeEnum.CHILDREN_AGE_ERROR.code);
                }
            }
        }
        if (roomGuestNumberDto.getNumberOfChildren() == null && !StringUtils.isEmpty(roomGuestNumberDto.getChildrenAges())) {
            throw new CustomException(DhubReturnCodeEnum.EMPTY_PARAM_CHILDREN_AGES_AND_NUM_ERROR.no, DhubReturnCodeEnum.EMPTY_PARAM_CHILDREN_AGES_AND_NUM_ERROR.code, DhubReturnCodeEnum.EMPTY_PARAM_CHILDREN_AGES_AND_NUM_ERROR.code);
        }
    }

    private CheckBookingInfoResponse convertCheckBooking(Integer domesticOrOverseas, CheckBookingResponse checkBookingResponse) {
        CheckBookingInfoResponse checkBookingInfoResponse = new CheckBookingInfoResponse();
        Integer canBook = checkBookingResponse.getCanBook();
        if (canBook == null) {
            checkBookingInfoResponse.setCanBook(0);
        } else {
            checkBookingInfoResponse.setCanBook(checkBookingResponse.getCanBook());
        }
        checkBookingInfoResponse.setRatePlanId(checkBookingResponse.getRatePlanId());
        checkBookingInfoResponse.setTotalSalePrice(checkBookingResponse.getTotalSalePrice());
        List<PriceInfoDetail> priceItems = checkBookingResponse.getPriceItems();
        if (CollUtilX.isEmpty(priceItems)) {
            throw new CustomException(DhubReturnCodeEnum.SUPPLY_RETURN_RESULT_FORMAT_ERROR.no, DhubReturnCodeEnum.SUPPLY_RETURN_RESULT_FORMAT_ERROR.code, DhubReturnCodeEnum.SUPPLY_RETURN_RESULT_FORMAT_ERROR.code);
        }
        List<OrderPriceItem> orderPriceItems = new ArrayList<>();

        OrderPriceItem orderPriceItem;
        for (PriceInfoDetail priceItem : priceItems) {
            orderPriceItem = new OrderPriceItem();
            orderPriceItem.setSaleDate(priceItem.getDate());
            orderPriceItem.setSalePrice(priceItem.getSalePrice());
            orderPriceItem.setCurrency(SettlementCurrencyEnum.getCodeByKey(String.valueOf(priceItem.getSettlementCurrency())));
            orderPriceItem.setBreakfastNum(priceItem.getBreakfastNum());
            orderPriceItem.setBreakfastType(priceItem.getBreakfastType());
            orderPriceItem.setRoomStatus(priceItem.getRoomStatus());
            orderPriceItem.setQuotaNum(priceItem.getQuotaNum());
            orderPriceItems.add(orderPriceItem);
        }
        checkBookingInfoResponse.setPriceItems(orderPriceItems);

        // 到店另付
        if (domesticOrOverseas == 2 && CollUtilX.isNotEmpty(checkBookingResponse.getOrderRoomDetails())) {
            BigDecimal payInStorePrice = BigDecimal.ZERO;
            String payInStoreCurrency = null;
            for (OrderRoomDetailDto orderRoomDetail : checkBookingResponse.getOrderRoomDetails()) {
                if (StrUtilX.isEmpty(payInStoreCurrency)) {
                    payInStoreCurrency = SettlementCurrencyEnum.getCodeByKey(String.valueOf(orderRoomDetail.getPayInStoreCurrency()));
                }
                if (orderRoomDetail.getPayInStorePrice() != null) {
                    payInStorePrice = payInStorePrice.add(orderRoomDetail.getPayInStorePrice());
                }
            }
            if (payInStoreCurrency != null) {
                checkBookingInfoResponse.setPayAtHotelFee(payInStorePrice);
                checkBookingInfoResponse.setPayAtHotelFeeCurrency(payInStoreCurrency);
            }
        }

        return checkBookingInfoResponse;
    }


    /**
     * 创建订单
     */
    @Override
    public ResponseResult<CreateOrderInfoResponse> createOrder(CreateOrderInfoRequest request) {
        // 参数校验
        this.checkCreateOrderParam(request);

        ResponseResult<CreateOrderInfoResponse> createOrderResponseResponseResult = new ResponseResult<>(DhubReturnCodeEnum.SUCCESS.no, DhubReturnCodeEnum.SUCCESS.code);

        // 1国内 2海外
        CountryCodeReq req = new CountryCodeReq();
        req.setHotelId(request.getHotelId());

        // 获取客户类型
        Integer domesticOrOverseas = request.getDomesticOrOverseas() != null ? request.getDomesticOrOverseas() : getDomesticOrOverseas();

        //校验房间信息
        if (domesticOrOverseas == 2) {
            RoomGuestNumberDto roomGuestNumberDto = request.getRoomGuestNumber();
            //校验房间人数信息
            checkRoom(roomGuestNumberDto);
            //组装天宫格式
            List<RoomGuestNumberDto> roomGuestNumbers = new ArrayList<>();
            for (int i = 0; i < request.getRoomNum(); i++) {
                RoomGuestNumberDto dto = new RoomGuestNumberDto();
                dto.setNumberOfAdults(roomGuestNumberDto.getNumberOfAdults());
                dto.setChildrenAges(roomGuestNumberDto.getChildrenAges());
                dto.setNumberOfChildren(roomGuestNumberDto.getNumberOfChildren());
                dto.setRoomNumber(i + 1);
                roomGuestNumbers.add(dto);
            }
            //校验手机号区号--全球必填校验
//            if (StringUtils.isEmpty(request.getLinkCountryCode())) {
//                throw new CustomException(ReturnCodeEnum.EMPTY_PARAM_LINKCOUNTRYCODE.no, ReturnCodeEnum.EMPTY_PARAM_LINKCOUNTRYCODE.code, ReturnCodeEnum.EMPTY_PARAM_LINKCOUNTRYCODE.description);
//            }
            request.setRoomGuestNumbers(roomGuestNumbers);
            List<GuestInfo> guestInfos = request.getGuestInfos();
            if (guestInfos == null || guestInfos.size() == 0) {
                throw new CustomException(DhubReturnCodeEnum.GUEST_NOT_EMPTY.no, DhubReturnCodeEnum.GUEST_NOT_EMPTY.code, DhubReturnCodeEnum.GUEST_NOT_EMPTY.code);
            }
            for (GuestInfo guestInfo : guestInfos) {
                if(StrUtilX.isNotEmpty(guestInfo.getGuestName())){
                    guestInfo.setGuestName(guestInfo.getGuestName().trim());
                }
                if (StrUtilX.isEmpty(guestInfo.getFirstName()) || StrUtilX.isEmpty(guestInfo.getLastName())) {
                    throw new CustomException(DhubReturnCodeEnum.FIRST_NAME_AND_LAST_NAME_NOT_EMPTY.no, DhubReturnCodeEnum.FIRST_NAME_AND_LAST_NAME_NOT_EMPTY.code, DhubReturnCodeEnum.FIRST_NAME_AND_LAST_NAME_NOT_EMPTY.code);
                }
                guestInfo.setFirstName(guestInfo.getFirstName().trim());
                guestInfo.setLastName(guestInfo.getLastName().trim());
                if (StrUtilX.isEmpty(guestInfo.getFirstName()) || StrUtilX.isEmpty(guestInfo.getLastName())) {
                    throw new CustomException(DhubReturnCodeEnum.FIRST_NAME_AND_LAST_NAME_NOT_EMPTY.no, DhubReturnCodeEnum.FIRST_NAME_AND_LAST_NAME_NOT_EMPTY.code, DhubReturnCodeEnum.FIRST_NAME_AND_LAST_NAME_NOT_EMPTY.code);
                }
                // 客户为全球，姓和名只能为字母+空格
                String nameRegex = "^[A-Za-z\\s]+$";

                if (!guestInfo.getLastName().matches(nameRegex)) {
                    throw new CustomException(DhubReturnCodeEnum.LASTNAME_ONLY_USE_ENGLISH_LETTERS_SPACES.no, DhubReturnCodeEnum.LASTNAME_ONLY_USE_ENGLISH_LETTERS_SPACES.code, DhubReturnCodeEnum.LASTNAME_ONLY_USE_ENGLISH_LETTERS_SPACES.code);
                }
                if (!guestInfo.getFirstName().matches(nameRegex)) {
                    throw new CustomException(DhubReturnCodeEnum.FIRSTNAME_ONLY_USE_ENGLISH_LETTERS_SPACES.no, DhubReturnCodeEnum.FIRSTNAME_ONLY_USE_ENGLISH_LETTERS_SPACES.code, DhubReturnCodeEnum.FIRSTNAME_ONLY_USE_ENGLISH_LETTERS_SPACES.code);
                }
                if (NationalityEnum.nationalityCheck(guestInfo.getNationality()) == null) {
                    throw new CustomException(DhubReturnCodeEnum.NATIONALITY_ERROR.no, DhubReturnCodeEnum.NATIONALITY_ERROR.code, DhubReturnCodeEnum.NATIONALITY_ERROR.code);
                }
                if (IdCardTypeEnum.idCardTypeCheck(guestInfo.getIdCardType()) == null) {
                    throw new CustomException(DhubReturnCodeEnum.ID_CARD_TYPE_ERROR.no, DhubReturnCodeEnum.ID_CARD_TYPE_ERROR.code, DhubReturnCodeEnum.ID_CARD_TYPE_ERROR.code);
                }
                if (guestInfo.getIdCardType() != null && StringUtils.isEmpty(guestInfo.getIdCardNo())) {
                    throw new CustomException(DhubReturnCodeEnum.ID_CARD_TYPE_AND_CARD_ERROR.no, DhubReturnCodeEnum.ID_CARD_TYPE_AND_CARD_ERROR.code, DhubReturnCodeEnum.ID_CARD_TYPE_AND_CARD_ERROR.code);
                }
                //手机号区号不为空，则手机号不为空
                if (StringUtils.isNotEmpty(guestInfo.getCountryCode())) {
                    //不为空，则校验格式
                    String regex = "^[0-9]+$";
                    if (!guestInfo.getCountryCode().matches(regex)) {
                        throw new CustomException(DhubReturnCodeEnum.COUNTRYCODE_FORMAT_ERROR.no, DhubReturnCodeEnum.COUNTRYCODE_FORMAT_ERROR.code, DhubReturnCodeEnum.COUNTRYCODE_FORMAT_ERROR.code);
                    }
                }
            }
        } else {
            //国内：guestinfos:国内城市酒店 guestname或lastname+firstname二选一必填。海外+港澳台 lastname+firstname必填。lastname+firstname 只接受英文。
            List<GuestInfo> guestInfos = request.getGuestInfos();
            if (guestInfos == null || guestInfos.size() == 0) {
                throw new CustomException(DhubReturnCodeEnum.GUEST_NOT_EMPTY.no, DhubReturnCodeEnum.GUEST_NOT_EMPTY.code, DhubReturnCodeEnum.GUEST_NOT_EMPTY.code);
            }
            for (GuestInfo guestInfo : guestInfos) {
                if ((StrUtilX.isEmpty(guestInfo.getFirstName()) || StrUtilX.isEmpty(guestInfo.getLastName())) && StrUtilX.isEmpty(guestInfo.getGuestName())) {
                    throw new CustomException(DhubReturnCodeEnum.FIRST_NAME_AND_LAST_NAME_OR_GUEST_NAME_NOT_EMPTY.no, DhubReturnCodeEnum.FIRST_NAME_AND_LAST_NAME_OR_GUEST_NAME_NOT_EMPTY.code, DhubReturnCodeEnum.FIRST_NAME_AND_LAST_NAME_OR_GUEST_NAME_NOT_EMPTY.code);
                }
                if (NationalityEnum.nationalityCheck(guestInfo.getNationality()) == null) {
                    throw new CustomException(DhubReturnCodeEnum.NATIONALITY_ERROR.no, DhubReturnCodeEnum.NATIONALITY_ERROR.code, DhubReturnCodeEnum.NATIONALITY_ERROR.code);
                }
                if (IdCardTypeEnum.idCardTypeCheck(guestInfo.getIdCardType()) == null) {
                    throw new CustomException(DhubReturnCodeEnum.ID_CARD_TYPE_ERROR.no, DhubReturnCodeEnum.ID_CARD_TYPE_ERROR.code, DhubReturnCodeEnum.ID_CARD_TYPE_ERROR.code);
                }
                if (guestInfo.getIdCardType() != null && StringUtils.isEmpty(guestInfo.getIdCardNo())) {
                    throw new CustomException(DhubReturnCodeEnum.ID_CARD_TYPE_AND_CARD_ERROR.no, DhubReturnCodeEnum.ID_CARD_TYPE_AND_CARD_ERROR.code, DhubReturnCodeEnum.ID_CARD_TYPE_AND_CARD_ERROR.code);
                }
            }
        }

        if(dhubConfig.getExpedia()){
            createTokenUtil.expediaTokenAndIp(httpServletRequest,request.getPartnerCode());
        }

        // 构建创建订单参数
        CreateOrderRequest createOrderRequest = createCreateOrderRequest(request, domesticOrOverseas);
        createOrderRequest.setSupplyType(String.valueOf(domesticOrOverseas));
        Date date = new Date();
        com.tiangong.dis.dto.ResponseResult<CreateOrderResponse> createOrderRemoteResopnse = hotelOrderRemote.createOrder(createOrderRequest);
        CommonUtils.saveInfoSlsLog(date, Constant.TIANGONG + "-createOrder", JSON.toJSONString(createOrderRequest), JSON.toJSONString(createOrderRemoteResopnse), "1", request.getPartnerCode());
        CommonUtil.checkResponseResult(createOrderRemoteResopnse);
        if (!DhubReturnCodeEnum.SUCCESS.no.equals(createOrderRemoteResopnse.getReturnCode())) {
            if (StrUtil.isNotBlank(createOrderRemoteResopnse.getFailCode())) {
                DhubReturnCodeEnum errorCodeByEnum = DhubReturnCodeEnum.getEnumByErrorCode(createOrderRemoteResopnse.getFailCode());
                throw new CustomException(errorCodeByEnum.getNo(), errorCodeByEnum.getCode(), errorCodeByEnum.getCode());
            }else {
                DhubReturnCodeEnum errorCodeByEnum = DhubReturnCodeEnum.getEnumByErrorCode(createOrderRemoteResopnse.getReturnCode());
                throw new CustomException(errorCodeByEnum.getNo(), errorCodeByEnum.getCode(), errorCodeByEnum.getCode());
            }
        }
        CreateOrderResponse bussinessResponse = createOrderRemoteResopnse.getBussinessResponse();
        if (bussinessResponse != null) {
            if (bussinessResponse.getResult() != null && bussinessResponse.getResult() == Constant.ACTIVE_1
                    && StrUtilX.isEmpty(bussinessResponse.getFcOrderCode())) {
                throw new CustomException(DhubReturnCodeEnum.CREATE_ORDER_FAIL.no, DhubReturnCodeEnum.CREATE_ORDER_FAIL.code, DhubReturnCodeEnum.CREATE_ORDER_FAIL.code);
            }
            createOrderResponseResponseResult.setBussinessResponse(DTOConvert.INSTANCE.convert(createOrderRemoteResopnse.getBussinessResponse()));
        }
        return createOrderResponseResponseResult;
    }

    /**
     * 校验创建订单参数
     */
    private void checkCreateOrderParam(CreateOrderInfoRequest request) {
        // 校验入离日期
        try {
            if (CheckInTypeEnum.checkType(request.getCheckInType()) == null) {
                throw new CustomException(DhubReturnCodeEnum.CHECK_IN_TYPE_ERROR.no, DhubReturnCodeEnum.CHECK_IN_TYPE_ERROR.code, DhubReturnCodeEnum.CHECK_IN_TYPE_ERROR.code);
            }
            //房间数量校验
            CommonUtil.checkRoomNum(request.getRoomNum());

            if (GuaranteeFlagEnum.guaranteeFlag(request.getGuaranteeFlag()) == null) {
                throw new CustomException(DhubReturnCodeEnum.GUARANTEE_FLAG_ERROR.no, DhubReturnCodeEnum.GUARANTEE_FLAG_ERROR.code, DhubReturnCodeEnum.GUARANTEE_FLAG_ERROR.code);
            }
            if (VipOrderEnum.vipOrderCheck(request.getIsVipOrder()) == null) {
                throw new CustomException(DhubReturnCodeEnum.VIP_ORDER_ERROR.no, DhubReturnCodeEnum.VIP_ORDER_ERROR.code, DhubReturnCodeEnum.VIP_ORDER_ERROR.code);
            }
            if (TravelTypeEnum.travelTypeCheck(request.getTravelType()) == null) {
                throw new CustomException(DhubReturnCodeEnum.TRAVEL_TYPE_ERROR.no, DhubReturnCodeEnum.TRAVEL_TYPE_ERROR.code, DhubReturnCodeEnum.TRAVEL_TYPE_ERROR.code);
            }
            // 入住日期
            Date checkInDate = DateUtilX.stringToDate(request.getCheckInDate());
            // 离店日期
            Date checkOutDate = DateUtilX.stringToDate(request.getCheckOutDate());

            // 校验入住日期、离店日期是否大于当前时间，离店日期是否大于入住日期
            CommonUtils.checkDateValidity(checkInDate, checkOutDate);

            if (CollUtilX.isNotEmpty(request.getGuestInfos())) {
                for (GuestInfo guestInfo : request.getGuestInfos()) {
                    if (guestInfo.getRoomNumber() != null && !guestInfo.getRoomNumber().matches("\\d+")) {
                        //客人信息房间序号必须是数字
                        throw new CustomException(DhubReturnCodeEnum.HUB_ROOM_NUMBER_ERROR.no, DhubReturnCodeEnum.HUB_ROOM_NUMBER_ERROR.code, DhubReturnCodeEnum.HUB_ROOM_NUMBER_ERROR.code);
                    } else if (guestInfo.getRoomNumber() != null && Integer.parseInt(guestInfo.getRoomNumber()) > request.getRoomNum()) {
                        //客人信息房间序号不能大于总房间数
                        throw new CustomException(DhubReturnCodeEnum.HUB_ROOM_NUMBER_MISMATCHING.no, DhubReturnCodeEnum.HUB_ROOM_NUMBER_MISMATCHING.code, DhubReturnCodeEnum.HUB_ROOM_NUMBER_MISMATCHING.code);
                    }
                }
            }


            // 每日日期
            List<Date> dateList = DateUtilX.getDateList(checkInDate, DateUtilX.getAddDay(checkOutDate, -1));

            // 价格日期
            Set<Date> priceDateSet = new HashSet<>();
            for (CreateOrderPriceItemDto priceItem : request.getPriceItems()) {
                Date date = DateUtilX.stringToDate(priceItem.getSaleDate());
                // 判断日期是否在入离日期范围内
                if (!dateList.contains(date)) {
                    throw new CustomException(DhubReturnCodeEnum.INVALID_DATE.no, DhubReturnCodeEnum.INVALID_DATE.code, DhubReturnCodeEnum.INVALID_DATE.code);
                }
                priceDateSet.add(date);
            }
            // 判断那些日期不存在
            for (Date date : dateList) {
                if (!priceDateSet.contains(date)) {
                    throw new CustomException(DhubReturnCodeEnum.NOT_EXIST_PRICE_DATE.no, DhubReturnCodeEnum.NOT_EXIST_PRICE_DATE.code, DateUtilX.dateToString(date) + DhubReturnCodeEnum.NOT_EXIST_PRICE_DATE.code);
                }
            }

            //校验联系人手机号区号正则
            if (StringUtils.isNotEmpty(request.getLinkCountryCode())) {
                String regex = "^[0-9]+$";
                if (!request.getLinkCountryCode().matches(regex)) {
                    throw new CustomException(DhubReturnCodeEnum.LINKCOUNTRYCODE_FORMAT_ERROR.no, DhubReturnCodeEnum.LINKCOUNTRYCODE_FORMAT_ERROR.code, DhubReturnCodeEnum.LINKCOUNTRYCODE_FORMAT_ERROR.code);
                }
            }
            //校验国籍
            if (StringUtils.isNotEmpty(request.getNationality())) {
                String nameByCode = NationalEnum.getNameByCode(request.getNationality());
                if (StringUtils.isEmpty(nameByCode)) {
                    throw new CustomException(DhubReturnCodeEnum.NATIONALITY_CODE_ERROR.no, DhubReturnCodeEnum.NATIONALITY_CODE_ERROR.code, DhubReturnCodeEnum.NATIONALITY_CODE_ERROR.code);
                }
            }
        } catch (CustomException e) {
            throw e;
        } catch (Exception e) {
            throw new CustomException(DhubReturnCodeEnum.INVALID_DATE.no, DhubReturnCodeEnum.INVALID_DATE.code, DhubReturnCodeEnum.INVALID_DATE.code);
        }

    }

    /**
     * 构建创建订单参数
     */
    private CreateOrderRequest createCreateOrderRequest(CreateOrderInfoRequest request, Integer domesticOrOverseas) {
        CreateOrderRequest createOrderRequest = new CreateOrderRequest();
        createOrderRequest.setAgentCode(request.getAgentCode());
        createOrderRequest.setPartnerCode(request.getPartnerCode());
        createOrderRequest.setHotelId(request.getHotelId());
        createOrderRequest.setRoomId(request.getRoomId());
        createOrderRequest.setRatePlanId(request.getRatePlanId());
        if (request.getCheckInType() == null || Objects.equals(CheckInTypeEnum.ALL_DAY_ROOM.dhubKey, request.getCheckInType())) {
            createOrderRequest.setHourlyRoom(CheckInTypeEnum.ALL_DAY_ROOM.tiangongkey);
        } else {
            createOrderRequest.setHourlyRoom(CheckInTypeEnum.CLOCK_ROOM.tiangongkey);
        }
        createOrderRequest.setCheckInDate(request.getCheckInDate());
        createOrderRequest.setCheckOutDate(request.getCheckOutDate());
        createOrderRequest.setRoomNum(request.getRoomNum());
        List<String> bedTypeList = StrUtilX.stringToList(request.getBedType(), ",");
        if (CollUtilX.isNotEmpty(bedTypeList)) {
            List<BedInfoDto> bedInfos = new ArrayList<>();
            for (String bedCode : bedTypeList) {
                BedInfoDto bedInfoDto = new BedInfoDto();
                bedInfoDto.setBedTypeCode(bedCode);
                String name = BedTypesEnum.getValueByKey(bedCode);
                if (StringUtils.isEmpty(name)) {
                    throw new CustomException(DhubReturnCodeEnum.BED_TYPE_ERROR.no, DhubReturnCodeEnum.BED_TYPE_ERROR.code, bedCode + ":" + DhubReturnCodeEnum.BED_TYPE_ERROR.code);
                }
                bedInfoDto.setBedTypeName(name);
                bedInfos.add(bedInfoDto);
            }
            createOrderRequest.setBedInfos(bedInfos);
        }
        createOrderRequest.setTotalAmount(request.getTotalAmount());
        createOrderRequest.setCoOrderCode(request.getCoOrderCode());
        createOrderRequest.setPayAtHotelFlag(request.getPayAtHotelFlag());
        createOrderRequest.setGuaranteeFlag(request.getGuaranteeFlag());
        createOrderRequest.setLinkMan(request.getLinkMan());
        createOrderRequest.setLinkPhone(request.getLinkPhone());
        createOrderRequest.setLinkCountryCode(request.getLinkCountryCode());
        createOrderRequest.setEmail(request.getEmail());
        createOrderRequest.setArriveTime(request.getArriveTime() + "~" + request.getLatestArriveTime());
        createOrderRequest.setSupplyCode(request.getSupplyCode());
        createOrderRequest.setRemark(request.getRemark());
        createOrderRequest.setSpecialDemand(request.getSpecialDemand());
        List<CreateOrderPriceItem> createOrderPriceItems = DTOConvert.INSTANCE.CreateOrderPriceConvert(request.getPriceItems());
        //校验早餐类型
        for (CreateOrderPriceItem priceItem : createOrderPriceItems) {
            if (BreakfastTypeEnum.breakfastTypeCheck(priceItem.getBreakfastType()) == null) {
                throw new CustomException(DhubReturnCodeEnum.BREAKFAST_TYPE_ERROR.no, DhubReturnCodeEnum.BREAKFAST_TYPE_ERROR.code, DhubReturnCodeEnum.BREAKFAST_TYPE_ERROR.code);
            }
        }
        createOrderRequest.setPriceItems(createOrderPriceItems);
        createOrderRequest.setPayStatus(request.getPayStatus());
        createOrderRequest.setIsVipOrder(request.getIsVipOrder());
        createOrderRequest.setTravelType(request.getTravelType());
        createOrderRequest.setSdistributorName(request.getSdistributorName());
        createOrderRequest.setChannelCode(StrUtilX.isNotEmpty(request.getChannelCode()) ? request.getChannelCode() :ChannelEnum.DHUB.key);
        createOrderRequest.setChannelName(StrUtilX.isNotEmpty(request.getChannelName()) ? request.getChannelName() : ChannelEnum.DHUB.key);
        createOrderRequest.setBedTypeDiff(request.getBedTypeDiff());
        //入住人
        Map<Integer, List<GuestInfo>> guestInfoMap = new HashMap<>();
        List<GuestInfo> guestInfos = request.getGuestInfos();

        // 房间数量 当没有传入客人的指定房间号时处理;
        boolean flag = false;
        for (GuestInfo guestInfo : guestInfos) {
            int number;
            if (!StringUtils.isEmpty(guestInfo.getRoomNumber())) {
                number = Integer.parseInt(guestInfo.getRoomNumber());
                guestInfo.setRoomNumber(String.valueOf(number));
                if (guestInfoMap.containsKey(number)) {
                    guestInfoMap.get(number).add(guestInfo);
                } else {
                    List<GuestInfo> guestInfoList = new ArrayList<>();
                    guestInfoList.add(guestInfo);
                    guestInfoMap.put(number, guestInfoList);
                }
            } else {
                // 存在客人没有房间号 重新进行分配
                flag = true;
                guestInfoMap.clear();
                break;
            }
        }
        // 存在客人未指定房间号 则进行分配
        if (flag) {
            // 房间数量
            Integer roomNum = request.getRoomNum();
            // 客人数量
            int guesuNum = guestInfos.size();
            // 每个房间平均分配的人员数量
            int guestsPerRoom = guesuNum / roomNum;
            // 多余客人
            int extraGuests = guesuNum % roomNum;
            // 房间号
            int roomAssignment;
            for (int i = 0; i < guesuNum; i++) {
                if (guestsPerRoom > extraGuests) {
                    if (i < (guestsPerRoom * roomNum)) {
                        // 计算当前客人应该分配到哪个房间
                        roomAssignment = (i / guestsPerRoom) + 1; // 房间号从1开始
                    } else {
                        // 超过平均分配的客人，由最后一个房间处理多余的客人
                        roomAssignment = roomNum; // 最后一个房间
                    }
                } else {
                    // 根据当前客人索引确定分配的房间
                    if (i < extraGuests * roomNum) {
                        roomAssignment = (i / extraGuests) + 1; // 计算当前客人应该分配到哪个房间
                    } else {
                        roomAssignment = roomNum; // 超出部分都分给最后一个房间
                    }
                }
                GuestInfo guestInfo = guestInfos.get(i);
                guestInfo.setRoomNumber(String.valueOf(roomAssignment));
                if (guestInfoMap.containsKey(roomAssignment)) {
                    guestInfoMap.get(roomAssignment).add(guestInfo);
                } else {
                    List<GuestInfo> guestInfoList = new ArrayList<>();
                    guestInfoList.add(guestInfo);
                    guestInfoMap.put(roomAssignment, guestInfoList);
                }
            }
        }

        boolean b = CommonUtils.validateIncrement(new ArrayList<>(guestInfoMap.keySet()));
        if (!b) {
            throw new CustomException(DhubReturnCodeEnum.HUB_ROOM_NUMBER_ERROR.no, DhubReturnCodeEnum.HUB_ROOM_NUMBER_ERROR.code, DhubReturnCodeEnum.HUB_ROOM_NUMBER_ERROR.code);
        }
        List<OrderRoomGuestDTO> roomGuestList = new ArrayList<>();
        OrderRoomGuestDTO orderRoomGuestDTO;
        //如果是海外，房间客人数量信息必传
        if (domesticOrOverseas == 2) {
            int roomNumber = 0;
            for (RoomGuestNumberDto roomGuestNumberDto : request.getRoomGuestNumbers()) {
                orderRoomGuestDTO = new OrderRoomGuestDTO();
                if (roomGuestNumberDto.getRoomNumber() == null) {
                    roomNumber += 1;
                } else {
                    roomNumber = roomGuestNumberDto.getRoomNumber();
                }
                orderRoomGuestDTO.setRoomNumber(roomNumber);
                orderRoomGuestDTO.setAdultQty(roomGuestNumberDto.getNumberOfAdults());
                orderRoomGuestDTO.setChildrenQty(roomGuestNumberDto.getNumberOfChildren());
                orderRoomGuestDTO.setChildrenAge(roomGuestNumberDto.getChildrenAges());
                if (guestInfoMap.containsKey(roomNumber) && guestInfoMap.get(roomNumber) != null) {
                    List<OrderGuestDTO> orderGuestList = new ArrayList<>();
                    for (GuestInfo guestInfo : guestInfoMap.get(roomNumber)) {
                        OrderGuestDTO orderGuestDTO = createOrderGuestDTO(guestInfo);
                        orderGuestList.add(orderGuestDTO);
                    }
                    orderRoomGuestDTO.setOrderGuestList(orderGuestList);
                } else {
                    throw new CustomException(DhubReturnCodeEnum.HUB_ROOM_NUMBER_MISMATCHING.no, DhubReturnCodeEnum.HUB_ROOM_NUMBER_MISMATCHING.code, DhubReturnCodeEnum.HUB_ROOM_NUMBER_MISMATCHING.code);
                }
                roomGuestList.add(orderRoomGuestDTO);
            }
        } else {
            for (Map.Entry<Integer, List<GuestInfo>> stringListEntry : guestInfoMap.entrySet()) {
                Integer roomNumber = stringListEntry.getKey();
                orderRoomGuestDTO = new OrderRoomGuestDTO();
                //orderRoomGuestDTO.setRoomNumber(roomNumber);
                orderRoomGuestDTO.setAdultQty(2);
                orderRoomGuestDTO.setRoomNumber(roomNumber);
                List<OrderGuestDTO> orderGuestList = new ArrayList<>();
                for (GuestInfo guestInfo : stringListEntry.getValue()) {
                    OrderGuestDTO orderGuestDTO = createOrderGuestDTO(guestInfo);
                    orderGuestList.add(orderGuestDTO);
                }
                orderRoomGuestDTO.setOrderGuestList(orderGuestList);
                roomGuestList.add(orderRoomGuestDTO);
            }
        }
        createOrderRequest.setRoomGuestList(roomGuestList);
        createOrderRequest.setLanguage(request.getLanguage());
        createOrderRequest.setQuickProcessingSwitch(request.getRapidProcessing());
        createOrderRequest.setSupplierLabel(request.getSupplierLabel());
        createOrderRequest.setUserAccount(request.getPartnerCode());

        request.setRequestId(UUID.randomUUID().toString());
        createOrderRequest.setRequestId(request.getRequestId());
        createOrderRequest.setNationality(request.getNationality());
        createOrderRequest.setServiceCharge(request.getServiceCharge());
        return createOrderRequest;
    }

    /**
     * 构建订单入住人信息
     */
    private OrderGuestDTO createOrderGuestDTO(GuestInfo guestInfo) {
        OrderGuestDTO orderGuestDTO = new OrderGuestDTO();
        orderGuestDTO.setName(guestInfo.getGuestName());
        orderGuestDTO.setNationality(guestInfo.getNationality());
        orderGuestDTO.setMobileNo(guestInfo.getMobileNo());
        orderGuestDTO.setCountryCode(guestInfo.getCountryCode());
        orderGuestDTO.setIdCardNo(guestInfo.getIdCardNo());
        orderGuestDTO.setIdCardType(guestInfo.getIdCardType());
        orderGuestDTO.setMembershipCardNumber(guestInfo.getMembershipCardNumber());
        orderGuestDTO.setRoomNumber(Integer.valueOf(guestInfo.getRoomNumber()));
        orderGuestDTO.setFirstName(guestInfo.getFirstName());
        orderGuestDTO.setLastName(guestInfo.getLastName());
        return orderGuestDTO;
    }


    /**
     * 取消订单
     */
    @Override
    public ResponseResult<CancelOrderInfoResponse> cancelOrder(CancelOrderInfoRequest request) {
        ResponseResult<CancelOrderInfoResponse> cancelOrderResponseResponseResult = new ResponseResult<>(DhubReturnCodeEnum.SUCCESS.no, DhubReturnCodeEnum.SUCCESS.code);
        if (StringUtils.isEmpty(request.getFcOrderCode()) && StringUtils.isEmpty(request.getCoOrderCode())) {
            throw new CustomException(DhubReturnCodeEnum.FC_ORDER_CODE_OR_OC_ORDER_CODE_NOT_EMPTY.no, DhubReturnCodeEnum.FC_ORDER_CODE_OR_OC_ORDER_CODE_NOT_EMPTY.code, DhubReturnCodeEnum.FC_ORDER_CODE_OR_OC_ORDER_CODE_NOT_EMPTY.code);
        }

        if(dhubConfig.getExpedia()){
            createTokenUtil.expediaTokenAndIp(httpServletRequest,request.getPartnerCode());
        }

        request.setRequestId(UUID.randomUUID().toString());
        CancelOrderRequest cancelOrderRequest = DTOConvert.INSTANCE.convert(request);
        cancelOrderRequest.setChannelCode(StrUtilX.isNotEmpty(request.getChannelCode()) ? request.getChannelCode() : ChannelEnum.DHUB.key);
        com.tiangong.dis.dto.ResponseResult<CancelOrderResponse> cancelOrderRemoteResponse = hotelOrderRemote.cancelOrder(cancelOrderRequest);
        CommonUtil.checkResponseResult(cancelOrderRemoteResponse);
        if (!DhubReturnCodeEnum.SUCCESS.no.equals(cancelOrderRemoteResponse.getReturnCode())) {
            if (StrUtil.isNotBlank(cancelOrderRemoteResponse.getFailCode())) {
                DhubReturnCodeEnum errorCodeByEnum = DhubReturnCodeEnum.getEnumByErrorCode(cancelOrderRemoteResponse.getFailCode());
                throw new CustomException(errorCodeByEnum.getNo(), errorCodeByEnum.getCode(), errorCodeByEnum.getCode());
            } else {
                DhubReturnCodeEnum errorCodeByEnum = DhubReturnCodeEnum.getEnumByErrorCode(cancelOrderRemoteResponse.getReturnCode());
                throw new CustomException(errorCodeByEnum.getNo(), errorCodeByEnum.getCode(), errorCodeByEnum.getCode());
            }
        }
        if (cancelOrderRemoteResponse.getBussinessResponse() != null) {
            CancelOrderInfoResponse cancelOrderInfoResponse = DTOConvert.INSTANCE.convert(cancelOrderRemoteResponse.getBussinessResponse());
            cancelOrderResponseResponseResult.setBussinessResponse(cancelOrderInfoResponse);
        }
        return cancelOrderResponseResponseResult;
    }

    /**
     * 订单详情查询
     */
    @Override
    public ResponseResult<OrderDetailInfoResponse> queryOrderDetail(OrderDetailInfoRequest request) {
        ResponseResult<OrderDetailInfoResponse> orderDetailResponseResponseResult = new ResponseResult<>(DhubReturnCodeEnum.SUCCESS.no, DhubReturnCodeEnum.SUCCESS.code);
        if (StringUtils.isEmpty(request.getFcOrderCode()) && StringUtils.isEmpty(request.getCoOrderCode())) {
            throw new CustomException(DhubReturnCodeEnum.FC_ORDER_CODE_OR_OC_ORDER_CODE_NOT_EMPTY.no, DhubReturnCodeEnum.FC_ORDER_CODE_OR_OC_ORDER_CODE_NOT_EMPTY.code, DhubReturnCodeEnum.FC_ORDER_CODE_OR_OC_ORDER_CODE_NOT_EMPTY.code);
        }

        if(dhubConfig.getExpedia()){
            createTokenUtil.expediaTokenAndIp(httpServletRequest,request.getPartnerCode());
        }

        OrderDetailRequest orderDetailRequest = new OrderDetailRequest();
        orderDetailRequest.setCoOrderCode(request.getCoOrderCode());
        orderDetailRequest.setFcOrderCode(request.getFcOrderCode());
        request.setRequestId(UUID.randomUUID().toString());
        orderDetailRequest.setRequestId(request.getRequestId());
        orderDetailRequest.setAgentCode(request.getAgentCode());
        orderDetailRequest.setChannelCode(StrUtilX.isNotEmpty(request.getChannelCode()) ? request.getChannelCode() : ChannelEnum.DHUB.key);
        Date date = new Date();
        com.tiangong.dis.dto.ResponseResult<OrderDetailResponse> queryOrderDetailRemoteResponse = hotelOrderRemote.queryAPIOrderDetail(orderDetailRequest);
        CommonUtils.saveInfoSlsLog(date, Constant.TIANGONG + "-queryOrderDetail", JSON.toJSONString(orderDetailRequest), JSON.toJSONString(queryOrderDetailRemoteResponse), "1", request.getPartnerCode());
        CommonUtil.checkResponseResult(queryOrderDetailRemoteResponse);
        if (!DhubReturnCodeEnum.SUCCESS.no.equals(queryOrderDetailRemoteResponse.getReturnCode())) {
            if (StrUtil.isNotBlank(queryOrderDetailRemoteResponse.getFailCode())) {
                DhubReturnCodeEnum errorCodeByEnum = DhubReturnCodeEnum.getEnumByErrorCode(queryOrderDetailRemoteResponse.getFailCode());
                throw new CustomException(errorCodeByEnum.getNo(), errorCodeByEnum.getCode(), errorCodeByEnum.getCode());
            }else {
                DhubReturnCodeEnum errorCodeByEnum = DhubReturnCodeEnum.getEnumByErrorCode(queryOrderDetailRemoteResponse.getReturnCode());
                throw new CustomException(errorCodeByEnum.getNo(), errorCodeByEnum.getCode(), errorCodeByEnum.getCode());
            }

        }
        if (queryOrderDetailRemoteResponse.getBussinessResponse() != null) {
            OrderDetailInfoResponse orderDetailInfoResponse = new OrderDetailInfoResponse();
            convertOrderDetail(queryOrderDetailRemoteResponse.getBussinessResponse(), orderDetailInfoResponse);

            orderDetailResponseResponseResult.setBussinessResponse(orderDetailInfoResponse);
        }
        return orderDetailResponseResponseResult;
    }

    /**
     * 转换订单详情
     */
    private void convertOrderDetail(OrderDetailResponse orderDetailResponse, OrderDetailInfoResponse orderDetailInfoResponse) {
        orderDetailInfoResponse.setOrderStatus(orderDetailResponse.getOrderStatus());
        orderDetailInfoResponse.setChannelState(orderDetailResponse.getChannelState());
        orderDetailInfoResponse.setHotelConfirmNo(orderDetailResponse.getHotelConfirmNo());
        orderDetailInfoResponse.setHotelId(orderDetailResponse.getHotelId());
        orderDetailInfoResponse.setHotelName(orderDetailResponse.getHotelName());
        orderDetailInfoResponse.setRoomId(orderDetailResponse.getRoomId());
        orderDetailInfoResponse.setRoomName(orderDetailResponse.getRoomName());
        orderDetailInfoResponse.setRatePlanId(orderDetailResponse.getRatePlanId());
        orderDetailInfoResponse.setRatePlanName(orderDetailResponse.getRatePlanName());
        if (orderDetailResponse.getTotalBaseAmount() != null) {
            orderDetailInfoResponse.setTotalBasePrice(BigDecimal.valueOf(orderDetailResponse.getTotalBaseAmount()));
        }
        orderDetailInfoResponse.setBaseCurrency(SettlementCurrencyEnum.getCodeByKey(String.valueOf(orderDetailResponse.getBaseCurrency())));

        if (CollUtilX.isNotEmpty(orderDetailResponse.getFeeList())) {
            List<OrderFeeDto> feeList = new ArrayList<>();
            for (OrderFeeDTO orderFeeDTO : orderDetailResponse.getFeeList()) {
                OrderFeeDto orderFee = new OrderFeeDto();
                orderFee.setRoomNumber(orderFeeDTO.getRoomNumber());
                orderFee.setGuestList(orderFeeDTO.getGuestList());
                orderFee.setAdultQty(orderFeeDTO.getAdultQty());
                orderFee.setChildrenQty(orderFeeDTO.getChildrenQty());
                orderFee.setStartDate(orderFeeDTO.getStartDate());
                orderFee.setEndDate(orderFeeDTO.getEndDate());
                orderFee.setPayAtHotelFee(orderFeeDTO.getPayAtHotelFee());
                orderFee.setPayAtHotelCurrencyCode(SettlementCurrencyEnum.getCodeByKey(String.valueOf(orderFeeDTO.getPayAtHotelCurrency())));


                if (CollUtilX.isNotEmpty(orderFeeDTO.getPriceList())) {
                    List<OrderFeeDetailDto> priceList = new ArrayList<>();
                    for (OrderFeeDetailDTO orderFeeDetailDTO : orderFeeDTO.getPriceList()) {
                        OrderFeeDetailDto orderFeeDetailDto = new OrderFeeDetailDto();
                        orderFeeDetailDto.setSaleDate(orderFeeDetailDTO.getSaleDate());
                        orderFeeDetailDto.setCurrencyCode(SettlementCurrencyEnum.getCodeByKey(String.valueOf(orderFeeDetailDTO.getCurrency())));

                        orderFeeDetailDto.setSalePrice(orderFeeDetailDTO.getSalePrice());
                        orderFeeDetailDto.setRoomPrice(orderFeeDetailDTO.getRoomPrice());
                        orderFeeDetailDto.setTimesTaxFee(orderFeeDetailDTO.getTimesTaxFee());
                        orderFeeDetailDto.setDiscount(orderFeeDetailDTO.getDiscount());
                        orderFeeDetailDto.setTaxList(convertTax(orderFeeDetailDTO.getTaxList()));
                        orderFeeDetailDto.setTimesTaxFeeList(convertTax(orderFeeDetailDTO.getTimesTaxFeeList()));
                        priceList.add(orderFeeDetailDto);
                    }
                    orderFee.setPriceList(priceList);
                }
                feeList.add(orderFee);
            }
            orderDetailInfoResponse.setFeeList(feeList);
        }

        orderDetailInfoResponse.setCheckInDate(orderDetailResponse.getCheckInDate());
        orderDetailInfoResponse.setCheckOutDate(orderDetailResponse.getCheckOutDate());
        orderDetailInfoResponse.setRoomNum(orderDetailResponse.getRoomNum());
        Integer hourlyRoom = orderDetailResponse.getHourlyRoom();
        orderDetailInfoResponse.setCheckInType(CheckInTypeEnum.getDhubByTiangongkey(hourlyRoom));
        HourlyRoomDetail hourlyRoomDetail = orderDetailResponse.getHourlyRoomDetail();

        if (hourlyRoom != null && hourlyRoom == Constant.ACTIVE_1 && hourlyRoomDetail != null) {
            //钟点房
            if (hourlyRoomDetail.getCheckInTime() != null) {
                try {
                    orderDetailInfoResponse.setArriveTime(DateUtilX.dateToString(DateUtilX.stringToDate(hourlyRoomDetail.getCheckInTime(), "yyyy-MM-dd HH:mm:ss"), "HH:mm"));
                    orderDetailInfoResponse.setLatestArriveTime(DateUtilX.dateToString(DateUtilX.stringToDate(hourlyRoomDetail.getCheckOutTime(), "yyyy-MM-dd HH:mm:ss"), "HH:mm"));
                } catch (Exception e) {
                    log.error("钟点房到到店日期格式有误,订单编码：{}", orderDetailInfoResponse.getFcOrderCode(), e);
                }
            }
        } else {
            //全日房
            orderDetailInfoResponse.setArriveTime(orderDetailResponse.getArriveTime());
            orderDetailInfoResponse.setLatestArriveTime(orderDetailResponse.getLatestArriveTime());
        }
        orderDetailInfoResponse.setTotalAmount(orderDetailResponse.getTotalAmount());
        String currency = SettlementCurrencyEnum.getCodeByKey(String.valueOf(orderDetailResponse.getCurrency()));
        orderDetailInfoResponse.setCurrency(currency);
        orderDetailInfoResponse.setPayAtHotelFee(orderDetailResponse.getPayAtHotelFee());
        orderDetailInfoResponse.setPayAtHotelFeeCurrency(orderDetailResponse.getPayAtHotelCurrencyCode());
        if (CollUtilX.isNotEmpty(orderDetailResponse.getPriceItems())) {
            List<QueryOrderPriceItem> queryOrderPriceItems = DTOConvert.INSTANCE.convert(orderDetailResponse.getPriceItems());
            for (QueryOrderPriceItem queryOrderPriceItem : queryOrderPriceItems) {
                queryOrderPriceItem.setCurrency(currency);
            }
            orderDetailInfoResponse.setPriceItems(queryOrderPriceItems);
        }

        if (CollUtilX.isNotEmpty(orderDetailResponse.getGuestInfos())) {
            List<GuestInfo> guestInfos = new ArrayList<>();
            GuestInfo guest;
            for (com.tiangong.dto.order.request.GuestInfo guestInfo : orderDetailResponse.getGuestInfos()) {
                guest = new GuestInfo();
                guest.setGuestName(guestInfo.getGuestName());
                guest.setLastName(guestInfo.getLastName());
                guest.setFirstName(guestInfo.getFirstName());
                guest.setRoomNumber(guestInfo.getRoomNumber());
                guest.setNationality(guestInfo.getNationality());
                //guest.setGender(guestInfo.getGender());
                guest.setIdCardNo(guestInfo.getIdCardNo());
                guest.setIdCardType(guestInfo.getIdCardType());
                guest.setMobileNo(guestInfo.getMobileNo());
                guest.setMembershipCardNumber(guestInfo.getMembershipCardNumber());
                guestInfos.add(guest);
            }
            orderDetailInfoResponse.setGuestInfos(guestInfos);
        }

        if (CollUtilX.isNotEmpty(orderDetailResponse.getRoomGuestDTOS())) {
            List<RoomGuestNumberDto> roomGuestNumbers = new ArrayList<>();
            RoomGuestNumberDto roomGuestNumberDto;
            for (OrderRoomGuestDTO roomGuestDTO : orderDetailResponse.getRoomGuestDTOS()) {
                roomGuestNumberDto = new RoomGuestNumberDto();
                roomGuestNumberDto.setRoomNumber(roomGuestDTO.getRoomNumber());
                roomGuestNumberDto.setNumberOfAdults(roomGuestDTO.getAdultQty());
                roomGuestNumberDto.setNumberOfChildren(roomGuestDTO.getChildrenQty());
                roomGuestNumberDto.setChildrenAges(roomGuestDTO.getChildrenAge());
                roomGuestNumbers.add(roomGuestNumberDto);
            }
            orderDetailInfoResponse.setRoomGuestNumbers(roomGuestNumbers);
        }

        orderDetailInfoResponse.setLinkMan(orderDetailResponse.getLinkMan());
        orderDetailInfoResponse.setLinkPhone(orderDetailResponse.getLinkPhone());
        orderDetailInfoResponse.setEmail(orderDetailResponse.getEmail());
        orderDetailInfoResponse.setInvoiceModel(orderDetailResponse.getInvoiceModel());
        InvoiceInfo invoiceInfo = orderDetailResponse.getInvoiceInfo();
        if (invoiceInfo != null) {
            InvoiceInfoDto invoiceInfoDto = new InvoiceInfoDto();
            invoiceInfoDto.setInvoiceType(invoiceInfo.getInvoiceType());
            invoiceInfoDto.setInvoiceHeader(invoiceInfo.getInvoiceHeader());
            invoiceInfoDto.setAmount(invoiceInfo.getAmount());
            invoiceInfoDto.setCompanyTax(invoiceInfo.getCompanyTax());
            invoiceInfoDto.setRegisterAddress(invoiceInfo.getRegisterAddress());
            invoiceInfoDto.setCompanyTel(invoiceInfo.getCompanyTel());
            invoiceInfoDto.setBank(invoiceInfo.getBank());
            invoiceInfoDto.setBankAccount(invoiceInfo.getBankAccount());
            InvoicePickup invoicePickup = invoiceInfo.getInvoicePickup();
            if (invoicePickup != null) {
                InvoicePickupDto invoicePickupDto = new InvoicePickupDto();
                invoicePickupDto.setGetMethod(invoicePickup.getGetMethod());
                invoicePickupDto.setReceiveName(invoicePickup.getReceiveName());
                invoicePickupDto.setTelePhone(invoicePickup.getTelePhone());
                invoicePickupDto.setAddress(invoicePickup.getAddress());
                invoicePickupDto.setEmail(invoicePickup.getEmail());
                invoiceInfoDto.setInvoicePickup(invoicePickupDto);
            }
            //invoiceInfoDto.setInvoiceUrlList();
            orderDetailInfoResponse.setInvoiceInfo(invoiceInfoDto);
        }
        orderDetailInfoResponse.setQuickProcessingSwitch(orderDetailResponse.getQuickProcessingSwitch());
        orderDetailInfoResponse.setRapidProcessing(orderDetailResponse.getQuickProcessingSwitch());
//        orderDetailInfoResponse.setSupplierLabel(orderDetailResponse.getSupplierLabel());
        orderDetailInfoResponse.setProductLabel(orderDetailResponse.getSupplierLabel());
        orderDetailInfoResponse.setBedType(orderDetailResponse.getBedType());
        orderDetailInfoResponse.setBedTypeDiff(orderDetailResponse.getBedTypeDiff());
        orderDetailInfoResponse.setFcOrderCode(orderDetailResponse.getFcOrderCode());
        orderDetailInfoResponse.setCoOrderCode(orderDetailResponse.getCoOrderCode());
        orderDetailInfoResponse.setPenaltiesValue(orderDetailResponse.getPenaltiesValue());
        orderDetailInfoResponse.setServiceCharge(orderDetailResponse.getServiceCharge());
        orderDetailInfoResponse.setCreateTime(orderDetailResponse.getCreateTime());
    }

    /**
     * 转换汇率信息对象
     */
    private TaxDto convertTax(TaxDTO taxDto) {
        if (taxDto == null) {
            return null;
        }
        TaxDto result = new TaxDto();
        result.setTax(taxDto.getTax());
        result.setSalesTax(taxDto.getSalesTax());
        result.setExtraTaxFee(taxDto.getExtraTaxFee());
        return result;
    }

    /**
     * 订单操作推送接口
     */
    @Override
    public Response<Object> orderStatusPush(OrderStatusPushRequest request) {
        String req = null;
        String resp = null;
        String url = null;
        String errorMsg = null;
        // 增加重试，但是不向下游输出
        Boolean retry = request.getRetry();
        request.setRetry(null);
        Response<Object> response = Response.error(ErrorCodeEnum.NOTIFY_DISTRIBUTOR_FAIL.errorCode, ErrorCodeEnum.NOTIFY_DISTRIBUTOR_FAIL.errorDesc);
        try {

            if (StrUtilX.isEmpty(request.getPartnerCode())) {
                response = Response.error(ParamErrorEnum.EMPTY_PARAM_PARTNERCODE.getCode(), ParamErrorEnum.EMPTY_PARAM_PARTNERCODE.getCode());
                return response;
            }
            AgentAccountConfig agentAccountConfig = AgentConfig.getAgentAccount(request.getPartnerCode());
            if (agentAccountConfig == null) {
                response = Response.error(ErrorCodeEnum.PARTNER_CONFIG_NOT_EXIST.errorCode, ErrorCodeEnum.PARTNER_CONFIG_NOT_EXIST.errorCode);
                return response;
            }
            if (StrUtil.isBlank(agentAccountConfig.getOrderStatusNotifyUrl())) {
                response = Response.error(ErrorCodeEnum.PUSH_ORDER_STATUS_URL_IS_EMPTY.errorCode, ErrorCodeEnum.PUSH_ORDER_STATUS_URL_IS_EMPTY.errorCode);
                return response;
            }
            Request<OrderStatusPushRequest> requestRequest = new Request<>();
            requestRequest.setBusinessRequest(request);
            Header header = getHeader(agentAccountConfig, ApiNameEnum.orderStatusPush);
            requestRequest.setHeader(header);
            req = JSONUtil.toJsonStr(requestRequest);
            url = agentAccountConfig.getOrderStatusNotifyUrl();

            RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(60000).setConnectTimeout(10000).build();
            resp = HttpUtilX.post(url, req, requestConfig);
            if (StrUtilX.isNotEmpty(resp)) {
                ResponseResult<OrderStatusPushResponse> result = JSONObject.parseObject(resp, new TypeReference<ResponseResult<OrderStatusPushResponse>>() {
                });
                if (Objects.nonNull(result) && Objects.nonNull(result.getBussinessResponse()) && Objects.equals(result.getReturnCode(), DhubReturnCodeEnum.SUCCESS.no)
                        && Objects.equals(result.getBussinessResponse().getReceiveStatus(), 1)) {
                    response = Response.success();
                }
            }

        } catch (Exception e) {
            log.error("订单操作推送接口异常", e);
            errorMsg = StrUtilX.getStackTraceAsString(e);
        } finally {
            if(retry != null && retry){
                SaveOrderLogDTO saveOrderLogDTO = new SaveOrderLogDTO();
                saveOrderLogDTO.setOrderCode(request.getFcOrderCode());
                saveOrderLogDTO.setTarget(com.tiangong.common.Constant.SYSTEM);
                saveOrderLogDTO.setCreatedBy(com.tiangong.common.Constant.SYSTEM);
                StringBuilder content = new StringBuilder();
                content.append("操作");
                Integer orderStatus = request.getOrderStatus();
                // 获取操作
                String orderStatusDesc = getOrderStatusDesc(orderStatus);
                if (response.getFailCode().equals(ParamErrorEnum.EMPTY_PARAM_PARTNERCODE.getCode())) {
                    content.append(orderStatusDesc).append("订单，订单状态推送失败，原因：").append(response.getFailReason());
                    saveOrderLogDTO.setPushOrderStatus(1);
                } else if (response.getFailCode().equals(ErrorCodeEnum.PARTNER_CONFIG_NOT_EXIST.errorCode)) {
                    content.append(orderStatusDesc).append("订单，订单状态推送失败，原因：").append(response.getFailReason());
                    saveOrderLogDTO.setPushOrderStatus(1);
                }else if (response.getFailCode().equals(ErrorCodeEnum.PUSH_ORDER_STATUS_URL_IS_EMPTY.errorCode)) {
                    // 未配置订单推送接口 记录日志，不进行重试
                    content.append(orderStatusDesc).append("订单，订单状态推送失败，原因：").append(response.getFailReason());
                    saveOrderLogDTO.setPushOrderStatus(1);
                } else {
                    // 记录日志
                    content.append(orderStatusDesc).append("订单，推送订单状态:").append(response.getFailReason());
                }
                saveOrderLogDTO.setContent(content.toString());
                orderChangeWordOrderTaskRemote.saveOrderLog(saveOrderLogDTO);
                //错误并且不是 未配置地址||信息为空 的这两种情况不触发重试，其他情况重试
                if (response.isError()
                        && !response.getFailCode().equals(ParamErrorEnum.EMPTY_PARAM_PARTNERCODE.getCode())
                        && !response.getFailCode().equals(ErrorCodeEnum.PARTNER_CONFIG_NOT_EXIST.errorCode)
                        && !response.getFailCode().equals(ErrorCodeEnum.PUSH_ORDER_STATUS_URL_IS_EMPTY.errorCode)) {
                    // 发送MQ 延迟1分钟 重试
                    rocketMQTemplate.syncSendDelayTimeMills(MqConstants.ORDER_STATUS_PUSH, JSON.toJSONString(request), 60000);
                }
            }

            // SLS日志接入
            Map<String, String> map = new HashMap<>();
            map.put(SlsEnum.LEVEL.getType(), Constant.SLSLOGLEVEL);
            map.put(SlsEnum.NAME.getType(), Constant.APPLICATIONNAME);
            map.put("url", url);
            map.put("request", req);
            map.put("requestStr", JSONUtil.toJsonStr(request));
            map.put("response", resp);
            map.put("errorMsg", errorMsg);
            map.put("partnerCode", request.getPartnerCode());
            map.put(SlsEnum.MESSAGE.getType(), "orderStatusPush");
            slsLoggerUtil.saveLog(map, "orderStatusPush", Constant.APPLICATIONNAME);
        }
        return response;
    }

    private String getOrderStatusDesc(Integer orderStatus){
        switch (orderStatus) {
            case 3:
                return "确认";
            case 4:
                return "拒绝";
            case 6:
                return "取消";
            case 7:
                return "取消失败";
            default:
                return "未知";
        }
    }

    /**
     * 获取响应头
     */
    private Header getHeader(AgentAccountConfig agentAccountConfig, ApiNameEnum apiNameEnum) {
        Header header = new Header();
        header.setTimestamp(System.currentTimeMillis());
        header.setPartnerCode(agentAccountConfig.getPartnerCode());
        header.setRequestType(apiNameEnum.apiName);
        header.setVersion("1.0.0");
        String str = header.getTimestamp() + header.getPartnerCode() +
                Md5Util.md5Encode(agentAccountConfig.getSecretkey()).toUpperCase() + header.getRequestType();
        header.setSignature(str);
        return header;
    }

    /**
     * 组装请求头--新
     */
    private Header getHeaderNew(AgentAccountConfig agentAccountConfig, ApiNameEnum apiNameEnum) {
        Header header = new Header();
        header.setTimestamp(System.currentTimeMillis());
        header.setPartnerCode(agentAccountConfig.getPartnerCode());
        header.setRequestType(apiNameEnum.apiName);
        header.setVersion("1.0.0");
        String str = Md5Util.md5Encode(header.getTimestamp() + header.getPartnerCode() + Md5Util.md5Encode(agentAccountConfig.getSecretkey()).toUpperCase() + header.getRequestType()).toUpperCase();
        header.setSignature(str);
        return header;
    }

    /**
     * 订单支付接口
     */
    @Override
    public ResponseResult<PayOrderInfoResponse> payOrder(PayOrderInfoRequest request) {
        ResponseResult<PayOrderInfoResponse> responseResponseResult = new ResponseResult<>(DhubReturnCodeEnum.SUCCESS.no, DhubReturnCodeEnum.SUCCESS.code);
        if (StringUtils.isEmpty(request.getFcOrderCode()) && StringUtils.isEmpty(request.getCoOrderCode())) {
            throw new CustomException(DhubReturnCodeEnum.FC_ORDER_CODE_OR_OC_ORDER_CODE_NOT_EMPTY.no, DhubReturnCodeEnum.FC_ORDER_CODE_OR_OC_ORDER_CODE_NOT_EMPTY.code, DhubReturnCodeEnum.FC_ORDER_CODE_OR_OC_ORDER_CODE_NOT_EMPTY.code);
        }
        // 校验支付订单参数
        checkPayOrderParam(request);

        if (StrUtilX.isNotEmpty(request.getAgentCode())) {
            request.setAgentCode(request.getAgentCode());
        } else {
            // 查询客户配置
            AgentAccountConfig partnerInfo = AgentConfig.getAgentAccount(request.getPartnerCode());
            if (partnerInfo == null) {
                throw new CustomException(DhubReturnCodeEnum.PARTNER_CONFIG_NOT_EXIST.no, DhubReturnCodeEnum.PARTNER_CONFIG_NOT_EXIST.code, DhubReturnCodeEnum.PARTNER_CONFIG_NOT_EXIST.code);
            }
            request.setAgentCode(partnerInfo.getAgentCode());
        }

        PayOrderRequest payOrderRequest = DTOConvert.INSTANCE.payOrderInfoConvert(request);
        if (StrUtilX.isEmpty(request.getChannelCode())) {
            payOrderRequest.setChannelCode(ChannelEnum.DHUB.key);
        }
        com.tiangong.dis.dto.ResponseResult<PayOrderResponse> payOrderRemoteResponse = hotelOrderRemote.payOrder(payOrderRequest);
        CommonUtil.checkResponseResult(payOrderRemoteResponse);
        if (!DhubReturnCodeEnum.SUCCESS.no.equals(payOrderRemoteResponse.getReturnCode())) {
            if(StrUtil.isNotBlank(payOrderRemoteResponse.getFailCode())){
                DhubReturnCodeEnum errorCodeByEnum = DhubReturnCodeEnum.getEnumByErrorCode(payOrderRemoteResponse.getFailCode());
                throw new CustomException(errorCodeByEnum.getNo(), errorCodeByEnum.getCode(), errorCodeByEnum.getCode());
            }else{
                DhubReturnCodeEnum errorCodeByEnum = DhubReturnCodeEnum.getEnumByErrorCode(payOrderRemoteResponse.getReturnCode());
                throw new CustomException(errorCodeByEnum.getNo(), errorCodeByEnum.getCode(), errorCodeByEnum.getCode());
            }
        }
        if (payOrderRemoteResponse.getBussinessResponse() != null) {
            responseResponseResult.setBussinessResponse(DTOConvert.INSTANCE.convert(payOrderRemoteResponse.getBussinessResponse()));
        }
        return responseResponseResult;
    }

    /**
     * 校验支付订单参数
     */
    private void checkPayOrderParam(PayOrderInfoRequest request) {
        if (request.getIsMixedPay() != null) {
            if (!(request.getIsMixedPay() == 1 || request.getIsMixedPay() == 0)) {
                throw new CustomException(DhubReturnCodeEnum.MIXED_PAY_ERROR.no, DhubReturnCodeEnum.MIXED_PAY_ERROR.code, DhubReturnCodeEnum.MIXED_PAY_ERROR.code);
            }
        }

        // 校验混合支付
        if (request.getIsMixedPay() != null && request.getIsMixedPay() == 1) {
            if (request.getPayAmount() == null) {
                throw new CustomException(DhubReturnCodeEnum.PAY_AMOUNT_TOTAL_NOT_EMPTY.no, DhubReturnCodeEnum.PAY_AMOUNT_TOTAL_NOT_EMPTY.code, DhubReturnCodeEnum.PAY_AMOUNT_TOTAL_NOT_EMPTY.code);
            }

            // 混合支付必须有两种方式，否则响应失败
            if (CollUtilX.isEmpty(request.getMixedPayList()) || request.getMixedPayList().size() != 2) {
                throw new CustomException(DhubReturnCodeEnum.INVALID_INPUTPARAM.no, DhubReturnCodeEnum.INVALID_INPUTPARAM.code, DhubReturnCodeEnum.INVALID_INPUTPARAM.code);
            }

            StringBuilder payMethod = new StringBuilder();
            BigDecimal totalAmount = BigDecimal.ZERO;

            for (MixedPayInfo mixedPayInfo : request.getMixedPayList()) {
                if (mixedPayInfo.getPayMethod() == null) {
                    throw new CustomException(DhubReturnCodeEnum.PAY_METHOD_NOT_EMPTY.no, DhubReturnCodeEnum.PAY_METHOD_NOT_EMPTY.code, DhubReturnCodeEnum.PAY_METHOD_NOT_EMPTY.code);
                }
                if (PayMethodEnum.payMethodCheck(mixedPayInfo.getPayMethod()) == null) {
                    throw new CustomException(DhubReturnCodeEnum.PAY_METHOD_ERROR.no, DhubReturnCodeEnum.PAY_METHOD_ERROR.code, DhubReturnCodeEnum.PAY_METHOD_ERROR.code);
                }
                payMethod.append(mixedPayInfo.getPayMethod());
                if (mixedPayInfo.getPayAmount() == null) {
                    throw new CustomException(DhubReturnCodeEnum.PAY_AMOUNT_NOT_EMPTY.no, DhubReturnCodeEnum.PAY_AMOUNT_NOT_EMPTY.code, DhubReturnCodeEnum.PAY_AMOUNT_NOT_EMPTY.code);
                }

                totalAmount = totalAmount.add(mixedPayInfo.getPayAmount());
            }

            // 混合支付两个支付方式不能相同，否则失败
            if (!payMethod.toString().equals("12") && !payMethod.toString().equals("21")) {
                throw new CustomException(DhubReturnCodeEnum.PAY_METHOD_ERROR.no, DhubReturnCodeEnum.PAY_METHOD_ERROR.code, DhubReturnCodeEnum.PAY_METHOD_ERROR.code);
            }

            // 支付金额和需等于订单总金额
            if (totalAmount.compareTo(request.getPayAmount()) != 0) {
                throw new CustomException(DhubReturnCodeEnum.PAY_AMOUNT_SUM_NEQ_ORDER_AMOUNT.no, DhubReturnCodeEnum.PAY_AMOUNT_SUM_NEQ_ORDER_AMOUNT.code, DhubReturnCodeEnum.PAY_AMOUNT_SUM_NEQ_ORDER_AMOUNT.code);
            }
        } else if (request.getPayMethod() != null) {
            if (PayMethodEnum.payMethodCheck(request.getPayMethod()) == null) {
                throw new CustomException(DhubReturnCodeEnum.PAY_METHOD_ERROR.no, DhubReturnCodeEnum.PAY_METHOD_ERROR.code, DhubReturnCodeEnum.PAY_METHOD_ERROR.code);
            }
            if (request.getPayAmount() == null) {
                throw new CustomException(DhubReturnCodeEnum.PAY_AMOUNT_TOTAL_NOT_EMPTY.no, DhubReturnCodeEnum.PAY_AMOUNT_TOTAL_NOT_EMPTY.code, DhubReturnCodeEnum.PAY_AMOUNT_TOTAL_NOT_EMPTY.code);
            }
        }
    }

    /**
     * 订单入住详细情况查询接口
     */
    @Override
    public ResponseResult<OrderCheckDetailResponse> queryOrderCheckDetailInfo(OrderCheckRequest request) {
        ResponseResult<OrderCheckDetailResponse> orderCheckDetailInfoResponseResponseResult = new ResponseResult<>(DhubReturnCodeEnum.SUCCESS.no, DhubReturnCodeEnum.SUCCESS.code);
        if (StringUtils.isEmpty(request.getFcOrderCode()) && StringUtils.isEmpty(request.getCoOrderCode())) {
            throw new CustomException(DhubReturnCodeEnum.FC_ORDER_CODE_OR_OC_ORDER_CODE_NOT_EMPTY.no, DhubReturnCodeEnum.FC_ORDER_CODE_OR_OC_ORDER_CODE_NOT_EMPTY.code, DhubReturnCodeEnum.FC_ORDER_CODE_OR_OC_ORDER_CODE_NOT_EMPTY.code);
        }
        OrderCheckInfoRequest orderCheckInfoRequest = DTOConvert.INSTANCE.convert(request);
        orderCheckInfoRequest.setChannelCode(ChannelEnum.DHUB.key);
        com.tiangong.dis.dto.ResponseResult<OrderCheckDetailInfoResponse> orderCheckDetailInfoRemoteResponse = hotelOrderRemote.queryOrderCheckDetailInfo(orderCheckInfoRequest);
        CommonUtil.checkResponseResult(orderCheckDetailInfoRemoteResponse);
        if (!DhubReturnCodeEnum.SUCCESS.no.equals(orderCheckDetailInfoRemoteResponse.getReturnCode())) {
            if (StrUtil.isNotBlank(orderCheckDetailInfoRemoteResponse.getFailCode())) {
                DhubReturnCodeEnum errorCodeByEnum = DhubReturnCodeEnum.getEnumByErrorCode(orderCheckDetailInfoRemoteResponse.getFailCode());
                throw new CustomException(errorCodeByEnum.getNo(), errorCodeByEnum.getCode(), errorCodeByEnum.getCode());
            } else {
                DhubReturnCodeEnum errorCodeByEnum = DhubReturnCodeEnum.getEnumByErrorCode(orderCheckDetailInfoRemoteResponse.getReturnCode());
                throw new CustomException(errorCodeByEnum.getNo(), errorCodeByEnum.getCode(), errorCodeByEnum.getCode());
            }
        }
        if (orderCheckDetailInfoRemoteResponse.getBussinessResponse() != null) {
            OrderCheckDetailResponse orderCheckDetailResponse = DTOConvert.INSTANCE.convert(orderCheckDetailInfoRemoteResponse.getBussinessResponse());
            orderCheckDetailInfoResponseResponseResult.setBussinessResponse(orderCheckDetailResponse);
        }
        return orderCheckDetailInfoResponseResponseResult;
    }

    @Override
    public ResponseResult<CheckoutApplyResponse> checkoutApply(CheckoutApplyRequest request) {
        //校验参数是否合法
        Integer refundType = request.getRefundType();
        if (Objects.nonNull(refundType) && refundType.intValue() != RefundTypeEnum.WHOLE_ORDER.getKey() && refundType.intValue() != RefundTypeEnum.PART_ORDER.getKey()) {
            throw new CustomException(DhubReturnCodeEnum.INVALID_CHECKOUT_TYPE.no, DhubReturnCodeEnum.INVALID_CHECKOUT_TYPE.code, DhubReturnCodeEnum.INVALID_CHECKOUT_TYPE.code);
        }
        List<CheckoutRoomInfo> checkoutInfoList = request.getCheckoutRoomInfoList();
        for (CheckoutRoomInfo checkoutApplyInfo : checkoutInfoList) {
            String cancelCheckInDate = checkoutApplyInfo.getCancelCheckInDate();
            String[] split = cancelCheckInDate.split(",");
            for (String checkinDate : split) {
                if (!DateUtilX.isValidDate(checkinDate)) {
                    throw new CustomException(DhubReturnCodeEnum.DATE_FORMAT_INCORRECT.no, DhubReturnCodeEnum.DATE_FORMAT_INCORRECT.code, DhubReturnCodeEnum.DATE_FORMAT_INCORRECT.code);
                }
            }
        }
        ResponseResult<CheckoutApplyResponse> responseResult = new ResponseResult<>();
        CheckoutApplyInfoRequest checkoutApplyInfoRequest = DTOConvert.INSTANCE.convert(request);
        //查询客户编码
        AgentAccountConfig agentAccountConfig = AgentConfig.getAgentAccount(request.getPartnerCode());
        Assert.isTrue(Objects.nonNull(agentAccountConfig), "分销商配置不存在");
        checkoutApplyInfoRequest.setAgentCode(agentAccountConfig.getAgentCode());
        com.tiangong.dis.dto.ResponseResult<CheckoutApplyInfoResponse> checkoutApplyInfoResponse = hotelOrderRemote.checkoutApply(checkoutApplyInfoRequest);

        if (!DhubReturnCodeEnum.SUCCESS.no.equals(checkoutApplyInfoResponse.getReturnCode())) {
            DhubReturnCodeEnum errorCodeByEnum = DhubReturnCodeEnum.getEnumByErrorCode(checkoutApplyInfoResponse.getReturnCode());
            responseResult.setReturnCode(errorCodeByEnum.getNo());
            responseResult.setReturnMsg(errorCodeByEnum.getCode());
        } else {
            responseResult.setReturnCode(DhubReturnCodeEnum.SUCCESS.no);
            responseResult.setReturnMsg(DhubReturnCodeEnum.SUCCESS.code);
        }

        if (Objects.nonNull(checkoutApplyInfoResponse.getBussinessResponse())) {
            CheckoutApplyResponse checkoutApplyResponse = DTOConvert.INSTANCE.convert(checkoutApplyInfoResponse.getBussinessResponse());
            responseResult.setBussinessResponse(checkoutApplyResponse);
        }
        return responseResult;
    }

    @Override
    public ResponseResult<CheckoutDetailResponse> queryCheckoutDetail(CheckoutDetailRequest request) {
        ResponseResult<CheckoutDetailResponse> responseResult = new ResponseResult<>();
        CheckoutDetailInfoRequest checkoutDetailInfoRequest = DTOConvert.INSTANCE.convert(request);
        //查询客户编码
        AgentAccountConfig agentAccountConfig = AgentConfig.getAgentAccount(request.getPartnerCode());
        Assert.isTrue(Objects.nonNull(agentAccountConfig), "分销商配置不存在");
        checkoutDetailInfoRequest.setAgentCode(agentAccountConfig.getAgentCode());
        com.tiangong.dis.dto.ResponseResult<CheckoutDetailInfoResponse> checkoutDetailInfoResponse = hotelOrderRemote.queryCheckoutDetail(checkoutDetailInfoRequest);
        CommonUtil.checkResponseResult(checkoutDetailInfoResponse);
        if (!DhubReturnCodeEnum.SUCCESS.no.equals(checkoutDetailInfoResponse.getReturnCode())) {
            if (StrUtil.isNotBlank(checkoutDetailInfoResponse.getFailCode())) {
                DhubReturnCodeEnum errorCodeByEnum = DhubReturnCodeEnum.getEnumByErrorCode(checkoutDetailInfoResponse.getFailCode());
                throw new CustomException(errorCodeByEnum.getNo(), errorCodeByEnum.getCode(), errorCodeByEnum.getCode());
            }else {
                DhubReturnCodeEnum errorCodeByEnum = DhubReturnCodeEnum.getEnumByErrorCode(checkoutDetailInfoResponse.getReturnCode());
                throw new CustomException(errorCodeByEnum.getNo(), errorCodeByEnum.getCode(), errorCodeByEnum.getCode());
            }
        }

        if (Objects.nonNull(checkoutDetailInfoResponse.getBussinessResponse())) {
            CheckoutDetailResponse checkoutDetailResponse = DTOConvert.INSTANCE.convert(checkoutDetailInfoResponse.getBussinessResponse());
            responseResult.setBussinessResponse(checkoutDetailResponse);
        }
        return responseResult;
    }

    @Override
    public Response<Object> checkoutApplyStatusPush(CheckoutApplyStatusPushInfoRequest request) {
        String req = null;
        String resp = null;
        String url = null;
        String errorMsg = null;
        Response<Object> response = Response.error(ErrorCodeEnum.NOTIFY_DISTRIBUTOR_FAIL.errorCode, ErrorCodeEnum.NOTIFY_DISTRIBUTOR_FAIL.errorCode);
        try {
            if (StrUtilX.isEmpty(request.getPartnerCode())) {
                return Response.error(ParamErrorEnum.EMPTY_PARAM_PARTNERCODE.getCode(), ParamErrorEnum.EMPTY_PARAM_PARTNERCODE.getCode());
            }
            AgentAccountConfig agentAccountConfig = AgentConfig.getAgentAccount(request.getPartnerCode());
            if (agentAccountConfig == null) {
                return Response.error(ErrorCodeEnum.PARTNER_CONFIG_NOT_EXIST.errorCode, ErrorCodeEnum.PARTNER_CONFIG_NOT_EXIST.errorCode);
            }
            url = agentAccountConfig.getOrderCheckoutNotifyUrl();
            if (StrUtilX.isEmpty(url)) {
                return Response.error(ErrorCodeEnum.CHECKOUT_APPLY_STATUS_PUSH_ERROR.errorCode, ErrorCodeEnum.CHECKOUT_APPLY_STATUS_PUSH_ERROR.errorCode);
            }
            Request<CheckoutApplyStatusPushInfoRequest> requestRequest = new Request<>();
            requestRequest.setBusinessRequest(request);
            Header header = getHeader(agentAccountConfig, ApiNameEnum.checkoutApplyStatusPush);
            requestRequest.setHeader(header);
            req = JSONUtil.toJsonStr(requestRequest);
            resp = HttpUtilX.post(url, req);
            if (StrUtilX.isNotEmpty(resp)) {
                ResponseResult<OrderStatusPushResponse> result = JSONObject.parseObject(resp, new TypeReference<ResponseResult<OrderStatusPushResponse>>() {
                });
                if (Objects.nonNull(result) && Objects.nonNull(result.getBussinessResponse()) && Objects.equals(result.getReturnCode(), DhubReturnCodeEnum.SUCCESS.no)
                        && Objects.equals(result.getBussinessResponse().getReceiveStatus(), 1)) {
                    response = Response.success();
                }
            }
        } catch (Exception e) {
            log.error("订单退房申请状态通知推送接口异常", e);
            errorMsg = StrUtilX.getStackTraceAsString(e);
        } finally {
            //SLS日志接入
            Map<String, String> map = new HashMap<>();
            map.put(SlsEnum.LEVEL.getType(), Constant.SLSLOGLEVEL);
            map.put(SlsEnum.NAME.getType(), Constant.APPLICATIONNAME);
            map.put("url", url);
            map.put("request", req);
            map.put("requestStr", JSONUtil.toJsonStr(request));
            map.put("response", resp);
            map.put("errorMsg", errorMsg);
            map.put("partnerCode", request.getPartnerCode());
            map.put(SlsEnum.MESSAGE.getType(), "checkoutApplyStatusPush");
            slsLoggerUtil.saveLog(map, "checkoutApplyStatusPush", Constant.APPLICATIONNAME);
        }
        return response;
    }

    /**
     * 订单入住推送接口
     */
    @Override
    public Response<Object> orderCheckDetailsNotify(OrderCheckDetailInfoRequest orderCheckDetailInfoRequest) {
        Response<Object> response = Response.success();
        try {
            AgentAccountConfig agentAccountConfig = AgentConfig.getAgentAccount(orderCheckDetailInfoRequest.getPartnerCode());
            Assert.isTrue(Objects.nonNull(agentAccountConfig), "分销商配置不存在");
            orderCheckDetailInfoRequest.setAgentCode(null);
            BigDecimal sumAdditionalCharges = new BigDecimal(0);
            if (CollUtilX.isNotEmpty(orderCheckDetailInfoRequest.getCheckDetails())) {
                for (OrderCheckDetails checkDetail : orderCheckDetailInfoRequest.getCheckDetails()) {
                    if (CollUtilX.isNotEmpty(checkDetail.getPriceItems())) {
                        BigDecimal roomAdditionalCharges = new BigDecimal(0);
                        for (OrderCheckDetailPriceItem priceItem : checkDetail.getPriceItems()) {
                            roomAdditionalCharges = roomAdditionalCharges.add(priceItem.getSalePrice() != null ? priceItem.getSalePrice() : new BigDecimal(0))
                                    .add(priceItem.getRefundPrice() != null ? priceItem.getRefundPrice() : new BigDecimal(0));
                        }
                        sumAdditionalCharges = sumAdditionalCharges.add(roomAdditionalCharges);
                    }
                }
            }
            orderCheckDetailInfoRequest.setOrderAdditionalCharges(sumAdditionalCharges);
            Request<OrderCheckDetailInfoRequest> requestRequest = new Request<>();
            requestRequest.setBusinessRequest(orderCheckDetailInfoRequest);
            Header header = getHeader(agentAccountConfig, ApiNameEnum.orderCheckDetailInfoPush);
            requestRequest.setHeader(header);
            String body = HttpUtilX.post(agentAccountConfig.getOrderCheckDetailNotifyUrl(), JSONUtil.toJsonStr(requestRequest));
            OrderStatusPushResponse orderStatusPushResponse = JSONObject.parseObject(body, OrderStatusPushResponse.class);
            if (Objects.nonNull(orderStatusPushResponse) && Objects.equals(orderStatusPushResponse.getReturnCode(), DhubReturnCodeEnum.SUCCESS.no)) {
                if (Objects.equals(orderStatusPushResponse.getReceiveStatus(), 1)) {
                    response.setResult(ResultCodeEnum.SUCCESS.code);
                }
            } else {
                response.setFailCode(ErrorCodeEnum.NOTIFY_DISTRIBUTOR_FAIL.errorCode);
                response.setFailReason(ErrorCodeEnum.NOTIFY_DISTRIBUTOR_FAIL.errorCode);
            }
        } catch (Exception e) {
            log.error("订单入住推送推送分销商系统异常:", e);
            response.setResult(ResultCodeEnum.FAILURE.code);
            response.setFailCode(ErrorCodeEnum.NOTIFY_DISTRIBUTOR_FAIL.errorCode);
            response.setFailReason(ErrorCodeEnum.NOTIFY_DISTRIBUTOR_FAIL.errorCode);
        }
        return response;
    }

    @Override
    public ResponseResult<RefundNoticeOrderResponse> refundNoticeOrder(RefundNoticeOrderRequest request) {
        ResponseResult<RefundNoticeOrderResponse> responseResponseResult = new ResponseResult<>(DhubReturnCodeEnum.SUCCESS.no, DhubReturnCodeEnum.SUCCESS.code);

        checkRefundNoticeOrderParam(request);

        // 查询客户配置
        AgentAccountConfig partnerInfo = AgentConfig.getAgentAccount(request.getPartnerCode());
        if (partnerInfo == null) {
            throw new CustomException(DhubReturnCodeEnum.PARTNER_CONFIG_NOT_EXIST.no, DhubReturnCodeEnum.PARTNER_CONFIG_NOT_EXIST.code, DhubReturnCodeEnum.PARTNER_CONFIG_NOT_EXIST.code);
        }
        request.setAgentCode(partnerInfo.getAgentCode());

        OrderRefundNoticeRequest refundNoticeRequest = DTOConvert.INSTANCE.refundNoticeOrderConvert(request);
        refundNoticeRequest.setChannelCode(ChannelEnum.DHUB.key);
        com.tiangong.dis.dto.ResponseResult<OrderRefundNoticeResponse> refundNoticeOrderResult = hotelOrderRemote.refundNoticeOrder(refundNoticeRequest);
        CommonUtil.checkResponseResult(refundNoticeOrderResult);
        if (!DhubReturnCodeEnum.SUCCESS.no.equals(refundNoticeOrderResult.getReturnCode())) {
            if (StrUtil.isNotBlank(refundNoticeOrderResult.getFailCode())) {
                DhubReturnCodeEnum errorCodeByEnum = DhubReturnCodeEnum.getEnumByErrorCode(refundNoticeOrderResult.getFailCode());
                throw new CustomException(errorCodeByEnum.getNo(), errorCodeByEnum.getCode(), errorCodeByEnum.getCode());
            }else{
                DhubReturnCodeEnum errorCodeByEnum = DhubReturnCodeEnum.getEnumByErrorCode(refundNoticeOrderResult.getReturnCode());
                throw new CustomException(errorCodeByEnum.getNo(), errorCodeByEnum.getCode(), errorCodeByEnum.getCode());
            }
        }
        if (refundNoticeOrderResult.getBussinessResponse() != null) {
            responseResponseResult.setBussinessResponse(DTOConvert.INSTANCE.convert(refundNoticeOrderResult.getBussinessResponse()));
        }
        responseResponseResult.setReturnCode(refundNoticeOrderResult.getReturnCode());
        responseResponseResult.setReturnMsg(refundNoticeOrderResult.getReturnMsg());
        return responseResponseResult;
    }

    @Override
    public Response<Object> orderDeductionPush(OrderDeductionPushRequest request) {
        String req = null;
        String resp = null;
        String url = null;
        String errorMsg = null;
        Response<Object> response = Response.error(ErrorCodeEnum.NOTIFY_DISTRIBUTOR_FAIL.errorCode, ErrorCodeEnum.NOTIFY_DISTRIBUTOR_FAIL.errorCode);
        try {
            if (StrUtilX.isEmpty(request.getPartnerCode())) {
                return Response.error(ParamErrorEnum.EMPTY_PARAM_PARTNERCODE.getCode(), ParamErrorEnum.EMPTY_PARAM_PARTNERCODE.getCode());
            }
            AgentAccountConfig agentAccountConfig = AgentConfig.getAgentAccount(request.getPartnerCode());
            if (agentAccountConfig == null) {
                return Response.error(ErrorCodeEnum.PARTNER_CONFIG_NOT_EXIST.errorCode, ErrorCodeEnum.PARTNER_CONFIG_NOT_EXIST.errorCode);
            }
            url = agentAccountConfig.getWarrantiesNotifyUrl();
            if (StrUtilX.isEmpty(url)) {
                return Response.error(ErrorCodeEnum.PUSH_WARRANTIES_NOTIFY_URL_IS_EMPTY.errorCode, ErrorCodeEnum.PUSH_WARRANTIES_NOTIFY_URL_IS_EMPTY.errorCode);
            }
            Request<OrderDeductionPushRequest> requestRequest = new Request<>();
            requestRequest.setBusinessRequest(request);
            Header header = this.getHeaderNew(agentAccountConfig, ApiNameEnum.orderDeductionPush);
            requestRequest.setHeader(header);
            req = JSONUtil.toJsonStr(requestRequest);

            //请求接口
            if (HttpUtilX.isHttps(url)) {
                resp = HttpUtilX.submitRequest(url, req);
            } else {
                resp = HttpUtilX.post(url, req);
            }

            if (StrUtilX.isNotEmpty(resp)) {
                ResponseResult<OrderDeductionPushResponse> result = JSONObject.parseObject(resp, new TypeReference<ResponseResult<OrderDeductionPushResponse>>() {
                });
                if (Objects.nonNull(result.getBussinessResponse()) && Objects.equals(result.getReturnCode(), DhubReturnCodeEnum.SUCCESS.no)
                        && Objects.equals(result.getBussinessResponse().getReceiveStatus(), "1")) {
                    response = Response.success();
                }else if (Objects.nonNull(result.getBussinessResponse())) {
                    response = Response.error(ErrorCodeEnum.NOTIFY_DISTRIBUTOR_FAIL.errorCode, result.getBussinessResponse().getMessage());
                }else {
                    response = Response.error(ErrorCodeEnum.NOTIFY_DISTRIBUTOR_FAIL.errorCode, result.getReturnMsg());
                }
            }
        } catch (Exception e) {
            log.error("订单担保扣款通知推送接口异常", e);
            errorMsg = StrUtilX.getStackTraceAsString(e);
            response = Response.error(ErrorCodeEnum.OUTER_IF_EXCEPTION);
        } finally {
            //SLS日志接入
            Map<String, String> map = new HashMap<>();
            map.put(SlsEnum.LEVEL.getType(), Constant.SLSLOGLEVEL);
            map.put(SlsEnum.NAME.getType(), Constant.APPLICATIONNAME);
            map.put("url", url);
            map.put("request", req);
            map.put("response", resp);
            map.put("errorMsg", errorMsg);
            map.put("partnerCode", request.getPartnerCode());
            map.put("tiangongResponse", JSONUtil.toJsonStr(response));
            map.put(SlsEnum.MESSAGE.getType(), "orderDeductionPush");
            slsLoggerUtil.saveLog(map, "orderDeductionPush", Constant.APPLICATIONNAME);
        }
        return response;
    }

    /**
     * 校验订单退款通知参数
     */
    private void checkRefundNoticeOrderParam(RefundNoticeOrderRequest request) {
        if (StringUtils.isEmpty(request.getFcOrderCode()) && StringUtils.isEmpty(request.getCoOrderCode())) {
            throw new CustomException(DhubReturnCodeEnum.FC_ORDER_CODE_OR_OC_ORDER_CODE_NOT_EMPTY.no, DhubReturnCodeEnum.FC_ORDER_CODE_OR_OC_ORDER_CODE_NOT_EMPTY.code, DhubReturnCodeEnum.FC_ORDER_CODE_OR_OC_ORDER_CODE_NOT_EMPTY.code);
        }

        // 校验混合退款
        if (request.getIsMixedRefund() != null && request.getIsMixedRefund() == 1) {
            if (request.getRefundAmount() == null) {
                throw new CustomException(DhubReturnCodeEnum.REFUND_AMOUNT_TOTAL_NOT_EMPTY.no, DhubReturnCodeEnum.REFUND_AMOUNT_TOTAL_NOT_EMPTY.code, DhubReturnCodeEnum.REFUND_AMOUNT_TOTAL_NOT_EMPTY.code);
            }

            // 混合退款必须有两种方式，否则响应失败
            if (CollUtilX.isEmpty(request.getMixedRefundList()) || request.getMixedRefundList().size() != 2) {
                throw new CustomException(DhubReturnCodeEnum.INVALID_INPUTPARAM.no, DhubReturnCodeEnum.INVALID_INPUTPARAM.code, DhubReturnCodeEnum.INVALID_INPUTPARAM.code);
            }

            StringBuilder refundMethod = new StringBuilder();
            BigDecimal totalAmount = BigDecimal.ZERO;

            for (MixedRefundInfo mixedRefundInfo : request.getMixedRefundList()) {
                if (mixedRefundInfo.getRefundMethod() == null) {
                    throw new CustomException(DhubReturnCodeEnum.REFUND_METHOD_NOT_EMPTY.no, DhubReturnCodeEnum.REFUND_METHOD_NOT_EMPTY.code, DhubReturnCodeEnum.REFUND_METHOD_NOT_EMPTY.code);
                }
                if (PayMethodEnum.payMethodCheck(mixedRefundInfo.getRefundMethod()) == null) {
                    throw new CustomException(DhubReturnCodeEnum.REFUND_METHOD_ERROR.no, DhubReturnCodeEnum.REFUND_METHOD_ERROR.code, DhubReturnCodeEnum.REFUND_METHOD_ERROR.code);
                }
                refundMethod.append(mixedRefundInfo.getRefundMethod());
                if (mixedRefundInfo.getRefundAmount() == null) {
                    throw new CustomException(DhubReturnCodeEnum.REFUND_AMOUNT_NOT_EMPTY.no, DhubReturnCodeEnum.REFUND_AMOUNT_NOT_EMPTY.code, DhubReturnCodeEnum.REFUND_AMOUNT_NOT_EMPTY.code);
                }

                totalAmount = totalAmount.add(mixedRefundInfo.getRefundAmount());
            }

            // 混合退款两个退款方式不能相同，否则失败
            if (!refundMethod.toString().equals("12") && !refundMethod.toString().equals("21")) {
                throw new CustomException(DhubReturnCodeEnum.REFUND_METHOD_ERROR.no, DhubReturnCodeEnum.REFUND_METHOD_ERROR.code, DhubReturnCodeEnum.REFUND_METHOD_ERROR.code);
            }

            // 退款金额和需等于退款总金额
            if (totalAmount.compareTo(request.getRefundAmount()) != 0) {
                throw new CustomException(DhubReturnCodeEnum.REFUND_AMOUNT_SUM_NEQ_ORDER_AMOUNT.no, DhubReturnCodeEnum.REFUND_AMOUNT_SUM_NEQ_ORDER_AMOUNT.code, DhubReturnCodeEnum.REFUND_AMOUNT_SUM_NEQ_ORDER_AMOUNT.code);
            }
        } else {
            if (request.getRefundMethod() == null) {
                throw new CustomException(DhubReturnCodeEnum.REFUND_METHOD_NOT_EMPTY.no, DhubReturnCodeEnum.REFUND_METHOD_NOT_EMPTY.code, DhubReturnCodeEnum.REFUND_METHOD_NOT_EMPTY.code);
            }
            if (PayMethodEnum.payMethodCheck(request.getRefundMethod()) == null) {
                throw new CustomException(DhubReturnCodeEnum.REFUND_METHOD_ERROR.no, DhubReturnCodeEnum.REFUND_METHOD_ERROR.code, DhubReturnCodeEnum.REFUND_METHOD_ERROR.code);
            }
            if (request.getRefundAmount() == null) {
                throw new CustomException(DhubReturnCodeEnum.REFUND_AMOUNT_TOTAL_NOT_EMPTY.no, DhubReturnCodeEnum.REFUND_AMOUNT_TOTAL_NOT_EMPTY.code, DhubReturnCodeEnum.REFUND_AMOUNT_TOTAL_NOT_EMPTY.code);
            }
        }
    }
}
