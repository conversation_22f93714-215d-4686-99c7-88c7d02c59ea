package com.tiangong.entity.response.order;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @description: 费用明细
 * @author: qiu
 * @create: 2024-07-08 10:48
 */
@Data
public class OrderFeeDto {

    /**
     * 房间号
     */
    private String roomNumber;

    /**
     * 入住人
     */
    private String guestList;

    /**
     * 入住成人数
     */
    private Integer adultQty;

    /**
     * 入住儿童数
     */
    private Integer childrenQty;

    /**
     * 开始日期
     */
    private String startDate;

    /**
     * 结束日期
     */
    private String endDate;

    /**
     * 到店另付费用
     */
    private BigDecimal payAtHotelFee;

    /**
     * 到店另付费用币种
     */
    private String payAtHotelCurrencyCode;

    /**
     * 费用明细
     */
    private List<OrderFeeDetailDto> priceList;
}