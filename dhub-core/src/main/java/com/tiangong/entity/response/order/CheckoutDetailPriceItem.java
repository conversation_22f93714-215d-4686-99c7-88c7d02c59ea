package com.tiangong.entity.response.order;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class CheckoutDetailPriceItem {
    /**
     * 退房日期
     */
    private String checkoutDate;

    /**
     * 退房手续费，手续费/罚金
     */
    private BigDecimal refundPrice;

    /**
     * 应退金额
     */
    private BigDecimal receivableRefundPrice;

    /**
     * 售价
     */
    private String salePrice;
}
