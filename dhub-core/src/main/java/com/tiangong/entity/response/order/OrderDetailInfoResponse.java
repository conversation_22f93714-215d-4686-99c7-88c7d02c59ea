package com.tiangong.entity.response.order;

import com.tiangong.dto.product.response.TipInfosDTO;
import com.tiangong.entity.base.BusinessResponse;
import com.tiangong.entity.vo.order.GuestInfo;
import com.tiangong.entity.vo.order.InvoiceInfoDto;
import com.tiangong.entity.vo.order.QueryOrderPriceItem;
import com.tiangong.entity.vo.order.RoomGuestNumberDto;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
//@JsonInclude(JsonInclude.Include.NON_NULL)
public class OrderDetailInfoResponse extends BusinessResponse {

    private String coOrderCode;

    private String fcOrderCode;

    private Integer orderStatus;

    private String channelState;

    /**
     * 酒店确认号
     */
    private String hotelConfirmNo;

    private Long hotelId;

    private String hotelName;

    private Long roomId;

    private String roomName;

    private String ratePlanId;

    private String ratePlanName;

    private String bedType;

    private String checkInDate;

    private String checkOutDate;

    private String checkInTime;

    private String checkOutTime;

    private String arriveTime;

    private String latestArriveTime;

    private Integer roomNum;

    private Double totalAmount;

    private List<QueryOrderPriceItem> priceItems;

    /**
     * 费用明细(按间明细)
     */
    private List<OrderFeeDto> feeList;

    private List<GuestInfo> guestInfos;

    private String linkMan;

    private String linkPhone;

    private String email;

    private Integer invoiceModel;

    /**
     * 发票信息
     */
    private InvoiceInfoDto invoiceInfo;

    /**
     * 订单创建时间
     */
    private String createTime;

    /**
     * 入住类型：1-全日房，2-钟点房
     */
    private Integer checkInType;

    /**
     * 币种
     */
    private String currency;

    /**
     * 到店另付费用；针对海外酒店有效
     */
    private BigDecimal payAtHotelFee;

    /**
     * 到店另付费用币种；针对海外酒店有效
     */
    private String payAtHotelFeeCurrency;

    /**
     * 房间客人数量信息
     */
    private List<RoomGuestNumberDto> roomGuestNumbers;

    /**
     * 快速处理标签开关
     */
    private Integer quickProcessingSwitch;

//    /**
//     * 供应商标签名称（待作废）
//     */
//    private Integer supplierLabel;

    /**
     * 供应商标签名称（带启用）
     */
    private Integer productLabel;

    /**
     * 床型不同（产品和基础信息）标识：0相同 1不相同
     */
    private Integer bedTypeDiff;

    /**
     * 每日总底价(供应商金额)
     */
    private BigDecimal totalBasePrice;

    /**
     * 底价币种（供应商币种）
     */
    private String baseCurrency;

    /**
     * 快速处理标签
     */
    private Integer rapidProcessing;

    /**
     * 提示信息
     */
    private List<TipInfosDTO> tips;

    /**
     * 罚金金额
     */
    private BigDecimal penaltiesValue;

    /**
     * 服务费
     */
    private BigDecimal serviceCharge;

}
