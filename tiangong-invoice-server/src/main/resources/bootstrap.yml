spring:
  profiles:
    active: pro
  application:
    name: tiangong-invoice-server
  jackson:
    time-zone: GMT+8
  servlet:
    multipart:
      #设置单个文件大小
      max-file-size: 100MB
      #设置单次请求文件的总大小
      max-request-size: 100MB
  main:
    allow-bean-definition-overriding: true
server:
  port: 14111
tiangong:
  security:
    switch: true # 是否开启security，true：开启，false：关闭
    secret: ZmQ0ZGI5NjQ0MDQwY2I4MjMxY2Y3ZmI3MjdhN2ZmMjNhODViOTg1ZGE0NTBjMGM4NDA5NzYxMjdjOWMwYWRmZGE= # Base64编码
    tokentime: 20 # 令牌过期时间 此处单位/小时 ，默认2小时，可在此网站生成 https://www.convertworld.com/zh-hans/time/milliseconds.html



#pagehelper分页插件配置
pagehelper:
  helperDialect: mysql
  reasonable: true
  supportMethodsArguments: true
  params: count=countSql


logstash:
  url: ************:5044

jasypt:
  encryptor:
    bean: sM4ECBStringEncryptor
    iv-generator-classname: org.jasypt.iv.NoIvGenerator
    passward: item
#xxljob配置
xxl:
  job:
    admin:
      addresses: http://127.0.0.1:18880/xxl-job-admin/
    accessToken: default_token
    executor:
      appname: tiangong-invoice-server
      address:
      logpath: ./applogs/tiangong-invoice-server
      logretentiondays: 3
      port: 0
