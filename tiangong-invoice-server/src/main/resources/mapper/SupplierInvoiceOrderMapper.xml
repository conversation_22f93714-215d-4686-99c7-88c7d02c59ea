<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.tiangong.invoice.supplierInvoice.mapper.SupplierInvoiceOrderMapper">

    <select id="queryInvoiceableSupplierInfoList" parameterType="com.tiangong.invoice.req.InvoiceableSupplierInfoQueryReq" resultType="com.tiangong.invoice.resp.InvoiceableSupplierInfoResp">
        SELECT oso.supplier_code as supplierCode,oso.supplier_name as supplierName,count(oso.supply_order_code) as waitingInvoiceNum,
               IFNULL(sum(oso.supply_order_amt),0)-IFNULL(sum(fsibos.already_invoice_amt),0) as waitingInvoiceAmt
        FROM o_supply_order oso
             LEFT JOIN o_order oo ON oo.id = oso.order_id
             LEFT JOIN f_supplier_invoice_bill_order_state fsibos ON oso.supply_order_code = fsibos.supply_order_code
        <where> oo.company_code = #{merchantCode}
            <!-- 供货单状态是已确认、已取消、已完成,人民币的 -->
            AND oso.confirmation_status in (1,2,3) and oso.base_currency=0
            <!-- 未添加过票单-->
            AND (fsibos.invoice_bill_status IS NULL or fsibos.invoice_bill_status in (9,10))
            <!-- 有供货单金额 -->
            AND oso.supply_order_amt != 0
            <!--     供货单金额不等于供货单已开票金额 或者 未开过票  -->
            AND ( oso.supply_order_amt != fsibos.already_invoice_amt OR fsibos.already_invoice_amt IS NULL )
            <!-- 查询口径 0-下单日期、1-入住日期、2-离店日期-->
            <if test="startDate != null and startDate != '' and endDate != null and endDate != ''">
                <choose>
                    <when test="dateQueryType != null and dateQueryType == 0">
                        AND date_format(oso.created_dt, '%Y-%m-%d') BETWEEN #{startDate} AND #{endDate}
                    </when>
                    <when test="dateQueryType != null and dateQueryType == 1">
                        AND date_format(oso.start_date, '%Y-%m-%d') BETWEEN #{startDate} AND #{endDate}
                    </when>
                    <when test="dateQueryType != null and dateQueryType == 2">
                        AND date_format(oso.end_date, '%Y-%m-%d') BETWEEN #{startDate} AND #{endDate}
                    </when>
                </choose>
            </if>
            <if test="settlementType != null ">
                AND oso.settlement_type = #{settlementType}
            </if>
            <if test="supplyOrderCode!=null and supplyOrderCode!=''">
                AND oso.supply_order_code like concat('%', #{supplyOrderCode}, '%')
            </if>
            <if test="supplierName!=null and supplierName!=''">
                AND oso.supplier_name like concat('%', #{supplierName}, '%')
            </if>
            <if test="hotelName!=null and hotelName!=''">
                AND oo.hotel_name like concat('%', #{hotelName}, '%')
            </if>
        </where>
        GROUP BY oso.supplier_code, oso.supplier_name
         HAVING count(oso.supply_order_code) > 0
        ORDER BY count(oso.supply_order_code) desc
    </select>
</mapper>