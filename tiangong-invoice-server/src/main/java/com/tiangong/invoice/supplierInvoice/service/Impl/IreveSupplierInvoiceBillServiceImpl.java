package com.tiangong.invoice.supplierInvoice.service.Impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.tiangong.enums.ErrorCodeEnum;
import com.tiangong.exception.SysException;
import com.tiangong.invoice.dto.DdtInvoiceBillItemAditResp;
import com.tiangong.invoice.enums.DdtReturnCodeEnum;
import com.tiangong.invoice.req.TgToDdtInvoiceBillAditReq;
import com.tiangong.invoice.req.TgToDdtInvoiceBillItemAditReq;
import com.tiangong.invoice.supplierInvoice.service.IreveSupplierInvoiceBillService;
import com.tiangong.util.CollUtilX;
import com.tiangong.util.HttpUtilX;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName:
 * @Description: 订单通同步天宫发票业务处理类
 * @date 2024/1/3
 */
@Service
@Slf4j
public class IreveSupplierInvoiceBillServiceImpl implements IreveSupplierInvoiceBillService {

    @Value("${ddt.modifyIreveInvoiceBillUrl}")
    private String addOrEditIreveInvoiceBillItemUrl;

    @Value("${ddt.delIreveInvoiceBillUrl}")
    private String delIreveInvoiceBillItemUrl;

    @Value("${ddt.addIreveInvoiceBillInvoiceItemUrl}")
    private String addIreveInvoiceBillInvoiceItemUrl;

    @Override
    public List<DdtInvoiceBillItemAditResp> addOrEditDdtInvoiceBillItem(TgToDdtInvoiceBillAditReq req) {
        // 校验参数
        validateAditDdtInvoiceBillItemParam(req);

        // 调用订单通新增或修改票单明细接口
        log.info("调用订单通系统：新增或者修改时参数：" + JSON.toJSONString(req));
        Map<String, String> header = new HashMap<>();
        String result = HttpUtilX.post(addOrEditIreveInvoiceBillItemUrl, JSON.toJSONString(req), header);
        log.info("新增或编辑订单通票单订单,request=" + JSON.toJSONString(req) + ",resp=" + result);
        if (StrUtilX.isEmpty(result)) {
            log.error("新增或编辑ireve票单明细失败,返回参数为空");
            throw new SysException(ErrorCodeEnum.SYNC_ADD_DDT_SUPPLYORDER_FAILED);
        }

        Map<String, Object> resultMap = JSON.parseObject(result, new TypeReference<Map<String, Object>>(){});
        if (resultMap.get("result").equals(DdtReturnCodeEnum.FAIL.getKey())) {
            log.error("新增或编辑ireve票单明细失败：" + resultMap.get("msg"));
            String msg = (String) resultMap.get("msg");
            if (msg.contains("没有找到对应的订单信息")) {
                throw new SysException(ErrorCodeEnum.ORDER_TONG_NOTFOUND_TIANGONG_ORDER);
            } else {
                throw new SysException(ErrorCodeEnum.SYNC_ADD_DDT_SUPPLYORDER_FAILED);
            }
        }
        if (resultMap.get("data") == null) {
            throw new SysException(ErrorCodeEnum.ORDER_TONG_NOTFOUND_TIANGONG_ORDER);
        }
        return JSON.parseObject(JSONUtil.toJsonStr(resultMap.get("data")), new TypeReference<List<DdtInvoiceBillItemAditResp>>(){});
    }


    @Override
    public void delDdtInvoiceBillItem(TgToDdtInvoiceBillAditReq req) {
        // 校验参数
        validateDelDdtInvoiceBillItemParam(req);

        Map<String, String> header = new HashMap<>();
        //调用订单通删除票单明细接口
        log.info("调用订单通系统：删除时参数：" + JSON.toJSONString(req));
        String result = HttpUtilX.post(delIreveInvoiceBillItemUrl, JSON.toJSONString(req), header);
        log.info("删除订单通票单订单,request=" + JSON.toJSONString(req) + ",resp=" + result);
        if (StrUtilX.isEmpty(result)) {
            log.error("删除ireve票单明细失败,返回参数为空");
            throw new SysException(ErrorCodeEnum.SYNC_DEL_DDT_SUPPLYORDER_FAILED);
        }

        Map<String, Object> resultMap = JSON.parseObject(result, new TypeReference<Map<String, Object>>(){});
        if (resultMap.get("result").equals(DdtReturnCodeEnum.FAIL.getKey())) {
            log.error("删除ireve票单明细失败：" + resultMap.get("msg"));
            throw new SysException(ErrorCodeEnum.SYNC_DEL_DDT_SUPPLYORDER_FAILED);
        }
    }


    @Override
    public void addDdtInvoiceBillInvoice(TgToDdtInvoiceBillAditReq req) {
        // 校验参数
        validateSyncDdtInvoiceBillItemParam(req);

        Map<String, String> header = new HashMap<>();
        // 调用订单通删除票单明细接口
        log.info("调用订单通系统:确认平账时同步发票参数：" + JSON.toJSONString(req));
        String result = HttpUtilX.post(addIreveInvoiceBillInvoiceItemUrl, JSON.toJSONString(req), header);
        log.info("调用订单通系统:确认平账时同步发票结果,request=" + JSON.toJSONString(req) + ",resp=" + result);
        if (StrUtilX.isEmpty(result)) {
            log.error("确认平账时同步发票失败,返回参数为空");
            throw new SysException(ErrorCodeEnum.SYNC_ADD_DDT_INVOICE_FAILED);
        }

        Map<String, Object> resultMap = JSON.parseObject(result, new TypeReference<Map<String, Object>>() {
        });
        if (resultMap.get("result").equals(DdtReturnCodeEnum.FAIL.getKey())) {
            log.error("确认平账时同步发票失败：" + resultMap.get("msg"));
            throw new SysException(ErrorCodeEnum.SYNC_ADD_DDT_INVOICE_FAILED);
        }
    }

    /**
     * 检验票单明细添加列表
     */
    private void validateAditDdtInvoiceBillItemParam(TgToDdtInvoiceBillAditReq req) {
        if (null == req) {
            log.error("sync进项票发票明细添加失败：请求参数为空");
            throw new SysException(ErrorCodeEnum.OPERATE_FAIL);
        } else if (StrUtilX.isEmpty(req.getInvoiceStatementCode())) {
            log.error("sync进项票发票明细添加失败：订单通票单编号为空");
            throw new SysException(ErrorCodeEnum.OPERATE_FAIL);
        } else if (CollUtilX.isEmpty(req.getOrderInfoList())) {
            log.error("sync进项票发票明细添加失败：订单信息列表为空");
            throw new SysException(ErrorCodeEnum.OPERATE_FAIL);
        }
        for (TgToDdtInvoiceBillItemAditReq orderInfo : req.getOrderInfoList()) {
            if (StrUtilX.isEmpty(orderInfo.getSupplyOrderCode())) {
                log.error("sync进项票发票明细添加失败：订单中的供货单编码为空");
                throw new SysException(ErrorCodeEnum.OPERATE_FAIL);
            } else if (null == orderInfo.getCurrentInvoiceAmt()) {
                log.error("sync进项票发票明细添加失败：本次使用金额为空，订单号是" + orderInfo.getSupplyOrderCode());
                throw new SysException(ErrorCodeEnum.OPERATE_FAIL);
            }
        }
    }

    /**
     * 校验参数
     */
    private void validateSyncDdtInvoiceBillItemParam(TgToDdtInvoiceBillAditReq req) {
        if (null == req) {
            log.error("同步发票到订单通失败,请求参数为空");
            throw new SysException(ErrorCodeEnum.OPERATE_FAIL);
        } else if (CollUtilX.isEmpty(req.getInvoiceList())) {
            log.error("同步发票到订单通失败，发票明细为空");
            throw new SysException(ErrorCodeEnum.OPERATE_FAIL);
        }
    }

    /**
     * 校验订单通删除票单明细参数
     */
    private void validateDelDdtInvoiceBillItemParam(TgToDdtInvoiceBillAditReq req) {
        if (null == req) {
            log.error("进项票发票明细添加失败：ireve删除票单明细失败，请求参数为空");
            throw new SysException(ErrorCodeEnum.OPERATE_FAIL);
        } else if (CollUtilX.isEmpty(req.getSupplyOrderCodeList())) {
            log.error("进项票发票明细添加失败：ireve删除票单明细失败，请求参数为空，供货单列表");
            throw new SysException(ErrorCodeEnum.OPERATE_FAIL);
        }
    }
}
