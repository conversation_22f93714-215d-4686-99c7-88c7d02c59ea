package com.tiangong.invoice.supplierInvoice.server;

import com.alibaba.fastjson.JSON;
import com.tiangong.cloud.common.anno.AnonymousAccess;
import com.tiangong.cloud.commonbean.common.BaseController;
import com.tiangong.common.Response;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.invoice.enums.InvoiceBillTypeEnum;
import com.tiangong.invoice.req.*;
import com.tiangong.invoice.resp.*;
import com.tiangong.invoice.supplierInvoice.service.SupplierInvoiceBillService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 供应商票单管理
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-22 10:49:56
 */
@RestController
@RequestMapping("/invoice/supplyInvoiceBill")
@Slf4j
public class SupplierInvoiceBillController extends BaseController {


    @Autowired
    private SupplierInvoiceBillService supplierInvoiceBillService;

    /**
     * 供应商票单表新增
     */
    @PostMapping("/supplierInvoiceBillAdd")
    public Response<Long> supplierInvoiceBillAdd(@RequestBody SupplierInvoiceBillAditReq req) {
        req.setMerchantCode(getCompanyCode());
        // 手动票单创建
        req.setIsAuto(InvoiceBillTypeEnum.MANUAL.getKey());
        req.setCreatedBy(getUserName());
        return Response.success(supplierInvoiceBillService.supplierInvoiceBillAdd(req, null, null));
    }

    /**
     * 供应商票单表编辑
     */
    @PostMapping("/supplierInvoiceBillEdit")
    public Response<Object> supplierInvoiceBillEdit(@RequestBody SupplierInvoiceBillAditReq req) {
        req.setMerchantCode(getCompanyCode());
        req.setUpdatedBy(getUserName());
        supplierInvoiceBillService.supplierInvoiceBillEdit(req);
        return Response.success();
    }

    /**
     * 票单分页
     */
    @PostMapping("/supplierInvoiceBillPage")
    public Response<PaginationSupportDTO<SupplierInvoiceBillResp>> supplierInvoiceBillPage(@RequestBody SupplierInvoiceBillQueryReq req) {
        req.setMerchantCode(getCompanyCode());
        return Response.success(supplierInvoiceBillService.supplierInvoiceBillPage2(req));
    }

    /**
     * 进项票单详情
     */
    @PostMapping("/supplierInvoiceBillDetail")
    public Response<SupplierInvoiceBillDetailResp> supplierInvoiceBillDetail(@RequestBody SupplierInvoiceBillQueryReq req) {
        req.setMerchantCode(getCompanyCode());
        return Response.success(supplierInvoiceBillService.supplierInvoiceBillDetail(req));
    }

    /**
     * 查询票单详情供货单分页数据
     */
    @PostMapping("/queryInvoiceBillItemPage")
    public Response<PaginationSupportDTO<SupplierInvoiceBillItemResp>> queryInvoiceBillItemPage(@RequestBody SupplierInvoiceBillQueryReq req) {
        req.setMerchantCode(getCompanyCode());
        return Response.success(supplierInvoiceBillService.queryInvoiceBillItemPage(req));
    }


    /**
     * 查询票单详情发票分页数据
     */
    @PostMapping("/queryInvoiceBillInvoicePage")
    public Response<PaginationSupportDTO<SupplierInvoiceResp>> queryInvoiceBillInvoicePage(@RequestBody SupplierInvoiceBillQueryReq req) {
        req.setMerchantCode(getCompanyCode());
        return Response.success(supplierInvoiceBillService.queryInvoiceBillInvoicePage(req));
    }


    /**
     * 供应商票单表删除
     */
    @PostMapping("/supplierInvoiceBillDel")
    public Response<Object> supplierInvoiceBillDel(@RequestBody SupplierInvoiceBillAditReq req) {
        req.setMerchantCode(getCompanyCode());
        req.setUpdatedBy(getUserName());
        supplierInvoiceBillService.supplierInvoiceBillDel(req);
        return Response.success();
    }

    /**
     * 确认平账
     */
    @PostMapping("/suppInvoiceBillConfirmBalan")
    public Response<Object> suppInvoiceBillConfirmBalan(@RequestBody SupplierInvoiceBillAditReq req) {
        req.setMerchantCode(getCompanyCode());
        req.setUpdatedBy(getUserName());
        supplierInvoiceBillService.suppInvoiceBillConfirmBalan(req);
        return Response.success();
    }

    /**
     * 确认坏账
     */
    @PostMapping("/supplierInvoiceBillConfirmBad")
    public Response<Object> supplierInvoiceBillConfirmBad(@RequestBody SupplierInvoiceBillAditReq req) {
        req.setMerchantCode(getCompanyCode());
        req.setUpdatedBy(getUserName());
        supplierInvoiceBillService.supplierInvoiceBillConfirmBad(req);
        return Response.success();
    }


    /**
     * 查询可添加的供货单明细
     */
    @PostMapping("/querySuppInvoiceBillList")
    public Response<PaginationSupportDTO<SupplierInvoiceBillItemResp>> querySupplierInvoiceableInvoiceBillItemList(@RequestBody SupplierInvoiceBillItemQueryReq req) {
        req.setMerchantCode(getCompanyCode());
        return Response.success(supplierInvoiceBillService.querySupplierInvoiceableInvoiceBillItemList(req));
    }

    /**
     * 票单中添加供货单
     */
    @PostMapping("/supplierInvoiceBillItemAdd")
    public Response<Object> supplierInvoiceBillItemAdd(@RequestBody SupplierInvoiceBillItemAditReq req) {
        req.setMerchantCode(getCompanyCode());
        req.setCreatedBy(getUserName());
        supplierInvoiceBillService.supplierInvoiceBillItemAdd(req);
        return Response.success();
    }

    /**
     * 票单中删除供货单
     */
    @PostMapping("/supplierInvoiceBillItemDel")
    public Response<Object> supplierInvoiceBillItemDel(@RequestBody SupplierInvoiceBillItemAditReq req) {
        req.setMerchantCode(getCompanyCode());
        req.setCreatedBy(getUserName());
        req.setUpdatedBy(getUserName());
        supplierInvoiceBillService.supplierInvoiceBillItemDel(req);
        return Response.success();
    }

//    /**
//     * 票单中票单明细导出
//     */
//    @PostMapping("/supplierInvoiceBillItemExport")
//    public void supplierInvoiceBillItemExport(@RequestBody SupplierInvoiceBillItemQueryReq req) {
//        ResultVo response = new ResultVo<>();
//        try {
//            req.setMerchantCode(getCompanyCode());
//            supplierInvoiceBillService.supplierInvoiceBillItemExport(req);
//        } catch (Exception e) {
//            response.setResult(ResultCodeEnum.FAILURE.code);
//            response.setCode(ErrorCodeEnum.SYSTEM_EXCEPTION.errorCode);
//            response.setMsg(e.getMessage());
//        }
//    }

    /**
     * 票单明细差异化查询(和ireve票单明细)
     */
    @PostMapping("/suppInvoiceBillItemIreveDiffPage")
    public Response<PaginationSupportDTO<SupplierInvoiceBillItemDiffResp>> suppInvoiceBillItemIreveDiffPage(@RequestBody SupplierInvoiceBillItemQueryReq req) {
        return Response.success(supplierInvoiceBillService.suppInvoiceBillItemIreveDiffPage(req));
    }

    /**
     * 票单明细差异化 移除订单通供货单
     */
    @PostMapping("/suppInvoiceBillItemIreveDiffDel")
    public Response<Object> suppInvoiceBillItemIreveDiffDel(@RequestBody SupplierInvoiceBillItemAditReq req) {
        supplierInvoiceBillService.suppInvoiceBillItemIreveDiffDel(req);
        return Response.success();
    }

    /**
     * 明细单有更新查询
     */
    @PostMapping("/supplierInvoiceBillItemDiffPage")
    public Response<PaginationSupportDTO<SupplierInvoiceBillItemResp>> supplierInvoiceBillItemDiffPage(@RequestBody SupplierInvoiceBillItemQueryReq req) {
        return Response.success(supplierInvoiceBillService.supplierInvoiceBillItemDiffPage(req));
    }

    /**
     * 明细单有更新-立即更新
     */
    @PostMapping("/supplierInvoiceBillItemDiffEdit")
    public Response<Object> supplierInvoiceBillItemDiffEdit(@RequestBody List<SupplierInvoiceBillItemResp> reqs) {
        supplierInvoiceBillService.supplierInvoiceBillItemDiffEdit(reqs, getUserName());
        return Response.success();
    }

    /**
     * 供货单开票记录
     */
    @PostMapping("/supplyOrderInvoiceRecordPage")
    public Response<PaginationSupportDTO<SupplierInvoiceBillItemResp>> supplyOrderInvoiceRecordPage(@RequestBody SupplierInvoiceBillItemQueryReq req) {
        return Response.success(supplierInvoiceBillService.supplyOrderInvoiceRecordPage(req));
    }


    /**
     * 查询可添加发票的列表
     */
    @PostMapping("/querySupplierInvoicePage")
    public Response<PaginationSupportDTO<SupplierInvoiceResp>> querySupplierInvoicePage(@RequestBody SupplierInvoiceQueryPageReq req) {
        req.setMerchantCode(getCompanyCode());
        return Response.success(supplierInvoiceBillService.querySupplierInvoicePage(req));
    }

    /**
     * 添加票单发票明细
     */
    @PostMapping("/invoiceBillInvoiceItemAdd")
    public Response<Object> invoiceBillInvoiceItemAdd(@RequestBody SupInvoiceBillRelationReq req) {
        req.setLoginName(getUserName());
        supplierInvoiceBillService.invoiceBillInvoiceItemAdd(req);
        return Response.success();
    }

    /**
     * 移除票单发票明细
     */
    @PostMapping("/invoiceBillInvoiceItemDel")
    public Response<Object> invoiceBillInvoiceItemDel(@RequestBody SupplierInvoiceBillRelationReq req) {
        req.setUpdatedBy(getUserName());
        supplierInvoiceBillService.invoiceBillInvoiceItemDel(req);
        return Response.success();
    }

    /**
     * 票单发票明细修改票单领用金额
     */
    @PostMapping("/invoiceBillInvoiceItemAmountEdit")
    public Response<Object> invoiceBillInvoiceItemAmountEdit(@RequestBody SupplierInvoiceBillRelationReq req) {
        req.setUpdatedBy(getUserName());
        supplierInvoiceBillService.invoiceBillInvoiceItemAmountEdit(req);
        return Response.success();
    }


    /**
     * 进项票单同步(订单通)
     */
    @AnonymousAccess
    @PostMapping("/supplierInvoiceBillSync")
    public Response<Object> supplierInvoiceBillSync(@RequestBody DdtToTgInvoiceBillSyncAditReq req) {
        // 自动票单创建
        req.setIsAuto(InvoiceBillTypeEnum.AUTOMATIC.getKey());
        supplierInvoiceBillService.supplierInvoiceBillSync(req);
        return Response.success();
    }

    /**
     * 票单明细导出
     */
    @PostMapping("/export")
    public Response<String> export(@RequestBody SupplierInvoiceBillQueryReq req) {
        return supplierInvoiceBillService.export(req);
    }

}
