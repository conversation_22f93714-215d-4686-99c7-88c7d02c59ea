package com.tiangong.invoice.supplierInvoice.service.Impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.tiangong.cloud.common.domain.LoginUser;
import com.tiangong.common.Constant;
import com.tiangong.common.Response;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.enums.ErrorCodeEnum;
import com.tiangong.enums.ParamErrorEnum;
import com.tiangong.exception.SysException;
import com.tiangong.file.remote.FileRemote;
import com.tiangong.file.req.FileReq;
import com.tiangong.file.resp.FileResp;
import com.tiangong.invoice.config.SettingsConstant;
import com.tiangong.invoice.convert.InvoiceConvert;
import com.tiangong.invoice.dto.InvoiceAttchDTO;
import com.tiangong.invoice.enums.BaiDuOcrErrorEnum;
import com.tiangong.invoice.enums.InvoiceSourceTypeEnum;
import com.tiangong.invoice.enums.InvoiceStatusEnum;
import com.tiangong.invoice.enums.InvoiceTypeEnums;
import com.tiangong.invoice.req.DdtToTgInvoiceSyncAditReq;
import com.tiangong.invoice.req.SupplierInvoiceAditReq;
import com.tiangong.invoice.req.SupplierInvoiceBillQueryReq;
import com.tiangong.invoice.req.SupplierInvoiceQueryPageReq;
import com.tiangong.invoice.resp.BaiduOcrResp;
import com.tiangong.invoice.resp.SupplierInvoiceBillResp;
import com.tiangong.invoice.resp.SupplierInvoiceResp;
import com.tiangong.invoice.supplierInvoice.domain.entity.InvoiceOcrEntity;
import com.tiangong.invoice.supplierInvoice.domain.entity.SupplierInvoiceEntity;
import com.tiangong.invoice.supplierInvoice.mapper.InvoiceOcrMapper;
import com.tiangong.invoice.supplierInvoice.mapper.SupplierInvoiceBillMapper;
import com.tiangong.invoice.supplierInvoice.mapper.SupplierInvoiceMapper;
import com.tiangong.invoice.supplierInvoice.service.SupplierInvoiceService;
import com.tiangong.invoice.utils.InvoiceUtil;
import com.tiangong.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName: 进项发票业务处理类
 * @Description: 进项发票业务处理
 * @date 2023/12/22
 */
@Slf4j
@Service
public class SupplierInvoiceServiceImpl implements SupplierInvoiceService {

    @Autowired
    private SupplierInvoiceMapper supplierInvoiceMapper;

    @Autowired
    HttpServletResponse httpServletResponse;

    @Autowired
    HttpServletRequest httpServletRequest;

    @Autowired
    private SupplierInvoiceBillMapper supplierInvoiceBillMapper;

    @Autowired
    private SettingsConstant settingsConstant;

    @Autowired
    private FileRemote fileRemote;

    @Autowired
    private InvoiceOcrMapper invoiceOcrMapper;

    // 发票可识别文件类型
    private final String[] invoiceFileAllowType = {"PNG", "JPG", "JPEG", "BMP", "PDF"};

    // 百度ORC识别id(生产环境)，用于判断，测试的只能用base64将图片给到百度
    private static String PRODUCTION_BAIDU_CLIENT_ID = "KPdcdshFCEXI7WBPx8nIUoSu";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void invoiceAdd(SupplierInvoiceAditReq req) {
        // 校验必填参数
        validateInvoiceAddParam(req);

        //做唯一校验
        LambdaQueryWrapper<SupplierInvoiceEntity> queryInvoiceWrapper = new LambdaQueryWrapper<>();
        queryInvoiceWrapper.eq(SupplierInvoiceEntity::getInvoiceNum, req.getInvoiceNum());
        queryInvoiceWrapper.eq(StrUtilX.isNotEmpty(req.getInvoiceCode()), SupplierInvoiceEntity::getInvoiceCode, req.getInvoiceCode());
        List<SupplierInvoiceEntity> existInvoiceList = supplierInvoiceMapper.selectList(queryInvoiceWrapper);
        if (CollUtilX.isNotEmpty(existInvoiceList)) {
            log.error(req.getInvoiceNum() + ",该发票已添加，不可再添加");
            throw new SysException(ParamErrorEnum.INVOICENUM_CORRES_INVOICE_EXIST);
        }
        //开票日期
        Date invoiceDate = DateUtilX.stringToDate(req.getInvoiceDate());
        //插入数据
        SupplierInvoiceEntity insertInvoiceParam = new SupplierInvoiceEntity();
        //税额
        if (null != req.getTotalTaxAmt()) {
            insertInvoiceParam.setTotalTaxAmt(req.getTotalTaxAmt());
            //未税金额=价税合计-税额
            BigDecimal totalAmt = req.getInvoiceAmt().subtract(req.getTotalTaxAmt());
            insertInvoiceParam.setTotalAmt(totalAmt);
        } else {
            BigDecimal totalTaxAmt = totalTaxAmtCalculate(req.getInvoiceAmt(), req.getInvoiceTaxRate());
            insertInvoiceParam.setTotalTaxAmt(totalTaxAmt);
            //未税金额=价税合计-税额
            BigDecimal totalAmt = req.getInvoiceAmt().subtract(totalTaxAmt);
            insertInvoiceParam.setTotalAmt(totalAmt);
        }
        insertInvoiceParam.setMerchantCode(req.getMerchantCode());
        insertInvoiceParam.setThirdId(req.getThirdId());
        insertInvoiceParam.setInvoiceStatus(InvoiceStatusEnum.UNUSED.getKey());
        insertInvoiceParam.setInvoiceType(req.getInvoiceType());
        insertInvoiceParam.setPurchaserName(req.getPurchaserName());
        insertInvoiceParam.setPurchaserRegisterNum(req.getPurchaserRegisterNum());
        insertInvoiceParam.setSellerName(req.getSellerName());
        insertInvoiceParam.setSellerRegisterNum(req.getSellerRegisterNum());
        insertInvoiceParam.setInvoiceNum(req.getInvoiceNum());
        insertInvoiceParam.setInvoiceCode(req.getInvoiceCode());
        insertInvoiceParam.setInvoiceAmt(req.getInvoiceAmt());
        insertInvoiceParam.setInvoiceTaxRate(req.getInvoiceTaxRate());
        insertInvoiceParam.setRemark(req.getRemark());
        insertInvoiceParam.setInvoiceSourceType(req.getInvoiceSourceType());
        insertInvoiceParam.setBalance(req.getInvoiceAmt());
        insertInvoiceParam.setRevision(0);
        insertInvoiceParam.setBillUsedAmt(BigDecimal.ZERO);
        insertInvoiceParam.setInvoiceDate(invoiceDate);
        insertInvoiceParam.setInvoiceContent(req.getInvoiceContent());
        insertInvoiceParam.setCreatedBy(req.getCreatedBy());
        insertInvoiceParam.setCreatedDt(DateUtilX.getCurrentDate());
        supplierInvoiceMapper.insert(insertInvoiceParam);

        // 保存发票附件
        updateInvoiceAttch(req, insertInvoiceParam);
    }

    /**
     * 保存发票附件
     */
    private void updateInvoiceAttch(SupplierInvoiceAditReq req, SupplierInvoiceEntity entity) {
        if (req.getUrl() != null && req.getUrl().size() > 0) {
            for (InvoiceAttchDTO photo : req.getUrl()) {
                FileReq fileReq = new FileReq();
                fileReq.setFileId(photo.getFileId());
                fileReq.setObjId(entity.getInvoiceId().toString());
                fileReq.setTableName("f_supplier_invoice");
                fileRemote.update(fileReq);
            }
        }
    }

    @Override
    public void invoiceEdit(SupplierInvoiceAditReq req) {
        // 校验入参
        validateInvoiceEditParam(req);

        // 校验是否存在该id的记录
        SupplierInvoiceEntity existInvoiceById = supplierInvoiceMapper.selectById(req.getInvoiceId());
        if (null == existInvoiceById) {
            log.error("进项票发票编辑失败：该发票id的记录不存在," + req.getInvoiceId());
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM.errorCode, "进项票发票编辑失败，该发票id的记录不存在," + req.getInvoiceId());
        }

        // 发票状态部分使用和已使用不可编辑
        if (existInvoiceById.getInvoiceStatus() == InvoiceStatusEnum.PARTIAL_USE.getKey() || existInvoiceById.getInvoiceStatus() == InvoiceStatusEnum.USED.getKey()) {
            log.error("进项票发票编辑失败：发票状态是部分使用或已使用，不可编辑发票");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM.errorCode, "进项票发票编辑失败：发票状态是部分使用或已使用，不可编辑发票");
        }

        // 校验是是否存在该发票号码的记录
        LambdaQueryWrapper<SupplierInvoiceEntity> queryInvoiceWrapper = new LambdaQueryWrapper<>();
        queryInvoiceWrapper.eq(SupplierInvoiceEntity::getInvoiceNum, req.getInvoiceNum());
        queryInvoiceWrapper.eq(StrUtilX.isNotEmpty(req.getInvoiceCode()), SupplierInvoiceEntity::getInvoiceCode, req.getInvoiceCode());
        queryInvoiceWrapper.ne(SupplierInvoiceEntity::getInvoiceId, req.getInvoiceId());
        SupplierInvoiceEntity existInvoiceByInvoice = supplierInvoiceMapper.selectOne(queryInvoiceWrapper);
        if (existInvoiceByInvoice != null) {
            log.error("进项票发票编辑失败：" + req.getInvoiceNum() + "，该发票号码的发票已存在！");
            throw new SysException(ParamErrorEnum.INVOICENUM_CORRES_INVOICE_EXIST);
        }

        //开票日期
        Date invoiceDate = DateUtilX.stringToDate(req.getInvoiceDate());
        //更新发票
        SupplierInvoiceEntity updateInvoiceParam = new SupplierInvoiceEntity();
        //算出合计税额 和 合计金额
        if (null != req.getTotalTaxAmt()) {
            updateInvoiceParam.setTotalTaxAmt(req.getTotalTaxAmt());
            //未税金额=价税合计-税额
            BigDecimal totalAmt = req.getInvoiceAmt().subtract(req.getTotalTaxAmt());
            updateInvoiceParam.setTotalAmt(totalAmt);
        } else {
            BigDecimal totalTaxAmt = totalTaxAmtCalculate(req.getInvoiceAmt(), req.getInvoiceTaxRate());
            updateInvoiceParam.setTotalTaxAmt(totalTaxAmt);
            //未税金额=价税合计-税额
            BigDecimal totalAmt = req.getInvoiceAmt().subtract(totalTaxAmt);
            updateInvoiceParam.setTotalAmt(totalAmt);
        }
        updateInvoiceParam.setInvoiceId(req.getInvoiceId());
        updateInvoiceParam.setInvoiceType(req.getInvoiceType());
        updateInvoiceParam.setPurchaserName(req.getPurchaserName());
        updateInvoiceParam.setPurchaserRegisterNum(req.getPurchaserRegisterNum());
        updateInvoiceParam.setSellerName(req.getSellerName());
        updateInvoiceParam.setSellerRegisterNum(req.getSellerRegisterNum());
        updateInvoiceParam.setInvoiceNum(req.getInvoiceNum());
        updateInvoiceParam.setInvoiceCode(req.getInvoiceCode());
        updateInvoiceParam.setInvoiceAmt(req.getInvoiceAmt());
        updateInvoiceParam.setInvoiceTaxRate(req.getInvoiceTaxRate());
        updateInvoiceParam.setRemark(req.getRemark());
        updateInvoiceParam.setBalance(req.getInvoiceAmt());
        updateInvoiceParam.setInvoiceDate(invoiceDate);
        updateInvoiceParam.setInvoiceContent(req.getInvoiceContent());
        updateInvoiceParam.setUpdatedBy(req.getUpdatedBy());
        updateInvoiceParam.setRevision(existInvoiceById.getRevision());
        int i = supplierInvoiceMapper.updateInvoice(updateInvoiceParam);
        if (i == 0) {
            // 记录无更新
            log.error("进项票发票编辑失败：发票记录无更新");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM.errorCode, "发票NO:" + req.getInvoiceNum() + " 数据已变化请刷新后重试");
        }
        // 保存发票附件
        updateInvoiceAttch(req, updateInvoiceParam);
    }

    /**
     * 分页查询
     * <p>
     * PageHelper.使用踩坑！！！
     * 如果使用 PageHelper 插件来分页会存在一个问题，这个问题也可以说不是问题。
     * PageHelper 分页查询步骤分为两步：
     * 1. select COUNT(*) from 表名   where ？=？
     * 2. select * from 表名   where ？=？ limit 0,10
     * <p>
     * 可以知道第一先去查询 总记录数。此时坑逼的来了！！！
     * PageHelper 在查询分页之前 就是再准备 执行 select * from 表名  where ？=？ limit 0,10 之前 它会做了一个判断！
     * 它会根据 select COUNT(*) from 表名   where ？=？ 查询的总记录数来模拟分页，得到总页数
     * 公式： pages = (int) (total / pageSize + ((total % pageSize == 0) ? 0 : 1));
     * 得到总页数，底层会判断 当前查询页 如果大于 总页数。 那么 把 总页数 当成当前页来查询 ！这样就会造成我们始终能够分页查询数据出来
     * 坑死了！！！
     * 所以尽量不要 使用  PageHelper.startPage(req.getPageNum(),req.getPageSize());
     * 要使用 PageHelper.startPage(req.getPageNum(),req.getPageSize(),true,false,null); 把 reasonable 设置为 false 就可以解决这个问题
     *
     * @param req
     * @return
     */
    @Override
    public PaginationSupportDTO<SupplierInvoiceResp> invoicePage(SupplierInvoiceQueryPageReq req) {
        //查询发票分页
        PaginationSupportDTO<SupplierInvoiceResp> paginationSupportDTO = new PaginationSupportDTO<>();
        PageHelper.startPage(req.getCurrentPage(), req.getPageSize(), true, false, null);
        List<SupplierInvoiceResp> invoiceList = supplierInvoiceMapper.queryInvoiceList(req);
        for (SupplierInvoiceResp invoice : invoiceList) {
            if (null != invoice.getInvoiceTaxRate()) {
                invoice.setInvoiceRate(invoice.getInvoiceTaxRate().stripTrailingZeros().toPlainString() + "%");
            }
            invoice.setInvoiceAmtCur(invoice.getInvoiceAmt() + " CNY");
            invoice.setBalanceCur(invoice.getBalance() + " CNY");
            //查询发票附件
            FileReq fileReq = new FileReq();
            fileReq.setObjId(invoice.getInvoiceId());
            fileReq.setTableName("f_supplier_invoice");
            Response<List<FileResp>> fileResp = fileRemote.list(fileReq);
            if (fileResp.getModel() != null && fileResp.getModel().size() > 0) {
                List<InvoiceAttchDTO> photoList = InvoiceConvert.INSTANCE.invoiceAttchDTO(fileResp.getModel());
                // 使用 Comparator 接口实现自定义的比较逻辑
                Comparator<InvoiceAttchDTO> comparator = Comparator.comparing(InvoiceAttchDTO::getFileId).reversed();
                // 使用 Collections.sort() 方法进行排序
                photoList.sort(comparator);
                invoice.setUrl(photoList);
            }
        }
        PageInfo<SupplierInvoiceResp> page = new PageInfo<>(invoiceList);
        paginationSupportDTO.setItemList(page.getList());
        paginationSupportDTO.setCurrentPage(page.getPageNum());
        paginationSupportDTO.setPageSize(page.getPageSize());
        paginationSupportDTO.setTotalCount(page.getTotal());
        paginationSupportDTO.setTotalPage(page.getPages());
        return paginationSupportDTO;
    }

    @Override
    public SupplierInvoiceResp invoiceDetail(String merchantCode, SupplierInvoiceAditReq supplierInvoiceAditReq) {
        if (null == supplierInvoiceAditReq.getInvoiceId() || null == merchantCode) {
            log.error("进项票查询发票详情失败：请求参数为空！");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }

        //查询发票详情
        SupplierInvoiceResp invoiceDetail = supplierInvoiceMapper.queryInvoice(merchantCode, supplierInvoiceAditReq.getInvoiceId());
        if (invoiceDetail.getInvoiceTaxRate() != null) {
            invoiceDetail.setInvoiceRate(invoiceDetail.getInvoiceTaxRate().stripTrailingZeros().toPlainString() + "%");
        }
        //查询发票附件
        FileReq fileReq = new FileReq();
        fileReq.setObjId(supplierInvoiceAditReq.getInvoiceId().toString());
        fileReq.setTableName("f_supplier_invoice");
        Response<List<FileResp>> fileResp = fileRemote.list(fileReq);
        if (fileResp.getModel() != null && fileResp.getModel().size() > 0) {
            List<InvoiceAttchDTO> photoList = InvoiceConvert.INSTANCE.invoiceAttchDTO(fileResp.getModel());
            // 使用 Comparator 接口实现自定义的比较逻辑
            Comparator<InvoiceAttchDTO> comparator = Comparator.comparing(InvoiceAttchDTO::getFileId).reversed();
            // 使用 Collections.sort() 方法进行排序
            photoList.sort(comparator);
            List<InvoiceAttchDTO> photoList0 = new ArrayList<>();
            photoList0.add(photoList.get(0));
            invoiceDetail.setUrl(photoList0);
        }
        //查询详情(票单维度)
        SupplierInvoiceBillQueryReq queryInvoiceBillParam = new SupplierInvoiceBillQueryReq();
        queryInvoiceBillParam.setInvoiceId(supplierInvoiceAditReq.getInvoiceId().toString());
        queryInvoiceBillParam.setMerchantCode(merchantCode);
        List<SupplierInvoiceBillResp> supplierInvoiceBillList = supplierInvoiceBillMapper.queryInvoiceBillList(queryInvoiceBillParam);
        for (SupplierInvoiceBillResp resp : supplierInvoiceBillList) {
            BigDecimal uninvoicedAmt = resp.getInvoicePayableAmt().subtract(resp.getInvoicedAmt());
            resp.setUninvoicedAmt(uninvoicedAmt);
            resp.setInvoicePayableAmtCur("CNY " + resp.getInvoicePayableAmt());
            resp.setInvoicedAmtCur("CNY " + resp.getInvoicedAmt());
            resp.setUninvoicedAmtCur("CNY " + resp.getUninvoicedAmt());
        }
        invoiceDetail.setInvoiceBillList(supplierInvoiceBillList);
        return invoiceDetail;

    }

    @Override
    public void invoiceDel(Long invoiceId) {
        // 验证参数
        if (null == invoiceId) {
            log.error("进项票查询发票删除失败：请求参数为空！");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }

        // 校验是否存在该id的记录
        SupplierInvoiceEntity existInvoiceById = supplierInvoiceMapper.selectById(invoiceId);
        if (null == existInvoiceById) {
            log.error("进项票查询发票删除失败：该发票id的记录不存在," + invoiceId);
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM.errorCode, "进项票查询发票删除失败，该发票id的记录不存在," + invoiceId);
        }

        // 发票状态部分使用和已使用不可删除
        if (existInvoiceById.getInvoiceStatus() == InvoiceStatusEnum.PARTIAL_USE.getKey() || existInvoiceById.getInvoiceStatus() == InvoiceStatusEnum.USED.getKey()) {
            log.error("进项票发票删除失败：发票状态是部分使用或已使用，不可删除发票");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM.errorCode, "进项票发票删除失败：发票状态是部分使用或已使用，不可删除发票");
        }

        // 删除发票
        SupplierInvoiceEntity delParam = new SupplierInvoiceEntity();
        delParam.setInvoiceId(invoiceId);
        delParam.setRevision(existInvoiceById.getRevision());
        int i = supplierInvoiceMapper.delInvoice(delParam);
        if (i == 0) {
            // 记录无更新
            log.error("进项票发票删除失败");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM.errorCode, "发票NO:" + existInvoiceById.getInvoiceNum() + " 数据已变化请刷新后重试");
        }
    }

    @Override
    @Transactional
    public void invoiceSyncToTg(DdtToTgInvoiceSyncAditReq req) {
        // 校验入参
        validateInvoiceSyncToTgParam(req);

        //转换参数新增发票
        SupplierInvoiceAditReq supplierInvoiceAddParam = new SupplierInvoiceAditReq();
        supplierInvoiceAddParam.setMerchantCode(req.getSaasMerchantCode());
        supplierInvoiceAddParam.setInvoiceSourceType(InvoiceSourceTypeEnum.SYSTEM_SYNC.getKey());
        supplierInvoiceAddParam.setInvoiceType(req.getInvoiceType());
        supplierInvoiceAddParam.setInvoiceNum(req.getInvoiceNum());
        supplierInvoiceAddParam.setInvoiceCode(req.getInvoiceCode());
        supplierInvoiceAddParam.setPurchaserName(req.getPurchaserName());
        supplierInvoiceAddParam.setPurchaserRegisterNum(req.getPurchaserRegisterNum());
        supplierInvoiceAddParam.setSellerName(req.getSellerName());
        supplierInvoiceAddParam.setSellerRegisterNum(req.getSellerRegisterNum());
        supplierInvoiceAddParam.setInvoiceContent(req.getCommodityName());
        supplierInvoiceAddParam.setInvoiceAmt(req.getAmountInFiguers());
        supplierInvoiceAddParam.setInvoiceTaxRate(req.getInvoicetaxrate());
        supplierInvoiceAddParam.setTotalTaxAmt(req.getTotaltax());
        supplierInvoiceAddParam.setTotalAmt(req.getTotalAmount());
        supplierInvoiceAddParam.setRemark(req.getRemarks());
        supplierInvoiceAddParam.setCreatedBy(Constant.SYSTEM);
        supplierInvoiceAddParam.setCheckCode(req.getCheckCode());

        // 新增发票
        invoiceAdd(supplierInvoiceAddParam);
    }

    @Override
    public List<SupplierInvoiceResp> querySupplierBillInvoiceItemList(SupplierInvoiceQueryPageReq req) {
        if (null == req) {
            log.error("查询票单的发票明细失败：请求参数为空！");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
        if (null != req.getCurrentPage() && null != req.getPageSize()) {
            PageHelper.startPage(req.getCurrentPage(), req.getPageSize());
        }
        return supplierInvoiceMapper.querySupplierInvoiceBillInvoiceItemList(req);
    }

    @Override
    public PaginationSupportDTO<SupplierInvoiceResp> querySupplierInvoiceList(SupplierInvoiceQueryPageReq req) {
        // 校验公共参数
//        validateInvoiceableInvoiceListParam(req);

        if (req.getInvoiceStartDt() != null && req.getInvoiceEndDt() != null) {
            req.setInvoiceStartDt(req.getInvoiceStartDt() + " 00:00:00");
            req.setInvoiceEndDt(req.getInvoiceEndDt() + " 23:59:59");
        }
        //查询待开票发票列表
        PageHelper.startPage(req.getCurrentPage(), req.getPageSize());
        List<SupplierInvoiceResp> invoiceableList = supplierInvoiceMapper.querySupplierInvoiceableInvoiceList(req);
        PaginationSupportDTO<SupplierInvoiceResp> paginationSupportDTO = new PaginationSupportDTO<>();
        PageInfo<SupplierInvoiceResp> page = new PageInfo<>(invoiceableList);
        paginationSupportDTO.setItemList(invoiceableList);
        paginationSupportDTO.setCurrentPage(page.getPageNum());
        paginationSupportDTO.setPageSize(page.getPageSize());
        paginationSupportDTO.setTotalCount(page.getTotal());
        paginationSupportDTO.setTotalPage(page.getPages());

        return paginationSupportDTO;
    }

    @Override
    public List<SupplierInvoiceEntity> invoiceQuery(SupplierInvoiceAditReq req) {
        LambdaQueryWrapper<SupplierInvoiceEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StringUtils.isNotBlank(req.getInvoiceNum()), SupplierInvoiceEntity::getInvoiceNum, req.getInvoiceNum());
        queryWrapper.eq(null != req.getInvoiceId(), SupplierInvoiceEntity::getInvoiceId, req.getInvoiceId());
        return supplierInvoiceMapper.selectList(queryWrapper);
    }

    @Override
    public void updateById(SupplierInvoiceEntity entity) {
        supplierInvoiceMapper.updateById(entity);
    }

    /**
     * OCR自动录入：
     * <p>
     * 文件格式：仅支持jpg/jpeg/png/bmp 格式进行图片识别，可支持多张上传，最大支持2M，最大可一次上传20张
     * <p>
     * 文件大小：单个不能超过2M，总的不能超过20M
     *
     * @param files
     * @param loginUser
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response<Boolean> orcFileCreateInvoice(MultipartFile[] files, LoginUser loginUser) {
        if (files == null) {
            log.error("请求参数不能为空");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
        if (files.length > 20) {
            log.error("一次最多识别20个文件");
            throw new SysException(ErrorCodeEnum.MAX_TWENTY_FILE_FAILED.errorCode, ErrorCodeEnum.MAX_TWENTY_FILE_FAILED.errorDesc);
        }
        //次数校验 todo 限制商家ocr使用功能先不处理
//        MerchantOcrRechargeReq merchantOcrRechargeVo = new MerchantOcrRechargeReq();
//        merchantOcrRechargeVo.setMerchantCode(merchantCode);
//        ResultVo resultVo = adminFeign.queryMerchantOcrResidueCount(merchantOcrRechargeVo);
//        if (resultVo == null || resultVo.getResult() != 1) {
//            log.error("调用admin服务查询充值记录失败request=" + JSON.toJSONString(merchantOcrRechargeVo) + ",response=" + JSON.toJSONString(resultVo));
//            throw new SysException(ResultEnum.SYSTEM_ERROR.getCode(), ResultEnum.SYSTEM_ERROR.getMessage());
//        }
//        Integer count = MyJSONUtils.jsonToObject(resultVo.getData(), Integer.class);
//        if (count == null) {
//            throw new SysException("您未充值暂无法使用");
//        } else if (count < 1) {
//            throw new SysException("充值余额不足无法使用");
//        } else if (count < files.length) {
//            throw new SysException("剩余次数不足无法使用");
//        }

        List<SupplierInvoiceEntity> entityList = new ArrayList<>();//需要新增的发票信息
        List<FileReq> delFileIds = new ArrayList<>();//识别失败需要删除的文件id
        List<String> delFileUrls = new ArrayList<>();//识别失败需要删除的文件url
//        List<FileReq> fileVoList = new ArrayList<>();//绑定图片关系
        //todo 限制商家ocr使用功能先不处理
        List<InvoiceOcrEntity> merchantOcrItemVoList = new ArrayList<>();//记录识别信息并扣减费用
        Map<String, SupplierInvoiceEntity> invoiceMap = new HashMap<>();//存放本次新增发票信息，用与判断重复发票，key = 发票号码+发票代码
        String baiduToken = queryBaiDuOrcToken();//获取百度token
        Date currentDate = DateUtilX.getCurrentDate();

        //上传文件到服务器
        List<CompletableFuture<FileResp>> uploadFutures = new ArrayList<>();
        // 获取当前线程请求头信息(解决丢失请求头问题)
        RequestAttributes attributes = RequestContextHolder.getRequestAttributes();
        for (MultipartFile file : files) {
            uploadFutures.add(CompletableFuture.supplyAsync(() -> {
                RequestContextHolder.setRequestAttributes(attributes);
                Response<FileResp> upload = fileRemote.upload(file);
                return upload.getModel();
            }));
        }

        // 等待所有上传任务完成并获取结果
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(uploadFutures.toArray(new CompletableFuture[0]));
        try {
            allFutures.join();  // 阻塞等待所有上传任务完成
        } catch (Exception e) {
            log.error("异步上传文件失败", e);
            throw new SysException(ErrorCodeEnum.ASYN_FILE_UPLOAD_FAILED.errorCode, ErrorCodeEnum.ASYN_FILE_UPLOAD_FAILED.errorDesc);
        }

        // 获取所有上传结果
        List<FileResp> uploadFileResp = uploadFutures.stream()
                .map(CompletableFuture::join)
                .collect(Collectors.toList());

        //上传文件到服务器
        //异步执行
        List<CompletableFuture<Response<String>>> futures = new ArrayList<>();
        for (FileResp fileDto : uploadFileResp) {
            futures.add(CompletableFuture.supplyAsync(() -> {
                try {
                    //1.调用百度ocr接口识别
                    BaiduOcrResp resp = this.baiduOcr(fileDto, baiduToken);
                    JSONObject resultObj = resp.getResultObj();

                    if (!resultObj.containsKey("words_result_num") && resultObj.getIntValue("words_result_num") < 1) {
                        log.error("百度API识别图片失败，result=" + resp.getResult());
                        if (resultObj.containsKey("error_code")) {
                            log.error(BaiDuOcrErrorEnum.getValueByKey(resultObj.getIntValue("error_code")));
                            throw new SysException(ErrorCodeEnum.BAIDUOCR_IMAGE_RECOGNITION_FAILED.errorCode,
                                    ErrorCodeEnum.BAIDUOCR_IMAGE_RECOGNITION_FAILED.errorDesc);

                        } else {
                            throw new SysException(ErrorCodeEnum.BAIDUOCR_IMAGE_RECOGNITION_FAILED.errorCode, ErrorCodeEnum.BAIDUOCR_IMAGE_RECOGNITION_FAILED.errorDesc);
                        }
                    } else {
                        // 组装发票信息
                        SupplierInvoiceEntity entity = this.assembleInvoice(resultObj, loginUser.getCompanyCode(),
                                currentDate, loginUser.getUserAccount(), resp.getIsRollInvoice(), invoiceMap, InvoiceSourceTypeEnum.OCR.getKey());
                        entity.setUpdatedBy(fileDto.getFileId());
                        entityList.add(entity);
                        String invoiceKey = entity.getInvoiceNum() + entity.getInvoiceCode();
                        invoiceMap.put(invoiceKey, entity);
                        // 2.发票识别成功，发票验真 todo 测试先不验真
//                        this.baiduInvoiceVerification(entity,baiduToken);

                        // 组装记录发票识别
                        InvoiceOcrEntity merchantOcrItemVo = new InvoiceOcrEntity();
                        merchantOcrItemVo.setInvoiceCode(entity.getInvoiceCode());
                        merchantOcrItemVo.setInvoiceNum(entity.getInvoiceNum());
                        merchantOcrItemVo.setPurchaserName(entity.getPurchaserName());
                        merchantOcrItemVo.setSellerName(entity.getSellerName());
                        merchantOcrItemVo.setUrl(fileDto.getFileUrl());
                        merchantOcrItemVo.setOldFileName(fileDto.getFileName());
                        merchantOcrItemVo.setCreatedBy(loginUser.getUserAccount());
                        merchantOcrItemVo.setCreatedDt(currentDate);
                        merchantOcrItemVo.setInvoiceDate(entity.getInvoiceDate());
                        // todo 限制商家ocr使用功能先不处理
                        merchantOcrItemVoList.add(merchantOcrItemVo);
                    }
                    return Response.success(fileDto.getFileName());
                } catch (SysException e) {
                    log.error("识别发票失败", e);
                    // 需要删除服务器文件并删除数据记录
                    FileReq req = new FileReq();
                    req.setFileId(fileDto.getFileId());
                    delFileIds.add(req);

                    return Response.error(e.getCode(), e.getMessage());
                } catch (Exception e) {
                    log.error("识别发票异常", e);
                    // 需要删除服务器文件并删除数据记录
                    FileReq req = new FileReq();
                    req.setFileId(fileDto.getFileId());
                    delFileIds.add(req);

                    return Response.error(ErrorCodeEnum.BAIDUOCR_IMAGE_RECOGNITION_FAILED.errorCode, ErrorCodeEnum.BAIDUOCR_IMAGE_RECOGNITION_FAILED.errorDesc);
                }
            }));
        }

        // 等待全部完成
        CompletableFuture<Void> voidCompletableFuture = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
        List<Response<String>> resultList = voidCompletableFuture.thenApply(v ->
                futures.stream().map(CompletableFuture::join).collect(Collectors.toList())).join();
        try {
            CompletableFuture.allOf(voidCompletableFuture).get();
        } catch (Exception e) {
            log.error("异步识别发票识别失败", e);
            throw new SysException(ErrorCodeEnum.BAIDUOCR_IMAGE_RECOGNITION_FAILED.errorCode, ErrorCodeEnum.BAIDUOCR_IMAGE_RECOGNITION_FAILED.errorDesc);
        }

        // 判断OCR是否出现异常
        if (CollUtilX.isNotEmpty(resultList)) {
            List<Response<String>> collect = resultList.stream().filter(item -> item.getResult() == 0).collect(Collectors.toList());
            if (CollUtilX.isNotEmpty(collect)) {
                log.error("ocr处理失败:失败原因" + JSON.toJSONString(collect));
                throw new SysException(ErrorCodeEnum.BAIDUOCR_IMAGE_RECOGNITION_FAILED.errorCode, ErrorCodeEnum.BAIDUOCR_IMAGE_RECOGNITION_FAILED.errorDesc);
            }
        }

        // 新增发票信息
        if (CollUtilX.isNotEmpty(entityList)) {
            for (SupplierInvoiceEntity entity : entityList) {
                try {
                    String fileId = entity.getUpdatedBy();
                    entity.setUpdatedBy(null);
                    int insert = supplierInvoiceMapper.insert(entity);
                    if (insert == 0) {
                        throw new SysException(ErrorCodeEnum.SYSTEM_ADD_FAILED);
                    }
                    FileReq fileReq = new FileReq();
                    fileReq.setFileId(fileId);
                    fileReq.setObjId(entity.getInvoiceId().toString());
                    fileReq.setTableName("f_supplier_invoice");
                    fileRemote.update(fileReq);
                } catch (DuplicateKeyException exception) {
                    log.error("异常信息====================>>>" + exception);
                    throw new SysException(ParamErrorEnum.INVOICENUM_CORRES_INVOICE_EXIST);
                } catch (Exception e) {
                    log.error("异常信息====================>>>" + e);
                    throw new SysException(ErrorCodeEnum.SYSTEM_ADD_FAILED);
                }
            }
        }

        // todo 限制商家ocr使用功能先不处理
        // 记录识别信息并扣减费用
        if (CollUtilX.isNotEmpty(merchantOcrItemVoList)) {
            for (InvoiceOcrEntity ocrEntity : merchantOcrItemVoList) {
                int insert = invoiceOcrMapper.insert(ocrEntity);
                if (insert == 0) {
                    log.error("OCR解析记录表新增失败" + JSON.toJSONString(merchantOcrItemVoList) + ",response=" + JSON.toJSONString(ocrEntity));
                    throw new SysException(ErrorCodeEnum.SYSTEM_ADD_FAILED);
                }
            }
        }

        // 需要删除的文件数据
        if (CollUtilX.isNotEmpty(delFileIds)) {
            for (FileReq fileReq : delFileIds) {
                fileRemote.del(fileReq);
            }
        }

        return Response.success(true);
    }

    @Override
    public BigDecimal taxAmtCalculate(SupplierInvoiceAditReq req) {
        if (null == req) {
            log.error("进项票计算税额：请求参数为空！");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (null == req.getInvoiceAmt()) {
            log.error("进项票计算税额：请求参数为空！");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (null == req.getInvoiceTaxRate()) {
            log.error("进项票计算税额：请求参数为空！");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (req.getInvoiceAmt().compareTo(BigDecimal.ZERO) < 0) {
            log.error("进项票计算税额：发票金额小于等0！");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (req.getInvoiceTaxRate().compareTo(BigDecimal.ZERO) < 0) {
            log.error("进项票计算税额：税率小于等于0！");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }

        // 计算税额
        return totalTaxAmtCalculate(req.getInvoiceAmt(), req.getInvoiceTaxRate());
    }

//    @Override
//    public ResultVo invoiceImageUpload(MultipartFile file) throws IOException {
//        ResultVo<Object> response = new ResultVo<>();
//        response.setResult(ResultCodeEnum.SUCCESS.code);
//        FileResp fileResp = fileService.upload(file);
//        response.setModel(fileResp);
//        return response;
//    }

    /**
     * 计算合计税额
     *
     * @param invoiceAmt     发票金额
     * @param invoiceTaxRate 发票税率
     *                       公式 合计税额 = 价税 - (价税/(1+合计税率))
     */
    private BigDecimal totalTaxAmtCalculate(BigDecimal invoiceAmt, BigDecimal invoiceTaxRate) {
        // 税率转成小数
        BigDecimal invoiceTaxRateNum = invoiceTaxRate.divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
        // 合计税额 = 价税 - (价税/(1+合计税率))

        return invoiceAmt.subtract(invoiceAmt.divide(BigDecimal.ONE.add(invoiceTaxRateNum), 2, RoundingMode.HALF_UP));
    }


    /**
     * author: weihua
     * description: 校验发票添加参数
     */
    private void validateInvoiceAddParam(SupplierInvoiceAditReq req) {
        if (null == req) {
            log.error("进项票添加发票失败：请求参数为空！");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (null == req.getInvoiceType()) {
            log.error("进项票添加发票失败：请求参数为空,发票类型");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (null == req.getInvoiceSourceType()) {
            log.error("进项票添加发票失败：请求参数为空,发票来源类型");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (StrUtilX.isEmpty(req.getInvoiceNum())) {
            log.error("进项票添加发票失败：请求参数为空,发票号码");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (StrUtilX.isEmpty(req.getInvoiceDate())) {
            log.error("进项票添加发票失败：请求参数为空,发票日期");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (StrUtilX.isEmpty(req.getPurchaserName())) {
            log.error("进项票添加发票失败：请求参数为空,购买方名称");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (StrUtilX.isEmpty(req.getPurchaserRegisterNum())) {
            log.error("进项票添加发票失败：请求参数为空,购买方纳税人识别号");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (StrUtilX.isEmpty(req.getSellerName())) {
            log.error("进项票添加发票失败：请求参数为空,销售方名称");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (StrUtilX.isEmpty(req.getSellerRegisterNum())) {
            log.error("进项票添加发票失败：请求参数为空,销售费纳税人识别号");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (StrUtilX.isEmpty(req.getInvoiceContent())) {
            log.error("进项票添加发票失败：请求参数为空,发票类型");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (null == req.getInvoiceAmt()) {
            log.error("进项票添加发票失败：请求参数为空,发票金额");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (null == req.getInvoiceTaxRate()) {
            log.error("进项票添加发票失败：请求参数为空,发票税率");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (null == req.getTotalTaxAmt()) {
            log.error("进项票添加发票失败：请求参数为空,合计税额");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (req.getInvoiceAmt().compareTo(BigDecimal.ZERO) < 0) {
            log.error("进项票添加发票失败：发票金额小于0！");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (req.getInvoiceTaxRate().compareTo(BigDecimal.ZERO) < 0) {
            log.error("进项票添加发票失败：税率小于0！");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (req.getInvoiceTaxRate().compareTo(new BigDecimal(100)) > 0) {
            log.error("进项票添加发票失败：税率大于100%！");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
    }

    /**
     * author: weihua
     * description: 校验发票编辑参数
     */
    private void validateInvoiceEditParam(SupplierInvoiceAditReq req) {
        if (null == req) {
            log.error("进项票发票编辑失败：请求参数为空！");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (null == req.getInvoiceId()) {
            log.error("进项票发票编辑失败：请求参数发票id为空！");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (null == req.getInvoiceType()) {
            log.error("进项票发票编辑失败：请求参数为空,发票类型");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (StrUtilX.isEmpty(req.getInvoiceNum())) {
            log.error("进项票发票编辑失败：请求参数为空,发票号码");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (StrUtilX.isEmpty(req.getInvoiceDate())) {
            log.error("进项票发票编辑失败：请求参数为空,发票日期");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (StrUtilX.isEmpty(req.getPurchaserName())) {
            log.error("进项票发票编辑失败：请求参数为空,购买方名称");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (StrUtilX.isEmpty(req.getPurchaserRegisterNum())) {
            log.error("进项票发票编辑失败：请求参数为空,购买方纳税人识别号");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (StrUtilX.isEmpty(req.getSellerName())) {
            log.error("进项票发票编辑失败：请求参数为空,销售方名称");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (StrUtilX.isEmpty(req.getSellerRegisterNum())) {
            log.error("进项票发票编辑失败：请求参数为空,销售费纳税人识别号");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (StrUtilX.isEmpty(req.getInvoiceContent())) {
            log.error("进项票发票编辑失败：请求参数为空,发票类型");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (null == req.getInvoiceAmt()) {
            log.error("进项票发票编辑失败：请求参数为空,发票金额");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (null == req.getInvoiceTaxRate()) {
            log.error("进项票发票编辑失败：请求参数为空,发票税率");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (null == req.getTotalTaxAmt()) {
            log.error("进项票发票编辑失败：请求参数为空,合计税额");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (req.getInvoiceAmt().compareTo(BigDecimal.ZERO) < 0) {
            log.error("进项票发票编辑失败：发票金额小于0！");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (req.getInvoiceTaxRate().compareTo(BigDecimal.ZERO) < 0) {
            log.error("进项票发票编辑失败：税率小于0！");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (req.getInvoiceTaxRate().compareTo(new BigDecimal(100)) > 0) {
            log.error("进项票发票编辑失败：税率大于100%！");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
    }

    /**
     * author: weihua
     * description: 校验发票请求列表参数
     */
    private void validateInvoiceListParam(SupplierInvoiceQueryPageReq req) {
        if (null == req) {
            log.error("进项票发票查询列表：请求参数为空！");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (StrUtilX.isEmpty(req.getInvoiceStartDt())) {
            log.error("进项票发票查询列表：开票开始日期为空");
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_STARTDATE);
        } else if (StrUtilX.isEmpty(req.getInvoiceEndDt())) {
            log.error("进项票发票查询列表：开票结束日期为空");
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_ENDDATE);
        } else if (StrUtilX.isEmpty(req.getCreatedStartDt())) {
            log.error("进项票发票查询列表：创建开始日期为空！");
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_STARTDATE);
        } else if (StrUtilX.isEmpty(req.getCreatedEndDt())) {
            log.error("进项票发票查询列表：创建结束日期为空！");
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_ENDDATE);
        }
        Date invoiceStartDate = DateUtilX.stringToDate(req.getInvoiceStartDt());
        Date invoiceEndDate = DateUtilX.stringToDate(req.getInvoiceEndDt());
        Date createdStartDate = DateUtilX.stringToDate(req.getCreatedStartDt());
        Date createdEndDate = DateUtilX.stringToDate(req.getCreatedEndDt());

        if (createdStartDate.after(createdEndDate)) {
            log.error("进项票发票查询列表：创建开始日期大于创建结束日期");
            throw new SysException(ParamErrorEnum.CRSTARTDT_GT_ENDDT_ERROR);
        } else if (invoiceStartDate.after(invoiceEndDate)) {
            log.error("进项票发票查询列表：开票开始日期大于开票结束日期！");
            throw new SysException(ParamErrorEnum.STARTDT_GT_ENDDT_ERROR);
        } else if (DateUtilX.getDay(createdStartDate, createdEndDate) > 92) {
            log.error("进项票发票查询列表：创建日期跨度大于三个月！");
            throw new SysException(ParamErrorEnum.DATE_GTT_THREE_MONTH);
        } else if (DateUtilX.getDay(invoiceStartDate, invoiceEndDate) > 92) {
            log.error("进项票发票查询列表：开票日期跨度大于三个月");
            throw new SysException(ParamErrorEnum.DATE_GTT_THREE_MONTH);
        }
    }

    /**
     * 校验同步发票参数
     */
    private void validateInvoiceSyncToTgParam(DdtToTgInvoiceSyncAditReq req) {
        if (null == req) {
            log.error("进项票发票同步发票失败：请求参数为空");
            throw new SysException(ParamErrorEnum.INVALID_INPUTPARAM);
        } else if (null == req.getInvoiceType()) {
            log.error("进项票发票同步发票失败：请求参数发票类型为空");
            throw new SysException(ParamErrorEnum.INVALID_INPUTPARAM);
        } else if (StrUtilX.isEmpty(req.getInvoiceNum())) {
            log.error("进项票发票同步发票失败：请求参数发票号码为空");
            throw new SysException(ParamErrorEnum.INVALID_INPUTPARAM);
        } else if (StrUtilX.isEmpty(req.getPurchaserRegisterNum())) {
            log.error("进项票发票同步发票失败：请求参数购买方纳税人识别号为空");
            throw new SysException(ParamErrorEnum.INVALID_INPUTPARAM);
        } else if (StrUtilX.isEmpty(req.getPurchaserName())) {
            log.error("进项票发票同步发票失败：请求参数购买方名称为空");
            throw new SysException(ParamErrorEnum.INVALID_INPUTPARAM);
        } else if (StrUtilX.isEmpty(req.getSellerRegisterNum())) {
            log.error("进项票发票同步发票失败：请求参数销售方纳税人识别号为空");
            throw new SysException(ParamErrorEnum.INVALID_INPUTPARAM);
        } else if (StrUtilX.isEmpty(req.getSellerName())) {
            log.error("进项票发票同步发票失败：请求参数销售方名称为空");
            throw new SysException(ParamErrorEnum.INVALID_INPUTPARAM);
        } else if (StrUtilX.isEmpty(req.getInvoiceDate())) {
            log.error("进项票发票同步发票失败：请求参数发票日期为空");
            throw new SysException(ParamErrorEnum.INVALID_INPUTPARAM);
        } else if (StrUtilX.isEmpty(req.getCommodityName())) {
            log.error("进项票发票同步发票失败：请求参数发票内容为空");
            throw new SysException(ParamErrorEnum.INVALID_INPUTPARAM);
        } else if (null == req.getAmountInFiguers()) {
            log.error("进项票发票同步发票失败：请求参数发票金额为空");
            throw new SysException(ParamErrorEnum.INVALID_INPUTPARAM);
        } else if (null == req.getTotaltax()) {
            log.error("进项票发票同步发票失败：请求参数合计税额为空");
            throw new SysException(ParamErrorEnum.INVALID_INPUTPARAM);
        } else if (null == req.getInvoicetaxrate()) {
            log.error("进项票发票同步发票失败：请求参数发票税率为空");
            throw new SysException(ParamErrorEnum.INVALID_INPUTPARAM);
        } else if (StrUtilX.isEmpty(req.getIreveAgentCode())) {
            log.error("进项票发票同步发票失败：请求参数ireve客户编码为空");
            throw new SysException(ParamErrorEnum.INVALID_INPUTPARAM);
        } else if (StrUtilX.isEmpty(req.getSaasMerchantCode())) {
            log.error("进项票发票同步发票失败：请求参数天宫商家编码为空");
            throw new SysException(ParamErrorEnum.INVALID_INPUTPARAM);
        }
    }

    /**
     * 校验可开票发票列表参数
     */
    private void validateInvoiceableInvoiceListParam(SupplierInvoiceQueryPageReq req) {
        if (null == req) {
            log.error("绑定发票查询列表：请求参数为空！");
            throw new SysException(ParamErrorEnum.INVALID_INPUTPARAM);
        } else if (StrUtilX.isEmpty(req.getInvoiceStartDt())) {
            log.error("绑定发票查询列表：开票开始日期为空");
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_STARTDATE);
        } else if (StrUtilX.isEmpty(req.getInvoiceEndDt())) {
            log.error("绑定发票查询列表：开票结束日期为空");
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_ENDDATE);
        }
        Date invoiceStartDate = DateUtilX.stringToDate(req.getInvoiceStartDt());
        Date invoiceEndDate = DateUtilX.stringToDate(req.getInvoiceEndDt());

        if (invoiceStartDate.after(invoiceEndDate)) {
            log.error("绑定发票查询列表：开票开始日期大于开票结束日期！");
            throw new SysException(ParamErrorEnum.STARTDT_GT_ENDDT_ERROR);
        } else if (DateUtilX.getDay(invoiceStartDate, invoiceEndDate) > 92) {
            log.error("绑定发票查询列表：开票日期跨度大于三个月");
            throw new SysException(ParamErrorEnum.DATE_GTT_THREE_MONTH);
        }
    }

    /**
     * 获取百度token
     * Access token默认有效期为30天
     *
     * @return
     */
    public String queryBaiDuOrcToken() {
        Map<String, String> map = new HashMap<>();
        //grant_type固定为client_credentials
        map.put("grant_type", "client_credentials");
        map.put("client_id", settingsConstant.getBaiduClientId());
        map.put("client_secret", settingsConstant.getBaiduClientSecret());
        String result = HttpUtilX.get(settingsConstant.getBaiduOrcTokenUrl(), map, null);
        log.info("获取百度token返回结果：" + result);
        if (StringUtils.isBlank(result)) {
            log.error("获取百度token失败，返回结果为空,请求参数:" + JSON.toJSONString(map));
            throw new SysException(ErrorCodeEnum.GET_BAIDUORC_TOKEN.errorCode, ErrorCodeEnum.GET_BAIDUORC_TOKEN.errorDesc);
        }
        String baiduToken = JSON.parseObject(result).getString("access_token");
        if (StringUtils.isBlank(baiduToken)) {
            log.error("获取百度token失败，返回结果不正确{}", result);
            throw new SysException(ErrorCodeEnum.GET_BAIDUORC_TOKEN.errorCode, ErrorCodeEnum.GET_BAIDUORC_TOKEN.errorDesc);
        }
        return baiduToken;
    }

    /**
     * 百度orc识别
     *
     * @param fileDto    识别文件参数
     * @param baiduToken 百度token
     * @return
     */
    public BaiduOcrResp baiduOcr(FileResp fileDto, String baiduToken) {
        if (StringUtils.isBlank(fileDto.getFileUrl())) {
            log.error("百度orc识别：文件路径为空");
            throw new SysException(ErrorCodeEnum.FILE_PATH_ISEMPTY.errorCode, ErrorCodeEnum.FILE_PATH_ISEMPTY.errorDesc);
        } else if (StringUtils.isBlank(fileDto.getFileFormat())) {
            log.error("百度orc识别：文件类型为空");
            throw new SysException(ErrorCodeEnum.FILE_TYPE_ISEMPTY.errorCode, ErrorCodeEnum.FILE_TYPE_ISEMPTY.errorDesc);
        } else if (StringUtils.isBlank(fileDto.getFileId())) {
            log.error("百度orc识别：文件id为空");
            throw new SysException(ErrorCodeEnum.FILE_ID_ISEMPTY.errorDesc, ErrorCodeEnum.FILE_ID_ISEMPTY.errorDesc);
        }
        //格式校验是否通过
        boolean flag = false;
        for (String type : invoiceFileAllowType) {
            if (fileDto.getFileFormat().equalsIgnoreCase(type)) {
                flag = true;
                break;
            }
        }
        if (!flag) {
            log.error("上传失败,文件的类型是:" + fileDto.getFileFormat() + ",不能上传该类型文件!");
            throw new SysException(ErrorCodeEnum.FILE_TYPE_MISMATCH.errorCode, ErrorCodeEnum.FILE_TYPE_MISMATCH.errorDesc);
        }
        //参数组装
        StringBuilder paramStr = new StringBuilder();
        try {
            byte[] imgData = InvoiceUtil.readFileByBytes(fileDto.getFileUrl());
            String imageStr = InvoiceUtil.encode(imgData);
            String imgParam = URLEncoder.encode(imageStr, "UTF-8");
            if ("pdf".equals(fileDto.getFileFormat())) {
                paramStr.append("pdf_file=").append(imgParam);
            } else if (!PRODUCTION_BAIDU_CLIENT_ID.equals(settingsConstant.getBaiduClientId())) {
                paramStr.append("image=").append(imgParam);
            } else {
                //生产环境并是图片类型使用url
                paramStr.append("url=").append(fileDto.getFileUrl());
            }
        } catch (Exception e) {
            log.error("文件解析异常", e);
            throw new SysException(ErrorCodeEnum.OPERATE_FAIL);
        }

        // 进行识别的增值税发票类 normal：可识别增值税普票、专票、电子发票 roll：可识别增值税卷票 默认为 normal
        String result = null;
        //重试
        for (int i = 0; i < settingsConstant.getBaiduMaxRetryCount(); i++) {
            long startTime1 = System.currentTimeMillis();
            try {
                result = InvoiceUtil.baiduOcrPost(settingsConstant.getBaiduOrcInvoiceUrl(), baiduToken, paramStr + "&type=normal");
            } catch (Exception e) {
                log.error("调用百度API识别图片异常", e);
            }
            log.info("OCR非卷票第" + i + 1 + "次识别用时" + (System.currentTimeMillis() - startTime1) + " ms, result=" + result);
            if (StringUtils.isBlank(result) || result.contains("Open api qps request limit reached")) {
                log.info("OCR非卷票第" + i + 1 + "次返回结果不正确，需要重试！result=" + result);
                try {
                    //休眠一秒,再重试
                    Thread.sleep(1000);
                } catch (Exception e) {
                    log.error("调用百度API识别图片异常", e);
                }
            } else {
                break;
            }
        }
        if (StringUtils.isBlank(result)) {
            throw new SysException(ErrorCodeEnum.BAIDUOCR_IMAGE_RECOGNITION_FAILED.errorCode, ErrorCodeEnum.BAIDUOCR_IMAGE_RECOGNITION_FAILED.errorDesc);
        }
        // 是否为卷票 false 使用普通发票识别，true 使用券票识别
        boolean isRollInvoice = false;
        JSONObject resultObj = JSON.parseObject(result);
        if (result.contains("failed to match the template")) {
            isRollInvoice = true;
        } else if (resultObj.containsKey("words_result_num") && resultObj.getIntValue("words_result_num") > 0
                && resultObj.getJSONObject("words_result").getBigDecimal("AmountInFiguers") == null) {
            isRollInvoice = true;
        }

        //为卷票再次调用接口
        if (isRollInvoice) {
            //重试
            for (int i = 0; i < settingsConstant.getBaiduMaxRetryCount(); i++) {
                long startTime2 = System.currentTimeMillis();
                try {
                    result = InvoiceUtil.baiduOcrPost(settingsConstant.getBaiduOrcInvoiceUrl(), baiduToken, paramStr + "&type=roll");
                } catch (Exception e) {
                    log.error("调用百度API识别图片异常", e);
                }
                log.info("OCR卷票第" + i + 1 + "次识别用时" + (System.currentTimeMillis() - startTime2) + " ms, result=" + result);
                if (StringUtils.isBlank(result) || result.contains("Open api qps request limit reached")) {
                    log.info("OCR卷票第" + i + 1 + "次返回结果不正确，需要重试！result=" + result);
                    try {
                        //休眠一秒,再重试
                        Thread.sleep(1000);
                    } catch (Exception e) {
                        log.error("调用百度API识别图片异常", e);
                    }
                    continue;
                } else {
                    break;
                }
            }
            if (StringUtils.isBlank(result)) {
                throw new SysException(ErrorCodeEnum.BAIDUOCR_IMAGE_RECOGNITION_FAILED.errorCode, ErrorCodeEnum.BAIDUOCR_IMAGE_RECOGNITION_FAILED.errorDesc);
            }
            resultObj = JSON.parseObject(result);
        }
        BaiduOcrResp resp = new BaiduOcrResp();
        resp.setResult(result);
        resp.setResultObj(resultObj);
        resp.setIsRollInvoice(isRollInvoice);

        return resp;
    }

    /**
     * 组装发票信息
     *
     * @param resultObj
     * @param merchantCode  商家编码
     * @param currentDate   当前时间
     * @param fullUserName  操作人
     * @param isRollInvoice 是否为卷票
     * @param invoiceMap    本次已识别的发票，判断是否是重复发票用
     * @return
     */
    public SupplierInvoiceEntity assembleInvoice(JSONObject resultObj,
                                                 String merchantCode,
                                                 Date currentDate,
                                                 String fullUserName,
                                                 Boolean isRollInvoice,
                                                 Map<String, SupplierInvoiceEntity> invoiceMap,
                                                 Integer sourceType) {
        JSONObject baiduInvoice = resultObj.getJSONObject("words_result");
        SupplierInvoiceEntity entity = new SupplierInvoiceEntity();
        entity.setInvoiceNum(baiduInvoice.getString("InvoiceNum"));
        entity.setInvoiceCode(baiduInvoice.getString("InvoiceCode"));
        entity.setPurchaserName(baiduInvoice.getString("PurchaserName"));
        entity.setPurchaserRegisterNum(baiduInvoice.getString("PurchaserRegisterNum"));
        entity.setSellerName(baiduInvoice.getString("SellerName"));
        entity.setSellerRegisterNum(baiduInvoice.getString("SellerRegisterNum"));
        String invoiceDate = baiduInvoice.getString("InvoiceDate");
        entity.setInvoiceDate(DateUtilX.stringToDate(invoiceDate, invoiceDate.indexOf("年") > 0 ? "yyyy年MM月dd" : "yyyy-MM-dd"));
        entity.setInvoiceContent((String) baiduJsonGetWord("CommodityName", baiduInvoice, false, true));
        entity.setRemark(baiduInvoice.getString("Remarks"));
        entity.setBillUsedAmt(BigDecimal.ZERO);
        entity.setMerchantCode(merchantCode);
        entity.setInvoiceSourceType(sourceType);
        entity.setCheckCode(baiduInvoice.getString("CheckCode"));
        entity.setCreatedDt(currentDate);
        entity.setCreatedBy(fullUserName);

        // 税额合计
        String totalTaxString = baiduInvoice.getString("TotalTax");

        // 货物列表税率拼接
        String commodityTaxRate = (String) baiduJsonGetWord("CommodityTaxRate", baiduInvoice, false, true);

        if ("免税".equals(commodityTaxRate)) {
            // 免税发票  的 税额合计设为0
            entity.setTotalTaxAmt(BigDecimal.ZERO);
        } else if (commodityTaxRate.contains("免税")
                && StringUtils.isBlank(commodityTaxRate.replaceAll("免税", "").replaceAll(",", ""))) {
            // 有免税字样，替换掉 免税 和逗号，还不为空说明免税和非免税的开到了一张票上，发票设为待检查
            entity.setInvoiceStatus(InvoiceStatusEnum.PENDING.key);
            log.info("发票商品列表，免税和非免税开一张，发票待检查  bai return json =>" + baiduInvoice.toJSONString());
        } else {
            // 不为空并不包含星号才设置税额
            if (totalTaxString != null && !totalTaxString.contains("*")) {
                entity.setTotalTaxAmt(new BigDecimal(totalTaxString));
            }
        }
        // 合计金额(未税金额)
        BigDecimal totalAmount = baiduInvoice.getBigDecimal("TotalAmount");
        entity.setTotalAmt(totalAmount);
        //合计税额
        BigDecimal totalTax = entity.getTotalTaxAmt();
        //价税合计
        BigDecimal invoiceAmt = baiduInvoice.getBigDecimal("AmountInFiguers");
        entity.setInvoiceAmt(invoiceAmt);
        entity.setBalance(invoiceAmt);
        if (totalTax == null) {
            entity.setTotalTaxAmt(BigDecimal.ZERO);
            entity.setInvoiceTaxRate(BigDecimal.ZERO);
            totalTax = BigDecimal.ZERO;
        }

        if (entity.getInvoiceStatus() == null) {
            if (invoiceAmt.compareTo(totalTax.add(totalAmount)) != 0) {
                log.info("价税合计  != 未税金额+ 税额合计 。发票待检查 " + entity.getInvoiceNum() + ",bai return json =>" + baiduInvoice.toJSONString());
                entity.setInvoiceStatus(InvoiceStatusEnum.PENDING.key);
            } else {
                entity.setInvoiceStatus(InvoiceStatusEnum.UNUSED.key);
                // 计算发票总的税率，公式 : 合计税额 /（价税合计-合计税额 ）;  分母为0不计算税率（卷票会是这种情况）
                if (BigDecimal.ZERO.compareTo(entity.getInvoiceAmt().subtract(entity.getTotalTaxAmt())) != 0) {
                    entity.setInvoiceTaxRate(new BigDecimal(100).multiply(entity.getTotalTaxAmt().
                            divide(entity.getInvoiceAmt().subtract(entity.getTotalTaxAmt()), 2, RoundingMode.HALF_EVEN)
                    ));
                }
            }
        }

        //卷票
        if (isRollInvoice) {
            entity.setInvoiceType(InvoiceTypeEnums.ROLL_TYPE.getKey());
            entity.setInvoiceTaxRate(BigDecimal.ZERO);
            entity.setTotalTaxAmt(BigDecimal.ZERO);
            if (entity.getInvoiceAmt() != null) {
                entity.setInvoiceStatus(InvoiceStatusEnum.UNUSED.key);
            }
        } else {
            entity.setInvoiceType(InvoiceTypeEnums.getKeyByValue(baiduInvoice.getString("InvoiceType")));
        }

        //如果为空或未使用或待检查，校验是不是重复发票
        if (entity.getInvoiceStatus() == null || entity.getInvoiceStatus() == InvoiceStatusEnum.UNUSED.getKey() ||
                entity.getInvoiceStatus() == InvoiceStatusEnum.PENDING.getKey()) {
            //验重 如果是多个发票识别，先校验本次识别发票再校验数据库数据
            if (invoiceMap != null && invoiceMap.size() != 0) {
                String invoiceKey = entity.getInvoiceNum() + entity.getInvoiceCode();
                if (invoiceMap.containsKey(invoiceKey)) {
                    entity.setInvoiceStatus(InvoiceStatusEnum.REPETITION.getKey());
                }
            }
            if (entity.getInvoiceStatus() == null || entity.getInvoiceStatus() != InvoiceStatusEnum.REPETITION.getKey()) {
                List<SupplierInvoiceEntity> list = supplierInvoiceMapper.selectList(new QueryWrapper<SupplierInvoiceEntity>()
                        .eq("merchant_code", entity.getMerchantCode())
                        .eq("invoice_num", entity.getInvoiceNum())
                        .eq("invoice_code", entity.getInvoiceCode()));
                if (CollUtilX.isNotEmpty(list)) {
                    entity.setInvoiceStatus(InvoiceStatusEnum.REPETITION.getKey());
                }
            }
        }

        return entity;
    }

    /**
     * 解析百度数据 数据，提取出结果
     *
     * @param jsonPro
     * @param baiduResultObj
     * @param returnIsNum          是否返回数字
     * @param strRemoveDuplication 返回String 是否去重
     */
    private Object baiduJsonGetWord(String jsonPro, JSONObject baiduResultObj, boolean returnIsNum, boolean strRemoveDuplication) {
        StringBuilder sb = new StringBuilder();
        List<String> strList = new ArrayList<String>();
        BigDecimal sum = BigDecimal.ZERO;
        if (baiduResultObj.containsKey(jsonPro)) {
            JSONArray array = baiduResultObj.getJSONArray(jsonPro);
            for (int i = 0; i < array.size(); i++) {
                if (returnIsNum) {
                    sum = sum.add(array.getJSONObject(i).getBigDecimal("word"));
                } else {
                    String str = array.getJSONObject(i).getString("word");
                    if (!(strRemoveDuplication && strList.contains(str))) {
                        strList.add(str);
                    }
                }
            }
        }

        for (String s : strList) {
            sb.append(",").append(s);
        }

        if (sb.length() == 0) {
            sb.append(",");
        }
        return returnIsNum ? sum : sb.substring(1);
    }

    /**
     * 百度发票验真
     */
    public void baiduInvoiceVerification(SupplierInvoiceEntity entity, String baiduToken) {
        if (StringUtils.isEmpty(entity.getInvoiceNum())
                && entity.getInvoiceDate() == null
                && entity.getInvoiceType() == null
                && entity.getTotalTaxAmt() == null) {
            log.error("发票验真不成功entity=" + JSON.toJSONString(entity));
            throw new SysException(ErrorCodeEnum.BAIDUOCR_VERIFY_FAILED.errorCode, ErrorCodeEnum.BAIDUOCR_VERIFY_FAILED.errorDesc);
        } else {
            StringBuilder sbStr = new StringBuilder();
            sbStr.append("invoice_code=").append(entity.getInvoiceCode())
                    .append("&invoice_num=").append(entity.getInvoiceNum())
                    .append("&invoice_date=").append(DateUtilX.dateToString(entity.getInvoiceDate(), "yyyyMMdd"));
            if (entity.getInvoiceType() != null) {
                InvoiceTypeEnums invoiceTypeEnum = InvoiceTypeEnums.getEnumByKey(entity.getInvoiceType());
                if (invoiceTypeEnum != null) {
                    sbStr.append("&invoice_type=").append(invoiceTypeEnum.baiDuCode);
                }
            }

            /*
             * 发票金额。增值税专票、电子专票、货运专票、机动车销售发票填写不含税金额；
             * 二手车销售发票填写车价合计；
             * 全电发票（专用发票）、全电发票（普通发票）填写价税合计金额，其他类型发票可为空
             */
            if (null != entity.getInvoiceType() && (InvoiceTypeEnums.A_E_NORMAL.key == entity.getInvoiceType()
                    || InvoiceTypeEnums.A_E_SPECIAL.key == entity.getInvoiceType())) {
                //价税合计金额
                sbStr.append("&total_amount=").append(entity.getInvoiceAmt());
            } else {
                //不含税金额
                sbStr.append("&total_amount=").append(entity.getTotalAmt());
            }

            if (StringUtils.isNotEmpty(entity.getCheckCode()) && entity.getCheckCode().length() >= 6) {
                sbStr.append("&check_code=").append(entity.getCheckCode().substring(entity.getCheckCode().length() - 6));
            } else {
                sbStr.append("&check_code=");
            }

            String validResultJson = null;
            //当前重试次数
            int retry_count = 0;
            //确认拿到百度
            boolean resultFlag = true;

            while (resultFlag) {
                // 去验真
                try {
                    log.info("验真请求对象：" + sbStr);
                    validResultJson = InvoiceUtil.baiduOcrPost(settingsConstant.getBaiduOrcInvoiceVerificationUrl(), baiduToken, sbStr.toString());
                } catch (Exception e) {
                    log.error("调用百度发票验真接口失败", e);
                }
                if (StringUtils.isEmpty(validResultJson)) {
                    continue;
                }
                //返回 QPS 超限，
                if (validResultJson.contains("Open api qps request limit reached")) {
                    retry_count++;
                    if (retry_count > settingsConstant.getBaiduMaxRetryCount()) {
                        log.error("重试" + settingsConstant.getBaiduMaxRetryCount() + "发票验真次失败，param=>" + sbStr + ",返回结果=>" + validResultJson);
                        break;
                    }
                    if (retry_count > 1) {
                        try {
                            Thread.sleep(1100);
                        } catch (Exception e) {
                            log.error("发票验真异常", e);
                        }
                        log.info("第" + retry_count + "次请求验真,param=> " + sbStr + ",返回结果=>" + validResultJson);
                    }
                } else {
                    JSONObject baiduResult = JSON.parseObject(validResultJson);

                    if (baiduResult.containsKey("error_code")) {
                        log.error("百度OCR验真失败,失败原因：error_code:" + baiduResult.getString("error_code") + ",error_msg:" + baiduResult.getString("error_msg"));
                        throw new SysException(ErrorCodeEnum.BAIDUOCR_VERIFY_FAILED.errorCode, ErrorCodeEnum.BAIDUOCR_VERIFY_FAILED.errorDesc);
                    } else if (!"0001".equals(baiduResult.getString("VerifyResult"))) {
                        log.error("百度OCR验真失败,失败原因：VerifyResult:" + baiduResult.getString("VerifyResult") + ",VerifyMessage:" + baiduResult.getString("VerifyMessage"));
                        throw new SysException(ErrorCodeEnum.BAIDUOCR_VERIFY_FAILED.errorCode, ErrorCodeEnum.BAIDUOCR_VERIFY_FAILED.errorDesc);
                    } else if ("0001".equals(baiduResult.getString("VerifyResult"))) {
                        //是否作废（冲红）。Y：已作废；H：已冲红；N：未作废
                        String InvalidSign = baiduResult.getString("InvalidSign");
                        if ("N".equals(InvalidSign)) {
                            //发票验真成功
                        } else if ("Y".equals(InvalidSign)) {
                            log.error("验真结果：已作废");
                            throw new SysException(ErrorCodeEnum.BAIDUOCR_VERIFY_FAILED.errorCode, ErrorCodeEnum.BAIDUOCR_VERIFY_FAILED.errorDesc);
                        } else if ("H".equals(InvalidSign)) {
                            log.error("验真结果：已冲红");
                            throw new SysException(ErrorCodeEnum.BAIDUOCR_VERIFY_FAILED.errorCode, ErrorCodeEnum.BAIDUOCR_VERIFY_FAILED.errorDesc);
                        } else {
                            log.error("验真结果：百度验真未被解析的响应");
                            throw new SysException(ErrorCodeEnum.BAIDUOCR_VERIFY_FAILED.errorCode, ErrorCodeEnum.BAIDUOCR_VERIFY_FAILED.errorDesc);
                        }
                    } else {
                        log.error("验真结果：百度验真未被解析的响应");
                        throw new SysException(ErrorCodeEnum.BAIDUOCR_VERIFY_FAILED.errorCode, ErrorCodeEnum.BAIDUOCR_VERIFY_FAILED.errorDesc);
                    }
                    resultFlag = false;
                }
            }
        }
    }

}

