package com.tiangong.invoice.supplierInvoice.service;

import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.invoice.req.InvoiceableSupplierInfoQueryReq;
import com.tiangong.invoice.resp.InvoiceableSupplierInfoResp;

public interface SupplierInvoiceOrderService {

    /**
     * 待开票供应商信息列表
     */
    PaginationSupportDTO<InvoiceableSupplierInfoResp> invoiceableSupplierInfoPage(InvoiceableSupplierInfoQueryReq req);
}
