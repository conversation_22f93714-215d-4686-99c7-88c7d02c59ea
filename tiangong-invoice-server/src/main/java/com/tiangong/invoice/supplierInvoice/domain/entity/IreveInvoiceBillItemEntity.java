package com.tiangong.invoice.supplierInvoice.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单通-天宫票单对应表
 * 表的实体类，参数和表字段一一对应
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-22 10:49:56
 */
@Data
@TableName("f_ireve_invoice_bill_item")
public class IreveInvoiceBillItemEntity extends Model<IreveInvoiceBillItemEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 自增长主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 订单通订单编码
     */
    private String ddtOrderCode;
    /**
     * 订单通订单开票金额
     */
    private BigDecimal ddtInvoiceAmount;
    /**
     * 订单通币种
     */
    private String ddtCurrency;
    /**
     * 订单通支付类型 订单通支付类型：0:预付无佣金，1:预付有佣金，2:到店付无佣金，3:到店付有佣金
     */
    private Integer ddtPayMethod;
    /**
     * 订单通票单id
     */
    private String ddtInvoiceBillId;
    /**
     * 天宫供应商票单id
     */
    private Long tgInvoiceBillId;
    /**
     * 天宫供货单票单明细id
     */
    private Long tgSupplierInvoiceBillItemId;
    /**
     * 天宫供货单编码
     */
    private String supplyOrderCode;
    /**
     * 天宫支付方式 支付方式：1-预付 2面付无佣金
     */
    private Integer tgPayMethod;
    /**
     * 删除状态
     */
    private Integer deleted;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private Date createdDt;
    /**
     * 更新人
     */
    private String updatedBy;
    /**
     * 更新时间
     */
    private Date updatedDt;

}
