package com.tiangong.invoice.supplierInvoice.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tiangong.invoice.resp.FinanceLogResp;
import com.tiangong.invoice.supplierInvoice.domain.entity.FinanceLogEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;


/**
 * 财务日志表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-22 10:49:56
 */
@Mapper
public interface FinanceLogMapper extends BaseMapper<FinanceLogEntity> {

    List<FinanceLogResp> queryLogList(FinanceLogEntity req);
}
