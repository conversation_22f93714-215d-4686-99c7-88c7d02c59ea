package com.tiangong.invoice.agentInvoice.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 供应商票单表
 * 表的实体类，参数和表字段一一对应
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-22 10:49:56
 */
@Data
@TableName("f_agent_invoice_bill")
public class AgentInvoiceBillEntity extends Model<AgentInvoiceBillEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 票单id
     */
    @TableId(value = "invoice_bill_id", type = IdType.AUTO)
    private Long invoiceBillId;
    /**
     * 票单编码
     */
    private String invoiceBillCode;

    /**
     * 票单编码
     */
    private String merchantCode;
    /**
     * 票单名称
     */
    private String invoiceBillName;
    /**
     * 客户编码
     */
    private String agentCode;
    /**
     * 客户名称
     */
    private String agentName;
    /**
     * 票单状态 票单状态：0未绑定，9已坏账，10已平账
     */
    private Integer invoiceBillStatus;

    /**
     * 开始日期
     */
    private Date startDate;
    /**
     * 结束日期
     */
    private Date endDate;
    /**
     * 日期类型 日期类型：0-下单日期、1-入住日期、2-离店日期
     */
    private Integer dateType;
    /**
     * 乐观锁版本号
     */
    private Integer revision;
    /**
     * 备注
     */
    private String remark;
    /**
     * 确认时间
     */
    private Date confirmDt;
    /**
     * 删除状态
     */
    private Integer deleted;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private Date createdDt;
    /**
     * 更新人
     */
    private String updatedBy;
    /**
     * 更新时间
     */
    private Date updatedDt;

    /**
     * 票单单数
     */
    private Integer invoicesNum;

    /**
     * 应开票金额
     */
    private BigDecimal invoicePayableAmt;

    /**
     * 已开票金额
     */
    private BigDecimal invoicedAmt;

    /**
     * 确认人
     */
    private String confirmer;
    /**
     * 票单来源 0 手动 1 自动
     */
    private Integer sourceType;
}
