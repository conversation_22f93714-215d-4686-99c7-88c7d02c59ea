package com.tiangong.invoice.agentInvoice.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tiangong.invoice.agentInvoice.domain.entity.AgentInvoiceBillEntity;
import com.tiangong.invoice.dto.AgentOrderInfoDTO;
import com.tiangong.invoice.req.AgentInvoiceBillAditReq;
import com.tiangong.invoice.req.AgentInvoiceBillQueryReq;
import com.tiangong.invoice.req.SupplierInvoiceBillQueryReq;
import com.tiangong.invoice.resp.AgentInvoiceBillResp;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 供应商票单表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-22 10:49:56
 */
@Mapper
public interface AgentInvoiceBillMapper extends BaseMapper<AgentInvoiceBillEntity> {

    List<AgentInvoiceBillResp> queryInvoiceBillList(AgentInvoiceBillQueryReq queryInvoiceBillParam);

    /**
     * 查询销项票单列表（分页）
     */
    IPage<AgentInvoiceBillResp> queryInvoiceBillPage(IPage<?> page, AgentInvoiceBillQueryReq req);

    List<AgentOrderInfoDTO> queryInvoiceableOrderList(AgentInvoiceBillAditReq req);

    int updateAgentInvoiceBill(AgentInvoiceBillEntity updateInvoiceBillParam);
}
