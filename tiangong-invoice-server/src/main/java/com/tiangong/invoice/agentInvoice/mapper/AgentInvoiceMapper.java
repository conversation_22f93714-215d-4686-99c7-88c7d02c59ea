package com.tiangong.invoice.agentInvoice.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tiangong.invoice.agentInvoice.domain.entity.AgentInvoiceEntity;
import com.tiangong.invoice.req.AgentInvoiceQueryPageReq;
import com.tiangong.invoice.resp.AgentInvoiceResp;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;


/**
 * 分销商发票表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-22 10:49:56
 */
@Mapper
public interface AgentInvoiceMapper extends BaseMapper<AgentInvoiceEntity> {

    int updateInvoice(AgentInvoiceEntity updateInvoiceParam);

    List<AgentInvoiceResp> queryInvoiceList(AgentInvoiceQueryPageReq req);

    AgentInvoiceResp queryInvoice(String merchantCode, Long invoiceId);

    List<AgentInvoiceResp> queryAgentInvoiceBillInvoiceItemList(AgentInvoiceQueryPageReq req);

    List<AgentInvoiceResp> queryAgentInvoiceableInvoiceList(AgentInvoiceQueryPageReq req);

    void updateInvoiceInfo(AgentInvoiceEntity updateInvoiceParam);
}
