package com.tiangong.invoice.agentInvoice.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.tiangong.cloud.common.domain.LoginUser;
import com.tiangong.cloud.commonbean.config.security.TokenManager;
import com.tiangong.common.Response;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.enums.ErrorCodeEnum;
import com.tiangong.enums.ParamErrorEnum;
import com.tiangong.exception.SysException;
import com.tiangong.invoice.agentInvoice.domain.entity.*;
import com.tiangong.invoice.agentInvoice.mapper.*;
import com.tiangong.invoice.agentInvoice.service.AgentInvoiceBillService;
import com.tiangong.invoice.agentInvoice.service.AgentInvoiceService;
import com.tiangong.invoice.dto.AgentInvoiceBaseRespDTO;
import com.tiangong.invoice.dto.AgentOrderInfoDTO;
import com.tiangong.invoice.enums.InvoiceBillStatusEnum;
import com.tiangong.invoice.enums.InvoiceConstant;
import com.tiangong.invoice.enums.InvoiceStatusEnum;
import com.tiangong.invoice.req.*;
import com.tiangong.invoice.resp.*;
import com.tiangong.order.remote.OrderRemote;
import com.tiangong.order.remote.request.QueryAutoInvoiceListDTO;
import com.tiangong.order.remote.request.SaveOrderLogDTO;
import com.tiangong.order.remote.response.OrderInvoiceWithCompanyDO;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.util.CollUtilX;
import com.tiangong.util.DateUtilX;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: zhiling
 * @date: 2024/3/15 18:07
 * @description:
 */
@Service
@Slf4j
public class AgentInvoiceBillServiceImpl implements AgentInvoiceBillService {

    @Autowired
    private AgentInvoiceBillMapper agentInvoiceBillMapper;

    @Autowired
    private AgentInvoiceBillItemServiceImpl agentInvoiceBillItemService;

    @Autowired
    private AgentInvoiceService agentInvoiceService;

    @Autowired
    private AgentInvoiceBillItemMapper agentInvoiceBillItemMapper;

    @Autowired
    private AgentInvoiceBillOrderStateMapper agentInvoiceBillOrderStateMapper;

    @Autowired
    private AgentInvoiceBillRelationMapper agentInvoiceBillRelationMapper;

    @Autowired
    private AgentInvoiceMapper agentInvoiceMapper;

    @Autowired
    private AgentInvoiceBillServiceImpl agentInvoiceBillService;
    @Autowired
    private OrderRemote orderRemote;
    @Autowired
    protected HttpServletRequest request;

    @Autowired
    private RocketMQTemplate rocketMQTemplate;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long agentInvoiceBillAdd(AgentInvoiceBillAditReq req) {
        // 校验参数
        validateInvoiceBillAddParam(req);
        //查询可添加的供货单信息
        List<AgentOrderInfoDTO> supplyOrderList = agentInvoiceBillMapper.queryInvoiceableOrderList(req);
        return agentInvoiceBillService.agentInvoiceBillAddM(req, supplyOrderList);
    }

    private String getOperator() {
        LoginUser loginUser = TokenManager.getUser(request);
        return null == loginUser ? "system" : loginUser.getUserName();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long agentInvoiceBillAdd(AgentInvoiceBillAditReq req, List<AgentOrderInfoDTO> supplyOrderList) {
        // 校验参数
        validateInvoiceBillAddParam(req);
        //查询可添加的供货单信息
        return agentInvoiceBillService.agentInvoiceBillAddM(req, supplyOrderList);
    }


    public Long agentInvoiceBillAddM(AgentInvoiceBillAditReq req, List<AgentOrderInfoDTO> supplyOrderList) {
        //判断可开票的供货单数量是否为0
        if (CollUtilX.isEmpty(supplyOrderList)) {
            log.error("销项票单创建失败：可开票供货单单数为0！");
            throw new SysException(ErrorCodeEnum.OPERATE_FAIL);
        }
        BigDecimal invoicePayableAmt = BigDecimal.ZERO;
        List<AgentInvoiceBillItemEntity> invoiceBillItems = new ArrayList<>();
        for (AgentOrderInfoDTO supplyOrderInfoDTO : supplyOrderList) {
            AgentInvoiceBillItemEntity agentInvoiceBillItem = new AgentInvoiceBillItemEntity();
            agentInvoiceBillItem.setOrderCode(supplyOrderInfoDTO.getOrderCode());
            agentInvoiceBillItem.setPayMethod(supplyOrderInfoDTO.getPayMethod());
            agentInvoiceBillItem.setCurrency(supplyOrderInfoDTO.getCurrency());
            agentInvoiceBillItem.setCreatedBy(req.getCreatedBy());
            agentInvoiceBillItem.setCreatedDt(DateUtilX.getCurrentDate());
            // 应开票金额
            agentInvoiceBillItem.setInvoicePayableAmt(supplyOrderInfoDTO.getUninvoicedAmt());
            // 供货单本次开票金额
            agentInvoiceBillItem.setInvoiceAmt(BigDecimal.ZERO);
            invoicePayableAmt = invoicePayableAmt.add(supplyOrderInfoDTO.getUninvoicedAmt());
            invoiceBillItems.add(agentInvoiceBillItem);
        }

        //获取票单编码
        AgentInvoiceBillEntity insertInvoiceBillParam = new AgentInvoiceBillEntity();
        //默认手动
        insertInvoiceBillParam.setSourceType(req.getSourceType() == null ? 0 : req.getSourceType());
        String invoiceBillCode = generateInvoiceBillCode();
        insertInvoiceBillParam.setInvoiceBillCode(invoiceBillCode);
        insertInvoiceBillParam.setAgentCode(req.getAgentCode());
        insertInvoiceBillParam.setAgentName(req.getAgentName());
        insertInvoiceBillParam.setInvoiceBillName(req.getInvoiceBillName());
        insertInvoiceBillParam.setInvoiceBillStatus(InvoiceBillStatusEnum.UNBOUND.getKey());
        insertInvoiceBillParam.setRemark(req.getRemark());
        Integer dateType = req.getDateQueryType() == null ? 0 : req.getDateQueryType();
        insertInvoiceBillParam.setDateType(dateType);
        if (StringUtils.isNotBlank(req.getStartDate())) {
            insertInvoiceBillParam.setStartDate(DateUtilX.stringToDate(req.getStartDate()));
        }
        if (StringUtils.isNotBlank(req.getEndDate())) {
            insertInvoiceBillParam.setEndDate(DateUtilX.stringToDate(req.getEndDate()));
        }
        insertInvoiceBillParam.setMerchantCode(req.getMerchantCode());
//        insertInvoiceBillParam.setIsAuto(req.getIsAuto());
        insertInvoiceBillParam.setRevision(0);
//        insertInvoiceBillParam.setHotelId(req.getHotelId());
//        insertInvoiceBillParam.setHotelName(req.getHotelName());
//        insertInvoiceBillParam.setIreveInvoiceBillId(req.getIreveInvoiceBillId());
        insertInvoiceBillParam.setCreatedBy(req.getCreatedBy());
        insertInvoiceBillParam.setCreatedDt(DateUtilX.getCurrentDate());
        insertInvoiceBillParam.setInvoicesNum(invoiceBillItems.size());
        insertInvoiceBillParam.setInvoicedAmt(BigDecimal.ZERO);
        insertInvoiceBillParam.setInvoicePayableAmt(invoicePayableAmt);
        // 新增票单
        int insertInvoiceBillResult = agentInvoiceBillMapper.insert(insertInvoiceBillParam);
        if (insertInvoiceBillResult == 0) {
            log.error("创建票单失败,req=" + JSON.toJSONString(insertInvoiceBillParam));
            throw new SysException(ErrorCodeEnum.SYSTEM_ADD_FAILED);
        }
        // 票单主键
        Long invoiceBillId = insertInvoiceBillParam.getInvoiceBillId();
        // 新增票单明细
        if (CollUtilX.isNotEmpty(invoiceBillItems)) {
            for (AgentInvoiceBillItemEntity item : invoiceBillItems) {
                item.setInvoiceBillId(invoiceBillId);
            }
            agentInvoiceBillItemService.addAgentInvoiceBillItem(invoiceBillItems);
        }
        return invoiceBillId;
    }

    @Override
    public void agentInvoiceBillEdit(AgentInvoiceBillAditReq req) {
        if (null == req) {
            log.error("销项票票单编辑失败：,请求参数为空");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (null == req.getInvoiceBillId()) {
            log.error("销项票票单编辑失败：，票单id为空");
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_INVOICEBILLID);
        }

        AgentInvoiceBillEntity AgentInvoiceBillEntity = agentInvoiceBillMapper.selectById(req.getInvoiceBillId());
        if (null == AgentInvoiceBillEntity) {
            log.error("销项票票单编辑失败：该票单id无票单记录：" + req.getInvoiceBillId());
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
        AgentInvoiceBillEntity updateInvoiceBillParam = new AgentInvoiceBillEntity();
        updateInvoiceBillParam.setInvoiceBillId(req.getInvoiceBillId());
        updateInvoiceBillParam.setRemark(req.getRemark());
        updateInvoiceBillParam.setRevision(AgentInvoiceBillEntity.getRevision() + 1);
        updateInvoiceBillParam.setUpdatedBy(req.getUpdatedBy());
        updateInvoiceBillParam.setUpdatedDt(DateUtilX.getCurrentDate());
        agentInvoiceBillMapper.updateById(updateInvoiceBillParam);
    }

    @Override
    public PaginationSupportDTO<AgentInvoiceBillResp> agentInvoiceBillPage(AgentInvoiceBillQueryReq req) {
        // 校验参数
        validateInvoiceBillListParam(req);

        // 根据发票号码进行查询
        if (StringUtils.isNotBlank(req.getInvoiceNum())) {
            AgentInvoiceAditReq invoiceQuery = new AgentInvoiceAditReq();
            invoiceQuery.setInvoiceNum(req.getInvoiceNum());
            List<AgentInvoiceEntity> list = agentInvoiceService.invoiceQuery(invoiceQuery);
            if (list.isEmpty()) {
                return null;
            }
            List<Long> invoiceNums = list.stream().map(AgentInvoiceEntity::getInvoiceId).collect(Collectors.toList());
            req.setInvoiceNums(invoiceNums);
        }
        String invoiceStartDt = req.getCreatedStartDt() + " 00:00:00";
        String invoiceEndDt = req.getCreatedEndDt() + " 23:59:59";
        if (StringUtils.isNotBlank(req.getConfirmStartDt()) && StringUtils.isNotBlank(req.getConfirmEndDt())) {
            String confirmStartDt = req.getConfirmStartDt() + " 00:00:00";
            String confirmEndDt = req.getConfirmEndDt() + " 23:59:59";
            req.setConfirmStartDt(confirmStartDt);
            req.setConfirmEndDt(confirmEndDt);
        }
        req.setCreatedStartDt(invoiceStartDt);
        req.setCreatedEndDt(invoiceEndDt);
        PageHelper.startPage(req.getCurrentPage(), req.getPageSize());
        List<AgentInvoiceBillResp> invoiceBillList = agentInvoiceBillMapper.queryInvoiceBillList(req);
        for (AgentInvoiceBillResp resp : invoiceBillList) {
            //未绑定金额
            BigDecimal uninvoicedAmt = resp.getInvoicePayableAmt().subtract(resp.getInvoicedAmt());
            resp.setUninvoicedAmt(uninvoicedAmt);
            resp.setInvoicePayableAmtCur("CNY " + resp.getInvoicePayableAmt());
            resp.setInvoicedAmtCur("CNY " + resp.getInvoicedAmt());
            resp.setUninvoicedAmtCur("CNY " + resp.getUninvoicedAmt());
        }
        PageInfo<AgentInvoiceBillResp> page = new PageInfo<>(invoiceBillList);
        // 查询票单列表
        PaginationSupportDTO<AgentInvoiceBillResp> paginationSupportDTO = new PaginationSupportDTO<>();
        paginationSupportDTO.setItemList(invoiceBillList);
        paginationSupportDTO.setCurrentPage(page.getPageNum());
        paginationSupportDTO.setPageSize(page.getPageSize());
        paginationSupportDTO.setTotalCount(page.getTotal());
        paginationSupportDTO.setTotalPage(page.getPages());

        return paginationSupportDTO;
    }

    @Override
    public PaginationSupportDTO<AgentInvoiceBillResp> agentInvoiceBillPage2(AgentInvoiceBillQueryReq req) {
        // 校验参数
        validateInvoiceBillListParam(req);

        // 查询票单
        IPage<AgentInvoiceBillQueryReq> page = new Page<>(req.getCurrentPage(), req.getPageSize());
        IPage<AgentInvoiceBillResp> iPage = agentInvoiceBillMapper.queryInvoiceBillPage(page, req);

        List<AgentInvoiceBillResp> collect = iPage.getRecords().stream().peek(item -> {
            //未绑定金额
            BigDecimal uninvoicedAmt = item.getInvoicePayableAmt().subtract(item.getInvoicedAmt());
            item.setUninvoicedAmt(uninvoicedAmt);
            item.setInvoicePayableAmtCur("CNY " + item.getInvoicePayableAmt());
            item.setInvoicedAmtCur("CNY " + item.getInvoicedAmt());
            item.setUninvoicedAmtCur("CNY " + item.getUninvoicedAmt());
        }).collect(Collectors.toList());

        PaginationSupportDTO<AgentInvoiceBillResp> paginationSupportDTO = new PaginationSupportDTO<>();
        paginationSupportDTO.setItemList(collect);
        paginationSupportDTO.setCurrentPage((int) iPage.getCurrent());
        paginationSupportDTO.setPageSize((int) iPage.getSize());
        paginationSupportDTO.setTotalCount(iPage.getTotal());
        paginationSupportDTO.setTotalPage((int) iPage.getPages());

        return paginationSupportDTO;
    }

    @Override
    public AgentInvoiceBillDetailResp agentInvoiceBillDetail(AgentInvoiceBillQueryReq req) {
        // 校验参数
        if (null == req) {
            log.error("销项票票单列表查询详情失败：请求参数为空！");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (StrUtilX.isEmpty(req.getInvoiceBillId())) {
            log.error("销项票票单列表查询详情失败：请求参数为空,票单id");
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_INVOICEBILLID);
        } else if (StrUtilX.isEmpty(req.getMerchantCode())) {
            log.error("销项票票单列表查询详情失败：请求参数为空,商家编码");
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_MERCHANTCODE);
        }

        String invoiceBillId = req.getInvoiceBillId();
        // 查询票单数据
        AgentInvoiceBillQueryReq queryInvoiceBillParam = new AgentInvoiceBillQueryReq();
        queryInvoiceBillParam.setInvoiceBillId(invoiceBillId);
        queryInvoiceBillParam.setMerchantCode(req.getMerchantCode());
        List<AgentInvoiceBillResp> agentInvoiceBillList = agentInvoiceBillMapper.queryInvoiceBillList(queryInvoiceBillParam);
        if (CollUtilX.isEmpty(agentInvoiceBillList)) {
            log.error("根据票单id查询票单详情为空,票单id：" + invoiceBillId);
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
        // 查询票单数据
        AgentInvoiceBillResp agentInvoiceBillResp = agentInvoiceBillList.get(0);
        AgentInvoiceBillDetailResp agentInvoiceBillDetailResp = new AgentInvoiceBillDetailResp();
        agentInvoiceBillDetailResp.setInvoiceBillId(agentInvoiceBillResp.getInvoiceBillId());
        agentInvoiceBillDetailResp.setInvoiceBillCode(agentInvoiceBillResp.getInvoiceBillCode());
        agentInvoiceBillDetailResp.setInvoiceBillName(agentInvoiceBillResp.getInvoiceBillName());
        agentInvoiceBillDetailResp.setAgentCode(agentInvoiceBillResp.getAgentCode());
        agentInvoiceBillDetailResp.setAgentName(agentInvoiceBillResp.getAgentName());
        agentInvoiceBillDetailResp.setInvoicesNum(agentInvoiceBillResp.getInvoicesNum());
        agentInvoiceBillDetailResp.setInvoiceBillStatus(agentInvoiceBillResp.getInvoiceBillStatus());
        //应开票金额
        agentInvoiceBillDetailResp.setInvoicePayableAmt(agentInvoiceBillResp.getInvoicePayableAmt());
        //已绑定金额
        agentInvoiceBillDetailResp.setInvoicedAmt(agentInvoiceBillResp.getInvoicedAmt());
        //未绑定金额
        BigDecimal uninvoicedAmt = agentInvoiceBillResp.getInvoicePayableAmt().subtract(agentInvoiceBillResp.getInvoicedAmt());
        agentInvoiceBillDetailResp.setUninvoicedAmt(uninvoicedAmt);
        agentInvoiceBillDetailResp.setRemark(agentInvoiceBillResp.getRemark());
        agentInvoiceBillDetailResp.setConfirmer(agentInvoiceBillResp.getConfirmer());
        agentInvoiceBillDetailResp.setConfirmDt(agentInvoiceBillResp.getConfirmDt());
        agentInvoiceBillDetailResp.setCreatedDt(agentInvoiceBillResp.getCreatedDt());
        agentInvoiceBillDetailResp.setCreatedBy(agentInvoiceBillResp.getCreatedBy());
        agentInvoiceBillDetailResp.setHotelName(agentInvoiceBillResp.getHotelName());

        //查询票单明细
        AgentInvoiceBillItemQueryReq queryInvoiceBillItemParam = new AgentInvoiceBillItemQueryReq();
        queryInvoiceBillItemParam.setInvoiceBillId(Long.valueOf(invoiceBillId));
        List<AgentInvoiceBillItemResp> agentInvoiceBillItemList = agentInvoiceBillItemService.queryAgentInvoiceBillItemList(queryInvoiceBillItemParam);
        if (CollUtilX.isEmpty(agentInvoiceBillItemList)) {
            agentInvoiceBillDetailResp.setInvoiceItemIsEmpty(true);
        }
        int updateNum = 0;
        for (AgentInvoiceBillItemResp itemResp : agentInvoiceBillItemList) {
            if (itemResp.getInvoicePayableAmt() != null) {
                itemResp.setInvoicePayableAmtCur("CNY " + itemResp.getInvoicePayableAmt());
            }
            BigDecimal amt = itemResp.getSupplyOrderAmt().subtract(itemResp.getInvoicePayableAmt()).subtract(itemResp.getAlreadyInvoiceAmt());
            if (amt.compareTo(BigDecimal.ZERO) != 0) {
                updateNum++;
            }
        }
        //票单明细-供货单应付金额变更数量
        agentInvoiceBillDetailResp.setUpdateNum(updateNum);
        //票单明细分页
        PaginationSupportDTO<AgentInvoiceBillItemResp> billItemDTO = new PaginationSupportDTO<>();
        PageInfo<AgentInvoiceBillItemResp> page = new PageInfo<>(agentInvoiceBillItemList);
        billItemDTO.setItemList(agentInvoiceBillItemList);
        billItemDTO.setCurrentPage(page.getPageNum());
        billItemDTO.setPageSize(page.getPageSize());
        billItemDTO.setTotalCount(page.getTotal());
        billItemDTO.setTotalPage(page.getPages());
        agentInvoiceBillDetailResp.setInvoiceBillDetails(billItemDTO);

        AgentInvoiceQueryPageReq queryInvoiceBillInvoiceItemParam = new AgentInvoiceQueryPageReq();
        queryInvoiceBillInvoiceItemParam.setInvoiceBillId(invoiceBillId);
        List<AgentInvoiceResp> agentInvoiceRespList = agentInvoiceService.queryAgentBillInvoiceItemList(queryInvoiceBillInvoiceItemParam);
        if (CollUtilX.isEmpty(agentInvoiceRespList)) {
            agentInvoiceBillDetailResp.setInvoiceDetailIsEmpty(true);
        }
        for (AgentInvoiceResp item : agentInvoiceRespList) {
            item.setInvoiceAmtCur("CNY " + item.getInvoiceAmt());
            item.setCurrentAmtCur("CNY " + item.getCurrentAmt());
        }

        // 发票明细分页查询
        PaginationSupportDTO<AgentInvoiceResp> billInvoiceItemDTO = new PaginationSupportDTO<>();
        PageInfo<AgentInvoiceResp> billInvoiceItemPage = new PageInfo<>(agentInvoiceRespList);
        billInvoiceItemDTO.setItemList(agentInvoiceRespList);
        billInvoiceItemDTO.setCurrentPage(billInvoiceItemPage.getPageNum());
        billInvoiceItemDTO.setPageSize(billInvoiceItemPage.getPageSize());
        billInvoiceItemDTO.setTotalCount(billInvoiceItemPage.getTotal());
        billInvoiceItemDTO.setTotalPage(billInvoiceItemPage.getPages());

        agentInvoiceBillDetailResp.setInvoiceDetails(billInvoiceItemDTO);
        return agentInvoiceBillDetailResp;
    }

    @Override
    public PaginationSupportDTO<AgentInvoiceBillItemResp> queryInvoiceBillItemPage(AgentInvoiceBillQueryReq req) {
        AgentInvoiceBillItemQueryReq queryInvoiceBillItemParam = new AgentInvoiceBillItemQueryReq();
        String invoiceBillId = req.getInvoiceBillId();
        queryInvoiceBillItemParam.setInvoiceBillId(Long.valueOf(invoiceBillId));
        queryInvoiceBillItemParam.setCurrentPage(req.getCurrentPage());
        queryInvoiceBillItemParam.setPageSize(req.getPageSize());
        List<AgentInvoiceBillItemResp> agentInvoiceBillItemList = agentInvoiceBillItemService.queryAgentInvoiceBillItemList(queryInvoiceBillItemParam);
        for (AgentInvoiceBillItemResp itemResp : agentInvoiceBillItemList) {
            if (itemResp.getInvoicePayableAmt() != null) {
                itemResp.setInvoicePayableAmtCur("CNY " + itemResp.getInvoicePayableAmt());

                if (itemResp.getAlreadyInvoiceAmt() != null) {
                    itemResp.setInvoicePayableAmtSubInAmtCur("CNY " + itemResp.getSupplyOrderAmt());
                    itemResp.setAlreadyInvoiceAmtSubInAmtCur("CNY " + itemResp.getAlreadyInvoiceAmt());
                }
                if (itemResp.getUninvoicedAmt() != null) {
                    itemResp.setUninvoicedAmtSubInAmtCur("CNY " + itemResp.getUninvoicedAmt());
                }
            }
        }
        // 票单明细分页
        PaginationSupportDTO<AgentInvoiceBillItemResp> billItemDTO = new PaginationSupportDTO<>();
        PageInfo<AgentInvoiceBillItemResp> page = new PageInfo<>(agentInvoiceBillItemList);
        billItemDTO.setItemList(agentInvoiceBillItemList);
        billItemDTO.setCurrentPage(page.getPageNum());
        billItemDTO.setPageSize(page.getPageSize());
        billItemDTO.setTotalCount(page.getTotal());
        billItemDTO.setTotalPage(page.getPages());

        return billItemDTO;
    }

    @Override
    public PaginationSupportDTO<AgentInvoiceResp> queryInvoiceBillInvoicePage(AgentInvoiceBillQueryReq req) {
        String invoiceBillId = req.getInvoiceBillId();
        AgentInvoiceQueryPageReq queryBillInvoiceItemParam = new AgentInvoiceQueryPageReq();
        queryBillInvoiceItemParam.setInvoiceBillId(invoiceBillId);
        queryBillInvoiceItemParam.setCurrentPage(req.getCurrentPage());
        queryBillInvoiceItemParam.setPageSize(req.getPageSize());
        List<AgentInvoiceResp> agentInvoiceRespList = agentInvoiceService.queryAgentBillInvoiceItemList(queryBillInvoiceItemParam);
        for (AgentInvoiceResp item : agentInvoiceRespList) {
            item.setInvoiceAmtCur("CNY " + item.getInvoiceAmt());
            item.setCurrentAmtCur("CNY " + item.getCurrentAmt());
        }

        // 发票明细分页查询
        PaginationSupportDTO<AgentInvoiceResp> billInvoiceItemDTO = new PaginationSupportDTO<>();
        PageInfo<AgentInvoiceResp> billInvoiceItemPage = new PageInfo<>(agentInvoiceRespList);
        billInvoiceItemDTO.setItemList(agentInvoiceRespList);
        billInvoiceItemDTO.setCurrentPage(billInvoiceItemPage.getPageNum());
        billInvoiceItemDTO.setPageSize(billInvoiceItemPage.getPageSize());
        billInvoiceItemDTO.setTotalCount(billInvoiceItemPage.getTotal());
        billInvoiceItemDTO.setTotalPage(billInvoiceItemPage.getPages());
        return billInvoiceItemDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void agentInvoiceBillDel(AgentInvoiceBillAditReq req) {
        if (null == req.getInvoiceBillId()) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_INVOICEBILLID);
        }
        // 查询票单信息
        AgentInvoiceBillEntity agentInvoiceBill = agentInvoiceBillMapper.selectById(req.getInvoiceBillId());
        if (null != agentInvoiceBill.getInvoiceBillStatus() && !(agentInvoiceBill.getInvoiceBillStatus().equals(0) || agentInvoiceBill.getInvoiceBillStatus().equals(11))) {
            throw new SysException(ParamErrorEnum.CHECK_INVOICEBILL_STATE_ERROR);
        }
        // 1.删除票单
        int invoiceBillDel = agentInvoiceBillMapper.deleteById(req.getInvoiceBillId());
        if (invoiceBillDel == 0) {
            log.error("票单id:[" + req.getInvoiceBillId() + "]票单删除失败！");
            throw new SysException(ErrorCodeEnum.OPERATE_FAIL.errorDesc, ErrorCodeEnum.OPERATE_FAIL.errorDesc);
        }
        // 2查询票单明细
        LambdaQueryWrapper<AgentInvoiceBillItemEntity> queryInvoiceBillItemWrapper = new LambdaQueryWrapper<>();
        queryInvoiceBillItemWrapper.eq(AgentInvoiceBillItemEntity::getInvoiceBillId, req.getInvoiceBillId());
        List<AgentInvoiceBillItemEntity> billItemList = agentInvoiceBillItemMapper.selectList(queryInvoiceBillItemWrapper);
        if (CollUtilX.isNotEmpty(billItemList)) {
            // 2.1删除票单明细
            LambdaQueryWrapper<AgentInvoiceBillItemEntity> delInvoiceBillItemWrapper = new LambdaQueryWrapper<>();
            delInvoiceBillItemWrapper.eq(AgentInvoiceBillItemEntity::getInvoiceBillId, req.getInvoiceBillId());
            agentInvoiceBillItemMapper.delete(delInvoiceBillItemWrapper);
        }
        // 3.删除分销商发票绑定表数据 仅删除未平账的
        LambdaQueryWrapper<AgentInvoiceBillOrderStateEntity> delInvoiceBillStateWrapper = new LambdaQueryWrapper<>();
        delInvoiceBillStateWrapper.eq(AgentInvoiceBillOrderStateEntity::getCurrentInvoiceBillId, req.getInvoiceBillId())
                .eq(AgentInvoiceBillOrderStateEntity::getAlreadyInvoiceAmt, BigDecimal.ZERO);
        agentInvoiceBillOrderStateMapper.delete(delInvoiceBillStateWrapper);
        // 4.退回发票领用金额
        LambdaQueryWrapper<AgentInvoiceBillRelationEntity> queryInvoiceBillRelationWrapper = new LambdaQueryWrapper<>();
        queryInvoiceBillRelationWrapper.eq(AgentInvoiceBillRelationEntity::getInvoiceBillId, req.getInvoiceBillId());
        List<AgentInvoiceBillRelationEntity> relationList = agentInvoiceBillRelationMapper.selectList(queryInvoiceBillRelationWrapper);
        if (CollUtilX.isNotEmpty(relationList)) {
            // 退回发票领用金额，更新发票状态
            for (AgentInvoiceBillRelationEntity relation : relationList) {
                LambdaQueryWrapper<AgentInvoiceEntity> queryAgentWrapper = new LambdaQueryWrapper<>();
                queryAgentWrapper.eq(AgentInvoiceEntity::getInvoiceId, relation.getInvoiceId());
                AgentInvoiceEntity agentEntity = agentInvoiceMapper.selectById(relation.getInvoiceId());
                agentEntity.setBalance(agentEntity.getBalance().add(relation.getBillUsedAmt()));
                agentEntity.setBillUsedAmt(agentEntity.getBillUsedAmt().subtract(relation.getBillUsedAmt()));
                agentEntity.setUpdatedDt(DateUtilX.getCurrentDate());
                agentEntity.setUpdatedBy(req.getUpdatedBy());
                if (agentEntity.getBillUsedAmt().compareTo(BigDecimal.ZERO) == 0) {
                    agentEntity.setInvoiceStatus(InvoiceStatusEnum.UNUSED.key);
                } else if (agentEntity.getBalance().compareTo(BigDecimal.ZERO) > 0 && agentEntity.getBalance().compareTo(agentEntity.getInvoiceAmt()) < 0) {
                    agentEntity.setInvoiceStatus(InvoiceStatusEnum.PARTIAL_USE.key);
                }
                agentInvoiceMapper.updateById(agentEntity);
            }
            // 5.删除票单发票明细
            LambdaQueryWrapper<AgentInvoiceBillRelationEntity> delInvoiceBillRelationWrapper = new LambdaQueryWrapper<>();
            delInvoiceBillRelationWrapper.eq(AgentInvoiceBillRelationEntity::getInvoiceBillId, req.getInvoiceBillId());
            agentInvoiceBillRelationMapper.delete(delInvoiceBillRelationWrapper);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void suppInvoiceBillConfirmBalan(AgentInvoiceBillAditReq req) {
        // 查询票单详情数据
        AgentInvoiceBillQueryReq queryReq = new AgentInvoiceBillQueryReq();
        queryReq.setInvoiceBillId(String.valueOf(req.getInvoiceBillId()));
        queryReq.setMerchantCode(req.getMerchantCode());
        // 票单详细数据
        AgentInvoiceBillDetailResp invoiceBillDetailModel = agentInvoiceBillDetail(queryReq);
        if (null == invoiceBillDetailModel) {
            log.error("查询票单详情数据为空,票单id:" + req.getInvoiceBillId());
            throw new SysException(ErrorCodeEnum.OPERATE_FAIL);
        }
        if (invoiceBillDetailModel.getInvoiceItemIsEmpty()) {
            log.error("票单明细为空,不可进行确认平账或确认坏账,票单id:" + req.getInvoiceBillId());
            throw new SysException(ErrorCodeEnum.INVOICEDETAIL_EMPTY_NOTBAD_CONFIRMED.errorCode, ErrorCodeEnum.INVOICEDETAIL_EMPTY_NOTBAD_CONFIRMED.errorDesc);
        }
        if (invoiceBillDetailModel.getInvoiceDetailIsEmpty()) {
            log.error("发票明细为空,不可进行确认平账或确认坏账,票单id:" + req.getInvoiceBillId());
            throw new SysException(ErrorCodeEnum.INVOICE_EMPTY_NOTBAD_CONFIRMED.errorCode, ErrorCodeEnum.INVOICE_EMPTY_NOTBAD_CONFIRMED.errorDesc);
        }
        if (invoiceBillDetailModel.getInvoiceBillStatus() == InvoiceBillStatusEnum.RECONCILED.key) {
            log.error("当前票单状态是已平账,不可重复操作,票单id:" + req.getInvoiceBillId());
            throw new SysException(ErrorCodeEnum.DUBBLE_CLICK_ERROR.errorCode, ErrorCodeEnum.DUBBLE_CLICK_ERROR.errorDesc);
        }
        if (invoiceBillDetailModel.getInvoicePayableAmt().compareTo(invoiceBillDetailModel.getInvoicedAmt()) != 0) {
            log.error("应开票金额不等于已绑定发票金额,不可进行确认平账,票单id:" + req.getInvoiceBillId());
            throw new SysException(ErrorCodeEnum.CANNOT_OPERATE.errorCode, ErrorCodeEnum.CANNOT_OPERATE.errorDesc);
        }
        // 状态处理,确认平账
        agentInvoiceBillService.confirmHandler(invoiceBillDetailModel, InvoiceBillStatusEnum.RECONCILED.key, req.getUpdatedBy());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void agentInvoiceBillConfirmBad(AgentInvoiceBillAditReq req) {
        //查询票单详情数据
        AgentInvoiceBillQueryReq queryReq = new AgentInvoiceBillQueryReq();
        queryReq.setInvoiceBillId(String.valueOf(req.getInvoiceBillId()));
        queryReq.setMerchantCode(req.getMerchantCode());
        //票单详细数据
        AgentInvoiceBillDetailResp invoiceBillDetailModel = agentInvoiceBillDetail(queryReq);
        if (null == invoiceBillDetailModel) {
            log.error("查询票单详情数据为空,票单id:" + req.getInvoiceBillId());
            throw new SysException(ErrorCodeEnum.OPERATE_FAIL);
        }
        if (invoiceBillDetailModel.getInvoiceItemIsEmpty()) {
            log.error("票单明细为空,不可进行确认平账或确认坏账,票单id:" + req.getInvoiceBillId());
            throw new SysException(ErrorCodeEnum.INVOICEDETAIL_EMPTY_NOTBAD_CONFIRMED.errorCode, ErrorCodeEnum.INVOICEDETAIL_EMPTY_NOTBAD_CONFIRMED.errorDesc);
        }
        if (invoiceBillDetailModel.getInvoiceDetailIsEmpty()) {
            log.error("发票明细为空,不可进行确认平账或确认坏账,票单id:" + req.getInvoiceBillId());
            throw new SysException(ErrorCodeEnum.INVOICE_EMPTY_NOTBAD_CONFIRMED.errorCode, ErrorCodeEnum.INVOICE_EMPTY_NOTBAD_CONFIRMED.errorDesc);
        }
        if (invoiceBillDetailModel.getInvoiceBillStatus() == InvoiceBillStatusEnum.BAD_DEBT.key) {
            log.error("当前票单状态是已坏账,不可重复操作,票单id:" + req.getInvoiceBillId());
            throw new SysException(ErrorCodeEnum.DUPLICATE_CLICK_ERROR.errorCode, ErrorCodeEnum.DUPLICATE_CLICK_ERROR.errorDesc);
        }
        if (invoiceBillDetailModel.getInvoicePayableAmt().compareTo(invoiceBillDetailModel.getInvoicedAmt()) == 0) {
            log.error("应开票金额等于已绑定发票金额,不可进行确认坏账,票单id:" + req.getInvoiceBillId());
            throw new SysException(ErrorCodeEnum.CANNOT_OPERATE.errorCode, ErrorCodeEnum.CANNOT_OPERATE.errorDesc);
        }

        // 状态处理,确认坏账
        agentInvoiceBillService.confirmHandler(invoiceBillDetailModel, InvoiceBillStatusEnum.BAD_DEBT.key, req.getUpdatedBy());
    }

    @Override
    public PaginationSupportDTO<AgentInvoiceBillItemResp> queryagentInvoiceableInvoiceBillItemList(AgentInvoiceBillItemQueryReq req) {
        // 校验参数
        validateInvoiceableInvoiceBillItemQueryParam(req);

        return agentInvoiceBillItemService.queryAgentInvoiceableInvoiceBillItemList(req);
    }

    @Override
    @Transactional
    public void agentInvoiceBillItemAdd(AgentInvoiceBillItemAditReq req) {
        // 校验参数
        validateInvoiceBillItemAddParam(req);

        // 根据票单id查询票单信息
        AgentInvoiceBillEntity agentInvoiceBillEntity = agentInvoiceBillMapper.selectById(req.getInvoiceBillId());
        if (null == agentInvoiceBillEntity) {
            log.error("销项票单添加票单明细失败：该票单id查询的票单记录为空");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
        //票单是最终状态
        if (agentInvoiceBillEntity.getInvoiceBillStatus() == InvoiceBillStatusEnum.BAD_DEBT.getKey() || agentInvoiceBillEntity.getInvoiceBillStatus() == InvoiceBillStatusEnum.RECONCILED.getKey()) {
            log.error("销项票单添加票单明细失败：该票单的票单状态是已平账或已坏账，不可添加票单明细");
            throw new SysException(ErrorCodeEnum.INVOICE_STATE_BOB_ERROR.errorCode, ErrorCodeEnum.INVOICE_STATE_BOB_ERROR.errorDesc);
        }

        //新增票单明细和供应商票单绑定表
        List<AgentInvoiceBillItemEntity> invoiceBillItems = new ArrayList<>();
        Date currentDate = DateUtilX.getCurrentDate();
        AgentInvoiceBillItemEntity invoiceBillItem = new AgentInvoiceBillItemEntity();
        invoiceBillItem.setInvoiceBillId(agentInvoiceBillEntity.getInvoiceBillId());
        invoiceBillItem.setOrderCode(req.getOrderCode());
        invoiceBillItem.setPayMethod(req.getPayMethod());
        invoiceBillItem.setInvoiceAmt(BigDecimal.ZERO);
        invoiceBillItem.setCurrency(req.getCurrency());
        invoiceBillItem.setCreatedBy(req.getCreatedBy());
        invoiceBillItem.setCreatedDt(currentDate);
        // 应开票金额
        invoiceBillItem.setInvoicePayableAmt(req.getInvoicePayableAmt());
        invoiceBillItems.add(invoiceBillItem);
        agentInvoiceBillItemService.addAgentInvoiceBillItem(invoiceBillItems);

        // 更新票单应开票金额和票单单数
        LambdaQueryWrapper<AgentInvoiceBillItemEntity> queryItemWrapper = new LambdaQueryWrapper<>();
        queryItemWrapper.eq(AgentInvoiceBillItemEntity::getInvoiceBillId, req.getInvoiceBillId());
        List<AgentInvoiceBillItemEntity> invoiceBillItemList = agentInvoiceBillItemMapper.selectList(queryItemWrapper);
        BigDecimal totalAmt = BigDecimal.ZERO;
        for (AgentInvoiceBillItemEntity item : invoiceBillItemList) {
            BigDecimal invoicePayableAmt = item.getInvoicePayableAmt() == null ? BigDecimal.ZERO : item.getInvoicePayableAmt();
            totalAmt = totalAmt.add(invoicePayableAmt);
        }
        AgentInvoiceBillEntity agentInvoiceBill = new AgentInvoiceBillEntity();
        agentInvoiceBill.setInvoicePayableAmt(totalAmt);
        agentInvoiceBill.setInvoicesNum(agentInvoiceBillEntity.getInvoicesNum() + 1);
        agentInvoiceBill.setInvoiceBillId(Long.valueOf(req.getInvoiceBillId()));
        int updateCount = agentInvoiceBillMapper.updateById(agentInvoiceBill);
        if (updateCount == 0) {
            log.error("销项票单添加票单明细失败,更新票单应开票金额和票单单数失败");
            throw new SysException(ErrorCodeEnum.OPERATE_FAIL);
        }

        //更新票单状态
        agentInvoiceBillService.updateInvoiceBillHandler(Long.valueOf(req.getInvoiceBillId()), req.getCreatedBy(), "agentInvoiceBillItemAdd");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void agentInvoiceBillItemDel(AgentInvoiceBillItemAditReq req) {
        // 校验参数
        validateInvoiceBillItemDelParam(req);

        // 查询该票单
        AgentInvoiceBillEntity existAgentInvoiceBill = agentInvoiceBillMapper.selectById(req.getInvoiceBillId());
        if (null == existAgentInvoiceBill) {
            log.error("销项票单删除票单明细失败：该票单不存在，票单id：" + req.getInvoiceBillId());
            throw new SysException(ErrorCodeEnum.INVOICEBILL_NOT_EXIST.errorCode, ErrorCodeEnum.INVOICEBILL_NOT_EXIST.errorDesc);
        }
        if (existAgentInvoiceBill.getInvoiceBillStatus() == InvoiceBillStatusEnum.BAD_DEBT.getKey() || existAgentInvoiceBill.getInvoiceBillStatus() == InvoiceBillStatusEnum.RECONCILED.getKey()) {
            log.error("销项票单删除票单明细失败：该票单的状态为已平账或已坏账，票单id：" + req.getInvoiceBillId());
            throw new SysException(ErrorCodeEnum.INVOICE_STATE_BOB_ERROR.errorCode, ErrorCodeEnum.INVOICE_STATE_BOB_ERROR.errorDesc);
        }

        List<String> delAgentInvoiceBillItemIds = new ArrayList<>();
        delAgentInvoiceBillItemIds.add(req.getInvoiceBillItemId());
        List<AgentInvoiceBillItemEntity> existAgentBillItems = agentInvoiceBillItemMapper.selectBatchIds(delAgentInvoiceBillItemIds);
        if (CollUtilX.isEmpty(existAgentBillItems)) {
            log.error("销项票单删除票单明细失败：票单明细不存在，票单id：" + JSON.toJSONString(delAgentInvoiceBillItemIds));
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
        if (existAgentBillItems.size() != delAgentInvoiceBillItemIds.size()) {
            log.error("销项票单删除票单明细失败：票单明细已被修改，请重新刷新！");
            throw new SysException(ErrorCodeEnum.OPERATE_FAIL);
        }

        // 更新或者删除供应商发票绑定表
        Date currentDate = DateUtilX.getCurrentDate();
        List<String> supplyOrderCodes = new ArrayList<>();
        BigDecimal subAmt = BigDecimal.ZERO;
        for (AgentInvoiceBillItemEntity billItem : existAgentBillItems) {
            supplyOrderCodes.add(billItem.getOrderCode());
            subAmt = subAmt.add(billItem.getInvoicePayableAmt());
        }
        LambdaQueryWrapper<AgentInvoiceBillOrderStateEntity> queryBillOrderStateWrapper = new LambdaQueryWrapper<>();
        queryBillOrderStateWrapper.in(AgentInvoiceBillOrderStateEntity::getOrderCode, supplyOrderCodes);
        List<AgentInvoiceBillOrderStateEntity> statelist = agentInvoiceBillOrderStateMapper.selectList(queryBillOrderStateWrapper);
        for (AgentInvoiceBillOrderStateEntity stateEntity : statelist) {
            if (stateEntity.getAlreadyInvoiceAmt().compareTo(BigDecimal.ZERO) > 0) {
                AgentInvoiceBillItemQueryReq queryReq = new AgentInvoiceBillItemQueryReq();
                queryReq.setOrderCode(stateEntity.getOrderCode());
                List<AgentInvoiceBillResp> invoiceBillList = agentInvoiceBillItemMapper.queryAgentInvoiceBillList(queryReq);
                if (CollUtilX.isNotEmpty(invoiceBillList)) {
                    stateEntity.setCurrentInvoiceBillId(Long.valueOf(invoiceBillList.get(0).getInvoiceBillId()));
                    stateEntity.setInvoiceBillStatus(invoiceBillList.get(0).getInvoiceBillStatus());
                    stateEntity.setUpdatedBy(req.getUpdatedBy());
                    stateEntity.setUpdatedDt(currentDate);
                    agentInvoiceBillOrderStateMapper.updateById(stateEntity);
                }
            } else {
                agentInvoiceBillOrderStateMapper.deleteById(stateEntity.getId());
            }
        }

        // 删除票单明细
        agentInvoiceBillItemMapper.deleteBatchIds(delAgentInvoiceBillItemIds);
        // 更新票单数据
        existAgentInvoiceBill.setInvoicesNum(existAgentInvoiceBill.getInvoicesNum() - delAgentInvoiceBillItemIds.size());
        BigDecimal subInvoicePayableAmt = existAgentInvoiceBill.getInvoicePayableAmt().subtract(subAmt);
        existAgentInvoiceBill.setInvoicePayableAmt(subInvoicePayableAmt);
        existAgentInvoiceBill.setRevision(existAgentInvoiceBill.getRevision() + 1);
        existAgentInvoiceBill.setUpdatedBy(req.getUpdatedBy());
        existAgentInvoiceBill.setUpdatedDt(currentDate);
        agentInvoiceBillMapper.updateById(existAgentInvoiceBill);

        // 更新票单状态
        agentInvoiceBillService.updateInvoiceBillHandler(Long.valueOf(req.getInvoiceBillId()), req.getCreatedBy(), "agentInvoiceBillItemDel");
    }

    @Override
    public PaginationSupportDTO<AgentInvoiceBillItemResp> agentInvoiceBillItemDiffPage(AgentInvoiceBillItemQueryReq req) {
        req.setIsDiffQuery(true);
        // 查询明细单有更新
        List<AgentInvoiceBillItemResp> agentInvoiceBillItemList = agentInvoiceBillItemService.queryAgentInvoiceBillItemList(req);
        if (CollUtilX.isNotEmpty(agentInvoiceBillItemList)) {
            for (AgentInvoiceBillItemResp itemResp : agentInvoiceBillItemList) {
                itemResp.setInvoicePayableAmtCur("CNY " + itemResp.getInvoicePayableAmt());
                itemResp.setSupplyOrderAmtCur(itemResp.getSupplyOrderAmt() + " CNY");
                itemResp.setAlreadyInvoiceAmtCur(itemResp.getAlreadyInvoiceAmt() + " CNY");
                itemResp.setUninvoicedAmtCur(itemResp.getUninvoicedAmt() + " CNY");
            }
        }

        PaginationSupportDTO<AgentInvoiceBillItemResp> paginationSupportDTO = new PaginationSupportDTO<>();
        PageInfo<AgentInvoiceBillItemResp> page = new PageInfo<>(agentInvoiceBillItemList);
        paginationSupportDTO.setItemList(agentInvoiceBillItemList);
        paginationSupportDTO.setCurrentPage(page.getPageNum());
        paginationSupportDTO.setPageSize(page.getPageSize());
        paginationSupportDTO.setTotalCount(page.getTotal());
        paginationSupportDTO.setTotalPage(page.getPages());
        return paginationSupportDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void agentInvoiceBillItemDiffEdit(List<AgentInvoiceBillItemResp> reqs, String loginName) {
        Response<Boolean> response = new Response<>();
        if (CollUtilX.isEmpty(reqs)) {
            log.error("请求参数为空!");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
        //查询票单信息
        String invoiceBillId = reqs.get(0).getInvoiceBillId();
        AgentInvoiceBillEntity agentInvoiceBill = agentInvoiceBillMapper.selectById(invoiceBillId);
        if (null == agentInvoiceBill) {
            log.error(invoiceBillId + ":对应的票单不存在");
            throw new SysException(ErrorCodeEnum.OPERATE_FAIL);
        }
        //票单是最终状态
        if (agentInvoiceBill.getInvoiceBillStatus() == InvoiceBillStatusEnum.BAD_DEBT.getKey() || agentInvoiceBill.getInvoiceBillStatus() == InvoiceBillStatusEnum.RECONCILED.getKey()) {
            log.error("进项票单添加票单明细失败：该票单的票单状态是已平账或已坏账，不可更新票单明细");
            throw new SysException(ErrorCodeEnum.INVOICE_STATE_BOB_ERROR.errorCode, ErrorCodeEnum.INVOICE_STATE_BOB_ERROR.errorDesc);
        }

        //因为前端是分页了，所以会少更新,后台查询有更新数据进行更新
        AgentInvoiceBillItemQueryReq queryDiffReq = new AgentInvoiceBillItemQueryReq();
        queryDiffReq.setIsDiffQuery(true);
        queryDiffReq.setPageSize(999);
        queryDiffReq.setInvoiceBillId(Long.valueOf(invoiceBillId));
        List<AgentInvoiceBillItemResp> billItemUpdateList = agentInvoiceBillItemService.queryAgentInvoiceBillItemList(queryDiffReq);
        //1.更新应开票金额
        for (AgentInvoiceBillItemResp req : billItemUpdateList) {
            AgentInvoiceBillItemEntity invoiceBillItem = new AgentInvoiceBillItemEntity();
            invoiceBillItem.setId(Long.valueOf(req.getInvoiceBillItemId()));
            invoiceBillItem.setInvoicePayableAmt(req.getUninvoicedAmt());
            invoiceBillItem.setUpdatedBy(loginName);
            invoiceBillItem.setUpdatedDt(DateUtilX.getCurrentDate());
            agentInvoiceBillItemMapper.updateById(invoiceBillItem);
        }
        //3.更新票单金额
        //重新汇总票单明细本次本票单要开票金额
        LambdaQueryWrapper<AgentInvoiceBillItemEntity> queryBillItemWrapper = new LambdaQueryWrapper<>();
        queryBillItemWrapper.eq(AgentInvoiceBillItemEntity::getInvoiceBillId, invoiceBillId);
        List<AgentInvoiceBillItemEntity> itemEntities = agentInvoiceBillItemMapper.selectList(queryBillItemWrapper);
        BigDecimal subInvoicePayableAmt = BigDecimal.ZERO;
        for (AgentInvoiceBillItemEntity entity : itemEntities) {
            subInvoicePayableAmt = subInvoicePayableAmt.add(entity.getInvoicePayableAmt());
        }
        agentInvoiceBill.setInvoicePayableAmt(subInvoicePayableAmt);
        agentInvoiceBill.setRevision(agentInvoiceBill.getRevision() + 1);
        agentInvoiceBill.setUpdatedBy(loginName);
        agentInvoiceBill.setUpdatedDt(DateUtilX.getCurrentDate());
        agentInvoiceBillMapper.updateById(agentInvoiceBill);
        //更新票单状态
        agentInvoiceBillService.updateInvoiceBillHandler(Long.valueOf(invoiceBillId), loginName, "agentInvoiceBillItemDiffEdit");
    }

    @Override
    public PaginationSupportDTO<AgentInvoiceBillItemResp> orderInvoiceRecordPage(AgentInvoiceBillItemQueryReq req) {
        // 查询票单明细
        req.setInvoiceBillId(-1L);
        List<AgentInvoiceBillItemResp> agentInvoiceBillItemList = agentInvoiceBillItemService.queryOrderInvoiceRecordPage(req);
        for (AgentInvoiceBillItemResp itemResp : agentInvoiceBillItemList) {
            if (itemResp.getSupplyOrderAmt() != null) {
                itemResp.setSupplyOrderAmtCur(itemResp.getSupplyOrderAmt() + " CNY");
            }
            itemResp.setInvoicedAmtCur(itemResp.getInvoicedAmt() + " CNY");
            itemResp.setUninvoicedAmtCur(itemResp.getUninvoicedAmt() + " CNY");
            if ((itemResp.getSupplyOrderAmt().subtract(itemResp.getInvoicedAmt())).compareTo(BigDecimal.ZERO) == 0) {
                itemResp.setInvoiceStatus(0);
            } else if (itemResp.getInvoicedAmt().compareTo(BigDecimal.ZERO) > 0 && (itemResp.getSupplyOrderAmt().subtract(itemResp.getInvoicedAmt())).compareTo(BigDecimal.ZERO) != 0) {
                itemResp.setInvoiceStatus(1);
            } else if (itemResp.getInvoicedAmt().compareTo(BigDecimal.ZERO) == 0) {
                itemResp.setInvoiceStatus(2);
            }
        }
        PaginationSupportDTO<AgentInvoiceBillItemResp> paginationSupportDTO = new PaginationSupportDTO<>();
        PageInfo<AgentInvoiceBillItemResp> page = new PageInfo<>(agentInvoiceBillItemList);
        paginationSupportDTO.setItemList(agentInvoiceBillItemList);
        paginationSupportDTO.setCurrentPage(page.getPageNum());
        paginationSupportDTO.setPageSize(page.getPageSize());
        paginationSupportDTO.setTotalCount(page.getTotal());
        paginationSupportDTO.setTotalPage(page.getPages());

        return paginationSupportDTO;
    }

    @Override
    public PaginationSupportDTO<AgentInvoiceResp> queryAgentInvoicePage(AgentInvoiceQueryPageReq req) {
        return agentInvoiceService.queryAgentInvoiceList(req);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void invoiceBillInvoiceItemAdd(AgeInvoiceBillRelationReq req) {
        if (CollUtilX.isEmpty(req.getInvoicList())) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
        BigDecimal totalBillUsedAmt = BigDecimal.ZERO;
        Date currentDate = DateUtilX.getCurrentDate();
        for (AgentInvoiceBillRelationReq relationReq : req.getInvoicList()) {
            try {
                // 1.新增票单关联发票表数据
                AgentInvoiceBillRelationEntity entity = new AgentInvoiceBillRelationEntity();
                entity.setInvoiceId(relationReq.getInvoiceId());
                entity.setInvoiceBillId(req.getInvoiceBillId());
                entity.setBillUsedAmt(relationReq.getCurrentAmt());
                entity.setRevision(0);
                entity.setCreatedBy(req.getLoginName());
                entity.setCreatedDt(currentDate);
                agentInvoiceBillRelationMapper.insert(entity);
            } catch (DuplicateKeyException exception) {
                // 修改票单关联发票表的领用发票金额
                LambdaQueryWrapper<AgentInvoiceBillRelationEntity> relationQueryWrapper = new LambdaQueryWrapper<>();
                relationQueryWrapper.eq(AgentInvoiceBillRelationEntity::getInvoiceBillId, req.getInvoiceBillId());
                relationQueryWrapper.eq(AgentInvoiceBillRelationEntity::getInvoiceId, relationReq.getInvoiceId());
                List<AgentInvoiceBillRelationEntity> relationList = agentInvoiceBillRelationMapper.selectList(relationQueryWrapper);
                if (CollUtilX.isNotEmpty(relationList)) {
                    AgentInvoiceBillRelationEntity relationEntity = relationList.get(0);
                    BigDecimal billUsedAmt = relationEntity.getBillUsedAmt().add(relationReq.getCurrentAmt().subtract(relationEntity.getBillUsedAmt()));
                    relationEntity.setBillUsedAmt(billUsedAmt);
                    agentInvoiceBillRelationMapper.updateById(relationEntity);
                }
            }
            // 2.更新发票-票单领用金额
            AgentInvoiceAditReq invoiceQuery = new AgentInvoiceAditReq();
            invoiceQuery.setInvoiceId(relationReq.getInvoiceId());
            List<AgentInvoiceEntity> invoiceEntity = agentInvoiceService.invoiceQuery(invoiceQuery);
            if (CollUtilX.isEmpty(invoiceEntity)) {
                log.error(relationReq.getInvoiceId() + ":对应的进项发票不存在");
                throw new SysException(ErrorCodeEnum.OPERATE_FAIL);
            }
            AgentInvoiceEntity agentInvoice = invoiceEntity.get(0);
            // 可使用金额
            BigDecimal balance = agentInvoice.getBalance() == null ? BigDecimal.ZERO : agentInvoice.getBalance();
            // 票单领用金额
            BigDecimal billUsedAmt = agentInvoice.getBillUsedAmt() == null ? BigDecimal.ZERO : agentInvoice.getBillUsedAmt();
            if (balance.compareTo(relationReq.getCurrentAmt()) < 0) {
                log.error("本票单使用金额使用金额不允许超过可使用金额");
                throw new SysException(ParamErrorEnum.CURRENTUSEAMT_G_USEAMT);
            }
            if (balance.compareTo(relationReq.getCurrentAmt()) == 0) {
                agentInvoice.setInvoiceStatus(com.tiangong.invoice.enums.InvoiceStatusEnum.USED.key);
            } else {
                agentInvoice.setInvoiceStatus(InvoiceStatusEnum.PARTIAL_USE.key);
            }

            agentInvoice.setBalance(balance.subtract(relationReq.getCurrentAmt()));
            agentInvoice.setBillUsedAmt(billUsedAmt.add(relationReq.getCurrentAmt()));
            agentInvoice.setUpdatedBy(req.getLoginName());
            agentInvoice.setUpdatedDt(currentDate);
            agentInvoiceService.updateById(agentInvoice);
            totalBillUsedAmt = totalBillUsedAmt.add(relationReq.getCurrentAmt());
        }

        // 3.更新票单状态
        agentInvoiceBillService.updateInvoiceBillHandler(req.getInvoiceBillId(), req.getLoginName(), "invoiceBillInvoiceItemAdd");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void invoiceBillInvoiceItemDel(AgentInvoiceBillRelationReq req) {
        if (null == req.getInvoiceId() || null == req.getInvoiceBillId()) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
        if (null != req.getInvoiceBillStatus() && !(req.getInvoiceBillStatus().equals(0) || req.getInvoiceBillStatus().equals(11))) {
            throw new SysException(ParamErrorEnum.CHECK_INVOICEBILL_STATE_ERROR);
        }
        // 移除票单绑定发票数据
        LambdaQueryWrapper<AgentInvoiceBillRelationEntity> relationWrapper = new LambdaQueryWrapper<>();
        relationWrapper.eq(AgentInvoiceBillRelationEntity::getInvoiceId, req.getInvoiceId());
        relationWrapper.eq(AgentInvoiceBillRelationEntity::getInvoiceBillId, req.getInvoiceBillId());
        agentInvoiceBillRelationMapper.delete(relationWrapper);
        // 更新发票-票单领用金额
        AgentInvoiceAditReq invoiceQuery = new AgentInvoiceAditReq();
        invoiceQuery.setInvoiceId(req.getInvoiceId());
        List<AgentInvoiceEntity> invoiceEntity = agentInvoiceService.invoiceQuery(invoiceQuery);
        if (CollUtilX.isEmpty(invoiceEntity)) {
            log.error(req.getInvoiceId() + ":对应的进项发票不存在");
            throw new SysException(ErrorCodeEnum.OPERATE_FAIL);
        }
        // 更新进项发票金额和状态
        AgentInvoiceEntity agentInvoice = invoiceEntity.get(0);
        BigDecimal add = agentInvoice.getBalance().add(req.getCurrentAmt());
        BigDecimal subtract = agentInvoice.getBillUsedAmt().subtract(req.getCurrentAmt());
        agentInvoice.setBalance(add);
        agentInvoice.setBillUsedAmt(subtract);
        agentInvoice.setUpdatedBy(req.getUpdatedBy());
        agentInvoice.setUpdatedDt(DateUtilX.getCurrentDate());
        agentInvoice.setRevision(agentInvoice.getRevision());
        if (agentInvoice.getBalance().compareTo(BigDecimal.ZERO) == 0) {
            agentInvoice.setInvoiceStatus(InvoiceStatusEnum.USED.key);
        } else if ((agentInvoice.getInvoiceAmt().subtract(agentInvoice.getBalance())).compareTo(BigDecimal.ZERO) > 0) {
            agentInvoice.setInvoiceStatus(InvoiceStatusEnum.PARTIAL_USE.key);
        } else if (agentInvoice.getBalance().compareTo(agentInvoice.getInvoiceAmt()) == 0) {
            agentInvoice.setInvoiceStatus(InvoiceStatusEnum.UNUSED.key);
        }
        agentInvoiceService.updateById(agentInvoice);
        //3.更新票单状态
        agentInvoiceBillService.updateInvoiceBillHandler(req.getInvoiceBillId(), req.getUpdatedBy(), "invoiceBillInvoiceItemDel");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void invoiceBillInvoiceItemAmountEdit(AgentInvoiceBillRelationReq req) {
        if (null == req.getInvoiceId() || null == req.getId() || null == req.getInvoiceBillId()) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
        if (null != req.getInvoiceBillStatus() && !(req.getInvoiceBillStatus().equals(0) || req.getInvoiceBillStatus().equals(11))) {
            throw new SysException(ParamErrorEnum.CHECK_INVOICEBILL_STATE_ERROR);
        }
        Date currentDate = DateUtilX.getCurrentDate();
        // 更新发票-票单领用金额
        AgentInvoiceAditReq invoiceQuery = new AgentInvoiceAditReq();
        invoiceQuery.setInvoiceId(req.getInvoiceId());
        List<AgentInvoiceEntity> invoiceEntity = agentInvoiceService.invoiceQuery(invoiceQuery);
        if (CollUtilX.isEmpty(invoiceEntity)) {
            log.error(req.getInvoiceId() + ":对应的进项发票不存在");
            throw new SysException(ErrorCodeEnum.INVOICEBILL_NOT_EXIST.errorCode, ErrorCodeEnum.INVOICEBILL_NOT_EXIST.errorDesc);
        }
        AgentInvoiceEntity agentInvoice = invoiceEntity.get(0);
        // 可使用金额
        BigDecimal balance = agentInvoice.getBalance() == null ? BigDecimal.ZERO : agentInvoice.getBalance();
        // 票单领用金额
        BigDecimal billUsedAmt = agentInvoice.getBillUsedAmt() == null ? BigDecimal.ZERO : agentInvoice.getBillUsedAmt();
        if (balance.compareTo(req.getAmount()) < 0) {
            log.error("本票单使用金额使用金额不允许超过可使用金额");
            throw new SysException(ParamErrorEnum.CURRENTUSEAMT_G_USEAMT);
        }
        agentInvoice.setBalance(balance.subtract(req.getAmount()));
        agentInvoice.setBillUsedAmt(billUsedAmt.add(req.getAmount()));
        agentInvoice.setUpdatedBy(req.getUpdatedBy());
        agentInvoice.setUpdatedDt(currentDate);
        if (balance.compareTo(req.getAmount()) == 0) {
            agentInvoice.setInvoiceStatus(InvoiceStatusEnum.USED.key);
        } else {
            agentInvoice.setInvoiceStatus(InvoiceStatusEnum.PARTIAL_USE.key);
        }
        agentInvoiceService.updateById(agentInvoice);

        // 票单发票明细-修改票单领用金额
        AgentInvoiceBillRelationEntity relationEntity = new AgentInvoiceBillRelationEntity();
        relationEntity.setId(req.getId());
        relationEntity.setBillUsedAmt(req.getModifyAmount());
        agentInvoiceBillRelationMapper.updateById(relationEntity);

        // 3.更新票单状态
        agentInvoiceBillService.updateInvoiceBillHandler(req.getInvoiceBillId(), req.getUpdatedBy(), "invoiceBillInvoiceItemAmountEdit");
    }

    @Override
    public AutoCreateAgentBillResp autoCreateAgentInvoice(String orderCode, String invoiceNo, String companyCode, String requestId, String agentCode, String agentName, BigDecimal invoiceAmt, String operator, Integer orderId) {
        AutoCreateAgentBillResp autoCreateAgentBillResp = new AutoCreateAgentBillResp();
        if (StrUtil.isEmpty(orderCode)) {
            throw new SysException(ErrorCodeEnum.ORDER_CODE_IS_NOT_EXIST);
        }
        AgentInvoiceBillAditReq req = new AgentInvoiceBillAditReq();
        req.setMerchantCode(companyCode);
        req.setCreatedBy(operator);
        req.setAgentCode(agentCode);
        req.setAgentName(agentName);
        req.setInvoiceBillName(StrUtil.format("{}-{}", agentName, orderCode));
        req.setOrderCode(orderCode);
        req.setSourceType(1);
        //按离店时间查询
        req.setDateQueryType(2);
        //查询销项发票根据发票编号发票号码
        AgentInvoiceBaseReqDTO invoiceQuery = new AgentInvoiceBaseReqDTO();
        invoiceQuery.setInvoiceNum(invoiceNo);
        AgentInvoiceBaseRespDTO agentInvoiceBaseRespDTO = agentInvoiceService.invoiceDetailBase(invoiceQuery);
        if (ObjectUtil.isNotEmpty(agentInvoiceBaseRespDTO) && agentInvoiceBaseRespDTO.getBillUsedAmt().compareTo(BigDecimal.ZERO) > 0) {
            //告知创建票单失败 7表示票单创建失败
            autoCreateAgentBillResp.setResult(0);
            autoCreateAgentBillResp.setErrorCode(ErrorCodeEnum.CREATE_BILL_ALREADY_OPENED_TYPE.getErrorCode());
            autoCreateAgentBillResp.setErrorMsg(ErrorCodeEnum.CREATE_BILL_ALREADY_OPENED_TYPE.getErrorDesc());
            autoCreateAgentBillResp.setOrderId(orderId);
            autoCreateAgentBillResp.setOrderCode(orderCode);
            autoCreateAgentBillResp.setFailReason(StrUtil.format("票单创建失败，原因：{}，请核对信息后，点击创建票单按钮，到销项票单界面创建对应票单", ErrorCodeEnum.CREATE_BILL_ALREADY_OPENED_TYPE.getErrorDesc()));
            autoCreateAgentBillResp.setInvoiceStatus(7);
            autoCreateAgentBillResp.setOperator(operator);
            return autoCreateAgentBillResp;
        }
        //通过查询符合条件的分销商订单
        List<AgentOrderInfoDTO> supplyOrderList = agentInvoiceBillMapper.queryInvoiceableOrderList(req);
        if (CollUtilX.isEmpty(supplyOrderList)) {
            //告知创建票单失败 7表示票单创建失败
            autoCreateAgentBillResp.setResult(0);
            autoCreateAgentBillResp.setErrorCode(ErrorCodeEnum.CREATE_BILL_ALREADY_OPENED_TYPE.getErrorCode());
            autoCreateAgentBillResp.setErrorMsg(ErrorCodeEnum.CREATE_BILL_ALREADY_OPENED_TYPE.getErrorDesc());
            autoCreateAgentBillResp.setOrderId(orderId);
            autoCreateAgentBillResp.setOrderCode(orderCode);
            autoCreateAgentBillResp.setFailReason(StrUtil.format("票单创建失败，原因：{}，请核对信息后，点击创建票单按钮，到销项票单界面创建对应票单", ErrorCodeEnum.CREATE_BILL_ALREADY_OPENED_TYPE.getErrorDesc()));
            autoCreateAgentBillResp.setInvoiceStatus(7);
            autoCreateAgentBillResp.setOperator(operator);
            return autoCreateAgentBillResp;
        }
        //校验 根据订单号查仅可能存在一条
        AgentOrderInfoDTO agentOrderInfoDTO = supplyOrderList.get(0);
        //本次开票金额>可开票金额退出 否则仅开本次开票金额 进行创建票单
        if (agentOrderInfoDTO.getUninvoicedAmt().compareTo(invoiceAmt) < 0) {
            throw new SysException(ErrorCodeEnum.INVOICE_AMOUNT_EXCEED_AVAILABLE_AMOUNT);
        }
        agentOrderInfoDTO.setUninvoicedAmt(invoiceAmt);
        //创建票单及票单明细 异常会抛出
        Long invoiceBillId = null;
        try {
            invoiceBillId = agentInvoiceBillService.agentInvoiceBillAdd(req, supplyOrderList);
            //记录创建开票成功
            saveLog(orderCode, operator, agentOrderInfoDTO.getOrderId(), StrUtil.format("订单号：{}创建票单成功", orderCode));
        } catch (Exception e) {
            log.error("自动创建分销商发票失败", e);
//            String failReason = e instanceof SysException ? e.getMessage() : "系统异常";
            String failReason ="";
            if(e instanceof SysException){
                SysException sysException=(SysException)e;
                autoCreateAgentBillResp.setErrorCode(ErrorCodeEnum.CREATE_BILL_ALREADY_OPENED_TYPE.getErrorCode());
                autoCreateAgentBillResp.setErrorMsg(ErrorCodeEnum.CREATE_BILL_ALREADY_OPENED_TYPE.getErrorDesc());
                failReason=sysException.getMessage();
            }else{
                autoCreateAgentBillResp.setErrorCode(ErrorCodeEnum.UNKNOWN_EXCEPTION.getErrorCode());
                autoCreateAgentBillResp.setErrorMsg(ErrorCodeEnum.UNKNOWN_EXCEPTION.getErrorDesc());
                failReason=ErrorCodeEnum.UNKNOWN_EXCEPTION.getErrorDesc();
            }
            //告知创建票单失败 7表示票单创建失败
            autoCreateAgentBillResp.setResult(0);
            autoCreateAgentBillResp.setOrderId(agentOrderInfoDTO.getOrderId());
            autoCreateAgentBillResp.setOrderCode(orderCode);
            autoCreateAgentBillResp.setFailReason(StrUtil.format("票单创建失败，原因：{}，请核对信息后，点击创建票单按钮，到销项票单界面创建对应票单", failReason));
            autoCreateAgentBillResp.setInvoiceStatus(7);
            autoCreateAgentBillResp.setOperator(operator);
            return autoCreateAgentBillResp;
        }

        if (ObjectUtil.isEmpty(agentInvoiceBaseRespDTO) || agentInvoiceBaseRespDTO.getBalance().compareTo(invoiceAmt) != 0) {
            //发送mq消息推送到发票端更新状态 8为发票绑定失败
            autoCreateAgentBillResp.setResult(0);
            autoCreateAgentBillResp.setErrorCode(ErrorCodeEnum.BIND_AMOUNT_NOT_EQUAL.getErrorCode());
            autoCreateAgentBillResp.setErrorMsg(ErrorCodeEnum.BIND_AMOUNT_NOT_EQUAL.getErrorDesc());
            autoCreateAgentBillResp.setOrderId(agentOrderInfoDTO.getOrderId());
            autoCreateAgentBillResp.setOrderCode(orderCode);
            autoCreateAgentBillResp.setFailReason("发票绑定失败，原因：发票可用金额不等于票单本次开票金额，请核对信息后手动绑定");
            autoCreateAgentBillResp.setInvoiceStatus(8);
            autoCreateAgentBillResp.setOperator(operator);
            return autoCreateAgentBillResp;
        }
        //创建票单后绑定票单
        AgeInvoiceBillRelationReq relationReq = new AgeInvoiceBillRelationReq();
        relationReq.setLoginName(operator);
        relationReq.setInvoiceBillId(invoiceBillId);
        AgentInvoiceBillRelationReq agentInvoiceBillRelationReq = new AgentInvoiceBillRelationReq();
        relationReq.setInvoicList(Collections.singletonList(agentInvoiceBillRelationReq));
        agentInvoiceBillRelationReq.setInvoiceId(agentInvoiceBaseRespDTO.getInvoiceId());
        agentInvoiceBillRelationReq.setCurrentAmt(agentInvoiceBaseRespDTO.getInvoiceAmt());
        agentInvoiceBillService.invoiceBillInvoiceItemAdd(relationReq);
        //记录创建开票成功
        saveLog(orderCode, operator, agentOrderInfoDTO.getOrderId(), StrUtil.format("订单号：{}发票绑定成功", orderCode));
        //平账
        AgentInvoiceBillAditReq agentInvoiceBillAditReq = new AgentInvoiceBillAditReq();
        agentInvoiceBillAditReq.setMerchantCode(companyCode);
        agentInvoiceBillAditReq.setUpdatedBy(operator);
        agentInvoiceBillAditReq.setInvoiceBillId(invoiceBillId);
        agentInvoiceBillService.suppInvoiceBillConfirmBalan(agentInvoiceBillAditReq);
        //发送mq消息推送到发票端更新状态 1为已开票
        autoCreateAgentBillResp.setResult(1);
        autoCreateAgentBillResp.setOrderId(agentOrderInfoDTO.getOrderId());
        autoCreateAgentBillResp.setOrderCode(orderCode);
        autoCreateAgentBillResp.setInvoiceStatus(1);
        autoCreateAgentBillResp.setOperator(operator);
        return autoCreateAgentBillResp;
    }

    @Override
    public void agentInvoiceBillItemAmountEdit(AgentInvoiceBillRelationReq req) {
        if (null == req.getId() || null == req.getInvoiceBillId() || null == req.getModifyAmount()) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
        // 查询票单信息
        AgentInvoiceBillEntity agentInvoiceBill = agentInvoiceBillMapper.selectById(req.getInvoiceBillId());
        if (null == agentInvoiceBill) {
            log.error(req.getInvoiceBillId() + ":对应的票单不存在");
            throw new SysException(ErrorCodeEnum.OPERATE_FAIL);
        }
        if (null != agentInvoiceBill.getInvoiceBillStatus() && !(agentInvoiceBill.getInvoiceBillStatus().equals(0) || agentInvoiceBill.getInvoiceBillStatus().equals(11))) {
            throw new SysException(ParamErrorEnum.CHECK_INVOICEBILL_STATE_ERROR);
        }
        AgentInvoiceBillItemQueryReq queryInvoiceBillItemParam = new AgentInvoiceBillItemQueryReq();
        queryInvoiceBillItemParam.setInvoiceBillId(req.getInvoiceBillId());
        List<AgentInvoiceBillItemEntity> agentInvoiceBillItemEntities = agentInvoiceBillItemMapper.selectList(Wrappers.<AgentInvoiceBillItemEntity>lambdaQuery()
                .eq(AgentInvoiceBillItemEntity::getInvoiceBillId, req.getInvoiceBillId()));
        AgentInvoiceBillItemEntity needUpdateItemEntity = null;
        BigDecimal usedAmount = BigDecimal.ZERO;
        for (AgentInvoiceBillItemEntity agentInvoiceBillItemEntity : agentInvoiceBillItemEntities) {
            if (agentInvoiceBillItemEntity.getId().equals(req.getId())) {
                usedAmount = usedAmount.add(req.getModifyAmount());
                needUpdateItemEntity = agentInvoiceBillItemEntity;
                needUpdateItemEntity.setInvoicePayableAmt(req.getModifyAmount());
            } else {
                usedAmount = usedAmount.add(agentInvoiceBillItemEntity.getInvoicePayableAmt());
            }
        }
        if (needUpdateItemEntity == null) {
            throw new SysException(ErrorCodeEnum.OPERATE_FAIL);
        }
        agentInvoiceBillItemMapper.updateById(needUpdateItemEntity);
        agentInvoiceBill.setInvoicePayableAmt(usedAmount);
        agentInvoiceBillMapper.updateById(agentInvoiceBill);
        // 3.更新票单状态
        agentInvoiceBillService.updateInvoiceBillHandler(req.getInvoiceBillId(), req.getUpdatedBy(), "invoiceBillInvoiceItemAmountEdit");

        log.info("票单发票明细修改票单领用金额成功，票单ID：{}，明细id：{}",
                req.getInvoiceBillId(), req.getId());
    }

    private void saveLog(String orderCode, String operator, Integer orderId, String msg) {
        try {
            SaveOrderLogDTO saveOrderLogDTO = new SaveOrderLogDTO();
            saveOrderLogDTO.setOrderId(orderId);
            saveOrderLogDTO.setCreatedBy(operator);
            saveOrderLogDTO.setOperator(operator);
            saveOrderLogDTO.setTarget(orderCode);
            saveOrderLogDTO.setContent(msg);
            orderRemote.saveOrderLog(Collections.singletonList(saveOrderLogDTO));
        } catch (Exception e) {
            log.error("日志记录失败", e);
        }
    }

    /**
     * 更新票单状态
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateInvoiceBillHandler(Long invoiceBillId, String loginName, String operation) {
        // 查询票单信息
        AgentInvoiceBillEntity agentInvoiceBill = agentInvoiceBillMapper.selectById(invoiceBillId);
        if (null == agentInvoiceBill) {
            log.error(invoiceBillId + ":对应的票单不存在");
            throw new SysException(ErrorCodeEnum.OPERATE_FAIL);
        }
        // 票单状态
        Integer invoiceBillStatus = agentInvoiceBill.getInvoiceBillStatus();

        // 1.查询绑定发票明细
        BigDecimal totalBillUsedAmt = BigDecimal.ZERO;
        LambdaQueryWrapper<AgentInvoiceBillRelationEntity> relationQueryWrapper = new LambdaQueryWrapper<>();
        relationQueryWrapper.eq(AgentInvoiceBillRelationEntity::getInvoiceBillId, invoiceBillId);
        List<AgentInvoiceBillRelationEntity> relationList = agentInvoiceBillRelationMapper.selectList(relationQueryWrapper);
        if (CollUtilX.isEmpty(relationList) && invoiceBillStatus.equals(InvoiceBillStatusEnum.CANACCOUNT.key)) {
            AgentInvoiceBillEntity updateInvoiceBillParam = new AgentInvoiceBillEntity();
            updateInvoiceBillParam.setInvoiceBillId(invoiceBillId);
            updateInvoiceBillParam.setUpdatedBy(loginName);
            updateInvoiceBillParam.setUpdatedDt(DateUtilX.getCurrentDate());
            updateInvoiceBillParam.setInvoiceBillStatus(InvoiceBillStatusEnum.UNBOUND.key);
            int i = agentInvoiceBillMapper.updateAgentInvoiceBill(updateInvoiceBillParam);
            if (i == 0) {
                log.error("销项票票单状态更新为未绑定失败,票单id:" + invoiceBillId);
                throw new SysException(ErrorCodeEnum.HOTEL_NULL.errorCode, ErrorCodeEnum.HOTEL_NULL.errorDesc);
            }
        } else {
            for (AgentInvoiceBillRelationEntity relation : relationList) {
                totalBillUsedAmt = totalBillUsedAmt.add(relation.getBillUsedAmt());
            }
        }

        // 2.查询票单明细
        AgentInvoiceBillItemQueryReq queryInvoiceBillItemParam = new AgentInvoiceBillItemQueryReq();
        queryInvoiceBillItemParam.setInvoiceBillId(invoiceBillId);
        List<AgentInvoiceBillItemResp> billItemResps = agentInvoiceBillItemService.queryAgentInvoiceBillItemList(queryInvoiceBillItemParam);
        BigDecimal totalinvoicePayableAmt = BigDecimal.ZERO;
        if (CollUtilX.isEmpty(billItemResps) && invoiceBillStatus.equals(InvoiceBillStatusEnum.CANACCOUNT.key)) {
            AgentInvoiceBillEntity updateInvoiceBillParam = new AgentInvoiceBillEntity();
            updateInvoiceBillParam.setInvoiceBillId(invoiceBillId);
            updateInvoiceBillParam.setUpdatedBy(loginName);
            updateInvoiceBillParam.setUpdatedDt(DateUtilX.getCurrentDate());
            updateInvoiceBillParam.setInvoiceBillStatus(InvoiceBillStatusEnum.UNBOUND.key);
            int i = agentInvoiceBillMapper.updateAgentInvoiceBill(updateInvoiceBillParam);
            if (i == 0) {
                log.error("销项票票单状态更新为未绑定失败,票单id:" + invoiceBillId);
                throw new SysException(ErrorCodeEnum.HOTEL_NULL.errorCode, ErrorCodeEnum.HOTEL_NULL.errorDesc);
            }
        } else {
            for (AgentInvoiceBillItemResp itemResp : billItemResps) {
                totalinvoicePayableAmt = totalinvoicePayableAmt.add(itemResp.getInvoicePayableAmt());
            }
        }

        if (totalBillUsedAmt.compareTo(BigDecimal.ZERO) > 0 && totalinvoicePayableAmt.compareTo(BigDecimal.ZERO) > 0) {
            if (totalBillUsedAmt.compareTo(totalinvoicePayableAmt) == 0) {
                AgentInvoiceBillEntity updateInvoiceBillParam = new AgentInvoiceBillEntity();
                updateInvoiceBillParam.setInvoiceBillId(invoiceBillId);
                updateInvoiceBillParam.setUpdatedBy(loginName);
                updateInvoiceBillParam.setUpdatedDt(DateUtilX.getCurrentDate());
                updateInvoiceBillParam.setInvoiceBillStatus(InvoiceBillStatusEnum.CANACCOUNT.key);
                updateInvoiceBillParam.setRevision(agentInvoiceBill.getRevision());
                int i = agentInvoiceBillMapper.updateAgentInvoiceBill(updateInvoiceBillParam);
                if (i == 0) {
                    log.error("销项票票单状态更新为可平账失败,票单id:" + invoiceBillId);
                    throw new SysException(ErrorCodeEnum.HOTEL_NULL.errorCode, ErrorCodeEnum.HOTEL_NULL.errorDesc);
                }
            } else if (operation.equals("invoiceBillInvoiceItemAmountEdit") && totalBillUsedAmt.compareTo(totalinvoicePayableAmt) > 0) {
                log.error("累计使用金额不可超过票单金额！");
                throw new SysException(ParamErrorEnum.USAGEAMT_G_TICKETAMT);
            } else {
                AgentInvoiceBillEntity updateInvoiceBillParam = new AgentInvoiceBillEntity();
                updateInvoiceBillParam.setInvoiceBillId(invoiceBillId);
                updateInvoiceBillParam.setUpdatedBy(loginName);
                updateInvoiceBillParam.setUpdatedDt(DateUtilX.getCurrentDate());
                updateInvoiceBillParam.setInvoiceBillStatus(InvoiceBillStatusEnum.UNBOUND.key);
                updateInvoiceBillParam.setRevision(agentInvoiceBill.getRevision());
                int i = agentInvoiceBillMapper.updateAgentInvoiceBill(updateInvoiceBillParam);
                if (i == 0) {
                    log.error("销项票票单状态更新为待绑定失败,票单id:" + invoiceBillId);
                    throw new SysException(ErrorCodeEnum.HOTEL_NULL.errorCode, ErrorCodeEnum.HOTEL_NULL.errorDesc);
                }
            }
        }
        Response.success(true);
    }

    /**
     * 确认平账/坏账处理逻辑
     */
    public void confirmHandler(AgentInvoiceBillDetailResp invoiceBillDetailModel, int invoiceBillStatus, String updatedBy) {
        String invoiceBillId = invoiceBillDetailModel.getInvoiceBillId();
        // 2.票单明细中的单增加已开票金额，增加金额=各单的“本票单要开票金额”
        PaginationSupportDTO<AgentInvoiceBillItemResp> invoiceBillDetailDto = invoiceBillDetailModel.getInvoiceBillDetails();
        if (CollUtilX.isEmpty(invoiceBillDetailDto.getItemList())) {
            log.error("票单明细数据为空,票单id:" + invoiceBillId);
            throw new SysException(ErrorCodeEnum.CANNOT_OPERATE.errorCode, ErrorCodeEnum.CANNOT_OPERATE.errorDesc);
        }
        List<String> supplyOrderCodes = new ArrayList<>();
        Map<String, BigDecimal> billItemMap = new HashMap<>();
        List<AgentInvoiceBillItemResp> invoiceBillDetails = invoiceBillDetailDto.getItemList();
        BigDecimal invoicedAmtTotal = BigDecimal.ZERO;
        for (AgentInvoiceBillItemResp detail : invoiceBillDetails) {
            AgentInvoiceBillItemEntity billItem = new AgentInvoiceBillItemEntity();
            billItem.setId(Long.valueOf(detail.getInvoiceBillItemId()));
            billItem.setInvoiceAmt(detail.getInvoicePayableAmt());
            agentInvoiceBillItemMapper.updateById(billItem);
            supplyOrderCodes.add(detail.getOrderCode());
            billItemMap.put(detail.getOrderCode(), detail.getInvoicePayableAmt());
            invoicedAmtTotal = invoicedAmtTotal.add(detail.getInvoicePayableAmt());
        }

        // 如果明细单单有更新不允许平账或坏账
        AgentInvoiceBillItemQueryReq diffQuery = new AgentInvoiceBillItemQueryReq();
        diffQuery.setInvoiceBillId(Long.valueOf(invoiceBillId));
        PaginationSupportDTO<AgentInvoiceBillItemResp> dto = agentInvoiceBillItemDiffPage(diffQuery);
//        if (dto != null && CollUtilX.isNotEmpty(dto.getItemList())) {
//            log.error("当前票单明细单有更新,不可进行平账或坏账,票单id:" + invoiceBillId);
//            throw new SysException(ErrorCodeEnum.ITEMUPEXIST_NOTBAD_CONFIRMED.errorCode, ErrorCodeEnum.ITEMUPEXIST_NOTBAD_CONFIRMED.errorDesc);
//        }
        // 1.票单状态改为已坏账/已平账
        AgentInvoiceBillEntity invoiceBill = new AgentInvoiceBillEntity();
        invoiceBill.setInvoiceBillId(Long.valueOf(invoiceBillId));
        invoiceBill.setInvoiceBillStatus(invoiceBillStatus);
        invoiceBill.setInvoicedAmt(invoicedAmtTotal);
        invoiceBill.setConfirmDt(DateUtilX.getCurrentDate());
        invoiceBill.setConfirmer(updatedBy);
        agentInvoiceBillMapper.updateById(invoiceBill);

        // 3.更新供应商发票绑定表-供货单累计开票金额
        LambdaQueryWrapper<AgentInvoiceBillOrderStateEntity> billOrderStateQueryWrapper = new LambdaQueryWrapper<>();
        billOrderStateQueryWrapper.in(AgentInvoiceBillOrderStateEntity::getOrderCode, supplyOrderCodes);
        billOrderStateQueryWrapper.eq(AgentInvoiceBillOrderStateEntity::getCurrentInvoiceBillId, invoiceBillId);
        List<AgentInvoiceBillOrderStateEntity> stateList = agentInvoiceBillOrderStateMapper.selectList(billOrderStateQueryWrapper);
        for (AgentInvoiceBillOrderStateEntity state : stateList) {
            AgentInvoiceBillOrderStateEntity stateEntity = new AgentInvoiceBillOrderStateEntity();
            stateEntity.setId(state.getId());
            BigDecimal currentInvoiceAmt = billItemMap.get(state.getOrderCode());
            BigDecimal alreadyInvoiceAmt = state.getAlreadyInvoiceAmt().add(currentInvoiceAmt);
            stateEntity.setAlreadyInvoiceAmt(alreadyInvoiceAmt);
            stateEntity.setInvoiceBillStatus(invoiceBillStatus);
            agentInvoiceBillOrderStateMapper.updateById(stateEntity);
        }
    }

    /**
     * 校验票单明细添加供货单参数
     */
    private void validateInvoiceBillItemAddParam(AgentInvoiceBillItemAditReq req) {
        if (null == req) {
            log.error("销项票单添加票单明细失败：参数为空");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (StrUtilX.isEmpty(req.getInvoiceBillId())) {
            log.error("销项票单添加票单明细失败：参数为空,票单id");
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_INVOICEBILLID);
        } else if (StrUtilX.isEmpty(req.getOrderCode())) {
            log.error("销项票单添加票单明细失败：参数为空,订单号");
            throw new SysException(ParamErrorEnum.SUPPLY_ORDER_ISEMPTY);
        } else if (null == req.getInvoicePayableAmt()) {
            log.error("销项票单添加票单明细失败:订单应收金额为空");
            throw new SysException(ParamErrorEnum.INVALID_INPUTPARAM);
        } else if (StrUtilX.isEmpty(req.getMerchantCode())) {
            log.error("销项票单添加票单明细失败：参数为空,商家编码");
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_MERCHANTCODE);
        }
    }

    /**
     * 校验可可添加供货单列表
     */
    private void validateInvoiceableInvoiceBillItemQueryParam(AgentInvoiceBillItemQueryReq req) {
        if (null == req) {
            log.error("添加票单明细失败：参数为空！");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (null == req.getDateQueryType()) {
            log.error("添加票单明细失败：参数为空,查询口径");
            throw new SysException(ParamErrorEnum.QUERY_CALIBER);
        } else if (StrUtilX.isEmpty(req.getStartDate())) {
            log.error("添加票单明细失败：参数为空,开始日期");
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_STARTTIME);
        } else if (StrUtilX.isEmpty(req.getEndDate())) {
            log.error("添加票单明细失败：参数为空,结束日期");
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_ENDTIME);
        }

        Date startDate = DateUtilX.stringToDate(req.getStartDate());
        Date endDate = DateUtilX.stringToDate(req.getEndDate());
        if (DateUtilX.getDay(startDate, endDate) > 92) {
            log.error("添加票单明细失败：日期间隔大于92天");
            throw new SysException(ParamErrorEnum.DATE_GTT_THREE_MONTH);
        }
    }


    /**
     * 校验票单列表查询参数
     */
    private void validateInvoiceBillListParam(AgentInvoiceBillQueryReq req) {
        if (StrUtilX.isNotEmpty(req.getCreatedStartDt()) && StrUtilX.isNotEmpty(req.getCreatedEndDt())) {
            Date createdStartDate = DateUtilX.stringToDate(req.getCreatedStartDt());
            Date createdEndDate = DateUtilX.stringToDate(req.getCreatedEndDt());

            if (createdStartDate.after(createdEndDate)) {
                log.error("销项票票单列表查询失败：创建开始日期大于创建结束日期");
                throw new SysException(ParamErrorEnum.CRSTARTDT_GT_ENDDT_ERROR);
            }

            if (DateUtilX.getDay(createdStartDate, createdEndDate) > 92) {
                log.error("销项票票单列表查询失败：创建日期跨度大于三个月！");
                throw new SysException(ParamErrorEnum.DATE_GTT_THREE_MONTH);
            }

            if (StringUtils.isNotBlank(req.getConfirmStartDt()) && StringUtils.isNotBlank(req.getConfirmEndDt())) {
                Date confirmStartDt = DateUtilX.stringToDate(req.getConfirmStartDt());
                Date confirmEndDt = DateUtilX.stringToDate(req.getConfirmEndDt());
                if (confirmStartDt.after(confirmEndDt)) {
                    log.error("销项票票单列表查询失败：确认开始日期大于确认结束日期！");
                    throw new SysException(ParamErrorEnum.CONFIRMDT_GT_ENDDT_ERROR);
                }
                if (DateUtilX.getDay(confirmStartDt, confirmEndDt) > 92) {
                    log.error("销项票票单列表查询失败：确认日期跨度大于三个月！");
                    throw new SysException(ParamErrorEnum.DATE_GTT_THREE_MONTH);
                }
            }
        }
    }

    /**
     * 生成票单编码
     */
    private String generateInvoiceBillCode() {
        String invoieCodeKey = InvoiceConstant.invoicePrefix + InvoiceConstant.AGENT_INVOICE_CODE;
        String invoieCode = RedisTemplateX.get(invoieCodeKey);
        if (StringUtils.isBlank(invoieCode)) {
            RedisTemplateX.set(invoieCodeKey, "1");
            invoieCode = "VI0000001";
        } else {
            Long incr = RedisTemplateX.incr(invoieCodeKey, 1);
            String serialNumberStr = String.format("%07d", incr);
            invoieCode = "VI" + serialNumberStr;
        }
        return invoieCode;
    }

    /**
     * 校验参数
     */
    private void validateInvoiceBillAddParam(AgentInvoiceBillAditReq req) {
        if (null == req) {
            log.error("销项票单创建失败：请求参数为空！");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (StrUtilX.isEmpty(req.getAgentCode())) {
            log.error("销项票单创建失败：请求参数为空,客户编码！");
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_SUPPLYCODE);
        } else if (StrUtilX.isEmpty(req.getInvoiceBillName())) {
            log.error("销项票单创建失败：请求参数为空,票单名称！");
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_ISEMPTY_INVOICEBILL_NAME);
        } else if (StrUtilX.isEmpty(req.getMerchantCode())) {
            log.error("销项票单创建失败：请求参数为空,商家编码！");
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_MERCHANTCODE);
        }
    }

    /**
     * 校验票单明细删除参数
     */
    private void validateInvoiceBillItemDelParam(AgentInvoiceBillItemAditReq req) {
        if (null == req) {
            log.error("销项票单删除票单明细失败：参数为空");
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        } else if (StrUtilX.isEmpty(req.getInvoiceBillId())) {
            log.error("销项票单删除票单明细失败：参数为空,票单id");
            throw new SysException(ParamErrorEnum.INVALID_INPUTPARAM);
        } else if (StrUtilX.isEmpty(req.getInvoiceBillItemId())) {
            log.error("销项票单删除票单明细失败：参数为空,票单明细id");
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_INVOICEBILLITEMID);
        }
    }

    /**
     * 查询符合自动开票条件的订单发票列表
     * 条件：
     * 1、订单开票状态是下载成功待创建票单
     * 2、订单是已确认或已完成
     * 3、且离店时间是近3个月的日期（当天往前的三个月）
     * 4、发票类型为电子普票、电子专票
     *
     * @return 符合条件的订单发票列表（包含商家编码）
     */
    private OrderInvoiceWithCompanyDO queryAutoInvoiceList(Integer orderInvoiceId) {
//        // 计算3个月前的日期
//        LocalDate threeMonthsAgoLocal = LocalDate.now().minusMonths(3);
//        LocalDate currentDateLocal = LocalDate.now();
//        // 转换为Date对象，并设置为0点0分00秒
//        Date threeMonthsAgo = Date.from(threeMonthsAgoLocal.atStartOfDay(ZoneId.systemDefault()).toInstant());
//        Date currentDate = Date.from(currentDateLocal.plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant().minusMillis(1));

        // 设置查询参数
//        List<Integer> invoiceStatusList = Arrays.asList(com.tiangong.enums.InvoiceStatusEnum.DOWNLOAD_SUCCESS_WAIT_CREATE_BILL.getNo(), com.tiangong.enums.InvoiceStatusEnum.CREATE_BILL_FAILED.getNo()); // 下载成功待创建票单
//        List<Integer> invoiceTypeList = Arrays.asList(InvoiceTypeEnum.ELECTRONIC_COMMON_INVOICE.getNo(), InvoiceTypeEnum.ELECTRONIC_SPECIAL_INVOICE.getNo()); // 电子普票和电子专票
//        List<Integer> confirmStatusList = Arrays.asList(ConfirmationStatusEnum.CONFIRMED.key, ConfirmationStatusEnum.DONE.key); // 已确认和已完成
        QueryAutoInvoiceListDTO queryAutoInvoiceListDTO = new QueryAutoInvoiceListDTO();
//        queryAutoInvoiceListDTO.setInvoiceStatusList(invoiceStatusList);
//        queryAutoInvoiceListDTO.setInvoiceTypeList(invoiceTypeList);
//        queryAutoInvoiceListDTO.setConfirmStatusList(confirmStatusList);
//        queryAutoInvoiceListDTO.setStartDate(threeMonthsAgo);
//        queryAutoInvoiceListDTO.setEndDate(currentDate);
        queryAutoInvoiceListDTO.setCompanyCode(null);
        queryAutoInvoiceListDTO.setOrderInvoiceId(orderInvoiceId);
        // 调用Mapper方法查询符合条件的订单发票
        Response<List<OrderInvoiceWithCompanyDO>> listResponse = orderRemote.queryAutoInvoiceList(
                queryAutoInvoiceListDTO
        );
        if (listResponse == null || !listResponse.isSuccess()) {
            log.error("创建发票失败 查询订单发票失败:{}", JSONUtil.toJsonStr(listResponse));
            throw new SysException(ErrorCodeEnum.UNKNOWN_EXCEPTION);
        }
        if (CollectionUtils.isEmpty(listResponse.getModel())) {
            return null;
        }
        return listResponse.getModel().get(0);
    }

}
