package com.tiangong.h5.domain.resp;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2023/11/10 10:33
 */
@Data
public class AgentFinanceResp {

    /**
     * 客户编码
     */
    private String agentCode;

    /**
     * 客户公司名称
     */
    private String orgName;

    /**
     * 客户结算类型
     */
    private Integer settlementType;

    /**
     * 客户币种
     */
    private Integer settlementCurrency;


    /**
     * 币种名
     */
    private String saleCurrencyCode;

    /**
     *剩余信用额度
     */
    private BigDecimal balance;

    /**
     * 保留小数位
     */
    private Integer decimalPlaces;

    /**
     * 取整方式：1向上取整 2向下取整 3四舍五入
     */
    private Integer roundingType;

    /**
     * 国内全球标识：1国内 2全球
     */
    private Integer supplyType;
}
