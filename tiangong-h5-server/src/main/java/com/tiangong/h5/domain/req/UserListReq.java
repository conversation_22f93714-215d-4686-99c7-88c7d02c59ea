package com.tiangong.h5.domain.req;

import com.tiangong.cloud.commonbean.utils.PageVo;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @create 2023/11/7 11:23
 */
@Data
public class UserListReq extends PageVo {

    /**
     * 客户编码
     */
    @NotEmpty(message = "EMPTY_PARAM_AGENTCODE")
    private String agentCode;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 是否启用 0否 1是 空全部
     */
    private Integer availableStatus;
}
