package com.tiangong.h5.service.impl;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.tiangong.common.Response;
import com.tiangong.dis.dto.ResponseResult;
import com.tiangong.dis.enums.DhubReturnCodeEnum;
import com.tiangong.dis.remote.HotelOrderRemote;
import com.tiangong.dis.remote.HotelProductRemote;
import com.tiangong.dis.remote.SysConfigRemote;
import com.tiangong.dto.common.CompanyDTO;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.dto.common.SysConfigDTO;
import com.tiangong.dto.common.SysConfigReq;
import com.tiangong.dto.hotel.base.HotelInfoCollectionDTO;
import com.tiangong.dto.hotel.base.HotelInfoCollectionReq;
import com.tiangong.dto.order.request.*;
import com.tiangong.dto.order.response.*;
import com.tiangong.dto.product.PriceInfoDetail;
import com.tiangong.dto.product.PriceItem;
import com.tiangong.dto.product.RoomItemDetail;
import com.tiangong.enums.ChannelEnum;
import com.tiangong.enums.ErrorCodeEnum;
import com.tiangong.enums.LanguageTypeEnum;
import com.tiangong.enums.UserTypeEnum;
import com.tiangong.exception.SysException;
import com.tiangong.h5.config.SettingsConstant;
import com.tiangong.h5.convert.DTOConvert;
import com.tiangong.h5.domain.DTO.QueryVoucherDTO;
import com.tiangong.h5.domain.req.AddOrderReq;
import com.tiangong.h5.domain.req.CancelOrderReq;
import com.tiangong.h5.domain.req.CheckBookingReq;
import com.tiangong.h5.domain.req.OrderDetailReq;
import com.tiangong.h5.domain.resp.CheckOrderResp;
import com.tiangong.h5.domain.resp.OrderResp;
import com.tiangong.h5.enums.ErrorEnum;
import com.tiangong.h5.mapper.OrderMapper;
import com.tiangong.h5.service.OrderService;
import com.tiangong.hotel.BaseHotelInfoUrl;
import com.tiangong.keys.RedisKey;
import com.tiangong.order.remote.OrderQueryRemote;
import com.tiangong.order.remote.request.OrderCodeReq;
import com.tiangong.order.remote.request.QueryOrderListDTO;
import com.tiangong.order.remote.response.OrderDTO;
import com.tiangong.order.remote.response.OrderSimpleDTO;
import com.tiangong.organization.remote.CompanyRemote;
import com.tiangong.organization.remote.dto.AgentAccountConfig;
import com.tiangong.organization.remote.dto.CompanyAddDTO;
import com.tiangong.organization.remote.dto.CompanySelectDTO;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.util.SendCodeUtil;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2023/11/9 15:00
 */
@Service
@Slf4j
public class OrderServiceImpl implements OrderService {

    @Autowired
    private CompanyRemote companyRemote;

    @Autowired
    private HotelOrderRemote hotelOrderRemote;

    @Autowired
    private HotelProductRemote hotelProductRemote;

    @Autowired
    private OrderQueryRemote orderQueryRemote;

    @Resource
    private OrderMapper orderMapper;

    @Autowired
    private SendCodeUtil sendCodeUtil;

    @Autowired
    private Executor defaultPool;

    @Autowired
    private SettingsConstant settingsConstant;

    @Autowired
    private SysConfigRemote sysConfigRemote;

    @Override
    public OrderResp submitOrder(AddOrderReq req) {
        OrderResp orderResp = new OrderResp();
        CreateOrderRequest dto = DTOConvert.INSTANCE.CreateOrderConvert(req);
        dto.setBedInfoDesc(req.getBedInfoDesc());
        dto.setRoomNum(req.getRoomQty());
        dto.setTotalAmount(req.getOrderAmt());
        dto.setLinkMan(req.getContactName());
        dto.setLinkPhone(req.getContactPhone());
        dto.setLinkCountryCode(req.getLinkCountryCode());
        dto.setEmail(req.getContactEmail());
        dto.setSpecialDemand(req.getSpecialRequest());
        dto.setPriceItems(req.getPriceItems());
        dto.setUserAccount(req.getUserAccount());
        dto.setBedInfos(req.getBedInfos());
        dto.setChannelCode("B2B");
        dto.setOrderTrafficJson(req.getOrderTrafficJson());
        dto.setBedTypeDiff(req.getBedTypeDiff());
        if (req.getUserType() != null && req.getUserType().intValue() == UserTypeEnum.SUPER_ADMIN.type) {
            dto.setIsSubstituted(2);//表示帮客户下单
        }
        AgentAccountConfig agentAccountConfig = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.AGENT_ACCOUNT_CONFIG, req.getAgentCode()), AgentAccountConfig.class);
        if (agentAccountConfig != null) {
            dto.setSupplyType(agentAccountConfig.getDomesticOrOverseas() + "");
        }
        String uuId = UUID.randomUUID().toString();
        req.setRequestId(uuId);
        dto.setRequestId(uuId);
        ResponseResult<CreateOrderResponse> order = hotelOrderRemote.createOrder(dto);
        if (order != null && DhubReturnCodeEnum.SUCCESS.no.equals(order.getReturnCode())) {
            CreateOrderResponse bussinessResponse = order.getBussinessResponse();
            orderResp.setResult(1);
            orderResp.setOrderCode(bussinessResponse.getFcOrderCode());
            return orderResp;
        } else {
            if (order != null && StrUtilX.isNotEmpty(order.getReturnCode())) {
                // 如果编码为数字，需要转换编码
                if (StrUtilX.isNumeric(order.getReturnCode())) {
                    String codeByNo = DhubReturnCodeEnum.getCodeByNo(order.getReturnCode());
                    if (StrUtilX.isNotEmpty(codeByNo)) {
                        order.setReturnCode(codeByNo);
                    }
                }
                throw new SysException(order.getReturnCode(), order.getReturnMsg());
            } else {
                throw new SysException(ErrorEnum.CREATE_ORDER_IS_ERROR.type, ErrorEnum.CREATE_ORDER_IS_ERROR.content + (order == null ? null : "," + order.getReturnMsg()));
            }
        }
    }

    /**
     * 试预定，比对总价
     */
    @Override
    public CheckOrderResp checkOrder(CheckBookingReq req) {
        CheckOrderResp resp = new CheckOrderResp();
        CheckBookingRequest dto = DTOConvert.INSTANCE.CheckOrderConvert(req);
        dto.setSource(2);
        dto.setRoomNum(req.getRoomQty());
        dto.setUserAccount(req.getUserAccount());
        dto.setOrderAmt(req.getOrderAmt());
        AgentAccountConfig agentAccountConfig = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.AGENT_ACCOUNT_CONFIG, req.getAgentCode()), AgentAccountConfig.class);
        if (agentAccountConfig != null) {
            dto.setSupplyType(agentAccountConfig.getDomesticOrOverseas() + "");
        }
        String uuId = UUID.randomUUID().toString();
        dto.setRequestId(uuId);
        req.setRequestId(uuId);
        //判断是否可以预定
        ResponseResult<CheckBookingResponse> responseResult = hotelOrderRemote.checkBooking(dto);
        if (responseResult == null || !"000".equals(responseResult.getReturnCode())) {
            if (responseResult != null && StrUtilX.isNotEmpty(responseResult.getReturnCode())) {
                // 如果编码为数字，需要转换编码
                if (StrUtilX.isNumeric(responseResult.getReturnCode())) {
                    String codeByNo = DhubReturnCodeEnum.getCodeByNo(responseResult.getReturnCode());
                    if (StrUtilX.isNotEmpty(codeByNo)) {
                        responseResult.setReturnCode(codeByNo);
                    }
                }
                throw new SysException(responseResult.getReturnCode(), responseResult.getReturnMsg());
            } else {
                throw new SysException(ErrorEnum.PRE_BOOK_ERROR.type, ErrorEnum.PRE_BOOK_ERROR.content + (responseResult == null ? null : "," + responseResult.getReturnMsg()));
            }
        }
        CheckBookingResponse checkBookingResponse = responseResult.getBussinessResponse();
        if (checkBookingResponse != null && checkBookingResponse.getCanBook() != null && checkBookingResponse.getCanBook() == 1) {
            //比对总价

            if (checkBookingResponse.getTotalSalePrice() != null && (checkBookingResponse.getTotalSalePrice().compareTo(dto.getOrderAmt()) == 0)) {
                resp.setCanBook(1);
            } else {
                if (checkBookingResponse.getTotalSalePrice() == null || checkBookingResponse.getTotalSalePrice().compareTo(BigDecimal.ZERO) < 0) {
                    throw new SysException(ErrorEnum.PRE_BOOK_ERROR.type, ErrorEnum.PRE_BOOK_ERROR.content);
                } else {
                    resp.setCanBook(2);
                    resp.setChangePrice(checkBookingResponse.getTotalSalePrice());
                    resp.setSalePrice(dto.getOrderAmt());
                    List<RoomItemDetail> roomItemDetails = new ArrayList<>();
                    for (OrderRoomDetailDto detailDto : checkBookingResponse.getOrderRoomDetails()) {
                        RoomItemDetail detail = DTOConvert.INSTANCE.RoomConvert(detailDto);
                        List<PriceItem> priceItems = new ArrayList<>();
                        //组装每一天的数据
                        for (PriceInfoDetail infoDetail : detailDto.getPriceInfoDetails()) {
                            PriceItem priceItem = DTOConvert.INSTANCE.PriceConvert(infoDetail);
                            priceItem.setSaleDate(infoDetail.getDate());
                            priceItems.add(priceItem);
                        }
                        detail.setPriceItems(priceItems);
                        roomItemDetails.add(detail);
                    }
                    resp.setRoomItemDetails(roomItemDetails);
                    resp.setCancelRestrictions(checkBookingResponse.getCancelRestrictions());
                }


            }
        } else {
            resp.setCanBook(0);
            throw new SysException(ErrorEnum.PRE_BOOK_ERROR.type, ErrorEnum.PRE_BOOK_ERROR.content);
        }
        return resp;
    }

    @Override
    public Response<PaginationSupportDTO<OrderSimpleDTO>> orderList(QueryOrderListDTO req) {
        req.setCompanyCode(CompanyDTO.COMPANY_CODE);
        if (StringUtils.isEmpty(req.getAgentCode())) {
            throw new SysException(ErrorEnum.AGENT_CODE_IS_NOT_NULL.type, ErrorEnum.AGENT_CODE_IS_NOT_NULL.content);
        }

        if (req.getOrderTypeList() == 1) {//查询全部
            req.setUserAccount("");
        }
        if (req.getOrderUser() != null) {
            req.setUserAccount(req.getOrderUser().getUserAccount());
        }
        req.setChannel(1);//查询渠道
        Response<PaginationSupportDTO<OrderSimpleDTO>> response = hotelOrderRemote.queryOrderList(req);
        // 转换错误编码，把no转换成code
        if (response != null && StrUtilX.isNotEmpty(response.getFailCode())) {
            String codeByNo = DhubReturnCodeEnum.getCodeByNo(response.getFailCode());
            if (StrUtilX.isNotEmpty(codeByNo)) {
                response.setFailCode(codeByNo);
            }
        }
        return response;
    }

    @Override
    public Map<String, Object> orderDetail(OrderDetailReq req) {
        Map<String, Object> map = new HashMap<>();
        // TODO 需要组装页面上没有的数据
        OrderDetailRequest request = new OrderDetailRequest();
        request.setFcOrderCode(req.getOrderCode());
        request.setLanguage(req.getLanguage());
        req.setRequestId(UUID.randomUUID().toString());
        request.setRequestId(req.getRequestId());
        try {
            OrderDTO orderDTO = hotelOrderRemote.queryB2BOrderDetail(request);
            map.put("orderDetail", orderDTO);
            map.put("orderStatus", orderDTO.getOrderConfirmationStatus());
            map.put("countryCode", orderDTO.getCountryCode());
        } catch (Exception e) {
            log.error("查询订单详情异常", e);
            throw new SysException(ErrorEnum.QUERY_ORDER_IS_ERROR.type, ErrorEnum.QUERY_ORDER_IS_ERROR.content);
        }

        //获取酒店政策
        HotelInfoCollectionReq infoReq = new HotelInfoCollectionReq();
        infoReq.setHotelIds(Collections.singletonList(req.getHotelId()));
        infoReq.setLanguageType(req.getLanguage());

        List<String> settings = new ArrayList<>();
        settings.add(BaseHotelInfoUrl.hotelTextPolicy);
        settings.add(BaseHotelInfoUrl.hotelName);
        settings.add(BaseHotelInfoUrl.tel);
        settings.add(BaseHotelInfoUrl.location);
        infoReq.setSettings(settings);
        Response<HotelInfoCollectionDTO> hotelDetailResp = hotelProductRemote.queryHotelInfo(infoReq);
        if (hotelDetailResp.isSuccess() && hotelDetailResp.getModel() != null) {
            HotelInfoCollectionDTO hotelInfo = hotelDetailResp.getModel();
            map.put("hotelPolicy", hotelInfo.getHotelTextPolicyList());
            map.put("hotelTel", hotelInfo.getTel());
            map.put("hotelName", hotelInfo.getHotelName());
            map.put("hotelAddress", hotelInfo.getLocation().getAddress());
            map.put("hotelLocalAddress", hotelInfo.getLocation().getLocalAddress());
            map.put("countryName", hotelInfo.getLocation().getCountryName());
            map.put("cityName", hotelInfo.getLocation().getCityName());
            int supplyType = 2;
            if ("CN".equals(hotelInfo.getLocation().getCountry())) {
                supplyType = 1;
            }
            map.put("supplyType", supplyType);
        }
        return map;
    }

    @Override
    public void cancerOrder(CancelOrderReq req) {
        CancelOrderRequest request = new CancelOrderRequest();
        request.setFcOrderCode(req.getOrderCode());
        request.setChannelCode(ChannelEnum.B2B.key);
        req.setRequestId(UUID.randomUUID().toString());
        request.setRequestId(req.getRequestId());
        ResponseResult<CancelOrderResponse> cancelOrderResponse = hotelOrderRemote.cancelOrder(request);
        //只判断失败的时候
        if (cancelOrderResponse == null || !cancelOrderResponse.getReturnCode().equals("000")) {
            if (cancelOrderResponse != null && StrUtilX.isNotEmpty(cancelOrderResponse.getReturnCode())) {
                // 如果编码为数字，需要转换编码
                if (StrUtilX.isNumeric(cancelOrderResponse.getReturnCode())) {
                    String codeByNo = DhubReturnCodeEnum.getCodeByNo(cancelOrderResponse.getReturnCode());
                    if (StrUtilX.isNotEmpty(codeByNo)) {
                        cancelOrderResponse.setReturnCode(codeByNo);
                    }
                }
                throw new SysException(cancelOrderResponse.getReturnCode(), cancelOrderResponse.getReturnMsg());
            }
            throw new SysException(ErrorEnum.CANCEL_ORDER_ERROR.type, ErrorEnum.CANCEL_ORDER_ERROR.content);
        }
    }

    @Override
    public void operateList(OrderCodeReq req) {
//        OrderIdDTO dto = new OrderIdDTO();
//        dto.setOrderId(req.getOrderId());
//        return orderRemote.queryOrderLog(dto);
    }

    @Override
    public Map<String, Object> orderVoucher(OrderDetailReq req, CompanyAddDTO companyAddDTO) {
        if (req.getHotelId() == null) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
        //客服电话
        String customerTel = null;
        Response<CompanySelectDTO> companyResp = companyRemote.queryCompanyDetail(companyAddDTO);
        if (companyResp != null && companyResp.getModel() != null) {
            customerTel = companyResp.getModel().getCustomerTel();
        }
        Map<String, Object> map = orderDetail(req);
        JSONObject hotelDetail = new JSONObject();
        hotelDetail.put("hotelPolicy", map.get("hotelPolicy"));
        hotelDetail.put("hotelTel", map.get("hotelTel"));
        hotelDetail.put("hotelAddress", map.get("hotelAddress"));
        hotelDetail.put("hotelLocalAddress", map.get("hotelLocalAddress"));
        hotelDetail.put("hotelName", map.get("hotelName"));
        hotelDetail.put("supplyType", map.get("supplyType"));
        hotelDetail.put("countryName", map.get("countryName"));
        hotelDetail.put("cityName", map.get("cityName"));
        map.put("customerTel", customerTel);
        map.put("hotelDetail", hotelDetail);
        //重新组装酒店政策为行程须知
        //入住凭证展示内容：
        //1、code：instructions；codeName：入住说明；
        //2、code：specialInstructions；codeName：特别入住说明
        //3、code：mandatoryFees；codeName：费用说明；
        //4、code：optionalFees；codeName：自选费用
        JSONArray hotelTextPolicy = hotelDetail.getJSONArray("hotelPolicy");
        if (hotelTextPolicy == null || hotelTextPolicy.isEmpty()) {
            map.put("hotelPolicy", new JSONArray());
        } else {
            Map<String, JSONObject> policyMap = hotelTextPolicy.toJavaList(JSONObject.class).stream().collect(Collectors.toMap(o -> o.getString("code"), t -> (t), (s1, s2) -> s2));

            JSONArray hotelPolicy = new JSONArray();
            //String travelNoticePolicy = travelNoticePolicy;
            SysConfigReq sysConfigReq = new SysConfigReq();
            sysConfigReq.setStrKey("travelNoticePolicy");
            Response<SysConfigDTO> travelNoticePolicy = sysConfigRemote.querySysConfigByKey(sysConfigReq);
            System.out.println("查询政策数据是：" + travelNoticePolicy);
            String[] policyList = {};
            if (travelNoticePolicy.isSuccess()) {
                if (travelNoticePolicy.getModel() != null) {
                    policyList = travelNoticePolicy.getModel().getStrValue().split(",");
                }
            }
            for (String s : policyList) {
                JSONObject object = policyMap.get(s);
                if (object == null) continue;
                if (req.getLanguage().equals(LanguageTypeEnum.en_US.getValue())) {
                    getPolicyEn(object);
                }
                hotelPolicy.add(object);
            }
            map.put("hotelPolicy", hotelPolicy);
        }
        return map;
    }

    public void getPolicyEn(JSONObject object) {
        if (object == null || StringUtils.isEmpty(object.getString("codeName"))) {
            return;
        }
        if (object.getString("codeName").equals("入住离店")) {
            object.put("codeName", "Arrival/Leave（Local time");
        }
        if (object.getString("codeName").equals("年龄限制")) {
            object.put("codeName", "Age Limits");
        }
        if (object.getString("codeName").equals("入住说明")) {
            object.put("codeName", "Check-in Limits");
        }
        if (object.getString("codeName").equals("特别入住说明")) {
            object.put("codeName", "Special check-in Limits");
        }
        if (object.getString("codeName").equals("酒店政策")) {
            object.put("codeName", "Property Policies");
        }
        if (object.getString("codeName").equals("费用说明")) {
            object.put("codeName", "Fees Instuction");
        }
        if (object.getString("codeName").equals("自选收费")) {
            object.put("codeName", "Optional extras");
        }
        if (object.getString("codeName").equals("清洁和安全措施")) {
            object.put("codeName", "Cleaning and safety");
        }
        if (object.getString("codeName").equals("订房必读")) {
            object.put("codeName", "Notification");
        }
        if (object.getString("codeName").equals("儿童政策")) {
            object.put("codeName", "Children Policy");
        }
        if (object.getString("codeName").equals("儿童及加床")) {
            object.put("codeName", "Child Fee & Extra Bed");
        }
        if (object.getString("codeName").equals("早餐")) {
            object.put("codeName", "Breakfast");
        }
        if (object.getString("codeName").equals("宠物")) {
            object.put("codeName", "Pet Policy");
        }
        if (object.getString("codeName").equals("停车场")) {
            object.put("codeName", "Parking");
        }
        if (object.getString("codeName").equals("充电车位")) {
            object.put("codeName", "Parking for EV charger");
        }
        if (object.getString("codeName").equals("酒店前台可用支付方式")) {
            object.put("codeName", "Payment on Front Office");
        }
    }

    @Override
    public String downloadOrderVoucher(OrderCodeReq req, CompanyAddDTO companyAddDTO) {
        //判断是否已经生成了pdf文件
//        QueryVoucherDTO voucher = orderMapper.queryVoucher(req.getOrderCode());
//        if (voucher != null && StrUtilX.isNotEmpty(voucher.getVoucherUrl())) {
//            return Response.success(voucher.getVoucherUrl());
//        }
        //生成pdf 并返回url
        return syncCreateVoucher(req, null, 0, companyAddDTO);
    }

    @Override
    public void sendOrderVoucher(OrderCodeReq req, CompanyAddDTO companyAddDTO) {
        //判断是否已经生成了pdf文件
        QueryVoucherDTO voucher = orderMapper.queryVoucher(req.getOrderCode());
        if (voucher != null) {
            //异步发送邮件
            defaultPool.execute(() -> {
                syncCreateVoucher(req, voucher, 1, companyAddDTO);
            });
        }
    }

    /**
     * 生成pdf文件
     *
     * @param req     请求参数
     * @param voucher 凭证信息
     * @param type    是否需要发送邮件 0否 1是
     */
    public String syncCreateVoucher(OrderCodeReq req, QueryVoucherDTO voucher, int type, CompanyAddDTO companyAddDTO) {
        // 没有生成pdf文件，则现在生成
        String voucherUrl = createVoucher(req, companyAddDTO);
        if (voucherUrl == null) {
            throw new RuntimeException("生成pdf文件异常");
        }
        // 更新订单凭据
        orderMapper.updateVoucher(req.getOrderCode(), voucherUrl);
        // 发送订单凭证到邮箱
        if (type == 1) {
            sendCodeUtil.sendQQEmailToFile(voucher.getContactEmail(), voucherUrl, req.getOrderCode(), req.getLanguage(), settingsConstant.getB2bDomain());
        }
        RedisTemplateX.setAndExpire(RedisKey.H5_VOUCHERURL + req.getOrderCode(), voucherUrl, 60 * 5);
        return voucherUrl;
    }

    @Override
    public String getVoucherUrl(OrderCodeReq req) {
        String voucher = null;
        if (RedisTemplateX.hasKey(RedisKey.H5_VOUCHERURL + req.getOrderCode())) {
            voucher = RedisTemplateX.get(RedisKey.H5_VOUCHERURL + req.getOrderCode());
            RedisTemplateX.delete(RedisKey.H5_VOUCHERURL + req.getOrderCode());
        }
        return voucher;
    }

    @Override
    public Response<OrderStatisticsDTOB2BResponse> queryOrderStatisticsB2B(QueryOrderStatisticsDTOB2BRequest request) {
        request.setCompanyCode(CompanyDTO.COMPANY_CODE);
        if (request.getOrderTypeList() == 1) {
            request.setUserAccount(null);
        }
        if (request.getOrderUser() != null) {
            request.setUserAccount(request.getOrderUser().getUserAccount());
        }
        Response<OrderStatisticsDTOB2BResponse> response = orderQueryRemote.queryOrderStatisticsB2B(request);
        // 转换错误编码，把no转换成code
        if (response != null && StrUtilX.isNotEmpty(response.getFailCode())) {
            String codeByNo = DhubReturnCodeEnum.getCodeByNo(response.getFailCode());
            if (StrUtilX.isNotEmpty(codeByNo)) {
                response.setFailCode(codeByNo);
            }
        }
        return response;
    }

    public String createVoucher(OrderCodeReq req, CompanyAddDTO companyAddDTO) {
        OrderDetailReq detailReq = new OrderDetailReq();
        detailReq.setHotelId(req.getHotelId());
        detailReq.setLanguage(req.getLanguage());
        detailReq.setOrderCode(req.getOrderCode());
        //获取数据
        Map<String, Object> map = orderVoucher(detailReq, companyAddDTO);
        if (map == null) {
            return null;
        }
        map.put("language", req.getLanguage());
        return HttpUtil.post(settingsConstant.getCreateVoucherUrl(), JSONUtil.toJsonStr(map));
    }

    /**
     * 获取取整方式
     */
    private RoundingMode getRoundingType(int roundingType) {
        switch (roundingType) {
            case 1:
                // 向上取整
                return RoundingMode.CEILING;
            case 2:
                // 向下取整
                return RoundingMode.FLOOR;
            case 3:
                // 四舍五入
                return RoundingMode.HALF_UP;
            default:
                return RoundingMode.CEILING;
        }
    }
}
