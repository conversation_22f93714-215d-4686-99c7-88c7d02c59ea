package com.tiangong.h5.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.tiangong.cloud.common.utils.JsonUtils;
import com.tiangong.common.Response;
import com.tiangong.dis.common.CommonUtils;
import com.tiangong.dis.dto.HotelInfoReq;
import com.tiangong.dis.dto.ResponseResult;
import com.tiangong.dis.entity.vo.hotel.HotelListPageResponse;
import com.tiangong.dis.enums.DhubReturnCodeEnum;
import com.tiangong.dis.remote.HotelProductRemote;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.dto.common.ResultX;
import com.tiangong.dto.hotel.*;
import com.tiangong.dto.hotel.base.*;
import com.tiangong.dto.product.PriceItem;
import com.tiangong.dto.product.RoomItemDetail;
import com.tiangong.dto.product.request.HotelImageRequest;
import com.tiangong.dto.product.request.HotelLowestPriceRequest;
import com.tiangong.dto.product.request.ProductDetailRequest;
import com.tiangong.dto.product.request.RoomGuestNumber;
import com.tiangong.dto.product.response.*;
import com.tiangong.enums.CanBookEnum;
import com.tiangong.enums.ErrorCodeEnum;
import com.tiangong.enums.ParamErrorEnum;
import com.tiangong.exception.CustomException;
import com.tiangong.exception.SysException;
import com.tiangong.h5.config.SettingsConstant;
import com.tiangong.h5.convert.DTOConvert;
import com.tiangong.h5.domain.req.CommonReqDTO;
import com.tiangong.h5.domain.req.HotelIdsToPriceReq;
import com.tiangong.h5.domain.req.HotelListPageRequest;
import com.tiangong.h5.domain.resp.MeetingImage;
import com.tiangong.h5.enums.ErrorEnum;
import com.tiangong.h5.enums.FacilityEnum;
import com.tiangong.h5.mapper.OrganizationMapper;
import com.tiangong.h5.service.HotelService;
import com.tiangong.hotel.BaseHotelInfoUrl;
import com.tiangong.hotel.remote.HotelRemote;
import com.tiangong.keys.RedisKey;
import com.tiangong.organization.remote.dto.AgentAccountConfig;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.util.CollUtilX;
import com.tiangong.util.DateUtilX;
import com.tiangong.util.HttpUtilX;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class HotelServiceImpl implements HotelService {

    @Autowired
    private HotelProductRemote hotelProductRemote;

    @Resource
    private OrganizationMapper organizationMapper;

    @Autowired
    private SettingsConstant settingsConstant;

    @Autowired
    private HotelRemote hotelRemote;

    @Override
    public Response<HotelListPageResponse> queryHotelBaseList(HotelListPageRequest request) {
        if (StrUtilX.isEmpty(request.getAgentCode())) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_AGENTCODE);
        }
        HotelListPageResponse response = new HotelListPageResponse();
        //价格校验
        if (request.getPriceBegin() != null && request.getPriceEnd() == null) {
            throw new SysException(ErrorCodeEnum.PRICE_END_IS_NOT_EMPTY);
        } else if (request.getPriceEnd() != null && request.getPriceBegin() == null) {
            throw new SysException(ErrorCodeEnum.PRICE_BEGIN_IS_NOT_EMPTY);
        } else if (request.getPriceBegin() != null && request.getPriceBegin() >= request.getPriceEnd()) {
            throw new SysException(ErrorCodeEnum.PRICE_BEGIN_LOWEST_PRICE_END);
        } else if (request.getPriceBegin() != null && request.getPriceBegin() <= 0) {
            throw new SysException(ErrorCodeEnum.PRICE_BEGIN_GREATER_ZERO);
        }
        if (request.getSortBy() < 1 || request.getSortBy() > 7) {
            throw new SysException(ErrorCodeEnum.SORT_BY_NOT_EXIST);
        }

        // 校验设施
        if (CollUtilX.isNotEmpty(request.getHotelFacilityCodes())) {
            for (String code : request.getHotelFacilityCodes()) {
                String facility = FacilityEnum.checkHotelFacility(code);
                if (StringUtils.isEmpty(facility)) {
                    throw new SysException(DhubReturnCodeEnum.ILLEGAL_HOTEL_FACILITY_CODE.code, DhubReturnCodeEnum.ILLEGAL_HOTEL_FACILITY_CODE.description);
                }
            }
        }

        if (CollUtilX.isNotEmpty(request.getRoomFacilityCodes())) {
            for (String code : request.getRoomFacilityCodes()) {
                String facility = FacilityEnum.checkRoomFacility(code);
                if (StringUtils.isEmpty(facility)) {
                    throw new SysException(DhubReturnCodeEnum.ILLEGAL_ROOM_FACILITY_CODE.code, DhubReturnCodeEnum.ILLEGAL_ROOM_FACILITY_CODE.description);
                }
            }
        }

        HotelPageReq req = DTOConvert.INSTANCE.hotelListPageConvert(request);
        Response<PaginationSupportDTO<HotelListResp>> result = hotelProductRemote.findHotelList(req);
        if (result.isSuccess()) {
            PaginationSupportDTO<HotelListResp> model = result.getModel();
            // 组装酒店主图信息
            convertHotelMainUrl(model.getItemList(), request.getLanguage());
            response.setList(model.getItemList());
            response.setPageSize(model.getPageSize());
            response.setCurrentPage(model.getCurrentPage());
            response.setTotalPage(model.getTotalPage());
            response.setTotalCount(model.getTotalCount());
        } else {
            log.error("获取酒店列表异常--- request: {} ,response：{}", JSON.toJSONString(req), JSON.toJSONString(result));
            throw new SysException(result.getFailCode(), result.getFailReason());
        }
        return Response.success(response);
    }

    /**
     * 组装酒店主图信息
     */
    private void convertHotelMainUrl(List<HotelListResp> itemList, String language) {
        // 获取主图为空的酒店
        List<Long> hotelIds = itemList.stream()
                .filter(hotelListResp -> StrUtilX.isEmpty(hotelListResp.getMainUrl()))
                .map(HotelListResp::getHotelId)
                .collect(Collectors.toList());
        if (CollUtilX.isEmpty(hotelIds)){
            return;
        }

        // 查询酒店图片
        HotelImageRequest hotelImageRequest = new HotelImageRequest();
        hotelImageRequest.setHotelIds(hotelIds);
        hotelImageRequest.setLanguage(language);
        com.tiangong.h5.domain.resp.HotelImageResponse hotelImageResponse = queryHotelImage(hotelImageRequest);
        if(hotelImageResponse == null || CollUtilX.isEmpty(hotelImageResponse.getHotelImages())){
            return;
        }
        // 获取酒店图片
        Map<Long, com.tiangong.h5.domain.resp.HotelImage> hotelImageMap = hotelImageResponse.getHotelImages().stream().collect(Collectors.toMap(com.tiangong.h5.domain.resp.HotelImage::getHotelId, hotelImage -> hotelImage));
        for (HotelListResp hotelListResp : itemList) {
            // 设置酒店主图 优先级 外景图>大堂图>其他类型图片
            if (hotelImageMap.containsKey(hotelListResp.getHotelId())){
                com.tiangong.h5.domain.resp.HotelImage hotelImage = hotelImageMap.get(hotelListResp.getHotelId());
                String mainUrl = ""; // 主图
                String otherUrl = "";
                if (CollUtilX.isNotEmpty(hotelImage.getImages())){
                    for (com.tiangong.h5.domain.resp.Image image : hotelImage.getImages()) {
                        if (StrUtilX.isEmpty(image.getUrl())){
                            continue;
                        }
                        // 外景图优先
                        if (image.getImageType() == 2){
                            mainUrl = image.getUrl();
                            break;
                        }
                        // 大堂图
                        if (image.getImageType() == 3){
                            otherUrl = image.getUrl();
                            mainUrl = image.getUrl();
                        } else if (StrUtilX.isEmpty(otherUrl)) {
                            // 其他类型图片
                            mainUrl = image.getUrl();
                        }
                    }
                }
                // 主图为空时，取房型图作为酒店主图
                if(StrUtilX.isEmpty(mainUrl) && CollUtilX.isNotEmpty(hotelImage.getRoomImages())){
                    for (com.tiangong.h5.domain.resp.RoomImage roomImage : hotelImage.getRoomImages()) {
                        if (CollUtilX.isEmpty(roomImage.getImages())){
                            continue;
                        }
                        mainUrl = roomImage.getImages().get(0).getUrl();
                        break;
                    }
                }
                //主图为空时，取会议室图作为酒店主图
                if(StrUtilX.isEmpty(mainUrl) && CollUtilX.isNotEmpty(hotelImage.getMeetingImages())){
                    for (MeetingImage meetingImage : hotelImage.getMeetingImages()) {
                        if (CollUtilX.isEmpty(meetingImage.getImages())){
                            continue;
                        }
                        mainUrl = meetingImage.getImages().get(0).getUrl();
                        break;
                    }
                }
                hotelListResp.setMainUrl(mainUrl);
            }
        }
    }

    private com.tiangong.h5.domain.resp.HotelImageResponse queryHotelImage(HotelImageRequest request) {
        com.tiangong.h5.domain.resp.HotelImageResponse hotelImageResponse = new com.tiangong.h5.domain.resp.HotelImageResponse();
        CommonReqDTO commonReqDTO = new CommonReqDTO();
        commonReqDTO.setHotelIds(request.getHotelIds());
        commonReqDTO.setLanguageType(request.getLanguage());
        String s = HttpUtilX.post(settingsConstant.getBaseHotelUrl() + "/manage/external/queryHotelImage", JSON.toJSONString(commonReqDTO));
        if (StrUtilX.isEmpty(s)) {
            log.error("酒店图片信息查询失败,未返回数据");
            throw new CustomException(DhubReturnCodeEnum.INTERNAL_TIMEOUT.no, DhubReturnCodeEnum.INTERNAL_TIMEOUT.code, DhubReturnCodeEnum.INTERNAL_TIMEOUT.code);
        }
        ResultX<List<HotelImageDTO>> listResultX = JSON.parseObject(s, new TypeReference<ResultX<List<HotelImageDTO>>>() {
        });
        if (listResultX.isError()) {
            log.error("酒店图片信息查询失败,失败原因:" + s);
            throw new CustomException("999", listResultX.getCode(), listResultX.getCode());
        }
        List<HotelImageDTO> hotelImageDTOS = listResultX.getData();
        if (CollUtilX.isEmpty(hotelImageDTOS)) {
            return hotelImageResponse;
        }
        List<com.tiangong.h5.domain.resp.HotelImage> hotelImages = new ArrayList<>();
        com.tiangong.h5.domain.resp.HotelImage hotelImage;
        for (HotelImageDTO hotelImageDTO : hotelImageDTOS) {
            hotelImage = new com.tiangong.h5.domain.resp.HotelImage();
            hotelImage.setHotelId(hotelImageDTO.getHotelId());
            convertHotelImage(hotelImage, hotelImageDTO);
            hotelImages.add(hotelImage);
        }
        hotelImageResponse.setHotelImages(hotelImages);
        return hotelImageResponse;
    }

    private void convertHotelImage(com.tiangong.h5.domain.resp.HotelImage hotelImage, HotelImageDTO hotelImageDTO) {
        if (CollUtilX.isNotEmpty(hotelImageDTO.getImages())) {
            List<com.tiangong.h5.domain.resp.Image> images = new ArrayList<>();
            com.tiangong.h5.domain.resp.Image image;
            for (ImageDTO imageDTO : hotelImageDTO.getImages()) {
                image = new com.tiangong.h5.domain.resp.Image();
                convertImage(image, imageDTO, 1);
                images.add(image);
            }
            hotelImage.setImages(images);
        }
        //房型图片
        if (CollUtilX.isNotEmpty(hotelImageDTO.getRoomImages())) {
            List<com.tiangong.h5.domain.resp.RoomImage> roomImages = new ArrayList<>();
            com.tiangong.h5.domain.resp.RoomImage roomImage;
            for (RoomImages roomImageInfo : hotelImageDTO.getRoomImages()) {
                roomImage = new com.tiangong.h5.domain.resp.RoomImage();
                roomImage.setRoomId(roomImageInfo.getRoomId());
                if (CollUtilX.isEmpty(roomImageInfo.getImages())) {
                    continue;
                }
                List<com.tiangong.h5.domain.resp.Image> images = new ArrayList<>();
                com.tiangong.h5.domain.resp.Image image;
                for (ImageDTO imageDTO : roomImageInfo.getImages()) {
                    image = new com.tiangong.h5.domain.resp.Image();
                    convertImage(image, imageDTO, null);
                    images.add(image);
                }
                roomImage.setImages(images);
                roomImages.add(roomImage);
            }
            hotelImage.setRoomImages(roomImages);
        }
        //视频图片
        if (CollUtilX.isNotEmpty(hotelImageDTO.getMeetingImages())) {
            List<MeetingImage> meetingImages = new ArrayList<>();
            MeetingImage meetingImage;
            for (MeetingImages meetingImagesInfo : hotelImageDTO.getMeetingImages()) {
                meetingImage = new MeetingImage();
                meetingImage.setMeetingId(meetingImagesInfo.getMeetingId());
                List<com.tiangong.h5.domain.resp.Image> images = new ArrayList<>();
                com.tiangong.h5.domain.resp.Image image;
                for (ImageDTO imageDTO : meetingImagesInfo.getImages()) {
                    image = new com.tiangong.h5.domain.resp.Image();
                    convertImage(image, imageDTO, null);
                    images.add(image);
                }
                meetingImage.setImages(images);
                meetingImages.add(meetingImage);
            }
            hotelImage.setMeetingImages(meetingImages);
        }
    }

    private void convertImage(com.tiangong.h5.domain.resp.Image image, ImageDTO imageDTO, Integer hotelMainType) {
        image.setImageId(imageDTO.getImgId());
        image.setImageType(imageDTO.getImgType());
        //酒店主图
        if (hotelMainType != null && hotelMainType == 1) {
            if (StrUtilX.isNotEmpty(imageDTO.getIsMainImg())) {
                image.setIsMain(Integer.valueOf(imageDTO.getIsMainImg()));
            }
        } else {
            if (StrUtilX.isNotEmpty(imageDTO.getIsRoomMainImg())) {
                image.setIsMain(Integer.valueOf(imageDTO.getIsRoomMainImg()));
            }
        }
        image.setUrl(imageDTO.getImgUrl());
    }

    @Override
    public Response<List<AddressRespDTO>> getAddress(Map<String, String> map) {
        if (StrUtilX.isEmpty(map.get("keyWord"))) {
            throw new SysException(ErrorCodeEnum.KEY_WORD_NOT_EMPTY);
        }
        Response<List<AddressRespDTO>> response = hotelProductRemote.getAddress(map);
        // 过滤数据
        if (CollUtilX.isNotEmpty(response.getModel())) {
            List<AddressRespDTO> collect = response.getModel().stream().filter(item ->
                    item.getDataType() != null && (item.getDataType() == 1 || item.getDataType() == 2)).collect(Collectors.toList());
            response.setModel(collect);
        }
        return response;
    }

    @Override
    public Response<List<EsHotelDto>> searchDestinationHotel(DestinationReq req) {
        return hotelRemote.searchDestinationHotel(req);
    }

    @Override
    public Response<List<JSONObject>> queryHotelBaseProducts(ProductDetailRequest request) {
        // 校验入离时间(90天报价输出开关校验)
        if (StrUtilX.isNotEmpty(request.getCityCode())
                && StrUtilX.isNotEmpty(request.getCheckInDate())
                && StrUtilX.isNotEmpty(request.getCheckOutDate())) {
            Integer quoteSwitch = organizationMapper.getQuoteSwitch(request.getAgentCode());
            if (quoteSwitch != null && quoteSwitch == 1) {
                CommonUtils.checkDate90(request.getCheckInDate(), request.getCheckOutDate());
            } else {
                CommonUtils.checkDateDefault(request.getCheckInDate(), request.getCheckOutDate());
            }
        } else {
            CommonUtils.checkDateDefault(request.getCheckInDate(), request.getCheckOutDate());
        }

        // 重新获取国内全球标识
        AgentAccountConfig agentAccountConfig = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.AGENT_ACCOUNT_CONFIG, request.getAgentCode()), AgentAccountConfig.class);
        if (agentAccountConfig != null) {
            request.setSupplyType(agentAccountConfig.getDomesticOrOverseas() + "");
        }
        request.setRequestId(UUID.randomUUID().toString());
        ResponseResult<ProductDetailResponse> responseResult = hotelProductRemote.queryProductDetail(request);
        if (responseResult == null || !"000".equals(responseResult.getReturnCode())) {
            return Response.success(null);
        }
        ProductDetailResponse productDetailResponse = responseResult.getBussinessResponse();
        // 组装房型实体
        Map<Long, RoomItem> roomItemMap = new HashMap<>();
        if (productDetailResponse.getRoomItems() != null && !productDetailResponse.getRoomItems().isEmpty()) {
            roomItemMap = productDetailResponse.getRoomItems().stream().collect(Collectors.toMap(RoomItem::getRoomId, Function.identity(), (s1, s2) -> s2));
        }
        Map<Long, RoomItem> hourlyRoomItems = new HashMap<>();
        if (productDetailResponse.getHourlyRoomItems() != null && !productDetailResponse.getHourlyRoomItems().isEmpty()) {
            hourlyRoomItems = productDetailResponse.getHourlyRoomItems().stream().collect(Collectors.toMap(RoomItem::getRoomId, Function.identity(), (s1, s2) -> s2));
        }
        if (roomItemMap.isEmpty() && hourlyRoomItems.isEmpty()) {
            return Response.success(null);
        }
        // 计算入住天数
        Date checkInDate = DateUtilX.stringToDate(request.getCheckInDate());
        Date checkOutDate = DateUtilX.stringToDate(request.getCheckOutDate());
        long dayNum = DateUtilX.getDay(checkInDate, checkOutDate);
        Integer roomNum = request.getRoomNum();
        // 获取最大最小人数
        Integer maxGuestNumber = null;
        Integer minGuestNumber = null;

        // 连住天数
        long day = DateUtilX.getDay(checkInDate, DateUtilX.stringToDate(request.getCheckOutDate()));

        if (CollUtilX.isNotEmpty(request.getRoomGuestNumbers())) {
            for (RoomGuestNumber roomGuestNumber : request.getRoomGuestNumbers()) {
                Integer tempGuestNumber = roomGuestNumber.getAdultNum();
                if (CollUtilX.isNotEmpty(roomGuestNumber.getChildrenInfos())) {
                    tempGuestNumber += roomGuestNumber.getChildrenInfos().size();
                }
                if (maxGuestNumber == null || tempGuestNumber > maxGuestNumber) {
                    maxGuestNumber = tempGuestNumber;
                }
                if (minGuestNumber == null || tempGuestNumber < minGuestNumber) {
                    minGuestNumber = tempGuestNumber;
                }
            }
        }

        // 获取酒店基本信息
        HotelInfoCollectionReq infoReq = new HotelInfoCollectionReq();
        infoReq.setHotelIds(Collections.singletonList(request.getHotelId()));
        infoReq.setLanguageType(request.getLanguage());

        List<String> settings = new ArrayList<>();
        settings.add(BaseHotelInfoUrl.images);
        settings.add(BaseHotelInfoUrl.facilities);
        settings.add(BaseHotelInfoUrl.rooms);
        settings.add(BaseHotelInfoUrl.location);
        infoReq.setSettings(settings);
        Response<HotelInfoCollectionDTO> hotelDetailResp = hotelProductRemote.queryHotelInfo(infoReq);
        if (hotelDetailResp.isSuccess() && hotelDetailResp.getModel() != null) {
            HotelInfoCollectionDTO hotelInfo = hotelDetailResp.getModel();
            // 获取房型图片
            List<ImageDTO> images = hotelInfo.getImages();
            // 临时存放房型图片
            Map<Long, List<ImageDTO>> roomImageMap = new HashMap<>();
            if (CollUtilX.isNotEmpty(images)) {
                for (ImageDTO image : images) {
                    if (image.getImgType() != null && image.getImgType() == 1) {
                        List<ImageDTO> imageList = roomImageMap.get(image.getRoomtypeId());
                        if (imageList == null) {
                            imageList = new ArrayList<>();
                            imageList.add(image);
                        } else {
                            imageList.add(image);
                        }
                        roomImageMap.put(image.getRoomtypeId(), imageList);
                    }
                }
            }

            // 获取房型设施
            List<FacilityPublicDTO> facilities = hotelInfo.getFacilities();
            Map<Long, List<RoomFacilityDTO>> roomFacilitiesMap = new HashMap<>();
            Map<String, String> roomFacilitiesArray = new HashMap<>();
            if (facilities != null && !facilities.isEmpty()) {
                for (FacilityPublicDTO facility : facilities) {
                    if (facility.getFacilityType() != null && facility.getFacilityType() == 2) {
                        // 房型设施数据组装
                        String dataName = facility.getDataName();
                        String facilityName = facility.getFacilityName();
                        String value = roomFacilitiesArray.get(dataName);
                        if (StringUtils.isEmpty(value)) {
                            roomFacilitiesArray.put(dataName, facilityName);
                        } else {
                            roomFacilitiesArray.put(dataName, value + " | " + facilityName);
                        }
                        // 判断房型里面有专属房型的设施没有
                        List<RoomFacilityDTO> roomFacilityList = facility.getRoomFacilityList();
                        if (CollUtilX.isEmpty(roomFacilityList)) {
                            continue;
                        }
                        // 有的话，则进行封装
                        for (RoomFacilityDTO roomFacilityDTO : roomFacilityList) {
                            Long roomtypeId = roomFacilityDTO.getRoomtypeId();
                            if (roomtypeId != null) {
                                List<RoomFacilityDTO> roomFacilitys = roomFacilitiesMap.get(roomtypeId);
                                if (roomFacilitys == null) {
                                    roomFacilitys = new ArrayList<>();
                                    roomFacilitys.add(roomFacilityDTO);
                                    roomFacilitiesMap.put(roomtypeId, roomFacilitys);
                                } else {
                                    roomFacilitys.add(roomFacilityDTO);
                                }
                            }
                        }
                    }
                }
            }

            // 获取房型其他信息
            List<RoomtypeDTO> rooms = hotelInfo.getRooms();
            Map<Long, RoomtypeDTO> basicRoomDetail = new HashMap<>();

            if (rooms != null && !rooms.isEmpty()) {
                basicRoomDetail = rooms.stream().collect(Collectors.toMap(RoomtypeDTO::getRoomtypeId, Function.identity(), (s1, s2) -> s2));
            }

            // 判断是国外还是海内数据
            LocationDTO location = hotelInfo.getLocation();
            int supplyType = 1;
            if (location != null) {
                String country = location.getCountry();
                if (!"CN".equals(country)) {
                    supplyType = 2;
                }
            }

            // 组装每个房型里面的数据
            JSONArray detailArray = new JSONArray();
            for (Long roomId : roomItemMap.keySet()) {
                RoomItem roomItem = roomItemMap.get(roomId);
                Long roomtypeId = roomItem.getRoomId();
                JSONObject roomDetail;
                RoomtypeDTO roomtypeDTO = basicRoomDetail.get(roomItem.getRoomId());

                // 基础信息房型为空时，返回产品数据
                if (roomtypeDTO == null) {
                    roomDetail = new JSONObject();
                    roomDetail.put("roomtypeName", roomItem.getRoomName());
                    roomDetail.put("description", null);
                    roomDetail.put("facilities", null);
                    roomDetail.put("hotelId", request.getHotelId());
                    roomDetail.put("imgUrl", null);
                    roomDetail.put("isAllowSmoking", null);
                    roomDetail.put("maxChild", null);
                    roomDetail.put("maxGrown", roomItem.getProducts().get(0).getMaxGuestPerson());
                    roomDetail.put("maxPerson", roomItem.getProducts().get(0).getMaxGuestPerson());
                    roomDetail.put("roomArea", null);
                    roomDetail.put("roomCount", null);
                    roomDetail.put("roomFloor", null);
                    roomDetail.put("roomtypeId", roomtypeId);
                    roomDetail.put("status", null);
                    roomDetail.put("windowDetail", null);
                    roomDetail.put("wiredBroadNet", null);
                    roomDetail.put("wirelessBroadNet", null);
                    roomDetail.put("timeZone", productDetailResponse.getTimeZone());
                } else {
                    roomDetail = JsonUtils.jsonToObject(roomtypeDTO, JSONObject.class);
                }

                // 判断是否满足预定条款
                if (CollUtilX.isNotEmpty(roomItem.getProducts())) {
                    for (ProductDetail product : roomItem.getProducts()) {
                        // 人数条款是否满足
                        if (product.getMaxGuestPerson() != null && maxGuestNumber != null && product.getMaxGuestPerson() < maxGuestNumber) {
                            // 不满足最大人数限制
                            product.setBookType(CanBookEnum.CAN_NOT_MEET_WITH_PRE_RESTRICT.value);
                            product.setNotReservationTerms("最大入住人数" + product.getMaxGuestPerson() + "人");
                            continue;
                        }

                        if (product.getMinGuestPerson() != null && maxGuestNumber != null && product.getMinGuestPerson() > minGuestNumber) {
                            // 不满足最少人数限制
                            product.setBookType(CanBookEnum.CAN_NOT_MEET_WITH_PRE_RESTRICT.value);
                            product.setNotReservationTerms("最小入住人数" + product.getMinGuestPerson() + "人");
                            continue;
                        }

                        // 入住条款限制
                        if (CollectionUtil.isNotEmpty(product.getRoomItemDetails())) {
                            outFor:
                            for (RoomItemDetail itemDetail : product.getRoomItemDetails()) {
                                for (PriceItem priceItem : itemDetail.getPriceItems()) {
                                    // 入住间数条款
                                    if (priceItem.getMinRoomCount() != null && priceItem.getMinRoomCount() > roomNum) {
                                        product.setBookType(CanBookEnum.CAN_NOT_MEET_WITH_PRE_RESTRICT.value);
                                        product.setNotReservationTerms("入住间数必须大于" + priceItem.getMinRoomCount() + "间");
                                        break outFor;
                                    }
                                    if (priceItem.getMaxRoomCount() != null && priceItem.getMaxRoomCount() > 0 && priceItem.getMaxRoomCount() < roomNum) {
                                        product.setBookType(CanBookEnum.CAN_NOT_MEET_WITH_PRE_RESTRICT.value);
                                        product.setNotReservationTerms("入住间数必须小于" + priceItem.getMaxRoomCount() + "间");
                                        break outFor;
                                    }

                                    //入住天数条款
                                    if (priceItem.getOccupancyRestrictionType() == null) continue;
                                    if (priceItem.getOccupancyRestrictionDay() == null) continue;

                                    if (priceItem.getOccupancyRestrictionType() == 1) {
                                        //入住天数需>=day天；
                                        if (dayNum < priceItem.getOccupancyRestrictionDay()) {
                                            product.setBookType(CanBookEnum.CAN_NOT_MEET_WITH_PRE_RESTRICT.value);
                                            product.setNotReservationTerms("最小入住天数" + priceItem.getOccupancyRestrictionDay() + "天");
                                            break outFor;
                                        }
                                    } else if (priceItem.getOccupancyRestrictionType() == 2) {
                                        //必须入住day天；
                                        if (dayNum != priceItem.getOccupancyRestrictionDay()) {
                                            product.setBookType(CanBookEnum.CAN_NOT_MEET_WITH_PRE_RESTRICT.value);
                                            product.setNotReservationTerms("入住天数必须为" + priceItem.getOccupancyRestrictionDay() + "天");
                                            break outFor;
                                        }
                                    } else if (priceItem.getOccupancyRestrictionType() == 3) {
                                        //入住天数需<=Day天。
                                        if (dayNum > priceItem.getOccupancyRestrictionDay()) {
                                            product.setBookType(CanBookEnum.CAN_NOT_MEET_WITH_PRE_RESTRICT.value);
                                            product.setNotReservationTerms("入住天数必须" + priceItem.getOccupancyRestrictionDay() + "天");
                                            break outFor;
                                        }
                                    }
                                }
                            }
                        }

                        if (CollUtilX.isNotEmpty(product.getRoomItemDetails()) && CollUtilX.isNotEmpty(product.getRoomItemDetails().get(0).getPriceItems())) {
                            //海外数据 每间每晚
                            PriceItem priceItem = product.getRoomItemDetails().get(0).getPriceItems().get(0);
                            String notReservationTerms = checkRestriction(priceItem, (int) day, checkInDate);
                            if (StrUtilX.isNotEmpty(notReservationTerms)) {
                                product.setBookType(CanBookEnum.CAN_NOT_MEET_WITH_PRE_RESTRICT.value);
                            }
                        }

                        if (CollUtilX.isNotEmpty(product.getPriceItems())) {
                            //取首日进行对比
                            PriceItem priceItem = product.getPriceItems().get(0);
                            String notReservationTerms = checkRestriction(priceItem, (int) day, checkInDate);
                            if (StrUtilX.isNotEmpty(notReservationTerms)) {
                                product.setBookType(CanBookEnum.CAN_NOT_MEET_WITH_PRE_RESTRICT.value);
                                product.setNotReservationTerms(notReservationTerms);
                            }
                        }
                    }
                }
                RoomItem hourlyRoomItem = hourlyRoomItems.get(roomtypeId);
                if (supplyType == 2) {
                    roomDetail.put("facilities", setRoomFacilities(facilities));
                } else {
                    roomDetail.put("facilities", roomFacilitiesArray);
                }
                // 过滤不可订房型
                List<ProductDetail> products = roomItem.getProducts();
                if (products != null && !products.isEmpty()) {
                    // 过滤不可订产品
                    List<ProductDetail> list = products.stream().filter(o -> o.getBookType() == 1).collect(Collectors.toList());

                    // 过滤打包价或者只查询打包价
                    if (request.getPackageRates() != null && request.getPackageRates() == 1) {
                        // 只查询打包价
                        list = list.stream().filter(o -> o.getSupplierLabel() != null && o.getSupplierLabel() == 6).collect(Collectors.toList());
                    } else {
                        // 不查询打包价
                        list = list.stream().filter(o -> o.getSupplierLabel() == null || o.getSupplierLabel() != 6).collect(Collectors.toList());
                    }
                    roomItem.setProducts(list);
                }

                // 凌晨房
                if (!Objects.isNull(hourlyRoomItem)) {
                    List<ProductDetail> products1 = hourlyRoomItem.getProducts();
                    if (products1 != null && !products1.isEmpty()) {
                        List<ProductDetail> list = products1.stream().filter(o -> o.getBookType() == 1).collect(Collectors.toList());
                        hourlyRoomItem.setProducts(list);
                    }
                }
                // 过滤产品为空的数据（不可订）
                if ((roomItem.getProducts() == null || roomItem.getProducts().isEmpty())) continue;

                roomDetail.put("otherFacilities", roomFacilitiesMap.get(roomtypeId));
                roomDetail.put("images", roomImageMap.get(roomtypeId));
                roomDetail.put("roomItems", roomItem);
                roomDetail.put("hourlyRoomItems", hourlyRoomItem);
                roomDetail.put("lowPrice", roomItem.getLowPrice());
                roomDetail.put("timeZone", productDetailResponse.getTimeZone());
                detailArray.add(roomDetail);
            }

            List<JSONObject> jsonList = new ArrayList<>();
            for (int i = 0; i < detailArray.size(); i++) {
                jsonList.add(detailArray.getJSONObject(i));
            }
            // 对List进行排序
            jsonList.sort(Comparator.comparingDouble(o -> o.getDouble("lowPrice")));

            // 创建一个新的排好序的JSONArray
            log.error("jsonList:" + JSON.toJSONString(jsonList));
            return Response.success(jsonList);
        }

        return Response.error(ErrorEnum.HOTEL_INFO_IS_ERROR.type, ErrorEnum.HOTEL_INFO_IS_ERROR.content);
    }

    /**
     * 校验条款
     */
    private String checkRestriction(PriceItem priceItem, Integer day, Date checkInDate) {
        if (priceItem.getOccupancyRestrictionType() != null && priceItem.getOccupancyRestrictionDay() != null) {
            if (priceItem.getOccupancyRestrictionType() == 1 && priceItem.getOccupancyRestrictionDay() > day) {
                // 最小连住条款
                return "需连续预定大于等于" + priceItem.getOccupancyRestrictionDay() + "天";
            } else if (priceItem.getOccupancyRestrictionType() == 2 && !priceItem.getOccupancyRestrictionDay().equals(day)) {
                // 限住条款
                return "需连续预定等于" + priceItem.getOccupancyRestrictionDay() + "天";
            } else if (priceItem.getOccupancyRestrictionType() == 3 && priceItem.getOccupancyRestrictionDay() < day) {
                // 最多住条款
                return "需连续预定小于等于" + priceItem.getOccupancyRestrictionDay() + "天";
            }
        }
        if (priceItem.getBookRestrictionType() != null
                && priceItem.getBookRestrictionDay() != null
                && StrUtilX.isNotEmpty(priceItem.getBookRestrictionTime())) {
            String bookRestrictionTime = priceItem.getBookRestrictionTime();
            if (bookRestrictionTime.contains(":")) {
                bookRestrictionTime = bookRestrictionTime.replace(":", "");
            }
            String tempBookRestrictionTime = insertStringAtPosition(bookRestrictionTime, ":", 2);
            String checkDateString = DateUtilX.dateToString(DateUtilX.getDate(checkInDate, -priceItem.getBookRestrictionDay(), 0)) + " " + bookRestrictionTime;
            Date bookDate = DateUtilX.stringToDate(checkDateString, "yyyy-MM-dd HHmm");
            if (priceItem.getBookRestrictionType() == 1) {
                // 提前几天几点之前预订
                if (DateUtilX.compareInMS(bookDate, DateUtilX.dateFormat(DateUtilX.getCurrentDate(), "yyyy-MM-dd HHmm")) < 0) {
                    return "需提前" + priceItem.getBookRestrictionDay() + "天" + tempBookRestrictionTime + "点之前预定";
                }
            } else if (priceItem.getBookRestrictionType() == 2) {
                // 在几天内几点之后预订
                if (DateUtilX.compareInMS(bookDate, DateUtilX.dateFormat(DateUtilX.getCurrentDate(), "yyyy-MM-dd HHmm")) > 0) {
                    return "需提前" + priceItem.getBookRestrictionDay() + "天" + tempBookRestrictionTime + "点之后预定";
                }
            }
        }
        return null;
    }

    /**
     * 在指定位置插入字符串
     */
    public static String insertStringAtPosition(String originalString, String insertString, int position) {
        StringBuilder stringBuilder = new StringBuilder(originalString);
        stringBuilder.insert(position, insertString);
        return stringBuilder.toString();
    }

    @Override
    public Response<JSONObject> queryHotelDetail(HotelInfoReq request) {
        request.getSettings().add(BaseHotelInfoUrl.facilities);

        HotelInfoCollectionReq req = new HotelInfoCollectionReq();
        req.setHotelIds(Collections.singletonList(request.getHotelId()));
        req.setSettings(new ArrayList<>(request.getSettings()));
        req.setLanguageType(request.getLanguageType());
        Response<HotelInfoCollectionDTO> hotelDetailResp = hotelProductRemote.queryHotelInfo(req);
        if (hotelDetailResp != null && hotelDetailResp.getResult() == 1) {
            // 获取标签信息
            Response<Set<String>> response = hotelProductRemote.queryHotelLabelInfo(request);
            if (response.isError()) {
                throw new SysException(ErrorCodeEnum.SYSTEM_EXCEPTION);
            }
            // 分析设施点亮标识
            JSONObject jsonObject = JSON.parseObject(JSONUtil.toJsonStr(hotelDetailResp.getModel()), JSONObject.class);
            if (jsonObject != null) {
                setFacilities(jsonObject, request.getLanguageType(), response.getModel());
            }
            return Response.success(jsonObject);
        } else {
            return Response.error(ErrorEnum.HOTEL_INFO_IS_ERROR.type, ErrorEnum.HOTEL_INFO_IS_ERROR.content);
        }
    }

    /**
     * 设置设施图标高亮显示
     */
    public void setFacilities(JSONObject jsonObject, String language, Set<String> labelNames) {
        JSONObject object = new JSONObject();
        object.put("parking", 0);//停车场
        object.put("swimming", 0);//游泳馆
        object.put("meeting", 0);//会议室
        object.put("restaurant", 0);//餐厅
        object.put("gym", 0);//健身房
        object.put("robot", 0);//机器人服务
        object.put("laundryRoom", 0);//洗衣房
        object.put("luggage", 0);//行李寄存
        jsonObject.put("hotelLabelNameList", labelNames);//标签信息
        Object praciceDate = jsonObject.get("praciceDate");// 开业时间
        if (praciceDate != null) {
            jsonObject.put("praciceDate", DateUtilX.longDateToString(Long.parseLong(praciceDate.toString())));
        }
        Object fitmentDate = jsonObject.get("fitmentDate");// 装修时间
        if (fitmentDate != null) {
            jsonObject.put("fitmentDate", DateUtilX.longDateToString(Long.parseLong(fitmentDate.toString())));
        }
        JSONArray facilities = jsonObject.getJSONArray("facilities");
        if (Objects.isNull(facilities)) {
            jsonObject.put("FacilityIdentification", object);
            return;
        }
        Map<String, String> facilitiesArrayMap = new HashMap<>();
        //获取设施id
        Response<FacilityDto> facilityDtoResponse = hotelRemote.queryFacility();
        FacilityDto model;
        if (facilityDtoResponse.isSuccess()) {
            model = facilityDtoResponse.getModel();
            if (Objects.isNull(model)) {
                return;
            }
        } else {
            return;
        }
        for (int i = 0; i < facilities.size(); i++) {
            JSONObject json = facilities.getJSONObject(i);
            String facilityId = json.getString("facilityId");
            if (model.getParkingIds().contains(facilityId)) {
                object.put("parking", 1);
            } else if (model.getMeetingIds().contains(facilityId)) {
                object.put("meeting", 1);
            } else if (model.getSwimmingIds().contains(facilityId)) {
                object.put("swimming", 1);
            } else if (model.getRestaurantIds().contains(facilityId)) {
                object.put("restaurant", 1);
            } else if (model.getGymIds().contains(facilityId)) {
                object.put("gym", 1);
            } else if (model.getRobotIds().contains(facilityId)) {
                object.put("robot", 1);
            } else if (model.getLaundryRoomIds().contains(facilityId)) {
                object.put("laundryRoom", 1);
            } else if (model.getLuggageIds().contains(facilityId)) {
                object.put("luggage", 1);
            }

            //酒店设施数据组装
            String dataName = json.getString("dataName");
            String facilityName = json.getString("facilityName");
            Integer facilityType = json.getInteger("facilityType");
            Integer status = json.getInteger("status");
            if (facilityType != null && facilityType == 1 && status != null && status == 1) {
                String value = facilitiesArrayMap.get(dataName);
                if (StringUtils.isEmpty(value)) {
                    facilitiesArrayMap.put(dataName, facilityName);
                } else {
                    facilitiesArrayMap.put(dataName, value + " | " + facilityName);
                }
            }
        }

        //判断是国外还是海内数据
        JSONObject localtion = jsonObject.getJSONObject("location");
        int supplyType = 1;
        if (localtion != null) {
            String country = localtion.getString("country");
            if (!"CN".equals(country)) {
                supplyType = 2;
            }
        }

        if (supplyType == 2) {
            jsonObject.put("facilities", setHotelFacilities(facilities, language));
        } else {
            jsonObject.put("facilities", facilitiesArrayMap);
        }
        jsonObject.put("FacilityIdentification", object);
    }

    /**
     * 设置酒店设施分类
     */
    public LinkedHashMap<String, String> setHotelFacilities(JSONArray facilities, String language) {

        LinkedHashMap<String, String> facilitiesArrayMap = new LinkedHashMap<>();
        if (facilities == null || facilities.isEmpty()) {
            return facilitiesArrayMap;
        } else {
            facilitiesArrayMap.put("transportationIds", "");
            facilitiesArrayMap.put("cleaningIds", "");
            facilitiesArrayMap.put("publicIds", "");
            facilitiesArrayMap.put("frontDeskIds", "");
            facilitiesArrayMap.put("playIds", "");
            facilitiesArrayMap.put("accessibilityIds", "");
            facilitiesArrayMap.put("beCurrentIds", "");
            facilitiesArrayMap.put("businessIds", "");
            facilitiesArrayMap.put("restaurantServerIds", "");
            facilitiesArrayMap.put("wellnessIds", "");
            facilitiesArrayMap.put("childrenIds", "");
            facilitiesArrayMap.put("otherIds", "");
        }

        for (int i = 0; i < facilities.size(); i++) {
            JSONObject json = facilities.getJSONObject(i);
            String facilityName = json.getString("facilityName");
            int facilityId = json.getInteger("facilityId");
            if (Arrays.stream(settingsConstant.getTransportationIds()).anyMatch(element -> element == facilityId)) {
                String key = "transportationIds";
                String transportation = facilitiesArrayMap.get(key);
                if (StringUtils.isEmpty(transportation)) {
                    facilitiesArrayMap.put(key, facilityName);
                } else {
                    facilitiesArrayMap.put(key, transportation + " | " + facilityName);
                }
            } else if (Arrays.stream(settingsConstant.getCleaningIds()).anyMatch(element -> element == facilityId)) {
                String key = "cleaningIds";
                String cleaning = facilitiesArrayMap.get(key);
                if (StringUtils.isEmpty(cleaning)) {
                    facilitiesArrayMap.put(key, facilityName);
                } else {
                    facilitiesArrayMap.put(key, cleaning + " | " + facilityName);
                }
            } else if (Arrays.stream(settingsConstant.getPublicIds()).anyMatch(element -> element == facilityId)) {
                String key = "publicIds";
                String publicId = facilitiesArrayMap.get(key);
                if (StringUtils.isEmpty(publicId)) {
                    facilitiesArrayMap.put(key, facilityName);
                } else {
                    facilitiesArrayMap.put(key, publicId + " | " + facilityName);
                }
            } else if (Arrays.stream(settingsConstant.getFrontDeskIds()).anyMatch(element -> element == facilityId)) {
                String key = "frontDeskIds";
                String frontDesk = facilitiesArrayMap.get(key);
                if (StringUtils.isEmpty(frontDesk)) {
                    facilitiesArrayMap.put(key, facilityName);
                } else {
                    facilitiesArrayMap.put(key, frontDesk + " | " + facilityName);
                }
            } else if (Arrays.stream(settingsConstant.getPlayIds()).anyMatch(element -> element == facilityId)) {
                String key = "playIds";
                String frontDesk = facilitiesArrayMap.get(key);
                if (StringUtils.isEmpty(frontDesk)) {
                    facilitiesArrayMap.put(key, facilityName);
                } else {
                    facilitiesArrayMap.put(key, frontDesk + " | " + facilityName);
                }
            } else if (Arrays.stream(settingsConstant.getAccessibilityIds()).anyMatch(element -> element == facilityId)) {
                String key = "accessibilityIds";
                String accessibility = facilitiesArrayMap.get(key);
                if (StringUtils.isEmpty(accessibility)) {
                    facilitiesArrayMap.put(key, facilityName);
                } else {
                    facilitiesArrayMap.put(key, accessibility + " | " + facilityName);
                }
            } else if (Arrays.stream(settingsConstant.getBeCurrentIds()).anyMatch(element -> element == facilityId)) {
                String key = "beCurrentIds";
                String beCurrent = facilitiesArrayMap.get(key);
                if (StringUtils.isEmpty(beCurrent)) {
                    facilitiesArrayMap.put(key, facilityName);
                } else {
                    facilitiesArrayMap.put(key, beCurrent + " | " + facilityName);
                }
            } else if (Arrays.stream(settingsConstant.getBusinessIds()).anyMatch(element -> element == facilityId)) {
                String key = "businessIds";
                String business = facilitiesArrayMap.get(key);
                if (StringUtils.isEmpty(business)) {
                    facilitiesArrayMap.put(key, facilityName);
                } else {
                    facilitiesArrayMap.put(key, business + " | " + facilityName);
                }
            } else if (Arrays.stream(settingsConstant.getRestaurantServerIds()).anyMatch(element -> element == facilityId)) {
                String key = "restaurantServerIds";
                String restaurantServer = facilitiesArrayMap.get(key);
                if (StringUtils.isEmpty(restaurantServer)) {
                    facilitiesArrayMap.put(key, facilityName);
                } else {
                    facilitiesArrayMap.put(key, restaurantServer + " | " + facilityName);
                }
            } else if (Arrays.stream(settingsConstant.getWellnessIds()).anyMatch(element -> element == facilityId)) {
                String key = "wellnessIds";
                String wellness = facilitiesArrayMap.get(key);
                if (StringUtils.isEmpty(wellness)) {
                    facilitiesArrayMap.put(key, facilityName);
                } else {
                    facilitiesArrayMap.put(key, wellness + " | " + facilityName);
                }
            } else if (Arrays.stream(settingsConstant.getChildrenIds()).anyMatch(element -> element == facilityId)) {
                String key = "childrenIds";
                String children = facilitiesArrayMap.get(key);
                if (StringUtils.isEmpty(children)) {
                    facilitiesArrayMap.put(key, facilityName);
                } else {
                    facilitiesArrayMap.put(key, children + " | " + facilityName);
                }
            } else if (Arrays.stream(settingsConstant.getOtherIds()).anyMatch(element -> element == facilityId)) {
                String key = "otherIds";
                String other = facilitiesArrayMap.get(key);
                if (StringUtils.isEmpty(other)) {
                    facilitiesArrayMap.put(key, facilityName);
                } else {
                    facilitiesArrayMap.put(key, other + " | " + facilityName);
                }
            }
        }
        return facilitiesArrayMap;
    }

    /**
     * 设置酒店设施分类
     */
    public LinkedHashMap<String, String> setRoomFacilities(List<FacilityPublicDTO> facilities) {
        LinkedHashMap<String, String> facilitiesArrayMap = new LinkedHashMap<>();
        if (facilities == null || facilities.isEmpty()) {
            return facilitiesArrayMap;
        } else {
            facilitiesArrayMap.put("toiletriesIds", "");
            facilitiesArrayMap.put("guestRoomFacilitiesIds", "");
            facilitiesArrayMap.put("technologyIds", "");
            facilitiesArrayMap.put("kitchenSuppliesIds", "");
            facilitiesArrayMap.put("specialFacilitiesIds", "");
            facilitiesArrayMap.put("outdoorLandscapeIds", "");
            facilitiesArrayMap.put("barrierFreeFacilitiesIds", "");
            facilitiesArrayMap.put("childrenFacilityServicesIds", "");
            facilitiesArrayMap.put("amenityIds", "");
            facilitiesArrayMap.put("otherServiceIds", "");
            facilitiesArrayMap.put("foodAndBeveragesIds", "");
            facilitiesArrayMap.put("bathroomIds", "");
        }

        for (FacilityPublicDTO facility : facilities) {
            String facilityName = facility.getFacilityName();
            Long facilityId = facility.getFacilityId();
            if (Arrays.stream(settingsConstant.getToiletriesIds()).anyMatch(element -> element == facilityId)) {
                String key = "toiletriesIds";
                String toiletries = facilitiesArrayMap.get(key);
                if (StringUtils.isEmpty(toiletries)) {
                    facilitiesArrayMap.put(key, facilityName);
                } else {
                    facilitiesArrayMap.put(key, toiletries + " | " + facilityName);
                }
            } else if (Arrays.stream(settingsConstant.getGuestRoomFacilitiesIds()).anyMatch(element -> element == facilityId)) {
                String key = "guestRoomFacilitiesIds";
                String guestRoomFacilities = facilitiesArrayMap.get(key);
                if (StringUtils.isEmpty(guestRoomFacilities)) {
                    facilitiesArrayMap.put(key, facilityName);
                } else {
                    facilitiesArrayMap.put(key, guestRoomFacilities + " | " + facilityName);
                }
            } else if (Arrays.stream(settingsConstant.getTechnologyIds()).anyMatch(element -> element == facilityId)) {
                String key = "technologyIds";
                String technology = facilitiesArrayMap.get(key);
                if (StringUtils.isEmpty(technology)) {
                    facilitiesArrayMap.put(key, facilityName);
                } else {
                    facilitiesArrayMap.put(key, technology + " | " + facilityName);
                }
            } else if (Arrays.stream(settingsConstant.getKitchenSuppliesIds()).anyMatch(element -> element == facilityId)) {
                String key = "kitchenSuppliesIds";
                String kitchenSupplies = facilitiesArrayMap.get(key);
                if (StringUtils.isEmpty(kitchenSupplies)) {
                    facilitiesArrayMap.put(key, facilityName);
                } else {
                    facilitiesArrayMap.put(key, kitchenSupplies + " | " + facilityName);
                }
            } else if (Arrays.stream(settingsConstant.getSpecialFacilitiesIds()).anyMatch(element -> element == facilityId)) {
                String key = "specialFacilitiesIds";
                String specialFacilities = facilitiesArrayMap.get(key);
                if (StringUtils.isEmpty(specialFacilities)) {
                    facilitiesArrayMap.put(key, facilityName);
                } else {
                    facilitiesArrayMap.put(key, specialFacilities + " | " + facilityName);
                }
            } else if (Arrays.stream(settingsConstant.getOutdoorLandscapeIds()).anyMatch(element -> element == facilityId)) {
                String key = "outdoorLandscapeIds";
                String outdoorLandscape = facilitiesArrayMap.get(key);
                if (StringUtils.isEmpty(outdoorLandscape)) {
                    facilitiesArrayMap.put(key, facilityName);
                } else {
                    facilitiesArrayMap.put(key, outdoorLandscape + " | " + facilityName);
                }
            } else if (Arrays.stream(settingsConstant.getBarrierFreeFacilitiesIds()).anyMatch(element -> element == facilityId)) {
                String key = "barrierFreeFacilitiesIds";
                String barrierFreeFacilities = facilitiesArrayMap.get(key);
                if (StringUtils.isEmpty(barrierFreeFacilities)) {
                    facilitiesArrayMap.put(key, facilityName);
                } else {
                    facilitiesArrayMap.put(key, barrierFreeFacilities + " | " + facilityName);
                }
            } else if (Arrays.stream(settingsConstant.getChildrenFacilityServicesIds()).anyMatch(element -> element == facilityId)) {
                String key = "childrenFacilityServicesIds";
                String childrenFacilityServicesIds = facilitiesArrayMap.get(key);
                if (StringUtils.isEmpty(childrenFacilityServicesIds)) {
                    facilitiesArrayMap.put(key, facilityName);
                } else {
                    facilitiesArrayMap.put(key, childrenFacilityServicesIds + " | " + facilityName);
                }
            } else if (Arrays.stream(settingsConstant.getAmenityIds()).anyMatch(element -> element == facilityId)) {
                String key = "amenityIds";
                String restaurantServer = facilitiesArrayMap.get(key);
                if (StringUtils.isEmpty(restaurantServer)) {
                    facilitiesArrayMap.put(key, facilityName);
                } else {
                    facilitiesArrayMap.put(key, restaurantServer + " | " + facilityName);
                }
            } else if (Arrays.stream(settingsConstant.getOtherServiceIds()).anyMatch(element -> element == facilityId)) {
                String key = "otherServiceIds";
                String otherService = facilitiesArrayMap.get(key);
                if (StringUtils.isEmpty(otherService)) {
                    facilitiesArrayMap.put(key, facilityName);
                } else {
                    facilitiesArrayMap.put(key, otherService + " | " + facilityName);
                }
            } else if (Arrays.stream(settingsConstant.getFoodAndBeveragesIds()).anyMatch(element -> element == facilityId)) {
                String key = "foodAndBeveragesIds";
                String foodAndBeverages = facilitiesArrayMap.get(key);
                if (StringUtils.isEmpty(foodAndBeverages)) {
                    facilitiesArrayMap.put(key, facilityName);
                } else {
                    facilitiesArrayMap.put(key, foodAndBeverages + " | " + facilityName);
                }
            } else if (Arrays.stream(settingsConstant.getBathroomIds()).anyMatch(element -> element == facilityId)) {
                String key = "bathroomIds";
                String bathroom = facilitiesArrayMap.get(key);
                if (StringUtils.isEmpty(bathroom)) {
                    facilitiesArrayMap.put(key, facilityName);
                } else {
                    facilitiesArrayMap.put(key, bathroom + " | " + facilityName);
                }
            }
        }
        return facilitiesArrayMap;
    }

    @Override
    public Response<HotelLowestPriceResponse> hotelIdsToPrice(HotelIdsToPriceReq req) {
        HotelLowestPriceRequest request = DTOConvert.INSTANCE.HotelLowestPriceConvert(req);
        List<String> agentCodes = new ArrayList<>();
        agentCodes.add(req.getAgentCode());
        request.setAgentCodes(agentCodes);
        request.setUserAccount(req.getUserAccount());
        ResponseResult<HotelLowestPriceResponse> response = hotelProductRemote.hotelLowestPrice(request);
        if (response != null) {
            return Response.success(response.getBussinessResponse());
        } else {
            return Response.error(ErrorEnum.HOTEL_PRICE_IS_ERROR.type, ErrorEnum.HOTEL_PRICE_IS_ERROR.content);
        }
    }

}
