package com.tiangong.h5.convert;

import com.tiangong.dto.hotel.HotelPageReq;
import com.tiangong.dto.order.request.CheckBookingRequest;
import com.tiangong.dto.order.request.CreateOrderRequest;
import com.tiangong.dto.order.response.OrderRoomDetailDto;
import com.tiangong.dto.product.PriceInfoDetail;
import com.tiangong.dto.product.PriceItem;
import com.tiangong.dto.product.RoomItemDetail;
import com.tiangong.dto.product.request.HotelLowestPriceRequest;
import com.tiangong.h5.domain.PO.UserPO;
import com.tiangong.h5.domain.req.*;
import com.tiangong.h5.domain.resp.UserListResp;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 类型转换
 */
@Mapper
public interface DTOConvert {

    DTOConvert INSTANCE = Mappers.getMapper(DTOConvert.class);

    HotelLowestPriceRequest HotelLowestPriceConvert(HotelIdsToPriceReq request);

    CreateOrderRequest CreateOrderConvert(AddOrderReq req);

    CheckBookingRequest CheckOrderConvert(CheckBookingReq req);

    RoomItemDetail RoomConvert(OrderRoomDetailDto detailDto);

    PriceItem PriceConvert(PriceInfoDetail infoDetail);

    UserListResp UserListRespConvert(UserPO userPO);

    UserPO UserPOConvert(AddOrUpdateUserReq req);

    HotelPageReq hotelListPageConvert(HotelListPageRequest hotelListPageRequest);
}
