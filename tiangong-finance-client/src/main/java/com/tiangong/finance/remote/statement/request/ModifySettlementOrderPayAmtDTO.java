package com.tiangong.finance.remote.statement.request;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class ModifySettlementOrderPayAmtDTO implements Serializable {


    /**
     * 账单id
     */
    @NotNull(message = "EMPTY_PARAM_STATEMENTID")
    private Integer statementId;

    /**
     * 账单供货单明细id
     */
    @NotNull(message = "EMPTY_PARAM_STATEMENTSUPPLYORDERID")
    private Integer statementSupplyOrderId;

    /**
     * 账单类型 0-供货单金额 1-奖励（收） 2-返佣
     */
    @Min(value = 0, message = "账单类型 0-供货单金额 1-奖励（收） 2-返佣，最小可填值为0")
    @Max(value = 2, message = "账单类型 0-供货单金额 1-奖励（收） 2-返佣，最大可填值为2")
    @NotNull(message = "EMPTY_PARAM_STATEMENTTYPE")
    private Integer statementType;

    /**
     * 币种
     */
    @NotNull(message = "EMPTY_PARAM_CURRENCY")
    private Integer currency;

    /**
     * 要付金额
     */
    @NotNull(message = "EMPTY_PARAM_STATEMENTPAYAMT")
    private BigDecimal payAmt;

    /**
     * 供应商编码
     */
    @NotEmpty(message = "EMPTY_PARAM_SUPPLYCODE")
    private String supplierCode;

    /**
     * 操作人
     */
    private String operator;
}
