<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.3.2.RELEASE</version>
    </parent>

    <groupId>com.tiangong</groupId>
    <artifactId>tiangong-parent</artifactId>
    <packaging>pom</packaging>
    <version>1.0.0</version>
    <modules>
        <module>hotel-dto</module>
        <module>tiangong-gateway</module>
        <module>tiangong-common</module>
        <!--<module>syyo-generator</module>-->
        <module>tiangong-common-bean</module>
        <module>tiangong-common-client</module>
        <module>tiangong-common-server</module>
        <module>tiangong-product-client</module>
        <module>tiangong-product-server</module>
        <module>tiangong-finance-client</module>
        <module>tiangong-finance-server</module>
        <module>tiangong-order-client</module>
        <module>tiangong-order-server</module>
        <module>tiangong-dis-common-client</module>
        <module>tiangong-dis-common-server</module>
        <module>tiangong-core</module>
        <module>dhub</module>
        <module>dhub-core</module>
        <!--        <module>tiangong-supply-direct-common-client</module>-->
        <!--        <module>tiangong-supply-direct-common-server</module>-->
        <!--        <module>mgs-operate-log</module>-->
        <module>tiangong-report-server</module>
        <module>tiangong-report-client</module>
        <module>tiangong-h5-server</module>
        <module>tiangong-supply-direct-shub-client</module>
        <module>tiangong-supply-direct-shub-server</module>
        <module>tiangong-settle-server</module>
        <module>tiangong-settle-client</module>
        <module>tiangong-invoice-server</module>
        <module>tiangong-invoice-client</module>
        <module>tiangong-message-notify-server</module>
        <module>tiangong-message-notify-client</module>
        <module>tiangong-hdp-server</module>
    </modules>

    <properties>

        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <log4j2.version>2.15.0</log4j2.version>
        <!--        <spring-cloud.version>Greenwich.SR3</spring-cloud.version>-->
        <spring-cloud-version>Hoxton.SR8</spring-cloud-version>
        <alibaba-nacos-discovery-version>2.2.3.RELEASE</alibaba-nacos-discovery-version>
        <alibaba-nacos-config-version>2.2.3.RELEASE</alibaba-nacos-config-version>
        <nacos-client-version>2.3.2</nacos-client-version>
        <nacos-api-version>2.3.2</nacos-api-version>

        <mysql-connector-java.version>8.0.17</mysql-connector-java.version>
        <mybatis-plus.version>3.2.0</mybatis-plus.version>
        <druid.version>1.1.20</druid.version>
        <fastjson.version>1.2.83</fastjson.version>
        <easyexcel.version>2.2.6</easyexcel.version>
        <lombok.version>1.18.2</lombok.version>

        <hutool-version>5.8.4.M1</hutool-version>

        <opentelemetry.version>1.23.0</opentelemetry.version>
        <jackson-databind.version>2.13.4.2</jackson-databind.version>
        <jackson-core.version>2.13.4</jackson-core.version>
        <jackson-annotations.version>2.13.4</jackson-annotations.version>
        <rocketMq.version>2.3.1</rocketMq.version>
        <poi-tl.version>1.10.6</poi-tl.version>
    </properties>

    <!--只是对版本进行管理，不会实际引入jar-->
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>io.opentelemetry</groupId>
                <artifactId>opentelemetry-bom</artifactId>
                <version>${opentelemetry.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!--boot套件-->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud-version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!--数据库-->
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql-connector-java.version}</version>
            </dependency>
            <!--mybatisPlus-->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>

            <!--json-->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <!--lombok-->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
                <version>${alibaba-nacos-discovery-version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
                <version>${alibaba-nacos-config-version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.nacos</groupId>
                <artifactId>nacos-client</artifactId>
                <version>${nacos-client-version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.nacos</groupId>
                <artifactId>nacos-api</artifactId>
                <version>${nacos-api-version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <!-- zly + 工具类 -->
                <version>${hutool-version}</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-annotations</artifactId>
                <version>${jackson-annotations.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${jackson-databind.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-core</artifactId>
                <version>${jackson-core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.deepoove</groupId>
                <artifactId>poi-tl</artifactId>
                <version>${poi-tl.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <distributionManagement>
        <repository>
            <id>releases</id>
            <name>Releases</name>
            <url>http://47.106.216.136:18081/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <name>Snapshot</name>
            <url>http://47.106.216.136:18081/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>
</project>