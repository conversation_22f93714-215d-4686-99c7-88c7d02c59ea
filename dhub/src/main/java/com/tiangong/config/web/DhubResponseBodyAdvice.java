package com.tiangong.config.web;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.tiangong.utils.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.http.server.ServletServerHttpRequest;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR> luzhiling
 * @date : 2024/1/22 17:43
 * @description :
 */
@RestControllerAdvice
@Slf4j
public class DhubResponseBodyAdvice implements ResponseBodyAdvice<Object> {

    @Override
    public boolean supports(MethodParameter returnType, Class<? extends HttpMessageConverter<?>> converterType) {
        // 在此处根据条件判断是否对指定的控制器方法执行处理
        return true; // 暂时对所有控制器方法都执行处理
    }

    @Override
    public Object beforeBodyWrite(Object body, MethodParameter returnType, MediaType selectedContentType,
                                  Class<? extends HttpMessageConverter<?>> selectedConverterType, ServerHttpRequest request,
                                  ServerHttpResponse response) {
        // 在此处对响应体进行处理，可以修改、增强或定制返回的数据
        // 返回修改后的响应体数据
        HttpServletRequest servletRequest = ((ServletServerHttpRequest) request).getServletRequest();
        if (null == servletRequest.getAttribute("dataEncryptionSwitch") || 0 == (Integer) servletRequest.getAttribute("dataEncryptionSwitch")) {
            return body;
        }
        String secretKey = (String) servletRequest.getAttribute("secretKey");
        String jsonStr = JSONUtil.toJsonStr(body);
        //加密
        String aesEncodeBody = CommonUtils.aesEncodeBase64(jsonStr, secretKey);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("data", aesEncodeBody);
        return jsonObject;

    }
}
