package com.tiangong.config.web;

import cn.hutool.core.util.StrUtil;
import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.tiangong.config.agentConfig.AgentConfig;
import com.tiangong.config.annotations.AccessLimit;
import com.tiangong.constant.Constant;
import com.tiangong.dis.enums.DhubReturnCodeEnum;
import com.tiangong.entity.base.*;
import com.tiangong.entity.dto.AgentApiConfigDTO;
import com.tiangong.enums.ApiNameEnum;
import com.tiangong.enums.SlsEnum;
import com.tiangong.enums.SlsLogLevelEnum;
import com.tiangong.keys.RedisKey;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.util.DateUtilX;
import com.tiangong.util.Md5Util;
import com.tiangong.util.SlsLoggerUtil;
import com.tiangong.util.StrUtilX;
import com.tiangong.utils.CommonUtils;
import com.tiangong.utils.SystemUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;
import java.util.*;

@Slf4j
@Service
public class RequestValidateIntercept implements HandlerInterceptor {


    @Autowired
    private SlsLoggerUtil slsLoggerUtil;

    /**
     * 频率限制key
     */
    public static final String LIMIT_KEY = "DIS:ACCESS_LIMIT:";

    /**
     * 分隔符
     */
    public static final String SPLIT_CODE = "_";


    @Override
    public boolean preHandle(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object handler) {
        boolean iscontinue = true;
        Request request = null;
        ResponseResult response = new ResponseResult();
        String requestBody = null;
        Header header = null;
        Date requestTime = new Date();
        AgentAccountConfig partnerInfo = null;
        String requestType = null;
        try {
            String path = httpServletRequest.getServletPath();
            // 1. 请求路径校验
            if (StrUtil.isBlank(path) ||  path.split("/").length < 2) {
                response.setReturnCode(DhubReturnCodeEnum.API_NOT_EXIST.no);
                response.setReturnMsg(DhubReturnCodeEnum.API_NOT_EXIST.code);
                writeResponse(partnerInfo, httpServletResponse, response);
                iscontinue = false;
                return false;
            }
            // 1.请求方法校验
            Class<?> className = ApiNameEnum.getClazzByApiName(path.split("/")[1]);
            if (className == null){
                response.setReturnCode(DhubReturnCodeEnum.API_NOT_EXIST.no);
                response.setReturnMsg(DhubReturnCodeEnum.API_NOT_EXIST.code);
                writeResponse(partnerInfo, httpServletResponse, response);
                iscontinue = false;
                return false;
            }
            requestBody = SystemUtil.getRequestBody(httpServletRequest);
            if (StrUtilX.isNotEmpty(requestBody)) {
                // 3.请求IP校验
                try {
                    request = JSON.parseObject(requestBody, Request.class);
                } catch (Exception e) {
                    response.setReturnCode(DhubReturnCodeEnum.INVALID_INPUTPARAM.no);
                    response.setReturnMsg(DhubReturnCodeEnum.INVALID_INPUTPARAM.code);
                    writeResponse(partnerInfo, httpServletResponse, response);
                    iscontinue = false;
                    return false;
                }
                // 4.请求头校验
                header = request.getHeader();
                if (Objects.isNull(header)){
                    response.setReturnCode(DhubReturnCodeEnum.HEADER_NOT_EMPTY.no);
                    response.setReturnMsg(DhubReturnCodeEnum.HEADER_NOT_EMPTY.code);
                    writeResponse(partnerInfo, httpServletResponse, response);
                    iscontinue = false;
                    return false;
                }

                requestType = request.getHeader().getRequestType();
                response = checkHeader(header);
                if (!response.getReturnCode().equals("000")){
                    iscontinue = false;
                    writeResponse(partnerInfo, httpServletResponse, response);
                    return false;
                }
                // 5.验证合作商
                partnerInfo = AgentConfig.getAgentAccount(header.getPartnerCode());

                if (Objects.isNull(partnerInfo)) {
                    response.setReturnCode(DhubReturnCodeEnum.INVALID_PARTNER.no);
                    response.setReturnMsg(DhubReturnCodeEnum.INVALID_PARTNER.code);
                    writeResponse(partnerInfo, httpServletResponse, response);
                    iscontinue = false;
                    return false;
                }

                httpServletRequest.setAttribute(Constant.AGENTCODE, partnerInfo.getAgentCode());
                httpServletRequest.setAttribute(Constant.DOMESTICOROVERSEAS, partnerInfo.getDomesticOrOverseas());
                httpServletRequest.setAttribute(Constant.QUOTESWITCH, partnerInfo.getQuoteSwitch());
                httpServletRequest.setAttribute(Constant.ORIGINALPROTOCOLPRICESWITCH, partnerInfo.getOriginalProtocolPriceSwitch());

                // 6.时间戳是否过期
                long maxLimit = 24 * 60 * 60 * 1000;
                long apart = System.currentTimeMillis() - header.getTimestamp();
                if (Long.compare(apart, maxLimit) == 1) {
                    response.setReturnCode(DhubReturnCodeEnum.TIMESTAMP_EXPIRATION.no);
                    response.setReturnMsg(DhubReturnCodeEnum.TIMESTAMP_EXPIRATION.code);
                    writeResponse(partnerInfo, httpServletResponse, response);
                    iscontinue = false;
                    return false;
                }

                // 7.签名校验
                //如果不开启数据加密开关才走hub自身的签名校验
                if (partnerInfo.getDataEncryptionSwitch() != null && partnerInfo.getDataEncryptionSwitch() == 0) {
                    if (!signatureValidate(header, partnerInfo)) {
                        response.setReturnCode(DhubReturnCodeEnum.SIGNATURE_INVALID.no);
                        response.setReturnMsg(DhubReturnCodeEnum.SIGNATURE_INVALID.code);
                        writeResponse(partnerInfo, httpServletResponse, response);
                        iscontinue = false;
                        return false;
                    }
                } else if (partnerInfo.getDataEncryptionSwitch() != null && partnerInfo.getDataEncryptionSwitch() == 1) {
                    //合作商加密数据进行解密处理
                    if(!decodeHandler(partnerInfo, request, httpServletRequest, httpServletResponse)){
                        return false;
                    }
                }

                // 8.频率校验 通过返回true 否则返回false
                if (!requestFrequencyValidate(partnerInfo, request, handler, httpServletResponse)) {
                    return false;
                }
            }else{
                iscontinue = false;
            }

        } catch (Exception e) {
            log.error("RequestValidateIntercept请求校验异常:", e);
            response.setReturnCode(DhubReturnCodeEnum.SYSTEM_EXCEPTION.no);
            response.setReturnMsg(DhubReturnCodeEnum.SYSTEM_EXCEPTION.code);
            //临时使用
            CommonUtils.saveInfoSlsLog(requestTime, requestType, StrUtilX.getStackTraceAsString(e), JSON.toJSONString(response), "1", Constant.PARAMERROR);
            iscontinue = false;
        } finally {
            //SLS日志接入
            if(!iscontinue){
                if(partnerInfo != null) {
                    CommonUtils.saveInfoSlsLog(requestTime, requestType, requestBody, JSON.toJSONString(response), "1",Constant.PARAMERROR);
                }else{
                    CommonUtils.saveInfoSlsLog(requestTime, requestType, requestBody, JSON.toJSONString(response), "1",Constant.PARAMERROR);
                }
            }
            //saveSlsLog("排查慢问题：dhub，验证合作商", requestTime, UUID.randomUUID().toString());
        }
        return iscontinue;
    }

    public boolean requestFrequencyValidate(AgentAccountConfig partnerInfo,Request request, Object handler,HttpServletResponse httpServletResponse) {
        ResponseResult response = null;
        if (handler instanceof HandlerMethod) {
            HandlerMethod hm = (HandlerMethod) handler;
            AccessLimit accessLimit = hm.getMethodAnnotation(AccessLimit.class);
            if (Objects.isNull(accessLimit)) {
                return true;
            }
            Header header = request.getHeader();
            String mapKey = header.getPartnerCode().concat(SPLIT_CODE).concat(header.getRequestType());
            AgentApiConfigDTO agentApiConfigDTO = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.AGENT_API_CONFIG_REDIS_KEY, mapKey), AgentApiConfigDTO.class);
            // 合作商不存在
            if (agentApiConfigDTO == null) {
                response = new ResponseResult();
                response.setReturnCode(DhubReturnCodeEnum.PARTNER_CONFIG_NOT_EXIST.no);
                response.setReturnMsg(DhubReturnCodeEnum.PARTNER_CONFIG_NOT_EXIST.code);
                writeResponse(partnerInfo, httpServletResponse, response);
                return false;
            }else {
                // 接口频率校验
                if (agentApiConfigDTO.getInvokeTimes() == null || agentApiConfigDTO.getInvokeTimes() == 0) {
                    response = new ResponseResult();
                    response.setReturnCode(DhubReturnCodeEnum.ACCESS_OVERCLOCKED.no);
                    response.setReturnMsg(DhubReturnCodeEnum.ACCESS_OVERCLOCKED.code);
                    writeResponse(partnerInfo, httpServletResponse, response);
                    return false;
                }
                String key = LIMIT_KEY.concat(mapKey);
                //设置自增
                Long incr = RedisTemplateX.incr(key, 1);
                if (incr == 1) {
                    // 如果是第一个请求，则设置过期时间60s
                    RedisTemplateX.expire(key, 60);
                }
                // 如果大于最大值，则返回操作频繁,其他的请求直接返回
                if (incr > agentApiConfigDTO.getInvokeTimes()) {
                    //超过最大值返回操作频繁
                    response = new ResponseResult();
                    response.setReturnCode(DhubReturnCodeEnum.ACCESS_OVERCLOCKED.no);
                    response.setReturnMsg(DhubReturnCodeEnum.ACCESS_OVERCLOCKED.code);
                    writeResponse(partnerInfo, httpServletResponse, response);
                    return false;
                }
            }
        }
        return true;
    }




    /**
     * 保存日志
     */
    private void saveSlsLog(String message, Date start, String logId) {
        Map<String, String> map = new HashMap<>();
        map.put(SlsEnum.LEVEL.getType(), SlsLogLevelEnum.INFO.getLevel());
        map.put(SlsEnum.NAME.getType(), "dhub-requestValidateIntercept");
        map.put("logFlag", "排查慢问题");
        map.put("logId", logId);
        map.put(SlsEnum.MESSAGE.getType(), message);
        map.put("start", DateUtilX.dateToString(start, DateUtilX.hour_ms_format));
        slsLoggerUtil.saveLog(map, "dhub-requestValidateIntercept", "dhub");
    }

    /**
     * 对加密数据进行解密
     *
     * @param request
     * @param httpServletRequest
     * @param httpServletResponse
     * @return
     */
    private boolean decodeHandler(AgentAccountConfig agentAccountConfig,Request request,HttpServletRequest httpServletRequest,HttpServletResponse httpServletResponse) {
        ResponseResult response = new ResponseResult();
        Header header = request.getHeader();


        if (StrUtilX.isEmpty(request.getData())) {
            response.setReturnCode(DhubReturnCodeEnum.ENCRYPTED_STRING_DATA_CANNOT_BE_EMPTY.no);
            response.setReturnMsg(DhubReturnCodeEnum.ENCRYPTED_STRING_DATA_CANNOT_BE_EMPTY.code);
            writeResponse(agentAccountConfig, httpServletResponse, response);
            return false;
        }

        // 解密请求内容
        String decodedText = CommonUtils.aesDecodeBase64(request.getData(), agentAccountConfig.getSecretkey());
        if (StrUtilX.isEmpty(decodedText)) {
            response.setReturnCode(DhubReturnCodeEnum.DECRYPTION_EXCEPTION.no);
            response.setReturnMsg(DhubReturnCodeEnum.DECRYPTION_EXCEPTION.code);
            writeResponse(agentAccountConfig, httpServletResponse, response);
            return false;
        }
        // 消除最外面的双引号，以便可以正常转对象，如果字符串正常可省略
        decodedText = JSON.parseObject(decodedText, String.class);

        // 必须用接口对应的请求类型的class强转，否则拿不到各接口对应的业务参数
        // 处理json串收尾引号和转义符，形成合法的json字符串
        Class<?> className = ApiNameEnum.getClazzByApiName(request.getHeader().getRequestType());
        if (null == className) {
            response.setReturnCode(DhubReturnCodeEnum.API_NOT_EXIST.no);
            response.setReturnMsg(DhubReturnCodeEnum.API_NOT_EXIST.code);
            writeResponse(agentAccountConfig, httpServletResponse, response);
            return false;
        }

        BusinessRequest businessRequest = (BusinessRequest) JSON.parseObject(decodedText, className);
        if (null == businessRequest) {
            response.setReturnCode(DhubReturnCodeEnum.DECRYPTION_EXCEPTION.no);
            response.setReturnMsg(DhubReturnCodeEnum.DECRYPTION_EXCEPTION.code);
            writeResponse(agentAccountConfig, httpServletResponse, response);
            return false;
        }

        /** 开始验签 **/
        String sign = businessRequest.getSign();
        if (StringUtils.isEmpty(sign)) {
            response.setReturnCode(DhubReturnCodeEnum.SIGNATURE_EMPTY.no);
            response.setReturnMsg(DhubReturnCodeEnum.SIGNATURE_EMPTY.code);
            writeResponse(agentAccountConfig, httpServletResponse, response);
            return false;
        } else {
            // 加签时的对象是不含签名的，故要先清空
            businessRequest.setSign(null);
            // 解密后的内容转成对象
            String decodeText = JSON.toJSONString(businessRequest);
            if (!checkSign(agentAccountConfig, header, sign, decodeText)) {
                response.setReturnCode(DhubReturnCodeEnum.SIGNATURE_INVALID.no);
                response.setReturnMsg(DhubReturnCodeEnum.SIGNATURE_INVALID.code);
                writeResponse(agentAccountConfig, httpServletResponse, response);
                return false;
            }
        }

        //平安--默认查询中文
        if (org.apache.commons.lang3.StringUtils.isEmpty(businessRequest.getLanguage())){
            businessRequest.setLanguage("zh-CN");
        }
        request.setBusinessRequest(businessRequest);
        // 解密解析后，清空加密数据
        request.setData(null);
        // 重新RequestBody，变成明文请求
        RequestWrapper requestWrapper = (RequestWrapper) httpServletRequest;
        requestWrapper.setBody(JSONObject.toJSONString(request).getBytes());

        // 避免在返回数据加密时再查一次配置，故传入秘钥
        httpServletRequest.setAttribute("secretKey", agentAccountConfig.getSecretkey());
        // 避免在返回数据加密时再查一次配置，故传入是否加密配置
        httpServletRequest.setAttribute("dataEncryptionSwitch", agentAccountConfig.getDataEncryptionSwitch());
        httpServletRequest.setAttribute("partnerCode", header.getPartnerCode());
        httpServletRequest.setAttribute("requestType", header.getRequestType());

        return true;
    }

    private boolean checkPartnerInfo(Header header, AgentAccountConfig agentAccountConfig) {
        if (agentAccountConfig == null ) {
            log.error("调用接口:" + header.getRequestType() + "失败，无效合作商.partnerCode:" + header.getPartnerCode());
            return false;
        }
        return true;
    }

    private void writeResponse(AgentAccountConfig partnerInfo, HttpServletResponse httpServletResponse, ResponseResult response) {
        PrintWriter printWriter = null;
        try {
            httpServletResponse.setContentType("application/json;charset=utf-8");
            printWriter = httpServletResponse.getWriter();
            printWriter.print(JSON.toJSON(response));
            printWriter.flush();
        } catch (Exception e) {
            log.error("RequestValidateIntercept输出响应结果异常:", e);
        } finally {
            if (printWriter != null) {
                printWriter.close();
            }
        }
    }

    private boolean signatureValidate(Header header, AgentAccountConfig partnerInfo) throws Exception {
        boolean result = true;
        if (partnerInfo != null && StrUtilX.isNotEmpty(partnerInfo.getSecretkey())) {
            StringBuffer sb = new StringBuffer();
            sb.append(header.getTimestamp()).append(header.getPartnerCode()).append(Md5Util.md5Encode(partnerInfo.getSecretkey()).toUpperCase()).append(header.getRequestType());
            String md5Str = Md5Util.md5Encode(sb.toString()).toUpperCase();
            if (!Objects.equals(md5Str, header.getSignature())) {
                result = false;
            }
        } else {
            result = false;
            log.error("未获取到合作商密匙配置信息，签名校验失败：" + header.getPartnerCode());
        }
        return result;
    }

    private boolean checkSign(AgentAccountConfig agentAccountConfig, Header header, String sign, String data) {
        StringBuffer sb = new StringBuffer();
        sb.append(header.getTimestamp());
        sb.append(header.getPartnerCode());
        sb.append(header.getRequestType());
        sb.append(header.getVersion());
        sb.append(data);
        return CommonUtils.rsa256CheckSign(sb.toString(), sign, agentAccountConfig.getCustomerPublicKey());
    }

    private ResponseResult checkHeader(Header header){
        ResponseResult response = new ResponseResult();
        response.setReturnCode(DhubReturnCodeEnum.SUCCESS.no);
        if (Objects.isNull(header)){
            response.setReturnCode(DhubReturnCodeEnum.HEADER_NOT_EMPTY.no);
            response.setReturnMsg(DhubReturnCodeEnum.HEADER_NOT_EMPTY.code);
            return response;
        }
        if (StrUtilX.isEmpty(header.getPartnerCode())){
            response.setReturnCode(DhubReturnCodeEnum.PARTNER_CODE_EMPTY.no);
            response.setReturnMsg(DhubReturnCodeEnum.PARTNER_CODE_EMPTY.code);
            return response;
        }
        if (StrUtilX.isEmpty(header.getRequestType())){
            response.setReturnCode(DhubReturnCodeEnum.REQUEST_TYPE_EMPTY.no);
            response.setReturnMsg(DhubReturnCodeEnum.REQUEST_TYPE_EMPTY.code);
            return response;
        }
        if (StrUtilX.isEmpty(header.getSignature())){
            response.setReturnCode(DhubReturnCodeEnum.SIGNATURE_EMPTY.no);
            response.setReturnMsg(DhubReturnCodeEnum.SIGNATURE_EMPTY.code);
            return response;
        }
        if (header.getTimestamp() == null){
            response.setReturnCode(DhubReturnCodeEnum.TIMESTAMP_EMPTY.no);
            response.setReturnMsg(DhubReturnCodeEnum.TIMESTAMP_EMPTY.code);
            return response;
        }
        return response;
    }
}
