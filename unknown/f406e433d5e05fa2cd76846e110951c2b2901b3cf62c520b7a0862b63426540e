package com.tiangong.organization.domain.req;

import com.tiangong.dto.common.BasePO;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @create 2023/10/27 14:32
 */
@Data
public class AgentUpdateMathReq extends BasePO {

    /**
     * 保留的小数位
     */
    @NotNull(message = "EMPTY_PARAM_DECIMALPLACES")
    private Integer decimalPlaces;

    /**
     * 取整方式 1向上取整 2向下取整 3四舍五入
     */
    @NotNull(message = "EMPTY_PARAM_ROUNDINGTYPE")
    private Integer roundingType;

    /**
     * 客户编码
     */
    private String agentCode;

    /**
     * 客户Id
     */
    private Integer agentId;
}
