package com.tiangong.organization.service;

import com.tiangong.common.Response;
import com.tiangong.dto.common.AddSupplierForeignReq;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.organization.domain.entity.OrgSupplierIncreaseLog;
import com.tiangong.organization.domain.req.*;
import com.tiangong.organization.domain.resp.QueryOrderSupplierDetailResp;
import com.tiangong.organization.domain.resp.SupplierLabelConfigResp;
import com.tiangong.organization.remote.dto.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/6/19 22:12
 **/
public interface SupplierService {
    /**
     * 新增供应商
     */
    OrgDTO addSupplier(AddSupplierReq supplierAddDTO);

    /**
     * 对外提供新增供应商接口
     */
    Map<String, Object> addSupplierForeign(List<AddSupplierForeignReq> addSupplierForeignReq);

    /**
     * 修改供应商启用状态
     */
    void modifySupplierStatus(SupplierStatusUpdateReq supplierAddDTO);

    /**
     * 修改供应商信息
     */
    void modifySupplier(SupplierUpdateReq supplierAddDTO);

    /**
     * 根据供应商Code,查询供应商详情
     */
    SupplierSelectDTO querySupplierDetail(SupplierDetailByCode supplierAddDTO);

    /**
     * 查询供应商列表（分页）
     */
    PaginationSupportDTO<QuerySupplierListDTO> querySupplierList(SupplierListReq request);

    /**
     * 查询供应商列表（不分页）
     */
    List<QuerySupplierListDTO> querySupplierInfoList(SupplierListReq request);

    /**
     * 查询企业落款
     */
    PaginationSupportDTO<SendEntityDTO> queryCompanySignature(QueryCompanySignatureDTO queryCompanySignatureDTO);

    /**
     * 验证密码获取手机号
     */
    String getPhoneAsPassword(ContactAsPhoneDTO contact);

    /**
     * 修改供应商手机号
     */
    void modifySupplierPhone(ContactAsPhoneDTO contact);

    /**
     * 修改供应商加幅
     */
    void modifySupplierAdjustment(SupplierAdjustmentUpdateReq req);

    /**
     * 修改供应商发票类型
     */
    void modifySupplierInvoice(SupplierInvoiceUpdateReq req);

    /**
     * 根据供应商编码查询加幅修改日志
     */
    PaginationSupportDTO<OrgSupplierIncreaseLog> supplierIncreaseLogList(IncreaseLogReq increaseLogReq);

    /**
     * 新增/编辑供应商标签
     */
    void addOrUpdateSupplierLabelConfig(SupplierLabelConfigReq req);

    /**
     * 删除供应商标签
     */
    void delSupplierLabelConfig(DelSupplierLabelConfigReq req);

    /**
     * 查询供应商标签配置分页
     */
    PaginationSupportDTO<SupplierLabelConfigResp> querySupplierLabelConfigPage(QuerySupplierLabelConfigReq req);

    /**
     * 查询订单客户信息
     */
    QueryOrderSupplierDetailResp queryOrderSupplierDetail(QueryOrderSupplierDetailReq req);

    /**
     * 查询供应商名称列表
     */
    List<QuerySupplierListDTO> querySupplierNameList(SupplierListReq request);

    /**
     * 导入供应商标签配置
     */
    Response<Object> importSupplierLabelConfig(MultipartFile file, String operator);
}

