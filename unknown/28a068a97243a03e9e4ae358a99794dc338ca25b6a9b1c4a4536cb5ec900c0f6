package com.tiangong.supply.direct.remote;

import com.tiangong.common.Response;
import com.tiangong.supply.direct.entity.dto.ProductDetailDTO;
import com.tiangong.supply.direct.entity.dto.ProductMiddleDto;
import com.tiangong.supply.direct.entity.request.QuerySupplyProductRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

/**
 * 供应商直连产品
 **/
@FeignClient(name = "tiangong-supply-direct-common-server", contextId = "SupplyDirectProductRemote")
public interface SupplyDirectProductRemote {

    /**
     * 获取产品详情数据
     */
    @RequestMapping(value = "/supply/direct/query/product", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    Response<ProductDetailDTO> queryProductInfo(@RequestBody QuerySupplyProductRequest querySupplyProductRequest);

    /**
     * 获取指定供应商产品详情
     */
    @RequestMapping(value = "/supply/direct/query/product/supplyCode", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    Response<List<ProductMiddleDto>> queryProductInfoBySupplyCode(@RequestBody QuerySupplyProductRequest querySupplyProductRequest);
}
