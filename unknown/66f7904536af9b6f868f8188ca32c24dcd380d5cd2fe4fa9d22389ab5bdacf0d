<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.tiangong.organization.mapper.OrgAgentApiConfigMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap id="BaseResultMap" type="com.tiangong.organization.domain.entity.OrgAgentApiConfigEntity">
        <result property="id" column="id"/>
        <result property="agentCode" column="agent_code"/>
        <result property="secretKey" column="secret_key"/>
        <result property="orderStatusNotifyUrl" column="order_status_notify_url"/>
        <result property="invoiceNotifyUrl" column="invoice_notify_url"/>
        <result property="orderRefundNotifyUrl" column="order_refund_notify_url"/>
        <result property="orderCheckDetailNotifyUrl" column="order_check_detail_notify_url"/>
        <result property="cooperatingState" column="cooperating_state"/>
        <result property="createdBy" column="created_by"/>
        <result property="createdDt" column="created_dt"/>
        <result property="updatedBy" column="updated_by"/>
        <result property="updatedDt" column="updated_dt"/>
        <result property="deleted" column="deleted"/>
    </resultMap>


</mapper>