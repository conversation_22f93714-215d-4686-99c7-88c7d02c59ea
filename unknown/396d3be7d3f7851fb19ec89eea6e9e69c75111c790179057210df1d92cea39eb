package com.tiangong.supply.direct.entity.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

@Data
//@JsonInclude(JsonInclude.Include.NON_DEFAULT)
public class PreBookingRequest {

    /**
     * MGS酒店ID
     */
    @NotNull(message = "EMPTY_PARAM_HOTELID")
    private String hotelId;

    /**
     * MGS房型ID
     */
    @NotNull(message = "EMPTY_PARAM_ROOMID")
    private String roomId;

    /**
     * MGS价格计划ID
     */
    private String pricePlanId;


    /**
     * 供应商编码
     */
    @NotNull(message = "EMPTY_PARAM_SUPPLYCODE")
    private String supplyCode;

    /**
     * 入住日期
     */
    @NotNull(message = "EMPTY_PARAM_CHECKINDATE")
    private String checkInDate;

    /**
     * 离店日期
     */
    @NotNull(message = "EMPTY_PARAM_CHECKOUTDATE")
    private String checkOutDate;

    /**
     * 预定房间数量
     */
    @NotNull(message = "EMPTY_PARAM_ROOMNUM")
    private Integer roomNum;

    /**
     * 入住人数
     */
    private Integer guestCount;

    /**
     *  单价
     */
    private BigDecimal roomPrice;

    /**
     * 总价
     */
    private BigDecimal orderSum;


    /**
     * 产品ID
     */
    @NotNull(message = "EMPTY_PARAM_SPPRODUCTID")
    private String spProductId;

    /**
     * 每间房成人数量
     */
    private Integer numberOfAdults;

    /**
     * 每间房儿童数量
     */
     private Integer numberOfChildren;

    /**
     * 儿童年龄,多个,号隔开
     */
    private String childrenAges;

    /**
     * 入住人国籍
     */
    private String clientNationality;

    /**
     * 携程联登用户id
     */
    private String userId;

    /**
     * 登陆账号
     */
    private String userAccount;

    /**
     * 是否钟点房1是0否 默认日历房
     */
    private int hourly;

    /**
     * 供应商类型 1:国内 2:海外
     */
    private String supplyType;

    /**
     * 房间人数信息
     */
    private List<RoomGuestNumber> roomGuestNumbers;

    /**
     * 排查问题
     */
    private String requestId;

    /**
     * 币种
     */
    private Integer currency;

    /**
     * 国籍
     */
    private String nationality;
}
