package com.tiangong.supply.direct.entity.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Set;

@Data
public class QuerySupplyProductRequest {

    /**
     * 酒店ID
     */
    @NotNull(message = "EMPTY_PARAM_HOTELID")
    private Long hotelId;

    /**
     * 房型ID
     */
    private Integer roomId;

    /**
     * 入住日期
     */
    @NotNull(message = "EMPTY_PARAM_STARTTIME")
    private String checkInDate;

    /**
     * 离店日期
     */
    @NotNull(message = "EMPTY_PARAM_ENDTIME")
    private String checkOutDate;

    /**
     * 间数
     */
    private Integer roomQty;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 供应商白名单列表
     */
    private Set<String> whiteSupplyCodes;

    /**
     * 入住总人数
     */
    private Integer guestQuantity;

    /**
     * 币种
     */
    private Integer currency;

    /**
     * 分销商编码 如果不填写 则必填 supplierCode供应商编码
     */
    private String agentCode;

    /**
     * 供应商产品id
     */
    private String spProductId;

    /**
     * 忽略之前的缓存 重新查询酒店产品 并更新缓存
     * 只有帮用户下单会设置为true 重新触发缓存
     */
    private boolean needQueryProduct = false;

    /**
     * 只查钟点房	Integer	否	1是0否 默认0
     */
    private int onlyHourRoom;

    /**
     * 供应商类型 1:国内 2:海外
     */
    private String supplyType;

    /**
     * 房间人数信息
     */
    private List<RoomGuestNumber> roomGuestNumbers;

    /**
     * 登陆账号
     */
    private String userAccount;

    /**
     * 排查问题
     */
    private String requestId;

    /**
     * 语言
     */
    private String language;

    /**
     * 国籍
     */
    private String nationality;
}
