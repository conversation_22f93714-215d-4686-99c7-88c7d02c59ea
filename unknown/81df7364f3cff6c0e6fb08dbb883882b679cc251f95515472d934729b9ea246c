<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tiangong.order.mapper.OrderRequestMapper">
    <resultMap id="BaseResultMap" type="com.tiangong.order.domain.OrderRequestPO">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="order_id" jdbcType="INTEGER" property="orderId"/>
        <result column="request_type" jdbcType="INTEGER" property="requestType"/>
        <result column="handle_result" jdbcType="INTEGER" property="handleResult"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="created_by" jdbcType="VARCHAR" property="createdBy"/>
        <result column="created_dt" jdbcType="TIMESTAMP" property="createdDt"/>
        <result column="updated_by" jdbcType="VARCHAR" property="updatedBy"/>
        <result column="updated_dt" jdbcType="TIMESTAMP" property="updatedDt"/>
    </resultMap>

    <select id="queryOrderRequestCount" parameterType="java.util.List"
            resultType="com.tiangong.order.remote.response.OrderRequestCountDTO">
        SELECT
        r.order_id orderId,
        SUM(IF(r.request_type=0,1,0))cancelCount,
        SUM(IF(r.request_type=1,1,0))modifyCount,
        SUM(IF(r.request_type=2,1,0))doubleConfirmationCount
        FROM
        o_order_request r
        WHERE
        r.handle_result = 0
        AND r.order_id IN
        <foreach collection="list" item="item" open="(" separator="," close=")">#{item}</foreach>
        GROUP BY r.order_id
    </select>

    <select id="queryOrderRequestStatistics" parameterType="com.tiangong.order.remote.request.QueryOrderStatisticsDTO"
            resultType="com.tiangong.order.remote.response.OrderRequestCountDTO">
        SELECT
        SUM(IF(r.request_type=0 or r.request_type=3,1,0))cancelCount,
        SUM(IF(r.request_type=1,1,0))modifyCount
        FROM
        o_order_request r,o_order o
        WHERE
        r.order_id=o.id
        AND r.handle_result = 0
        AND o.order_confirmation_status != 2
        AND o.company_code=#{companyCode}
    </select>
</mapper>