package com.tiangong.organization.domain;

import com.tiangong.dto.common.BasePO;
import com.tiangong.sensitive.EncryptField;
import com.tiangong.sensitive.SensitiveClass;
import lombok.Data;

import javax.persistence.*;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019/6/19 16:32
 **/
@Data
@SensitiveClass
@Table(name = "t_auth_user")
public class UserPO extends BasePO {
    /**
     * 账号信息Id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer userId;

    /**
     * 用户名
     */
    @Column(name = "user_name")
    private String userName;
    /**
     * 登录账号
     */
    @Column(name = "user_account")
    private String userAccount;

    /**
     * 手机号
     */
    @EncryptField
    @Column(name = "user_tel")
    private String userTel;

    /**
     * 身份证
     */
    @EncryptField
    @Column(name = "user_id_number")
    private String userIDNumber;

    /**
     * 账号类型 0-长期 1-短期
     */
    @Column(name = "user_account_type")
    private Integer userAccountType;

    /**
     * 账号有效期开始时间
     */
    @Column(name = "account_avl_start_time")
    private String accountAvlStartTime;

    /**
     * 账号有效期结束时间
     */
    @Column(name = "account_avl_end_time")
    private String accountAvlEndTime;

    /**
     * 账号有效期开始时间
     */
    @Column(name = "visit_start_time")
    private String visitStartTime;

    /**
     * 账号有效期结束时间
     */
    @Column(name = "visit_end_time")
    private String visitEndTime;

    /**
     * 最新登录时间
     */
    @Column(name = "last_login_time")
    private Date lastLoginTime;

    /**
     * 可访问IP(白名单)
     */
    @Column(name = "visit_ip")
    private String visitIp;

    /**
     * 账号状态 0-激活 1-休眠 2-注销 3-锁定
     */
    @Column(name = "account_status")
    private Integer accountStatus;

    /**
     * 最新修改密码时间
     */
    @Column(name = "update_pwd_time")
    private Date updatePwdTime;

    /**
     * 账号解锁时间
     */
    @Column(name = "unlock_time")
    private Date unlockTime;

    /**
     * 有效性 0-无效 1-有效
     */
    @Column(name = "active")
    private Integer active;


    /**
     *是否是超级管理员
     */
    @Column(name = "is_super_admin")
    private Integer isSuperAdmin;
    /**
     *邮箱
     */
    @Column(name = "email")
    private String email;

    /**
     *是否启用1是，0否
     */
    @Column(name = "available_status")
    private Integer availableStatus;

    /**
     *登录密码
     */
    @Column(name = "user_pwd")
    private String userPwd;

    /**
     * 加签密码
     */
    @Column(name = "signature_pwd")
    private String signaturePwd;

    /**
     *盐值
     */
    @Column(name = "salt")
    private String salt;
    /**
     *微信openId,公众号内唯一
     */
    @Column(name = "wexin_openid")
    private String wexinOpenid;
    /**
     *微信unionid,企业内唯一
     */
    @Column(name = "wexin_unionid")
    private String wexinUnionid;

    /**
     *合作商类型
     */
    @Column(name = "partner")
    private String partner;

    /**
     *企业编码
     */
    @Column(name = "org_code")
    private String orgCode;

    /**
     * 是否有教程编辑权限 0-否 1-是
     */
    @Column(name = "is_note_permission")
    private Integer isNotePermission;

    /**
     * 审核状态 0-无需审核 1-提交审核失败 2-审核中 3-审核通过 4-审核不通过
     */
    @Column(name = "approval_status")
    private Integer approvalStatus;

    public UserPO(){}

    public UserPO(Integer userId) {
        this.userId = userId;
    }
}
