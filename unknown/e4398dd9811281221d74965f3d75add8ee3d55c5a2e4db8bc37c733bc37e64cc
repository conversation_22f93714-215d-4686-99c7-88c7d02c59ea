package com.tiangong.dto.product.response;


import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 11．酒店列表查询接口-queryHotelList 接口返回对象
 */
@Data
@Accessors(chain = true)
public class HotelLowestPriceInfo {

    /**
     * hotelId	酒店id	Long
     */
    private Long hotelId;
    /**
     * hotelName	酒店名称	String
     */
    private String hotelName;
    /**
     * hotelStar	星级描述	String	枚举code
     */
    private String hotelStar;

    /**
     * districtName	行政区名称	String	如：朝阳区
     */
    private String districtName;
    /**
     * businessName	商业区名称	String	如：望京地区
     */
    private String businessName;
    /**
     * hotelLabel	酒店标签	Integer	1.协议酒店
     * 2.自签酒店
     * 为空表示无标签
     */
    private Integer hotelLabel;

    /**
     * distance	距离	String	传入的位置（入参中的经纬度）与当前酒店的距离
     */
    private String distance;
    /**
     * appearancePicUrl	酒店主图	String	图片Http地址
     */
    private String appearancePicUrl;

    /**
     * lowestPrice	最低价	BigDecimal	否	最低价
     */
    private BigDecimal lowestPrice;

    //------

    /**
     * hotelAddress	酒店地址	String	酒店地址
     */
    private String hotelAddress;
    /**
     * hotelStarDesc	星级描述	String	如：舒适型
     */
    private String hotelStarDesc;
    /**
     * district	行政区编码	String
     */
    private String district;
    /**
     * business	商业区编码	String
     */
    private String business;

    /**
     * longitude	经度	String	否	百度经度
     */
    private String longitude;
    /**
     * latitude	纬度	String	否	百度纬度
     */
    private String latitude;

}
