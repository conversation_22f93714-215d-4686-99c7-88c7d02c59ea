spring:
  application:
    name: tiangong-product-server
  jackson:
    time-zone: GMT+8
  main:
    allow-bean-definition-overriding: true
  profiles:
    active: pro

server:
  port: 14002

#mybatis:
#  mapper-locations: classpath*:/mapping/*Mapper.xml
#  type-aliases-package: com.tiangong.*.domain,com.tiangong.*.*.domain.*

mybatis-plus:
  mapper-locations: classpath*:/mapping/*Mapper.xml
  type-aliases-package: com.tiangong.*.domain,com.tiangong.*.*.domain.*
  global-config:
    db-config:
      id-type: auto
      field-strategy: 2
      refresh-mapper: true
      db-type: mysql

tiangong:
  security:
    switch: true # 是否开启security，true：开启，false：关闭
    secret: ZmQ0ZGI5NjQ0MDQwY2I4MjMxY2Y3ZmI3MjdhN2ZmMjNhODViOTg1ZGE0NTBjMGM4NDA5NzYxMjdjOWMwYWRmZGE= # Base64编码
    tokentime: 20 # 令牌过期时间 此处单位/小时 ，默认2小时，可在此网站生成 https://www.convertworld.com/zh-hans/time/milliseconds.html


mapper:
  mappers: com.tiangong.dto.common.MyMapper
  not-empty: false
  identity: MYSQL

pagehelper:
  helperDialect: mysql
  reasonable: true
  supportMethodsArguments: true
  params: count=countSql


logging:
  level:
    com:
      alibaba:
        nacos: info

ribbon:
  ReadTimeout: 60000
  ConnectTimeout: 60000

#xxljob配置
xxl:
  job:
    admin:
      addresses: http://127.0.0.1:18880/xxl-job-admin/
    accessToken: default_token
    executor:
      appname: tiangong-product-server
      address:
      logpath: ./applogs/tiangong-product-server
      logretentiondays: 3
      port: 0


logstash:
  url: ************:5044

#床型语言信息
bed:
  name:
    zh_cn:
      - "1000000": "大床"
      - "A000000": "单人床"
      - "D000000": "榻榻米"
      - "E000000": "水床"
      - "F000000": "圆床"
      - "H000000": "炕"
      - "I000000": "沙发床"
      - "N000000": "胶囊床"
      - "P000000": "双人床"
      - "S000000": "双人日式床"
      - "J000000": "太空舱"
      - "L000000": "特大床"
      - "M000000": "地面床铺"
      - "2000000": "双床"
      - "3000000": "三床"
      - "4000000": "四床"
      - "5000000": "大/双床"
      - "B000000": "上下铺"
      - "C000000": "通铺"
      - "G000000": "拼床"
      - "K000000": "小型双人床"
      - "Q000000": "伸缩床"
      - "R000000": "两用长椅"
      - "T000000": "简易折叠床"
      - "U000000": "壁柜折叠床"
      - "V000000": "婴儿床"
    en_us:
      - "1000000": "Queen Bed"
      - "A000000": "Twin Bed"
      - "D000000": "Tatami"
      - "E000000": "Water Bed"
      - "F000000": "Round Bed"
      - "H000000": "Kang Bed"
      - "I000000": "Sofa Bed"
      - "N000000": "Capsule Bed"
      - "P000000": "Full Bed"
      - "S000000": "Futon"
      - "J000000": "Bunk Bed"
      - "L000000": "King Bed"
      - "M000000": "Floor Mattress"
      - "2000000": "Double Bed"
      - "3000000": "Triple Bed"
      - "4000000": "Quadruple Bed"
      - "5000000": "King Bed"
      - "B000000": "Bunk Bed"
      - "C000000": "Dormitory Bed"
      - "G000000": "Combination Bed"
      - "K000000": "Twin Bed"
      - "Q000000": "Trundle Bed"
      - "R000000": "Day Bed"
      - "T000000": "Rollaway Bed"
      - "U000000": "MurphyBed"
      - "V000000": "Crib"