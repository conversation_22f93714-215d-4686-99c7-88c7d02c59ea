package com.tiangong.product.task;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.tiangong.dto.common.CompanyDTO;
import com.tiangong.dto.product.response.HotelIdDto;
import com.tiangong.hotel.remote.HotelHeatRemote;
import com.tiangong.hotel.req.HotelAvgPriceReq;
import com.tiangong.keys.RedisKey;
import com.tiangong.product.dto.mongo.SupplyHotelLowestPrice;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.util.DateUtilX;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
 * 初始化需要计算酒店热度酒店平均房价的酒店id到缓存任务
 */
@Slf4j
@Component
public class InitCalculateHotelAvgPriceHotelIdToRedisTask {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private HotelHeatRemote hotelHeatRemote;

    @XxlJob("initCalculateHotelAvgPriceHotelIdToRedisTask")
    public void initCalculateHotelAvgPriceHotelIdToRedisTask() {
        try {
            XxlJobHelper.log("执行初始化需要计算酒店热度酒店平均房价的酒店id到缓存任务开始");

            // 初始化需要计算酒店热度酒店平均房价的酒店id
            initCalculateHotelAvgPriceHotelId();

            // 清除旧的数据
            deleteHotelAvgPrice();

            XxlJobHelper.log("执行初始化需要计算酒店热度酒店平均房价的酒店id到缓存任务结束");
        } catch (Exception e) {
            log.error("执行初始化需要计算酒店热度酒店平均房价的酒店id到缓存任务异常", e);
            XxlJobHelper.log("执行初始化需要计算酒店热度酒店平均房价的酒店id到缓存任务异常", e);
        }
    }

    /**
     * 初始化需要计算酒店热度酒店平均房价的酒店id
     */
    public void initCalculateHotelAvgPriceHotelId() {
        int pageSize = 10;

        // 构建匹配条件
        Criteria criteria = Criteria.where("merchantSource").is(CompanyDTO.COMPANY_NAME)
                .and("merchantCode").is(CompanyDTO.COMPANY_CODE)
                .and("saleDate").gte(DateUtilX.dateToString(DateUtilX.getAddDay(DateUtilX.getCurrentDate(), 1)))
                .lte(DateUtilX.dateToString(DateUtilX.getAddDay(DateUtilX.getCurrentDate(), 5)));

        while (true) {
            Aggregation aggregation = Aggregation.newAggregation(
                    Aggregation.match(criteria),
                    Aggregation.group("hotelId"), // 按 hotelId 分组
                    Aggregation.sort(Sort.by(Sort.Direction.ASC, "_id")),
                    Aggregation.limit(pageSize)
            );

            List<HotelIdDto> result = mongoTemplate.aggregate(aggregation, SupplyHotelLowestPrice.class, HotelIdDto.class).getMappedResults();

            if (CollUtil.isEmpty(result)){
                break;
            }

            List<String> hotelIds = result.stream()
                    .map(hotel -> hotel.get_id().toString())
                    .collect(Collectors.toList());

            // 结果不为空且结果等于pageSize,则设置游标为当前页最后一条数据的id
            if (CollectionUtil.isNotEmpty(hotelIds) && hotelIds.size() >= pageSize) {
                criteria.and("hotelId").gt(String.valueOf(hotelIds.get(pageSize - 1)));
            }

            RedisTemplateX.setAdd(RedisKey.CALCULATE_HOTEL_AVG_PRICE_HOTEL_ID_KEY, hotelIds.toArray(new String[0]));

            // 提前释放内存
            hotelIds.clear();

        }
    }


    /**
     * 删除酒店平均价格
     */
    private void deleteHotelAvgPrice() {
        HotelAvgPriceReq hotelAvgPriceReq = new HotelAvgPriceReq();
        hotelHeatRemote.delHotelAvgPrice(hotelAvgPriceReq);
    }
}
