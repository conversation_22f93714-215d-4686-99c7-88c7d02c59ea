package com.tiangong.product.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.tiangong.ProductInitializer;
import com.tiangong.common.Constant;
import com.tiangong.common.Response;
import com.tiangong.dis.common.CommonUtils;
import com.tiangong.dis.common.DisCommonUtils;
import com.tiangong.dis.dto.*;
import com.tiangong.dis.enums.DhubReturnCodeEnum;
import com.tiangong.dis.enums.RoomStatusEnum;
import com.tiangong.dto.hotel.base.*;
import com.tiangong.dto.product.*;
import com.tiangong.dto.product.TaxDetailDto;
import com.tiangong.dto.product.request.ChildrenInfo;
import com.tiangong.dto.product.request.ProductDetailRequest;
import com.tiangong.dto.product.response.*;
import com.tiangong.dto.product.response.BedInfoDto;
import com.tiangong.dto.product.response.BedTypesDetailDto;
import com.tiangong.enums.*;
import com.tiangong.exception.SysException;
import com.tiangong.finance.OrgDTO;
import com.tiangong.hotel.BaseHotelInfoUrl;
import com.tiangong.hotel.remote.HotelRemote;
import com.tiangong.initializer.CommonInitializer;
import com.tiangong.keys.RedisKey;
import com.tiangong.organization.remote.dto.AgentAccountConfig;
import com.tiangong.organization.remote.dto.SupplierAddDTO;
import com.tiangong.product.config.BedNameConfig;
import com.tiangong.product.convert.CommonConvert;
import com.tiangong.product.domain.areatadatimezone.AreaDataTimeZoneReqDTO;
import com.tiangong.product.domain.req.HotelTimeZoneRequest;
import com.tiangong.product.domain.resp.*;
import com.tiangong.product.dto.ProTaxDetailDTO;
import com.tiangong.product.dto.ProTaxRuleConfigDTO;
import com.tiangong.product.dto.ProductDTO;
import com.tiangong.product.exception.BusinessValidationException;
import com.tiangong.product.exception.FinancialException;
import com.tiangong.product.service.HotelProductService;
import com.tiangong.product.service.ProOrgHotelAvailableService;
import com.tiangong.product.service.ProductInfoService;
import com.tiangong.product.util.QueryUtil;
import com.tiangong.product.util.RateUtil;
import com.tiangong.product.util.TimeZoneUtil;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.supply.direct.remote.SupplyDirectShubProductRemote;
import com.tiangong.supply.direct.remote.dto.*;
import com.tiangong.supply.direct.remote.request.QueryProductInfoRequest;
import com.tiangong.supply.direct.remote.request.RoomGuestNumber;
import com.tiangong.util.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.stream.Collectors.*;

/**
 * 用途：产品信息查询
 */
@Slf4j
@Service
public class HotelProductServiceImpl implements HotelProductService {

    @Autowired
    private QueryUtil queryUtil;

    @Autowired
    private HotelRemote hotelRemote;

    @Autowired
    private SlsLoggerUtil slsLoggerUtil;

    @Autowired
    private BedNameConfig bedNameConfig;

    @Autowired
    private ProductInfoService productInfoService;

    @Resource(name = "processingProductInfoExecutor")
    private ThreadPoolTaskExecutor processingProductInfoExecutor;

    @Autowired
    private SupplyDirectShubProductRemote supplyDirectShubProductRemote;

    @Autowired
    private ProOrgHotelAvailableService proOrgHotelAvailableService;

    @Override
    public ResponseResult<DhubProductDetailInfoResponse> queryHotelProductDetail(ProductDetailRequest request) {
        // 检查酒店黑名单
        if (isHotelBlacklisted(request)) {
            return new ResponseResult<>(DhubReturnCodeEnum.HOTEL_IS_NOT_AVAILABLE.no, DhubReturnCodeEnum.HOTEL_IS_NOT_AVAILABLE.description);
        }

        // 查询供应商和本地产品数据
        Date start1 = new Date();
        DhubProductDetailInfoResponse productDetail = queryDirectProduct(request);
        if (productDetail == null) {
            return new ResponseResult<>(DhubReturnCodeEnum.SUCCESS.no, null);
        }
        saveSlsLog("排查慢问题：查询供应商和本地产品数据，productDetail=" + JSONUtil.toJsonStr(productDetail), start1, request.getRequestId());

        productDetail.setHotelId(request.getHotelId());
        if (CollUtil.isEmpty(productDetail.getRoomItems())) {
            return new ResponseResult<>(DhubReturnCodeEnum.SUCCESS.no, null);
        }

        return new ResponseResult<>(DhubReturnCodeEnum.SUCCESS.no, null, productDetail);
    }

    /**
     * 检查酒店黑名单
     */
    private boolean isHotelBlacklisted(ProductDetailRequest request) {
        return Boolean.TRUE.equals(RedisTemplateX.hashHasKey(RedisKey.AGENT_HOTEL_BLACKLISTED, request.getAgentCode() + "_" + request.getHotelId()));
    }

    /**
     * 查询产品数据
     */
    private DhubProductDetailInfoResponse queryDirectProduct(ProductDetailRequest request) {
        DhubProductDetailInfoResponse response = new DhubProductDetailInfoResponse();
        response.setHotelId(request.getHotelId());
        response.setRoomItems(new ArrayList<>());

        // 处理供应商
        Map<String, SupplierAddDTO> supplyMap = handleSuppliers(request);
        if (supplyMap == null || supplyMap.isEmpty()) {
            return null;
        }
        // 供应商酒店黑白名单筛选
        List<String> filterWhiteList = proOrgHotelAvailableService.filterSuppliersByHotelAvailableRule(request.getHotelId(), new ArrayList<>(supplyMap.keySet()));
        if (CollUtilX.isEmpty(filterWhiteList)) {
            return null;
        }

        // 根据筛选后的白名单过滤supplyMap，只保留在筛选后白名单中的供应商
        Iterator<Map.Entry<String, SupplierAddDTO>> iterator = supplyMap.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, SupplierAddDTO> entry = iterator.next();
            if (!filterWhiteList.contains(entry.getKey())) {
                iterator.remove();
            }
        }
        // 直连供应商
        Map<String, SupplierAddDTO> apiSupplyMap = supplyMap.values().stream()
                .filter(item -> item.getSupplierType() != null
                        && SupplierTypeEnum.API.getKey() == item.getSupplierType()
                        && StrUtilX.isNotEmpty(item.getSupplierCode()))
                .collect(Collectors.toMap(
                        SupplierAddDTO::getSupplierCode, // key
                        item -> item // value
                ));
        // 自签供应商
        Map<String, SupplierAddDTO> selfSupplyMap = supplyMap.values().stream()
                .filter(item -> item.getSupplierType() != null
                        && SupplierTypeEnum.SELF.getKey() == item.getSupplierType()
                        && StrUtilX.isNotEmpty(item.getSupplierCode()))
                .collect(Collectors.toMap(
                        SupplierAddDTO::getSupplierCode, // key
                        item -> item // value
                ));

        // 获取客户配置
        AgentAccountConfig agentConfig = getAgentConfig(request.getAgentCode());
        request.setSaleCurrency(agentConfig.getSettlementCurrency());

        // 获取商家信息
        OrgDTO orgInfo = CommonInitializer.getOrgInfo();
        int orgCurrency = Integer.parseInt(orgInfo.getOrgCurrency());

        try {
            return CommonUtils.parallelHandleTriple(
                    response,
                    request,
                    () -> agreement(request, selfSupplyMap, orgCurrency, agentConfig),
                    () -> direct(request, apiSupplyMap, orgCurrency, agentConfig),
                    () -> handleTimeZoneInfo(request),
                    this::detailMerge,
                    processingProductInfoExecutor
            );
        } catch (Exception e) {
            log.error("查询直连产品异常", e);
            return response;
        }
    }

    /**
     * 处理供应商
     */
    private Map<String, SupplierAddDTO> handleSuppliers(ProductDetailRequest request) {
        // 校验客户编码
        validateAgentCode(request);

        // 根据分销商编码查询供应商白名单
        List<String> whiteList = getSupplierWhiteList(request.getAgentCode());
        if (CollUtilX.isEmpty(whiteList)) {
            return null;
        }

        // 从缓存中获取供应商信息
        List<SupplierAddDTO> suppliers = getSuppliersFromCache(whiteList);
        // 校验供应商存在
        validateSuppliersExist(suppliers);

        // 判断入住日期是否超过1天
        if (isCheckInDateInvalid(request.getCheckInDate())) {
            // 记录日志
            logEarlyCheckInAttempt(request);
            return null;
        }

        // 过滤无效供应商
        return filterValidSuppliers(suppliers, request.getHotelId(), request.getCheckInDate());
    }

    /**
     * 校验客户编码
     */
    private void validateAgentCode(ProductDetailRequest request) {
        if (StrUtilX.isEmpty(request.getAgentCode())) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_AGENTWHITELIST);
        }
    }

    /**
     * 根据分销商编码查询供应商白名单
     */
    private List<String> getSupplierWhiteList(String agentCode) {
        String supplyCodes = (String) RedisTemplateX.hashGet(RedisKey.AGENT_SUPPLY_KEY, agentCode);
        List<String> supplyCodeList = StrUtilX.stringToList(supplyCodes, ",");
        if (CollUtilX.isEmpty(supplyCodeList)) {
            return null;
        }
        return supplyCodeList;
    }

    /**
     * 从缓存中获取供应商信息
     */
    private List<SupplierAddDTO> getSuppliersFromCache(List<String> supplierCodes) {
        return StrUtilX.parseObject(RedisTemplateX.hMultiGet(RedisKey.SUPPLY_ACCOUNT_CONFIG, supplierCodes), new TypeReference<List<SupplierAddDTO>>() {
        });
    }

    /**
     * 校验供应商存在
     */
    private void validateSuppliersExist(List<SupplierAddDTO> suppliers) {
        if (CollUtil.isEmpty(suppliers)) {
            log.error("供应商缓存不存在");
            throw new SysException(ErrorCodeEnum.SUPPLY_CHECK_NOT_EXIST);
        }
    }

    /**
     * 判断入住日期是否超过1天
     */
    private boolean isCheckInDateInvalid(String checkInDate) {
        return DateUtilX.getDay(DateUtilX.stringToDate(checkInDate), DateUtilX.getCurrentDate()) > 1;
    }

    /**
     * 记录日志
     */
    private void logEarlyCheckInAttempt(ProductDetailRequest request) {
        Map<String, String> map = new HashMap<>();
        map.put(SlsEnum.LEVEL.getType(), SlsLogLevelEnum.INFO.getLevel());
        map.put(SlsEnum.NAME.getType(), "queryProductInfo");
        map.put(SlsEnum.MESSAGE.getType(), "查询供应商产品，入住时间小于当前时间两天");

        Date currentDate = DateUtilX.getCurrentDate();
        map.put("request", JSONUtil.toJsonStr(request));
        map.put("start", DateUtilX.dateToString(currentDate, DateUtilX.hour_ms_format));
        map.put("data", "currentDate=" + DateUtilX.dateToString(currentDate) + "checkInData=" + request.getCheckInDate());
        slsLoggerUtil.saveLog(map, "isCheckInDateInvalid", "tingong-product-server");
    }

    /**
     * 过滤无效供应商
     */
    private Map<String, SupplierAddDTO> filterValidSuppliers(List<SupplierAddDTO> suppliers, Long hotelId, String checkInDate) {
        // 预计算固定参数
        final LocalTime currentTime = LocalTime.now();
        final long daysBetween = DateUtilX.getDay(DateUtilX.stringToDate(checkInDate), DateUtilX.getCurrentDate());

        Map<String, SupplierAddDTO> validSuppliers = new HashMap<>(calculateInitialCapacity(suppliers.size()));
        for (SupplierAddDTO supplier : suppliers) {
            if (isValidSupplier(supplier, hotelId, daysBetween, currentTime)) {
                String supplierCode = supplier.getSupplierCode();
                if (validSuppliers.containsKey(supplierCode)) {
                    log.warn("发现重复供应商编码: {}，已覆盖旧值", supplierCode);
                }
                validSuppliers.put(supplierCode, supplier);
            }
        }
        return validSuppliers;
    }

    /**
     * 计算HashMap初始容量（避免扩容）
     * 公式: (expectedSize / 0.75) + 1
     */
    private static int calculateInitialCapacity(int expectedSize) {
        return (int) Math.ceil(expectedSize / 0.75) + 1;
    }

    /**
     * 判断供应商是否可用
     */
    private boolean isValidSupplier(SupplierAddDTO supplier, Long hotelId, long days, LocalTime now) {
        return isHotelValid(supplier, hotelId)
                && isSupplierAvailable(supplier)
                && meetsTimingCriteria(supplier, days, now);
    }

    /**
     * 判断酒店是否匹配
     */
    private boolean isHotelValid(SupplierAddDTO supplier, Long hotelId) {
        return supplier.getSupplierForm() == null || supplier.getHotelId() == null ||
                supplier.getSupplierForm() != 1 || hotelId.equals(supplier.getHotelId());
    }

    /**
     * 判断供应商是否可用
     */
    private boolean isSupplierAvailable(SupplierAddDTO supplier) {
        return supplier.getAvailableStatus() != null && supplier.getAvailableStatus() == 1;
    }

    /**
     * 判断供应商是否支持早订
     */
    private boolean meetsTimingCriteria(SupplierAddDTO supplier, long days, LocalTime now) {
        if (supportsEarlyMorning(supplier)) {
            return days < 1 || (now.isAfter(LocalTime.MIDNIGHT) && now.isBefore(LocalTime.of(6, 0)));
        }
        return days < 1;
    }

    /**
     * 判断供应商是否支持早订
     */
    private boolean supportsEarlyMorning(SupplierAddDTO supplier) {
        return supplier.getIsSupportEarlyMorningRoomOrder() != null
                && supplier.getIsSupportEarlyMorningRoomOrder() == 1;
    }

    /**
     * 获取客户配置
     */
    private AgentAccountConfig getAgentConfig(String agentCode) {
        Object configObj = RedisTemplateX.hashGet(RedisKey.AGENT_ACCOUNT_CONFIG, agentCode);
        if (configObj == null) {
            throw new SysException(ErrorCodeEnum.AGENT_INFO_NOT_EXIST);
        }
        return JSON.parseObject(configObj.toString(), AgentAccountConfig.class);
    }


    /**
     * 自签产品（优化版）
     */
    private DhubProductDetailInfoResponse agreement(ProductDetailRequest request,
                                                    Map<String, SupplierAddDTO> supplyMap,
                                                    Integer orgCurrency,
                                                    AgentAccountConfig agentConfig) {
        final Date startTime = new Date();
        DhubProductDetailInfoResponse response = new DhubProductDetailInfoResponse();

        try {
            // 判断供应商集合是否为空
            if (supplyMap == null || supplyMap.isEmpty()) {
                return response;
            }
            // 判断是否需要跳过处理
            if (shouldSkipProcessing(request)) {
                return response;
            }

            // 初始化响应基础信息
            initResponseBaseInfo(response, request);

            // 并行处理获取产品ID集合和处理价格和税费逻辑
            List<CompletableFuture<?>> futures = new ArrayList<>();

            // 获取产品ID集合
            CompletableFuture<List<String>> productIdsFuture = CompletableFuture.supplyAsync(() -> fetchProductIds(request, agentConfig, supplyMap), processingProductInfoExecutor);
            futures.add(productIdsFuture);

            // 处理价格和税费逻辑
            CompletableFuture<ProductFinancialData> financialDataFuture = productIdsFuture.thenApplyAsync(productIds -> {
                if (CollUtil.isEmpty(productIds)) {
                    return null;
                }
                return processFinancialData(productIds, orgCurrency, agentConfig);
            }, processingProductInfoExecutor);
            futures.add(financialDataFuture);

            // 等待所有任务完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

            // 获取产品ID集合结果
            List<String> productIds = productIdsFuture.join();
            if (CollUtil.isEmpty(productIds)) {
                return response;
            }

            // 获取价格和税费逻辑结果
            ProductFinancialData financialData = financialDataFuture.join();
            if (financialData == null || !financialData.isValid()) {
                logExchangeRateError(orgCurrency, agentConfig);
                return response;
            }

            // 获取酒店信息
            Date start2 = new Date();
            HotelInfoCollectionDTO hotelInfo = fetchHotelInfo(request);
            if (hotelInfo == null || CollUtilX.isEmpty(hotelInfo.getRooms())) {
                saveSlsLog("查询自签产品，获取酒店信息为空，req=" + JSONUtil.toJsonStr(request) + "，resp=" + JSONUtil.toJsonStr(hotelInfo), start2, request.getRequestId());
                return response;
            }
            Map<Long, String> roomNameMap = hotelInfo.getRooms().stream().collect(toMap(RoomtypeDTO::getRoomtypeId, RoomtypeDTO::getRoomtypeName));
            financialData.setRoomNameMap(roomNameMap);
            financialData.setRequest(request);
            financialData.setSupplyMap(supplyMap);

            // 组装房间数据
            assembleRoomItems(response, request, productIds, financialData);
        } catch (Exception e) {
            log.error("自签产品处理异常", e);
            saveSlsLog("排查慢问题：自签产品转换异常", startTime, request.getRequestId());
        } finally {
            saveSlsLog("排查慢问题：查询自签产品信息", startTime, request.getRequestId());
        }
        return response;
    }

    /**
     * 判断是否需要跳过处理
     */
    private boolean shouldSkipProcessing(ProductDetailRequest request) {
        return request.getOnlyHourRoom() == 1
                || !isContractHotelExists(request.getHotelId());
    }

    /**
     * 判断是否存在自签酒店
     */
    private boolean isContractHotelExists(Long hotelId) {
        return RedisTemplateX.hGet(RedisKey.CONTRACT_HOTEL_KEY, String.valueOf(hotelId)) != null;
    }

    /**
     * 初始化响应基础信息
     */
    private void initResponseBaseInfo(DhubProductDetailInfoResponse response, ProductDetailRequest request) {
        response.setHotelId(request.getHotelId());
        response.setRoomItems(new ArrayList<>());
    }

    /**
     * 获取产品ID集合
     */
    private List<String> fetchProductIds(ProductDetailRequest request,
                                         AgentAccountConfig agentConfig,
                                         Map<String, SupplierAddDTO> supplyMap) {
        // 构建产品ID查询请求
        QueryProductIdsRequest queryRequest = buildProductIdQuery(request, agentConfig, new ArrayList<>(supplyMap.keySet()));

        // 查询产品ID集合
        List<HotelProductIdsResponse> productResponses = productInfoService.queryProductIdsByHotelId(queryRequest);

        // 验证海外业务房间客人关系为必填项
        validateOverseasRequirements(agentConfig, request.getRoomGuestNumbers());

        return productResponses.stream()
                .map(HotelProductIdsResponse::getProductId)
                .collect(Collectors.toList());
    }

    /**
     * 构建产品ID查询请求
     */
    private QueryProductIdsRequest buildProductIdQuery(ProductDetailRequest request,
                                                       AgentAccountConfig agentConfig,
                                                       List<String> supplyCodes) {
        QueryProductIdsRequest query = new QueryProductIdsRequest();
        query.setHotelId(request.getHotelId().toString());
        query.setCompanyCode(request.getCompanyCode());
        query.setPriceType(getPriceType(agentConfig));
        query.setSupplyCodes(supplyCodes);
        return query;
    }

    /**
     * 获取价格类型
     */
    private Integer getPriceType(AgentAccountConfig config) {
        return config.getDomesticOrOverseas() == 1 ? 0 : null;
    }

    /**
     * 验证海外业务房间客人关系为必填项
     */
    private void validateOverseasRequirements(AgentAccountConfig config, List<?> roomGuests) {
        if (config.getDomesticOrOverseas() == 2 && CollUtil.isEmpty(roomGuests)) {
            log.error("海外业务房间客人关系为必填项");
            throw new BusinessValidationException(ErrorCodeEnum.INVALID_INPUTPARAM.errorCode, ErrorCodeEnum.INVALID_INPUTPARAM.errorDesc);
        }
    }

    /**
     * 处理价格和税费逻辑
     */
    private ProductFinancialData processFinancialData(List<String> productIds, Integer orgCurrency, AgentAccountConfig config) {
        ProductFinancialData data = new ProductFinancialData();
        data.setProductIds(productIds);
        data.setAgentConfig(config);

        // 获取汇率信息
        data.setOrgCurrency(orgCurrency);
        data.setExchangeRate(validateExchangeRate(orgCurrency, config));

        // 获取产品基础信息
        Map<String, ProductDTO> products = getProductMap(productIds);
        data.setProducts(products);

        // 处理税费规则
        processTaxRules(data, products);

        return data;
    }

    /**
     * 获取汇率
     */
    private BigDecimal validateExchangeRate(Integer orgCurrency, AgentAccountConfig config) {
        BigDecimal rate = RateUtil.getRate(orgCurrency, config.getSettlementCurrency());
        if (rate == null || rate.compareTo(BigDecimal.ZERO) == 0) {
            logExchangeRateError(orgCurrency, config);
            throw new FinancialException(ErrorCodeEnum.EXCHANGE_RATE_IS_NOT_ALREADT.errorCode, ErrorCodeEnum.EXCHANGE_RATE_IS_NOT_ALREADT.errorDesc);
        }
        return rate;
    }

    /**
     * 汇率错误日志
     */
    private void logExchangeRateError(Integer orgCurrency, AgentAccountConfig config) {
        String errorMsg = String.format("商家转客户汇率不存在，orgCurrency=%s，settlementCurrency=%s",
                orgCurrency, config.getSettlementCurrency());
        log.error(errorMsg);

        Map<String, String> logMap = new HashMap<>();
        logMap.put(SlsEnum.LEVEL.getType(), SlsLogLevelEnum.ERROR.getLevel());
        logMap.put(SlsEnum.NAME.getType(), "exchange-rate-error");
        logMap.put(SlsEnum.MESSAGE.getType(), errorMsg);
        slsLoggerUtil.saveLog(logMap, "financial-processing", "tiangong-product-server");
    }

    /**
     * 处理税费规则
     */
    private void processTaxRules(ProductFinancialData data, Map<String, ProductDTO> products) {
        // 提取税规则
        Map<String, String> taxRuleMappings = extractTaxRuleMappings(products);
        data.setTaxRuleMappings(taxRuleMappings);
        // 提供币种信息
        Map<String, Integer> currencyMappings = extractCurrencyMappings(products);
        data.setCurrencyMappings(currencyMappings);
        Map<String, ProTaxRuleConfigDTO> taxRules = getTaxRules(taxRuleMappings.values());
        data.setTaxRules(taxRules);
    }

    /**
     * 提取税规则
     */
    private Map<String, String> extractTaxRuleMappings(Map<String, ProductDTO> products) {
        return products.values().stream()
                .filter(p -> p.getPriceType() == 1 && p.getTaxRuleConfigId() != null)
                .collect(Collectors.toMap(
                        p -> String.valueOf(p.getProductId()),
                        p -> String.valueOf(p.getTaxRuleConfigId()))
                );
    }

    /**
     * 提取币种映射
     */
    private Map<String, Integer> extractCurrencyMappings(Map<String, ProductDTO> products) {
        return products.values().stream()
                .filter(p -> p.getCurrency() != null)
                .collect(Collectors.toMap(
                        p -> String.valueOf(p.getProductId()),
                        ProductDTO::getCurrency)
                );
    }

    /**
     * 获取税费规则
     */
    private Map<String, ProTaxRuleConfigDTO> getTaxRules(Collection<String> taxRuleIds) {
        if (CollUtil.isEmpty(taxRuleIds)) {
            return Collections.emptyMap();
        }

        List<Object> ruleObjects = RedisTemplateX.hMultiGet(RedisKey.proTaxRuleConfigKey, new ArrayList<>(taxRuleIds));
        List<ProTaxRuleConfigDTO> rules = ruleObjects.stream()
                .filter(Objects::nonNull)
                .map(obj -> JSON.parseObject(obj.toString(), ProTaxRuleConfigDTO.class))
                .collect(Collectors.toList());

        return rules.stream()
                .collect(Collectors.toMap(
                        rule -> String.valueOf(rule.getId()),
                        Function.identity(),
                        (existing, replacement) -> existing));
    }

    /**
     * 处理时区信息
     */
    private DhubProductDetailInfoResponse handleTimeZoneInfo(ProductDetailRequest request) {
        Date start = new Date();
        try {
            HotelInfoCollectionDTO hotel = fetchHotelInfo(request);
            if (hotel != null && hotel.getLocation() != null) {
                // 查询时区
                String timeZone = queryTimeZone(hotel.getLocation(), request.getCheckInDate());

                DhubProductDetailInfoResponse response = new DhubProductDetailInfoResponse();
                response.setTimeZone(timeZone);
                return response;
            }
        } catch (Exception e) {
            log.error("时区信息处理异常", e);
        } finally {
            saveSlsLog("排查慢问题：查询酒店时区", start, request.getRequestId());
        }
        return null;
    }

    /**
     * 获取酒店信息
     */
    private HotelInfoCollectionDTO fetchHotelInfo(ProductDetailRequest request) {
        try {
            Response<HotelInfoCollectionDTO> response = hotelRemote.queryHotelInfo(buildHotelInfoRequest(request));
            if (response.isSuccess() && response.getModel() != null) {
                return response.getModel();
            }
        } catch (Exception e) {
            log.error("酒店信息查询异常", e);
        }
        return null;
    }

    /**
     * 构建酒店信息查询请求
     */
    private HotelInfoCollectionReq buildHotelInfoRequest(ProductDetailRequest request) {
        HotelInfoCollectionReq hotelInfoReq = new HotelInfoCollectionReq();
        hotelInfoReq.setHotelIds(Collections.singletonList(request.getHotelId()));
        if (StrUtilX.isNotEmpty(request.getLanguage())) {
            hotelInfoReq.setLanguageType(request.getLanguage());
        } else {
            hotelInfoReq.setLanguageType(LanguageTypeEnum.zh_CN.getValue());
        }
        List<String> settings = new ArrayList<>();
        settings.add(BaseHotelInfoUrl.rooms);
        settings.add(BaseHotelInfoUrl.location);
        hotelInfoReq.setSettings(settings);
        return hotelInfoReq;
    }

    /**
     * 查询时区
     */
    private String queryTimeZone(LocationDTO location, String checkInDate) {
        AreaDataTimeZoneReqDTO dto = new AreaDataTimeZoneReqDTO();
        dto.setCountryCode(location.getCountry());
        dto.setProvinceCode(location.getProvince());
        dto.setDateTime(checkInDate);
        return TimeZoneUtil.getDateTimeZone(dto).getModel();
    }

    /**
     * 组装房间数据
     */
    private void assembleRoomItems(DhubProductDetailInfoResponse response,
                                   ProductDetailRequest request,
                                   List<String> productIds,
                                   ProductFinancialData financialData) {
        List<String> saleDates = getSaleDates(request);
        // 生成产品日期key
        List<String> productDateKeys = generateProductDateKeys(productIds, saleDates);

        // 获取价格数据
        Map<String, ProductBasePriceAndRoomStatusDTO> priceMap = getPriceData(request, productDateKeys, financialData);

        // 获取产品id映射
        financialData.setProductIdsMapping(queryUtil.getCacheProductIdsWithUUID(productIds, request.getHotelId()));

        List<DhubRoomItemDto> roomItems = buildRoomItems(
                request.getRoomGuestNumbers(),
                saleDates,
                priceMap,
                financialData);

        response.setRoomItems(roomItems);
    }

    /**
     * 获取日期
     */
    public List<String> getSaleDates(ProductDetailRequest request) {
        List<String> saleStatus = Lists.newArrayList();
        long day = DateUtilX.getDay(DateUtilX.stringToDate(request.getCheckInDate()), DateUtilX.stringToDate(request.getCheckOutDate()));
        for (int i = 0; i < day; i++) {
            saleStatus.add(DateUtilX.dateToString(DateUtilX.addDate(DateUtilX.stringToDate(request.getCheckInDate()), i)));
        }
        return saleStatus;
    }

    /**
     * 生成产品日期key
     */
    private List<String> generateProductDateKeys(List<String> productIds, List<String> dates) {
        return productIds.stream()
                .flatMap(id -> dates.stream().map(date -> id + Constant.SEPARATOR + date))
                .collect(Collectors.toList());
    }

    /**
     * 获取价格数据
     */
    private Map<String, ProductBasePriceAndRoomStatusDTO> getPriceData(
            ProductDetailRequest request,
            List<String> productDateKeys,
            ProductFinancialData financialData) {

        try {
            return getRoomStatusAndPrice(
                    request.getCheckInDate(),
                    request.getCompanyCode(),
                    productDateKeys,
                    financialData.getProducts(),
                    financialData.getAgentConfig(),
                    financialData.getTaxDetails(),
                    financialData.getTaxRuleMappings(),
                    financialData.getTaxRules(),
                    financialData.getCurrencyMappings(),
                    financialData.getOrgCurrency(),
                    financialData.getExchangeRate()
            );
        } catch (Exception e) {
            log.error("价格数据获取异常", e);
            return Collections.emptyMap();
        }
    }

    /**
     * 产品每日报价信息(加幅后)
     */
    public Map<String, ProductBasePriceAndRoomStatusDTO> getRoomStatusAndPrice(String checkInDate,
                                                                               String companyCode,
                                                                               List<String> productAndSaleDates,
                                                                               Map<String, ProductDTO> productMap,
                                                                               AgentAccountConfig agentAccountConfig,
                                                                               Map<Integer, RoomTaxDetail> roomTaxDetailMap,
                                                                               Map<String, String> productIdAndTaxRuleConfigId,
                                                                               Map<String, ProTaxRuleConfigDTO> taxRuleMap,
                                                                               Map<String, Integer> productIdAndCurrencyMap,
                                                                               Integer orgCurrency,
                                                                               BigDecimal orgToAgentRate) {

        List<Object> objects = RedisTemplateX.hMultiGet(RedisKey.productBasePriceAndRoomStatusKey, productAndSaleDates);
        if (CollUtilX.isEmpty(objects)) {
            return new HashMap<>();
        }

        List<ProductBasePriceAndRoomStatusDTO> productBasePriceAndRoomStatusDTOS = StrUtilX.parseObject(objects, new TypeReference<List<ProductBasePriceAndRoomStatusDTO>>() {
        });
        if (CollUtilX.isEmpty(productBasePriceAndRoomStatusDTOS)) {
            return new HashMap<>();
        }
        List<String> salePriceSaleKeys = productAndSaleDates.stream().map(e -> companyCode.concat(Constant.SEPARATOR).concat(e)).collect(Collectors.toList());

        List<Object> objectslist = RedisTemplateX.hMultiGet(RedisKey.productSalePriceKey, salePriceSaleKeys);
        if (CollUtilX.isEmpty(objectslist)) {
            return new HashMap<>();
        }

        List<ProductSaleIncreaseDTO> productSaleIncreaseDTOS = StrUtilX.parseObject(objectslist, new TypeReference<List<ProductSaleIncreaseDTO>>() {
        });
        if (CollUtilX.isEmpty(productSaleIncreaseDTOS)) {
            return new HashMap<>();
        }

        Map<String, ProductSaleIncreaseDTO> saleIncreaseMap = new HashMap<>();
        for (ProductSaleIncreaseDTO dto : productSaleIncreaseDTOS) {
            if (dto != null && StrUtilX.isNotEmpty(dto.getProductId()) && StrUtilX.isNotEmpty(dto.getSaleDate())) {
                // 创建键，使用产品ID与销售日期的组合
                String key = String.valueOf(dto.getProductId()).concat(Constant.SEPARATOR).concat(dto.getSaleDate());
                // 将 dto 放入 map 中，如果存在重复键，则覆盖值
                saleIncreaseMap.put(key, dto);
            }
        }
        if (saleIncreaseMap.isEmpty()) {
            return new HashMap<>();
        }

        return productBasePriceAndRoomStatusDTOS.stream()
                .filter(DisCommonUtils::filterAgreeProduct) // 过滤符合条件的产品
                .map(e -> {
                    // 调用 priceMakup 方法并捕获可能返回的 null
                    return priceMakup(checkInDate, saleIncreaseMap, e,
                            productIdAndTaxRuleConfigId, taxRuleMap, productIdAndCurrencyMap,
                            agentAccountConfig, roomTaxDetailMap, orgCurrency, orgToAgentRate, productMap);
                })
                .filter(i -> i != null && i.getBasePrice() != null && i.getBasePrice().doubleValue() > 0) // 过滤不符合条件的项
                .collect(Collectors.toMap(
                        e -> String.valueOf(e.getProductId()).concat(Constant.SEPARATOR).concat(e.getSaleDate()),
                        Function.identity(),
                        (v1, v2) -> v2 // 合并函数
                ));
    }

    /**
     * 自签加幅
     */
    private ProductBasePriceAndRoomStatusDTO priceMakup(String checkInDate,
                                                        Map<String, ProductSaleIncreaseDTO> map,
                                                        ProductBasePriceAndRoomStatusDTO productBasePriceAndRoomStatusDTO,
                                                        Map<String, String> productIdAndTaxRuleConfigId,
                                                        Map<String, ProTaxRuleConfigDTO> taxRuleMap,
                                                        Map<String, Integer> productIdAndCurrencyMap,
                                                        AgentAccountConfig agentAccountConfig,
                                                        Map<Integer, RoomTaxDetail> taxDetailDtoMap,
                                                        Integer orgCurrency,
                                                        BigDecimal orgToAgentRate,
                                                        Map<String, ProductDTO> productMap) {
        ProductSaleIncreaseDTO productSaleIncreaseDTO = map.get(productBasePriceAndRoomStatusDTO.getRedisKey());

        BigDecimal basePrice = Objects.isNull(productBasePriceAndRoomStatusDTO.getBasePrice()) ? BigDecimal.ZERO : productBasePriceAndRoomStatusDTO.getBasePrice();
        productBasePriceAndRoomStatusDTO.setTmpBasePrice(basePrice);

        Integer productId = productBasePriceAndRoomStatusDTO.getProductId();
        // 根据productId获取对应的税费规则ID
        String taxRuleConfigId = productIdAndTaxRuleConfigId.get(String.valueOf(productId));
        ProTaxRuleConfigDTO proTaxRuleConfigDTO = null;
        // 获取具体税费规则
        if (StrUtilX.isNotEmpty(taxRuleConfigId)) {
            proTaxRuleConfigDTO = taxRuleMap.get(taxRuleConfigId);
        }

        Integer b2bAdjustmentType;
        BigDecimal b2bModifiedAmt;
        BigDecimal b2bMiniAddRadiation = null;
        if (productSaleIncreaseDTO == null) {
            ProductDTO productDTO = productMap.get(String.valueOf(productId));
            if (productDTO != null && productDTO.getPayMethod() != null && productDTO.getPayMethod().equals(PayMethodEnum.PAY.key)) {
                // 现付 +0
                b2bAdjustmentType = 0;
                b2bModifiedAmt = new BigDecimal("0");
            } else {
                // 预付 且没有加幅
                return null;
            }
        } else {
            b2bAdjustmentType = productSaleIncreaseDTO.getB2bAdjustmentType();
            b2bModifiedAmt = productSaleIncreaseDTO.getB2bModifiedAmt();
            b2bMiniAddRadiation = productSaleIncreaseDTO.getB2bMiniAddRadiation();
        }
        adjustmentPrice(checkInDate, productBasePriceAndRoomStatusDTO, proTaxRuleConfigDTO, basePrice, b2bAdjustmentType,
                b2bModifiedAmt, b2bMiniAddRadiation, productIdAndCurrencyMap, agentAccountConfig, taxDetailDtoMap, orgCurrency, orgToAgentRate, productMap);
        return productBasePriceAndRoomStatusDTO;
    }

    /**
     * 底价计算加幅后的价格 统一使用 默认0 四舍五入
     *
     * @param basePrice      底价
     * @param adjustmentType 加幅类型 SaleAdjustmentTypeEnum （0加数值 1减数值 2加百分比 3减百分比 4等于）
     * @param modifiedAmt    加幅值
     */
    public static void adjustmentPrice(String checkInDate,
                                       ProductBasePriceAndRoomStatusDTO productBasePriceAndRoomStatus,
                                       ProTaxRuleConfigDTO proTaxRuleConfigDTO,
                                       BigDecimal basePrice,
                                       Integer adjustmentType,
                                       BigDecimal modifiedAmt,
                                       BigDecimal miniAddRadiation,
                                       Map<String, Integer> productIdAndCurrencyMap,
                                       AgentAccountConfig agentAccountConfig,
                                       Map<Integer, RoomTaxDetail> roomTaxDetailMap,
                                       Integer orgCurrency,
                                       BigDecimal orgToAgentRate,
                                       Map<String, ProductDTO> productMap) {

        // 1. 检查产品是否存在
        ProductDTO productDTO = Optional.ofNullable(productMap.get(String.valueOf(productBasePriceAndRoomStatus.getProductId())))
                .orElseThrow(() -> new IllegalArgumentException("产品不存在"));

        // 2. 处理底价
        if (Objects.isNull(basePrice)) {
            productBasePriceAndRoomStatus.setBasePrice(null);
            productBasePriceAndRoomStatus.setTmpBasePrice(null);
            return;
        }

        // 3. 获取汇率
        BigDecimal merchantRate = getMerchantRate(productBasePriceAndRoomStatus, productIdAndCurrencyMap, orgCurrency);
        BigDecimal agentRate = getAgentRate(agentAccountConfig, orgCurrency, orgToAgentRate);

        // 4. 处理税费
        if (proTaxRuleConfigDTO != null && CollUtilX.isNotEmpty(proTaxRuleConfigDTO.getProTaxDetails())) {
            getAllFaxPrice(checkInDate, productBasePriceAndRoomStatus, proTaxRuleConfigDTO.getProTaxDetails(),
                    productIdAndCurrencyMap.get(String.valueOf(productBasePriceAndRoomStatus.getProductId())),
                    roomTaxDetailMap, merchantRate);
        } else {
            productBasePriceAndRoomStatus.setTmpBasePrice(basePrice);
            productBasePriceAndRoomStatus.setRoomPrice(basePrice.multiply(merchantRate));
        }

        // 5. 处理加幅
        if (isPayAtHotel(productDTO)) {
            handlePayAtHotel(productBasePriceAndRoomStatus, agentRate, agentAccountConfig, checkInDate, roomTaxDetailMap);
        } else if (adjustmentType == null || modifiedAmt == null) {
            productBasePriceAndRoomStatus.setBasePrice(productBasePriceAndRoomStatus.getRoomPrice().multiply(agentRate).setScale(2, RoundingMode.HALF_UP));
        } else {
            BigDecimal adjustment = calculateAdjustment(productBasePriceAndRoomStatus, adjustmentType, modifiedAmt, miniAddRadiation);
            handleAdjustment(productBasePriceAndRoomStatus, agentRate, agentAccountConfig, checkInDate, roomTaxDetailMap, adjustment);
        }
    }

    /**
     * 获取商家币种汇率
     */
    private static BigDecimal getMerchantRate(ProductBasePriceAndRoomStatusDTO productBasePriceAndRoomStatus,
                                              Map<String, Integer> productIdAndCurrencyMap,
                                              Integer orgCurrency) {
        Integer productCurrency = productIdAndCurrencyMap.get(String.valueOf(productBasePriceAndRoomStatus.getProductId()));
        BigDecimal merchantRate = RateUtil.getRate(productCurrency, orgCurrency);
        return productCurrency.equals(orgCurrency) ? BigDecimal.ONE : merchantRate;
    }

    /**
     * 获取客户币种汇率
     */
    private static BigDecimal getAgentRate(AgentAccountConfig agentAccountConfig, Integer orgCurrency, BigDecimal orgToAgentRate) {
        return agentAccountConfig.getSettlementCurrency().equals(orgCurrency) ? BigDecimal.ONE : orgToAgentRate;
    }

    /**
     * 处理现付产品逻辑
     */
    private static void handlePayAtHotel(ProductBasePriceAndRoomStatusDTO productBasePriceAndRoomStatus,
                                         BigDecimal agentRate, AgentAccountConfig agentAccountConfig,
                                         String checkInDate, Map<Integer, RoomTaxDetail> roomTaxDetailMap) {
        // 转换税费
        convertTaxToAgentCurrency(productBasePriceAndRoomStatus, agentRate, agentAccountConfig);

        // 计算首日税费
        BigDecimal fastTax = calculateFirstDayTax(productBasePriceAndRoomStatus, checkInDate, roomTaxDetailMap, agentRate, agentAccountConfig);

        // 计算售价
        BigDecimal salePrice = addPrice(
                productBasePriceAndRoomStatus.getRoomPrice(),
                productBasePriceAndRoomStatus.getSalesTax(),
                productBasePriceAndRoomStatus.getTax(),
                productBasePriceAndRoomStatus.getOtherTaxFee(),
                fastTax
        );
        productBasePriceAndRoomStatus.setBasePrice(salePrice);
    }

    /**
     * 转换税费为客户币种
     */
    private static void convertTaxToAgentCurrency(ProductBasePriceAndRoomStatusDTO productBasePriceAndRoomStatus,
                                                  BigDecimal agentRate, AgentAccountConfig agentAccountConfig) {
        productBasePriceAndRoomStatus.setSalesTax(convertAndScale(productBasePriceAndRoomStatus.getSalesTax(), agentRate, agentAccountConfig));
        productBasePriceAndRoomStatus.setTax(convertAndScale(productBasePriceAndRoomStatus.getTax(), agentRate, agentAccountConfig));
        productBasePriceAndRoomStatus.setOtherTaxFee(convertAndScale(productBasePriceAndRoomStatus.getOtherTaxFee(), agentRate, agentAccountConfig));
        productBasePriceAndRoomStatus.setRoomPrice(convertAndScale(productBasePriceAndRoomStatus.getRoomPrice(), agentRate, agentAccountConfig));
    }

    /**
     * 计算首日税费
     */
    private static BigDecimal calculateFirstDayTax(ProductBasePriceAndRoomStatusDTO productBasePriceAndRoomStatus,
                                                   String checkInDate,
                                                   Map<Integer, RoomTaxDetail> roomTaxDetailMap,
                                                   BigDecimal agentRate,
                                                   AgentAccountConfig agentAccountConfig) {
        RoomTaxDetail roomTaxDetail = roomTaxDetailMap.get(productBasePriceAndRoomStatus.getProductId());
        if (roomTaxDetail == null || !checkInDate.equals(productBasePriceAndRoomStatus.getSaleDate())) {
            return BigDecimal.ZERO;
        }

        return addPrice(
                convertAndScale(roomTaxDetail.getSalesTax(), agentRate, agentAccountConfig),
                convertAndScale(roomTaxDetail.getOtherTax(), agentRate, agentAccountConfig),
                convertAndScale(roomTaxDetail.getTaxFee(), agentRate, agentAccountConfig)
        );
    }

    /**
     * 计算加幅值
     */
    private static BigDecimal calculateAdjustment(ProductBasePriceAndRoomStatusDTO productBasePriceAndRoomStatusDTO, Integer adjustmentType,
                                                  BigDecimal modifiedAmt, BigDecimal miniAddRadiation) {
        switch (adjustmentType) {
            case 0: // 加数值
                return modifiedAmt;
            case 1: // 减数值
                return BigDecimal.ZERO.subtract(modifiedAmt);
            case 3: // 减百分比
                return BigDecimal.ZERO.subtract(productBasePriceAndRoomStatusDTO.getRoomPrice().multiply(modifiedAmt));
            case 2: // 加百分比
                BigDecimal addRadiation = productBasePriceAndRoomStatusDTO.getRoomPrice().multiply(modifiedAmt);
                return (miniAddRadiation != null && addRadiation.compareTo(miniAddRadiation) < 0) ? miniAddRadiation : addRadiation;
            case 4: // 等于
                // 修改金额大于房费则 加幅=修改金额-房费
                // 否则 房费=修改金额 加幅=0
                if (modifiedAmt.compareTo(productBasePriceAndRoomStatusDTO.getRoomPrice()) > 0) {
                    return modifiedAmt.subtract(productBasePriceAndRoomStatusDTO.getRoomPrice());
                } else {
                    // 否则直接修改房费 加幅=0  避免出现 房费和加幅
                    productBasePriceAndRoomStatusDTO.setRoomPrice(modifiedAmt);// 售价=多少
                    return BigDecimal.ZERO;
                }
            default:
                throw new IllegalArgumentException("无效的加幅类型: " + adjustmentType);
        }
    }

    /**
     * 处理加幅逻辑
     */
    private static void handleAdjustment(ProductBasePriceAndRoomStatusDTO productBasePriceAndRoomStatus,
                                         BigDecimal agentRate, AgentAccountConfig agentAccountConfig,
                                         String checkInDate, Map<Integer, RoomTaxDetail> roomTaxDetailMap,
                                         BigDecimal adjustment) {
        // 加幅转换汇率并进位
        adjustment = CommonTgUtils.setScale(adjustment.multiply(agentRate), agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType());

        // 税费转换汇率并进位
        convertTaxToAgentCurrency(productBasePriceAndRoomStatus, agentRate, agentAccountConfig);

        // 折扣转汇率
        convertAndScale(adjustment, agentRate, agentAccountConfig);

        // 计算首日税费
        BigDecimal fastTax = calculateFirstDayTax(productBasePriceAndRoomStatus, checkInDate, roomTaxDetailMap, agentRate, agentAccountConfig);

        // 计算售价
        BigDecimal salePrice = addPrice(
                productBasePriceAndRoomStatus.getRoomPrice(),
                productBasePriceAndRoomStatus.getSalesTax(),
                productBasePriceAndRoomStatus.getTax(),
                productBasePriceAndRoomStatus.getOtherTaxFee(),
                fastTax
        ).add(adjustment);

        productBasePriceAndRoomStatus.setBasePrice(salePrice);

        if (adjustment.compareTo(BigDecimal.ZERO) < 0) {
            productBasePriceAndRoomStatus.setDiscount(adjustment);
        } else {
            productBasePriceAndRoomStatus.setDiscount(BigDecimal.ZERO);
            // 其他税费 = 其他税费+加幅
            productBasePriceAndRoomStatus.setOtherTaxFee(addPrice(productBasePriceAndRoomStatus.getOtherTaxFee(), adjustment));
        }
    }

    /**
     * 转换并四舍五入
     */
    private static BigDecimal convertAndScale(BigDecimal value, BigDecimal rate, AgentAccountConfig agentAccountConfig) {
        return value == null ? BigDecimal.ZERO : CommonTgUtils.setScale(value.multiply(rate), agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType());
    }

    /**
     * 相加
     */
    public static BigDecimal addPrice(BigDecimal... prices) {
        return Stream.of(prices)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 组装房间数据
     */
    private List<DhubRoomItemDto> buildRoomItems(
            List<com.tiangong.dto.product.request.RoomGuestNumber> roomGuests,
            List<String> saleDates,
            Map<String, ProductBasePriceAndRoomStatusDTO> priceMap,
            ProductFinancialData financialData) {

        try {
            // 获取产品限制条款
            List<String> allProductKeys = Stream.concat(
                    financialData.getProducts().keySet().stream(),
                    priceMap.keySet().stream()
            ).collect(Collectors.toList());

            Map<String, ProductRestrictDTO> restrictions = getProductRestrict(allProductKeys);

            // 按房间分组产品
            Map<Integer, List<ProductDTO>> roomProducts = financialData.getProducts().values().stream()
                    .collect(Collectors.groupingBy(ProductDTO::getRoomId));

            // 组装最终房间数据
            return assembleRoomItem(
                    roomGuests,
                    saleDates,
                    priceMap,
                    restrictions,
                    roomProducts,
                    financialData
            );
        } catch (Exception e) {
            log.error("房间项组装异常", e);
            return Collections.emptyList();
        }
    }

    /**
     * 计算平均价格
     */
    private BigDecimal calculateAveragePrice(List<DhubPriceItemDto> priceItems, AgentAccountConfig agentAccountConfig) {
        BigDecimal totalSalePrice = priceItems.stream()
                .map(DhubPriceItemDto::getSalePrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal averagePrice = totalSalePrice.divide(new BigDecimal(priceItems.size()), 2, RoundingMode.HALF_DOWN);
        return CommonTgUtils.setScale(averagePrice, agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType());
    }

    /**
     * 记录错误日志
     */
    private void logError(String message, String requestId) {
        Map<String, String> map = new HashMap<>();
        map.put(SlsEnum.LEVEL.getType(), SlsLogLevelEnum.ERROR.getLevel());
        map.put(SlsEnum.NAME.getType(), "queryProductDetailTmp-direct");
        map.put(SlsEnum.MESSAGE.getType(), message);
        map.put("requestId", requestId);
        slsLoggerUtil.saveLog(map, "queryProductDetailTmp-direct", "tiangong-product-server");
    }

    /**
     * 安全集合流处理（空安全）
     */
    private static <T> Stream<T> safeStream(Collection<T> collection) {
        return (collection == null) ? Stream.empty() : collection.stream();
    }

    /**
     * 处理单个床型组
     */
    private Stream<BedTypesDetailDto> processBedGroups(List<com.tiangong.dto.hotel.base.BedGroupsDTO> bedGroups, String language) {
        if (CollUtilX.isEmpty(bedGroups)) return Stream.empty();

        return bedGroups.stream()
                .filter(group -> CollUtilX.isNotEmpty(group.getBedInfos()))
                .map(group -> buildBedTypeDetail(group.getBedInfos(), language));
    }

    /**
     * 构建床型详情对象
     */
    private BedTypesDetailDto buildBedTypeDetail(List<com.tiangong.dto.hotel.base.BedInfosDTO> bedInfos, String language) {
        List<BedInfoDto> infoDTOS = bedInfos.stream()
                .filter(bed -> StrUtilX.isNotEmpty(bed.getBedCode()))
                .map(bed -> convertToBedInfoDto(bed, language))
                .collect(Collectors.toList());

        if (infoDTOS.isEmpty()) return null;

        BedTypesDetailDto dto = new BedTypesDetailDto();
        dto.setBedInfos(infoDTOS);
        dto.setBedInfoDesc(buildBedDescription(infoDTOS, language));
        return dto;
    }

    /**
     * 转换床型基础信息
     */
    private BedInfoDto convertToBedInfoDto(BedInfosDTO bed, String language) {
        BedInfoDto dto = new BedInfoDto();
        dto.setBedTypeCode(bed.getBedCode());
        dto.setBedTypeName(bedNameConfig.getBedTypeName(bed.getBedCode(), language));
        dto.setBedNum(bed.getBedNum());
        return dto;
    }

    /**
     * 构建床型描述
     */
    private String buildBedDescription(List<BedInfoDto> beds, String language) {
        boolean isChinese = LanguageTypeEnum.zh_CN.getValue().equals(language);
        String quantitySuffix = isChinese ? "张" : " ";
        String connector = isChinese ? "和" : " and ";

        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < beds.size(); i++) {
            BedInfoDto bed = beds.get(i);
            // 处理数量描述
            if (bed.getBedNum() != null && bed.getBedNum() > 0) {
                sb.append(bed.getBedNum()).append(quantitySuffix);
            }
            // 处理类型名称
            sb.append(bed.getBedTypeName());
            // 处理复数形式
            if (!isChinese && bed.getBedNum() != null && bed.getBedNum() > 1) {
                sb.append("s");
            }
            // 添加连接词
            if (i < beds.size() - 1) {
                sb.append(connector);
            }
        }
        return sb.toString();
    }

    /**
     * 构建组合床型描述
     */
    private String buildCombinedBedDescription(List<BedTypesDetailDto> bedTypes, String language) {
        boolean isChinese = LanguageTypeEnum.zh_CN.getValue().equals(language);
        String separator = isChinese ? "或" : " or ";

        return bedTypes.stream()
                .map(BedTypesDetailDto::getBedInfoDesc)
                .filter(StrUtilX::isNotEmpty)
                .collect(Collectors.joining(separator));
    }

    /**
     * 计算含税价格
     */
    private static void getAllFaxPrice(String checkInDate, ProductBasePriceAndRoomStatusDTO productDayQuotationDTO,
                                       List<ProTaxDetailDTO> proTaxDetails, Integer productCurrency,
                                       Map<Integer, RoomTaxDetail> roomTaxDetailMap, BigDecimal merchantRate) {
        // 1. 检查底价是否有效
        BigDecimal roomPrice = productDayQuotationDTO.getBasePrice();
        if (roomPrice == null || roomPrice.compareTo(BigDecimal.ZERO) == 0) {
            return;
        }

        // 2. 检查税费规则是否有效
        if (CollUtilX.isEmpty(proTaxDetails)) {
            productDayQuotationDTO.setTmpBasePrice(roomPrice);
            productDayQuotationDTO.setRoomPrice(roomPrice.multiply(merchantRate));
            return;
        }

        // 3. 处理税费
        TaxCalculationResult taxResult = calculateTaxes(checkInDate, productDayQuotationDTO, proTaxDetails, productCurrency, roomTaxDetailMap);

        // 4. 计算总价
        BigDecimal totalPrice = calculateTotalPrice(roomPrice, taxResult, productDayQuotationDTO.getSaleDate().equals(checkInDate));

        // 5. 设置结果
        setProductPriceDetails(productDayQuotationDTO, taxResult, roomPrice, merchantRate, totalPrice);
    }

    /**
     * 计算税费
     */
    private static TaxCalculationResult calculateTaxes(String checkInDate, ProductBasePriceAndRoomStatusDTO productDayQuotationDTO,
                                                       List<ProTaxDetailDTO> proTaxDetails, Integer productCurrency, Map<Integer, RoomTaxDetail> roomTaxDetailMap) {
        TaxCalculationResult result = new TaxCalculationResult();
        boolean isFirstDay = productDayQuotationDTO.getSaleDate().equals(checkInDate);

        proTaxDetails.forEach(proTaxDetail -> {
            BigDecimal taxAmount = getTax(productDayQuotationDTO.getBasePrice(), proTaxDetail);
            switch (proTaxDetail.getTaxType()) {
                case 0: // 销售税
                    handleSalesTax(result, proTaxDetail, taxAmount, isFirstDay, roomTaxDetailMap, productDayQuotationDTO.getProductId());
                    break;
                case 1: // 税费
                    handleTaxFee(result, proTaxDetail, taxAmount, isFirstDay, roomTaxDetailMap, productDayQuotationDTO.getProductId());
                    break;
                case 2: // 到店付
                    handlePayInStoreTax(taxAmount, roomTaxDetailMap, productCurrency, productDayQuotationDTO.getProductId());
                    break;
                case 3: // 其他税费
                    handleOtherTax(result, proTaxDetail, taxAmount, isFirstDay, roomTaxDetailMap, productDayQuotationDTO.getProductId());
                    break;
            }
        });

        return result;
    }

    /**
     * 处理销售税
     */
    private static void handleSalesTax(TaxCalculationResult result, ProTaxDetailDTO proTaxDetail,
                                       BigDecimal taxAmount, boolean isFirstDay,
                                       Map<Integer, RoomTaxDetail> roomTaxDetailMap, Integer productId) {
        if (Objects.equals(proTaxDetail.getTaxStandard(), TaxStandardsEnum.NIGHT.getCode())) {
            result.salesTax = result.salesTax.add(taxAmount);
        } else if (isFirstDay && Objects.equals(proTaxDetail.getTaxStandard(), TaxStandardsEnum.MIDNIGHT.getCode())) {
            RoomTaxDetail roomTaxDetail = getOrCreateRoomTaxDetail(roomTaxDetailMap, productId);
            roomTaxDetail.setSalesTax(addToNullable(roomTaxDetail.getSalesTax(), taxAmount));
        }
    }

    /**
     * 处理税费
     */
    private static void handleTaxFee(TaxCalculationResult result, ProTaxDetailDTO proTaxDetail,
                                     BigDecimal taxAmount, boolean isFirstDay,
                                     Map<Integer, RoomTaxDetail> roomTaxDetailMap, Integer productId) {
        if (Objects.equals(proTaxDetail.getTaxStandard(), TaxStandardsEnum.NIGHT.getCode())) {
            result.tax = result.tax.add(taxAmount);
        } else if (isFirstDay && Objects.equals(proTaxDetail.getTaxStandard(), TaxStandardsEnum.MIDNIGHT.getCode())) {
            RoomTaxDetail roomTaxDetail = getOrCreateRoomTaxDetail(roomTaxDetailMap, productId);
            roomTaxDetail.setTaxFee(addToNullable(roomTaxDetail.getTaxFee(), taxAmount));
        }
    }

    /**
     * 处理到店付税费
     */
    private static void handlePayInStoreTax(BigDecimal taxAmount, Map<Integer, RoomTaxDetail> roomTaxDetailMap,
                                            Integer productCurrency, Integer productId) {
        RoomTaxDetail roomTaxDetail = getOrCreateRoomTaxDetail(roomTaxDetailMap, productId);
        roomTaxDetail.setPayInStorePrice(addToNullable(roomTaxDetail.getPayInStorePrice(), taxAmount));
        roomTaxDetail.setPayInStoreCurrency(productCurrency);
    }

    /**
     * 处理其他税费
     */
    private static void handleOtherTax(TaxCalculationResult result, ProTaxDetailDTO proTaxDetail,
                                       BigDecimal taxAmount, boolean isFirstDay,
                                       Map<Integer, RoomTaxDetail> roomTaxDetailMap, Integer productId) {
        if (Objects.equals(proTaxDetail.getTaxStandard(), TaxStandardsEnum.NIGHT.getCode())) {
            result.otherTaxFee = result.otherTaxFee.add(taxAmount);
        } else if (isFirstDay && Objects.equals(proTaxDetail.getTaxStandard(), TaxStandardsEnum.MIDNIGHT.getCode())) {
            RoomTaxDetail roomTaxDetail = getOrCreateRoomTaxDetail(roomTaxDetailMap, productId);
            roomTaxDetail.setOtherTax(addToNullable(roomTaxDetail.getOtherTax(), taxAmount));
        }
    }

    /**
     * 获取或创建 RoomTaxDetail
     */
    private static RoomTaxDetail getOrCreateRoomTaxDetail(Map<Integer, RoomTaxDetail> roomTaxDetailMap, Integer productId) {
        return roomTaxDetailMap.computeIfAbsent(productId, k -> new RoomTaxDetail());
    }

    /**
     * 判断是否为现付产品
     */
    private static boolean isPayAtHotel(ProductDTO productDTO) {
        return productDTO.getPayMethod() != null && productDTO.getPayMethod().equals(PayMethodEnum.PAY.key);
    }

    /**
     * 计算总价
     */
    private static BigDecimal calculateTotalPrice(BigDecimal roomPrice, TaxCalculationResult taxResult, boolean isFirstDay) {
        BigDecimal total = roomPrice.add(taxResult.salesTax).add(taxResult.tax).add(taxResult.otherTaxFee);
        if (isFirstDay) {
            total = total.add(taxResult.firstDayTax);
        }
        return total;
    }

    /**
     * 设置产品价格详情
     */
    private static void setProductPriceDetails(ProductBasePriceAndRoomStatusDTO productDayQuotationDTO,
                                               TaxCalculationResult taxResult, BigDecimal roomPrice,
                                               BigDecimal merchantRate, BigDecimal totalPrice) {
        productDayQuotationDTO.setTmpBasePrice(totalPrice);
        productDayQuotationDTO.setRoomPrice(roomPrice.multiply(merchantRate));
        productDayQuotationDTO.setSalesTax(taxResult.salesTax.multiply(merchantRate));
        productDayQuotationDTO.setTax(taxResult.tax.multiply(merchantRate));
        productDayQuotationDTO.setOtherTaxFee(taxResult.otherTaxFee.multiply(merchantRate));
    }

    /**
     * 获取税费
     */
    private static BigDecimal getTax(BigDecimal roomPrice, ProTaxDetailDTO proTaxDetail) {
        if (proTaxDetail.getTaxIncreaseType() == null || proTaxDetail.getTaxIncreaseValue() == null) {
            return BigDecimal.ZERO;
        }
        return proTaxDetail.getTaxIncreaseType().equals(TaxIncreaseEnum.FIXED.getCode())
                ? proTaxDetail.getTaxIncreaseValue()
                : roomPrice.multiply(proTaxDetail.getTaxIncreaseValue().divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
    }

    /**
     * 相加（支持空值）
     */
    private static BigDecimal addToNullable(BigDecimal original, BigDecimal value) {
        return (original == null ? BigDecimal.ZERO : original).add(value);
    }

    /**
     * 税费计算结果
     */
    private static class TaxCalculationResult {
        BigDecimal salesTax = BigDecimal.ZERO;
        BigDecimal tax = BigDecimal.ZERO;
        BigDecimal otherTaxFee = BigDecimal.ZERO;
        BigDecimal firstDayTax = BigDecimal.ZERO;
    }

    /**
     * 根据产品Ids获取产品
     */
    private Map<String, ProductDTO> getProductMap(List<String> productIds) {
        List<Object> objects = RedisTemplateX.hMultiGet(RedisKey.productInfoKey, productIds);

        if (CollUtilX.isEmpty(objects)) {
            return Maps.newHashMap();
        }
        List<ProductDTO> productDTOS = StrUtilX.parseObject(objects, new TypeReference<List<ProductDTO>>() {
        });
        if (CollUtilX.isNotEmpty(productDTOS)) {
            return productDTOS.stream().collect(Collectors.toMap(e -> String.valueOf(e.getProductId()), Function.identity(), (v1, v2) -> v2));
        }
        return Maps.newHashMap();
    }

    /**
     * 条款信息: 查询product_saledate, product汇总
     * 每日条款没有设置的,按产品级别设置
     * 每日的 key 为 productId_saleDate
     * 产品级别的 key 为 productId 所以优先获取 每天的 不存在在获取默认的
     */
    private Map<String, ProductRestrictDTO> getProductRestrict(List<String> productIds) {
        List<Object> list = RedisTemplateX.hMultiGet(RedisKey.productRestrictKey, productIds);
        if (CollUtilX.isEmpty(list)) {
            return new HashMap<>();
        }
        List<ProductRestrictDTO> productRestrictDTOS = StrUtilX.parseObject(list, new TypeReference<List<ProductRestrictDTO>>() {
        });
        if (productRestrictDTOS == null) {
            return new HashMap<>();
        }
        Map<String, ProductRestrictDTO> restrictDTOMap = new HashMap<>();
        for (ProductRestrictDTO productRestrictDTO : productRestrictDTOS) {
            if (productRestrictDTO != null) {
                String key = String.valueOf(productRestrictDTO.getProductId());
                if (StrUtilX.isNotEmpty(productRestrictDTO.getSaleDate())) {
                    key = productRestrictDTO.getProductId() + "_" + productRestrictDTO.getSaleDate();
                }
                restrictDTOMap.put(key, productRestrictDTO);
            }
        }
        return restrictDTOMap;
    }

    /**
     * 组装房型item
     */
    private List<DhubRoomItemDto> assembleRoomItem(List<com.tiangong.dto.product.request.RoomGuestNumber> roomGuestNumbers,
                                                   List<String> saleDates,
                                                   Map<String, ProductBasePriceAndRoomStatusDTO> roomStatusAndPrice,
                                                   Map<String, ProductRestrictDTO> productRestrict,
                                                   Map<Integer, List<ProductDTO>> roomProductMap,
                                                   ProductFinancialData financialData) {
        List<DhubRoomItemDto> roomItems = Lists.newArrayList();
        Set<Integer> rooms = roomProductMap.keySet();
        for (Integer room : rooms) {
            DhubRoomItemDto roomItem = new DhubRoomItemDto();
            roomItem.setRoomId(Long.valueOf(room));
            List<ProductDTO> productList = roomProductMap.get(room);
            if (productList.stream().findFirst().isPresent()) {
                ProductDTO productDTO = productList.stream().findFirst().get();
                if (financialData.getRoomNameMap() != null && productDTO.getRoomId() != null) {
                    roomItem.setRoomName(financialData.getRoomNameMap().get(productDTO.getRoomId().longValue()));
                }
            }
            List<DhubProductDetailDto> products = assembleProduct(roomGuestNumbers, saleDates, roomStatusAndPrice, productRestrict, productList, financialData);
            if (CollectionUtils.isNotEmpty(products)) {
                roomItem.setProducts(products);
            } else {
                continue;
            }
            roomItems.add(roomItem);
        }
        return roomItems;
    }

    /**
     * 组装产品信息
     */
    private List<DhubProductDetailDto> assembleProduct(List<com.tiangong.dto.product.request.RoomGuestNumber> roomGuestNumbers,
                                                       List<String> saleDates,
                                                       Map<String, ProductBasePriceAndRoomStatusDTO> roomStatusAndPrice,
                                                       Map<String, ProductRestrictDTO> productRestrict,
                                                       List<ProductDTO> productDTOS,
                                                       ProductFinancialData financialData) {
        List<DhubProductDetailDto> products = new ArrayList<>();
        for (ProductDTO dto : productDTOS) {
            DhubProductDetailDto productDetail = new DhubProductDetailDto();
            // 获取产品id映射
            String ratePlanId = financialData.getProductIdsMapping().get(String.valueOf(dto.getProductId()));
            productDetail.setRatePlanId(ratePlanId);
            productDetail.setRatePlanName(dto.getProductName());
            // 供应商币种
            productDetail.setBaseCurrency(SettlementCurrencyEnum.getCodeByKey(String.valueOf(dto.getCurrency())));
            productDetail.setBedTypeDetails(convertBedType(dto.getBedTypes(), financialData.getRequest().getLanguage()));
            productDetail.setSupplyCode(dto.getSupplierCode());

            BigDecimal merchantRate = RateUtil.getRate(dto.getCurrency(), financialData.getOrgCurrency());
            if (CollectionUtil.isNotEmpty(financialData.getTaxDetails())
                    && financialData.getTaxDetails().get(dto.getProductId()) != null) {
                RoomTaxDetail roomTaxDetail = financialData.getTaxDetails().get(dto.getProductId());
                if (roomTaxDetail.getSalesTax() != null
                        || roomTaxDetail.getTaxFee() != null
                        || roomTaxDetail.getOtherTax() != null
                        || roomTaxDetail.getPayInStorePrice() != null) {
                    // 根据产品币种转换 客户币种金额和
                    if (roomTaxDetail.getPayInStorePrice() != null && roomTaxDetail.getPayInStoreCurrency() != null) {
                        // 到店另付费用 产品原始币种和金额
                        productDetail.setPayAtHotelFee(roomTaxDetail.getPayInStorePrice().multiply(merchantRate).multiply(financialData.getExchangeRate()));
                        productDetail.setPayAtHotelFeeCurrency(SettlementCurrencyEnum.getCodeByKey(String.valueOf(roomTaxDetail.getPayInStoreCurrency())));
                    }
                }
            }

            List<String> priceDates = Lists.newArrayList();
            saleDates.forEach(saleDate -> priceDates.add(String.valueOf(dto.getProductId()).concat(Constant.SEPARATOR).concat(saleDate)));
            PriceDto totalPrice = new PriceDto();

            AtomicBoolean bookTypeFlag = new AtomicBoolean(true);
            // 不含税价格计划，存在税费规则 则按照全球标准输出
            List<DhubRoomItemDetailDto> roomItemDetails = assembleRoomItems(financialData.getRequest(), roomGuestNumbers, roomStatusAndPrice,
                    productRestrict, dto, priceDates, financialData.getProductIds(), totalPrice, financialData.getTaxDetails(), financialData.getAgentConfig(), financialData.getOrgCurrency(),bookTypeFlag);
            if (CollUtilX.isNotEmpty(roomItemDetails)) {
                DhubRoomItemDetailDto roomItemDetail = roomItemDetails.get(0);
                List<DhubPriceDetailItemDto> priceItems = roomItemDetail.getPriceItems();
                if (CollUtilX.isEmpty(priceItems) || priceItems.size() != saleDates.size()) {
                    continue;
                }
                List<DhubPriceItemDto> convert = CommonConvert.INSTANCE.convert(priceItems);
                productDetail.setPriceItems(convert);
                productDetail.setRoomItemDetails(roomItemDetails);
            } else {
                continue;
            }

            productDetail.setBookType(bookTypeFlag.get() ? 1 : 2);
            // 2、设置供应商标签
            setSupplierLabelInfo(productDetail, financialData.getRequest().getAgentCode());
            // 总卖价
            productDetail.setTotalSalePrice(totalPrice.getTotalSalePrice());
            // 总售价
            productDetail.setTotalBasePrice(totalPrice.getTotalBasePrice());
            // 总房费
            productDetail.setRoomPrice(totalPrice.getTotalRoomPrice());
            // 均价 每间每晚
            productDetail.setAvgPrice(totalPrice.getTotalSalePrice().divide(new BigDecimal(financialData.getRequest().getRoomNum()), 2, RoundingMode.HALF_DOWN)
                    .divide(new BigDecimal(saleDates.size()), 2, RoundingMode.HALF_DOWN).setScale(2, RoundingMode.HALF_DOWN));
            if (dto.getPriceType() != null && dto.getPayMethod().equals(1)) {
                // 到店付
                productDetail.setPayAtHotelFlag(1);
            } else {
                productDetail.setPayAtHotelFlag(0);
            }
            productDetail.setInvoiceModel(1);
            productDetail.setInvoiceType(1);
            productDetail.setAddBed(0);
            //最大入住人数 自签产品默认为2
            if (dto.getMaxAdultQty() == null || dto.getMaxAdultQty() <= 0) {
                dto.setMaxAdultQty(2);
            }
            productDetail.setMaxPerson(dto.getMaxAdultQty());
            productDetail.setSmallProductLabel(0);
            // 价格计划名称存在中文直接转换成指定英文
            if (LanguageTypeEnum.en_US.getValue().equals(financialData.getRequest().getLanguage()) && StrUtilX.isNotEmpty(productDetail.getRatePlanName())) {
                if (StrUtilX.notIsEnglishAndSpace(productDetail.getRatePlanName())) {
                    productDetail.setRatePlanName("Business travel rate");
                }
            }

            // 查询供应商配置的供应商类型
            SupplierAddDTO supplierReq = financialData.getSupplyMap().get(productDetail.getSupplyCode());
            if (supplierReq != null) {
                productDetail.setSupplierForm(supplierReq.getSupplierForm());
            }
            products.add(productDetail);
        }
        return products;
    }

    /**
     * 转换床型
     */
    private List<BedTypesDetailDto> convertBedType(String bedType, String language) {
        if (StrUtilX.isNotEmpty(bedType)) {
            try {
                List<BedTypesDetailDto> bedTypeDetails = JSON.parseArray(bedType, BedTypesDetailDto.class);
                for (BedTypesDetailDto bedTypeDetail : bedTypeDetails) {
                    for (BedInfoDto bedInfo : bedTypeDetail.getBedInfos()) {
                        bedInfo.setBedTypeName(bedNameConfig.getBedTypeName(bedInfo.getBedTypeCode(), language));
                    }
                }
            } catch (Exception e) {
                log.error("实时查询自签产品系统异常", e);
            }
        }
        return null;
    }

    /**
     * 组装每日报价
     */
    private List<DhubPriceDetailItemDto> assemblePriceItems(Map<String, ProductBasePriceAndRoomStatusDTO> roomStatusAndPrice,
                                                            Map<String, ProductRestrictDTO> productRestrict,
                                                            ProductDTO dto,
                                                            List<String> priceDates,
                                                            List<String> productIds,
                                                            PriceDto totalPrice,
                                                            AgentAccountConfig agentAccountConfig,
                                                            int orgCurrency,
                                                            AtomicBoolean bookTypeFlag) {
        List<Object> objects = RedisTemplateX.hMultiGet(RedisKey.productInfoKey, productIds);
        if (CollUtilX.isEmpty(objects)) {
            return new ArrayList<>();
        }
        List<ProductDTO> productDTOS = StrUtilX.parseObject(objects, new TypeReference<List<ProductDTO>>() {
        });
        Map<Integer, Integer> productIdAndBreakfastQty = productDTOS.stream().filter(Objects::nonNull).collect(toMap(ProductDTO::getProductId, ProductDTO::getBreakfastQty));
        List<DhubPriceDetailItemDto> priceItems = Lists.newArrayList();
        for (String productAndsaleDate : priceDates) {
            ProductBasePriceAndRoomStatusDTO productBasePriceAndRoomStatusDTO = roomStatusAndPrice.getOrDefault(productAndsaleDate, new ProductBasePriceAndRoomStatusDTO());

            DhubPriceDetailItemDto priceItem = new DhubPriceDetailItemDto();
            if (Objects.nonNull(productBasePriceAndRoomStatusDTO)) {
                priceItem.setSaleDate(productAndsaleDate.split("_")[1]);
                // 价格为0 进行过滤
                if (productBasePriceAndRoomStatusDTO.getBasePrice() == null
                        || productBasePriceAndRoomStatusDTO.getBasePrice().compareTo(BigDecimal.ZERO) == 0) {
                    continue;
                }
                // 获取每日 条款
                ProductRestrictDTO productRestrictDTO = productRestrict.get(dto.getProductId() + "_" + productBasePriceAndRoomStatusDTO.getSaleDate());
                if (productRestrictDTO == null) {
                    // 每日条款获取不到则查询默认条款 产品级别
                    productRestrictDTO = productRestrict.getOrDefault(String.valueOf(dto.getProductId()), new ProductRestrictDTO());
                }
                DhubPriceTaxDetail taxDetail = new DhubPriceTaxDetail();
                if (productBasePriceAndRoomStatusDTO.getTax() != null
                        || productBasePriceAndRoomStatusDTO.getRoomPrice() != null
                        || productBasePriceAndRoomStatusDTO.getPayAtHotelFee() != null
                        || productBasePriceAndRoomStatusDTO.getOtherTaxFee() != null
                        || productBasePriceAndRoomStatusDTO.getDiscount() != null) {
                    taxDetail.setRoomPrice(productBasePriceAndRoomStatusDTO.getRoomPrice());
                    taxDetail.setSalesTax(productBasePriceAndRoomStatusDTO.getSalesTax());
                    taxDetail.setOtherTax(productBasePriceAndRoomStatusDTO.getOtherTaxFee());
                    taxDetail.setTaxFee(productBasePriceAndRoomStatusDTO.getTax());
                } else {
                    taxDetail.setRoomPrice(productBasePriceAndRoomStatusDTO.getBasePrice());
                    taxDetail.setSalesTax(new BigDecimal("0"));
                    taxDetail.setOtherTax(new BigDecimal("0"));
                    taxDetail.setTaxFee(new BigDecimal("0"));
                }
                priceItem.setTaxDetail(taxDetail);
                String currency = SettlementCurrencyEnum.getCodeByKey(String.valueOf(agentAccountConfig.getSettlementCurrency()));
                priceItem.setCurrency(currency);
                priceItem.setSalePrice(productBasePriceAndRoomStatusDTO.getBasePrice());
                // 总房费
                if (productBasePriceAndRoomStatusDTO.getRoomPrice() != null) {
                    totalPrice.setTotalRoomPrice(totalPrice.getTotalRoomPrice().add(productBasePriceAndRoomStatusDTO.getRoomPrice()));
                }
                // 计算总售价
                totalPrice.setTotalSalePrice(totalPrice.getTotalSalePrice().add(productBasePriceAndRoomStatusDTO.getBasePrice()));
                // 计算总底价
                totalPrice.setTotalBasePrice(totalPrice.getTotalBasePrice().add(productBasePriceAndRoomStatusDTO.getTmpBasePrice()));
                priceItem.setBreakfastNum(productIdAndBreakfastQty.get(productBasePriceAndRoomStatusDTO.getProductId()));
                priceItem.setQuotaNum(productBasePriceAndRoomStatusDTO.getQuota());
                if (null != productBasePriceAndRoomStatusDTO.getRoomStatus()) {//房态已经过滤了 理论上这里不需要处理了
                    if (productBasePriceAndRoomStatusDTO.getRemainingQuota() == null) {
                        productBasePriceAndRoomStatusDTO.setRemainingQuota(0);
                    }
                    if (productBasePriceAndRoomStatusDTO.getOverDraftStatus() == null) {
                        productBasePriceAndRoomStatusDTO.setOverDraftStatus(0);
                    }
                    if (productBasePriceAndRoomStatusDTO.getRoomStatus().equals(1)) {//开房
                        //有房 不可超 剩余配额为0 已经设置默认值了
                        if (productBasePriceAndRoomStatusDTO.getOverDraftStatus().equals(0) && productBasePriceAndRoomStatusDTO.getRemainingQuota().equals(0)) {
                            priceItem.setRoomStatus(RoomStatusEnum.FULL.getType());//满房
                            bookTypeFlag.set(false);
                        } else {
                            priceItem.setRoomStatus(RoomStatusEnum.EXIST.getType());
                        }
                    } else {
                        continue;//没关房过滤
                    }
                } else {
                    continue;//没设置过滤
                }
                priceItem.setMinRoomCount(productRestrictDTO.getReservationLimitRooms());
                int bookRestrictionType = 0;
                if (Objects.equals(productRestrictDTO.getComparisonType(), 0)) {
                    bookRestrictionType = 1;
                }
                priceItem.setBookRestrictionType(bookRestrictionType);
                priceItem.setBookRestrictionDay(productRestrictDTO.getReservationAdvanceDays());
                String reservationDueTime = Objects.isNull(productRestrictDTO.getReservationDueTime()) ? null : productRestrictDTO.getReservationDueTime().replace(":", "");
                priceItem.setBookRestrictionTime(reservationDueTime);
                int occupancyRestrictionType = 0;
                if (productRestrictDTO.getReservationLimitNights() != null && !Objects.equals(productRestrictDTO.getReservationLimitNights(), 0)) {
                    occupancyRestrictionType = 1;
                }
                priceItem.setOccupancyRestrictionType(occupancyRestrictionType);
                priceItem.setOccupancyRestrictionDay(productRestrictDTO.getReservationLimitNights());
                priceItem.setMinRoomCount(productRestrictDTO.getReservationLimitRooms());
                Integer cancelRestrictionType = null;
                if (Objects.equals(productRestrictDTO.getCancellationType(), 1)) {
                    cancelRestrictionType = 1;
                } else if (Objects.equals(productRestrictDTO.getCancellationType(), 2)) {
                    cancelRestrictionType = 2;
                } else if (Objects.equals(productRestrictDTO.getCancellationType(), 3)) {
                    cancelRestrictionType = 3;
                } else if (Objects.equals(productRestrictDTO.getCancellationType(), 4)) {
                    cancelRestrictionType = 4;
                }

                // 担保条款
                if (CollUtilX.isNotEmpty(productRestrictDTO.getGuarantees())) {
                    GuaranteeDTO guaranteeDTO = productRestrictDTO.getGuarantees().get(0);
                    priceItem.setGuaranteeType(guaranteeDTO.getGuaranteeType());
                    priceItem.setGuaranteeFeeType(guaranteeDTO.getGuaranteeFeeType());
                    priceItem.setGuaranteeCondition(guaranteeDTO.getGuaranteeCondition());
                }

                priceItem.setCancelRestrictionType(cancelRestrictionType);
                priceItem.setCancelRestrictionDay(productRestrictDTO.getCancellationAdvanceDays());

                String cancellationDueTime = Objects.isNull(productRestrictDTO.getCancellationDueTime()) ? null : productRestrictDTO.getCancellationDueTime().replace(":", "");
                priceItem.setCancelRestrictionTime(cancellationDueTime);

                // 取消罚金处理
                if (productRestrictDTO.getCancelPenaltiesType() != null
                        && productRestrictDTO.getCancelPenaltiesValue() != null) {
                    List<DhubCancelPenalties> cancelPenalties = new ArrayList<>();
                    DhubCancelPenalties cancelPenalDTO = new DhubCancelPenalties();
                    cancelPenalDTO.setPenaltiesType(productRestrictDTO.getCancelPenaltiesType());
                    if (productRestrictDTO.getCancelPenaltiesType().equals(3) || productRestrictDTO.getCancelPenaltiesType().equals(4)) {
                        cancelPenalDTO.setPenaltiesValue(productRestrictDTO.getCancelPenaltiesValue() + "%");
                        cancelPenalDTO.setCurrency(SettlementCurrencyEnum.getCodeByKey(String.valueOf(cancelPenalDTO.getCurrency())));
                    } else if (productRestrictDTO.getCancelPenaltiesType().equals(2) && productRestrictDTO.getCurrency() != null && productRestrictDTO.getCurrency().compareTo(agentAccountConfig.getSettlementCurrency()) != 0) {
                        // 转换商家币种
                        BigDecimal orgRate = BigDecimal.ONE;
                        if (new BigDecimal(productRestrictDTO.getCurrency()).compareTo(new BigDecimal(orgCurrency)) != 0) {
                            orgRate = RateUtil.getRate(productRestrictDTO.getCurrency(), orgCurrency);
                            if (orgRate == null) {
                                throw new SysException(ErrorCodeEnum.EXCHANGE_RATE_IS_NOT_ALREADT);
                            }
                        }
                        // 转换汇率
                        BigDecimal cancelPenaltiesAmt = CommonTgUtils.setRate(BigDecimal.valueOf(productRestrictDTO.getCancelPenaltiesValue()), orgRate);
                        // 转换客户币种
                        BigDecimal agentRate = BigDecimal.ONE;
                        if (new BigDecimal(orgCurrency).compareTo(new BigDecimal(agentAccountConfig.getSettlementCurrency())) != 0) {
                            agentRate = RateUtil.getRate(orgCurrency, agentAccountConfig.getSettlementCurrency());
                            if (agentRate == null) {
                                throw new SysException(ErrorCodeEnum.EXCHANGE_RATE_IS_NOT_ALREADT);
                            }
                        }
                        // 转换汇率
                        cancelPenaltiesAmt = CommonTgUtils.setRate(cancelPenaltiesAmt, agentRate);
                        // 根据客户配置进位
                        BigDecimal cancelPenaltiesValue = CommonTgUtils.setScale(cancelPenaltiesAmt, agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType());
                        cancelPenalDTO.setPenaltiesValue(String.valueOf(cancelPenaltiesValue));
                        cancelPenalDTO.setCurrency(SettlementCurrencyEnum.getCodeByKey(String.valueOf(agentAccountConfig.getSettlementCurrency())));
                    } else {
                        cancelPenalDTO.setPenaltiesValue(String.valueOf(productRestrictDTO.getCancelPenaltiesValue()));
                        cancelPenalDTO.setCurrency(SettlementCurrencyEnum.getCodeByKey(String.valueOf(cancelPenalDTO.getCurrency())));
                    }
                    String dateStr = (DateUtilX.dateToString((DateUtilX.getDate(DateUtilX.stringToDate(priceItem.getSaleDate()),
                            -priceItem.getCancelRestrictionDay())), "yyyy-MM-dd") + " " + cancellationDueTime.substring(0, 2) + ":" + cancellationDueTime.substring(2, 4));
                    Date date = DateUtilX.stringToDate(dateStr, "yyyy-MM-dd HH:mm");
                    cancelPenalDTO.setStartDate(DateUtilX.dateToString(date, "yyyy-MM-dd'T'HH:mm:ss.SSSXXX"));
                    cancelPenalDTO.setEndData(cancelPenalDTO.getStartDate());
                    cancelPenalties.add(cancelPenalDTO);
                    priceItem.setCancelPenalties(cancelPenalties);

                    // 当罚金类型为首晚、首晚百分比时，转换取消类型为3
                    if (cancelPenalDTO.getPenaltiesType() != null && (PenaltiesTypeEnum.NIGHTS.getKey().equals(cancelPenalDTO.getPenaltiesType()) ||
                            PenaltiesTypeEnum.FIRSTNIGHTPERCENTAGE.getKey().equals(cancelPenalDTO.getPenaltiesType()))) {
                        priceItem.setCancelRestrictionType(3);
                        priceItem.setCancelPenalties(null);
                    }
                }

                priceItems.add(priceItem);
            }
        }
        return priceItems;
    }

    /**
     * 组装产品信息
     */
    private List<DhubRoomItemDetailDto> assembleRoomItems(ProductDetailRequest request,
                                                          List<com.tiangong.dto.product.request.RoomGuestNumber> roomGuestNumbers,
                                                          Map<String, ProductBasePriceAndRoomStatusDTO> roomStatusAndPrice,
                                                          Map<String, ProductRestrictDTO> productRestrict,
                                                          ProductDTO dto,
                                                          List<String> priceDates,
                                                          List<String> productIds,
                                                          PriceDto totalPrice,
                                                          Map<Integer, RoomTaxDetail> roomTaxDetailMap,
                                                          AgentAccountConfig agentAccountConfig,
                                                          int orgCurrency,
                                                          AtomicBoolean bookTypeFlag) {
        List<Object> objects = RedisTemplateX.hMultiGet(RedisKey.productInfoKey, productIds);
        if (CollUtilX.isEmpty(objects)) {
            return new ArrayList<>();
        }
        List<DhubRoomItemDetailDto> roomItemDetails = Lists.newArrayList();
        if (CollUtilX.isNotEmpty(roomGuestNumbers)) {
            for (com.tiangong.dto.product.request.RoomGuestNumber roomGuestNumber : roomGuestNumbers) {
                DhubRoomItemDetailDto roomItemDetail = new DhubRoomItemDetailDto();
                roomItemDetail.setRoomIndex(roomGuestNumber.getRoomIndex());
                roomItemDetail.setAdultNum(roomGuestNumber.getAdultNum());
                if (CollUtilX.isNotEmpty(roomGuestNumber.getChildrenInfos())) {
                    StringBuilder childAges = new StringBuilder();
                    for (ChildrenInfo childrenInfo : roomGuestNumber.getChildrenInfos()) {
                        childAges.append(childrenInfo.getChildrenAge()).append(",");
                    }
                    roomItemDetail.setChildAges(childAges.substring(0, childAges.length() - 1));
                }
                if (CollectionUtil.isNotEmpty(roomTaxDetailMap) && roomTaxDetailMap.get(dto.getProductId()) != null) {
                    RoomTaxDetail roomTaxDetail = roomTaxDetailMap.get(dto.getProductId());

                    DhubPriceTaxDetail taxDetailDto = new DhubPriceTaxDetail();
                    if (roomTaxDetail.getSalesTax() != null
                            || roomTaxDetail.getTaxFee() != null
                            || roomTaxDetail.getOtherTax() != null
                            || roomTaxDetail.getDiscount() != null) {
                        taxDetailDto.setSalesTax(roomTaxDetail.getSalesTax());
                        taxDetailDto.setTaxFee(roomTaxDetail.getTaxFee());
                        taxDetailDto.setOtherTax(roomTaxDetail.getOtherTax());
                    } else {
                        taxDetailDto.setSalesTax(new BigDecimal("0"));
                        taxDetailDto.setTaxFee(new BigDecimal("0"));
                        taxDetailDto.setOtherTax(new BigDecimal("0"));
                    }
                    roomItemDetail.setTaxDetail(taxDetailDto);
                    // 产品币种
                    roomItemDetail.setPayInStorePrice(roomTaxDetail.getPayInStorePrice());
                    roomItemDetail.setPayInStoreCurrencyCode(SettlementCurrencyEnum.getCodeByKey(String.valueOf(roomTaxDetail.getPayInStoreCurrency())));
                } else {
                    DhubPriceTaxDetail taxDetailDto = new DhubPriceTaxDetail();
                    taxDetailDto.setSalesTax(new BigDecimal("0"));
                    taxDetailDto.setTaxFee(new BigDecimal("0"));
                    taxDetailDto.setOtherTax(new BigDecimal("0"));
                    roomItemDetail.setTaxDetail(taxDetailDto);
                }
                List<DhubPriceDetailItemDto> priceItems = assemblePriceItems(roomStatusAndPrice, productRestrict, dto, priceDates, productIds, totalPrice, agentAccountConfig, orgCurrency, bookTypeFlag);
                roomItemDetail.setPriceItems(priceItems);
                roomItemDetails.add(roomItemDetail);
            }
        } else {
            for (Integer i = 1; i <= request.getRoomNum(); i++) {
                DhubRoomItemDetailDto roomItemDetail = new DhubRoomItemDetailDto();
                roomItemDetail.setRoomIndex(i);
                DhubPriceTaxDetail taxDetailDto = new DhubPriceTaxDetail();
                taxDetailDto.setSalesTax(new BigDecimal("0"));
                taxDetailDto.setTaxFee(new BigDecimal("0"));
                taxDetailDto.setOtherTax(new BigDecimal("0"));
                List<DhubPriceDetailItemDto> priceItems = assemblePriceItems(roomStatusAndPrice, productRestrict, dto, priceDates, productIds, totalPrice, agentAccountConfig, orgCurrency, bookTypeFlag);
                roomItemDetail.setPriceItems(priceItems);
                roomItemDetails.add(roomItemDetail);
            }
        }

        return roomItemDetails;
    }

    /**
     * 处理预订状态
     */
    private void handleBookStatus(ProductDetailRequest request, DhubProductDetailDto productDetail, ProductMiddleDto productMiddleDto) {
        // 处理日期转换和获取日期列表
        Date checkInDate = DateUtilX.stringToDate(request.getCheckInDate());
        Date checkOutDate = DateUtilX.stringToDate(request.getCheckOutDate());
        Date checkOutDateMinusOne = DateUtilX.getDate(checkOutDate, -1, 0);
        List<Date> dateList = DateUtilX.getDateList(checkInDate, checkOutDateMinusOne);

        // 预生成日期字符串集合以提高效率
        Set<String> dateStrSet = dateList.stream()
                .map(DateUtilX::dateToString)
                .collect(Collectors.toCollection(LinkedHashSet::new)); // 保持顺序

        int roomCount = productMiddleDto.getRoomItemDetails().size();
        int daysCount = dateList.size();
        int sum = roomCount * daysCount; // 总天数 = 房间数 * 日期数
        int canBookCount = 0;

        for (RoomItemDetailDto roomItemDetail : productMiddleDto.getRoomItemDetails()) {
            // 遍历房间的每个价格详情，过滤符合条件的记录
            canBookCount += (int) roomItemDetail.getProductDetails().stream()
                    .filter(p -> dateStrSet.contains(p.getSaleDate()))
                    .filter(p -> isRoomBookable(p, request))
                    .count();
        }

        // 根据可预订数量设置预订类型
        if (sum == 0 || canBookCount == 0) {
            productDetail.setBookType(CanBookEnum.CAN_NOT_BOOK.value);
        } else if (canBookCount == sum) {
            productDetail.setBookType(CanBookEnum.CAN_BOOK.value);
        } else {
            productDetail.setBookType(CanBookEnum.CAN_PART_BOOK.value);
        }
    }

    /**
     * 判断单个房间的某天是否可预订
     */
    private boolean isRoomBookable(ProductDetailMiddleDto priceItem, ProductDetailRequest request) {
        boolean isAvailable = priceItem.getRoomStatus() != null
                && priceItem.getRoomStatus() == RoomStateEnum.HAVA_ROOM.key
                && !Boolean.TRUE.equals(priceItem.getOverDraft());

        if (!isAvailable) return true;

        Integer quota = priceItem.getQuotaNum();
        if (quota == null || quota < 0) return false;

        Integer required = request.getRoomNum();
        return required == null || quota >= required;
    }

    /**
     * 后置处理
     */
    private void postProcessConfiguration(DhubProductDetailDto target,
                                          ProductMiddleDto middleDto,
                                          SupplierAddDTO supplierConfig,
                                          Map<String, String> productIdMapping) {
        // 设置产品id
        String ratePlanId = buildPricePlanId(middleDto.getSpPricePlanId(), middleDto.getSpPricePlanName());
        target.setRatePlanId(productIdMapping.get(ratePlanId));
        // 设置发票信息
        setInvoiceInfo(target, supplierConfig);
        target.setSupplierForm(Optional.ofNullable(supplierConfig)
                .map(SupplierAddDTO::getSupplierForm)
                .orElse(null));
    }

    /**
     * 设置发票信息
     */
    private void setInvoiceInfo(DhubProductDetailDto productDetail, SupplierAddDTO supplierAddDTO) {
        if (Objects.nonNull(supplierAddDTO)) {
            productDetail.setInvoiceModel(supplierAddDTO.getInvoiceModel());
            productDetail.setInvoiceType(supplierAddDTO.getInvoiceType());
            productDetail.setRapidProcessing(supplierAddDTO.getQuickProcessingSwitch() != null ? supplierAddDTO.getQuickProcessingSwitch() : 0);
        } else {
            productDetail.setRapidProcessing(0);
        }
    }

    /**
     * 处理价格明细
     */
    private List<DhubPriceDetailItemDto> processPriceItems(RoomItemDetailDto roomItemDetail,
                                                           List<ProductDetailMiddleDto> productDetails,
                                                           ProductDetailRequest request,
                                                           AgentAccountConfig config,
                                                           SupplierAddDTO supplierConfig,
                                                           SupplyIncrease agentIncrease,
                                                           SupplyHotelIncrease hotelIncrease,
                                                           int orgCurrency,
                                                           BigDecimal orgToAgentRate,
                                                           BigDecimal supplierRate,
                                                           boolean isPrepay) {
        return productDetails.stream()
                .map(priceItem -> processSinglePriceItem(roomItemDetail,
                        priceItem, request, config,
                        supplierConfig, agentIncrease, hotelIncrease,
                        orgCurrency, orgToAgentRate,
                        supplierRate, isPrepay
                ))
                .collect(Collectors.toList());
    }

    /**
     * 处理单个价格明细
     */
    private DhubPriceDetailItemDto processSinglePriceItem(RoomItemDetailDto roomItemDetail,
                                                          ProductDetailMiddleDto priceItem,
                                                          ProductDetailRequest request,
                                                          AgentAccountConfig config,
                                                          SupplierAddDTO supplierConfig,
                                                          SupplyIncrease agentIncrease,
                                                          SupplyHotelIncrease hotelIncrease,
                                                          int orgCurrency,
                                                          BigDecimal orgToAgentRate,
                                                          BigDecimal supplierRate,
                                                          boolean isPrepay) {
        DhubPriceDetailItemDto dto = new DhubPriceDetailItemDto();

        // 处理按次税费
        TaxDetailDto roomTaxDetail = processRoomTaxDetail(priceItem, request, roomItemDetail, config, orgCurrency, supplierRate, orgToAgentRate);
        // 转换商家币种汇率
        TaxDetailDto dailyTaxDetail = convertMerchantCurrencyRate(priceItem, orgCurrency, supplierRate);

        // 加幅计算（仅预付场景）
        if (isPrepay) {
            applyPriceAdjustments(dailyTaxDetail, supplierConfig, agentIncrease, hotelIncrease);
        }
        // 转换客户币种汇率
        convertAgentCurrencyRate(dailyTaxDetail, config, orgCurrency, orgToAgentRate);

        // 计算最终售价
        BigDecimal salePrice = calculateFinalPrice(dailyTaxDetail, roomTaxDetail, priceItem, request);

        // 设置DTO基础信息
        populateBasicInfo(dto, priceItem, salePrice, request);

        // 处理条款信息
        populateRestrictionInfo(dto, priceItem, config, orgCurrency);

        // 设置货币和税费详情
        populateCurrencyAndTax(dto, config, dailyTaxDetail);

        return dto;
    }

    /**
     * 处理加幅
     */
    private void applyPriceAdjustments(TaxDetailDto taxDetail,
                                       SupplierAddDTO supplierConfig,
                                       SupplyIncrease agentIncrease,
                                       SupplyHotelIncrease hotelIncrease) {
        BigDecimal basePrice = taxDetail.getRoomPrice();

        // 供应商加幅
        BigDecimal supplierAdjustment = BigDecimal.ZERO;
        if (supplierConfig != null) {
            supplierAdjustment = calculateAdjustment2(
                    basePrice,
                    supplierConfig.getAdjustmentType(),
                    supplierConfig.getModifiedAmt(),
                    supplierConfig.getLowestIncreaseAmt()
            );
        }

        // 分销商-酒店加幅
        BigDecimal agentHotelAdjustment = BigDecimal.ZERO;
        if(hotelIncrease != null){
            // 优先酒店级别加幅
            agentHotelAdjustment = calculateAdjustment2(
                    basePrice.add(supplierAdjustment),
                    hotelIncrease.getAdjustmentType(),
                    hotelIncrease.getModifiedAmt(),
                    hotelIncrease.getLowestIncreaseAmt()
            );
        } else if (agentIncrease != null) {
            //分销商维度加幅
            agentHotelAdjustment = calculateAdjustment2(
                    basePrice.add(supplierAdjustment),
                    agentIncrease.getAdjustmentType(),
                    agentIncrease.getModifiedAmt(),
                    agentIncrease.getLowestIncreaseAmt()
            );
        }

        // 折扣
        BigDecimal discount = BigDecimal.ZERO;
        // 其他税费
        BigDecimal otherTax = CommonTgUtils.formatBigDecimal(taxDetail.getOtherTax());

        if (supplierAdjustment.compareTo(BigDecimal.ZERO) < 0) {
            discount = discount.add(supplierAdjustment);
        } else {
            otherTax = otherTax.add(supplierAdjustment);
        }

        if (agentHotelAdjustment.compareTo(BigDecimal.ZERO) < 0) {
            discount = discount.add(agentHotelAdjustment);
        } else {
            otherTax = otherTax.add(agentHotelAdjustment);
        }
        // 加幅值计算到其他税中
        taxDetail.setOtherTax(otherTax);

        // 折扣
        taxDetail.setDiscount(CommonTgUtils.formatBigDecimal(discount));
    }

    /**
     * 计算加幅
     */
    private BigDecimal calculateAdjustment2(BigDecimal base,
                                            Integer adjustmentType,
                                            BigDecimal modifiedAmt,
                                            BigDecimal lowestAmt) {
        BigDecimal adjustment = CommonTgUtils.adjustmentAmt(base, adjustmentType, modifiedAmt);

        if (Objects.equals(SaleAdjustmentTypeEnum.PLUS_PERCENTAGE.no, adjustmentType)
                && lowestAmt != null && adjustment.compareTo(lowestAmt) < 0) {
            return lowestAmt;
        }
        return adjustment;
    }

    /**
     * 处理房间按次税费（首日税费）
     */
    private TaxDetailDto processRoomTaxDetail(ProductDetailMiddleDto priceItem,
                                              ProductDetailRequest request,
                                              RoomItemDetailDto roomItemDetail,
                                              AgentAccountConfig config,
                                              int orgCurrency,
                                              BigDecimal supplierRate,
                                              BigDecimal orgToAgentRate) {
        if (!priceItem.getSaleDate().equals(request.getCheckInDate()) ||
                roomItemDetail.getTaxDetail() == null) {
            return null;
        }

        // 基础转换
        TaxDetailDto taxDetail = CommonConvert.INSTANCE.convertTaxDetail(roomItemDetail.getTaxDetail());

        // 供应商转商家汇率转换
        if (priceItem.getCurrency() != null && priceItem.getCurrency() != orgCurrency) {
            convertTaxCurrency(taxDetail, supplierRate);
        }

        // 商家转客户汇率转换
        if (config.getSettlementCurrency() != null &&
                config.getSettlementCurrency() != orgCurrency) {
            convertTaxCurrency(taxDetail, orgToAgentRate);
        }

        // 统一进位规则
        applyTaxRounding(taxDetail, config);

        // 设置按次税费
        roomItemDetail.setTaxDetail(CommonConvert.INSTANCE.convertTaxDetail(taxDetail));
        return taxDetail;
    }

    /**
     * 转换供应商币种汇率
     */
    private TaxDetailDto convertMerchantCurrencyRate(ProductDetailMiddleDto priceItem,
                                                     int orgCurrency,
                                                     BigDecimal supplierRate) {
        TaxDetailDto taxDetail = CommonConvert.INSTANCE.convertTaxDetail(priceItem.getTaxDetail());

        // 供应商转商家汇率
        if (priceItem.getCurrency() != null && priceItem.getCurrency() != orgCurrency) {
            convertTaxCurrency(taxDetail, supplierRate);
        }
        return taxDetail;
    }

    /**
     * 转换客户币种汇率
     */
    private void convertAgentCurrencyRate(TaxDetailDto taxDetail,
                                          AgentAccountConfig config,
                                          int orgCurrency,
                                          BigDecimal orgToAgentRate) {
        // 商家转客户汇率
        if (config.getSettlementCurrency() != null &&
                config.getSettlementCurrency() != orgCurrency) {
            convertTaxCurrency(taxDetail, orgToAgentRate);
        }

        // 统一进位规则
        applyTaxRounding(taxDetail, config);
    }

    /**
     * 计算最终售价
     */
    private BigDecimal calculateFinalPrice(TaxDetailDto dailyTax,
                                           TaxDetailDto roomTax,
                                           ProductDetailMiddleDto priceItem,
                                           ProductDetailRequest request) {
        BigDecimal total = dailyTax.getRoomPrice()
                .add(dailyTax.getSalesTax())
                .add(dailyTax.getTaxFee())
                .add(dailyTax.getOtherTax())
                .add(dailyTax.getDiscount());

        // 首日添加按次税费
        if (priceItem.getSaleDate().equals(request.getCheckInDate()) && roomTax != null) {
            total = total.add(roomTax.getSalesTax())
                    .add(roomTax.getTaxFee())
                    .add(roomTax.getOtherTax());
        }
        return total;
    }

    /**
     * 填充基础信息
     */
    private void populateBasicInfo(DhubPriceDetailItemDto dto,
                                   ProductDetailMiddleDto priceItem,
                                   BigDecimal salePrice,
                                   ProductDetailRequest request) {
        dto.setSaleDate(priceItem.getSaleDate());
        dto.setSalePrice(salePrice);

        // 原始协议价开关
        if (request.getOriginalProtocolPriceSwitch() != null &&
                request.getOriginalProtocolPriceSwitch() == 1) {
            dto.setBasePrice(priceItem.getBasePrice());
        }
        dto.setBreakfastType(priceItem.getBreakfastType());
        dto.setBreakfastNum(priceItem.getBreakfastNum());
        dto.setRoomStatus(priceItem.getRoomStatus());
        dto.setQuotaNum(priceItem.getQuotaNum());
    }

    /**
     * 填充限制条款信息
     */
    private void populateRestrictionInfo(DhubPriceDetailItemDto dto, ProductDetailMiddleDto priceItem, AgentAccountConfig config, int orgCurrency) {
        // 房间数量限制
        dto.setMinRoomCount(priceItem.getMinBookRoomsRestrict());
        Integer maxRooms = priceItem.getMaxBookRoomsRestrict();
        dto.setMaxRoomCount(maxRooms != null && maxRooms == -1 ? null : maxRooms);

        // 预订限制
        dto.setBookRestrictionType(priceItem.getBookRestrictType());
        dto.setBookRestrictionDay(priceItem.getBookRestrictDays());
        dto.setBookRestrictionTime(priceItem.getBookRestrictTime());

        // 入住限制
        dto.setOccupancyRestrictionType(priceItem.getOccupancyRestrictType());
        dto.setOccupancyRestrictionDay(priceItem.getOccupancyRestrictDays());

        // 取消条款（取第一条）
        if (CollUtilX.isNotEmpty(priceItem.getCancelRestrictions())) {
            CancelRestriction cancel = priceItem.getCancelRestrictions().get(0);
            dto.setCancelRestrictionType(cancel.getCancelRestrictionType());
            dto.setCancelRestrictionDay(cancel.getCancelRestrictionDay());
            dto.setCancelRestrictionTime(cancel.getCancelRestrictionTime());
            dto.setCancelPenalties(convertCancelPenalties(cancel.getCancelPenalties(), config, orgCurrency));
        }

        // 担保条款
        dto.setGuaranteeType(priceItem.getGuaranteeType());
        dto.setGuaranteeCondition(priceItem.getGuaranteeCondition());
        dto.setGuaranteeFeeType(priceItem.getGuaranteeFeeType());
    }

    /**
     * 转换取消条款
     */
    private List<DhubCancelPenalties> convertCancelPenalties(List<CancelPenalties> cancelPenaltiesList, AgentAccountConfig agentAccountConfig, int orgCurrency) {
        if (CollUtilX.isEmpty(cancelPenaltiesList)) {
            return null;
        }

        return cancelPenaltiesList.stream().map(item -> {
            DhubCancelPenalties temp = new DhubCancelPenalties();
            temp.setPenaltiesType(item.getPenaltiesType());
            if (item.getPenaltiesType() != null && item.getPenaltiesType().equals(2)) {
                // 币种不一样转换汇率
                if (StrUtilX.isNotEmpty(item.getPenaltiesValue()) && item.getCurrency() != null && item.getCurrency().compareTo(agentAccountConfig.getSettlementCurrency()) != 0) {
                    // 转换商家币种
                    BigDecimal orgRate = BigDecimal.ONE;
                    if (new BigDecimal(item.getCurrency()).compareTo(new BigDecimal(orgCurrency)) != 0) {
                        orgRate = RateUtil.getRate(item.getCurrency(), orgCurrency);
                        if (orgRate == null) {
                            throw new SysException(ErrorCodeEnum.EXCHANGE_RATE_IS_NOT_ALREADT);
                        }
                    }
                    // 转换汇率
                    BigDecimal cancelPenaltiesAmt = CommonTgUtils.setRate(new BigDecimal(item.getPenaltiesValue()), orgRate);
                    // 转换客户币种
                    BigDecimal agentRate = BigDecimal.ONE;
                    if (new BigDecimal(orgCurrency).compareTo(new BigDecimal(agentAccountConfig.getSettlementCurrency())) != 0) {
                        agentRate = RateUtil.getRate(orgCurrency, agentAccountConfig.getSettlementCurrency());
                        if (agentRate == null) {
                            throw new SysException(ErrorCodeEnum.EXCHANGE_RATE_IS_NOT_ALREADT);
                        }
                    }
                    // 转换汇率
                    cancelPenaltiesAmt = CommonTgUtils.setRate(cancelPenaltiesAmt, agentRate);
                    // 根据客户配置进位
                    BigDecimal cancelPenaltiesValue = CommonTgUtils.setScale(cancelPenaltiesAmt, agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType());
                    temp.setPenaltiesValue(String.valueOf(cancelPenaltiesValue));
                    temp.setCurrency(SettlementCurrencyEnum.getCodeByKey(String.valueOf(agentAccountConfig.getSettlementCurrency())));
                } else {
                    temp.setPenaltiesValue(item.getPenaltiesValue());
                    temp.setCurrency(SettlementCurrencyEnum.getCodeByKey(String.valueOf(item.getCurrency())));
                }
            } else {
                temp.setPenaltiesValue(item.getPenaltiesValue());
                temp.setCurrency(SettlementCurrencyEnum.getCodeByKey(String.valueOf(item.getCurrency())));
            }
            temp.setStartDate(item.getStartDate());
            temp.setEndData(item.getEndData());
            return temp;
        }).collect(toList());
    }

    /**
     * 税费项汇率转换
     */
    private void convertTaxCurrency(TaxDetailDto taxDetail, BigDecimal rate) {
        taxDetail.setRoomPrice(CommonTgUtils.setRate(taxDetail.getRoomPrice(), rate));
        taxDetail.setSalesTax(CommonTgUtils.setRate(taxDetail.getSalesTax(), rate));
        taxDetail.setTaxFee(CommonTgUtils.setRate(taxDetail.getTaxFee(), rate));
        taxDetail.setOtherTax(CommonTgUtils.setRate(taxDetail.getOtherTax(), rate));
        taxDetail.setDiscount(CommonTgUtils.setRate(taxDetail.getDiscount(), rate));
    }

    /**
     * 统一税费进位规则
     */
    private void applyTaxRounding(TaxDetailDto taxDetail, AgentAccountConfig config) {
        taxDetail.setRoomPrice(CommonTgUtils.setScale(taxDetail.getRoomPrice(),
                config.getDecimalPlaces(), config.getRoundingType()));
        taxDetail.setSalesTax(CommonTgUtils.setScale(taxDetail.getSalesTax(),
                config.getDecimalPlaces(), config.getRoundingType()));
        taxDetail.setTaxFee(CommonTgUtils.setScale(taxDetail.getTaxFee(),
                config.getDecimalPlaces(), config.getRoundingType()));
        taxDetail.setOtherTax(CommonTgUtils.setScale(taxDetail.getOtherTax(),
                config.getDecimalPlaces(), config.getRoundingType()));
        taxDetail.setDiscount(CommonTgUtils.setScale(taxDetail.getDiscount(),
                config.getDecimalPlaces(), config.getRoundingType()));
    }

    /**
     * 设置币种和税费详情
     */
    private void populateCurrencyAndTax(DhubPriceDetailItemDto dto,
                                        AgentAccountConfig config,
                                        TaxDetailDto taxDetail) {
        dto.setCurrency(SettlementCurrencyEnum.getCodeByKey(String.valueOf(config.getSettlementCurrency())));

        DhubPriceTaxDetail taxDetailDto = new DhubPriceTaxDetail();
        taxDetailDto.setTaxFee(taxDetail.getTaxFee());
        taxDetailDto.setRoomPrice(taxDetail.getRoomPrice());
        taxDetailDto.setSalesTax(taxDetail.getSalesTax());
        taxDetailDto.setOtherTax(taxDetail.getOtherTax());
        dto.setDiscount(taxDetail.getDiscount());
        dto.setTaxDetail(taxDetailDto);
    }

    /**
     * 保存日志
     */
    private void saveSlsLog(String message, Date start, String logId) {
        Map<String, String> map = new HashMap<>();
        map.put(SlsEnum.LEVEL.getType(), SlsLogLevelEnum.INFO.getLevel());
        map.put(SlsEnum.NAME.getType(), "queryProductDetailTmp");
        map.put("logFlag", "排查慢问题");
        map.put("logId", logId);
        map.put(SlsEnum.MESSAGE.getType(), message);
        map.put("start", DateUtilX.dateToString(start, DateUtilX.hour_ms_format));
        slsLoggerUtil.saveLog(map, "queryProductDetailTmp", "tiangong-product-server");
    }

    /**
     * 直连产品（不落地）
     */
    private DhubProductDetailInfoResponse direct(ProductDetailRequest request,
                                                 Map<String, SupplierAddDTO> supplyMap,
                                                 Integer orgCurrency,
                                                 AgentAccountConfig agentConfig) {
        DhubProductDetailInfoResponse response = new DhubProductDetailInfoResponse();
        response.setHotelId(request.getHotelId());
        Date start = new Date();

        try {
            // 判断供应商集合是否为空
            if (supplyMap == null || supplyMap.isEmpty()) {
                return response;
            }

            // 1. 获取供应商产品信息
            Response<List<ProductMiddleDto>> productResponse = getSupplyProductInfo(request, supplyMap);
            if (invalidResponse(productResponse)) {
                return response;
            }

            // 2. 提取供应商编码
            Set<String> supplyCodeSet = extractSupplyCodes(productResponse.getModel());
            if (CollUtilX.isEmpty(supplyCodeSet)) {
                return response;
            }

            // 2. 提取产品id
            List<String> productIds = extractProductIds(productResponse.getModel());
            Map<String, String> productIdMapping = queryUtil.getCacheProductIdsWithUUID(productIds, request.getHotelId());

            // 3. 获取商家币种和汇率
            BigDecimal orgToAgentRate = RateUtil.getRate(orgCurrency, agentConfig.getSettlementCurrency());
            if (orgToAgentRate == null || orgToAgentRate.compareTo(BigDecimal.ZERO) == 0) {
                logError("商家转客户汇率不存在，orgCurrency=" + orgCurrency + "，settlementCurrency=" + agentConfig.getSettlementCurrency(), request.getRequestId());
                return response;
            }

            // 4. 获取酒店信息
            Date start2 = new Date();
            HotelInfoCollectionDTO hotelInfo = fetchHotelInfo(request);
            saveSlsLog("排查慢问题：获取酒店信息", start2, request.getRequestId());

            // 6. 查询加幅配置
            // 查询分销商-供应商加幅配置
            Map<String, SupplyIncrease> agentSupplyIncreaseMap = queryUtil.getSupplyIncrease(request.getAgentCode(), new ArrayList<>(supplyCodeSet));
            // 查询分销商-供应商-酒店加幅配置
            Map<String, SupplyHotelIncrease> agentSupplyHotelIncreaseMap = queryUtil.getSupplyHotelIncrease(request.getAgentCode(), new ArrayList<>(supplyCodeSet), Collections.singletonList(request.getHotelId()));

            // 7. 过滤产品
            List<ProductMiddleDto> productMiddles = filterProduct(request, productResponse.getModel(), supplyMap, orgCurrency);
            if (CollUtilX.isEmpty(productMiddles)) {
                return response;
            }

            // 8. 并行处理产品信息
            List<DhubRoomItemDto> newRoomItems = processProductMiddles(productMiddles, agentConfig, request, agentSupplyIncreaseMap, agentSupplyHotelIncreaseMap, supplyMap, orgToAgentRate, hotelInfo, productIdMapping, orgCurrency);
            response.setRoomItems(newRoomItems);
        } catch (Exception e) {
            log.error("direct 查询直连产品异常，requestId={}", request.getRequestId(), e);
        } finally {
            saveSlsLog("排查慢问题：直连产品查询结束统计", start, request.getRequestId());
        }

        return response;
    }

    /**
     * 获取供应商产品数据
     */
    private Response<List<ProductMiddleDto>> getSupplyProductInfo(ProductDetailRequest request, Map<String, SupplierAddDTO> supplyMap) {
        QueryProductInfoRequest queryProductInfoRequest = new QueryProductInfoRequest();
        queryProductInfoRequest.setSpHotelId(request.getHotelId() == null ? null : String.valueOf(request.getHotelId()));
        if (request.getRoomId() != null) {
            queryProductInfoRequest.setSpRoomId(request.getRoomId().intValue());
        }
        queryProductInfoRequest.setCheckInDate(request.getCheckInDate());
        queryProductInfoRequest.setCheckOutDate(request.getCheckOutDate());
        queryProductInfoRequest.setGuestQuantity(request.getGuestQuantity());
        queryProductInfoRequest.setRoomQty(request.getRoomNum());
        // 过滤出直连供应商
        Set<String> supplyCodes = supplyMap.values().stream().filter(item -> item.getSupplierType() != null
                && SupplierTypeEnum.API.getKey() == item.getSupplierType() && StrUtilX.isNotEmpty(item.getSupplierCode())).map(SupplierAddDTO::getSupplierCode).collect(toSet());
        queryProductInfoRequest.setSupplyCodes(supplyCodes);
        queryProductInfoRequest.setOnlyHourRoom(request.getOnlyHourRoom());
        queryProductInfoRequest.setSupplyType(request.getSupplyType());
        if (CollUtilX.isNotEmpty(request.getRoomGuestNumbers())) {
            queryProductInfoRequest.setRoomGuestNumbers(JSON.parseArray(JSON.toJSONString(request.getRoomGuestNumbers()), RoomGuestNumber.class));
        }
        queryProductInfoRequest.setUserAccount(request.getUserAccount());
        queryProductInfoRequest.setRequestId(request.getRequestId());
        queryProductInfoRequest.setCurrency(request.getSaleCurrency());
        queryProductInfoRequest.setLanguage(request.getLanguage());
        queryProductInfoRequest.setNationality(request.getNationality());
        // 获取供应商产品数据
        return supplyDirectShubProductRemote.queryProductList(queryProductInfoRequest);
    }

    /**
     * 校验响应结果
     */
    private boolean invalidResponse(Response<List<ProductMiddleDto>> response) {
        return Objects.isNull(response) || !Objects.equals(ResultCodeEnum.SUCCESS.code, response.getResult()) || CollUtilX.isEmpty(response.getModel());
    }

    /**
     * 提取供应商编码
     */
    private Set<String> extractSupplyCodes(List<ProductMiddleDto> productMiddleDtos) {
        return productMiddleDtos.stream().filter(Objects::nonNull)
                .map(ProductMiddleDto::getSupplyCode)
                .collect(Collectors.toSet());
    }

    /**
     * 提取产品id
     */
    private List<String> extractProductIds(List<ProductMiddleDto> productMiddleDtos) {
        return productMiddleDtos.stream()
                .map(item -> buildPricePlanId(item.getSpPricePlanId(), item.getSpPricePlanName()))
                .collect(Collectors.toList());
    }

    /**
     * 组合价格计划id
     */
    private String buildPricePlanId(String spPricePlanId, String spPricePlanName) {
        return String.format("%s%s%s", spPricePlanId, "#", spPricePlanName);
    }

    /**
     * 组装房间数据
     */
    private Map<Long, RoomMeta> buildRoomMetaMap(HotelInfoCollectionDTO hotelInfo, String language) {
        Map<Long, RoomMeta> roomMetaMap = new HashMap<>();
        if (hotelInfo != null && CollUtilX.isNotEmpty(hotelInfo.getRooms())) {
            hotelInfo.getRooms().forEach(roomInfo -> {
                RoomMeta meta = new RoomMeta();
                meta.setName(roomInfo.getRoomtypeName());
                meta.setMaxPerson(roomInfo.getMaxPerson());
                meta.setBedTypes(processBedTypes(roomInfo, language));
                meta.setBedInfoDesc(buildCombinedBedDescription(meta.getBedTypes(), language)); // 设置床型描述
                roomMetaMap.put(roomInfo.getRoomtypeId(), meta);
            });
        }
        return roomMetaMap;
    }

    /**
     * 处理床型信息
     */
    private List<BedTypesDetailDto> processBedTypes(RoomtypeDTO roomInfo, String language) {
        List<com.tiangong.dto.hotel.base.RoomDetailBedDTO> allBeds = Stream.concat(
                safeStream(roomInfo.getRoomBed()),
                safeStream(roomInfo.getLivingBed())
        ).collect(Collectors.toList());

        if (CollUtilX.isEmpty(allBeds)) return Collections.emptyList();

        return allBeds.stream()
                .flatMap(bedDTO -> processBedGroups(bedDTO.getBedGroups(), language))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 转换房型数据
     */
    private DhubProductDetailDto convertRoomItem(AgentAccountConfig agentConfig,
                                                 ProductDetailRequest request,
                                                 ProductMiddleDto middleDto,
                                                 Map<String, SupplyIncrease> agentIncreaseMap,
                                                 Map<String, SupplyHotelIncrease> agentSupplyHotelIncreaseMap,
                                                 Map<String, SupplierAddDTO> supplyConfigMap,
                                                 BigDecimal orgToAgentRate,
                                                 Map<String, String> productIdMapping,
                                                 int orgCurrency) {
        DhubProductDetailDto dto = new DhubProductDetailDto();
        // 基础属性设置
        setupBasicAttributes(request,dto, middleDto);
        // 床型信息转换
        convertBedTypes(middleDto, dto, request);
        // 提示信息转换
        convertTipInfos(middleDto, dto);

        // 获取加幅信息
        SupplierAddDTO supplierConfig = supplyConfigMap.get(middleDto.getSupplyCode());
        SupplyIncrease agentIncrease = agentIncreaseMap.get(middleDto.getSupplyCode());
        SupplyHotelIncrease hotelIncrease = agentSupplyHotelIncreaseMap.get(middleDto.getSupplyCode().concat(StrUtilX.SPLIT_CODE).concat(String.valueOf(request.getHotelId())));

        // 价格明细处理
        List<DhubRoomItemDetailDto> priceDetails = convertPriceItems(middleDto, agentConfig, supplierConfig, agentIncrease, hotelIncrease, request, orgToAgentRate, orgCurrency);
        dto.setRoomItemDetails(priceDetails);

        // 处理预订状态
        handleBookStatus(request, dto, middleDto);

        // 后处理配置
        postProcessConfiguration(dto, middleDto, supplierConfig, productIdMapping);
        return dto;
    }

    /**
     * 基础属性设置
     */
    private void setupBasicAttributes( ProductDetailRequest request,DhubProductDetailDto target, ProductMiddleDto source) {
        target.setRoomId(Long.valueOf(source.getSpRoomTypeId()));
        target.setRoomName(source.getSpRoomName());
        target.setRatePlanName(source.getSpPricePlanName());
        // 价格计划名称替换
        if (LanguageTypeEnum.en_US.getValue().equals(request.getLanguage()) && StrUtilX.isNotEmpty(source.getSpPricePlanName())) {
            if (StrUtilX.notIsEnglishAndSpace(source.getSpPricePlanName())) {
                target.setRatePlanName("Business travel rate");
            }
        }

        target.setSupplyCode(source.getSupplyCode());
        target.setPayAtHotelFlag(source.getPayAtHotelFlag());
        target.setProductLabel(source.getProductLabel());
    }

    /**
     * 床型信息转换
     */
    private void convertBedTypes(ProductMiddleDto source, DhubProductDetailDto target, ProductDetailRequest request) {
        Optional.ofNullable(source.getBedTypeDetails())
                .filter(CollUtilX::isNotEmpty)
                .ifPresent(bedTypes -> {
                    List<BedTypesDetailDto> converted = bedTypes.stream()
                            .map(bedType -> convertBedType(bedType, request))
                            .collect(Collectors.toList());
                    target.setBedTypeDetails(converted);
                });
    }

    /**
     * 床型信息转换
     */
    private BedTypesDetailDto convertBedType(com.tiangong.supply.direct.remote.dto.BedTypesDetailDto source, ProductDetailRequest request) {
        BedTypesDetailDto target = new BedTypesDetailDto();
        target.setBedInfoDesc(source.getBedInfoDesc());
        Optional.ofNullable(source.getBedInfos())
                .filter(CollUtilX::isNotEmpty)
                .ifPresent(bedInfos -> target.setBedInfos(convertBedInfos(bedInfos, request)));
        return target;
    }

    /**
     * 床型信息转换
     */
    private List<BedInfoDto> convertBedInfos(List<com.tiangong.supply.direct.remote.dto.BedInfoDto> sources, ProductDetailRequest request) {
        return sources.stream()
                .map(source -> {
                    BedInfoDto bedInfo = new BedInfoDto();
                    bedInfo.setBedTypeCode(source.getBedTypeCode());
                    bedInfo.setBedTypeName(bedNameConfig.getBedTypeName(source.getBedTypeCode(), request.getLanguage()));
                    bedInfo.setBedNum(source.getBedNum());
                    return bedInfo;
                })
                .collect(Collectors.toList());
    }

    /**
     * 提示信息转换
     */
    private void convertTipInfos(ProductMiddleDto source, DhubProductDetailDto target) {
        Optional.ofNullable(source.getTips())
                .filter(CollUtilX::isNotEmpty)
                .ifPresent(tips -> target.setTips(CommonConvert.INSTANCE.tipInfosListConvert(tips)));
    }

    /**
     * 价格明细处理
     */
    private List<DhubRoomItemDetailDto> convertPriceItems(ProductMiddleDto middleDto,
                                                          AgentAccountConfig agentConfig,
                                                          SupplierAddDTO supplierConfig,
                                                          SupplyIncrease agentIncrease,
                                                          SupplyHotelIncrease hotelIncrease,
                                                          ProductDetailRequest request,
                                                          BigDecimal orgToAgentRate,
                                                          int orgCurrency) {
        return middleDto.getRoomItemDetails().stream()
                .map(priceItem -> convertPriceItem(priceItem, agentConfig, supplierConfig, agentIncrease, hotelIncrease, request, orgToAgentRate, orgCurrency, middleDto.getPayAtHotelFlag()))
                .collect(Collectors.toList());
    }

    /**
     * 处理价格信息
     */
    private DhubRoomItemDetailDto convertPriceItem(RoomItemDetailDto source,
                                                   AgentAccountConfig agentConfig,
                                                   SupplierAddDTO supplierConfig,
                                                   SupplyIncrease agentIncrease,
                                                   SupplyHotelIncrease hotelIncrease,
                                                   ProductDetailRequest request,
                                                   BigDecimal orgToAgentRate,
                                                   int orgCurrency,
                                                   Integer payAtHotelFlag) {
        DhubRoomItemDetailDto target = new DhubRoomItemDetailDto();
        agentIncrease(source, agentConfig, supplierConfig, agentIncrease, hotelIncrease, request, orgToAgentRate, orgCurrency, target, !Boolean.TRUE.equals(null != payAtHotelFlag && 1 == payAtHotelFlag));
        setupBasicAttributes(target, source);
        convertTaxDetail(source, target);
        convertCurrencyCode(source, target);
        setupPaymentInfo(source, target);
        return target;
    }

    /**
     * 计算分销商加幅 - 统一处理预付和现付
     */
    private void agentIncrease(RoomItemDetailDto roomItemDetail,
                               AgentAccountConfig agentAccountConfig,
                               SupplierAddDTO supplierIncreaseConfig,
                               SupplyIncrease agentSupplyIncrease,
                               SupplyHotelIncrease agentSupplyHotelIncrease,
                               ProductDetailRequest request,
                               BigDecimal orgToAgentRate,
                               int orgCurrency,
                               DhubRoomItemDetailDto roomItemDetailDto,
                               boolean isPrepay) {
        try {
            if (Objects.isNull(agentAccountConfig)) {
                return;
            }

            List<ProductDetailMiddleDto> productDetails = roomItemDetail.getProductDetails();
            if (CollUtilX.isEmpty(productDetails)) {
                return;
            }

            BigDecimal rate = RateUtil.getRate(productDetails.get(0).getCurrency(), orgCurrency);
            if (rate == null) {
                rate = BigDecimal.ONE;
            }
            List<DhubPriceDetailItemDto> priceItems = processPriceItems(roomItemDetail, productDetails, request,
                    agentAccountConfig, supplierIncreaseConfig, agentSupplyIncrease, agentSupplyHotelIncrease, orgCurrency, orgToAgentRate, rate, isPrepay);
            roomItemDetailDto.setPriceItems(priceItems);
        } catch (Exception e) {
            log.error("Agent price calculation error", e);
        }
    }

    /**
     * 设置房间基本属性
     */
    private void setupBasicAttributes(DhubRoomItemDetailDto target, RoomItemDetailDto source) {
        target.setRoomIndex(source.getRoomIndex());
        target.setAdultNum(source.getAdultNum());
        target.setChildAges(source.getChildAges());
    }

    /**
     * 税详情转换
     */
    private void convertTaxDetail(RoomItemDetailDto source, DhubRoomItemDetailDto target) {
        Optional.ofNullable(source.getTaxDetail())
                .ifPresent(taxDetail -> target.setTaxDetail(CommonConvert.INSTANCE.convert(taxDetail)));
    }

    /**
     * 币种转换
     */
    private void convertCurrencyCode(RoomItemDetailDto source, DhubRoomItemDetailDto target) {
        Optional.ofNullable(source.getPayInStoreCurrency())
                .ifPresent(currency -> target.setPayInStoreCurrencyCode(SettlementCurrencyEnum.getCodeByKey(String.valueOf(currency))));
        Optional.ofNullable(source.getSupplyPayInStoreCurrency())
                .ifPresent(currency -> target.setSupplyPayInStoreCurrencyCode(SettlementCurrencyEnum.getCodeByKey(String.valueOf(currency))));
    }

    /**
     * 设置支付信息
     */
    private void setupPaymentInfo(RoomItemDetailDto source, DhubRoomItemDetailDto target) {
        target.setPayInStorePrice(source.getPayInStorePrice());
        target.setSupplyPayInStorePrice(source.getSupplyPayInStorePrice());
    }

    /**
     * 过滤产品
     */
    private List<ProductMiddleDto> filterProduct(ProductDetailRequest request, List<ProductMiddleDto> productDetailList,
                                                 Map<String, SupplierAddDTO> supplyConfigMap, int orgCurrency) {
        Date start5 = new Date();
        List<ProductMiddleDto> dtoList = new ArrayList<>();
        for (ProductMiddleDto productMiddleDto : productDetailList) {
            // 没有每日数据
            if (CollUtilX.isEmpty(productMiddleDto.getRoomItemDetails()) || productMiddleDto.getRoomItemDetails().isEmpty()) {
                continue;
            }
            // 未设置供应商加幅，过滤掉产品
            if ((MapUtil.isEmpty(supplyConfigMap) || !supplyConfigMap.containsKey(productMiddleDto.getSupplyCode()) ||
                    supplyConfigMap.get(productMiddleDto.getSupplyCode()).getAdjustmentType() == null ||
                    supplyConfigMap.get(productMiddleDto.getSupplyCode()).getModifiedAmt() == null)) {
                continue;
            }

            // 未设置供应商-商家汇率，过滤掉产品
            BigDecimal rate = RateUtil.getRate(productMiddleDto.getRoomItemDetails().get(0).getProductDetails().get(0).getCurrency(), orgCurrency);
            if (Objects.isNull(rate) || rate.compareTo(BigDecimal.ZERO) == 0) {
                continue;
            }
            if (productMiddleDto.getRoomItemDetails().get(0).getPayInStoreCurrency() != null) {
                BigDecimal payInStoreRate = RateUtil.getRate(productMiddleDto.getRoomItemDetails().get(0).getPayInStoreCurrency(), orgCurrency);
                if (Objects.isNull(payInStoreRate) || payInStoreRate.compareTo(BigDecimal.ZERO) == 0) {
                    continue;
                }
            }

            // 判断产品类型是否一致、不一致过滤掉产品
            if (request.getPayMethod() != null && !request.getPayMethod().equals(productMiddleDto.getPayAtHotelFlag())) {
                continue;
            }

            // 添加供货单查询产品 判断是否过滤币种
            if (request.getShowSupplyName() != null && request.getShowSupplyName() == 1 && request.getBaseCurrency() != null) {
                if (productMiddleDto.getRoomItemDetails().get(0).getProductDetails().get(0).getCurrency() == null ||
                        productMiddleDto.getRoomItemDetails().get(0).getProductDetails().get(0).getCurrency().compareTo(request.getBaseCurrency()) != 0) {
                    continue;
                }
            }

            dtoList.add(productMiddleDto);
        }

        saveSlsLog("排查慢问题：过滤产品信息", start5, request.getRequestId());
        return dtoList;
    }

    /**
     * 处理产品信息并组装房间数据（优化版）
     */
    private List<DhubRoomItemDto> processProductMiddles(List<ProductMiddleDto> productMiddles,
                                                        AgentAccountConfig agentConfig,
                                                        ProductDetailRequest request,
                                                        Map<String, SupplyIncrease> agentSupplyIncreaseMap,
                                                        Map<String, SupplyHotelIncrease> agentSupplyHotelIncreaseMap,
                                                        Map<String, SupplierAddDTO> supplyConfigMap,
                                                        BigDecimal orgToAgentRate,
                                                        HotelInfoCollectionDTO hotelSummary,
                                                        Map<String, String> productIdMapping,
                                                        int orgCurrency) {
        List<DhubRoomItemDto> roomItems = new ArrayList<>();
        // 组装房间数据
        Map<Long, RoomMeta> roomMetaMap = buildRoomMetaMap(hotelSummary, request.getLanguage());

        for (ProductMiddleDto productMiddleDto : productMiddles) {
            // 过滤房型在基础信息不存在并且不是虚拟房型
            RoomMeta roomMeta = roomMetaMap.get(Long.valueOf(productMiddleDto.getSpRoomTypeId()));
            if (null == roomMeta && !roomIdISVirtualRoom(productMiddleDto.getSpRoomTypeId())) {
                continue;
            }
            // 转换房型数据
            DhubProductDetailDto productDetail = convertRoomItem(agentConfig, request, productMiddleDto, agentSupplyIncreaseMap, agentSupplyHotelIncreaseMap, supplyConfigMap, orgToAgentRate, productIdMapping, orgCurrency);

            // 设置床型信息
            setBedTypesInfo(productDetail, roomMetaMap,request.getLanguage());

            // 设置最大入住人数
            productDetail.setMaxPerson(Optional.ofNullable(productDetail.getMaxPerson())
                    .orElse(roomMetaMap.getOrDefault(productDetail.getRoomId(), new RoomMeta()).getMaxPerson()));

            // 初始化价格和税费信息
            BigDecimal totalSalePrice = BigDecimal.ZERO;
            BigDecimal totalBasePrice = BigDecimal.ZERO;
            BigDecimal payInStorePrice = BigDecimal.ZERO;
            BigDecimal supplyPayInStorePrice = BigDecimal.ZERO;
            BigDecimal totalRoomPrice = BigDecimal.ZERO;
            TaxDetailDto totalTax = new TaxDetailDto();
            Map<String, DhubPriceDetailItemDto> salePriceMap = new HashMap<>();
            String baseCurrency = null;

            // 处理每个房间的价格和税费
            for (DhubRoomItemDetailDto roomItemDetail : productDetail.getRoomItemDetails()) {
                payInStorePrice = payInStorePrice.add(CommonTgUtils.formatBigDecimal(roomItemDetail.getPayInStorePrice()));
                supplyPayInStorePrice = supplyPayInStorePrice.add(CommonTgUtils.formatBigDecimal(roomItemDetail.getSupplyPayInStorePrice()));

                // 处理按次税费
                Optional.ofNullable(roomItemDetail.getTaxDetail()).ifPresent(taxDetail -> {
                    totalTax.setSalesTax(CommonTgUtils.formatBigDecimal(totalTax.getSalesTax()).add(CommonTgUtils.formatBigDecimal(taxDetail.getSalesTax())));
                    totalTax.setTaxFee(CommonTgUtils.formatBigDecimal(totalTax.getTaxFee()).add(CommonTgUtils.formatBigDecimal(taxDetail.getTaxFee())));
                    totalTax.setOtherTax(CommonTgUtils.formatBigDecimal(totalTax.getOtherTax()).add(CommonTgUtils.formatBigDecimal(taxDetail.getOtherTax())));
                });

                // 处理每日价格
                for (DhubPriceDetailItemDto priceItem : roomItemDetail.getPriceItems()) {
                    if (!salePriceMap.containsKey(priceItem.getSaleDate())) {
                        salePriceMap.put(priceItem.getSaleDate(), priceItem);
                    }

                    if (baseCurrency == null) {
                        baseCurrency = priceItem.getCurrency();
                    }
                    totalSalePrice = totalSalePrice.add(CommonTgUtils.formatBigDecimal(priceItem.getSalePrice()));
                    totalBasePrice = totalBasePrice.add(CommonTgUtils.formatBigDecimal(priceItem.getBasePrice()));

                    // 处理每日税费
                    if (priceItem.getTaxDetail() != null) {
                        totalRoomPrice = totalRoomPrice.add(CommonTgUtils.formatBigDecimal(priceItem.getTaxDetail().getRoomPrice()));
                        totalTax.setSalesTax(CommonTgUtils.formatBigDecimal(totalTax.getSalesTax()).add(CommonTgUtils.formatBigDecimal(priceItem.getTaxDetail().getSalesTax())));
                        totalTax.setTaxFee(CommonTgUtils.formatBigDecimal(totalTax.getTaxFee()).add(CommonTgUtils.formatBigDecimal(priceItem.getTaxDetail().getTaxFee())));
                        totalTax.setOtherTax(CommonTgUtils.formatBigDecimal(totalTax.getOtherTax()).add(CommonTgUtils.formatBigDecimal(priceItem.getTaxDetail().getOtherTax())));
                    }
                }
            }

            // 计算每日价格均价和总价
            List<DhubPriceItemDto> priceItemList = calculateDailyPrices(salePriceMap.values(), request.getOriginalProtocolPriceSwitch());

            // 设置产品级别的价格和税费信息
            productDetail.setTotalSalePrice(totalSalePrice);
            if ((productDetail.getPayAtHotelFlag() != null && Objects.equals(productDetail.getPayAtHotelFlag(), 1)) ||
                    (request.getShowSupplyName() != null && request.getShowSupplyName() == 1)) {
                if (totalBasePrice.compareTo(BigDecimal.ZERO) > 0) {
                    productDetail.setTotalBasePrice(totalBasePrice);
                    productDetail.setBaseCurrency(baseCurrency);
                }
            }

            productDetail.setPayAtHotelFeeCurrency(productDetail.getRoomItemDetails().get(0).getPayInStoreCurrencyCode());
            productDetail.setPayAtHotelFee(payInStorePrice);
            productDetail.setAvgPrice(calculateAveragePrice(priceItemList, agentConfig));
            productDetail.setRoomPrice(totalRoomPrice);
            productDetail.setPriceItems(priceItemList);
            productDetail.setCheckInType(1);

            // 设置发票模式、供应商标签和床型信息
            setInvoiceModelInfo(productDetail);
            setSupplierLabelInfo(productDetail, request.getAgentCode());

            // 组装房间数据
            DhubRoomItemDto roomItem = new DhubRoomItemDto();
            roomItem.setRoomId(productDetail.getRoomId());
            // 不是虚拟房型，房型名称取基础信息的
            if (!roomIdISVirtualRoom(String.valueOf(productDetail.getRoomId())) && roomMeta != null) {
                roomItem.setRoomName(roomMeta.getName());
            } else {
                roomItem.setRoomName(productDetail.getRoomName());
            }
            roomItem.setProducts(Collections.singletonList(productDetail));
            roomItems.add(roomItem);
        }

        return roomItems;
    }

    /**
     * 根据房型id前缀判断是否虚拟房型
     * 虚拟房型id 则必须是hashCol 开头
     * 并且是固定长度
     */
    public static boolean roomIdISVirtualRoom(String roomId) {
        return roomId.startsWith("99") && roomId.length() == 9;
    }

    /**
     * 设置床型信息
     */
    private void setBedTypesInfo(DhubProductDetailDto product, Map<Long, RoomMeta> roomMetaMap,String language) {
        // 如果产品床型信息为空，尝试从缓存中获取
        if (CollUtilX.isEmpty(product.getBedTypeDetails())) {
            RoomMeta roomMeta = roomMetaMap.get(product.getRoomId());
            if (roomMeta != null && CollUtilX.isNotEmpty(roomMeta.getBedTypes())) {
                product.setBedTypeDetails(roomMeta.getBedTypes());
                product.setBedTypeDiff(1);
            }
            return;
        }

        // 过滤并处理床型信息
        List<BedTypesDetailDto> filteredBedList = product.getBedTypeDetails().stream()
                .filter(bedTypeDetail -> CollUtilX.isNotEmpty(bedTypeDetail.getBedInfos()))
                .map(bedTypeDetail ->buildFilteredBedTypeDetail(bedTypeDetail,language))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        // 设置过滤后的床型信息
        product.setBedTypeDetails(filteredBedList.isEmpty() ? null : filteredBedList);

        // 构建床型描述并设置
        if (CollUtilX.isNotEmpty(product.getBedTypeDetails())) {
            String bedInfoDesc = buildCombinedBedDescription(product.getBedTypeDetails());
            product.setBedInfoDesc(bedInfoDesc);

            // 设置床型差异标志
            if (product.getBedTypeDiff() == null) {
                RoomMeta roomMeta = roomMetaMap.get(product.getRoomId());
                if (roomMeta != null) {
                    product.setBedTypeDiff(bedInfoDesc.equals(roomMeta.getBedInfoDesc()) ? 0 : 1);
                } else {
                    product.setBedTypeDiff(1);
                }
            }
        }
    }

    /**
     * 构建过滤后的床型详情
     */
    private BedTypesDetailDto buildFilteredBedTypeDetail(BedTypesDetailDto bedTypeDetail,String language) {
        List<BedInfoDto> filteredBedInfos = bedTypeDetail.getBedInfos().stream()
                .filter(bedInfo -> StrUtilX.isNotEmpty(bedInfo.getBedTypeCode()) && StrUtilX.isNotEmpty(bedInfo.getBedTypeName()))
                .collect(Collectors.toList());

        if (CollUtilX.isEmpty(filteredBedInfos)) {
            return null;
        }

        BedTypesDetailDto filteredDto = new BedTypesDetailDto();
        filteredDto.setBedInfos(filteredBedInfos);
        filteredDto.setBedInfoDesc(buildBedDescription(filteredBedInfos,language));
        return filteredDto;
    }

    /**
     * 构建床型描述
     */
//    private String buildBedDescription(List<BedInfoDto> beds,String language) {
//        StringBuilder sb = new StringBuilder();
//        for (int i = 0; i < beds.size(); i++) {
//            BedInfoDto bed = beds.get(i);
//            // 处理数量描述
//            if (bed.getBedNum() != null && bed.getBedNum() > 0) {
//                sb.append(bed.getBedNum()).append("张");
//            }
//            // 处理类型名称
//            sb.append(bed.getBedTypeName());
//            // 处理复数形式
//            if (bed.getBedNum() != null && bed.getBedNum() > 1) {
//                sb.append("s");
//            }
//            // 添加连接词
//            if (i < beds.size() - 1) {
//                sb.append("和");
//            }
//        }
//        return sb.toString();
//    }

    /**
     * 构建组合床型描述
     */
    private String buildCombinedBedDescription(List<BedTypesDetailDto> bedTypes) {
        return bedTypes.stream()
                .map(BedTypesDetailDto::getBedInfoDesc)
                .filter(StrUtilX::isNotEmpty)
                .collect(Collectors.joining("或"));
    }

    /**
     * 计算每日价格均价和总价
     */
    private List<DhubPriceItemDto> calculateDailyPrices(Collection<DhubPriceDetailItemDto> priceDetails, Integer originalProtocolPriceSwitch) {
        List<DhubPriceItemDto> priceItemList = new ArrayList<>();
        for (DhubPriceDetailItemDto priceDetail : priceDetails) {
            DhubPriceItemDto priceItem = new DhubPriceItemDto();
            priceItem.setSaleDate(priceDetail.getSaleDate());
            priceItem.setSalePrice(priceDetail.getSalePrice());
            priceItem.setCurrency(priceDetail.getCurrency());

            // 原始协议价开关
            if (originalProtocolPriceSwitch != null && originalProtocolPriceSwitch == 1) {
                priceItem.setBasePrice(priceDetail.getBasePrice());
            }
            priceItem.setBreakfastType(priceDetail.getBreakfastType());
            priceItem.setBreakfastNum(priceDetail.getBreakfastNum());
            priceItem.setRoomStatus(priceDetail.getRoomStatus());
            priceItem.setQuotaNum(priceDetail.getQuotaNum());

            // 设置限制条款信息
            priceItem.setMinRoomCount(priceDetail.getMinRoomCount());
            priceItem.setMaxRoomCount(priceDetail.getMaxRoomCount());
            priceItem.setBookRestrictionType(priceDetail.getBookRestrictionType());
            priceItem.setBookRestrictionDay(priceDetail.getBookRestrictionDay());
            priceItem.setBookRestrictionTime(priceDetail.getBookRestrictionTime());
            priceItem.setOccupancyRestrictionType(priceDetail.getOccupancyRestrictionType());
            priceItem.setOccupancyRestrictionDay(priceDetail.getOccupancyRestrictionDay());
            priceItem.setCancelRestrictionType(priceDetail.getCancelRestrictionType());
            priceItem.setCancelRestrictionDay(priceDetail.getCancelRestrictionDay());
            priceItem.setCancelRestrictionTime(priceDetail.getCancelRestrictionTime());
            priceItem.setCancelPenalties(priceDetail.getCancelPenalties());

            // 担保条款
            priceItem.setGuaranteeType(priceDetail.getGuaranteeType());
            priceItem.setGuaranteeCondition(priceDetail.getGuaranteeCondition());
            priceItem.setGuaranteeFeeType(priceDetail.getGuaranteeFeeType());

            priceItemList.add(priceItem);
        }

        return priceItemList;
    }

    /**
     * 设置发票模式，到店付的发票模式必须是酒店前台开票
     */
    private void setInvoiceModelInfo(DhubProductDetailDto product) {
        if (product.getPayAtHotelFlag() != null && product.getPayAtHotelFlag() == 1) {
            product.setInvoiceModel(2);
        }
    }

    /**
     * 设置供应商标签
     */
    private void setSupplierLabelInfo(DhubProductDetailDto product, String agentCode) {
        Map<Object, Object> agentSupplierLabelMap = ProductInitializer.getAgentSupplierLabelMap();
        if (agentSupplierLabelMap == null) {
            return;
        }
        Object supplierLabel = agentSupplierLabelMap.get(agentCode + "_" + product.getSupplyCode());
        if (Objects.nonNull(supplierLabel)) {
            product.setProductLabel(Integer.parseInt(supplierLabel.toString()));
        }
    }

    /**
     * 落地和不落地数据合并
     */
    private DhubProductDetailInfoResponse detailMerge(DhubProductDetailInfoResponse agreementInfo, DhubProductDetailInfoResponse directInfo, DhubProductDetailInfoResponse detailInfo, ProductDetailRequest request) {
        DhubProductDetailInfoResponse productDetailResponse = new DhubProductDetailInfoResponse();
        Date start = new Date();
        try {
            Long hotelId = Objects.isNull(agreementInfo.getHotelId()) ? directInfo.getHotelId() : agreementInfo.getHotelId();
            if (hotelId == null) {
                return null;
            }
            productDetailResponse.setHotelId(hotelId);

            if (directInfo.getRoomItems() == null) {
                directInfo.setRoomItems(new ArrayList<>());
            }
            Map<Long, List<DhubRoomItemDto>> roomItemMap = Stream.of(agreementInfo, directInfo)
                    .map(DhubProductDetailInfoResponse::getRoomItems)
                    .filter(CollUtilX::isNotEmpty)
                    .flatMap(Collection::stream)
                    .collect(groupingBy(DhubRoomItemDto::getRoomId, toList()));
            //获取客户的优选产品是否开启
            Boolean isPreferredProducts;
            if (Objects.nonNull(request) && StrUtil.isNotBlank(request.getAgentCode())) {
                Object preferredProductsStatusObject = RedisTemplateX.hashGet(RedisKey.AGENT_PREFERRED_PRODUCTS_STATUS, request.getAgentCode());
                if (Objects.isNull(preferredProductsStatusObject) || "1".equals(preferredProductsStatusObject.toString())) {
                    //默认开启
                    isPreferredProducts = Boolean.TRUE;
                } else {
                    isPreferredProducts = Boolean.FALSE;
                }
            } else {
                isPreferredProducts = Boolean.FALSE;
            }
            List<DhubRoomItemDto> roomItems = Lists.newArrayList();
            roomItemMap.forEach((key, value) -> {
                DhubRoomItemDto roomItem = new DhubRoomItemDto();
                if (value.stream().findFirst().isPresent()) {
                    String roomName = value.stream().findFirst().get().getRoomName();
                    roomItem.setRoomName(roomName);
                }
                roomItem.setRoomId(key);
                List<DhubProductDetailDto> productDetails = value.stream().map(item ->
                        item.getProducts().stream().filter(this::hasValidPrices).collect(toList())
                ).flatMap(Collection::stream).collect(toList());
                //进行优选产品
                if (isPreferredProducts) {
                    productDetails = getPreferredProductsByRoom(productDetails);
                }
                roomItem.setProducts(productDetails);
                roomItems.add(roomItem);
            });

            // 产品排序
            roomItems.forEach(roomItem -> {
                if (roomItem.getProducts().size() > 1) {
                    // 排序（可订大于不可定、价格低大于价格高）
                    roomItem.getProducts().sort(Comparator.comparing(DhubProductDetailDto::getTotalSalePrice));
                }
                // 设置最低价
                roomItem.setLowPrice(roomItem.getProducts().get(0).getAvgPrice());
            });
            if (roomItems.size() > 1) {
                // 房型排序（价格低大于价格高）
                roomItems.sort(Comparator.comparing(DhubRoomItemDto::getLowPrice));
            }
            productDetailResponse.setRoomItems(roomItems);

            // 设置时区
            if (detailInfo != null) {
                productDetailResponse.setTimeZone(detailInfo.getTimeZone());
            }
        } catch (Exception e) {
            log.error("detailMerge error!", e);
        } finally {
            saveSlsLog("排查慢问题：落地和不落地数据合并", start, request.getRequestId());
        }

        return productDetailResponse;
    }

    /**
     * 判断价格是否有效
     */
    private boolean hasValidPrices(DhubProductDetailDto product) {
        return product.getPriceItems().stream()
                .noneMatch(price -> price.getSalePrice() != null
                        && price.getSalePrice().compareTo(BigDecimal.ZERO) <= 0);
    }


    /**
     * 针对房型的下面的所有产品进行优选
     */
    private List<DhubProductDetailDto> getPreferredProductsByRoom(List<DhubProductDetailDto> productDetails) {
        //用来保存有任意标签的产品(不参与优选)
        List<DhubPreferredProductDTO> productDetailListTmp = new ArrayList<>();
        Map<String, List<DhubPreferredProductDTO>> resultMap = new HashMap<>();
        for (DhubProductDetailDto productDetail : productDetails) {
            Map<Integer, Set<String>> tipsDetailGroup = new HashMap<>();

            //如果存在产品标签，则临时保存不做任何处理，等最后再和优选之后的产品进行合并输出。
            if ((Objects.nonNull(productDetail.getProductLabel()) && !productDetail.getProductLabel().equals(0))
                    || (Objects.nonNull(productDetail.getSupplierLabel()) && !productDetail.getSupplierLabel().equals(0))) {
                productDetailListTmp.add(new DhubPreferredProductDTO(productDetail, tipsDetailGroup));
                continue;
            }

            //不存在产品标签，则按照产品类型->早餐数量->取消条款->提示信息依次进行拼接
            StringBuilder key = new StringBuilder();

            //产品类型
            key.append(productDetail.getPayAtHotelFlag()).append("_");

            //取房间列表的第一个对象的对应的售卖列表的第一个对象作为代表(正常情况下是第一间的第一天)
            if (CollectionUtil.isNotEmpty(productDetail.getRoomItemDetails())
                    && CollectionUtil.isNotEmpty(productDetail.getRoomItemDetails().get(0).getPriceItems())) {

                List<DhubPriceDetailItemDto> priceItems = productDetail.getRoomItemDetails().get(0).getPriceItems();
                DhubPriceDetailItemDto priceItem = priceItems.get(0);

                //早餐数量
                key.append(priceItem.getBreakfastNum()).append("_");

                //取消条款
                key.append(priceItem.getCancelRestrictionType()).append("_");
            } else {
                //占位
                key.append("null_null_");
            }

            //提示类型
            if (CollectionUtil.isNotEmpty(productDetail.getTips())) {
                //按照对应的数量先进行排列,以免相同的数据因排列的顺序不同而分成了不同组
                productDetail.getTips().sort(Comparator.comparingInt((TipInfosDTO t) -> Objects.nonNull(t.getTipsType()) ? t.getTipsType() : 0)
                        .thenComparingInt(t -> CollectionUtil.isNotEmpty(t.getTipsDetails()) ? t.getTipsDetails().size() : 0));
                for (TipInfosDTO tipInfo : productDetail.getTips()) {
                    //提示类型以及对应的提示列表数量
                    key.append(tipInfo.getTipsType()).append("_")
                            .append(CollectionUtil.isNotEmpty(tipInfo.getTipsDetails()) ? tipInfo.getTipsDetails().size() : 0).append("_");

                    //根据提示类型进行分组,同一组的则把提示信息进行拼接,用于后面的比较
                    Set<String> tipsDetailSet = tipsDetailGroup.get(tipInfo.getTipsType());
                    if (CollectionUtil.isEmpty(tipsDetailSet)) {
                        tipsDetailSet = new HashSet<>();
                    }
                    for (TipsDetail tipsDetail : tipInfo.getTipsDetails()) {
                        tipsDetailSet.add(tipsDetail.getTitle() + "_" + tipsDetail.getDetails());
                    }
                    tipsDetailGroup.put(tipInfo.getTipsType(), tipsDetailSet);
                }
            } else {
                //占位
                key.append("null_null_");
            }

            //查看当前Map里面是否存在,不存在则表示是新的对象,则存入其中,存在则进一步进行比较
            List<DhubPreferredProductDTO> productDetailsCurrent = resultMap.get(key.toString());
            if (CollectionUtil.isEmpty(productDetailsCurrent)) {
                productDetailsCurrent = new ArrayList<>();
                productDetailsCurrent.add(new DhubPreferredProductDTO(productDetail, tipsDetailGroup));
                resultMap.put(key.toString(), productDetailsCurrent);
            } else if (CollectionUtil.isEmpty(tipsDetailGroup)) {
                //存在则依次进行对比,当内容存在不一样的,则直接保存对象,当全部内容都是一样,则对比较totalSalePrice,如果当前对象的更小则替换列表里面的对象
                //没有卖点信息
                boolean isBreak = Boolean.FALSE;
                for (int i = 0; i < productDetailsCurrent.size(); i++) {
                    DhubPreferredProductDTO preferredProductDTO = productDetailsCurrent.get(i);
                    //当前列表存在同样都是无卖点信息的,进行价格对之后,决定是否要过滤掉
                    if (CollectionUtil.isEmpty(preferredProductDTO.getTipsDetailGroup())) {
                        //替换进去
                        if (productDetail.getTotalSalePrice().compareTo(Objects.isNull(preferredProductDTO.getProductDetail().getTotalSalePrice()) ? BigDecimal.ZERO : preferredProductDTO.getProductDetail().getTotalSalePrice()) < 0) {
                            productDetailsCurrent.set(i, new DhubPreferredProductDTO(productDetail, tipsDetailGroup));
                        }
                        //此产品不符合条件,被过滤掉
                        isBreak = Boolean.TRUE;
                        break;
                    }
                }
                if (!isBreak) {
                    //当前列表里面都是有卖点信息的
                    productDetailsCurrent.add(new DhubPreferredProductDTO(productDetail, tipsDetailGroup));
                }
            } else {
                //存在则依次进行对比,当内容存在不一样的,则直接保存对象,当全部内容都是一样,则对比较totalSalePrice,如果当前对象的更小则替换列表里面的对象
                //有卖点信息
                boolean isBreak = Boolean.FALSE;
                for (int i = 0; i < productDetailsCurrent.size(); i++) {
                    DhubPreferredProductDTO preferredProductDTO = productDetailsCurrent.get(i);
                    if (CollectionUtil.isNotEmpty(preferredProductDTO.getTipsDetailGroup())) {
                        Map<Integer, Set<String>> tipsDetailGroupMap = preferredProductDTO.getTipsDetailGroup();

                        // 当前对象的卖点信息和列表对象的卖点信息进行比较，如果全部内容都是一样，则对比较totalSalePrice，如果当前对象的更小则替换列表里面的对象，如果内容不一样，则跳过这个列表对象，与下一个列表对象进行比较，如果最终内容都是不一样的，则保存当前对象到列表里面
                        boolean isSame = Boolean.TRUE;
                        for (Map.Entry<Integer, Set<String>> tipsDetailGroupEntry : tipsDetailGroup.entrySet()) {
                            Set<String> tipsType = tipsDetailGroupMap.get(tipsDetailGroupEntry.getKey());
                            if (CollectionUtil.isEmpty(tipsType) || !tipsType.containsAll(tipsDetailGroupEntry.getValue())) {
                                //存在不一样的卖点信息
                                isSame = Boolean.FALSE;
                                break;
                            }
                        }
                        if (isSame) {
                            //全部内容都是一样，则对比较totalSalePrice，如果当前对象的更小则替换列表里面的对象，否则则过滤掉当前对象
                            if (productDetail.getTotalSalePrice().compareTo(Objects.isNull(preferredProductDTO.getProductDetail().getTotalSalePrice()) ? BigDecimal.ZERO : preferredProductDTO.getProductDetail().getTotalSalePrice()) < 0) {
                                productDetailsCurrent.set(i, new DhubPreferredProductDTO(productDetail, tipsDetailGroup));
                            }
                            //此产品不符合条件,被过滤掉
                            isBreak = Boolean.TRUE;
                            break;
                        }

                    }
                }
                if (!isBreak) {
                    //当前列表里面只有一个无卖点信息的，或者列表里面的对象的卖点信息都和当前的不一样，则直接保存对象
                    productDetailsCurrent.add(new DhubPreferredProductDTO(productDetail, tipsDetailGroup));
                }
            }
        }

        //合并对象进行输出
        return Stream.concat(
                        productDetailListTmp.stream(),
                        resultMap.values().stream().flatMap(List::stream)
                ).map(DhubPreferredProductDTO::getProductDetail)
                .collect(Collectors.toList());
    }

    /**
     * 查询酒店时区
     *
     * @param request
     * @return
     */
    @Override
    public Response<HotelTimeZoneResp> queryHotelTimeZone(HotelTimeZoneRequest request) {
        try {
            // 构建酒店信息查询请求
            HotelInfoCollectionReq hotelInfoReq = new HotelInfoCollectionReq();
            hotelInfoReq.setHotelIds(Collections.singletonList(request.getHotelId()));
            if (StrUtilX.isNotEmpty(request.getLanguage())) {
                hotelInfoReq.setLanguageType(request.getLanguage());
            } else {
                hotelInfoReq.setLanguageType(LanguageTypeEnum.zh_CN.getValue());
            }
            List<String> settings = new ArrayList<>();
            settings.add(BaseHotelInfoUrl.location);
            hotelInfoReq.setSettings(settings);

            // 查询酒店信息
            Response<HotelInfoCollectionDTO> response = hotelRemote.queryHotelInfo(hotelInfoReq);
            if (response.isSuccess() && response.getModel() != null && response.getModel().getLocation() != null) {
                LocationDTO location = response.getModel().getLocation();

                // 查询标准时区
                AreaDataTimeZoneReqDTO dto = new AreaDataTimeZoneReqDTO();
                dto.setCountryCode(location.getCountry());
                dto.setProvinceCode(location.getProvince());

                Response<String> timeZoneResponse = TimeZoneUtil.getStandardTimeZone(dto);
                if (timeZoneResponse.isSuccess()) {
                    return Response.success(new HotelTimeZoneResp(timeZoneResponse.getModel()));
                }
            }
            return Response.success(null);
        } catch (Exception e) {
            log.error("查询酒店时区异常", e);
            return Response.error();
        }
    }
}