package com.tiangong.product.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.tiangong.common.Constant;
import com.tiangong.common.Response;
import com.tiangong.constanct.MqConstants;
import com.tiangong.dis.dto.*;
import com.tiangong.dis.dto.ProductSaleStatusDTO;
import com.tiangong.dis.remote.InitRemote;
import com.tiangong.dto.common.*;
import com.tiangong.dto.hotel.DestinationReq;
import com.tiangong.dto.hotel.EsHotelDto;
import com.tiangong.dto.hotel.base.HotelInfoCollectionDTO;
import com.tiangong.dto.hotel.base.HotelInfoCollectionReq;
import com.tiangong.dto.hotel.base.RoomtypeDTO;
import com.tiangong.dto.order.CancelRestriction;
import com.tiangong.dto.product.RoomTaxDetail;
import com.tiangong.dto.product.SupplyHotelIncrease;
import com.tiangong.dto.product.SupplyIncrease;
import com.tiangong.dto.product.TaxDetailDto;
import com.tiangong.dto.product.response.HotelIdDto;
import com.tiangong.enums.*;
import com.tiangong.exception.SysException;
import com.tiangong.finance.OrgDTO;
import com.tiangong.hotel.BaseHotelInfoUrl;
import com.tiangong.hotel.remote.HotelHeatRemote;
import com.tiangong.hotel.remote.HotelRemote;
import com.tiangong.hotel.req.HotelAvgPriceReq;
import com.tiangong.hotel.req.HotelHeatReq;
import com.tiangong.initializer.CommonInitializer;
import com.tiangong.keys.RedisKey;
import com.tiangong.organization.remote.dto.AgentAccountConfig;
import com.tiangong.organization.remote.dto.SupplierAddDTO;
import com.tiangong.product.config.BedNameConfig;
import com.tiangong.product.config.SettingsConstant;
import com.tiangong.product.convert.CommonConvert;
import com.tiangong.product.domain.*;
import com.tiangong.product.domain.entity.AgentHotelLowestPriceEntity;
import com.tiangong.product.domain.req.*;
import com.tiangong.product.domain.resp.HotelProductIdsResponse;
import com.tiangong.product.domain.resp.HotelSaleInfoResp;
import com.tiangong.product.domain.resp.HotelSaleInfoItemResp;
import com.tiangong.product.domain.resp.QueryProductIdsRequest;
import com.tiangong.product.dto.*;
import com.tiangong.product.dto.ProductSaleIncreaseDTO;
import com.tiangong.product.dto.mongo.SupplyHotelLowestPrice;
import com.tiangong.product.mapper.*;
import com.tiangong.product.service.ProTaxRuleConfigService;
import com.tiangong.product.service.ProductInfoService;
import com.tiangong.product.service.ProductSaleService;
import com.tiangong.product.util.QueryUtil;
import com.tiangong.product.util.RateUtil;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.redis.util.RedisUtil;
import com.tiangong.util.CollUtilX;
import com.tiangong.util.CommonTgUtils;
import com.tiangong.util.DateUtilX;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.web.bind.annotation.RequestBody;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.tiangong.util.DateUtilX.hour_format;

/**
 * @Auther: Owen
 * @Date: 2019/4/24 11:21
 * @Description: 产品销售service实现类
 */
@Slf4j
@Service("productSaleService")
public class ProductSaleServiceImpl implements ProductSaleService {

    @Autowired
    private ProductMapper productMapper;

    @Autowired
    private ProductPriceMapper productPriceMapper;

    @Autowired
    private ProductQuotaMapper productQuotaMapper;

    @Autowired
    private ProductDayIncreaseMapper productDayIncreaseMapper;

    @Autowired
    private ProductSaleStatusMapper productSaleStatusMapper;

    @Autowired
    private ProductIncreaseMapper productIncreaseMapper;

    @Autowired
    private ProductSaleLogMapper productSaleLogMapper;

    @Autowired
    private InitRemote initRemote;

    @Autowired
    private DebitedQuotaMapper debitedQuotaMapper;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private QueryUtil queryUtil;

    @Autowired
    private SettingsConstant settingsConstant;

    @Autowired
    private HotelHeatRemote hotelHeatRemote;

    @Autowired
    private HotelRemote hotelRemote;

    @Autowired
    private ProTaxRuleConfigService proTaxRuleConfigService;

    @Resource(name = "processingProductInfoExecutor")
    private ThreadPoolTaskExecutor processingProductInfoExecutor;

    @Autowired
    private BedNameConfig bedNameConfig;

    @Autowired
    private ProductInfoService productInfoService;

    @Resource
    private RocketMQTemplate rocketMQTemplate;

    @Override
    public Response<PaginationSupportDTO<HotelProductsDTO>> queryHotelList(QueryProductRequestDTO queryProductRequestDTO) {
        if (StrUtilX.isEmpty(queryProductRequestDTO.getChannelCode())) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
        if (null != queryProductRequestDTO.getPurchaseType() && queryProductRequestDTO.getPurchaseType().equals("-1")) {
            queryProductRequestDTO.setPurchaseType(null);
        }

        PaginationSupportDTO<HotelProductsDTO> paginationSupportDTO = new PaginationSupportDTO<>();

        PageHelper.startPage(queryProductRequestDTO.getCurrentPage(), queryProductRequestDTO.getPageSize());
        List<Long> saleHotelIds = productMapper.querySaleHotelIdsByStatistics(queryProductRequestDTO);
        if (CollUtilX.isNotEmpty(saleHotelIds)) {
            queryProductRequestDTO.setHotelIdList(saleHotelIds);
            // 查询底价，加幅，房态，配额等数据
            List<ProductTempDTO> productTempDTOList = productMapper.querySaleProducts(queryProductRequestDTO);
            if (CollUtilX.isNotEmpty(productTempDTOList)) {
                // 组装酒店售卖产品数据
                List<HotelProductsDTO> hotelProductsDTOList = this.assemblySaleHotelProduct(queryProductRequestDTO, productTempDTOList, saleHotelIds, queryProductRequestDTO.getChannelCode());

                PageInfo<Long> page = new PageInfo<>(saleHotelIds);
                paginationSupportDTO.setItemList(hotelProductsDTOList);
                paginationSupportDTO.setCurrentPage(page.getPageNum());
                paginationSupportDTO.setPageSize(page.getPageSize());
                paginationSupportDTO.setTotalCount(page.getTotal());
                paginationSupportDTO.setTotalPage(page.getPages());
                return Response.success(paginationSupportDTO);
            }
        }
        return Response.success(null);
    }

    @Override
    public Response<ProductSalePriceDetailDTO> querySalePriceList(QueryProductRequestDTO queryProductRequestDTO) {
        if (StrUtilX.isEmpty(queryProductRequestDTO.getProductId()) || StrUtilX.isEmpty(queryProductRequestDTO.getChannelCode())
                || StrUtilX.isEmpty(queryProductRequestDTO.getStartDate()) || StrUtilX.isEmpty(queryProductRequestDTO.getEndDate())
                || DateUtilX.compare(DateUtilX.stringToDate(queryProductRequestDTO.getStartDate()), DateUtilX.stringToDate(queryProductRequestDTO.getEndDate())) > 0) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
        if (StrUtilX.isEmpty(queryProductRequestDTO.getCompanyCode())) {
            queryProductRequestDTO.setCompanyCode(CompanyDTO.COMPANY_CODE);
        }
        // 获取日期列表
        Map<Date, Date> dateMap = DateUtilX.getDateMap(DateUtilX.stringToDate(queryProductRequestDTO.getStartDate()), DateUtilX.stringToDate(queryProductRequestDTO.getEndDate()));
        // 查询产品
        ProductDTO productDTO = productMapper.queryProduct(queryProductRequestDTO.getProductId());
        ProductSalePriceDetailDTO productSalePriceDetailDTO = CommonConvert.INSTANCE.productSalePriceDetailDTOConvert(productDTO);
        productSalePriceDetailDTO.setPayAtHotelFlag(productDTO.getPayMethod());
        // 查询酒店信息
        HotelInfoCollectionReq hotelInfoCollectionReq = new HotelInfoCollectionReq();
        hotelInfoCollectionReq.setHotelIds(Collections.singletonList(productDTO.getHotelId()));
        hotelInfoCollectionReq.setLanguageType(queryProductRequestDTO.getLanguage());
        List<String> settings = new ArrayList<>();
        settings.add(BaseHotelInfoUrl.hotelName);
        settings.add(BaseHotelInfoUrl.rooms);
        hotelInfoCollectionReq.setSettings(settings);
        Response<HotelInfoCollectionDTO> response = hotelRemote.queryHotelInfo(hotelInfoCollectionReq);
        // 设置酒店名称和房型名称
        if (response.isSuccess() && response.getModel() != null) {
            productSalePriceDetailDTO.setHotelName(response.getModel().getHotelName());
            if (CollUtilX.isNotEmpty(response.getModel().getRooms())) {
                for (RoomtypeDTO room : response.getModel().getRooms()) {
                    if (room.getRoomtypeId().compareTo(productSalePriceDetailDTO.getRoomId().longValue()) == 0) {
                        productSalePriceDetailDTO.setRoomName(room.getRoomtypeName());
                        break;
                    }
                }
            }
        }
        if (productDTO.getCancellationType() != null) {
            List<CancelRestriction> cancelRestrictions = new ArrayList<>();
            CancelRestriction cancelRestriction = new CancelRestriction();
            cancelRestriction.setCancelRestrictionDay(productDTO.getCancellationAdvanceDays());
            cancelRestriction.setCancelRestrictionTime(productDTO.getCancellationDueTime());
            if (StrUtilX.isNotEmpty(productDTO.getSaleDate()) && productDTO.getCancellationAdvanceDays() != null) {
                cancelRestriction.setCancelRestrictionDate(DateUtilX.dateToString(DateUtilX.getDate(DateUtilX.stringToDate(productDTO.getSaleDate()), -productDTO.getCancellationAdvanceDays())));
            }
            // 自签产品特殊处理
            if (productDTO.getCancellationType() == 2) {
                cancelRestriction.setCancelRestrictionType(3);
            } else {
                cancelRestriction.setCancelRestrictionType(productDTO.getCancellationType());
            }

            if (productDTO.getCancelPenaltiesValue() != null) {
                cancelRestriction.setCancellationPrice(new BigDecimal(String.valueOf(productDTO.getCancelPenaltiesValue())));
            }
            cancelRestriction.setCancelPenaltiesType(productDTO.getCancelPenaltiesType());
            cancelRestriction.setCancelPenaltiesValue(productDTO.getCancelPenaltiesValue());
            cancelRestrictions.add(cancelRestriction);

            StringBuilder stringBuilder = new StringBuilder();
            if (productDTO.getCancelPenaltiesType() != null) {
                if (productDTO.getCancelPenaltiesType().equals(4)) {
                    if (productDTO.getCancelPenaltiesValue().equals(100.0)) {
                        stringBuilder.append("取消或更改,收取首晚房费");
                    } else {
                        stringBuilder.append("取消或更改,扣首晚房费的").append(String.valueOf(productDTO.getCancelPenaltiesValue())).append("%，作为退订费。");
                    }
                } else if (productDTO.getCancelPenaltiesType().equals(3)) {
                    stringBuilder.append("取消或更改,扣全额房费的").append(String.valueOf(productDTO.getCancelPenaltiesValue())).append("%，作为退订费");
                }
            }

            cancelRestriction.setCancelRestrictionRemark(stringBuilder.toString());
            productSalePriceDetailDTO.setCancelRestrictions(cancelRestrictions);
        }


        ProTaxRuleConfigResp proTaxRuleConfigResp = null;
        // 获取税费规则
        if (productDTO.getPriceType() != null
                && productDTO.getPriceType() == 1
                && productDTO.getTaxRuleConfigId() != null) {
            ProTaxRuleConfigQueryReq proTaxRuleConfigQueryReq = new ProTaxRuleConfigQueryReq();
            proTaxRuleConfigQueryReq.setId(productDTO.getTaxRuleConfigId());
            proTaxRuleConfigResp = proTaxRuleConfigService.proTaxRuleConfigDetail(proTaxRuleConfigQueryReq);
        }


        if (productDTO.getSupplyType() != null && productDTO.getSupplyType() == 2) {
            assemblySupplyDailyPrice(dateMap, productSalePriceDetailDTO, queryProductRequestDTO.getChannelCode(), productDTO.getCurrency(), queryProductRequestDTO.getCompanyCode());
        } else {
            // 查询底价
            Example priceExample = new Example(ProductPricePO.class);
            Example.Criteria priceCriteria = priceExample.createCriteria();
            priceCriteria.andEqualTo("productId", queryProductRequestDTO.getProductId());
            priceCriteria.andBetween("saleDate", queryProductRequestDTO.getStartDate(), queryProductRequestDTO.getEndDate());
            List<ProductPricePO> pricePOList = productPriceMapper.selectByExample(priceExample);
            // 查询配额和房态
            List<ProductQuotaPO> productQuotaPOList = null;
            Map<String, Integer> debitedQuotaMap = new HashMap<>();
            if (null != productDTO.getQuotaAccountId()) {
                Example quotaExample = new Example(ProductQuotaPO.class);
                Example.Criteria quotaCriteria = quotaExample.createCriteria();
                quotaCriteria.andEqualTo("quotaAccountId", productDTO.getQuotaAccountId());
                quotaCriteria.andBetween("saleDate", queryProductRequestDTO.getStartDate(), queryProductRequestDTO.getEndDate());
                productQuotaPOList = productQuotaMapper.selectByExample(quotaExample);

                Example debitedExample = new Example(DebitedQuotaPO.class);
                Example.Criteria debitedCriteria = debitedExample.createCriteria();
                debitedCriteria.andEqualTo("quotaAccountId", productDTO.getQuotaAccountId());
                debitedCriteria.andEqualTo("productId", productDTO.getProductId());
                debitedCriteria.andBetween("saleDate", queryProductRequestDTO.getStartDate(), queryProductRequestDTO.getEndDate());
                List<DebitedQuotaPO> debitedQuotaPOList = debitedQuotaMapper.selectByExample(debitedExample);
                if (CollUtilX.isNotEmpty(debitedQuotaPOList)) {
                    for (DebitedQuotaPO debitedQuotaPO : debitedQuotaPOList) {
                        if (null != debitedQuotaPO.getQuota()) {
                            if (null == debitedQuotaMap.get(debitedQuotaPO.getSaleDate())) {
                                debitedQuotaMap.put(debitedQuotaPO.getSaleDate(), -debitedQuotaPO.getQuota());
                            } else {
                                debitedQuotaMap.put(debitedQuotaPO.getSaleDate(), debitedQuotaMap.get(debitedQuotaPO.getSaleDate()) - debitedQuotaPO.getQuota());
                            }
                        }
                    }
                }
            }


            // 查询加幅
            Example salePriceExample = new Example(ProductDayIncreasePO.class);
            Example.Criteria salePriceCriteria = salePriceExample.createCriteria();
            salePriceCriteria.andEqualTo("productId", queryProductRequestDTO.getProductId());
            salePriceCriteria.andEqualTo("companyCode", queryProductRequestDTO.getCompanyCode());
            salePriceCriteria.andBetween("saleDate", queryProductRequestDTO.getStartDate(), queryProductRequestDTO.getEndDate());
            List<ProductDayIncreasePO> productDayIncreasePOList = productDayIncreaseMapper.selectByExample(salePriceExample);
            // 组装数据
            this.assemblyProductSalePrice(proTaxRuleConfigResp, dateMap, productSalePriceDetailDTO, pricePOList, productQuotaPOList, productDayIncreasePOList, productDTO, queryProductRequestDTO);
        }
        return Response.success(productSalePriceDetailDTO);
    }


    /**
     * 组装供应商产品
     */
    private void assemblySupplyDailyPrice(Map<Date, Date> dateMap, ProductSalePriceDetailDTO productSalePriceDTO, String channelCode, Integer currency, String companyCode) {
        Map<Date, ProductSalePriceItemDTO> productSaleItemDTOMap = new TreeMap<Date, ProductSalePriceItemDTO>(new Comparator<Date>() {
            public int compare(Date obj1, Date obj2) {
                // 升序排序
                return obj1.compareTo(obj2);
            }
        });


        // 查询币种对应的汇率
        BigDecimal rate = RateUtil.getToMerchantRate(currency);

        for (Date date : dateMap.keySet()) {
            ProductSalePriceItemDTO productSalePriceItemDTO = new ProductSalePriceItemDTO();
            productSalePriceItemDTO.setSaleDate(DateUtilX.dateToString(date));

            ProductBasePriceAndRoomStatusDTO productBasePriceAndRoomStatusDTO = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.productBasePriceAndRoomStatusKey,
                    StrUtilX.concat(String.valueOf(productSalePriceDTO.getProductId()), "_", DateUtilX.dateToString(date))), ProductBasePriceAndRoomStatusDTO.class);
            if (null != productBasePriceAndRoomStatusDTO && null != productBasePriceAndRoomStatusDTO.getBasePrice() && productBasePriceAndRoomStatusDTO.getBasePrice().compareTo(BigDecimal.ZERO) > 0) {
                productSalePriceItemDTO.setBasePrice(productBasePriceAndRoomStatusDTO.getBasePrice());
                productSalePriceItemDTO.setEquivalentBasePrice(productBasePriceAndRoomStatusDTO.getBasePrice().multiply(rate));
                productSalePriceItemDTO.setOverDraftStatus(productBasePriceAndRoomStatusDTO.getOverDraftStatus());
                productSalePriceItemDTO.setQuota(productBasePriceAndRoomStatusDTO.getQuota());
                productSalePriceItemDTO.setRemainingQuota(productBasePriceAndRoomStatusDTO.getRemainingQuota());
                productSalePriceItemDTO.setRoomStatus(productBasePriceAndRoomStatusDTO.getRoomStatus());

                SupplierSaleIncreaseDTO supplierSaleIncreaseDTO = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.noCachedProductSaleIncreaseKey,
                        StrUtilX.concat(productSalePriceDTO.getHotelId().toString(), "_", DateUtilX.dateToString(date), "_", productSalePriceDTO.getSupplierCode(), "_", companyCode)), SupplierSaleIncreaseDTO.class);

                if (supplierSaleIncreaseDTO == null) {
                    supplierSaleIncreaseDTO = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.noCachedProductSaleIncreaseKey,
                            StrUtilX.concat(productSalePriceDTO.getSupplierCode(), "_", companyCode)), SupplierSaleIncreaseDTO.class);
                }
                if (supplierSaleIncreaseDTO == null) {
                    log.error("供应商加幅信息不存在，supplyCode={}", productSalePriceDTO.getSupplierCode());
                    throw new SysException(ErrorCodeEnum.SYSTEM_EXCEPTION);
                }
                BigDecimal basePrice = productBasePriceAndRoomStatusDTO.getBasePrice();//底价
                Integer adjustmentType = null;//调整方式（0加数值 1减数值 2加百分比 3减百分比 4等于）
                BigDecimal modifiedAmt = null;//调整金额
                if (ChannelEnum.getNoByKey(channelCode) == 0) {
                    adjustmentType = supplierSaleIncreaseDTO.getB2bAdjustmentType();
                    modifiedAmt = supplierSaleIncreaseDTO.getB2bModifiedAmt();
                }


                // 没有加幅方式或者没有加幅金额，则没有售价和利润
                if (null != adjustmentType && null != modifiedAmt) {
                    BigDecimal zero = new BigDecimal("0");
                    switch (adjustmentType) {
                        case 0:
                            productSalePriceItemDTO.setSalePrice(basePrice.multiply(rate).add(modifiedAmt).setScale(2, RoundingMode.HALF_DOWN));
                            productSalePriceItemDTO.setProfit(modifiedAmt);
                            break;
                        case 1:
                            productSalePriceItemDTO.setSalePrice(basePrice.multiply(rate).subtract(modifiedAmt).compareTo(zero) > 0 ?
                                    basePrice.multiply(rate).subtract(modifiedAmt).setScale(2, RoundingMode.HALF_DOWN) : zero);
                            productSalePriceItemDTO.setProfit(basePrice.multiply(rate).subtract(modifiedAmt).compareTo(zero) > 0 ?
                                    zero.subtract(modifiedAmt).setScale(2, RoundingMode.HALF_DOWN) : zero.subtract(basePrice).setScale(2, RoundingMode.HALF_DOWN));
                            break;
                        case 2:
                            productSalePriceItemDTO.setSalePrice(basePrice.multiply(rate).add(basePrice.multiply(modifiedAmt)).setScale(2, RoundingMode.HALF_DOWN));
                            productSalePriceItemDTO.setProfit(basePrice.multiply(rate).multiply(modifiedAmt).setScale(2, RoundingMode.HALF_DOWN));
                            break;
                        case 3:
                            productSalePriceItemDTO.setSalePrice(basePrice.multiply(rate).subtract(basePrice.multiply(modifiedAmt)).setScale(2, RoundingMode.HALF_DOWN).compareTo(BigDecimal.ZERO) < 0 ?
                                    BigDecimal.ZERO : basePrice.multiply(rate).subtract(basePrice.multiply(modifiedAmt)).setScale(2, RoundingMode.HALF_DOWN));
                            productSalePriceItemDTO.setProfit(BigDecimal.ZERO.subtract(basePrice.multiply(rate).multiply(modifiedAmt).setScale(2, RoundingMode.HALF_DOWN)));
                            break;
                        case 4:
                            productSalePriceItemDTO.setSalePrice(modifiedAmt);
                            productSalePriceItemDTO.setProfit(modifiedAmt.subtract(basePrice.multiply(rate)).setScale(2, RoundingMode.HALF_DOWN));
                            break;
                        default:
                    }
                }
            }
            productSaleItemDTOMap.put(date, productSalePriceItemDTO);
        }
        List<ProductSalePriceItemDTO> productSaleItemDTOList = new ArrayList<>();
        for (Date date : productSaleItemDTOMap.keySet()) {
            productSaleItemDTOList.add(productSaleItemDTOMap.get(date));
        }
        productSalePriceDTO.setPriceList(productSaleItemDTOList);
    }

    @Override
    @Transactional
    public void singleProductModifySaleStatus(@RequestBody Map<String, String> requestMap) {
        // 参数校验：产品Id，渠道编码，售卖状态
        if (StrUtilX.isEmpty(requestMap.get("productId")) || StrUtilX.isEmpty(requestMap.get("saleStatus"))
                || StrUtilX.isEmpty(requestMap.get("channelCode"))) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
        ProductSaleStatusPO productSaleStatusPO = new ProductSaleStatusPO();
        productSaleStatusPO.setUpdatedBy(requestMap.get("updatedBy"));
        productSaleStatusPO.setUpdatedDt(requestMap.get("updatedDt"));
        productSaleStatusPO.setProductId(Integer.valueOf(requestMap.get("productId")));
        productSaleStatusPO.setCompanyCode(requestMap.get("companyCode"));
        if (ChannelEnum.getNoByKey(requestMap.get("channelCode")) == 0) {
            productSaleStatusPO.setB2bSaleStatus(Integer.valueOf(requestMap.get("saleStatus")));
        }

        List<ProductSaleStatusPO> productSaleStatusList = new ArrayList<>(Collections.singletonList(productSaleStatusPO));
        List<String> increment = handlerProductSaleStatus(productSaleStatusList);

        if (CollUtilX.isNotEmpty(productSaleStatusList)) {
            Example productSaleStatusExample = new Example(ProductSaleStatusPO.class);
            Example.Criteria criteria = productSaleStatusExample.createCriteria();
            criteria.andEqualTo("productId", requestMap.get("productId"));
            criteria.andEqualTo("companyCode", requestMap.get("companyCode"));
            productSaleStatusMapper.updateByExampleSelective(productSaleStatusPO, productSaleStatusExample);

            ProductSaleLogPO productSaleLogPO = new ProductSaleLogPO();
            productSaleLogPO.setChannelCode(requestMap.get("channelCode"));
            productSaleLogPO.setCompanyCode(requestMap.get("companyCode"));
            productSaleLogPO.setContent("销售状态改为\"" + SaleStatusEnum.getDesc(Integer.parseInt(requestMap.get("saleStatus"))) + "\"");
            productSaleLogPO.setOperationType(ProductSaleOperationEnum.MODIFY_SALE.no);
            productSaleLogPO.setCreatedBy(requestMap.get("updatedBy"));
            productSaleLogPO.setCreatedDt(requestMap.get("updatedDt"));
            productSaleLogPO.setProductId(Integer.valueOf(requestMap.get("productId")));
            productSaleLogMapper.insert(productSaleLogPO);

            IncrementObjectDTO incrementObjectDTO = new IncrementObjectDTO();
//            incrementObjectDTO.setObject(productSaleStatusList.stream().map(t -> CommonConvert.INSTANCE.productSaleStatusDTOConvert(t)).collect(Collectors.toList()));
            incrementObjectDTO.setObject(productSaleStatusList.stream().map(CommonConvert.INSTANCE::productSaleStatusDTOConvert).collect(Collectors.toList()));
            incrementObjectDTO.setIncrementList(increment);
            initRemote.initSaleStatus(incrementObjectDTO);

            // 注册事务同步器
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    // 提交队列处理
                    if (StrUtilX.isNotEmpty(requestMap.get("hotelId")) && StrUtilX.isNotEmpty(requestMap.get("supplierCode"))) {
                        List<HotelSaleInfoItemResp> itemRespList = Collections.singletonList(HotelSaleInfoItemResp.builder()
                                .hotelId(Long.valueOf(requestMap.get("hotelId")))
                                .supplierCode(requestMap.get("supplierCode"))
                                .startDate(DateUtilX.dateToString(DateUtilX.getCurrentDate()))
                                .endDate(DateUtilX.dateToString(DateUtilX.getDate(DateUtilX.getCurrentDate(), 59, 0)))
                                .saleStatus(Integer.valueOf(requestMap.get("saleStatus")))
                                .build());

                        RedisTemplateX.convertAndSend(RedisKey.CALCULATE_SELF_HOTEL_LOWEST, JSONUtil.toJsonStr(HotelSaleInfoResp.builder()
                                .logId(UUID.randomUUID().toString())
                                .respList(itemRespList)
                                .build()));
                    }
                }
            });
        }
    }

    @Override
    @Transactional
    public void dailyModifySalePrice(@RequestBody Map<String, String> requestMap) {
        //参数校验：产品Id，日期，渠道编码，调整方式，调整金额
        if (StrUtilX.isEmpty(requestMap.get("productId")) || StrUtilX.isEmpty(requestMap.get("saleDate"))
                || StrUtilX.isEmpty(requestMap.get("salePriceAdjustmentType")) || StrUtilX.isEmpty(requestMap.get("modifiedSalePrice"))
                || StrUtilX.isEmpty(requestMap.get("channelCode"))) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
        requestMap.put("updatedDt", DateUtilX.getCurrentDateStr(1));
        // 组装单日加幅
        // 将百分比正确化
        if (requestMap.get("salePriceAdjustmentType").equals("2") || requestMap.get("salePriceAdjustmentType").equals("3")) {
            String modifiedAmt = requestMap.get("modifiedSalePrice");
            double aDouble = Double.parseDouble(modifiedAmt);
            aDouble = aDouble / 100;
            String s = new DecimalFormat("0.0000").format(new BigDecimal(aDouble));
            requestMap.put("modifiedSalePrice", s);
        }
        ProductDayIncreasePO productDayIncreasePO = this.assemblyDayIncrease(requestMap);

        List<ProductDayIncreasePO> productDayIncreaseList = new ArrayList<ProductDayIncreasePO>(Collections.singletonList(productDayIncreasePO));
        List<String> incrementList = handlerProductIncrease(productDayIncreaseList);

        if (CollUtilX.isNotEmpty(productDayIncreaseList)) {
            //查询单日加幅
            Example example = new Example(ProductDayIncreasePO.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andEqualTo("productId", requestMap.get("productId"));
            criteria.andEqualTo("companyCode", requestMap.get("companyCode"));
            criteria.andEqualTo("saleDate", requestMap.get("saleDate"));
            List<ProductDayIncreasePO> productDayIncreasePOList = productDayIncreaseMapper.selectByExample(example);
            if (CollUtilX.isEmpty(productDayIncreasePOList)) {//新增单日加幅
                productDayIncreaseMapper.insert(productDayIncreasePO);
            } else {//修改单日加幅
                productDayIncreaseMapper.updateByExampleSelective(productDayIncreasePO, example);
            }
            //组装加幅总表数据
            ProductIncreasePO productIncreasePO = this.assemblySingleIncrease(requestMap);
            //插入加幅总表数据
            productIncreaseMapper.insert(productIncreasePO);

            ProductSaleLogPO productSaleLogPO = new ProductSaleLogPO();
            productSaleLogPO.setCreatedDt(requestMap.get("updatedDt"));
            productSaleLogPO.setCreatedBy(requestMap.get("updatedBy"));
            productSaleLogPO.setOperationType(ProductSaleOperationEnum.SALE.no);
            productSaleLogPO.setStartDate(requestMap.get("saleDate"));
            productSaleLogPO.setEndDate(requestMap.get("saleDate"));
//              productSaleLogPO.setContent(StrUtilX.isNotEmpty(requestMap.get(requestMap.get("modifiedSalePrice"))) ? "" : SaleAdjustmentTypeEnum.getDesc(Integer.valueOf(requestMap.get("salePriceAdjustmentType"))).replace("#", requestMap.get("modifiedSalePrice")));
            if (requestMap.get("salePriceAdjustmentType") != null && (Integer.parseInt(requestMap.get("salePriceAdjustmentType")) == 2 || Integer.parseInt(requestMap.get("salePriceAdjustmentType")) == 3)) {
                BigDecimal modifiedSalePrice = new BigDecimal(0);
                try {//防止转化时异常
                    if (StrUtilX.isNotEmpty(requestMap.get("modifiedSalePrice"))) {
                        modifiedSalePrice = new BigDecimal(requestMap.get("modifiedSalePrice"));
                    }
                } catch (Exception e) {
                    modifiedSalePrice = new BigDecimal(0);
                    log.error("dailyModifySalePrice--转换异常", e);
                }
                productSaleLogPO.setContent(StrUtilX.isNotEmpty(requestMap.get(requestMap.get("modifiedSalePrice"))) ?
                        "" : "售价" + Objects.requireNonNull(SaleAdjustmentTypeEnum.getDesc(Integer.parseInt(requestMap.get("salePriceAdjustmentType"))))
                        .replace("#", modifiedSalePrice.multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP) + "%"));
            } else {
                productSaleLogPO.setContent(StrUtilX.isNotEmpty(requestMap.get(requestMap.get("modifiedSalePrice"))) ?
                        "" : "售价" + Objects.requireNonNull(SaleAdjustmentTypeEnum.getDesc(Integer.parseInt(requestMap.get("salePriceAdjustmentType")))).replace("#", requestMap.get("modifiedSalePrice")));
            }
            productSaleLogPO.setCompanyCode(requestMap.get("companyCode"));
            productSaleLogPO.setChannelCode(requestMap.get("channelCode"));
            productSaleLogPO.setProductId(Integer.valueOf(requestMap.get("productId")));
            productSaleLogMapper.insert(productSaleLogPO);

            IncrementObjectDTO incrementObjectDTO = new IncrementObjectDTO();
            incrementObjectDTO.setObject(new ArrayList<>(Collections.singletonList(productDayIncreasePO)));
            incrementObjectDTO.setIncrementList(incrementList);
            initRemote.initSalePrice(incrementObjectDTO);

            // 注册事务同步器
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    // 提交队列处理
                    if (StrUtilX.isNotEmpty(requestMap.get("hotelId")) && StrUtilX.isNotEmpty(requestMap.get("supplierCode"))) {
                        List<HotelSaleInfoItemResp> itemRespList = Collections.singletonList(HotelSaleInfoItemResp.builder()
                                .hotelId(Long.valueOf(requestMap.get("hotelId")))
                                .supplierCode(requestMap.get("supplierCode"))
                                .startDate(requestMap.get("saleDate"))
                                .endDate(requestMap.get("saleDate"))
                                .build());

                        RedisTemplateX.convertAndSend(RedisKey.CALCULATE_SELF_HOTEL_LOWEST, JSONUtil.toJsonStr(HotelSaleInfoResp.builder()
                                .logId(UUID.randomUUID().toString())
                                .respList(itemRespList)
                                .build()));
                    }
                }
            });
        }
    }

    @Override
    @Transactional
    public void batchModifySalePrice(@RequestBody BatchSaleDTO batchSaleDTO) {
        if (StrUtilX.isEmpty(batchSaleDTO.getChannelCode()) || CollUtilX.isEmpty(batchSaleDTO.getItemList())) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
        // 需要计算起价的列表
        // key=hotelId_supplierCode
        Map<String, HotelSaleInfoItemResp> hotelSaleInfoRespMap = new HashMap<>();
        for (BatchSaleItemDTO batchSaleItemDTO : batchSaleDTO.getItemList()) {
            if (null == batchSaleItemDTO.getProductId() || StrUtilX.isEmpty(batchSaleItemDTO.getStartDate())
                    || StrUtilX.isEmpty(batchSaleItemDTO.getEndDate()) || DateUtilX.compare(DateUtilX.stringToDate(batchSaleItemDTO.getStartDate()), DateUtilX.stringToDate(batchSaleItemDTO.getEndDate())) > 1
                    || null == batchSaleItemDTO.getSalePriceAdjustmentType() || null == batchSaleItemDTO.getModifiedSalePrice()) {
                throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
            }
            // 组装需要计算起价数据
            if (batchSaleItemDTO.getHotelId() != null && StrUtilX.isNotEmpty(batchSaleItemDTO.getSupplierCode())) {
                String key = String.format("%s_%s", batchSaleItemDTO.getHotelId(), batchSaleItemDTO.getSupplierCode());
                HotelSaleInfoItemResp hotelSaleInfoResp = hotelSaleInfoRespMap.get(key);
                if (hotelSaleInfoResp == null) {
                    hotelSaleInfoRespMap.put(key, HotelSaleInfoItemResp.builder()
                            .hotelId(batchSaleItemDTO.getHotelId())
                            .supplierCode(batchSaleItemDTO.getSupplierCode())
                            .startDate(batchSaleItemDTO.getStartDate())
                            .endDate(batchSaleItemDTO.getEndDate())
                            .build());
                } else {
                    // 取时间更早的
                    if (DateUtilX.compare(DateUtilX.stringToDate(hotelSaleInfoResp.getStartDate()), DateUtilX.stringToDate(batchSaleItemDTO.getStartDate())) == 1) {
                        hotelSaleInfoResp.setStartDate(batchSaleItemDTO.getStartDate());
                    }
                    // 取时间更晚的
                    if (DateUtilX.compare(DateUtilX.stringToDate(hotelSaleInfoResp.getEndDate()), DateUtilX.stringToDate(batchSaleItemDTO.getEndDate())) == -1) {
                        hotelSaleInfoResp.setEndDate(batchSaleItemDTO.getEndDate());
                    }
                }
            }
        }

        List<ProductSaleLogPO> productSaleLogPOList = new ArrayList<>();
        for (int i = 0; i < batchSaleDTO.getItemList().size(); i++) {
            if (batchSaleDTO.getItemList().get(i).getSalePriceAdjustmentType().equals(2) || batchSaleDTO.getItemList().get(i).getSalePriceAdjustmentType().equals(3)) {
                batchSaleDTO.getItemList().get(i).setModifiedSalePrice(batchSaleDTO.getItemList().get(i).getModifiedSalePrice().divide(new BigDecimal(100), 2, RoundingMode.UP));
            }
        }

        // 组装加幅总表数据
        List<ProductIncreasePO> productIncreasePOList = this.assemblyIncrease(batchSaleDTO);
        // 组装加幅每日表数据
        List<ProductDayIncreasePO> productDayIncreasePOList = this.assemblyDayIncreaseList(batchSaleDTO);

        List<String> incrementList = handlerProductIncrease(productDayIncreasePOList);

        if (CollUtilX.isNotEmpty(productDayIncreasePOList)) {
            // 加幅总表数据入库
            if (CollUtilX.isNotEmpty(productIncreasePOList)) {
                productIncreaseMapper.insertList(productIncreasePOList);
                for (BatchSaleItemDTO batchSaleItemDTO : batchSaleDTO.getItemList()) {
                    List<Long> weekList = StrUtilX.stringToLongList(batchSaleItemDTO.getWeekList());
                    StringBuilder sbf = new StringBuilder();
                    for (Long week : weekList) {
                        if (week == 1) {
                            sbf.append("周一、");
                        }
                        if (week == 2) {
                            sbf.append("周二、");
                        }
                        if (week == 3) {
                            sbf.append("周三、");
                        }
                        if (week == 4) {
                            sbf.append("周四、");
                        }
                        if (week == 5) {
                            sbf.append("周五、");
                        }
                        if (week == 6) {
                            sbf.append("周六、");
                        }
                        if (week == 0) {
                            sbf.append("周日、");
                        }

                    }
                    sbf.deleteCharAt(sbf.length() - 1);
                    ProductSaleLogPO productSaleLogPO = new ProductSaleLogPO();
                    productSaleLogPO.setCreatedDt(batchSaleDTO.getUpdatedDt());
                    productSaleLogPO.setCreatedBy(batchSaleDTO.getUpdatedBy());
                    productSaleLogPO.setOperationType(ProductSaleOperationEnum.SALE.no);
                    productSaleLogPO.setStartDate(batchSaleItemDTO.getStartDate());
                    productSaleLogPO.setEndDate(batchSaleItemDTO.getEndDate());
                    if (batchSaleItemDTO.getSalePriceAdjustmentType() != null && (batchSaleItemDTO.getSalePriceAdjustmentType() == 2 || batchSaleItemDTO.getSalePriceAdjustmentType() == 3)) {
                        productSaleLogPO.setContent(batchSaleItemDTO.getModifiedSalePrice() == null ? "" : productSaleLogPO.getStartDate() + "-" + productSaleLogPO.getEndDate()
                                + "每" + sbf + "的售价" + Objects.requireNonNull(SaleAdjustmentTypeEnum.getDesc(batchSaleItemDTO.getSalePriceAdjustmentType()))
                                .replace("#", batchSaleItemDTO.getModifiedSalePrice().multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP) + "%"));
                    } else {
                        productSaleLogPO.setContent(batchSaleItemDTO.getModifiedSalePrice() == null ? "" : productSaleLogPO.getStartDate() + "-" + productSaleLogPO.getEndDate()
                                + "每" + sbf + "的售价" + Objects.requireNonNull(SaleAdjustmentTypeEnum.getDesc(batchSaleItemDTO.getSalePriceAdjustmentType())).replace("#", batchSaleItemDTO.getModifiedSalePrice().toString()));
                    }
                    productSaleLogPO.setCompanyCode(batchSaleDTO.getCompanyCode());
                    productSaleLogPO.setChannelCode(batchSaleDTO.getChannelCode());
                    productSaleLogPO.setProductId(batchSaleItemDTO.getProductId());
                    productSaleLogPOList.add(productSaleLogPO);
                }
                productSaleLogMapper.insertList(productSaleLogPOList);
            }

            //加幅每日表数据入库
            if (CollUtilX.isNotEmpty(productDayIncreasePOList)) {
                productDayIncreaseMapper.mergeProductDayIncrease(productDayIncreasePOList);
            }

            IncrementObjectDTO incrementObjectDTO = new IncrementObjectDTO();
            incrementObjectDTO.setObject(productDayIncreasePOList.stream().map(CommonConvert.INSTANCE::productSaleIncreaseDTOConvert).collect(Collectors.toList()));
            incrementObjectDTO.setIncrementList(incrementList);
            initRemote.initSalePrice(incrementObjectDTO);

            // 注册事务同步器
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    // 提交队列处理
                    RedisTemplateX.convertAndSend(RedisKey.CALCULATE_SELF_HOTEL_LOWEST, JSONUtil.toJsonStr(HotelSaleInfoResp.builder()
                            .logId(UUID.randomUUID().toString())
                            .respList(new ArrayList<>(hotelSaleInfoRespMap.values()))
                            .build()));
                }
            });
        }
    }

    @Override
    @Transactional
    public void batchModifySaleStatus(@RequestBody BatchSaleDTO batchSaleDTO) {
        //参数校验：渠道编码，售卖状态，每个对象的产品Id
        if (StrUtilX.isEmpty(batchSaleDTO.getChannelCode()) || null == batchSaleDTO.getSaleStatus()
                || CollUtilX.isEmpty(batchSaleDTO.getItemList())) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
        for (BatchSaleItemDTO batchSaleItemDTO : batchSaleDTO.getItemList()) {
            if (null == batchSaleItemDTO.getProductId()) {
                throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
            }
        }
        List<ProductSaleStatusPO> productSaleStatusPOList = new ArrayList<>();
        List<ProductSaleLogPO> productSaleLogPOList = new ArrayList<>();
        if (CollUtilX.isNotEmpty(batchSaleDTO.getItemList())) {
            for (BatchSaleItemDTO batchSaleItemDTO : batchSaleDTO.getItemList()) {
                ProductSaleStatusPO productSaleStatusPO = new ProductSaleStatusPO();
                productSaleStatusPO.setUpdatedDt(batchSaleDTO.getUpdatedDt());
                productSaleStatusPO.setUpdatedBy(batchSaleDTO.getUpdatedBy());
                productSaleStatusPO.setProductId(batchSaleItemDTO.getProductId());
                productSaleStatusPO.setCompanyCode(batchSaleDTO.getCompanyCode());
                if (ChannelEnum.getNoByKey(batchSaleDTO.getChannelCode()) == 0) {
                    productSaleStatusPO.setB2bSaleStatus(batchSaleDTO.getSaleStatus());
                }

                productSaleStatusPOList.add(productSaleStatusPO);
            }
        }

        if (CollUtilX.isNotEmpty(productSaleStatusPOList)) {
            if (!productSaleStatusPOList.isEmpty()) {
                productSaleStatusMapper.batchModifyProductSaleStatus(productSaleStatusPOList, batchSaleDTO.getSaleStatus(), batchSaleDTO.getChannelCode(), batchSaleDTO.getUpdatedBy(), batchSaleDTO.getCompanyCode());
                for (ProductSaleStatusPO productSaleStatusPO : productSaleStatusPOList) {
                    ProductSaleLogPO productSaleLogPO = new ProductSaleLogPO();
                    productSaleLogPO.setChannelCode(batchSaleDTO.getChannelCode());
                    productSaleLogPO.setCompanyCode(batchSaleDTO.getCompanyCode());
                    productSaleLogPO.setContent("销售状态改为\"" + SaleStatusEnum.getDesc(batchSaleDTO.getSaleStatus()) + "\"");
                    productSaleLogPO.setOperationType(ProductSaleOperationEnum.MODIFY_SALE.no);
                    productSaleLogPO.setCreatedBy(batchSaleDTO.getUpdatedBy());
                    productSaleLogPO.setCreatedDt(batchSaleDTO.getUpdatedDt());
                    productSaleLogPO.setProductId(productSaleStatusPO.getProductId());
                    productSaleLogPOList.add(productSaleLogPO);
                }
                productSaleLogMapper.insertList(productSaleLogPOList);
            }

            List<ProductSaleStatusDTO> productSaleStatusList = productSaleStatusPOList.stream().map(CommonConvert.INSTANCE::productSaleStatusDTOConvert).collect(Collectors.toList());
            IncrementObjectDTO incrementObjectDTO = new IncrementObjectDTO();
            incrementObjectDTO.setObject(productSaleStatusList);
            List<String> incrementList = handlerProductSaleStatus(productSaleStatusPOList);
            incrementObjectDTO.setIncrementList(incrementList);
            initRemote.initSaleStatus(incrementObjectDTO);
        }
    }


    /**
     * 组装酒店售卖产品数据
     */
    private List<HotelProductsDTO> assemblySaleHotelProduct(QueryProductRequestDTO queryProductRequestDTO, List<ProductTempDTO> productTempDTOList,
                                                            List<Long> saleHotelIds, String channelCode) {
        Map<Long, HotelProductsDTO> hotelMap = new HashMap<>();
        //酒店房型
        Map<Long, Map<Integer, ProductRoomDTO>> roomMap = new HashMap<>();
        //房型产品
        Map<Integer, Map<Integer, ProductForShowDTO>> productForShowDTOMap = new HashMap<>();
        //产品渠道推送状态
        Map<Integer, Integer> productMappingMap = new HashMap<>();
        productTempDTOList.forEach(productTempDTO -> {
            if (null == roomMap.get(productTempDTO.getHotelId())) {
                //房型产品
                ProductForShowDTO productForShowDTO = this.assemblyProductForShow(productTempDTO, channelCode, productMappingMap, queryProductRequestDTO.getLanguage());
                Map<Integer, ProductForShowDTO> pMap = new HashMap<>();
                pMap.put(productTempDTO.getProductId(), productForShowDTO);
                productForShowDTOMap.put(productTempDTO.getRoomId(), pMap);

                //酒店房型
                ProductRoomDTO productRoomDTO = new ProductRoomDTO();
                productRoomDTO.setRoomId(productTempDTO.getRoomId());
                productRoomDTO.setRoomName(productTempDTO.getRoomName());
                productRoomDTO.setMappingFailed(productTempDTO.getRoomMappingStatus() != null && productTempDTO.getRoomMappingStatus().equals(MappingStatusEnum.MAPPING_FAIL.no) ? 1 : 0);
                Map<Integer, ProductRoomDTO> mMap = new HashMap<>();
                mMap.put(productTempDTO.getRoomId(), productRoomDTO);
                roomMap.put(productTempDTO.getHotelId(), mMap);

                //酒店
                HotelProductsDTO hotelProductsDTO = new HotelProductsDTO();
                hotelProductsDTO.setHotelId(productTempDTO.getHotelId());
                hotelProductsDTO.setHotelName(productTempDTO.getHotelName());
                hotelProductsDTO.setMappingFailed(productTempDTO.getHotelMappingStatus() != null && productTempDTO.getHotelMappingStatus().equals(MappingStatusEnum.MAPPING_FAIL.no) ? 1 : 0);
                hotelMap.put(productTempDTO.getHotelId(), hotelProductsDTO);
            } else if (null == roomMap.get(productTempDTO.getHotelId()).get(productTempDTO.getRoomId())) {
                //房型产品
                ProductForShowDTO productForShowDTO = this.assemblyProductForShow(productTempDTO, channelCode, productMappingMap, queryProductRequestDTO.getLanguage());
                Map<Integer, ProductForShowDTO> pMap = new HashMap<>();
                pMap.put(productTempDTO.getProductId(), productForShowDTO);
                productForShowDTOMap.put(productTempDTO.getRoomId(), pMap);

                //酒店房型
                ProductRoomDTO productRoomDTO = new ProductRoomDTO();
                productRoomDTO.setRoomId(productTempDTO.getRoomId());
                productRoomDTO.setRoomName(productTempDTO.getRoomName());
                productRoomDTO.setMappingFailed(productTempDTO.getRoomMappingStatus() != null && productTempDTO.getRoomMappingStatus().equals(MappingStatusEnum.MAPPING_FAIL.no) ? 1 : 0);
                roomMap.get(productTempDTO.getHotelId()).put(productTempDTO.getRoomId(), productRoomDTO);
            } else {
                //房型产品
                ProductForShowDTO productForShowDTO = this.assemblyProductForShow(productTempDTO, channelCode, productMappingMap, queryProductRequestDTO.getLanguage());
                productForShowDTOMap.get(productTempDTO.getRoomId()).put(productTempDTO.getProductId(), productForShowDTO);
            }
        });

        // 查询酒店
        HotelInfoCollectionReq hotelInfoReq = new HotelInfoCollectionReq();
        hotelInfoReq.setHotelIds(saleHotelIds);
        hotelInfoReq.setLanguageType(queryProductRequestDTO.getLanguage());
        List<String> settings = new ArrayList<>();
        settings.add(BaseHotelInfoUrl.hotelName);
        settings.add(BaseHotelInfoUrl.rooms);
        hotelInfoReq.setSettings(settings);
        Response<List<HotelInfoCollectionDTO>> response = hotelRemote.queryHotelInfoList(hotelInfoReq);
        Map<Long, String> hotelNameMap = new HashMap<>();
        Map<Long, String> roomNameMap = new HashMap<>();
        if (response.isSuccess() && CollUtilX.isNotEmpty(response.getModel())) {
            response.getModel().forEach(item -> {
                hotelNameMap.put(item.getHotelId(), item.getHotelName());
                if (CollUtilX.isNotEmpty(item.getRooms())) {
                    item.getRooms().forEach(room -> {
                        roomNameMap.put(room.getRoomtypeId(), room.getRoomtypeName());
                    });
                }
            });
        }

        //封装数据
        List<HotelProductsDTO> hotelProductsDTOList = new ArrayList<>();
        for (Long hotelId : saleHotelIds) {
            HotelProductsDTO hotelProductsDTO = hotelMap.get(hotelId);
            hotelProductsDTO.setHotelName(hotelNameMap.get(hotelId));
            List<ProductRoomDTO> productRoomDTOList = new ArrayList<>();
            if (null != roomMap.get(hotelId)) {
                for (Integer roomId : roomMap.get(hotelId).keySet()) {
                    ProductRoomDTO productRoomDTO = roomMap.get(hotelId).get(roomId);
                    productRoomDTO.setRoomName(roomNameMap.get(roomId.longValue()));
                    List<ProductForShowDTO> productForShowDTOList = new ArrayList<>();
                    for (Integer productId : productForShowDTOMap.get(roomId).keySet()) {
                        productForShowDTOList.add(productForShowDTOMap.get(roomId).get(productId));
                    }
                    productRoomDTO.setProductList(productForShowDTOList);
                    productRoomDTOList.add(productRoomDTO);
                }
                hotelProductsDTO.setRoomList(productRoomDTOList);
                hotelProductsDTOList.add(hotelProductsDTO);
            }
        }

        return hotelProductsDTOList;
    }

    /**
     * 组装产品
     */
    private ProductForShowDTO assemblyProductForShow(ProductTempDTO productTempDTO, String channelCode, Map<Integer, Integer> productMappingMap, String language) {
        ProductForShowDTO productForShowDTO = CommonConvert.INSTANCE.productForShowDTOConvert(productTempDTO);
        String bedTypes = productTempDTO.getBedTypes();

        if (StrUtilX.isNotEmpty(bedTypes)) {
            try {
                Map<String, String> bedMap = bedNameConfig.getBedNames(language);
                com.alibaba.fastjson.JSONArray array = JSON.parseArray(bedTypes);
                for (Object o : array) {
                    JSONObject json = (JSONObject) o;
                    JSONArray bedInfos = json.getJSONArray("bedInfos");
                    for (Object bedInfo : bedInfos) {
                        JSONObject jsonObject = (JSONObject) bedInfo;
                        String bedType = jsonObject.getString("bedCode");
                        jsonObject.put("bedName", bedMap.get(bedType));
                    }
                }
                productForShowDTO.setBedTypes(JSON.toJSONString(array));
            } catch (Exception e) {
                log.error("组装产品异常", e);
            }
        }
        productForShowDTO.setPayMethod(productTempDTO.getPayMethod());

        if (null != productMappingMap && null != productMappingMap.get(productForShowDTO.getProductId())) {
            productForShowDTO.setChannelSaleStatus(1);//OTA渠道销售中
        } else {
            productForShowDTO.setChannelSaleStatus(0);//OTA渠道未销售
        }
        if (ChannelEnum.getNoByKey(channelCode) == 0) {
            productForShowDTO.setSaleStatus(productTempDTO.getB2bSaleStatus());
        } else {
            productForShowDTO.setSaleStatus(0);
        }
        return productForShowDTO;
    }

    /**
     * 获取税后价格
     */
    private static void getAllFaxPrice(ProductDTO productDTO, RoomTaxDetail roomTaxDetail, Boolean isFirstDay, ProductSalePriceItemDTO productSalePriceItemDTO, BigDecimal roomPrice, List<ProTaxDetailBase> proTaxDetails, BigDecimal orgRate) {
        if (CollUtilX.isNotEmpty(proTaxDetails)) {

            // 存在税费
            BigDecimal basePrice = BigDecimal.ZERO;
            BigDecimal salesTax = BigDecimal.ZERO;
            BigDecimal tax = BigDecimal.ZERO;
            BigDecimal otherTaxFee = BigDecimal.ZERO;

            for (ProTaxDetailBase proTaxDetail : proTaxDetails) {
                switch (proTaxDetail.getTaxType()) {
                    case 0:
                        //销售税
                        if (Objects.equals(proTaxDetail.getTaxStandard(), TaxStandardsEnum.NIGHT.getCode())) {
                            salesTax = salesTax.add(getTax(roomPrice, proTaxDetail));
                        } else if (isFirstDay && Objects.equals(proTaxDetail.getTaxStandard(), TaxStandardsEnum.MIDNIGHT.getCode())) {
                            BigDecimal oldSaleTax = roomTaxDetail.getSalesTax();
                            if (oldSaleTax == null) {
                                oldSaleTax = BigDecimal.ZERO;
                            }
                            oldSaleTax = oldSaleTax.add(getTax(roomPrice, proTaxDetail));
                            roomTaxDetail.setSalesTax(oldSaleTax);
                            roomTaxDetail.setSupplySalesTax(oldSaleTax);
                        }
                        break;
                    case 1:
                        //销售税
                        if (Objects.equals(proTaxDetail.getTaxStandard(), TaxStandardsEnum.NIGHT.getCode())) {
                            tax = tax.add(getTax(roomPrice, proTaxDetail));
                        } else if (isFirstDay && Objects.equals(proTaxDetail.getTaxStandard(), TaxStandardsEnum.MIDNIGHT.getCode())) {
                            BigDecimal oldTax = roomTaxDetail.getTaxFee();
                            if (oldTax == null) {
                                oldTax = BigDecimal.ZERO;
                            }
                            oldTax = oldTax.add(getTax(roomPrice, proTaxDetail));
                            roomTaxDetail.setTaxFee(oldTax);
                            roomTaxDetail.setSupplyTaxFee(oldTax);
                        }
                        break;
                    case 2:
                        // 到店付
                        BigDecimal oldPayInStorePrice = roomTaxDetail.getPayInStorePrice();
                        if (oldPayInStorePrice == null) {
                            oldPayInStorePrice = BigDecimal.ZERO;
                        }
                        oldPayInStorePrice = oldPayInStorePrice.add(getTax(roomPrice, proTaxDetail));
                        roomTaxDetail.setPayInStorePrice(oldPayInStorePrice);
                        roomTaxDetail.setPayInStoreCurrency(productDTO.getCurrency());
                        break;
                    case 3:
                        if (Objects.equals(proTaxDetail.getTaxStandard(), TaxStandardsEnum.NIGHT.getCode())) {
                            otherTaxFee = otherTaxFee.add(getTax(roomPrice, proTaxDetail));
                        } else if (isFirstDay && Objects.equals(proTaxDetail.getTaxStandard(), TaxStandardsEnum.MIDNIGHT.getCode())) {
                            BigDecimal oldOtherTax = roomTaxDetail.getOtherTax();
                            if (oldOtherTax == null) {
                                oldOtherTax = BigDecimal.ZERO;
                            }
                            oldOtherTax = oldOtherTax.add(getTax(roomPrice, proTaxDetail));
                            roomTaxDetail.setOtherTax(oldOtherTax);
                            roomTaxDetail.setSupplyOtherTax(oldOtherTax);
                        }
                        break;
                }
            }
            TaxDetailDto taxDetailDto = new TaxDetailDto();
            taxDetailDto.setSupplyRoomPrice(roomPrice);

            if (salesTax.compareTo(BigDecimal.ZERO) != 0) {
                taxDetailDto.setSupplySalesTax(salesTax);
            }
            if (tax.compareTo(BigDecimal.ZERO) != 0) {
                taxDetailDto.setSupplyTaxFee(tax);
            }
            if (otherTaxFee.compareTo(BigDecimal.ZERO) != 0) {
                taxDetailDto.setSupplyOtherTax(otherTaxFee);
            }

            roomPrice = roomPrice.multiply(orgRate);
            salesTax = salesTax.multiply(orgRate);
            tax = tax.multiply(orgRate);
            otherTaxFee = otherTaxFee.multiply(orgRate);

            // 每次税费处理 如果是首日并且存在每次税费 则需要在底价中加入次税费
            if (isFirstDay && roomTaxDetail != null) {
                // 修改后的价格
                if (roomTaxDetail.getSalesTax() != null) {
                    roomTaxDetail.setSalesTax(roomTaxDetail.getSalesTax().multiply(orgRate));
                }
                if (roomTaxDetail.getTaxFee() != null) {
                    roomTaxDetail.setTaxFee(roomTaxDetail.getTaxFee().multiply(orgRate));
                }
                if (roomTaxDetail.getOtherTax() != null) {
                    roomTaxDetail.setOtherTax(roomTaxDetail.getOtherTax().multiply(orgRate));
                }
            }


            taxDetailDto.setRoomPrice(roomPrice);
            if (salesTax.compareTo(BigDecimal.ZERO) != 0) {
                taxDetailDto.setSalesTax(salesTax);
            }
            if (tax.compareTo(BigDecimal.ZERO) != 0) {
                taxDetailDto.setTaxFee(tax);
            }
            if (otherTaxFee.compareTo(BigDecimal.ZERO) != 0) {
                taxDetailDto.setOtherTax(otherTaxFee);
            }
            productSalePriceItemDTO.setTaxDetail(taxDetailDto);
            // 修改后的价格  底价=房费+所有税费 不含到点另付费用
            basePrice = roomPrice;
            productSalePriceItemDTO.setBasePrice(basePrice);
        }
    }

    /**
     * 获取税费
     */
    private static BigDecimal getTax(BigDecimal roomPrice, ProTaxDetailBase proTaxDetail) {
        if (proTaxDetail.getTaxIncreaseType() != null && proTaxDetail.getTaxIncreaseValue() != null) {
            if (proTaxDetail.getTaxIncreaseType().equals(TaxIncreaseEnum.FIXED.getCode())) {
                // 固定
                return proTaxDetail.getTaxIncreaseValue();
            } else {
                // 百分比
                return roomPrice.multiply(proTaxDetail.getTaxIncreaseValue().divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
            }
        }
        return BigDecimal.ZERO;
    }


    /**
     * 组装产品售卖信息
     */
    private void assemblyProductSalePrice(ProTaxRuleConfigResp proTaxRuleConfigResp,
                                          Map<Date, Date> dateMap,
                                          ProductSalePriceDetailDTO productSalePriceDTO,
                                          List<ProductPricePO> pricePOList,
                                          List<ProductQuotaPO> productQuotaPOList,
                                          List<ProductDayIncreasePO> productDayIncreasePOList,
                                          ProductDTO productDTO,
                                          QueryProductRequestDTO request) {
        Map<Date, ProductSalePriceItemDTO> productSaleItemDTOMap = new TreeMap<>(new Comparator<Date>() {
            public int compare(Date obj1, Date obj2) {
                // 升序排序
                return obj1.compareTo(obj2);
            }
        });

        // 1. 获取商家币种
        OrgDTO orgDTO = CommonInitializer.getOrgInfo();

        // 客户 可能不存在 这时候 需要返回商家币种 那么汇率默认就是1
        BigDecimal agentRate = new BigDecimal("1");
        AgentAccountConfig agentAccountConfig = null;
        if (StrUtil.isNotBlank(request.getAgentCode())) {
            agentAccountConfig = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.AGENT_ACCOUNT_CONFIG, request.getAgentCode()), AgentAccountConfig.class);
            if (agentAccountConfig != null) {
                //查询商家币种对应客户币种汇率
                BigDecimal tempRate = RateUtil.getRate(Integer.valueOf(orgDTO.getOrgCurrency()), agentAccountConfig.getSettlementCurrency());
                if (tempRate != null) {
                    agentRate = tempRate;
                }
            }
        }

        for (Date date : dateMap.keySet()) {
            ProductSalePriceItemDTO productSalePriceItemDTO = new ProductSalePriceItemDTO();
            productSalePriceItemDTO.setSaleDate(DateUtilX.dateToString(date));
            productSalePriceItemDTO.setBaseCurrency(productDTO.getCurrency());
            if (agentAccountConfig != null) {
                productSalePriceItemDTO.setSettlementCurrency(agentAccountConfig.getSettlementCurrency());
            }
            productSalePriceItemDTO.setBreakfastNum(productSalePriceDTO.getBreakfastQty());
            productSaleItemDTOMap.put(date, productSalePriceItemDTO);
        }


        //查询币种对应的汇率
        BigDecimal orgRate = RateUtil.getToMerchantRate(productDTO.getCurrency());

        // 如果产品币种=商家币种那么 汇率等于1
        if (productDTO.getCurrency().equals(Integer.valueOf(orgDTO.getOrgCurrency()))) {
            orgRate = new BigDecimal("1");
        }
        if (orgRate == null) {
            return;
        }


        RoomTaxDetail roomTaxDetail = new RoomTaxDetail();
        //组装底价 因为要返回商家币种，所以这里要*商家汇率
        if (CollUtilX.isNotEmpty(pricePOList)) {
            boolean flag = true;
            for (ProductPricePO productPricePO : pricePOList) {
                if (proTaxRuleConfigResp != null && CollUtilX.isNotEmpty(proTaxRuleConfigResp.getProTaxDetails())) {
                    // 是否首日  如果存在此税费 则需要计算在今天
                    boolean isFirstDay = DateUtilX.dateToString(productPricePO.getSaleDate()).equals(request.getStartDate());
                    // 如果没有客户信息 那么需要需要将此税增加到每日的底价 并且是首日 那么需要计算首日税费
                    if (agentAccountConfig == null && flag) {
                        isFirstDay = true;
                        flag = false;
                    }
                    // basePrice = (底*汇率)+(税*汇率)
                    ProductSalePriceItemDTO productSalePriceItemDTO = productSaleItemDTOMap.get(productPricePO.getSaleDate());
                    getAllFaxPrice(productDTO, roomTaxDetail, isFirstDay, productSalePriceItemDTO, productPricePO.getBasePrice(), proTaxRuleConfigResp.getProTaxDetails(), orgRate);
                } else {
                    ProductSalePriceItemDTO productSalePriceItemDTO = productSaleItemDTOMap.get(productPricePO.getSaleDate());
                    productSalePriceItemDTO.setBasePrice(productPricePO.getBasePrice().multiply(orgRate));
                    TaxDetailDto taxDetailDto = new TaxDetailDto();
                    taxDetailDto.setSupplyRoomPrice(productPricePO.getBasePrice());
                    taxDetailDto.setSupplySalesTax(new BigDecimal("0"));
                    taxDetailDto.setSupplyOtherTax(new BigDecimal("0"));
                    taxDetailDto.setSupplyTaxFee(new BigDecimal("0"));
                    productSalePriceItemDTO.setTaxDetail(taxDetailDto);
                }
                ProductSalePriceItemDTO productSalePriceItemDTO = productSaleItemDTOMap.get(productPricePO.getSaleDate());
                productSalePriceItemDTO.setEquivalentBasePrice(productSalePriceItemDTO.getBasePrice());
            }
        }

        //组装配额和房态
        if (CollUtilX.isNotEmpty(productQuotaPOList)) {
            for (ProductQuotaPO productQuotaPO : productQuotaPOList) {
                productSaleItemDTOMap.get(DateUtilX.stringToDate(productQuotaPO.getSaleDate())).setQuota(productQuotaPO.getQuota());
                productSaleItemDTOMap.get(DateUtilX.stringToDate(productQuotaPO.getSaleDate())).setQuotaNum(productQuotaPO.getRemainingQuota());
                productSaleItemDTOMap.get(DateUtilX.stringToDate(productQuotaPO.getSaleDate())).setRemainingQuota(productQuotaPO.getRemainingQuota());
                productSaleItemDTOMap.get(DateUtilX.stringToDate(productQuotaPO.getSaleDate())).setOverDraftStatus(productQuotaPO.getOverDraftStatus());
                productSaleItemDTOMap.get(DateUtilX.stringToDate(productQuotaPO.getSaleDate())).setRoomStatus(productQuotaPO.getRoomStatus());
            }
        }

        //组装售价和利润
        if (CollUtilX.isNotEmpty(productDayIncreasePOList)) {
            for (ProductDayIncreasePO productDayIncreasePO : productDayIncreasePOList) {

                boolean isFirstDay = DateUtilX.dateToString(DateUtilX.stringToDate(productDayIncreasePO.getSaleDate())).equals(request.getStartDate());
                // 如果没有客户信息 那么需要需要将此税增加到每日的底价
                if (agentAccountConfig == null) {
                    isFirstDay = true;
                }
                //有底价才进行价格计算
                if (null != productSaleItemDTOMap.get(DateUtilX.stringToDate(productDayIncreasePO.getSaleDate())).getBasePrice()) {

                    ProductSalePriceItemDTO tempPrice = productSaleItemDTOMap.get(DateUtilX.stringToDate(productDayIncreasePO.getSaleDate()));
                    //底价 此时是净房费 不含税
                    BigDecimal roomPrice = tempPrice.getBasePrice();

                    // 如果小于0 那么直接返回0
                    if (roomPrice.compareTo(BigDecimal.ZERO) == 0) {
                        continue;
                    }
                    Integer adjustmentType = null;//调整方式（0加数值 1减数值 2加百分比 3减百分比 4等于）
                    BigDecimal modifiedAmt = null;//调整金额
                    BigDecimal miniAddRadiation = null; // 售价最小加幅
                    BigDecimal discount = BigDecimal.ZERO; //订单的折扣,默认为0
                    BigDecimal supplyDiscount = BigDecimal.ZERO; // 供货单的折扣，但未使用 默认为0
                    switch (ChannelEnum.getNoByKey(request.getChannelCode())) {
                        case 0:
                        case 20:
                            adjustmentType = productDayIncreasePO.getB2bAdjustmentType();
                            modifiedAmt = productDayIncreasePO.getB2bModifiedAmt();
                            miniAddRadiation = productDayIncreasePO.getB2bMiniAddRadiation();
                            break;
                        default:
                    }
                    ProductSalePriceItemDTO productSalePriceItemDTO = productSaleItemDTOMap.get(DateUtilX.stringToDate(productDayIncreasePO.getSaleDate()));
                    //没有加幅方式或者没有加幅金额，则没有售价和利润
                    if (agentAccountConfig != null) {
                        //加幅值 商家币种
                        BigDecimal adjustment = new BigDecimal("0");
                        // 只有现付产品才有加幅度 预付没有加幅
                        if (null != adjustmentType && null != modifiedAmt && productDTO.getPayMethod().equals(PayMethodEnum.PREPAY.key)) {
                            switch (adjustmentType) {
                                case 0:
                                    adjustment = modifiedAmt;
                                    break;
                                case 1:
//                                    roomPrice = roomPrice.subtract(modifiedAmt);
                                    discount = modifiedAmt.negate();
                                    break;
                                case 2:
                                    // 这里需要再次进行比较
                                    BigDecimal addRadiation = roomPrice.multiply(modifiedAmt);
                                    // 存在最小加辐值 那么则需要获取最小加辐值
                                    if (miniAddRadiation != null) {
                                        // 产品加辐值小于 最小加辐值, 则使用商家币种的产品加辐值
                                        if (addRadiation.compareTo(miniAddRadiation) < 0) {
                                            adjustment = miniAddRadiation;
                                        } else {
                                            // 百分比售价加辐+底价
                                            adjustment = addRadiation;
                                        }
                                    } else {
                                        adjustment = addRadiation;
                                    }
                                    break;
                                case 3:
//                                    roomPrice = roomPrice.subtract(modifiedAmt);
                                    discount = roomPrice.multiply(modifiedAmt).negate();
                                    break;
                                case 4:
                                    // 加辐
                                    // 修改金额大于房费则 加幅=修改金额-房费
                                    // 否则 房费=修改金额 加幅=0
                                    if (modifiedAmt.compareTo(roomPrice) > 0) {
                                        adjustment = modifiedAmt.subtract(roomPrice);
                                    } else {
                                        roomPrice = modifiedAmt; // 售价=多少
                                    }
                                    break;
                                default:
                            }
                        }

                        //newScale 进位数
                        int newScale = 2;
                        //进位类型 1向上取整 2向下取整 3四舍五入
                        int roundingType = BigDecimal.ROUND_UP;

                        // 存在客户 那么需要设置 客户的进位数字 以及进位类型
                        newScale = agentAccountConfig.getDecimalPlaces();
                        roundingType = agentAccountConfig.getRoundingType();

                        // 加幅值 客户币种
                        adjustment = CommonTgUtils.setScale(adjustment.multiply(agentRate), newScale, roundingType);
                        TaxDetailDto taxDetail = new TaxDetailDto();
                        if (productSalePriceItemDTO.getTaxDetail() != null) {
                            taxDetail = CommonConvert.INSTANCE.taxDetailConvert(productSalePriceItemDTO.getTaxDetail());
                            // 设置折扣的值
                            taxDetail.setDiscount(discount);
                            taxDetail.setSupplyDiscount(supplyDiscount);
                            // 税费 客户币种
                            if (taxDetail.getSalesTax() != null) {
                                taxDetail.setSalesTax(CommonTgUtils.setScale(taxDetail.getSalesTax().multiply(agentRate), newScale, roundingType));
                            }
                            if (taxDetail.getTaxFee() != null) {
                                taxDetail.setTaxFee(CommonTgUtils.setScale(taxDetail.getTaxFee().multiply(agentRate), newScale, roundingType));
                            }
                            if (taxDetail.getOtherTax() != null) {
                                taxDetail.setOtherTax(CommonTgUtils.setScale((taxDetail.getOtherTax().multiply(agentRate)), newScale, roundingType));
                            }
                            if (taxDetail.getDiscount() != null) {
                                taxDetail.setDiscount(CommonTgUtils.setScale((taxDetail.getDiscount().multiply(agentRate)), newScale, roundingType));
                            }
                        }


                        taxDetail.setRoomPrice(CommonTgUtils.setScale((roomPrice.multiply(agentRate)), newScale, roundingType));


                        // 首日税费
                        BigDecimal fastTax = new BigDecimal("0");
                        if (isFirstDay) {
                            if (roomTaxDetail.getSalesTax() != null) {
                                roomTaxDetail.setSalesTax(CommonTgUtils.setScale(roomTaxDetail.getSalesTax().multiply(agentRate), newScale, roundingType));
                                fastTax = fastTax.add(roomTaxDetail.getSalesTax());
                            }
                            if (roomTaxDetail.getOtherTax() != null) {
                                roomTaxDetail.setOtherTax(CommonTgUtils.setScale(roomTaxDetail.getOtherTax().multiply(agentRate), newScale, roundingType));
                                fastTax = fastTax.add(roomTaxDetail.getOtherTax());
                            }
                            if (roomTaxDetail.getTaxFee() != null) {
                                roomTaxDetail.setTaxFee(CommonTgUtils.setScale(roomTaxDetail.getTaxFee().multiply(agentRate), newScale, roundingType));
                                fastTax = fastTax.add(roomTaxDetail.getTaxFee());
                            }
                        }

                        // 折扣金额 默认为0 如果有加幅金额折扣应该为0
                        roomTaxDetail.setDiscount(taxDetail.getDiscount());

                        // 加幅金额 需要加到 其他税费里面
                        taxDetail.setOtherTax(addPrice(taxDetail.getOtherTax(), adjustment));

                        productSalePriceItemDTO.setTaxDetail(taxDetail);
                        // 售价 含加幅
                        // 售价=房费+税费+加幅+首日税费+折扣
                        BigDecimal salePrice = addPrice(taxDetail.getRoomPrice(), taxDetail.getSalesTax(), taxDetail.getTaxFee(), taxDetail.getOtherTax(), fastTax, taxDetail.getDiscount());

                        productSalePriceItemDTO.setSalePrice(salePrice);
                        // 利润=售价-底价
                        productSalePriceItemDTO.setProfit(productSalePriceItemDTO.getSalePrice().subtract(productSalePriceItemDTO.getBasePrice()));


                        productSalePriceItemDTO.setTaxDetail(taxDetail);
//                        // 房费小于售价是 那么底价= 原始房费+加幅
//                        if (oldRoomPrice.compareTo(roomPrice) > 0) {
//                            oldRoomPrice = CommonTgUtils.setScale(oldRoomPrice, newScale, roundingType);
//                            oldRoomPrice = addPrice(oldRoomPrice, taxDetail.getSalesTax(), taxDetail.getTaxFee(), taxDetail.getOtherTax(), fastTax);
//                            productSalePriceItemDTO.setBasePrice(oldRoomPrice);
//                        }

                        // 产品币种
                        TaxDetailDto tempPriceTaxDetail = tempPrice.getTaxDetail();
                        // 底价= 供应底价+税费
                        BigDecimal basePrice = addPrice(tempPriceTaxDetail.getSupplyRoomPrice(), tempPriceTaxDetail.getSupplyOtherTax(), tempPriceTaxDetail.getSupplySalesTax(), tempPriceTaxDetail.getSupplyTaxFee());
                        // 首日 需要加 此税费
                        if (isFirstDay) {
                            basePrice = addPrice(basePrice, roomTaxDetail.getSupplySalesTax(), roomTaxDetail.getSupplyTaxFee(), roomTaxDetail.getSupplyOtherTax());
                        }

                        productSalePriceItemDTO.setBasePrice(basePrice);

                    } else {
                        //没有加幅方式或者没有加幅金额，则没有售价和利润
                        BigDecimal salePrice = BigDecimal.ZERO;
                        if (productDTO.getPayMethod() != null && productDTO.getPayMethod().equals(PayMethodEnum.PAY.key)) {
                            salePrice = roomPrice;
                        } else if (null != adjustmentType && null != modifiedAmt) {
                            switch (adjustmentType) {
                                case 0:
                                    salePrice = roomPrice.add(modifiedAmt);
                                    break;
                                case 1:
                                    salePrice = roomPrice.subtract(modifiedAmt);
                                    break;
                                case 2:
                                    // 这里需要再次进行比较
                                    BigDecimal addRadiation = roomPrice.multiply(modifiedAmt);
                                    // 存在最小加辐值 那么则需要获取最小加辐值
                                    if (miniAddRadiation != null) {
                                        // 产品加辐值小于 最小加辐值, 则使用商家币种的产品加辐值
                                        if (addRadiation.compareTo(miniAddRadiation) < 0) {
                                            salePrice = roomPrice.add(miniAddRadiation);
                                        } else {
                                            // 百分比售价加辐+底价
                                            salePrice = roomPrice.add(addRadiation);
                                        }
                                    } else {
                                        salePrice = roomPrice.add(roomPrice.multiply(modifiedAmt));
                                    }
                                    break;
                                case 3:
                                    // 加辐
                                    BigDecimal add = roomPrice.multiply(modifiedAmt);
                                    salePrice = roomPrice.subtract(add);
                                    break;
                                case 4:
                                    salePrice = modifiedAmt;
                                    break;
                                default:
                            }
                        }

                        // 如果存在税费则需要+税费 不含税价
                        if (tempPrice.getTaxDetail() != null) {
                            TaxDetailDto taxDetail = tempPrice.getTaxDetail();
                            if (taxDetail.getSalesTax() != null) {
                                roomPrice = roomPrice.add(taxDetail.getSalesTax());
                                salePrice = salePrice.add(taxDetail.getSalesTax());
                            }
                            if (taxDetail.getTaxFee() != null) {
                                roomPrice = roomPrice.add(taxDetail.getTaxFee());
                                salePrice = salePrice.add(taxDetail.getTaxFee());
                            }
                            if (taxDetail.getOtherTax() != null) {
                                roomPrice = roomPrice.add(taxDetail.getOtherTax());
                                salePrice = salePrice.add(taxDetail.getOtherTax());
                            }
                        }
                        // 次税 必须加到首日 商家币种金额
                        if (roomTaxDetail.getSalesTax() != null) {
                            roomPrice = roomPrice.add(roomTaxDetail.getSalesTax());
                            salePrice = salePrice.add(roomTaxDetail.getSalesTax());
                        }
                        if (roomTaxDetail.getOtherTax() != null) {
                            roomPrice = roomPrice.add(roomTaxDetail.getOtherTax());
                            salePrice = salePrice.add(roomTaxDetail.getOtherTax());
                        }
                        if (roomTaxDetail.getTaxFee() != null) {
                            roomPrice = roomPrice.add(roomTaxDetail.getTaxFee());
                            salePrice = salePrice.add(roomTaxDetail.getTaxFee());
                        }


                        // 如果小于0 那么直接返回0
                        if (salePrice.compareTo(BigDecimal.ZERO) < 0) {
                            salePrice = BigDecimal.ZERO;
                        }
                        roomPrice = roomPrice.multiply(agentRate);
                        salePrice = salePrice.multiply(agentRate);

                        productSalePriceItemDTO.setSalePrice(salePrice.setScale(2, RoundingMode.HALF_UP));
                        productSalePriceItemDTO.setBasePrice(roomPrice.setScale(2, RoundingMode.HALF_UP));
                        // 利润=销售价-底价
                        BigDecimal profit = productSalePriceItemDTO.getSalePrice().subtract(productSalePriceItemDTO.getBasePrice());
                        productSalePriceItemDTO.setProfit(profit.setScale(2, RoundingMode.HALF_UP));

                    }
                }
            }
        }
        List<ProductSalePriceItemDTO> productSaleItemDTOList = new ArrayList<>();
        for (Date date : productSaleItemDTOMap.keySet()) {
            ProductSalePriceItemDTO productSalePriceItemDTO = productSaleItemDTOMap.get(date);
            productSaleItemDTOList.add(productSalePriceItemDTO);
        }
        productSalePriceDTO.setPriceList(productSaleItemDTOList);
        productSalePriceDTO.setRoomTaxDetail(roomTaxDetail);
    }

    /**
     * 相加
     */
    public static BigDecimal addPrice(BigDecimal... prices) {
        BigDecimal flag = new BigDecimal("0");
        for (BigDecimal price : prices) {
            if (price != null) {
                flag = flag.add(price);
            }
        }
        return flag;
    }

    /**
     * 组装每日售卖信息
     */
    private ProductDayIncreasePO assemblyDayIncrease(Map<String, String> requestMap) {
        ProductDayIncreasePO productDayIncreasePO = new ProductDayIncreasePO();
        productDayIncreasePO.setCreatedBy(requestMap.get("updatedBy"));
        productDayIncreasePO.setUpdatedBy(requestMap.get("updatedBy"));
        productDayIncreasePO.setCreatedDt(requestMap.get("updatedDt"));
        productDayIncreasePO.setUpdatedDt(requestMap.get("updatedDt"));
        productDayIncreasePO.setProductId(Integer.valueOf(requestMap.get("productId")));
        productDayIncreasePO.setCompanyCode(requestMap.get("companyCode"));
        productDayIncreasePO.setSaleDate(requestMap.get("saleDate"));

        int salePriceAdjustmentType = Integer.parseInt(requestMap.get("salePriceAdjustmentType"));
        BigDecimal saleMiniAddRadiation = null;
        if (StrUtil.isNotBlank(requestMap.get("saleMiniAddRadiation"))) {
            saleMiniAddRadiation = new BigDecimal(requestMap.get("saleMiniAddRadiation"));
        }

        if (ChannelEnum.getNoByKey(requestMap.get("channelCode")) == 0) {
            productDayIncreasePO.setB2bAdjustmentType(Integer.valueOf(requestMap.get("salePriceAdjustmentType")));
            productDayIncreasePO.setB2bModifiedAmt(new BigDecimal(requestMap.get("modifiedSalePrice")));
            if (salePriceAdjustmentType == 2) {
                productDayIncreasePO.setB2bMiniAddRadiation(saleMiniAddRadiation);
            }
        }
        return productDayIncreasePO;
    }

    /**
     * 组装加幅总表数据
     */
    private List<ProductIncreasePO> assemblyIncrease(BatchSaleDTO batchSaleDTO) {
        List<ProductIncreasePO> productIncreasePOList = new ArrayList<>();
        batchSaleDTO.getItemList().forEach(batchSaleItemDTO -> {
            ProductIncreasePO productIncreasePO = new ProductIncreasePO();
            productIncreasePO.setProductId(batchSaleItemDTO.getProductId());
            productIncreasePO.setCompanyCode(batchSaleDTO.getCompanyCode());
            productIncreasePO.setChannelCode(batchSaleDTO.getChannelCode());
            productIncreasePO.setChannelId(ChannelEnum.getNoByKey(batchSaleDTO.getChannelCode()));
            productIncreasePO.setStartDate(batchSaleItemDTO.getStartDate());
            productIncreasePO.setEndDate(batchSaleItemDTO.getEndDate());
            productIncreasePO.setWeekStr(batchSaleItemDTO.getWeekList());
            // 24-09-12 author: 湫 售价最低加辐值 当加辐类型为百分比时落地
            if (batchSaleItemDTO.getSalePriceAdjustmentType() != null
                    && batchSaleItemDTO.getSalePriceAdjustmentType() == 2
                    && batchSaleItemDTO.getSaleMiniAddRadiation() != null) {
                productIncreasePO.setSaleMiniAddRadiation(batchSaleItemDTO.getSaleMiniAddRadiation());
            }
            productIncreasePO.setAdjustmentType(batchSaleItemDTO.getSalePriceAdjustmentType());
            productIncreasePO.setModifiedAmt(batchSaleItemDTO.getModifiedSalePrice());
            productIncreasePO.setCreatedBy(batchSaleDTO.getUpdatedBy());
            productIncreasePO.setCreatedDt(batchSaleDTO.getUpdatedDt());
            productIncreasePOList.add(productIncreasePO);
        });
        return productIncreasePOList;
    }

    /**
     * 组装加幅总表数据(单天调整)
     */
    private ProductIncreasePO assemblySingleIncrease(Map<String, String> requestMap) {
        ProductIncreasePO productIncreasePO = new ProductIncreasePO();
        productIncreasePO.setProductId(Integer.valueOf(requestMap.get("productId")));
        productIncreasePO.setCompanyCode(requestMap.get("companyCode"));
        productIncreasePO.setChannelCode(requestMap.get("channelCode"));
        productIncreasePO.setChannelId(ChannelEnum.getNoByKey(requestMap.get("channelCode")));
        productIncreasePO.setStartDate(requestMap.get("saleDate"));
        productIncreasePO.setEndDate(requestMap.get("saleDate"));
        productIncreasePO.setWeekStr(null);
        productIncreasePO.setAdjustmentType(Integer.valueOf(requestMap.get("salePriceAdjustmentType")));
        if (StrUtil.isNotBlank(requestMap.get("saleMiniAddRadiation"))) {
            productIncreasePO.setSaleMiniAddRadiation(new BigDecimal(requestMap.get("saleMiniAddRadiation")));
        }

        productIncreasePO.setModifiedAmt(new BigDecimal(requestMap.get("modifiedSalePrice")));
        productIncreasePO.setCreatedBy(requestMap.get("updatedBy"));
        productIncreasePO.setCreatedDt(requestMap.get("updatedDt"));
        return productIncreasePO;
    }

    /**
     * 组装每日表加幅数据list
     */
    private List<ProductDayIncreasePO> assemblyDayIncreaseList(BatchSaleDTO batchSaleDTO) {
        List<ProductDayIncreasePO> productDayIncreasePOList = new ArrayList<>();
        for (BatchSaleItemDTO batchSaleItemDTO : batchSaleDTO.getItemList()) {
            Date startDate = null;
            Date endDate = null;
            //开始日期在21个月之后的不处理
            if (DateUtilX.compare(DateUtilX.stringToDate(batchSaleItemDTO.getStartDate()), DateUtilX.getDate(DateUtilX.getCurrentDate(), 21)) < 0) {
                startDate = DateUtilX.stringToDate(batchSaleItemDTO.getStartDate());
                if (DateUtilX.compare(DateUtilX.stringToDate(batchSaleItemDTO.getEndDate()), DateUtilX.getDate(DateUtilX.getCurrentDate(), 21)) > 0) {
                    endDate = DateUtilX.getDate(DateUtilX.getCurrentDate(), 3);
                } else {
                    endDate = DateUtilX.stringToDate(batchSaleItemDTO.getEndDate());
                }
                //获取日期Map
                List<Date> dateList = DateUtilX.getDateInWeekList(startDate, endDate, batchSaleItemDTO.getWeekList());
                if (!dateList.isEmpty()) {
                    for (Date date : dateList) {
                        ProductDayIncreasePO productDayIncreasePO = new ProductDayIncreasePO();
                        productDayIncreasePO.setProductId(batchSaleItemDTO.getProductId());
                        productDayIncreasePO.setCompanyCode(batchSaleDTO.getCompanyCode());
                        productDayIncreasePO.setSaleDate(DateUtilX.dateToString(date));
                        productDayIncreasePO.setUpdatedBy(batchSaleDTO.getUpdatedBy());
                        productDayIncreasePO.setUpdatedDt(batchSaleDTO.getUpdatedDt());
                        BigDecimal saleMiniAddRadiation = null;
                        if (batchSaleItemDTO.getSalePriceAdjustmentType() != null
                                && batchSaleItemDTO.getSalePriceAdjustmentType() == 2
                                && batchSaleItemDTO.getSaleMiniAddRadiation() != null) {
                            saleMiniAddRadiation = batchSaleItemDTO.getSaleMiniAddRadiation();
                        }
                        if (ChannelEnum.getNoByKey(batchSaleDTO.getChannelCode()) == 0) {
                            productDayIncreasePO.setB2bAdjustmentType(batchSaleItemDTO.getSalePriceAdjustmentType());
                            productDayIncreasePO.setB2bModifiedAmt(batchSaleItemDTO.getModifiedSalePrice());
                            productDayIncreasePO.setB2bMiniAddRadiation(saleMiniAddRadiation);
                        }
                        productDayIncreasePOList.add(productDayIncreasePO);
                    }
                }
            }
        }
        return productDayIncreasePOList;
    }

    @Override
    public Response<List<ProductSalePriceItemDTO>> queryOrderProductPrice(QueryProductRequestDTO queryProductRequestDTO) {
        if (StrUtilX.isEmpty(queryProductRequestDTO.getChannelCode()) || StrUtilX.isEmpty(queryProductRequestDTO.getStartDate())
                || StrUtilX.isEmpty(queryProductRequestDTO.getEndDate())) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }

        // 落地产品
        if (StrUtilX.isNotEmpty(queryProductRequestDTO.getSpProductId())) {
            return Response.success(null);
        }

        if (queryProductRequestDTO.getEndDate() != null) {
            queryProductRequestDTO.setEndDate(DateUtilX.dateToString(DateUtilX.getDate(DateUtilX.stringToDate(queryProductRequestDTO.getEndDate()), -1, 0)));
        }
        //获取日期列表
        Map<Date, Date> dateMap = DateUtilX.getDateMap(DateUtilX.stringToDate(queryProductRequestDTO.getStartDate()), DateUtilX.stringToDate(queryProductRequestDTO.getEndDate()));
        //查询产品
        ProductDTO productDTO = productMapper.queryProduct(queryProductRequestDTO.getProductId());
        ProductSalePriceDetailDTO productSalePriceDetailDTO = CommonConvert.INSTANCE.productSalePriceDetailDTOConvert(productDTO);

        ProTaxRuleConfigResp proTaxRuleConfigResp = null;
        // 获取税费规则
        if (productDTO.getPriceType() != null
                && productDTO.getPriceType() == 1
                && productDTO.getTaxRuleConfigId() != null) {
            ProTaxRuleConfigQueryReq proTaxRuleConfigQueryReq = new ProTaxRuleConfigQueryReq();
            proTaxRuleConfigQueryReq.setId(productDTO.getTaxRuleConfigId());
            proTaxRuleConfigResp = proTaxRuleConfigService.proTaxRuleConfigDetail(proTaxRuleConfigQueryReq);
        }

        if (productDTO.getSupplyType() != null && productDTO.getSupplyType() == 2) {
            assemblySupplyDailyPrice(dateMap, productSalePriceDetailDTO, queryProductRequestDTO.getChannelCode(), productDTO.getCurrency(), queryProductRequestDTO.getCompanyCode());
        } else {
            //查询底价
            Example priceExample = new Example(ProductPricePO.class);
            Example.Criteria priceCriteria = priceExample.createCriteria();
            priceCriteria.andEqualTo("productId", queryProductRequestDTO.getProductId());
            priceCriteria.andBetween("saleDate", queryProductRequestDTO.getStartDate(), queryProductRequestDTO.getEndDate());
            List<ProductPricePO> pricePOList = productPriceMapper.selectByExample(priceExample);
            //查询配额和房态
            List<ProductQuotaPO> productQuotaPOList = null;
            Map<String, Integer> debitedQuotaMap = new HashMap<>();
            if (null != productDTO.getQuotaAccountId()) {
                Example quotaExample = new Example(ProductQuotaPO.class);
                Example.Criteria quotaCriteria = quotaExample.createCriteria();
                quotaCriteria.andEqualTo("quotaAccountId", productDTO.getQuotaAccountId());
                quotaCriteria.andBetween("saleDate", queryProductRequestDTO.getStartDate(), queryProductRequestDTO.getEndDate());
                productQuotaPOList = productQuotaMapper.selectByExample(quotaExample);

                Example debitedExample = new Example(DebitedQuotaPO.class);
                Example.Criteria debitedCriteria = debitedExample.createCriteria();
                debitedCriteria.andEqualTo("quotaAccountId", productDTO.getQuotaAccountId());
                debitedCriteria.andEqualTo("productId", productDTO.getProductId());
                debitedCriteria.andBetween("saleDate", queryProductRequestDTO.getStartDate(), queryProductRequestDTO.getEndDate());
                List<DebitedQuotaPO> debitedQuotaPOList = debitedQuotaMapper.selectByExample(debitedExample);
                if (CollUtilX.isNotEmpty(debitedQuotaPOList)) {
                    for (DebitedQuotaPO debitedQuotaPO : debitedQuotaPOList) {
                        if (null != debitedQuotaPO.getQuota()) {
                            if (null == debitedQuotaMap.get(debitedQuotaPO.getSaleDate())) {
                                debitedQuotaMap.put(debitedQuotaPO.getSaleDate(), -debitedQuotaPO.getQuota());
                            } else {
                                debitedQuotaMap.put(debitedQuotaPO.getSaleDate(), debitedQuotaMap.get(debitedQuotaPO.getSaleDate()) - debitedQuotaPO.getQuota());
                            }
                        }
                    }
                }
            }
            //查询加幅
            Example salePriceExample = new Example(ProductDayIncreasePO.class);
            Example.Criteria salePriceCriteria = salePriceExample.createCriteria();
            salePriceCriteria.andEqualTo("productId", queryProductRequestDTO.getProductId());
            salePriceCriteria.andEqualTo("companyCode", queryProductRequestDTO.getCompanyCode());
            salePriceCriteria.andBetween("saleDate", queryProductRequestDTO.getStartDate(), queryProductRequestDTO.getEndDate());
            List<ProductDayIncreasePO> productDayIncreasePOList = productDayIncreaseMapper.selectByExample(salePriceExample);
            //组装数据
            this.assemblyProductSalePrice(proTaxRuleConfigResp, dateMap, productSalePriceDetailDTO, pricePOList, productQuotaPOList, productDayIncreasePOList, productDTO, queryProductRequestDTO);
        }
        if (CollUtilX.isNotEmpty(productSalePriceDetailDTO.getPriceList())) {
            return Response.success(productSalePriceDetailDTO.getPriceList());
        } else {
            return Response.success(new ArrayList<>());
        }
    }


    @Override
    public PageInfo<ProductSaleLogDTO> queryProductSaleLogList(Map<String, String> requestMap) {
        if (StrUtilX.isEmpty(requestMap.get("productId"))) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
        if (StrUtilX.isEmpty(requestMap.get("pageSize"))) {
            requestMap.put("pageSize", String.valueOf(new BaseRequest().getPageSize()));
        }
        if (StrUtilX.isEmpty(requestMap.get("currentPage"))) {
            requestMap.put("currentPage", String.valueOf(new BaseRequest().getCurrentPage()));
        }
        PageHelper.startPage(Integer.parseInt(requestMap.get("currentPage")), Integer.parseInt(requestMap.get("pageSize")));
        List<ProductSaleLogDTO> productSaleLogDTOS = productMapper.queryProductSaleLogList(requestMap);

        if (StrUtilX.isNotEmpty(requestMap.get("saleDate")) && StrUtilX.isNotEmpty(requestMap.get("operationType"))
                && requestMap.get("operationType").equals("1")) {
            //计算星期几
            String saleDate = requestMap.get("saleDate");
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(DateUtilX.stringToDate(saleDate));
            String week = String.valueOf(calendar.get(Calendar.DAY_OF_WEEK));

            //批量操作时候，可能会有适应星期几的操作，这里处理，若是不在该操作范围内移除
            productSaleLogDTOS.removeIf(productSaleLogDTO -> productSaleLogDTO.getOperationWeek() != null && StrUtilX.isNotEmpty(productSaleLogDTO.getOperationWeek()) && !productSaleLogDTO.getOperationWeek().contains(week));
        }
        return new PageInfo<>(productSaleLogDTOS);
    }

    @Override
    public Response<String> resolveDayIncrease(String startDate, String endDate) {
        if (StrUtilX.isEmpty(startDate) || StrUtilX.isEmpty(endDate)
                || DateUtilX.compare(DateUtilX.stringToDate(startDate), DateUtilX.stringToDate(endDate)) > 0) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }
        int resolveProductCount = 0;
        //循环每一天分解加幅数据
        List<Date> dateList = DateUtilX.getDateList(DateUtilX.stringToDate(startDate), DateUtilX.stringToDate(endDate));
        Map<String, Object> paramMap;
        //按商家+产品Id分组
        Map<String, ProductIncreasePO> companyProductMap;
        //按商家+产品Id存放每一天加幅数据（用于多渠道分解）
        Map<String, ProductDayIncreasePO> productDayIncreasePOMap;
        for (Date saleDate : dateList) {
            //查询日期的星期
            int week = DateUtilX.getWeekOfDate(saleDate) - 1;//0-6表示周一至周天
            //查询每一天的加幅总表数据
            paramMap = new HashMap<>();
            paramMap.put("saleDate", saleDate);
            paramMap.put("week", week);
            List<ProductIncreasePO> productIncreasePOList = productIncreaseMapper.queryOneDayIncrease(paramMap);
            if (CollUtilX.isNotEmpty(productIncreasePOList)) {
                companyProductMap = new HashMap<>();
                productDayIncreasePOMap = new HashMap<>();
                for (ProductIncreasePO productIncreasePO : productIncreasePOList) {
                    companyProductMap.put(productIncreasePO.getCompanyCode() + "#" + productIncreasePO.getProductId(), productIncreasePO);
                }
                resolveProductCount += companyProductMap.size();
                for (String companyProductId : companyProductMap.keySet()) {
                    ProductIncreasePO productIncreasePO = companyProductMap.get(companyProductId);
                    if (null == productDayIncreasePOMap.get(companyProductId)) {
                        ProductDayIncreasePO productDayIncreasePO = new ProductDayIncreasePO();
                        productDayIncreasePO.setProductId(productIncreasePO.getProductId());
                        productDayIncreasePO.setCompanyCode(productIncreasePO.getCompanyCode());
                        productDayIncreasePO.setSaleDate(DateUtilX.dateToString(saleDate));
                        productDayIncreasePO.setCreatedBy(productIncreasePO.getCreatedBy());
                        productDayIncreasePO.setCreatedDt(DateUtilX.getCurrentDateStr(1));
                        this.assembleDayIncrease(productDayIncreasePO, productIncreasePO);
                        productDayIncreasePOMap.put(companyProductId, productDayIncreasePO);
                    } else {
                        this.assembleDayIncrease(productDayIncreasePOMap.get(companyProductId), productIncreasePO);
                    }
                }
                //每日加幅数据入库
                List<ProductDayIncreasePO> productDayIncreasePOList = new ArrayList<>(productDayIncreasePOMap.values());
                int oneTimeCount = 1000;//每次入库1000条
                for (int i = 0; i < productDayIncreasePOList.size(); i += oneTimeCount) {
                    if (i + oneTimeCount > productDayIncreasePOList.size()) {
                        productDayIncreaseMapper.mergeProductDayIncrease(productDayIncreasePOList.subList(i, productDayIncreasePOList.size() - 1));
                    } else {
                        productDayIncreaseMapper.mergeProductDayIncrease(productDayIncreasePOList.subList(i, i + oneTimeCount));
                    }
                }
            }
        }
        return Response.success("总共分解" + resolveProductCount + "个产品;");
    }

    @Override
    public void updateSalePrice(UpdateSalePriceDTO updateSalePriceDTO) {
        if (StrUtilX.isEmpty(updateSalePriceDTO.getChannelCode()) || updateSalePriceDTO.getProductId() == null) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }

        // 更新售价时添加日志
        ProductSaleLogPO productSaleLogPO = new ProductSaleLogPO();
        productSaleLogPO.setChannelCode(updateSalePriceDTO.getChannelCode());
        productSaleLogPO.setCompanyCode(updateSalePriceDTO.getCompanyCode());
        productSaleLogPO.setCreatedBy(updateSalePriceDTO.getCreatedBy());
        productSaleLogPO.setCreatedDt(DateUtilX.dateToString(new Date(), hour_format));
        productSaleLogPO.setOperationType(ProductSaleOperationEnum.SALE.no);
        productSaleLogPO.setProductId(updateSalePriceDTO.getProductId());
        productSaleLogPO.setStartDate(updateSalePriceDTO.getStartDate());
        productSaleLogPO.setEndDate(updateSalePriceDTO.getEndDate());
        productSaleLogPO.setContent("更新售价");
        productSaleLogMapper.insert(productSaleLogPO);
    }

    /**
     * 将加幅总表加幅组装到每日加幅对象
     */
    private void assembleDayIncrease(ProductDayIncreasePO productDayIncreasePO, ProductIncreasePO productIncreasePO) {
        if (productIncreasePO.getChannelId() == 0) {
            productDayIncreasePO.setB2bAdjustmentType(productIncreasePO.getAdjustmentType());
            productDayIncreasePO.setB2bModifiedAmt(productIncreasePO.getModifiedAmt());
        }
    }

    /**
     * 过滤产品是否有变化
     */
    private List<String> handlerProductSaleStatus(List<ProductSaleStatusPO> productSaleStatusList) {
        if (CollUtilX.isEmpty(productSaleStatusList)) {
            return null;
        }

        List<String> incrementList = new ArrayList<>();
        Iterator<ProductSaleStatusPO> iterator = productSaleStatusList.iterator();
        while (iterator.hasNext()) {
            ProductSaleStatusPO productSaleStatusPO = iterator.next();
            String redisKey = StrUtilX.concat(productSaleStatusPO.getCompanyCode(), "_", String.valueOf(productSaleStatusPO.getProductId()));

            ProductSaleStatusDTO productSaleStatusDTO = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.productSaleStatusKey, redisKey), ProductSaleStatusDTO.class);
            int type = -1;
            String channelCode = null;

            if ((productSaleStatusPO.getB2bSaleStatus() != null) && (productSaleStatusDTO == null || productSaleStatusDTO.getB2bSaleStatus() == null
                    || (!productSaleStatusDTO.getB2bSaleStatus().equals(productSaleStatusPO.getB2bSaleStatus())))) {
                channelCode = ChannelEnum.B2B.key;
                type = IncrementTypeEnum.ON_SALE.no;
            }

            if (type != -1) {
                IncrementDTO incrementDTO = new IncrementDTO();
                incrementDTO.setProductId(productSaleStatusPO.getProductId());
                incrementDTO.setType(type);
                incrementDTO.setChannelCode(channelCode);
                incrementDTO.setCompanyCode(productSaleStatusPO.getCompanyCode());
                incrementList.add(JSON.toJSONString(incrementDTO));
            } else {
                iterator.remove();
            }
        }
        return incrementList;
    }

    /**
     * 处理产品加幅
     */
    private List<String> handlerProductIncrease(List<ProductDayIncreasePO> productDayIncreasePOList) {
        if (CollUtilX.isEmpty(productDayIncreasePOList)) {
            return null;
        }

        Map<String, IncrementDTO> incrementMap = new HashMap<>();
        Iterator<ProductDayIncreasePO> iterator = productDayIncreasePOList.iterator();
        while (iterator.hasNext()) {
            ProductDayIncreasePO productDayIncreasePO = iterator.next();
            String redisKey = StrUtilX.concat(productDayIncreasePO.getCompanyCode(), "_", String.valueOf(productDayIncreasePO.getProductId()), "_", productDayIncreasePO.getSaleDate());

            ProductSaleIncreaseDTO productSaleIncreaseDTO = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.productSalePriceKey, redisKey), ProductSaleIncreaseDTO.class);

            int type = -1;
            String channelCode = null;

            if ((productDayIncreasePO.getB2bAdjustmentType() != null && productDayIncreasePO.getB2bModifiedAmt() != null) &&
                    (productSaleIncreaseDTO == null || productSaleIncreaseDTO.getB2bAdjustmentType() == null || productSaleIncreaseDTO.getB2bModifiedAmt() == null
                            || productDayIncreasePO.getB2bModifiedAmt().compareTo(productSaleIncreaseDTO.getB2bModifiedAmt()) != 0
                            || !productDayIncreasePO.getB2bAdjustmentType().equals(productSaleIncreaseDTO.getB2bAdjustmentType())
                            || !compareMiniAddRadiation(productDayIncreasePO.getB2bMiniAddRadiation(), productSaleIncreaseDTO.getB2bMiniAddRadiation())
                    )) {
                type = IncrementTypeEnum.SALE_PRICE.no;
                channelCode = ChannelEnum.B2B.key;
            }

            if (type != -1) {
                if (!incrementMap.isEmpty() && incrementMap.get(StrUtilX.concat(productDayIncreasePO.getCompanyCode(), "_", String.valueOf(productDayIncreasePO.getProductId()))) != null) {
                    incrementMap.get(StrUtilX.concat(productDayIncreasePO.getCompanyCode(), "_", String.valueOf(productDayIncreasePO.getProductId()))).getSaleDate().add(productDayIncreasePO.getSaleDate());
                } else {
                    IncrementDTO incrementDTO = new IncrementDTO();
                    incrementDTO.setProductId(productDayIncreasePO.getProductId());
                    incrementDTO.setCompanyCode(productDayIncreasePO.getCompanyCode());
                    incrementDTO.setChannelCode(channelCode);
                    incrementDTO.setType(type);
                    incrementDTO.setSaleDate(new HashSet<>(Collections.singletonList(productDayIncreasePO.getSaleDate())));
                    incrementMap.put(StrUtilX.concat(productDayIncreasePO.getCompanyCode(), "_", String.valueOf(productDayIncreasePO.getProductId())), incrementDTO);
                }
            } else {
                iterator.remove();
            }
        }

        if (!incrementMap.isEmpty()) {
            return incrementMap.values().stream().map(JSON::toJSONString).collect(Collectors.toList());
        }
        return null;
    }

    /**
     * 比较最小加幅和加幅系数是否相等  相等 返回true 否则返回false
     */
    private boolean compareMiniAddRadiation(BigDecimal oldMiniAddRadiation, BigDecimal newMiniAddRadiation) {
        if (oldMiniAddRadiation == null && newMiniAddRadiation == null) {
            return true;
        } else if (oldMiniAddRadiation != null && newMiniAddRadiation == null) {
            return false;
        } else if (oldMiniAddRadiation == null) {
            return false;
        } else return oldMiniAddRadiation.compareTo(newMiniAddRadiation) == 0;
    }

//    /**
//     * 计算分销商可售酒店列表
//     */
//    @Override
//    public void calculateAgentAvailableHotel(String agentCode, List<String> hotelIdList) {
//        try {
//            if (Objects.isNull(agentCode) || CollUtilX.isEmpty(hotelIdList)) {
//                return;
//            }
//            // 查询分销商
//            AgentAccountConfig agentAccountConfig = JSON.parseObject(RedisTemplateX.hashGet(RedisKey.AGENT_ACCOUNT_CONFIG, agentCode).toString(), AgentAccountConfig.class);
//
//            // 获取商家币种，判断是否存在商家转客户
//            OrgDTO orgDTO = commonInitializer.getOrgInfo();
//            if (orgDTO == null || StrUtilX.isEmpty(orgDTO.getOrgCode()) || StrUtilX.isEmpty(orgDTO.getOrgCurrency())) {
//                return;
//            }
//            // 获取所有币种
//            Map<Integer, String> currencyMap = queryUtil.queryAllCurrency();
//            // 币种不存在不计算
//            int orgCurrency = Integer.parseInt(orgDTO.getOrgCurrency());
//            if (!currencyMap.containsKey(Integer.parseInt(orgDTO.getOrgCurrency())) || !currencyMap.containsKey(agentAccountConfig.getSettlementCurrency())) {
//                return;
//            }
//            // 获取所有汇率
//            Map<String, BigDecimal> rateMap = queryUtil.queryAllRate();
//            // 判断商家转客户汇率是否存在，不存在默认1
//            String orgToAgentKey = orgDTO.getOrgCode() + "_" + currencyMap.get(Integer.parseInt(orgDTO.getOrgCurrency())) + "_" + currencyMap.get(agentAccountConfig.getSettlementCurrency());
//            if (!rateMap.containsKey(orgToAgentKey)) {
//                rateMap.put(orgToAgentKey, BigDecimal.ONE);
//            }
//            // 商家转客户汇率
//            BigDecimal orgToAgentRate = rateMap.get(orgToAgentKey);
//
//            // 查询客户白名单供应商
//            List<String> supplyCodes = queryUtil.getAgentSupplyAvailable(agentCode);
//
//            // 查询供应商配置
//            Map<String, SupplierAddDTO> supplyConfigMap = queryUtil.getSupplierConfig(supplyCodes);
//
//            // 查询分销商-供应商加幅
//            Map<String, SupplyIncrease> supplyIncreaseMap = queryUtil.getSupplyIncrease(agentCode, supplyCodes);
//            // 可能不存在客户加幅
//            if (Objects.isNull(supplyIncreaseMap)) {
//                supplyIncreaseMap = new HashMap<>();
//            }
//
//            // 根据酒店id、售卖日期查询起价
//            Criteria criteria = new Criteria();
//            criteria.andOperator(
//                    Criteria.where("merchantSource").is("TIANGONG"),
//                    Criteria.where("merchantCode").is(orgDTO.getOrgCode()),
//                    Criteria.where("hotelId").in(hotelIdList),
//                    Criteria.where("supplyCode").in(supplyCodes),
//                    Criteria.where("saleDate").gte(DateUtilX.dateToString(DateUtilX.getCurrentDate())).lte(DateUtilX.dateToString(DateUtilX.getAddDay(DateUtilX.getCurrentDate(), 29)))
//            );
//            Query query = new Query(criteria);
//            List<SupplyHotelLowestPrice> supplyHotelLowestPriceList = mongoTemplate.find(query, SupplyHotelLowestPrice.class);
//            if (CollUtilX.isEmpty(supplyHotelLowestPriceList)) {
//                return;
//            }
//
//            // 遍历酒店起价
//            Map<Long, Map<String, List<SupplyHotelLowestPrice>>> hotelLowestMap = new HashMap<>();
//            for (SupplyHotelLowestPrice supplyHotelLowestPrice : supplyHotelLowestPriceList) {
//                if (hotelLowestMap.containsKey(supplyHotelLowestPrice.getHotelId())) {
//                    if (hotelLowestMap.get(supplyHotelLowestPrice.getHotelId()).containsKey(supplyHotelLowestPrice.getSaleDate())) {
//                        hotelLowestMap.get(supplyHotelLowestPrice.getHotelId()).get(supplyHotelLowestPrice.getSaleDate()).add(supplyHotelLowestPrice);
//                    } else {
//                        List<SupplyHotelLowestPrice> priceList = new ArrayList<>();
//                        priceList.add(supplyHotelLowestPrice);
//                        hotelLowestMap.get(supplyHotelLowestPrice.getHotelId()).put(supplyHotelLowestPrice.getSaleDate(), priceList);
//                    }
//                } else {
//                    Map<String, List<SupplyHotelLowestPrice>> map = new HashMap<>();
//                    List<SupplyHotelLowestPrice> priceList = new ArrayList<>();
//                    priceList.add(supplyHotelLowestPrice);
//                    map.put(supplyHotelLowestPrice.getSaleDate(), priceList);
//                    hotelLowestMap.put(supplyHotelLowestPrice.getHotelId(), map);
//                }
//            }
//
//            // 循环30天，直到查到起价为止
//            List<Date> dateList = DateUtilX.getDateList(DateUtilX.getCurrentDate(), DateUtilX.addDate(DateUtilX.getCurrentDate(), 29));
////            // 日期排序 2024-11-22 getDateList 返回的时间已经是升序了，故此不需要再次进行排序
////            dateList.sort(Date::compareTo);
//
//            // 需要新增的数据
//            List<AgentHotelLowestPriceEntity> agentHotelLowestPriceEntityList = new ArrayList<>();
//            // 处理酒店起价
//            for (Map.Entry<Long, Map<String, List<SupplyHotelLowestPrice>>> entry : hotelLowestMap.entrySet()) {
//                BigDecimal salePrice = null;// 最低售价
//                SupplyHotelLowestPrice supplyHotelLowestPriceAvail = null;// 最低售价信息
//                for (Date date : dateList) {
//                    String dateStr = DateUtilX.dateToString(date);
//                    // 判断当前日期数据是否存在，不存在则取后一天，直到取完30天
//                    if (!entry.getValue().containsKey(dateStr)) {
//                        continue;
//                    }
//                    List<SupplyHotelLowestPrice> priceList = entry.getValue().get(dateStr);
//                    for (SupplyHotelLowestPrice supplyHotelLowestPrice : priceList) {
//                        BigDecimal newPrice = null;
//                        if (Objects.nonNull(supplyHotelLowestPrice.getLowestPrice()) && Objects.nonNull(supplyHotelLowestPrice.getLowestPriceCurreny())) {
//                            BigDecimal rate;
//                            // 判断币种是否一致
//                            if (supplyHotelLowestPrice.getLowestPriceCurreny().equals(Integer.parseInt(orgDTO.getOrgCurrency()))) {
//                                rate = BigDecimal.ONE;
//                            } else {
//                                // 组装key
//                                String supplyToOrgKey = orgDTO.getOrgCode() + "_" + currencyMap.get(supplyHotelLowestPrice.getLowestPriceCurreny()) + "_" + currencyMap.get(Integer.parseInt(orgDTO.getOrgCurrency()));
//                                // 查供应商转商家汇率
//                                rate = rateMap.get(supplyToOrgKey);
//                            }
//                            if (Objects.nonNull(rate)) {
//                                // 计算售价
//                                newPrice = queryUtil.calculateSalePrice(supplyHotelLowestPrice,
//                                        agentAccountConfig,
//                                        supplyConfigMap.get(supplyHotelLowestPrice.getSupplyCode()),
//                                        supplyIncreaseMap.get(supplyHotelLowestPrice.getSupplyCode()),
//                                        rate, orgToAgentRate, orgCurrency, supplyHotelLowestPrice.getLowestPriceCurreny());
//
//                                // 计算现付价-只换算汇率
//                                if (Objects.nonNull(supplyHotelLowestPrice.getCashPrice()) && Objects.nonNull(supplyHotelLowestPrice.getCashCurrency())) {
//                                    BigDecimal cashRate;
//                                    // 判断币种是否一致
//                                    if (supplyHotelLowestPrice.getCashCurrency().equals(Integer.parseInt(orgDTO.getOrgCurrency()))) {
//                                        cashRate = BigDecimal.ONE;
//                                    } else {
//                                        // 组装key
//                                        String supplyToOrgCashKey = orgDTO.getOrgCode() + "_" + currencyMap.get(supplyHotelLowestPrice.getCashCurrency()) + "_" + currencyMap.get(Integer.parseInt(orgDTO.getOrgCurrency()));
//                                        // 查供应商转商家现付汇率
//                                        cashRate = rateMap.get(supplyToOrgCashKey);
//                                    }
//
//                                    // 比较预付价和现付价
//                                    if (Objects.nonNull(cashRate)) {
//                                        BigDecimal newCashPrice = queryUtil.calculateCashSalePrice(supplyHotelLowestPrice.getCashPrice(), supplyHotelLowestPrice.getCashCurrency(), agentAccountConfig, cashRate, orgToAgentRate, orgCurrency);
//                                        if (Objects.nonNull(newCashPrice) && (Objects.isNull(newPrice) || newCashPrice.compareTo(newPrice) < 0)) {
//                                            newPrice = newCashPrice;
//                                        }
//                                    }
//                                }
//                            }
//                        } else if (Objects.nonNull(supplyHotelLowestPrice.getCashPrice()) && Objects.nonNull(supplyHotelLowestPrice.getCashCurrency())) {
//                            BigDecimal cashRate;
//                            // 判断币种是否一致
//                            if (supplyHotelLowestPrice.getCashCurrency().equals(Integer.parseInt(orgDTO.getOrgCurrency()))) {
//                                cashRate = BigDecimal.ONE;
//                            } else {
//                                // 组装key
//                                String supplyToOrgCashKey = orgDTO.getOrgCode() + "_" + currencyMap.get(supplyHotelLowestPrice.getCashCurrency()) + "_" + currencyMap.get(Integer.parseInt(orgDTO.getOrgCurrency()));
//                                // 查供应商转商家现付汇率
//                                cashRate = rateMap.get(supplyToOrgCashKey);
//                            }
//
//                            // 比较预付价和现付价
//                            if (Objects.nonNull(cashRate)) {
//                                BigDecimal newCashPrice = queryUtil.calculateCashSalePrice(supplyHotelLowestPrice.getCashPrice(), supplyHotelLowestPrice.getCashCurrency(), agentAccountConfig, cashRate, orgToAgentRate, orgCurrency);
//                                if (Objects.nonNull(newCashPrice)) {
//                                    newPrice = newCashPrice;
//                                }
//                            }
//                        }
//                        if (Objects.nonNull(newPrice) && (Objects.isNull(salePrice) || newPrice.compareTo(salePrice) < 0)) {
//                            salePrice = newPrice;
//                            supplyHotelLowestPriceAvail = supplyHotelLowestPrice;
//                        }
//                    }
//                    break;
//                }
//
//                // 组装客户可售酒店信息
//                if (Objects.nonNull(salePrice) && Objects.nonNull(supplyHotelLowestPriceAvail)) {
//                    agentHotelLowestPriceEntityList.add(assemblyLowestPriceEntity(agentCode, String.valueOf(entry.getKey()), salePrice, agentAccountConfig.getSettlementCurrency(),
//                            supplyHotelLowestPriceAvail.getPayInStorePrice(), supplyHotelLowestPriceAvail.getPayInStoreCurrency(), supplyHotelLowestPriceAvail.getCityCode()));
//                }
//            }
//
//            // 保存到数据库
//            if (CollUtilX.isNotEmpty(agentHotelLowestPriceEntityList)) {
//                try {

    /// /                    productMapper.batchInsertOrUpdateAHotel(agentHotelLowestPriceEntityList, getXTableName(agentCode, false));
//
//                    //将数据存储到基础信息mongodb的公共表
//                    HotelPublicDTO hotelPublicDTO = new HotelPublicDTO();
//                    List<LowestPrice> list = new ArrayList<>();
//                    for (AgentHotelLowestPriceEntity entity : agentHotelLowestPriceEntityList) {
//                        //hotelPublicDTO.setHotelId(entity.getHotelId());
//                        LowestPrice lowestPrice = new LowestPrice();
//                        lowestPrice.setAgentCode(entity.getAgentCode());
//                        lowestPrice.setHotelId(entity.getHotelId());
//                        if (entity.getLowestPrice() != null) {
//                            lowestPrice.setLowestPrice(entity.getLowestPrice().doubleValue());
//                        }
//                        lowestPrice.setSaleCurrency(entity.getSaleCurrency());
//                        if (entity.getPayAtHotelFee() != null) {
//                            lowestPrice.setPayAtHotelFee(entity.getPayAtHotelFee().doubleValue());
//                        }
//                        lowestPrice.setPayAtHotelCurrency(entity.getPayAtHotelCurrency());
//                        list.add(lowestPrice);
//                    }
//                    hotelPublicDTO.setLowestPriceDTOList(list);
//                    CompletableFuture.runAsync(() -> {
//                        hotelRemote.updateHotelMongodbByHotelId(hotelPublicDTO);
//                    },processingProductInfoExecutor);
//                } catch (Exception e) {
//                    log.error("分销商可售酒店入库失败", e);
//                }
//            }
//        } catch (Exception e) {
//            log.error("calculateAgentAvailableHotel error!", e);
//        }
//    }
//
//    /**
//     * 组装客户可售酒店列表Entity
//     */
//    private AgentHotelLowestPriceEntity assemblyLowestPriceEntity(String agentCode, String hotelId,
//                                                                  BigDecimal lowestPrice, Integer saleCurrency,
//                                                                  BigDecimal payAtHotelPrice, Integer payAtHotelCurrency,
//                                                                  String cityCode) {
//        AgentHotelLowestPriceEntity agentHotelLowestPriceEntity = new AgentHotelLowestPriceEntity();
//        agentHotelLowestPriceEntity.setAgentCode(agentCode);
//        agentHotelLowestPriceEntity.setCityCode(cityCode);
//        agentHotelLowestPriceEntity.setHotelId(Long.parseLong(hotelId));
//        agentHotelLowestPriceEntity.setCreatedBy(Constant.SYSTEM);
//        agentHotelLowestPriceEntity.setCreatedDt(new Date());
//        agentHotelLowestPriceEntity.setDeleted(0);
//        agentHotelLowestPriceEntity.setLowestPrice(lowestPrice);
//        agentHotelLowestPriceEntity.setSaleCurrency(saleCurrency);
//        agentHotelLowestPriceEntity.setPayAtHotelFee(payAtHotelPrice);
//        agentHotelLowestPriceEntity.setPayAtHotelCurrency(payAtHotelCurrency);
//        return agentHotelLowestPriceEntity;
//    }
    @Override
    public void calculateAgentAvailableHotel(String agentCode, List<String> hotelIdList) {
        try {
            // 前置校验
            if (Objects.isNull(agentCode) || CollUtil.isEmpty(hotelIdList)) {
                return;
            }

            // 获取客户配置信息
            AgentAccountConfig agentConfig = getCachedAgentConfig(agentCode);
            OrgDTO orgDTO = CommonInitializer.getOrgInfo();
            // 获取所有汇率
            Map<String, BigDecimal> rateMap = RedisUtil.queryAllRate();

            // 校验基础配置
            if (!validateBaseConfig(agentConfig, orgDTO)) {
                return;
            }

            // 准备汇率计算参数
            BigDecimal orgToAgentRate = prepareExchangeRate(orgDTO, agentConfig, rateMap);
            if (orgToAgentRate == null) return;

            // 获取供应商信息
            List<String> supplyCodes = queryUtil.getAgentSupplyAvailable(agentCode);
            if (CollUtil.isEmpty(supplyCodes)) return;

            // 批量获取供应商配置
            Map<String, SupplierAddDTO> supplyConfigMap = queryUtil.getSupplierConfig(supplyCodes);

            Map<String, SupplyIncrease> supplyIncreaseMap = queryUtil.getSupplyIncrease(agentCode, supplyCodes);

            // 分页查询MongoDB数据
            List<SupplyHotelLowestPrice> allPrices = paginatedMongoQuery(orgDTO, hotelIdList, supplyCodes);

            // 流式分组处理
            Map<Long, Map<String, List<SupplyHotelLowestPrice>>> hotelLowestMap = allPrices.stream()
                    .collect(Collectors.groupingBy(
                            SupplyHotelLowestPrice::getHotelId,
                            Collectors.groupingBy(SupplyHotelLowestPrice::getSaleDate)
                    ));

            //获取分销商-供应商-酒店维度加幅配置
            Set<Long> hotelIds = hotelLowestMap.keySet();
            Map<String, SupplyHotelIncrease> supplyHotelIncreaseMap = queryUtil.getSupplyHotelIncrease(agentCode, supplyCodes, new ArrayList<>(hotelIds));
            // 分批次处理结果
            processInBatches(hotelLowestMap, agentCode, orgDTO, agentConfig,
                    supplyConfigMap, supplyIncreaseMap, supplyHotelIncreaseMap, rateMap, orgToAgentRate);

        } catch (Exception e) {
            log.error("calculateAgentAvailableHotel error!", e);
        }
    }

    /**
     * 验证基础配置有效性
     */
    private boolean validateBaseConfig(AgentAccountConfig agentConfig, OrgDTO orgDTO) {
        if (agentConfig == null || agentConfig.getSettlementCurrency() == null) {
            log.warn("分销商配置无效 agentCode={}", agentConfig != null ? agentConfig.getAgentCode() : "null");
            return false;
        }
        if (orgDTO == null || StrUtilX.isEmpty(orgDTO.getOrgCode())) {
            log.warn("商家信息不完整 orgDTO={}", orgDTO);
            return false;
        }
        if (StrUtilX.isEmpty(SettlementCurrencyEnum.getCodeByKey(String.valueOf(agentConfig.getSettlementCurrency())))) {
            log.warn("分销商结算币种不存在 currency={}", agentConfig.getSettlementCurrency());
            return false;
        }
        try {
            if (StrUtilX.isEmpty(SettlementCurrencyEnum.getCodeByKey(orgDTO.getOrgCurrency()))) {
                log.warn("商家币种不存在 currency={}", orgDTO.getOrgCurrency());
                return false;
            }
        } catch (NumberFormatException e) {
            log.error("商家币种格式错误 currencyStr={}", orgDTO.getOrgCurrency());
            return false;
        }
        return true;
    }

    /**
     * 准备汇率参数
     */
    private BigDecimal prepareExchangeRate(OrgDTO orgDTO, AgentAccountConfig agentConfig,
                                           Map<String, BigDecimal> rateMap) {
        try {
            String orgCurrCode = SettlementCurrencyEnum.getCodeByKey(orgDTO.getOrgCurrency());
            String settleCurrCode = SettlementCurrencyEnum.getCodeByKey(String.valueOf(agentConfig.getSettlementCurrency()));
            String rateKey = String.format("%s_%s_%s",
                    orgDTO.getOrgCode(), orgCurrCode, settleCurrCode);

            BigDecimal rate = rateMap.get(rateKey);
            if (rate == null) {
                log.info("未找到商家转客户汇率，使用默认值1 key={}", rateKey);
                rate = BigDecimal.ONE;
                rateMap.put(rateKey, rate);
            }
            return rate;
        } catch (Exception e) {
            log.error("汇率参数准备失败", e);
            return null;
        }
    }

    /**
     * 分页查询MongoDB实现
     */
    private List<SupplyHotelLowestPrice> paginatedMongoQuery(OrgDTO orgDTO, List<String> hotelIds, List<String> supplyCodes) {
        List<SupplyHotelLowestPrice> allPrices = new ArrayList<>(2048);
        int pageSize = 1000;
        int pageNumber = 0;

        Criteria criteria = Criteria.where("merchantSource").is(CompanyDTO.COMPANY_NAME)
                .and("merchantCode").is(orgDTO.getOrgCode())
                .and("hotelId").in(hotelIds)
                .and("supplyCode").in(supplyCodes)
                .and("saleDate").gte(DateUtilX.dateToString(DateUtilX.getCurrentDate()))
                .lte(DateUtilX.dateToString(DateUtilX.getAddDay(DateUtilX.getCurrentDate(), 29)));

        while (true) {
            Query query = new Query(criteria)
                    .skip((long) pageNumber * pageSize)
                    .limit(pageSize)
                    .with(Sort.by(Sort.Direction.ASC, "saleDate"));

            List<SupplyHotelLowestPrice> pageResults = mongoTemplate.find(query, SupplyHotelLowestPrice.class);
            if (CollUtil.isEmpty(pageResults)) break;

            allPrices.addAll(pageResults);
            pageNumber++;

            // 提前释放内存
            pageResults.clear();
        }
        return allPrices;
    }

    /**
     * 批次处理核心逻辑
     */
    private void processInBatches(Map<Long, Map<String, List<SupplyHotelLowestPrice>>> hotelLowestMap,
                                  String agentCode, OrgDTO orgDTO, AgentAccountConfig agentConfig,
                                  Map<String, SupplierAddDTO> supplyConfigMap,
                                  Map<String, SupplyIncrease> supplyIncreaseMap,
                                  Map<String, SupplyHotelIncrease> supplyHotelIncreaseMap,
                                  Map<String, BigDecimal> rateMap,
                                  BigDecimal orgToAgentRate) {
        final int BATCH_SIZE = 500;
        List<AgentHotelLowestPriceEntity> batchList = new ArrayList<>(BATCH_SIZE);
        int orgCurrency = Integer.parseInt(orgDTO.getOrgCurrency());

        for (Map.Entry<Long, Map<String, List<SupplyHotelLowestPrice>>> hotelEntry : hotelLowestMap.entrySet()) {
            Long hotelId = hotelEntry.getKey();
            Map<String, List<SupplyHotelLowestPrice>> datePriceMap = hotelEntry.getValue();

            // 获取最低价格
            Optional<PriceResult> minPriceOpt = findMinPrice(datePriceMap, orgDTO, agentConfig,
                    supplyConfigMap, supplyIncreaseMap, supplyHotelIncreaseMap, rateMap, orgToAgentRate, orgCurrency);

            if (minPriceOpt.isPresent()) {
                PriceResult result = minPriceOpt.get();
                AgentHotelLowestPriceEntity entity = assembleEntity(agentCode, agentConfig.getSettlementCurrency(), hotelId, result);
                batchList.add(entity);

                // 批量提交
                if (batchList.size() >= BATCH_SIZE) {
                    submitBatch(batchList);
                    batchList.clear();
                }
            }
        }

        // 提交最后一批
        if (!batchList.isEmpty()) {
            submitBatch(batchList);
        }
    }

    /**
     * 获取最低价格
     */
    private Optional<PriceResult> findMinPrice(Map<String, List<SupplyHotelLowestPrice>> datePriceMap,
                                               OrgDTO orgDTO, AgentAccountConfig agentConfig,
                                               Map<String, SupplierAddDTO> supplyConfigMap,
                                               Map<String, SupplyIncrease> supplyIncreaseMap,
                                               Map<String, SupplyHotelIncrease> supplyHotelIncreaseMap,
                                               Map<String, BigDecimal> rateMap,
                                               BigDecimal orgToAgentRate,
                                               int orgCurrency) {
        return datePriceMap.values().stream()
                .flatMap(List::stream)
                .map(price -> calculatePrice(price, orgDTO, agentConfig,
                        supplyConfigMap, supplyIncreaseMap, supplyHotelIncreaseMap,
                        rateMap, orgToAgentRate, orgCurrency))
                .filter(Objects::nonNull)
                .min(Comparator.comparing(PriceResult::getFinalPrice));
    }

    /**
     * 计算价格
     */
    private PriceResult calculatePrice(SupplyHotelLowestPrice price,
                                       OrgDTO orgDTO, AgentAccountConfig agentConfig,
                                       Map<String, SupplierAddDTO> supplyConfigMap,
                                       Map<String, SupplyIncrease> supplyIncreaseMap,
                                       Map<String, SupplyHotelIncrease> supplyHotelIncreaseMap,
                                       Map<String, BigDecimal> rateMap,
                                       BigDecimal orgToAgentRate,
                                       int orgCurrency) {
        try {
            // 预付价格计算
            BigDecimal prepayPrice = calculatePrepayPrice(price, orgDTO, agentConfig,
                    supplyConfigMap, supplyIncreaseMap, supplyHotelIncreaseMap,
                    rateMap, orgToAgentRate, orgCurrency);


            // 现付价格计算
            BigDecimal cashPrice = calculateCashPrice(price, orgDTO, agentConfig,
                    rateMap, orgToAgentRate, orgCurrency);

            // 取最小值
            BigDecimal finalPrice = Stream.of(prepayPrice, cashPrice)
                    .filter(Objects::nonNull)
                    .min(BigDecimal::compareTo)
                    .orElse(null);

            return finalPrice != null ?
                    new PriceResult(finalPrice, price) : null;
        } catch (Exception e) {
            log.error("价格计算异常 hotelId:{} supply:{}", price.getHotelId(), price.getSupplyCode(), e);
            return null;
        }
    }

    /**
     * 计算预付价格（含加价逻辑）
     */
    private BigDecimal calculatePrepayPrice(SupplyHotelLowestPrice price, OrgDTO orgDTO,
                                            AgentAccountConfig agentConfig,
                                            Map<String, SupplierAddDTO> supplyConfigMap,
                                            Map<String, SupplyIncrease> supplyIncreaseMap,
                                            Map<String, SupplyHotelIncrease> supplyHotelIncreaseMap,
                                            Map<String, BigDecimal> rateMap,
                                            BigDecimal orgToAgentRate,
                                            int orgCurrency) {
        try {
            // 1. 获取原始价格和币种
            BigDecimal originPrice = price.getLowestPrice();
            Integer originCurrency = price.getLowestPriceCurreny();
            if (originPrice == null || originCurrency == null) return null;

            // 2. 转换到商家币种
            BigDecimal toOrgRate = getConversionRate(originCurrency, orgCurrency,
                    orgDTO.getOrgCode(), rateMap);
            if (toOrgRate == null) return null;

            // 3. 计算加价
            return queryUtil.calculateSalePrice(price, agentConfig, supplyConfigMap.get(price.getSupplyCode()),
                    supplyIncreaseMap.get(price.getSupplyCode()), supplyHotelIncreaseMap.get(price.getSupplyCode().concat(StrUtilX.SPLIT_CODE).concat(String.valueOf(price.getHotelId()))), toOrgRate, orgToAgentRate, orgCurrency, price.getLowestPriceCurreny());
        } catch (Exception e) {
            log.error("预付价格计算异常 hotelId={}", price.getHotelId(), e);
            return null;
        }
    }

    /**
     * 计算现付价格（直接汇率换算）
     */
    private BigDecimal calculateCashPrice(SupplyHotelLowestPrice price, OrgDTO orgDTO,
                                          AgentAccountConfig agentConfig,
                                          Map<String, BigDecimal> rateMap,
                                          BigDecimal orgToAgentRate,
                                          int orgCurrency) {
        try {
            // 1. 获取现付价格信息
            BigDecimal cashPrice = price.getCashPrice();
            Integer cashCurrency = price.getCashCurrency();
            if (cashPrice == null || cashCurrency == null) return null;

            // 2. 转换到商家币种
            BigDecimal toOrgRate = getConversionRate(cashCurrency, orgCurrency,
                    orgDTO.getOrgCode(), rateMap);
            if (toOrgRate == null) return null;

            // 3. 计算最终价格
            return queryUtil.calculateCashSalePrice(price.getCashPrice(), price.getCashCurrency(), price.getSupplyClass(), agentConfig, toOrgRate, orgToAgentRate, orgCurrency);
        } catch (Exception e) {
            log.error("现付价格计算异常 hotelId={}", price.getHotelId(), e);
            return null;
        }
    }

    /**
     * 获取汇率
     */
    private BigDecimal getConversionRate(int fromCurrency, int toCurrency,
                                         String orgCode,
                                         Map<String, BigDecimal> rateMap) {
        if (fromCurrency == toCurrency) return BigDecimal.ONE;

        String fromCode = SettlementCurrencyEnum.getCodeByKey(String.valueOf(fromCurrency));
        String toCode = SettlementCurrencyEnum.getCodeByKey(String.valueOf(toCurrency));
        String rateKey = String.format("%s_%s_%s", orgCode, fromCode, toCode);

        BigDecimal rate = rateMap.get(rateKey);
        if (rate == null) {
            log.warn("关键汇率不存在 key={}", rateKey);
            return null;
        }
        return rate;
    }

    /**
     * 组装实体
     */
    private AgentHotelLowestPriceEntity assembleEntity(String agentCode, Integer saleCurrency, Long hotelId, PriceResult result) {
        SupplyHotelLowestPrice sourcePrice = result.getSourcePrice();

        AgentHotelLowestPriceEntity entity = new AgentHotelLowestPriceEntity();
        entity.setAgentCode(agentCode);
        entity.setCityCode(sourcePrice.getCityCode());
        entity.setHotelId(hotelId);
        entity.setCreatedBy(Constant.SYSTEM);
        entity.setCreatedDt(new Date());
        entity.setDeleted(0);
        entity.setLowestPrice(result.getFinalPrice());
        entity.setSaleCurrency(saleCurrency);
        entity.setPayAtHotelFee(sourcePrice.getPayInStorePrice());
        entity.setPayAtHotelCurrency(sourcePrice.getLowestPriceCurreny());
        return entity;
    }

    /**
     * 批次提交方法
     */
    private void submitBatch(List<AgentHotelLowestPriceEntity> batch) {
        try {
            // 同步写入MySQL（分库分表）
//            productMapper.batchInsertOrUpdateAHotel(batch, getXTableName(agentCode, false));

            // 异步更新MongoDB
            List<LowestPrice> mongoPrices = convertToMongoDTO(batch);
            CompletableFuture.runAsync(() -> hotelRemote.updateHotelMongodbByHotelId(HotelPublicDTO.builder().lowestPriceDTOList(mongoPrices).build()), processingProductInfoExecutor).exceptionally(e -> {
                log.error("MongoDB异步更新失败 batchSize:{}", batch.size(), e);
                return null;
            });
        } catch (Exception e) {
            log.error("批次提交异常", e);
        } finally {
            // 强制释放资源
            batch.clear();
        }
    }

    /**
     * 将实体列表转换为MongoDB的LowestPrice对象列表
     */
    private List<LowestPrice> convertToMongoDTO(List<AgentHotelLowestPriceEntity> batch) {
        return batch.stream()
                .map(entity -> {
                    LowestPrice lowestPrice = new LowestPrice();
                    lowestPrice.setAgentCode(entity.getAgentCode());
                    lowestPrice.setHotelId(entity.getHotelId());
                    if (entity.getLowestPrice() != null) {
                        lowestPrice.setLowestPrice(entity.getLowestPrice().doubleValue());
                    }
                    lowestPrice.setSaleCurrency(entity.getSaleCurrency());
                    if (entity.getPayAtHotelFee() != null) {
                        lowestPrice.setPayAtHotelFee(entity.getPayAtHotelFee().doubleValue());
                    }
                    lowestPrice.setPayAtHotelCurrency(entity.getPayAtHotelCurrency());
                    return lowestPrice;
                })
                .collect(Collectors.toList());
    }

    /**
     * 获取客户信息缓存
     */
    public AgentAccountConfig getCachedAgentConfig(String agentCode) {
        Object config = RedisTemplateX.hashGet(RedisKey.AGENT_ACCOUNT_CONFIG, agentCode);
        return JSON.parseObject(config.toString(), AgentAccountConfig.class);
    }

    @Override
    public void disposeAgentAvailableHotel(String agentCode, List<String> supplyCodes) {
        // 批量获取供应商配置
        Map<String, SupplierAddDTO> supplyConfigMap = queryUtil.getSupplierConfig(supplyCodes);
        if (supplyConfigMap.isEmpty()) {
            return;
        }

        // 筛选出单体酒店
        List<String> monoHotelIds = new ArrayList<>();
        Iterator<Map.Entry<String, SupplierAddDTO>> iterator = supplyConfigMap.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, SupplierAddDTO> entry = iterator.next();
            SupplierAddDTO supplierAddDTO = entry.getValue();

            // 判断是否满足条件（supplierForm 为单体酒店）
            if (supplierAddDTO.getSupplierForm() != null
                    && supplierAddDTO.getSupplierForm().equals(SupplierFormEnum.MONO_HOTEL.getKey())) {
                // 添加到 monoHotelIds 列表
                monoHotelIds.add(String.valueOf(supplierAddDTO.getHotelId()));
                // 从 map 中移除
                iterator.remove();
            }
        }

        // 1、处理单体酒店
        if (CollUtilX.isNotEmpty(monoHotelIds)) {
            // 构建查询条件
            Criteria criteria = Criteria.where("merchantSource").is("TIANGONG")
                    .and("merchantCode").is(CompanyDTO.COMPANY_CODE)
                    .and("hotelId").in(monoHotelIds)
                    .and("saleDate").gte(DateUtilX.dateToString(DateUtilX.getCurrentDate()))
                    .lte(DateUtilX.dateToString(DateUtilX.getAddDay(DateUtilX.getCurrentDate(), 29)));

            Query query = new Query();
            query.addCriteria(criteria);
            List<String> hotelIds = mongoTemplate.findDistinct(query, "hotelId", SupplyHotelLowestPrice.class, String.class);

            // 存在可售酒店
            if (CollUtilX.isNotEmpty(hotelIds)) {
                RedisTemplateX.lLeftPush(RedisKey.AVAILABLE_AGENT_LIST, agentCode);
                RedisTemplateX.lLeftPushAll(RedisKey.AVAILABLE_AGENT_HOTEL_LIST + agentCode, hotelIds.toArray(new String[0]));
            }
        }

        // 2、处理非单体酒店
        if (supplyConfigMap.isEmpty()) {
            return;
        }
        int pageSize = 1000;
        int pageNumber = 0;

        // 构建查询条件
        Criteria criteria = Criteria.where("merchantSource").is(CompanyDTO.COMPANY_NAME)
                .and("merchantCode").is(CompanyDTO.COMPANY_CODE)
                .and("supplyCode").in(supplyConfigMap.keySet())
                .and("saleDate").gte(DateUtilX.dateToString(DateUtilX.getCurrentDate()))
                .lte(DateUtilX.dateToString(DateUtilX.getAddDay(DateUtilX.getCurrentDate(), 29)));

        // 获取可售酒店id
        while (true) {
            Aggregation aggregation = Aggregation.newAggregation(
                    Aggregation.match(criteria),
                    Aggregation.group("hotelId"), // 按 hotelId 分组
                    Aggregation.skip((long) pageNumber * pageSize),
                    Aggregation.limit(pageSize)
            );

            List<HotelIdDto> result = mongoTemplate.aggregate(aggregation, SupplyHotelLowestPrice.class, HotelIdDto.class).getMappedResults();
            if (CollUtil.isEmpty(result)) break;

            List<String> hotelIds = result.stream()
                    .map(hotel -> hotel.get_id().toString())
                    .collect(Collectors.toList());

            RedisTemplateX.lLeftPush(RedisKey.AVAILABLE_AGENT_LIST, agentCode);
            RedisTemplateX.lLeftPushAll(RedisKey.AVAILABLE_AGENT_HOTEL_LIST + agentCode, hotelIds.toArray(new String[0]));

            // 提前释放内存
            hotelIds.clear();

            pageNumber++;
        }
    }

    @Override
    public void deleteAgentNotAvailHotel(String xTable, String date) {
        productMapper.deleteAgentNotAvailHotel(xTable, date);
    }

    @Override
    public void calculateHotelHeatBookableScoreTask(String param) {
        List<String> hotelIds;
        if (StrUtilX.isNotEmpty(param)) {
            hotelIds = StrUtilX.stringToList(param, ",");
        } else {
            if (!RedisTemplateX.hasKey(RedisKey.CALCULATE_BOOKABLE_SCORE_HOTEL_ID_KEY)) {
                return;
            }
            hotelIds = RedisTemplateX.setPopCount(RedisKey.CALCULATE_BOOKABLE_SCORE_HOTEL_ID_KEY, settingsConstant.getConsumerCalculateBookableScoreCount());
        }
        if (CollUtilX.isEmpty(hotelIds)) {
            return;
        }
        try {
            // 创建Criteria对象来定义查询条件
            String startDate;
            String endDate;
            if (settingsConstant.getConsumerCalculateBookableScoreDay() != null) {
                startDate = DateUtilX.dateToString(DateUtilX.getAddDay(DateUtilX.getCurrentDate(), 0));
                endDate = DateUtilX.dateToString(DateUtilX.getAddDay(DateUtilX.getCurrentDate(), settingsConstant.getConsumerCalculateBookableScoreDay() - 1));
            } else {
                startDate = DateUtilX.dateToString(DateUtilX.getAddDay(DateUtilX.getCurrentDate(), 0));
                endDate = DateUtilX.dateToString(DateUtilX.getAddDay(DateUtilX.getCurrentDate(), 1));
            }
            Criteria criteria = Criteria.where("merchantSource").is(CompanyDTO.COMPANY_NAME).and("hotelId").in(hotelIds)
                    .and("saleDate").gte(startDate).lte(endDate);
            // 构建聚合查询
//            Aggregation aggregation = Aggregation.newAggregation(
//                    Aggregation.match(criteria),
//                    Aggregation.group("hotelId"),
//                    Aggregation.project("hotelId").andExclude("hotelId")
//            );

            // 查询起价酒店
//            AggregationResults<SupplyHotelLowestPrice> results = mongoTemplate.aggregate(aggregation, "supply_hotel_lowest_price", SupplyHotelLowestPrice.class);
//            List<String> hotelIdList = results.getMappedResults().stream().map(SupplyHotelLowestPrice::getId).collect(Collectors.toList());
            // 2025-01-14 使用mongodb的distinct 替代 group 查询 获取酒店id, 性能比group聚合查询快
            Query query = new Query();
            query.addCriteria(criteria);
            List<String> hotelIdList = mongoTemplate.findDistinct(query, "hotelId", SupplyHotelLowestPrice.class, String.class);

            List<String> hotelIdList2;
            if (CollUtilX.isNotEmpty(hotelIdList)) {
                // 创建Criteria对象来定义查询条件
                Criteria criteria1 = Criteria.where("merchantSource").is(CompanyDTO.COMPANY_NAME).and("hotelId").in(hotelIds).and("updateDt").gte(DateUtilX.dateToString(DateUtilX.getAddDay(DateUtilX.getCurrentDate(), -2), hour_format))
                        .and("saleDate").gte(startDate).lte(endDate);
//                // 构建聚合查询
//                Aggregation aggregation1 = Aggregation.newAggregation(
//                        Aggregation.match(criteria1),
//                        Aggregation.group("hotelId"),
//                        Aggregation.project("hotelId").andExclude("hotelId")
//                );

                // 查询起价酒店
//                AggregationResults<SupplyHotelLowestPrice> results2 = mongoTemplate.aggregate(aggregation1, "supply_hotel_lowest_price", SupplyHotelLowestPrice.class);
//                hotelIdList2 = results2.getMappedResults().stream().map(SupplyHotelLowestPrice::getId).collect(Collectors.toList());
                Query query1 = new Query();
                query1.addCriteria(criteria1);
                hotelIdList2 = mongoTemplate.findDistinct(query, "hotelId", SupplyHotelLowestPrice.class, String.class);

            } else {
                hotelIdList2 = null;
            }

            Date currentDate = DateUtilX.getCurrentDate();
            List<HotelHeatReq> reqList = hotelIds.stream().map(item -> {
                HotelHeatReq hotelHeatReq = new HotelHeatReq();
                hotelHeatReq.setHotelId(Long.parseLong(item));
                // 起价日期更新时间小于48小时3000W，存在起价2000W，否则1000W
                if (CollUtilX.isNotEmpty(hotelIdList2) && hotelIdList2.contains(item)) {
                    hotelHeatReq.setBookableScore(30000000L);
                } else if (CollUtilX.isNotEmpty(hotelIdList) && hotelIdList.contains(item)) {
                    hotelHeatReq.setBookableScore(20000000L);
                } else {
                    hotelHeatReq.setBookableScore(10000000L);
                }
                hotelHeatReq.setUpdatedBy(Constant.SYSTEM);
                hotelHeatReq.setUpdatedDt(currentDate);
                return hotelHeatReq;
            }).collect(Collectors.toList());

            // 修改热度分数
            hotelHeatRemote.updateHotelHeatScore(reqList);
        } catch (Exception e) {
            log.error("计算酒店热度可订分数任务异常", e);
            // 异常重新插入到缓存
            String[] ids = hotelIds.toArray(new String[0]);
            RedisTemplateX.setAdd(RedisKey.CALCULATE_BOOKABLE_SCORE_HOTEL_ID_KEY, ids);
        }
    }

    @Override
    public void calculateHotelHeatHotelAvgPriceTask(String param) {
        List<String> hotelIds;
        if (StrUtilX.isNotEmpty(param)) {
            hotelIds = StrUtilX.stringToList(param, ",");
            if (CollUtilX.isNotEmpty(hotelIds)) {
                // 清除旧的数据
                HotelAvgPriceReq hotelAvgPriceReq = new HotelAvgPriceReq();
                hotelAvgPriceReq.setHotelIds(hotelIds);
                hotelHeatRemote.delHotelAvgPrice(hotelAvgPriceReq);
            }
        } else {
            if (!RedisTemplateX.hasKey(RedisKey.CALCULATE_HOTEL_AVG_PRICE_HOTEL_ID_KEY)) {
                return;
            }
            hotelIds = RedisTemplateX.setPopCount(RedisKey.CALCULATE_HOTEL_AVG_PRICE_HOTEL_ID_KEY, settingsConstant.getConsumerCalculateHotelAvgPriceScoreCount());
        }
        if (CollUtilX.isEmpty(hotelIds)) {
            return;
        }

        try {
            // 根据酒店id、售卖日期查询起价
            String startDate;
            String endDate;
            if (settingsConstant.getConsumerCalculateHotelAvgPriceScoreDay() != null) {
                startDate = DateUtilX.dateToString(DateUtilX.getAddDay(DateUtilX.getCurrentDate(), 0));
                endDate = DateUtilX.dateToString(DateUtilX.getAddDay(DateUtilX.getCurrentDate(), settingsConstant.getConsumerCalculateHotelAvgPriceScoreDay() - 1));
            } else {
                startDate = DateUtilX.dateToString(DateUtilX.getAddDay(DateUtilX.getCurrentDate(), 0));
                endDate = DateUtilX.dateToString(DateUtilX.getAddDay(DateUtilX.getCurrentDate(), 5));
            }
            Criteria criteria = new Criteria();
            criteria.andOperator(
                    Criteria.where("merchantSource").is(CompanyDTO.COMPANY_NAME),
                    Criteria.where("hotelId").in(hotelIds),
                    Criteria.where("saleDate").gte(startDate).lte(endDate),
                    Criteria.where("cityCode").ne(null)
            );
            Query query = new Query(criteria);
            List<SupplyHotelLowestPrice> supplyHotelLowestPriceList = mongoTemplate.find(query, SupplyHotelLowestPrice.class);
            if (CollUtilX.isEmpty(supplyHotelLowestPriceList)) {
                return;
            }

            // 获取商家币种，判断是否存在商家转客户
            OrgDTO orgDTO = CommonInitializer.getOrgInfo();
            if (StrUtilX.isEmpty(orgDTO.getOrgCode()) || StrUtilX.isEmpty(orgDTO.getOrgCurrency())) {
                return;
            }
            // 币种不存在不计算
            String orgCurrency = SettlementCurrencyEnum.getCodeByKey(String.valueOf(orgDTO.getOrgCurrency()));
            if (StrUtilX.isEmpty(orgCurrency)) {
                return;
            }
            // 获取所有汇率
            Map<String, BigDecimal> rateMap = RedisUtil.queryAllRate();

            // 酒店最低价格
            Map<Long, Map<String, BigDecimal>> hotelPriceMap = new HashMap<>();
            // 酒店城市
            Map<Long, HotelCityReq> hotelCityMap = new HashMap<>();
            for (SupplyHotelLowestPrice supplyHotelLowestPrice : supplyHotelLowestPriceList) {
                // 城市编码不能为空
                if (StrUtilX.isEmpty(supplyHotelLowestPrice.getCityCode())) {
                    continue;
                }
                // 存放酒店城市
                if (!hotelCityMap.containsKey(supplyHotelLowestPrice.getHotelId())) {
                    HotelCityReq hotelCityReq = new HotelCityReq();
                    hotelCityReq.setCityCode(supplyHotelLowestPrice.getCityCode());
                    hotelCityReq.setCountryCode(supplyHotelLowestPrice.getCountryCode());
                    hotelCityMap.put(supplyHotelLowestPrice.getHotelId(), hotelCityReq);
                }
                if (Objects.nonNull(supplyHotelLowestPrice.getLowestPrice()) && Objects.nonNull(supplyHotelLowestPrice.getLowestPriceCurreny())) {
                    BigDecimal rate;
                    // 判断币种是否一致
                    if (supplyHotelLowestPrice.getLowestPriceCurreny().equals(Integer.parseInt(orgDTO.getOrgCurrency()))) {
                        rate = BigDecimal.ONE;
                    } else {
                        // 组装key
                        String supplyToOrgKey = orgDTO.getOrgCode() + "_" + SettlementCurrencyEnum.getCodeByKey(String.valueOf(supplyHotelLowestPrice.getLowestPriceCurreny())) + "_" + orgCurrency;
                        // 查供应商转商家汇率
                        rate = rateMap.get(supplyToOrgKey);
                    }

                    if (Objects.nonNull(rate)) {
                        BigDecimal newPrice = CommonTgUtils.setScale(CommonTgUtils.setRateAmt(supplyHotelLowestPrice.getLowestPrice(), rate), 2, BigDecimal.ROUND_UP);
                        // 计算现付价-只换算汇率
                        if (Objects.nonNull(supplyHotelLowestPrice.getCashPrice()) && Objects.nonNull(supplyHotelLowestPrice.getCashCurrency())) {
                            BigDecimal cashRate;
                            // 判断币种是否一致
                            if (supplyHotelLowestPrice.getCashCurrency().equals(Integer.parseInt(orgDTO.getOrgCurrency()))) {
                                cashRate = BigDecimal.ONE;
                            } else {
                                // 组装key
                                String supplyToOrgCashKey = orgDTO.getOrgCode() + "_" + SettlementCurrencyEnum.getCodeByKey(String.valueOf(supplyHotelLowestPrice.getCashCurrency())) + "_" + orgCurrency;
                                // 查供应商转商家现付汇率
                                cashRate = rateMap.get(supplyToOrgCashKey);
                            }

                            // 比较预付价和现付价
                            if (Objects.nonNull(cashRate)) {
                                BigDecimal newCashPrice = CommonTgUtils.setScale(CommonTgUtils.setRateAmt(supplyHotelLowestPrice.getCashPrice(), rate), 2, BigDecimal.ROUND_UP);
                                if (Objects.nonNull(newCashPrice) && (Objects.isNull(newPrice) || newCashPrice.compareTo(newPrice) < 0)) {
                                    newPrice = newCashPrice;
                                }
                            }
                        }

                        // 判断数据是否存在
                        if (hotelPriceMap.containsKey(supplyHotelLowestPrice.getHotelId()) && hotelPriceMap.get(supplyHotelLowestPrice.getHotelId()).get(supplyHotelLowestPrice.getSaleDate()) != null) {
                            // 判断金额是否小于
                            if (newPrice.compareTo(hotelPriceMap.get(supplyHotelLowestPrice.getHotelId()).get(supplyHotelLowestPrice.getSaleDate())) < 0) {
                                hotelPriceMap.get(supplyHotelLowestPrice.getHotelId()).put(supplyHotelLowestPrice.getSaleDate(), newPrice);
                            }
                        } else {
                            if (!hotelPriceMap.containsKey(supplyHotelLowestPrice.getHotelId())) {
                                Map<String, BigDecimal> priceMap = new HashMap<>();
                                priceMap.put(supplyHotelLowestPrice.getSaleDate(), newPrice);
                                hotelPriceMap.put(supplyHotelLowestPrice.getHotelId(), priceMap);
                            } else {
                                hotelPriceMap.get(supplyHotelLowestPrice.getHotelId()).put(supplyHotelLowestPrice.getSaleDate(), newPrice);
                            }
                        }
                    }
                } else if (Objects.nonNull(supplyHotelLowestPrice.getCashPrice()) && Objects.nonNull(supplyHotelLowestPrice.getCashCurrency())) {
                    BigDecimal cashRate;
                    // 判断币种是否一致
                    if (supplyHotelLowestPrice.getCashCurrency().equals(Integer.parseInt(orgDTO.getOrgCurrency()))) {
                        cashRate = BigDecimal.ONE;
                    } else {
                        // 组装key
                        String supplyToOrgCashKey = orgDTO.getOrgCode() + "_" + SettlementCurrencyEnum.getCodeByKey(String.valueOf(supplyHotelLowestPrice.getCashCurrency())) + "_" + orgCurrency;
                        // 查供应商转商家现付汇率
                        cashRate = rateMap.get(supplyToOrgCashKey);
                    }

                    // 比较预付价和现付价
                    if (Objects.nonNull(cashRate)) {
                        BigDecimal newCashPrice = CommonTgUtils.setScale(CommonTgUtils.setRateAmt(supplyHotelLowestPrice.getCashPrice(), cashRate), 2, BigDecimal.ROUND_UP);

                        // 判断数据是否存在
                        if (hotelPriceMap.containsKey(supplyHotelLowestPrice.getHotelId()) && hotelPriceMap.get(supplyHotelLowestPrice.getHotelId()).get(supplyHotelLowestPrice.getSaleDate()) != null) {
                            // 判断金额是否小于
                            if (newCashPrice.compareTo(hotelPriceMap.get(supplyHotelLowestPrice.getHotelId()).get(supplyHotelLowestPrice.getSaleDate())) < 0) {
                                hotelPriceMap.get(supplyHotelLowestPrice.getHotelId()).put(supplyHotelLowestPrice.getSaleDate(), newCashPrice);
                            }
                        } else {
                            if (!hotelPriceMap.containsKey(supplyHotelLowestPrice.getHotelId())) {
                                Map<String, BigDecimal> priceMap = new HashMap<>();
                                priceMap.put(supplyHotelLowestPrice.getSaleDate(), newCashPrice);
                                hotelPriceMap.put(supplyHotelLowestPrice.getHotelId(), priceMap);
                            } else {
                                hotelPriceMap.get(supplyHotelLowestPrice.getHotelId()).put(supplyHotelLowestPrice.getSaleDate(), newCashPrice);
                            }
                        }
                    }
                }
            }
            // 计算起价完毕，手动清空集合
            supplyHotelLowestPriceList = null;

            if (!hotelPriceMap.isEmpty()) {
                Date currentDate = DateUtilX.getCurrentDate();
                List<HotelAvgPriceReq> collect = hotelPriceMap.entrySet().stream().filter(item -> hotelCityMap.containsKey(item.getKey())
                        && StrUtilX.isNotEmpty(hotelCityMap.get(item.getKey()).getCityCode())).map(item -> {
                    BigDecimal priceSum = BigDecimal.ZERO;
                    for (BigDecimal price : item.getValue().values()) {
                        priceSum = priceSum.add(price);
                    }
                    BigDecimal avgPrice = priceSum.divide(new BigDecimal(item.getValue().size()), 2, RoundingMode.UP);
                    HotelAvgPriceReq hotelAvgPriceReq = new HotelAvgPriceReq();
                    hotelAvgPriceReq.setHotelId(item.getKey());
                    HotelCityReq hotelCityReq = hotelCityMap.get(item.getKey());
                    hotelAvgPriceReq.setCityCode(hotelCityReq.getCityCode());
                    hotelAvgPriceReq.setCountryCode(hotelCityReq.getCountryCode());
                    hotelAvgPriceReq.setAvgPrice(avgPrice);
                    hotelAvgPriceReq.setCreatedBy(Constant.SYSTEM);
                    hotelAvgPriceReq.setCreatedDt(currentDate);
                    return hotelAvgPriceReq;
                }).collect(Collectors.toList());

                // 批量保存酒店平均价
                hotelHeatRemote.batchSaveHotelAvgPrice(collect);

                // 清空集合
                hotelPriceMap.clear();
            }
        } catch (Exception e) {
            log.error("计算酒店热度酒店平均房价任务异常", e);
            // 异常重新插入到缓存
            String[] ids = hotelIds.toArray(new String[0]);
            RedisTemplateX.setAdd(RedisKey.CALCULATE_HOTEL_AVG_PRICE_HOTEL_ID_KEY, ids);
        } finally {
            // 判断是否是最后一次执行
            if (StrUtilX.isEmpty(param) && !RedisTemplateX.hasKey(RedisKey.CALCULATE_HOTEL_AVG_PRICE_HOTEL_ID_KEY) && RedisTemplateX.setSize(RedisKey.CALCULATE_HOTEL_AVG_PRICE_HOTEL_ID_KEY) < 1) {
                // 异步执行，初始化需要计算酒店热度城市平均房价的城市编码到缓存
                CompletableFuture.runAsync(this::initCalculateCityAvgPriceCityCodeToRedis);
            }
        }
    }

    /**
     * 初始化需要计算酒店热度城市平均房价的城市编码到缓存
     */
    private void initCalculateCityAvgPriceCityCodeToRedis() {
        // 查询城市列表
        Response<List<String>> response = hotelHeatRemote.queryHotelAvgPriceCityCodes();
        if (response.isSuccess()) {
            if (CollUtilX.isNotEmpty(response.getModel())) {
                String[] cityCodes = response.getModel().toArray(new String[0]);
                RedisTemplateX.setAdd(RedisKey.CALCULATE_CITY_AVG_PRICE_CITY_CODE_KEY, cityCodes);
            }
            // 计算数据之前删除旧缓存数据
            RedisTemplateX.delete(RedisKey.HOTEL_HEAT_CITY_AVG_PRICE_KEY);
        } else {
            log.error("初始化需要计算酒店热度城市平均房价的城市编码到缓存失败，调用common服务查询酒店平均价城市列表失败，resp={}", JSONUtil.toJsonStr(response));
        }
    }

    @Override
    public void initCalculateSelfHotelLowestPriceTask(String param) {
        try {
            // 配置参数
            final int pageSize = 100;
            long lastId = 0;

            // 分页循环
            while (true) {
                // 分页查询
                HotelSaleInfoReq req = new HotelSaleInfoReq();
                req.setLastId(lastId);
                req.setBatchSize(pageSize);
                if (StrUtilX.isNotEmpty(param)) {
                    req.setHotelId(Long.parseLong(param));
                }
                List<HotelSaleInfoItemResp> respList = productMapper.selectHotelSaleInfoList(req);

                if (CollUtilX.isEmpty(respList)) break;

                // 提交处理
                processData(respList);

                // 更新游标
                lastId = respList.get(respList.size() - 1).getSaleInfoId();
            }
        } catch (Exception e) {
            log.error("初始化计算自签酒店起价任务，处理数据异常", e);
        }
    }

    /**
     * 处理数据
     */
    private void processData(List<HotelSaleInfoItemResp> respList) {
        // 提交队列处理
        RedisTemplateX.convertAndSend(RedisKey.CALCULATE_SELF_HOTEL_LOWEST, JSONUtil.toJsonStr(HotelSaleInfoResp.builder()
                .logId(UUID.randomUUID().toString())
                .respList(respList)
                .build()));
    }

    @Override
    public void calculateSelfHotelLowestPrice(List<HotelSaleInfoItemResp> respList) {
        if (CollUtilX.isEmpty(respList)) {
            return;
        }
        // 获取供应商编码
        List<String> supplyCodes = new ArrayList<>();
        Set<Long> hotelIds = new HashSet<>();
        for (HotelSaleInfoItemResp resp : respList) {
            if (!supplyCodes.contains(resp.getSupplierCode())) {
                supplyCodes.add(resp.getSupplierCode());
            }
            hotelIds.add(resp.getHotelId());
        }

        // 获取供应商信息
        List<SupplierAddDTO> suppliers = getSuppliersFromCache(supplyCodes);
        if (CollUtilX.isEmpty(suppliers)) {
            return;
        }
        Map<String, SupplierAddDTO> supplierMap = new HashMap<>(suppliers.size());
        for (SupplierAddDTO supplier : suppliers) {
            supplierMap.put(supplier.getSupplierCode(), supplier);
        }

        // 获取酒店信息
        DestinationReq destinationReq = new DestinationReq();
        destinationReq.setHotelIds(hotelIds);
        Response<List<EsHotelDto>> esHotelDTOResponse = hotelRemote.searchDestinationHotel2(destinationReq);
        if (esHotelDTOResponse.isError() || CollUtil.isEmpty(esHotelDTOResponse.getModel())) {
            throw new SysException(ErrorCodeEnum.NOT_EXIST_HOTEL);
        }
        Map<Long, EsHotelDto> hotelMap = new HashMap<>();
        for (EsHotelDto esHotelDto : esHotelDTOResponse.getModel()) {
            hotelMap.put(esHotelDto.getHotelId(), esHotelDto);
        }

        // 获取商家信息
        OrgDTO orgInfo = CommonInitializer.getOrgInfo();
        int orgCurrency = Integer.parseInt(orgInfo.getOrgCurrency());

        // 需要查询价格的日期
        List<Date> dateList = DateUtilX.getDateList(DateUtilX.getCurrentDate(), DateUtilX.getDate(DateUtilX.getCurrentDate(), settingsConstant.getCalculateSelfHotelLowestPriceDay(), 0));

        List<SupplyHotelLowestPriceReq> lowestPriceReqList = new ArrayList<>();
        // 当前时间
        LocalDateTime currentDate = LocalDateTime.now();
        for (HotelSaleInfoItemResp resp : respList) {
            try {
                SupplierAddDTO supplierAddDTO = supplierMap.get(resp.getSupplierCode());
                if (supplierAddDTO == null) {
                    continue;
                }
                // 传了日期按实际日期计算
                if (StrUtilX.isNotEmpty(resp.getStartDate()) && StrUtilX.isNotEmpty(resp.getEndDate())) {
                    dateList = DateUtilX.getDateList(DateUtilX.stringToDate(resp.getStartDate()), DateUtilX.stringToDate(resp.getEndDate()));
                }
                // 查询酒店产品id
                QueryProductIdsRequest queryProductIdsRequest = new QueryProductIdsRequest();
                queryProductIdsRequest.setHotelId(String.valueOf(resp.getHotelId()));
                queryProductIdsRequest.setCompanyCode(CompanyDTO.COMPANY_CODE);
                queryProductIdsRequest.setSupplyCodes(Collections.singletonList(resp.getSupplierCode()));
                // 国内 含税价 全球 不含税价格
                queryProductIdsRequest.setPriceType(supplierAddDTO.getDomesticOrOverseas() == 1 ? 0 : null);
                List<HotelProductIdsResponse> hotelProductIdsResponses = productInfoService.queryProductIdsByHotelId(queryProductIdsRequest);
                if (CollUtilX.isEmpty(hotelProductIdsResponses)) {
                    // 停售了并且没有产品删除起价
                    if (resp.getSaleStatus() != null && resp.getSaleStatus() == 0) {
                        rocketMQTemplate.syncSend(MqConstants.HOTEL_LOWEST_PRICE_TOPIC + ":" + MqConstants.HOTEL_LOWEST_PRICE_DELETE_TAG, SupplyHotelLowestPriceDelReq.builder()
                                .merchantSource(CompanyDTO.COMPANY_NAME)
                                .merchantCode(CompanyDTO.COMPANY_CODE)
                                .supplyClass(SupplyClassEnum.ZIQIAN.getCode())
                                .supplyCodes(Collections.singletonList(resp.getSupplierCode()))
                                .hotelId(String.valueOf(resp.getHotelId()))
                                .build());
                    }
                    continue;
                }
                // 提取所有产品id
                List<String> productIds = hotelProductIdsResponses.stream().map(HotelProductIdsResponse::getProductId).collect(Collectors.toList());

                // 构建集合key（productId_saleDate）
                List<String> productAndSaleDates = Lists.newArrayList();
                List<Date> finalDateList = dateList;
                productIds.forEach(productId -> finalDateList.forEach(saleDate -> productAndSaleDates.add(productId.concat(Constant.SEPARATOR).concat(DateUtilX.dateToString(saleDate)))));

                // 产品基础信息
                Map<String, ProductDTO> productMap = getProductMap(productIds);
                if (productMap.isEmpty()) {
                    continue;
                }

                // 产品id和税费规则关系
                Map<String, String> productIdAndTaxRuleConfigIdMap = new HashMap<>();
                for (String key : productMap.keySet()) {
                    ProductDTO productDTO = productMap.get(key);
                    // 存在税费规则并且 价格类型为不含税价格
                    if (productDTO != null
                            && (productDTO.getPriceType() != null && productDTO.getPriceType() == 1)
                            && productDTO.getTaxRuleConfigId() != null) {
                        productIdAndTaxRuleConfigIdMap.put(String.valueOf(productDTO.getProductId()), String.valueOf(productDTO.getTaxRuleConfigId()));
                    }
                }

                // 获取产品税费规则
                Map<String, ProTaxRuleConfigDTO> taxRuleMap = new HashMap<>();
                if (!productIdAndTaxRuleConfigIdMap.isEmpty()) {
                    List<ProTaxRuleConfigDTO> proTaxRuleConfigs = StrUtilX.parseObject(RedisTemplateX.hMultiGet(RedisKey.proTaxRuleConfigKey, new ArrayList<>(productIdAndTaxRuleConfigIdMap.values())), new TypeReference<List<ProTaxRuleConfigDTO>>() {
                    });
                    if (CollUtilX.isNotEmpty(proTaxRuleConfigs)) {
                        for (ProTaxRuleConfigDTO proTaxRuleConfig : proTaxRuleConfigs) {
                            taxRuleMap.put(String.valueOf(proTaxRuleConfig.getId()), proTaxRuleConfig);
                        }
                    }
                }

                // 获取产品每日价格
                List<Object> objects = RedisTemplateX.hMultiGet(RedisKey.productBasePriceAndRoomStatusKey, productAndSaleDates);
                if (CollUtilX.isEmpty(objects)) {
                    continue;
                }
                List<ProductBasePriceAndRoomStatusDTO> productBasePriceAndRoomStatusDTOS = StrUtilX.parseObject(objects, new TypeReference<List<ProductBasePriceAndRoomStatusDTO>>() {
                });
                if (CollUtilX.isEmpty(productBasePriceAndRoomStatusDTOS)) {
                    continue;
                }

                // 获取产品每日加幅
                List<String> salePriceSaleKeys = productAndSaleDates.stream().map(e -> CompanyDTO.COMPANY_CODE.concat(Constant.SEPARATOR).concat(e)).collect(Collectors.toList());
                List<Object> objectslist = RedisTemplateX.hMultiGet(RedisKey.productSalePriceKey, salePriceSaleKeys);
                if (CollUtilX.isEmpty(objectslist)) {
                    continue;
                }
                List<ProductSaleIncreaseDTO> productSaleIncreaseDTOS = StrUtilX.parseObject(objectslist, new TypeReference<List<ProductSaleIncreaseDTO>>() {
                });
                if (CollUtilX.isEmpty(productSaleIncreaseDTOS)) {
                    continue;
                }
                Map<String, ProductSaleIncreaseDTO> saleIncreaseMap = new HashMap<>();
                for (ProductSaleIncreaseDTO dto : productSaleIncreaseDTOS) {
                    if (dto != null && StrUtilX.isNotEmpty(dto.getProductId()) && StrUtilX.isNotEmpty(dto.getSaleDate())) {
                        // 创建键，使用产品ID与销售日期的组合
                        String key = String.valueOf(dto.getProductId()).concat(Constant.SEPARATOR).concat(dto.getSaleDate());
                        // 将 dto 放入 map 中，如果存在重复键，则覆盖值
                        saleIncreaseMap.put(key, dto);
                    }
                }
                if (saleIncreaseMap.isEmpty()) {
                    continue;
                }

                // 每个产品 按次税费
                Map<Integer, RoomTaxDetail> roomTaxDetailMap = new HashMap<>();
                Map<String, SelfHotelLowestReq> selfHotelLowestReqMap = new HashMap<>();
                for (ProductBasePriceAndRoomStatusDTO dto : productBasePriceAndRoomStatusDTOS) {
                    if (dto == null) {
                        continue;
                    }
                    ProductDTO productDTO = productMap.get(String.valueOf(dto.getProductId()));
                    if (productDTO == null) {
                        continue;
                    }
                    SelfHotelLowestReq priceDto = calculateLowestPrice(saleIncreaseMap, dto, productDTO, productIdAndTaxRuleConfigIdMap, taxRuleMap, roomTaxDetailMap, orgCurrency);
                    if (priceDto == null) {
                        continue;
                    }
                    SelfHotelLowestReq selfHotelLowestReq = selfHotelLowestReqMap.get(priceDto.getSaleDate());
                    if (selfHotelLowestReq == null || selfHotelLowestReq.getTotalPrice() == null || selfHotelLowestReq.getTotalPrice().compareTo(priceDto.getTotalPrice()) > 0) {
                        selfHotelLowestReqMap.put(priceDto.getSaleDate(), priceDto);
                    }
                }
                // 构建起价对象
                if (!selfHotelLowestReqMap.isEmpty()) {
                    EsHotelDto esHotelDto = hotelMap.get(resp.getHotelId());
                    for (SelfHotelLowestReq selfHotelLowestReq : selfHotelLowestReqMap.values()) {
                        lowestPriceReqList.add(SupplyHotelLowestPriceReq.builder()
                                .saleDate(LocalDate.parse(selfHotelLowestReq.getSaleDate()))
                                .lowestPrice(selfHotelLowestReq.getPayMethod() != null && selfHotelLowestReq.getPayMethod().equals(PayMethodEnum.PAY.key) ? null : selfHotelLowestReq.getTotalPrice())
                                .lowestPriceCurreny(selfHotelLowestReq.getPayMethod() != null && selfHotelLowestReq.getPayMethod().equals(PayMethodEnum.PAY.key) ? null : orgCurrency)
                                .roomPrice(selfHotelLowestReq.getRoomPrice())
                                .taxFee(selfHotelLowestReq.getTax())
                                .salesTax(selfHotelLowestReq.getSalesTax())
                                .otherTax(selfHotelLowestReq.getOtherTaxFee())
                                .secondTaxFee(selfHotelLowestReq.getSecondTaxFee())
                                .secondSalesTax(selfHotelLowestReq.getSecondSalesTax())
                                .secondOtherTax(selfHotelLowestReq.getSecondOtherTax())
                                .payInStorePrice(selfHotelLowestReq.getPayAtHotelFee())
                                .payInStoreCurrency(selfHotelLowestReq.getPayAtHotelFeeCurrency())
                                .cashPrice(selfHotelLowestReq.getPayMethod() != null && selfHotelLowestReq.getPayMethod().equals(PayMethodEnum.PAY.key) ? selfHotelLowestReq.getTotalPrice() : null)
                                .cashCurrency(selfHotelLowestReq.getPayMethod() != null && selfHotelLowestReq.getPayMethod().equals(PayMethodEnum.PAY.key) ? orgCurrency : null)
                                .cityCode(esHotelDto != null ? esHotelDto.getCityCode() : null)
                                .countryCode(esHotelDto != null ? esHotelDto.getCountryCode() : null)
                                .supplyClass(SupplyClassEnum.ZIQIAN.getCode())
                                .hotelId(String.valueOf(resp.getHotelId()))
                                .supplyCode(resp.getSupplierCode())
                                .merchantSource(CompanyDTO.COMPANY_NAME)
                                .merchantCode(CompanyDTO.COMPANY_CODE)
                                .supplyType(String.valueOf(supplierAddDTO.getDomesticOrOverseas()))
                                .status(1)
                                .createdBy("system(自签起价)")
                                .createdDt(currentDate)
                                .updatedBy("system(自签起价)")
                                .updatedDt(currentDate)
                                .build());
                    }
                }
            } catch (Exception e) {
                log.error("计算自签酒店起价任务，处理数据异常，resp={}", JSONUtil.toJsonStr(resp), e);
            }
        }
        // 发送MQ消息
        if (CollUtilX.isNotEmpty(lowestPriceReqList)) {
            rocketMQTemplate.syncSend(MqConstants.HOTEL_LOWEST_PRICE_TOPIC + ":" + MqConstants.HOTEL_LOWEST_PRICE_ADD_TAG, lowestPriceReqList);
        }
    }

    /**
     * 从缓存中获取供应商信息
     */
    private List<SupplierAddDTO> getSuppliersFromCache(List<String> supplierCodes) {
        return StrUtilX.parseObject(RedisTemplateX.hMultiGet(RedisKey.SUPPLY_ACCOUNT_CONFIG, supplierCodes), new TypeReference<List<SupplierAddDTO>>() {
        });
    }

    /**
     * 根据产品Ids获取产品
     */
    private Map<String, ProductDTO> getProductMap(List<String> productIds) {
        List<Object> objects = RedisTemplateX.hMultiGet(RedisKey.productInfoKey, productIds);

        if (CollUtilX.isEmpty(objects)) {
            return Maps.newHashMap();
        }
        List<ProductDTO> productDTOS = StrUtilX.parseObject(objects, new TypeReference<List<ProductDTO>>() {
        });
        if (CollUtilX.isNotEmpty(productDTOS)) {
            return productDTOS.stream().filter(Objects::nonNull).collect(Collectors.toMap(e -> String.valueOf(e.getProductId()), Function.identity(), (v1, v2) -> v2));
        }
        return Maps.newHashMap();
    }

    /**
     * 计算酒店最低价格
     */
    private SelfHotelLowestReq calculateLowestPrice(Map<String, ProductSaleIncreaseDTO> map,
                                                    ProductBasePriceAndRoomStatusDTO productBasePriceAndRoomStatusDTO,
                                                    ProductDTO productDTO,
                                                    Map<String, String> productIdAndTaxRuleConfigId,
                                                    Map<String, ProTaxRuleConfigDTO> taxRuleMap,
                                                    Map<Integer, RoomTaxDetail> roomTaxDetailMap,
                                                    int orgCurrency) {

        ProductSaleIncreaseDTO productSaleIncreaseDTO = map.get(productBasePriceAndRoomStatusDTO.getRedisKey());

        BigDecimal basePrice = Objects.isNull(productBasePriceAndRoomStatusDTO.getBasePrice()) ? BigDecimal.ZERO : productBasePriceAndRoomStatusDTO.getBasePrice();

        Integer productId = productBasePriceAndRoomStatusDTO.getProductId();
        // 根据productId获取对应的税费规则ID
        String taxRuleConfigId = productIdAndTaxRuleConfigId.get(String.valueOf(productId));
        ProTaxRuleConfigDTO proTaxRuleConfigDTO = null;
        // 获取具体税费规则
        if (StrUtilX.isNotEmpty(taxRuleConfigId)) {
            proTaxRuleConfigDTO = taxRuleMap.get(taxRuleConfigId);
        }

        Integer adjustmentType;
        BigDecimal modifiedAmt;
        BigDecimal miniAddRadiation = null;
        if (productSaleIncreaseDTO == null) {
            if (productDTO.getPayMethod() != null && productDTO.getPayMethod().equals(PayMethodEnum.PAY.key)) {
                // 现付 +0
                adjustmentType = 0;
                modifiedAmt = new BigDecimal("0");
            } else {
                // 预付 且没有加幅
                return null;
            }
        } else {
            adjustmentType = productSaleIncreaseDTO.getB2bAdjustmentType();
            modifiedAmt = productSaleIncreaseDTO.getB2bModifiedAmt();
            miniAddRadiation = productSaleIncreaseDTO.getB2bMiniAddRadiation();
        }

        // 获取产品币种 一定存在
        Integer productCurrency = productDTO.getCurrency();

        // 产品币种转 商家币种汇率
        BigDecimal merchantRate = RateUtil.getRate(productCurrency, orgCurrency);

        // 如果产品币种等于商家币种  那么汇率=1
        if (productCurrency.equals(orgCurrency)) {
            merchantRate = new BigDecimal("1");
        }

        // 1. 如果不含税 则需要计算税费 公式=底价+税费
        if (proTaxRuleConfigDTO != null && CollUtilX.isNotEmpty(proTaxRuleConfigDTO.getProTaxDetails())) {
            getAllFaxPrice(productBasePriceAndRoomStatusDTO, proTaxRuleConfigDTO.getProTaxDetails(), productCurrency, roomTaxDetailMap, merchantRate);
        } else {
            // 底价 由产品币种变更为商家币种 因为需要加辐 所以需要*汇率
            basePrice = basePrice.multiply(merchantRate);
            productBasePriceAndRoomStatusDTO.setRoomPrice(basePrice);
        }

        // 房费 商家币种
        BigDecimal roomPrice = productBasePriceAndRoomStatusDTO.getRoomPrice();
        RoomTaxDetail roomTaxDetail = null;

        // 现付产品加幅为0 所以不需要加幅
        if (productDTO.getPayMethod() != null && productDTO.getPayMethod().equals(PayMethodEnum.PAY.key)) {
            // 是否首日 如果存在此税费 则需要计算在今天
            roomTaxDetail = roomTaxDetailMap.get(productBasePriceAndRoomStatusDTO.getProductId());
            // 首日税费
            BigDecimal fastTax = new BigDecimal("0");
            if (roomTaxDetail != null) {
                if (roomTaxDetail.getSalesTax() != null) {
                    fastTax = fastTax.add(roomTaxDetail.getSalesTax());
                }
                if (roomTaxDetail.getOtherTax() != null) {
                    fastTax = fastTax.add(roomTaxDetail.getOtherTax());
                }
                if (roomTaxDetail.getTaxFee() != null) {
                    fastTax = fastTax.add(roomTaxDetail.getTaxFee());
                }
            }
            // 售价=房费+税费+加幅+首日税费
            BigDecimal salePrice = addPrice(productBasePriceAndRoomStatusDTO.getRoomPrice(), productBasePriceAndRoomStatusDTO.getSalesTax(), productBasePriceAndRoomStatusDTO.getTax(), productBasePriceAndRoomStatusDTO.getOtherTaxFee(), fastTax);
            productBasePriceAndRoomStatusDTO.setBasePrice(salePrice);
        } else if (adjustmentType == null || modifiedAmt == null) {
            // 防止出现没有设置加幅的情况 此时 已经是商家商家 币种价格了.所以只需要*客户币种汇率 | 现付产品 不需要加幅
            productBasePriceAndRoomStatusDTO.setBasePrice(roomPrice);
        } else {
            // 加幅值 商家币种 当+ 时存在 -时 为0  这个很重要
            BigDecimal adjustment = new BigDecimal("0");
            // 折扣字段
            BigDecimal discount = BigDecimal.ZERO;
            // 加幅类型
            if (Objects.equals(SaleAdjustmentTypeEnum.PLUS_NUMBER.no, adjustmentType)) {
                adjustment = modifiedAmt;
            } else if (Objects.equals(SaleAdjustmentTypeEnum.DIVIDE_NUMBER.no, adjustmentType)) {
                discount = modifiedAmt.negate();
            } else if (Objects.equals(SaleAdjustmentTypeEnum.PLUS_PERCENTAGE.no, adjustmentType)) {
                // 这里需要再次进行比较
                BigDecimal addRadiation = roomPrice.multiply(modifiedAmt);
                // 存在最小加辐值 那么则需要获取最小加辐值
                if (miniAddRadiation != null) {
                    // 产品加辐值小于 最小加辐值, 则使用商家币种的产品加辐值
                    if (addRadiation.compareTo(miniAddRadiation) < 0) {
                        adjustment = miniAddRadiation;
                    } else {
                        // 百分比售价加辐+房费
                        adjustment = addRadiation;
                    }
                } else {
                    // 不存在最小加辐值 则使用百分比加幅
                    adjustment = addRadiation;
                }
            } else if (Objects.equals(SaleAdjustmentTypeEnum.SUBTRACT_PERCENTAGE.no, adjustmentType)) {
                discount = roomPrice.multiply(modifiedAmt).negate();
            } else if (Objects.equals(SaleAdjustmentTypeEnum.EQUALS.no, adjustmentType)) {
                // 修改金额大于房费则 加幅=修改金额-房费
                // 否则 房费=修改金额 加幅=0
                if (modifiedAmt.compareTo(roomPrice) > 0) {
                    adjustment = modifiedAmt.subtract(roomPrice);
                } else {
                    // 否则直接修改房费 加幅=0  避免出现 房费和加幅
                    roomPrice = modifiedAmt; // 售价=多少
                }
            }
            productBasePriceAndRoomStatusDTO.setRoomPrice(roomPrice);

            // 设置折扣
            productBasePriceAndRoomStatusDTO.setDiscount(discount);

            // 是否首日 如果存在此税费 则需要计算在今天
            roomTaxDetail = roomTaxDetailMap.get(productBasePriceAndRoomStatusDTO.getProductId());
            // 首日税费
            BigDecimal fastTax = new BigDecimal("0");
            if (roomTaxDetail != null) {
                if (roomTaxDetail.getSalesTax() != null) {
                    fastTax = fastTax.add(roomTaxDetail.getSalesTax());
                }
                if (roomTaxDetail.getOtherTax() != null) {
                    fastTax = fastTax.add(roomTaxDetail.getOtherTax());
                }
                if (roomTaxDetail.getTaxFee() != null) {
                    fastTax = fastTax.add(roomTaxDetail.getTaxFee());
                }
            }
            // 售价=房费+税费+加幅+首日税费+折扣
            BigDecimal salePrice = addPrice(productBasePriceAndRoomStatusDTO.getRoomPrice(), productBasePriceAndRoomStatusDTO.getSalesTax(), productBasePriceAndRoomStatusDTO.getTax(), productBasePriceAndRoomStatusDTO.getOtherTaxFee(), fastTax, productBasePriceAndRoomStatusDTO.getDiscount());
            // 其他税费=其他税费+加幅
            productBasePriceAndRoomStatusDTO.setOtherTaxFee(addPrice(productBasePriceAndRoomStatusDTO.getOtherTaxFee(), adjustment));
            productBasePriceAndRoomStatusDTO.setBasePrice(salePrice.add(adjustment));
        }
        return SelfHotelLowestReq.builder()
                .saleDate(productBasePriceAndRoomStatusDTO.getSaleDate())
                .totalPrice(productBasePriceAndRoomStatusDTO.getBasePrice())
                .productCurrency(productDTO.getCurrency())
                .payMethod(productDTO.getPayMethod())
                .roomPrice(productBasePriceAndRoomStatusDTO.getRoomPrice())
                .salesTax(productBasePriceAndRoomStatusDTO.getSalesTax())
                .tax(productBasePriceAndRoomStatusDTO.getTax())
                .otherTaxFee(productBasePriceAndRoomStatusDTO.getOtherTaxFee())
                .discount(productBasePriceAndRoomStatusDTO.getDiscount())
                .payAtHotelFeeCurrency(productBasePriceAndRoomStatusDTO.getPayAtHotelFeeCurrency())
                .payAtHotelFee(productBasePriceAndRoomStatusDTO.getPayAtHotelFee())
                .secondTaxFee(roomTaxDetail == null ? null : roomTaxDetail.getTaxFee())
                .secondSalesTax(roomTaxDetail == null ? null : roomTaxDetail.getSalesTax())
                .secondOtherTax(roomTaxDetail == null ? null : roomTaxDetail.getOtherTax())
                .build();
    }

    /**
     * 获取税后价格
     */
    private static void getAllFaxPrice(ProductBasePriceAndRoomStatusDTO productDayQuotationDTO, List<ProTaxDetailDTO> proTaxDetails, Integer productCurrency, Map<Integer, RoomTaxDetail> roomTaxDetailMap, BigDecimal merchantRate) {
        // 税后价格   房费 = 底价
        BigDecimal roomPrice = productDayQuotationDTO.getBasePrice();
        if (roomPrice == null || roomPrice.compareTo(BigDecimal.ZERO) == 0) {
            return;
        }
        if (CollUtilX.isNotEmpty(proTaxDetails)) {
            // 是否首日  如果存在此税费 则需要计算在今天
            roomTaxDetailMap.put(productDayQuotationDTO.getProductId(), new RoomTaxDetail());

            // 存在税费
            BigDecimal basePrice;
            BigDecimal salesTax = new BigDecimal("0");
            BigDecimal tax = new BigDecimal("0");
            BigDecimal otherTaxFee = new BigDecimal("0");

            for (ProTaxDetailDTO proTaxDetail : proTaxDetails) {
                switch (proTaxDetail.getTaxType()) {
                    case 0:
                        // 销售税
                        if (Objects.equals(proTaxDetail.getTaxStandard(), TaxStandardsEnum.NIGHT.getCode())) {
                            salesTax = salesTax.add(getTax(roomPrice, proTaxDetail));
                        } else if (Objects.equals(proTaxDetail.getTaxStandard(), TaxStandardsEnum.MIDNIGHT.getCode())) {
                            RoomTaxDetail roomTaxDetail = roomTaxDetailMap.get(productDayQuotationDTO.getProductId());
                            BigDecimal oldSaleTax = roomTaxDetail.getSalesTax();
                            if (oldSaleTax == null) {
                                oldSaleTax = new BigDecimal("0");
                            }
                            oldSaleTax = oldSaleTax.add(getTax(roomPrice, proTaxDetail));
                            roomTaxDetail.setSalesTax(oldSaleTax);
                        }
                        break;
                    case 1:
                        // 税费
                        if (Objects.equals(proTaxDetail.getTaxStandard(), TaxStandardsEnum.NIGHT.getCode())) {
                            tax = tax.add(getTax(roomPrice, proTaxDetail));
                        } else if (Objects.equals(proTaxDetail.getTaxStandard(), TaxStandardsEnum.MIDNIGHT.getCode())) {
                            RoomTaxDetail roomTaxDetail = roomTaxDetailMap.get(productDayQuotationDTO.getProductId());
                            BigDecimal oldTax = roomTaxDetail.getTaxFee();
                            if (oldTax == null) {
                                oldTax = new BigDecimal("0");
                            }
                            oldTax = oldTax.add(getTax(roomPrice, proTaxDetail));
                            roomTaxDetail.setTaxFee(oldTax);
                        }
                        break;
                    case 2:
                        // 到店付 只能增加到 每次
                        RoomTaxDetail countTaxDetail = roomTaxDetailMap.get(productDayQuotationDTO.getProductId());
                        BigDecimal oldPayInStorePrice = countTaxDetail.getPayInStorePrice();
                        if (oldPayInStorePrice == null) {
                            oldPayInStorePrice = new BigDecimal("0");
                        }
                        oldPayInStorePrice = oldPayInStorePrice.add(getTax(roomPrice, proTaxDetail));
                        countTaxDetail.setPayInStorePrice(oldPayInStorePrice);
                        countTaxDetail.setPayInStoreCurrency(productCurrency);
                        break;
                    case 3:
                        // 其他税费
                        if (Objects.equals(proTaxDetail.getTaxStandard(), TaxStandardsEnum.NIGHT.getCode())) {
                            otherTaxFee = otherTaxFee.add(getTax(roomPrice, proTaxDetail));
                        } else if (Objects.equals(proTaxDetail.getTaxStandard(), TaxStandardsEnum.MIDNIGHT.getCode())) {
                            RoomTaxDetail roomTaxDetail = roomTaxDetailMap.get(productDayQuotationDTO.getProductId());
                            BigDecimal oldOtherTax = roomTaxDetail.getOtherTax();
                            if (oldOtherTax == null) {
                                oldOtherTax = new BigDecimal("0");
                            }
                            oldOtherTax = oldOtherTax.add(getTax(roomPrice, proTaxDetail));
                            roomTaxDetail.setOtherTax(oldOtherTax);
                        }
                        break;
                }
            }
            RoomTaxDetail roomTaxDetail = roomTaxDetailMap.get(productDayQuotationDTO.getProductId());
            // 产品原始底价输出
            if (roomTaxDetail != null) {
                // 含次税
                productDayQuotationDTO.setTmpBasePrice(getTotal(roomPrice, salesTax, tax, otherTaxFee, roomTaxDetail.getSalesTax(), roomTaxDetail.getTaxFee(), roomTaxDetail.getOtherTax()));
            } else {
                // 不含次税
                productDayQuotationDTO.setTmpBasePrice(getTotal(roomPrice, salesTax, tax, otherTaxFee));
            }

            basePrice = roomPrice.multiply(merchantRate);
            salesTax = salesTax.multiply(merchantRate);
            tax = tax.multiply(merchantRate);
            otherTaxFee = otherTaxFee.multiply(merchantRate);

            // 到店另付币种 为产品原始币种
            productDayQuotationDTO.setRoomPrice(roomPrice);
            if (salesTax.compareTo(BigDecimal.ZERO) != 0) {
                productDayQuotationDTO.setSalesTax(salesTax);
            }
            if (tax.compareTo(BigDecimal.ZERO) != 0) {
                productDayQuotationDTO.setTax(tax);
            }
            if (otherTaxFee.compareTo(BigDecimal.ZERO) != 0) {
                productDayQuotationDTO.setOtherTaxFee(otherTaxFee);
            }

            // 每次税费处理 如果是首日并且存在每次税费 则需要在底价中加入次税费
            if (roomTaxDetail != null) {
                // 修改后的价格
                if (roomTaxDetail.getSalesTax() != null) {
                    roomTaxDetail.setSalesTax(roomTaxDetail.getSalesTax().multiply(merchantRate));
                }
                if (roomTaxDetail.getTaxFee() != null) {
                    roomTaxDetail.setTaxFee(roomTaxDetail.getTaxFee().multiply(merchantRate));
                }
                if (roomTaxDetail.getOtherTax() != null) {
                    roomTaxDetail.setOtherTax(roomTaxDetail.getOtherTax().multiply(merchantRate));
                }
            }
            productDayQuotationDTO.setRoomPrice(basePrice);
        } else {
            // 原始底价 不转换币种 产品原始币种
            productDayQuotationDTO.setTmpBasePrice(roomPrice);

            roomPrice = roomPrice.multiply(merchantRate);
            // 含税价
            productDayQuotationDTO.setRoomPrice(roomPrice);
        }
    }

    private static BigDecimal getTotal(BigDecimal... price) {
        BigDecimal total = BigDecimal.ZERO;
        if (price != null) {
            for (BigDecimal bigDecimal : price) {
                if (bigDecimal != null) {
                    total = total.add(bigDecimal);
                }
            }
        }
        return total;
    }

    /**
     * 获取税费
     */
    private static BigDecimal getTax(BigDecimal roomPrice, ProTaxDetailDTO proTaxDetail) {
        if (proTaxDetail.getTaxIncreaseType() != null && proTaxDetail.getTaxIncreaseValue() != null) {
            if (proTaxDetail.getTaxIncreaseType().equals(TaxIncreaseEnum.FIXED.getCode())) {
                // 固定
                return proTaxDetail.getTaxIncreaseValue();
            } else {
                // 百分比
                return roomPrice.multiply(proTaxDetail.getTaxIncreaseValue().divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
            }
        }
        return BigDecimal.ZERO;
    }

    @Override
    public void calculateNeedSelfHotelLowestPrice(CalculateSelfHotelLowestDTO dto) {
        if (dto.getCurrency() == null && dto.getTaxRuleConfigId() == null && dto.getProductId() == null) {
            return;
        }
        QueryProductIdsRequest request = new QueryProductIdsRequest();
        request.setCurrency(dto.getCurrency());
        request.setTaxRuleConfigId(dto.getTaxRuleConfigId());
        request.setProductId(dto.getProductId());
        List<HotelSaleInfoItemResp> respList = productInfoService.selectProductHotel(request);
        // 提交起价队列
        if (CollUtilX.isNotEmpty(respList)) {
            processData(respList);
        }
    }
}
