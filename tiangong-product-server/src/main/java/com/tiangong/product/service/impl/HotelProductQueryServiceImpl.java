package com.tiangong.product.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.tiangong.common.Constant;
import com.tiangong.common.Response;
import com.tiangong.dis.common.CommonUtils;
import com.tiangong.dis.common.DisCommonUtils;
import com.tiangong.dis.dto.*;
import com.tiangong.dis.enums.DhubReturnCodeEnum;
import com.tiangong.dis.enums.RoomStatusEnum;
import com.tiangong.dto.common.CompanyDTO;
import com.tiangong.dto.common.TipsDTO;
import com.tiangong.dto.common.TipsResp;
import com.tiangong.dto.hotel.HotelLowestPrice;
import com.tiangong.dto.hotel.HotelPriceItem;
import com.tiangong.dto.hotel.HotelSummary;
import com.tiangong.dto.hotel.RoomSummary;
import com.tiangong.dto.hotel.base.*;
import com.tiangong.dto.order.CancelRestriction;
import com.tiangong.dto.product.*;
import com.tiangong.dto.product.request.*;
import com.tiangong.dto.product.response.*;
import com.tiangong.enums.*;
import com.tiangong.exception.SysException;
import com.tiangong.finance.OrgDTO;
import com.tiangong.fuzzyquery.dto.FuzzyQueryDTO;
import com.tiangong.fuzzyquery.dto.FuzzySupplierDTO;
import com.tiangong.fuzzyquery.remote.FuzzyQueryRemote;
import com.tiangong.hotel.BaseHotelInfoUrl;
import com.tiangong.hotel.remote.HotelRemote;
import com.tiangong.initializer.CommonInitializer;
import com.tiangong.keys.RedisKey;
import com.tiangong.organization.remote.TipsRemote;
import com.tiangong.organization.remote.dto.AgentAccountConfig;
import com.tiangong.organization.remote.dto.SupplierAddDTO;
import com.tiangong.product.config.BedNameConfig;
import com.tiangong.product.config.SettingsConstant;
import com.tiangong.product.convert.CommonConvert;
import com.tiangong.product.domain.areatadatimezone.AreaDataTimeZoneReqDTO;
import com.tiangong.product.domain.entity.AgentHotelLowestPriceEntity;
import com.tiangong.product.domain.resp.HotelProductIdsResponse;
import com.tiangong.product.domain.resp.PriceDto;
import com.tiangong.product.domain.resp.QueryProductIdsRequest;
import com.tiangong.product.dto.CancelPenalDTO;
import com.tiangong.product.dto.ProTaxDetailDTO;
import com.tiangong.product.dto.ProTaxRuleConfigDTO;
import com.tiangong.product.dto.ProductDTO;
import com.tiangong.product.dto.mongo.SupplyHotelLowestPrice;
import com.tiangong.product.mapper.ProductMapper;
import com.tiangong.product.service.HotelProductQueryService;
import com.tiangong.product.service.ProOrgHotelAvailableService;
import com.tiangong.product.service.ProductInfoService;
import com.tiangong.product.util.QueryUtil;
import com.tiangong.product.util.RateUtil;
import com.tiangong.product.util.TimeZoneUtil;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.redis.util.RedisUtil;
import com.tiangong.supply.direct.remote.SupplyDirectShubProductRemote;
import com.tiangong.supply.direct.remote.dto.ProductMiddleDto;
import com.tiangong.supply.direct.remote.dto.RoomItemDetailDto;
import com.tiangong.supply.direct.remote.request.QueryProductInfoRequest;
import com.tiangong.util.*;
import it.unimi.dsi.fastutil.objects.Object2ObjectMap;
import it.unimi.dsi.fastutil.objects.Object2ObjectOpenHashMap;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.stream.Collectors.*;

/**
 * 用途：产品信息查询
 */
@Slf4j
@Service
public class HotelProductQueryServiceImpl implements HotelProductQueryService {

    @Autowired
    private SupplyDirectShubProductRemote supplyDirectShubProductRemote;

    @Autowired
    private QueryUtil queryUtil;

    @Autowired
    private HotelRemote hotelRemote;

    @Autowired
    private ProductMapper productMapper;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private TipsRemote tipsRemote;

    @Autowired
    private FuzzyQueryRemote fuzzyQueryRemote;

    @Autowired
    private SlsLoggerUtil slsLoggerUtil;

    @Autowired
    private BedNameConfig bedNameConfig;

    @Autowired
    private ProductInfoService productInfoService;

    @Resource(name = "defaultPool")
    private ThreadPoolTaskExecutor defaultPool;

    @Resource(name = "processingProductInfoExecutor")
    private ThreadPoolTaskExecutor processingProductInfoExecutor;

    @Autowired
    private SettingsConstant settingsConstant;

    @Autowired
    private ProOrgHotelAvailableService proOrgHotelAvailableService;
    @Override
    public List<Long> queryAvailableHotelList(AvailableHotelRequest request) {
        // 查询分销商
        AgentAccountConfig agentAccountConfig = JSON.parseObject(RedisTemplateX.hashGet(RedisKey.AGENT_ACCOUNT_CONFIG, request.getAgentCode()).toString(), AgentAccountConfig.class);
        if (agentAccountConfig == null) {
            return null;
        }

        // 查询客户白名单供应商
        List<String> supplyCodes = queryUtil.getAgentSupplyAvailable(request.getAgentCode());
        if (CollUtilX.isEmpty(supplyCodes)) {
            return null;
        }

        // 总天数
        Long daysBetween = DateUtilX.getDay(DateUtilX.stringToDate(request.getCheckInDate()), DateUtilX.stringToDate(request.getCheckOutDate()));

        Criteria criteria = new Criteria();
        criteria.and("merchantSource").is(CompanyDTO.COMPANY_NAME);
        criteria.and("supplyCode").in(supplyCodes);
        criteria.and("saleDate").gte(request.getCheckInDate()).lt(request.getCheckOutDate());
        // 酒店列表
        if (CollectionUtil.isNotEmpty(request.getHotelIds())) {
            List<String> hotelIds = request.getHotelIds().stream().map(String::valueOf).collect(toList());
            criteria.and("hotelId").in(hotelIds);
        }
        // 城市编码
        if (request.getCityCode() != null) {
            criteria.and("cityCode").is(request.getCityCode());
        }

        // 构建聚合查询
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(criteria),  // 匹配条件
                Aggregation.group("hotelId", "supplyCode", "merchantCode", "merchantSource").count().as("count"),// 按 hotelId,供应商编码,商家来源，商家编码 分组
                Aggregation.match(Criteria.where("count").is(daysBetween)), // 过滤出记录数等于天数的 hotelId
                Aggregation.group("hotelId"),// 获取去重后的酒店id
                Aggregation.project("hotelId")  // 投影需要的字段
        );

        // 执行聚合查询
        AggregationResults<HotelIdDto> results = mongoTemplate.aggregate(aggregation, SupplyHotelLowestPrice.class, HotelIdDto.class);

        // 转换为 List<Long>
        return results.getMappedResults().stream()
                .map(HotelIdDto::get_id) // 将每个字符串转换为 Long
                .collect(Collectors.toList());
    }

    /**
     * 酒店每日起价查询-Dhub
     */
    @Override
    public ResponseResult<HotelLowestPriceResponse> queryHotelLowestPrice(HotelLowestPriceRequest request) {
        request.setChannelCode(ChannelEnum.DHUB.value);
        return queryHotelLowestPriceMogoDB(request);
    }

    /**
     * 从MongoDB 查询酒店每日起价
     * @param request 请求参数
     * @return 响应结果
     */
    public ResponseResult<HotelLowestPriceResponse> queryHotelLowestPriceMogoDB(HotelLowestPriceRequest request) {
        // 1. 参数校验
        if (request.getAgentCode() == null || CollUtilX.isEmpty(request.getHotelIds())) {
            return new ResponseResult<>(DhubReturnCodeEnum.INVALID_INPUTPARAM.code, DhubReturnCodeEnum.INVALID_INPUTPARAM.description);
        }

        // 2. 使用轻量级响应构建器
        final HotelLowestPriceResponse response = new HotelLowestPriceResponse();
        final List<HotelLowestPrice> results = new ArrayList<>(request.getHotelIds().size());

        try {
            // 3. 获取必要配置
            final AgentAccountConfig agentConfig = getAgentAccountConfig(request.getAgentCode());
            if (agentConfig == null) {
                return new ResponseResult<>(ErrorCodeEnum.AGENT_INFO_NOT_EXIST.errorCode, ErrorCodeEnum.AGENT_INFO_NOT_EXIST.errorDesc);
            }

            // 4. 批量获取供应商数据
            final List<String> supplyCodes = queryUtil.getAgentSupplyAvailable(request.getAgentCode());
            if (CollUtilX.isEmpty(supplyCodes)) {
                return new ResponseResult<>(DhubReturnCodeEnum.SUCCESS.no, DhubReturnCodeEnum.SUCCESS.description);
            }

            // 5. 使用高效数据结构
            final Object2ObjectMap<String, SupplierAddDTO> supplyConfigMap = new Object2ObjectOpenHashMap<>(
                    queryUtil.getSupplierConfig(supplyCodes)
            );

            // 6. 汇率计算优化
            OrgDTO orgDTO = CommonInitializer.getOrgInfo();
            int orgCurrency = Integer.parseInt(orgDTO.getOrgCurrency());
            final BigDecimal orgToAgentRate = calculateExchangeRate(orgCurrency, agentConfig.getSettlementCurrency());

            // 7. 查询MongoDB数据
            final List<SupplyHotelLowestPrice> dbResults = mongoTemplate.find(
                    buildMongoQuery(request, supplyCodes),
                    SupplyHotelLowestPrice.class
            );

            //提取出所有的酒店id
            List<Long> hotelIds = dbResults.stream()
                    .map(SupplyHotelLowestPrice::getHotelId)
                    .collect(Collectors.toList());


            // 8. 使用原始类型集合优化内存
            final Object2ObjectMap<Long, Object2ObjectMap<String, HotelPriceItem>> priceMap =
                    new Object2ObjectOpenHashMap<>(dbResults.size());

            // 9. 流式处理转换逻辑
            convertPricesStreaming(
                    dbResults,
                    supplyConfigMap,
                    queryUtil.getSupplyIncrease(request.getAgentCode(), supplyCodes),
                    queryUtil.getSupplyHotelIncrease(request.getAgentCode(), supplyCodes, hotelIds),
                    orgToAgentRate,
                    priceMap,
                    agentConfig,
                    orgCurrency
            );

            // 10. 构建响应数据
            buildResponse(results, priceMap, request.getCheckInDate(), request.getCheckOutDate(), request.getChannelCode());

            response.setHotelLowestPrices(results);
            response.setCurrency(agentConfig.getSettlementCurrency());
        } catch (Exception e) {
            log.error("dhub酒店每日起价查询异常", e);
            return new ResponseResult<>(DhubReturnCodeEnum.SYSTEM_EXCEPTION.no, DhubReturnCodeEnum.SYSTEM_EXCEPTION.description);
        }

        return new ResponseResult<>(DhubReturnCodeEnum.SUCCESS.no, DhubReturnCodeEnum.SUCCESS.description, response);
    }

    /**
     * 获取客户配置
     */
    private AgentAccountConfig getAgentAccountConfig(String agentCode) {
        Object config = RedisTemplateX.hashGet(RedisKey.AGENT_ACCOUNT_CONFIG, agentCode);
        return config != null ? StrUtilX.parseObject(config, AgentAccountConfig.class) : null;
    }

    /**
     * 获取汇率
     */
    private BigDecimal calculateExchangeRate(Integer orgCurrency, Integer settlementCurrency) {
        BigDecimal orgToAgentRate = RedisUtil.getRateToTargetCurrency(orgCurrency, CompanyDTO.COMPANY_CODE, settlementCurrency);
        if (orgToAgentRate == null) {
            orgToAgentRate = BigDecimal.ONE;
        }
        return orgToAgentRate;
    }

    /**
     * 构建查询Mongodb参数
     */
    private Query buildMongoQuery(HotelLowestPriceRequest request, List<String> supplyCodes) {
        List<String> hotelIds = request.getHotelIds().stream().map(String::valueOf).collect(toList());
        Criteria criteria = new Criteria();
        criteria.andOperator(
                Criteria.where("merchantSource").is(CompanyDTO.COMPANY_NAME),
                Criteria.where("hotelId").in(hotelIds),
                Criteria.where("saleDate").gte(request.getCheckInDate()).lt(request.getCheckOutDate()),
                Criteria.where("supplyCode").in(supplyCodes)
        );
        Query query = new Query(criteria);
        // 过滤无需返回的字段，减少返回数据 优化性能
        query.fields().include("hotelId")
                .include("lowestPrice").include("roomPrice")
                .include("taxFee").include("salesTax").include("otherTax").include("secondTaxFee").include("secondSalesTax").include("secondOtherTax")
                .include("lowestPriceCurreny").include("cashPrice").include("cashCurrency")
                .include("payInStoreCurrency").include("payInStorePrice").include("supplyCode")
                .include("saleDate").include("supplyClass").exclude("_id");
        return query;
    }

    /**
     * 使用高效类型存储价格数据
     */
    private void convertPricesStreaming(List<SupplyHotelLowestPrice> prices,
                                        Object2ObjectMap<String, SupplierAddDTO> configs,
                                        Map<String, SupplyIncrease> increases,
                                        Map<String, SupplyHotelIncrease> supplyHotelIncreases,
                                        BigDecimal exchangeRate,
                                        Object2ObjectMap<Long, Object2ObjectMap<String, HotelPriceItem>> output,
                                        AgentAccountConfig agentConfig,
                                        int orgCurrency) {
        final Object2ObjectMap<Integer, BigDecimal> rateCache = new Object2ObjectOpenHashMap<>(32);

        for (SupplyHotelLowestPrice price : prices) {
            final Long hotelId = price.getHotelId();
            final String dateKey = price.getSaleDate();

            // 计算最低价格
            final BigDecimal lowestPrice = calculateLowestPrice(price, configs, increases, supplyHotelIncreases, exchangeRate, rateCache, agentConfig, orgCurrency);
            if (lowestPrice == null) continue;

            // 比较价格
            if (output.get(hotelId) != null) {
                HotelPriceItem hotelPriceItem = output.get(hotelId).get(dateKey);
                // 价格高就不设置
                if (hotelPriceItem != null && lowestPrice.compareTo(hotelPriceItem.getSalePrice()) >= 0) {
                    continue;
                }
            }

            // 组装价格项
            final HotelPriceItem item = assemblyHotelPriceItem(
                    assemblyLowestPriceEntity(
                            agentConfig.getAgentCode(),
                            hotelId.toString(),
                            lowestPrice,
                            agentConfig.getSettlementCurrency(),
                            price.getPayInStorePrice(),
                            price.getPayInStoreCurrency()
                    ),
                    dateKey
            );

            // 存储结果
            output.computeIfAbsent(hotelId, k -> new Object2ObjectOpenHashMap<>(32))
                    .put(dateKey, item);
        }
    }

    /**
     * 计算最低价格
     */
    private BigDecimal calculateLowestPrice(SupplyHotelLowestPrice price,
                                            Object2ObjectMap<String, SupplierAddDTO> configs,
                                            Map<String, SupplyIncrease> increases,
                                            Map<String, SupplyHotelIncrease> supplyHotelIncreases,
                                            BigDecimal exchangeRate,
                                            Object2ObjectMap<Integer, BigDecimal> rateCache,
                                            AgentAccountConfig agentConfig,
                                            int orgCurrency) {
        BigDecimal lowestPrice = null;

        // 计算预付价
        if (price.getLowestPrice() != null && price.getLowestPriceCurreny() != null) {
            final BigDecimal rate = getCachedRate(price.getLowestPriceCurreny(), orgCurrency, rateCache);
            if (rate != null) {
                lowestPrice = queryUtil.calculateSalePrice(
                        price,
                        agentConfig,
                        configs.get(price.getSupplyCode()),
                        increases.get(price.getSupplyCode()),
                        supplyHotelIncreases.get(price.getSupplyCode().concat(StrUtilX.SPLIT_CODE).concat(String.valueOf(price.getHotelId()))),
                        rate,
                        exchangeRate,
                        orgCurrency,
                        price.getLowestPriceCurreny()
                );
            }
        }

        // 计算现付价
        if (price.getCashPrice() != null && price.getCashCurrency() != null) {
            final BigDecimal cashRate = getCachedRate(price.getCashCurrency(), orgCurrency, rateCache);
            if (cashRate != null) {
                final BigDecimal cashPrice = queryUtil.calculateCashSalePrice(
                        price.getCashPrice(),
                        price.getCashCurrency(),
                        price.getSupplyClass(),
                        agentConfig,
                        cashRate,
                        exchangeRate,
                        orgCurrency
                );
                if (lowestPrice == null || cashPrice.compareTo(lowestPrice) < 0) {
                    lowestPrice = cashPrice;
                }
            }
        }

        return lowestPrice;
    }

    /**
     * 获取缓存的汇率
     */
    private BigDecimal getCachedRate(int currency, int orgCurrency, Object2ObjectMap<Integer, BigDecimal> rateCache) {
        BigDecimal rate = rateCache.get(currency);
        if (rate == null) {
            rate = RateUtil.getRate(currency, orgCurrency);
            if (rate != null) {
                rateCache.put(currency, rate);
            }
        }
        return rate;
    }

    /**
     * 构建响应数据
     */
    private void buildResponse(List<HotelLowestPrice> results,
                               Object2ObjectMap<Long, Object2ObjectMap<String, HotelPriceItem>> priceMap,
                               String checkInDate,
                               String checkOutDate,
                               String channel) {
        final DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        final Date[] dateRange = getDateRange(checkInDate, checkOutDate);

        for (Object2ObjectMap.Entry<Long, Object2ObjectMap<String, HotelPriceItem>> entry : priceMap.object2ObjectEntrySet()) {
            final HotelLowestPrice hotelPrice = new HotelLowestPrice();
            hotelPrice.setHotelId(entry.getKey());

            final List<HotelPriceItem> items = new ArrayList<>(dateRange.length);
            for (Date date : dateRange) {
                final String dateStr = dateFormat.format(date);
                final HotelPriceItem item = entry.getValue().get(dateStr);
                if (item != null) {
                    items.add(item);
                }
            }
            // 如果是b2b渠道，则需要将价格计算为平均价
            if (ChannelEnum.B2B.value.equals(channel)) {
                BigDecimal totalPrice = BigDecimal.ZERO;
                for (Map.Entry<String, HotelPriceItem> itemEntry : entry.getValue().entrySet()) {
                    HotelPriceItem hotelPriceItem = itemEntry.getValue();
                    totalPrice = totalPrice.add(CommonTgUtils.formatBigDecimal(hotelPriceItem.getSalePrice()));
                }
                BigDecimal salePrice = totalPrice.divide(BigDecimal.valueOf(entry.getValue().size()), 2, RoundingMode.UP);
                for (HotelPriceItem item : items) {
                    item.setSalePrice(salePrice);
                }
            }
            hotelPrice.setPriceItems(items);
            results.add(hotelPrice);
        }
    }

    /**
     * 使用原始类型优化日期范围生成
     */
    private Date[] getDateRange(String checkIn, String checkOut) {
        final Date start = DateUtilX.stringToDate(checkIn);
        final Date end = DateUtilX.addDate(DateUtilX.stringToDate(checkOut), -1);
        final int days = (int) ((end.getTime() - start.getTime()) / (1000 * 3600 * 24)) + 1;

        final Date[] dates = new Date[days];
        Calendar cal = Calendar.getInstance();
        cal.setTime(start);
        for (int i = 0; i < days; i++) {
            dates[i] = cal.getTime();
            cal.add(Calendar.DATE, 1);
        }
        return dates;
    }

    /**
     * 组装价格项
     */
    private HotelPriceItem assemblyHotelPriceItem(AgentHotelLowestPriceEntity agentHotelLowestPriceEntity, String date) {
        HotelPriceItem hotelPriceItem = new HotelPriceItem();
        hotelPriceItem.setSalePrice(CommonTgUtils.formatBigDecimal(agentHotelLowestPriceEntity.getLowestPrice()));
        hotelPriceItem.setPayAtHotelCurrency(agentHotelLowestPriceEntity.getPayAtHotelCurrency());
        hotelPriceItem.setPayAtHotelFee(agentHotelLowestPriceEntity.getPayAtHotelFee());
        hotelPriceItem.setSaleCurrency(agentHotelLowestPriceEntity.getSaleCurrency());
        hotelPriceItem.setSaleDate(date);
        return hotelPriceItem;
    }

    /**
     * 酒店每日起价查询-B2B
     */
    @Override
    public ResponseResult<HotelLowestPriceResponse> hotelLowestPrice(HotelLowestPriceRequest request) {
        request.setChannelCode(ChannelEnum.B2B.value);
        request.setHotelIds(Collections.singletonList(request.getHotelId()));
        return queryHotelLowestPriceMogoDB(request);
    }

    @Override
    public ResponseResult<HotelIdListResponse> queryHotelIdList(HotelIdListRequest request) {
        return queryHotelIdListMogoDB(request);
    }

    /**
     * 查询酒店Id列表
     */
    private ResponseResult<HotelIdListResponse> queryHotelIdListMogoDB(HotelIdListRequest request) {
        // 响应结果
        ResponseResult<HotelIdListResponse> responseResult = new ResponseResult<>(DhubReturnCodeEnum.SUCCESS.no, DhubReturnCodeEnum.SUCCESS.description);

        //1.组装查询参数 组装日期，*天内存在起价 则认为可售
        Date date = new Date();
        String beginDate = DateUtilX.dateToString(date);


        //1.2 查询客户白名单供应商
        List<String> supplyCodes = queryUtil.getAgentSupplyAvailable(request.getAgentCode());

        // 从MongoDB中查询客户可售酒店列表
        Criteria criteria = new Criteria();
        //1.3 添加查询条件
        criteria.and("merchantSource").is(CompanyDTO.COMPANY_NAME);

        //1.4 等于1 则查询当天数据，比范围查询速度块
        if (settingsConstant.getAvailableHotelDates() == 1) {
            criteria.and("saleDate").is(beginDate);
        } else {
            String endDate = DateUtilX.dateToString(DateUtilX.getDate(date, settingsConstant.getAvailableHotelDates(), 0, 0));
            criteria.and("saleDate").gte(beginDate).lt(endDate);
        }
        //1.4 存在maxId 则查询大于maxId的数据
        if (request.getMaxId() != null && request.getMaxId() > 0) {
            criteria.and("hotelId").gt(String.valueOf(request.getMaxId()));
        }
        //1.6 可售供应商白名单列表
        criteria.and("supplyCode").in(supplyCodes);

        //1.7 添加城市代码条件
        if (StrUtil.isNotBlank(request.getCityCode())) {
            criteria.and("cityCode").is(request.getCityCode());
        }


        Integer pageSize = request.getPageSize();
        //2. 优化后版本 解决深度分页问题
        if (request.getMaxId() != null) {

            //2.1.1 构建聚合查询
            Aggregation aggregation = Aggregation.newAggregation(
                    Aggregation.match(criteria),  // 匹配条件
                    Aggregation.group("hotelId"),
                    Aggregation.sort(Sort.by(Sort.Direction.ASC, "_id")),
                    // 这里+1 是为了判断是否存在下一页数据 最后会去除掉多余的数据
                    Aggregation.limit(pageSize + 1));

            //3. 使用聚合框架进行去重和分页
            List<HotelIdDto> resultsDto = mongoTemplate.aggregate(aggregation, SupplyHotelLowestPrice.class,HotelIdDto.class).getMappedResults();
            // 组装响应结果
            List<Long> uniqueHotelIds = resultsDto.stream().map(HotelIdDto::get_id).collect(toList());

            //4. 组装响应结果

            HotelIdListResponse hotelIdListResponse = new HotelIdListResponse();
            //4.1 结果为空 或者 结果小于pageSize,则代表当前为最后一页
            if (CollectionUtil.isEmpty(uniqueHotelIds) || uniqueHotelIds.size() < pageSize) {
                hotelIdListResponse.setMaxId(new Long(-1));
            } else {
                // 设置响应结果  获取最后一个 pageSize-1
                hotelIdListResponse.setMaxId(uniqueHotelIds.get(pageSize - 1));
                // 移除多余的记录，只保留 pageSize 条记录
                uniqueHotelIds = uniqueHotelIds.subList(0, pageSize);
            }
            hotelIdListResponse.setHotelIds(uniqueHotelIds);
            hotelIdListResponse.setPageSize(pageSize);
            responseResult.setBussinessResponse(hotelIdListResponse);
            return responseResult;
        }else {
            // 分页查询 目前发现深度分页查询慢问题，使用maxId查询 后续这里会废弃掉

            request.setPageNo(request.getPageNo() - 1);
            PageHelper.startPage(request.getPageNo(), pageSize);

            // 2.2.1 创建聚合管道
            Aggregation aggregationForCount = Aggregation.newAggregation(Aggregation.match(criteria),
                    Aggregation.group("hotelId"),    // 分组并获取total
                    Aggregation.count().as("totalCount")
            );

            CountDownLatch countDownLatch = new CountDownLatch(2);
            // 3. 并发查询列表和总条数

            //3.1 查询总条数
            Future<Integer> totalFuture = defaultPool.submit(() -> {
                int total;
                try {
                    // 2.2 获取符合条件的记录总数
                    long now = System.currentTimeMillis();
                    // 2.2 执行聚合以获取总记录数（去重后的计数）
                    AggregationResults<Map> countResults = mongoTemplate.aggregate(aggregationForCount, SupplyHotelLowestPrice.class, Map.class);
                    total = countResults.getMappedResults().isEmpty() ? 0 : (Integer) countResults.getMappedResults().get(0).get("totalCount");
                    log.info("queryHotelIdListMongoDB.查询总数耗时：{}ms", System.currentTimeMillis() - now);
                } catch (Exception e) {
                    log.error("queryHotelIdListMongoDB.查询总数耗时系统异常", e);
                    return 0;
                } finally {
                    countDownLatch.countDown();
                }
                return total;
            });

            // 3.2 查询列表
            Future<List<Long>> aggregateFuture = defaultPool.submit(() -> {
                List<Long> uniqueHotelIds = new ArrayList<>();
                try {
                    long now = System.currentTimeMillis();
                    // 构建聚合查询
                    Aggregation aggregation = Aggregation.newAggregation(
                            Aggregation.match(criteria),  // 匹配条件
                            Aggregation.group("hotelId"),// 按 hotelId,供应商编码,商家来源，商家编码 分组
                            Aggregation.skip(request.getPageNo() * pageSize),
                            Aggregation.limit(pageSize)
                    );
                    // 2. 使用聚合框架进行去重和分页
                    List<HotelIdDto> results = mongoTemplate.aggregate(aggregation, SupplyHotelLowestPrice.class, // 输入集合
                            HotelIdDto.class // 输出集合
                    ).getMappedResults();
                    uniqueHotelIds = results.stream().map(HotelIdDto::get_id).collect(toList());
                    log.info("queryHotelIdListMongoDB.分页查询结果耗时：{}ms", System.currentTimeMillis() - now);
                } catch (Exception e) {
                    log.error("queryHotelIdListMongoDB.查询总数耗时系统异常", e);
                } finally {
                    countDownLatch.countDown();
                }
                return uniqueHotelIds;
            });

            int totalCount = 0;
            List<Long> uniqueHotelIds = null;
            try {
                // 等地并获取结果
                countDownLatch.await(10000, TimeUnit.MILLISECONDS);
                totalCount = totalFuture.get();
                uniqueHotelIds = aggregateFuture.get();
            } catch (Exception e) {
                log.error("queryHotelIdListMongoDB.查询总数异常", e);
            }

            //4. 组装响应结果
            //4.1  没有结果直接返回
            if (totalCount == 0) {
                HotelIdListResponse hotelIdListResponse = new HotelIdListResponse();
                hotelIdListResponse.setCurrentPage(0);
                hotelIdListResponse.setTotalPage(0);
                hotelIdListResponse.setTotal(0);
                hotelIdListResponse.setHotelIds(new ArrayList<>());
                responseResult.setBussinessResponse(hotelIdListResponse);
                return responseResult;
            }

            //4.2 计算总页数
            int totalPages = (int) Math.ceil((double) totalCount / pageSize);

            HotelIdListResponse hotelIdListResponse = new HotelIdListResponse();
            hotelIdListResponse.setCurrentPage(request.getPageNo() + 1);
            hotelIdListResponse.setTotalPage(totalPages);
            hotelIdListResponse.setTotal(totalCount);
            hotelIdListResponse.setHotelIds(uniqueHotelIds);
            responseResult.setBussinessResponse(hotelIdListResponse);
            return responseResult;
        }
    }

    /**
     * 酒店实时产品查询
     */
    @Override
    public ResponseResult<ProductDetailResponse> queryProductDetail(ProductDetailRequest request) {
        ResponseResult<ProductDetailResponse> responseResult = new ResponseResult<>(DhubReturnCodeEnum.SUCCESS.no, DhubReturnCodeEnum.SUCCESS.description);
        ProductDetailResponse productDetailResponse = queryProductDetailTmp(request);
        //处理有钟点房的数据 需要合并到日历房数据中进行返回
        if (CollUtilX.isNotEmpty(productDetailResponse.getHourlyRoomItems())) {
            List<RoomItem> roomItems = productDetailResponse.getRoomItems();
            if (roomItems == null) {
                roomItems = new ArrayList<>();
                productDetailResponse.setRoomItems(roomItems);
            }
            Map<Long, List<RoomItem>> hourlyRoomItemsMap = productDetailResponse.getHourlyRoomItems()
                    .stream()
                    .collect(Collectors.groupingBy(RoomItem::getRoomId));
            for (RoomItem tmpRoomItem : roomItems) {
                List<RoomItem> hourlyRoomItems = hourlyRoomItemsMap.remove(tmpRoomItem.getRoomId());
                if (tmpRoomItem.getProducts() == null) {
                    tmpRoomItem.setProducts(new ArrayList<>());
                }
                if (hourlyRoomItems != null && !hourlyRoomItems.isEmpty()) {
                    tmpRoomItem.getProducts().addAll(hourlyRoomItems.get(0).getProducts());
                }
            }
            roomItems.addAll(hourlyRoomItemsMap.values().stream()
                    .map(items -> items.get(0))
                    .collect(Collectors.toList()));
        }
        productDetailResponse.setHourlyRoomItems(null);
        responseResult.setBussinessResponse(productDetailResponse);
        return responseResult;
    }

    @Override
    public Response<ProductDetailResponse> queryHotelProductList(ProductDetailRequest request) {
        // 校验参数
        checkQueryHotelProductListParam(request);

        // 判断入离时间是否大于当前时间
        Date checkInDate = DateUtilX.stringToDate(request.getCheckInDate());
        Date checkOutDate = DateUtilX.stringToDate(request.getCheckOutDate());
        if (DateUtilX.compare(DateUtilX.getCurrentDate(), checkInDate) > 0 || DateUtilX.compare(DateUtilX.getCurrentDate(), checkOutDate) > 0) {
            return Response.success(null);
        }

        // 查询酒店信息
        HotelInfoCollectionReq infoReq = new HotelInfoCollectionReq();
        infoReq.setHotelIds(Collections.singletonList(request.getHotelId()));
        // 设置语言
        if (StrUtilX.isEmpty(request.getLanguage())) {
            infoReq.setLanguageType(LanguageTypeEnum.en_US.getValue());
        } else {
            infoReq.setLanguageType(request.getLanguage());
        }
        infoReq.setSettings(Collections.singletonList(BaseHotelInfoUrl.rooms));
        Response<HotelInfoCollectionDTO> hotelDetailResp = hotelRemote.queryHotelInfo(infoReq);
        if (hotelDetailResp.isError()) {
            throw new SysException(ErrorCodeEnum.OUTER_IF_EXCEPTION);
        }
        if (hotelDetailResp.getModel() == null) {
            throw new SysException(ErrorCodeEnum.NOT_EXIST_HOTEL);
        }
        HotelSummary hotelSummary = JSON.parseObject(JSON.toJSONString(hotelDetailResp.getModel()), HotelSummary.class);
        Map<Long, RoomSummary> basicRoomDetail;
        if (CollUtilX.isNotEmpty(hotelSummary.getRooms())) {
            basicRoomDetail = hotelSummary.getRooms().stream().collect(Collectors.toMap(RoomSummary::getRoomtypeId, Function.identity(), (s1, s2) -> s2));
        } else {
            basicRoomDetail = new HashMap<>();
        }

        request.setShowSupplyName(1);// 显示供应商名称
        //查询分销商
        AgentAccountConfig agentAccountConfig = JSON.parseObject(RedisTemplateX.hashGet(RedisKey.AGENT_ACCOUNT_CONFIG, request.getAgentCode()).toString(), AgentAccountConfig.class);
        if (agentAccountConfig.getDomesticOrOverseas() != null && agentAccountConfig.getDomesticOrOverseas().compareTo(2) == 0) {
            request.setSupplyType("2");// 全球
        } else {
            request.setSupplyType("1");// 国内
        }
        ProductDetailResponse productDetailResponse = queryProductDetailTmp(request);
        // 过滤房型数据
        if (CollUtilX.isNotEmpty(productDetailResponse.getRoomItems()) && request.getRoomId() != null) {
            List<RoomItem> collect = productDetailResponse.getRoomItems().stream().filter(item -> item.getRoomId() != null &&
                    ((item.getRoomId().equals(request.getRoomId()) && basicRoomDetail.containsKey(request.getRoomId())) || roomIdISVirtualRoom(String.valueOf(item.getRoomId())))).peek(item -> {
                RoomSummary roomSummary = basicRoomDetail.get(request.getRoomId());
                if (roomSummary != null) {
                    item.setRoomName(roomSummary.getRoomtypeName());
                }
            }).collect(toList());
            productDetailResponse.setRoomItems(collect);
        }
        //处理有钟点房的数据 需要合并到日历房数据中进行返回
        if (CollUtilX.isNotEmpty(productDetailResponse.getHourlyRoomItems())) {
            List<RoomItem> roomItems = productDetailResponse.getRoomItems();
            if (CollUtilX.isEmpty(roomItems)) {
                roomItems = new ArrayList<>();
                productDetailResponse.setRoomItems(roomItems);
            }
            Map<Long, List<RoomItem>> hourlyRoomItemsMap = productDetailResponse.getHourlyRoomItems()
                    .stream()
                    .collect(Collectors.groupingBy(RoomItem::getRoomId));
            for (RoomItem tmpRoomItem : roomItems) {
                List<RoomItem> hourlyRoomItems = hourlyRoomItemsMap.remove(tmpRoomItem.getRoomId());
                if (tmpRoomItem.getRoomId() == null) {
                    continue;
                }
                if (request.getRoomId() != null && !tmpRoomItem.getRoomId().equals(request.getRoomId())) {
                    continue;
                }
                if (tmpRoomItem.getProducts() == null) {
                    tmpRoomItem.setProducts(new ArrayList<>());
                }
                if (hourlyRoomItems != null && !hourlyRoomItems.isEmpty()) {
                    tmpRoomItem.getProducts().addAll(hourlyRoomItems.get(0).getProducts());
                }
            }
            roomItems.addAll(hourlyRoomItemsMap.values().stream()
                    .map(items -> items.get(0))
                    .collect(Collectors.toList()));
        }
        return Response.success(productDetailResponse);
    }

    /**
     * 根据房型id前缀判断是否虚拟房型
     * 虚拟房型id 则必须是hashCol 开头
     * 并且是固定长度
     */
    public static boolean roomIdISVirtualRoom(String roomId) {
        return roomId.startsWith("99") && roomId.length() == 9;
    }

    @Override
    public Response<List<FuzzySupplierDTO>> getAgentSupplyAvailable(FuzzyQueryDTO request) {
        if (StrUtilX.isEmpty(request.getAgentCode())) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_AGENTCODE);
        }
        // 查询供应商白名单
        List<String> supplyCodes = getAgentSupplyAvailable(request.getAgentCode());
        if (CollUtilX.isNotEmpty(supplyCodes)) {
            Response<List<FuzzySupplierDTO>> listResponse = fuzzyQueryRemote.querySupplierList(request);
            if (listResponse.isSuccess() && CollUtilX.isNotEmpty(listResponse.getModel())) {
                List<FuzzySupplierDTO> collect = listResponse.getModel().stream().filter(item -> supplyCodes.contains(item.getSupplierCode())).collect(toList());
                return Response.success(collect);
            }
        }
        return Response.success(new ArrayList<>());
    }

    @Override
    public void autoUpdateAgentLowestPriceTask(String param) {
        Map<Object, Object> configMap = RedisTemplateX.hGetAll(RedisKey.AUTO_UPDATE_LOWEST_PRICE_CITY_CONFIG_KEY);
        if (configMap.isEmpty()) {
            return;
        }

        // 生成日期区间列表
        Date currentDate = DateUtilX.getCurrentDate();
        List<Date> dateIntervals = generateDateIntervals(currentDate);

        for (Object agentCodeObj : configMap.keySet()) {
            String agentCode = (String) agentCodeObj;
            String hotelId;
            if (StrUtilX.isNotEmpty(param)) {
                hotelId = param;
            } else {
                hotelId = RedisTemplateX.lRightPop(RedisKey.AUTO_UPDATE_LOWEST_PRICE_CITY_HOTEL_ID_KEY + agentCode);
            }
            if (StrUtilX.isEmpty(hotelId)) {
                continue;
            }

            // 获取客户信息配置
            AgentAccountConfig agentAccountConfig = getAgentAccountConfig(agentCode);
            if (agentAccountConfig == null) {
                continue;
            }

            // 获取客户可见性供应商
            List<String> supplyCodes = getAgentSupplyAvailable(agentCode);
            if (CollUtilX.isEmpty(supplyCodes)) {
                continue;
            }

            // 获取供应商信息
            List<SupplierAddDTO> suppliers = getSuppliersFromCache(supplyCodes);
            if (CollUtilX.isEmpty(suppliers)) {
                continue;
            }

            // 过滤供应商
            supplyCodes = suppliers.stream()
                    .filter(supplier -> isHotelValid(supplier, Long.parseLong(hotelId)))
                    .map(SupplierAddDTO::getSupplierCode)
                    .collect(Collectors.toList());
            if (CollUtilX.isEmpty(supplyCodes)) {
                continue;
            }

            for (int i = 0; i < 7; i++) {
                QueryProductInfoRequest request = buildQueryRequest(
                        hotelId,
                        dateIntervals.get(i),
                        dateIntervals.get(i + 1),
                        supplyCodes,
                        agentAccountConfig
                );
                supplyDirectShubProductRemote.queryProductList(request);
            }
        }
    }

    /**
     * 生成日期区间列表
     */
    private List<Date> generateDateIntervals(Date currentDate) {
        List<Integer> intervals = Arrays.asList(1, 2, 3, 6, 9, 12, 15);
        List<Date> dates = new ArrayList<>();
        dates.add(currentDate);
        for (int interval : intervals) {
            dates.add(DateUtilX.getDate(currentDate, interval, 0));
        }
        return dates;
    }

    /**
     * 从缓存中获取供应商信息
     */
    private List<SupplierAddDTO> getSuppliersFromCache(List<String> supplierCodes) {
        return StrUtilX.parseObject(RedisTemplateX.hMultiGet(RedisKey.SUPPLY_ACCOUNT_CONFIG, supplierCodes), new TypeReference<List<SupplierAddDTO>>() {
        });
    }

    /**
     * 判断酒店是否匹配
     */
    private boolean isHotelValid(SupplierAddDTO supplier, Long hotelId) {
        return supplier.getSupplierForm() == null || supplier.getHotelId() == null ||
                supplier.getSupplierForm() != 1 || hotelId.equals(supplier.getHotelId());
    }

    /**
     * 构建查询请求
     */
    private QueryProductInfoRequest buildQueryRequest(String hotelId, Date checkInDate, Date checkOutDate,
                                                      List<String> supplyCodes, AgentAccountConfig agentAccountConfig) {
        QueryProductInfoRequest request = new QueryProductInfoRequest();
        request.setSpHotelId(hotelId);
        request.setCheckInDate(DateUtilX.dateToString(checkInDate));
        request.setCheckOutDate(DateUtilX.dateToString(checkOutDate));
        request.setGuestQuantity(1);
        request.setRoomQty(1);
        request.setSupplyCodes(new HashSet<>(supplyCodes));
        request.setSupplyType(String.valueOf(agentAccountConfig.getDomesticOrOverseas()));
        List<com.tiangong.supply.direct.remote.request.RoomGuestNumber> roomGuestNumbers = new ArrayList<>();
        com.tiangong.supply.direct.remote.request.RoomGuestNumber roomGuestNumber = new com.tiangong.supply.direct.remote.request.RoomGuestNumber();
        roomGuestNumber.setRoomIndex(1);
        roomGuestNumber.setAdultNum(1);
        roomGuestNumbers.add(roomGuestNumber);
        request.setRoomGuestNumbers(roomGuestNumbers);
        request.setRequestId(UUID.randomUUID().toString());
        request.setSponsor("模拟客户");
        return request;
    }

    /**
     * 校验参数
     */
    private void checkQueryHotelProductListParam(ProductDetailRequest request) {
        if (request.getHotelId() == null) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_HOTELID);
        }
        if (StrUtilX.isEmpty(request.getCheckInDate())) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_CHECKINDATE);
        }
        if (StrUtilX.isEmpty(request.getCheckOutDate())) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_CHECKOUTDATE);
        }
        if (StrUtilX.isEmpty(request.getAgentCode())) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_AGENTCODE);
        }
        if (request.getRoomNum() == null) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_ROOMNUM);
        }
        if (request.getGuestQuantity() == null) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_GUESTQUANTITY);
        }
        if (CollUtilX.isEmpty(request.getRoomGuestNumbers())) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_ROOMNUMBER);
        }
    }

    /**
     * 获取产品信息
     */
    public ProductDetailResponse queryProductDetailTmp(ProductDetailRequest request) {
        ProductDetailResponse productDetailResponse = new ProductDetailResponse();

        // 处理供应商
        Map<String, SupplierAddDTO> supplyMap = handleSuppliers(request);
        if (supplyMap == null || supplyMap.isEmpty()) {
            return productDetailResponse;
        }
        // 供应商酒店黑白名单筛选
        List<String> filterWhiteList = proOrgHotelAvailableService.filterSuppliersByHotelAvailableRule(request.getHotelId(), new ArrayList<>(supplyMap.keySet()));
        if (CollUtilX.isEmpty(filterWhiteList)) {
            return productDetailResponse;
        }

        // 根据筛选后的白名单过滤supplyMap，只保留在筛选后白名单中的供应商
        Iterator<Map.Entry<String, SupplierAddDTO>> iterator = supplyMap.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, SupplierAddDTO> entry = iterator.next();
            if (!filterWhiteList.contains(entry.getKey())) {
                iterator.remove();
            }
        }
        // 直连供应商
        Map<String, SupplierAddDTO> apiSupplyMap = supplyMap.values().stream()
                .filter(item -> item.getSupplierType() != null
                        && SupplierTypeEnum.API.getKey() == item.getSupplierType()
                        && StrUtilX.isNotEmpty(item.getSupplierCode()))
                .collect(Collectors.toMap(
                        SupplierAddDTO::getSupplierCode, // key
                        item -> item // value
                ));
        // 自签供应商
        Map<String, SupplierAddDTO> selfSupplyMap = supplyMap.values().stream()
                .filter(item -> item.getSupplierType() != null
                        && SupplierTypeEnum.SELF.getKey() == item.getSupplierType()
                        && StrUtilX.isNotEmpty(item.getSupplierCode()))
                .collect(Collectors.toMap(
                        SupplierAddDTO::getSupplierCode, // key
                        item -> item // value
                ));

        // 查询分销商
        AgentAccountConfig agentConfig = JSON.parseObject(RedisTemplateX.hashGet(RedisKey.AGENT_ACCOUNT_CONFIG, request.getAgentCode()).toString(), AgentAccountConfig.class);

        // 获取商家信息
        OrgDTO orgInfo = CommonInitializer.getOrgInfo();
        int orgCurrency = Integer.parseInt(orgInfo.getOrgCurrency());

        Date start3 = new Date();
        // 获取产品信息
        ProductDetailResponse productDetailResponseTmp = CommonUtils.parallelHandle(
                productDetailResponse,
                request,
                () -> agreement(request, selfSupplyMap, orgCurrency, agentConfig),
                () -> direct(request, apiSupplyMap, orgCurrency, agentConfig),
                this::detailMerge,
                processingProductInfoExecutor);
        if (productDetailResponseTmp == null) {
            return productDetailResponse;
        }
        saveSlsLog("排查慢问题：查询供应商和本地产品数据", start3, request.getRequestId());

        //log.info("ProductServiceImpl--queryProductDetail--productDetailResponseTmp:{}, requestId={}", JSON.toJSONString(productDetailResponseTmp), request.getRequestId());
        List<RoomItem> roomItems = productDetailResponseTmp.getRoomItems();
        if (CollUtilX.isEmpty(roomItems)) {
            return productDetailResponseTmp;
        }

        // 临时存放酒店信息
        final HotelInfoCollectionDTO[] hotelInfo = {null};
        // 临时存放房型名称
        Map<Long, String> roomIdNameMap = new HashMap<>();
        // 临时存放房型最大入住人数据
        Map<Long, Integer> roomIdMaxPersonMap = new HashMap<>();
        // 临时存放房型窗型
        Map<Long, Integer> roomIdWindowTypeMap = new HashMap<>();
        // 临时存放房型床型信息
        Map<Long, List<BedTypesDetailDto>> roomIdBedMap = new HashMap<>();
        // 临时存放房型床型名称描述
        Map<Long, String> roomIdBedInfoMap = new HashMap<>();
        // 临时存放提示信息
        Map<String, List<TipsResp>> tipMap = new HashMap<>();

        // 获取酒店信息
        CompletableFuture<Response<HotelInfoCollectionDTO>> hotelInfoFuture = CompletableFuture.supplyAsync(() -> getHotelInfo(request), processingProductInfoExecutor);

        Date start6 = new Date();
        try {
            Response<HotelInfoCollectionDTO> hotelInfoResponse = hotelInfoFuture.get(2000, TimeUnit.MILLISECONDS); // 等待远程调用完成
            if (hotelInfoResponse.isSuccess() && hotelInfoResponse.getModel() != null) {
                hotelInfo[0] = hotelInfoResponse.getModel();
                if (hotelInfo[0] != null && CollUtilX.isNotEmpty(hotelInfo[0].getRooms())) {
                    // 并行处理
                    hotelInfo[0].getRooms().parallelStream().forEach(roomInfo -> {
                        // 处理map数据，存放到临时对象
                        handleMapData(roomInfo, roomIdNameMap, roomIdWindowTypeMap, roomIdBedInfoMap, roomIdBedMap, roomIdMaxPersonMap, request.getLanguage());
                    });
                }
                if (hotelInfo[0] != null && Objects.nonNull(hotelInfo[0].getLocation())) {
                    try {
                        LocationDTO location = hotelInfo[0].getLocation();
                        //根据国家编码和省份编码获取时区
                        AreaDataTimeZoneReqDTO dto = new AreaDataTimeZoneReqDTO();
                        dto.setCountryCode(location.getCountry());
                        dto.setProvinceCode(location.getProvince());
                        dto.setDateTime(request.getCheckInDate());
                        //log.info("查询时区传递的参数是：{}", dto);
                        Response<String> dateTimeZone = TimeZoneUtil.getDateTimeZone(dto);
                        //log.info("时区数据是：{}", dateTimeZone);
                        if (dateTimeZone.isSuccess()) {
                            productDetailResponseTmp.setTimeZone(dateTimeZone.getModel());
                        }
                    } catch (Exception e) {
                        log.error("获取时区异常。。。。。。", e);
                    }

                }
            }
        } catch (InterruptedException | ExecutionException e) {
            // 异常处理
            log.error("处理map数据，存放到临时对象异常, requestId={}", request.getRequestId(), e);
        } catch (TimeoutException e) {
            log.error("获取酒店信息操作超时！");
        } catch (Exception e) {
            log.error("获取酒店信息系统异常", e);
        }
        saveSlsLog("排查慢问题：设置酒店信息到临时对象", start6, request.getRequestId());

        Date start7 = new Date();
        // 查询提示信息
        getTip(tipMap, request.getLanguage());
        saveSlsLog("排查慢问题：查询提示信息", start7, request.getRequestId());

        Date start8 = new Date();
        // 查询分销商配置（是否需要输出原始协议价）
        AgentApiConfigDTO agentApiConfigDTO = productMapper.agentApiConfig(request.getAgentCode(), request.getSupplyType());
        // 判断供应商是否是eps的
        String epsSupplier = RedisTemplateX.get(RedisKey.EPS_SUPPLIER);

        // 并行处理数据
        for (RoomItem roomItem : roomItems) {
            if (CollUtilX.isNotEmpty(roomItem.getProducts())) {
                // 过滤房型在基础信息不存在并且不是虚拟房型
                if (null == roomIdNameMap.get(roomItem.getRoomId()) && !roomIdISVirtualRoom(String.valueOf(roomItem.getRoomId()))) {
                    roomItem.setProducts(null);
                    continue;
                }
                // 不是虚拟房型，房型名称取基础信息的
                if (!roomIdISVirtualRoom(String.valueOf(roomItem.getRoomId()))) {
                    roomItem.setRoomName(roomIdNameMap.get(roomItem.getRoomId()));
                }
                // 检查当前产品的价格项，确保所有价格均大于0
                List<ProductDetail> collect = roomItem.getProducts().stream().filter(product ->
                        product.getPriceItems().stream().noneMatch(priceItem -> priceItem.getSalePrice() != null &&
                                priceItem.getSalePrice().compareTo(BigDecimal.ZERO) <= 0)
                ).collect(toList());
                // 判断产品是否为空
                if (CollUtilX.isEmpty(collect)) {
                    roomItem.setProducts(null);
                } else {
                    for (ProductDetail product : collect) {
                        // 1、修改发票模式
                        setInvoiceModelInfo(product);

                        // 2、设置免责声明
                        setDisclaimersInfo(product, epsSupplier);

                        // 3、设置供应商标签(在前面合并产品的时候去处理了)
                        //setSupplierLabelInfo(product, request.getAgentCode());

                        // 4、设置提示信息
                        product.setTipsList(tipMap.get(product.getSupplyCode()));

                        // 5、设置窗型信息
                        setWindowTypeInfo(product, roomIdWindowTypeMap, hotelInfo, roomItem.getRoomId());

                        // 6、设置床型信息
                        setBedTypesInfo(product, roomIdBedMap, roomIdBedInfoMap, roomItem.getRoomId(), request.getLanguage());

                        // 如果供应商没有返回入住最大人数就去基础信息的最大入住人数
                        if (product.getMaxGuestPerson() == null) {
                            product.setMaxGuestPerson(roomIdMaxPersonMap.get(roomItem.getRoomId()));
                        }
                        // 添加供货单查询产品需要输出底价
                        if (agentApiConfigDTO == null || agentApiConfigDTO.getOriginalProtocolPriceSwitch() == 0) {
                            if (product.getPriceItems() != null) {
                                for (PriceItem priceItem : product.getPriceItems()) {
                                    // 添加供货单查询产品时需要输出
                                    if (request.getShowSupplyName() == null || request.getShowSupplyName() == 0) {
                                        priceItem.setBasePrice(null);
                                        priceItem.setBaseCurrency(null);
                                    }
                                }
                            }
                        }
                    }
                    roomItem.setProducts(collect);

                    // 排序（可订大于不可定、价格低大于价格高）
                    roomItem.getProducts().sort(Comparator.comparing(ProductDetail::getAvgPrice));

                    // 设置最低价
                    roomItem.setLowPrice(roomItem.getProducts().get(0).getAvgPrice());
                }
            }
        }
        //过滤产品为空的房型并房型排序（价格低大于价格高）
        List<RoomItem> collect = roomItems.stream().filter(item -> CollUtilX.isNotEmpty(item.getProducts())).sorted(Comparator.comparing(RoomItem::getLowPrice)).collect(toList());
        productDetailResponseTmp.setRoomItems(collect);
        saveSlsLog("排查慢问题：设置酒店信息到产品对象并排序，productDetailResponseTmp=" + JSONUtil.toJsonStr(productDetailResponseTmp), start8, request.getRequestId());
        return productDetailResponseTmp;
    }

    /**
     * 处理供应商
     */
    private Map<String, SupplierAddDTO> handleSuppliers(ProductDetailRequest request) {
        // 校验客户编码
        validateAgentCode(request);

        // 根据分销商编码查询供应商白名单
        List<String> whiteList = getSupplierWhiteList(request.getAgentCode());
        if (CollUtilX.isEmpty(whiteList)) {
            return null;
        }

        // 如果传入了供应商编码，则只处理该供应商
        if (StrUtilX.isNotEmpty(request.getSupplyCode())) {
            if (whiteList.contains(request.getSupplyCode())) {
                whiteList = Collections.singletonList(request.getSupplyCode());
            } else {
                return null;
            }
        }

        // 从缓存中获取供应商信息
        List<SupplierAddDTO> suppliers = getSuppliersFromCache(whiteList);
        // 校验供应商存在
        validateSuppliersExist(suppliers);

        // 判断入住日期是否超过1天
        if (isCheckInDateInvalid(request.getCheckInDate())) {
            // 记录日志
            logEarlyCheckInAttempt(request);
            return null;
        }

        // 过滤无效供应商
        return filterValidSuppliers(suppliers, request.getHotelId(), request.getCheckInDate());
    }

    /**
     * 校验客户编码
     */
    private void validateAgentCode(ProductDetailRequest request) {
        if (StrUtilX.isEmpty(request.getAgentCode())) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_AGENTWHITELIST);
        }
    }

    /**
     * 根据分销商编码查询供应商白名单
     */
    private List<String> getSupplierWhiteList(String agentCode) {
        String supplyCodes = (String) RedisTemplateX.hashGet(RedisKey.AGENT_SUPPLY_KEY, agentCode);
        List<String> supplyCodeList = StrUtilX.stringToList(supplyCodes, ",");
        if (CollUtilX.isEmpty(supplyCodeList)) {
            return null;
        }
        return supplyCodeList;
    }

    /**
     * 校验供应商存在
     */
    private void validateSuppliersExist(List<SupplierAddDTO> suppliers) {
        if (CollUtil.isEmpty(suppliers)) {
            log.error("供应商缓存不存在");
            throw new SysException(ErrorCodeEnum.SUPPLY_CHECK_NOT_EXIST);
        }
    }

    /**
     * 判断入住日期是否超过1天
     */
    private boolean isCheckInDateInvalid(String checkInDate) {
        return DateUtilX.getDay(DateUtilX.stringToDate(checkInDate), DateUtilX.getCurrentDate()) > 1;
    }

    /**
     * 记录日志
     */
    private void logEarlyCheckInAttempt(ProductDetailRequest request) {
        Map<String, String> map = new HashMap<>();
        map.put(SlsEnum.LEVEL.getType(), SlsLogLevelEnum.INFO.getLevel());
        map.put(SlsEnum.NAME.getType(), "queryProductInfo");
        map.put(SlsEnum.MESSAGE.getType(), "查询供应商产品，入住时间小于当前时间两天");

        Date currentDate = DateUtilX.getCurrentDate();
        map.put("request", JSONUtil.toJsonStr(request));
        map.put("start", DateUtilX.dateToString(currentDate, DateUtilX.hour_ms_format));
        map.put("data", "currentDate=" + DateUtilX.dateToString(currentDate) + "checkInData=" + request.getCheckInDate());
        slsLoggerUtil.saveLog(map, "isCheckInDateInvalid", "tingong-product-server");
    }

    /**
     * 过滤无效供应商
     */
    private Map<String, SupplierAddDTO> filterValidSuppliers(List<SupplierAddDTO> suppliers, Long hotelId, String checkInDate) {
        // 预计算固定参数
        final LocalTime currentTime = LocalTime.now();
        final long daysBetween = DateUtilX.getDay(DateUtilX.stringToDate(checkInDate), DateUtilX.getCurrentDate());

        Map<String, SupplierAddDTO> validSuppliers = new HashMap<>(calculateInitialCapacity(suppliers.size()));
        for (SupplierAddDTO supplier : suppliers) {
            if (isValidSupplier(supplier, hotelId, daysBetween, currentTime)) {
                String supplierCode = supplier.getSupplierCode();
                if (validSuppliers.containsKey(supplierCode)) {
                    log.warn("发现重复供应商编码: {}，已覆盖旧值", supplierCode);
                }
                validSuppliers.put(supplierCode, supplier);
            }
        }
        return validSuppliers;
    }

    /**
     * 计算HashMap初始容量（避免扩容）
     * 公式: (expectedSize / 0.75) + 1
     */
    private static int calculateInitialCapacity(int expectedSize) {
        return (int) Math.ceil(expectedSize / 0.75) + 1;
    }

    /**
     * 判断供应商是否可用
     */
    private boolean isValidSupplier(SupplierAddDTO supplier, Long hotelId, long days, LocalTime now) {
        return isHotelValid(supplier, hotelId)
                && isSupplierAvailable(supplier)
                && meetsTimingCriteria(supplier, days, now);
    }

    /**
     * 判断供应商是否可用
     */
    private boolean isSupplierAvailable(SupplierAddDTO supplier) {
        return supplier.getAvailableStatus() != null && supplier.getAvailableStatus() == 1;
    }

    /**
     * 判断供应商是否支持早订
     */
    private boolean meetsTimingCriteria(SupplierAddDTO supplier, long days, LocalTime now) {
        if (supportsEarlyMorning(supplier)) {
            return days < 1 || (now.isAfter(LocalTime.MIDNIGHT) && now.isBefore(LocalTime.of(6, 0)));
        }
        return days < 1;
    }

    /**
     * 判断供应商是否支持早订
     */
    private boolean supportsEarlyMorning(SupplierAddDTO supplier) {
        return supplier.getIsSupportEarlyMorningRoomOrder() != null
                && supplier.getIsSupportEarlyMorningRoomOrder() == 1;
    }

    /**
     * 获取酒店信息
     */
    private Response<HotelInfoCollectionDTO> getHotelInfo(ProductDetailRequest request) {
        Date start4 = new Date();
        // 查询基本信息
        HotelInfoCollectionReq hotelInfoReq = new HotelInfoCollectionReq();
        hotelInfoReq.setHotelIds(Collections.singletonList(request.getHotelId()));
        if (StrUtilX.isNotEmpty(request.getLanguage())) {
            hotelInfoReq.setLanguageType(request.getLanguage());
        } else {
            hotelInfoReq.setLanguageType(LanguageTypeEnum.zh_CN.getValue());
        }
        List<String> settings = new ArrayList<>();
        settings.add(BaseHotelInfoUrl.rooms);
        settings.add(BaseHotelInfoUrl.location);
        hotelInfoReq.setSettings(settings);
        Response<HotelInfoCollectionDTO> hotelInfoResp = hotelRemote.queryHotelInfo(hotelInfoReq);

        saveSlsLog("排查慢问题：查询酒店信息，hotelInfoReq=" + JSONUtil.toJsonStr(hotelInfoReq), start4, request.getRequestId());
        return hotelInfoResp;
    }

    /**
     * 处理map数据
     *
     * @param roomInfo            房型信息
     * @param roomIdNameMap       临时存放房型id
     * @param roomIdWindowTypeMap 临时存放房型床型
     * @param roomIdBedInfoMap    临时存放房型床型信息
     * @param roomIdBedMap        临时存放房型床型
     * @param roomIdMaxPersonMap  临时存放房型最大人数
     * @param language            语言
     */
    private void handleMapData(RoomtypeDTO roomInfo, Map<Long, String> roomIdNameMap, Map<Long, Integer> roomIdWindowTypeMap,
                               Map<Long, String> roomIdBedInfoMap, Map<Long, List<BedTypesDetailDto>> roomIdBedMap, Map<Long, Integer> roomIdMaxPersonMap, String language) {
        String basicBedInfo = "";
        StringBuilder basicBedInfoBuilder = new StringBuilder();
        if (StrUtilX.isNotEmpty(roomInfo.getRoomtypeName())) {
            roomIdNameMap.put(roomInfo.getRoomtypeId(), roomInfo.getRoomtypeName());
        }
        if (null != roomInfo.getWindowDetail()) {
            roomIdWindowTypeMap.put(roomInfo.getRoomtypeId(), roomInfo.getWindowDetail());
        }
        List<RoomDetailBedDTO> beds = new ArrayList<>();
        if (CollUtilX.isNotEmpty(roomInfo.getRoomBed())) {
            beds = new ArrayList<>(roomInfo.getRoomBed());
        }
        if (CollUtilX.isNotEmpty(roomInfo.getLivingBed())) {
            beds.addAll(roomInfo.getLivingBed());
        }
        if (CollUtilX.isNotEmpty(beds)) {
            List<BedTypesDetailDto> bedList = new ArrayList<>();
            for (RoomDetailBedDTO bedDTO : beds) {
                List<BedTypesDetailDto> bedTypesDetailDtos = bedDTO.getBedGroups().stream()
                        .filter(item -> CollUtilX.isNotEmpty(item.getBedInfos()))
                        .map(itemBedInfo -> {
                            // 循环中的逻辑代码
                            BedTypesDetailDto bedTypesDetailDto = new BedTypesDetailDto();
                            List<BedInfoDto> collect = itemBedInfo.getBedInfos().stream().filter(item -> StrUtilX.isNotEmpty(item.getBedCode())).map(bedInfo -> {
                                BedInfoDto bedInfoDto = new BedInfoDto();
                                bedInfoDto.setBedTypeCode(bedInfo.getBedCode());
                                bedInfoDto.setBedTypeName(bedNameConfig.getBedTypeName(bedInfo.getBedCode(), language));
                                bedInfoDto.setBedNum(bedInfo.getBedNum());
                                return bedInfoDto;
                            }).collect(toList());
                            StringBuilder bedInfoDesc = new StringBuilder();
                            for (int i = 0; i < collect.size(); i++) {
                                BedInfoDto bedInfoDto = collect.get(i);
                                if (bedInfoDto.getBedNum() != null && bedInfoDto.getBedNum() > 0) {
                                    if (LanguageTypeEnum.zh_CN.getValue().equals(language)) {
                                        bedInfoDesc.append(bedInfoDto.getBedNum()).append("张");
                                    } else {
                                        bedInfoDesc.append(bedInfoDto.getBedNum()).append(" ");
                                    }
                                }

                                bedInfoDesc.append(bedInfoDto.getBedTypeName());
                                if (bedInfoDto.getBedNum() != null && bedInfoDto.getBedNum() > 1 && !LanguageTypeEnum.zh_CN.getValue().equals(language)) {
                                    bedInfoDesc.append("s");
                                }
                                if (i != collect.size() - 1) {
                                    if (LanguageTypeEnum.zh_CN.getValue().equals(language)) {
                                        bedInfoDesc.append("和");
                                    } else {
                                        bedInfoDesc.append(" and ");
                                    }
                                }
                            }
                            bedTypesDetailDto.setBedInfoDesc(bedInfoDesc.toString());
                            bedTypesDetailDto.setBedInfos(collect);
                            return bedTypesDetailDto;
                        }).collect(toList());
                if (CollUtilX.isNotEmpty(bedTypesDetailDtos)) {
                    // 组装基础信息床型
                    for (int i = 0; i < bedTypesDetailDtos.size(); i++) {
                        BedTypesDetailDto dto = bedTypesDetailDtos.get(i);
                        if (StrUtilX.isEmpty(dto.getBedInfoDesc())) {
                            continue;
                        }
                        basicBedInfoBuilder.append(dto.getBedInfoDesc());
                        if (i != bedTypesDetailDtos.size() - 1) {
                            if (LanguageTypeEnum.zh_CN.getValue().equals(language)) {
                                basicBedInfoBuilder.append("或");
                            } else {
                                basicBedInfoBuilder.append(" or ");
                            }
                        }
                        basicBedInfo = basicBedInfoBuilder.toString();

                        bedList.add(dto);
                    }
                    if (StrUtilX.isNotEmpty(basicBedInfo) && basicBedInfo.endsWith("或")) {
                        basicBedInfo = basicBedInfo.substring(0, basicBedInfo.length() - 1);
                    } else if (StrUtilX.isNotEmpty(basicBedInfo) && basicBedInfo.endsWith(" or ")) {
                        basicBedInfo = basicBedInfo.substring(0, basicBedInfo.length() - 3);
                    }
                }
                roomIdBedInfoMap.put(roomInfo.getRoomtypeId(), basicBedInfo);
            }

            // 存放房型床型
            if (CollUtilX.isNotEmpty(bedList)) {
                roomIdBedMap.put(roomInfo.getRoomtypeId(), bedList);
            }
        }
        // 存放房型最大入住人数
        if (roomInfo.getMaxPerson() != null) {
            roomIdMaxPersonMap.put(roomInfo.getRoomtypeId(), roomInfo.getMaxPerson());
        }
    }

    /**
     * 获取提示信息
     */
    private void getTip(Map<String, List<TipsResp>> tipMap, String language) {
        TipsDTO tipsDTO = new TipsDTO();
        if (StrUtilX.isNotEmpty(language)) {
            tipsDTO.setLanguage(language);
        } else {
            tipsDTO.setLanguage(LanguageTypeEnum.en_US.getValue());
        }
        Response<List<TipsResp>> tipList = tipsRemote.findTipList(tipsDTO);
        if (tipList.isSuccess() && Objects.nonNull(tipList.getModel())) {
            for (TipsResp tipsResp : tipList.getModel()) {
                if (tipMap.containsKey(tipsResp.getSupplierCode())) {
                    tipMap.get(tipsResp.getSupplierCode()).add(tipsResp);
                } else {
                    List<TipsResp> respList = new ArrayList<>();
                    respList.add(tipsResp);
                    tipMap.put(tipsResp.getSupplierCode(), respList);
                }
            }
        }
    }

    /**
     * 设置发票模式，到店付的发票模式必须是酒店前台开票
     */
    private void setInvoiceModelInfo(ProductDetail product) {
        if (product.getPayAtHotelFlag() != null && product.getPayAtHotelFlag() == 1) {
            product.setInvoiceModel(2);
        }
    }

    /**
     * 设置免责声明
     */
    private void setDisclaimersInfo(ProductDetail product, String epsSupplier) {
        if (epsSupplier != null && epsSupplier.contains(product.getSupplyCode())) {
            product.setDisclaimers(1);
        } else {
            product.setDisclaimers(0);
        }
    }

    /**
     * 设置供应商标签
     */
    private void setSupplierLabelInfo(ProductDetail product, String agentCode) {
        Object supplierLabel = RedisTemplateX.hGet(RedisKey.AGENT_SUPPLIER_LABEL, agentCode + "_" + product.getSupplyCode());
        if (Objects.nonNull(supplierLabel)) {
            product.setSupplierLabel(Integer.parseInt(supplierLabel.toString()));
        } else {
            product.setSupplierLabel(product.getProductLabel());
        }
    }

    /**
     * 设置窗型信息
     */
    private void setWindowTypeInfo(ProductDetail product, Map<Long, Integer> roomIdWindowTypeMap, HotelInfoCollectionDTO[] hotelInfo, Long roomId) {
        if (null == product.getWindowType()) {
            Integer windowType = roomIdWindowTypeMap.get(roomId);
            product.setWindowType(windowType != null ? windowType : 1); // 默认为部分有窗
        } else if (product.getWindowType().equals(2)) {
            if (null != hotelInfo[0] && null != hotelInfo[0].getHotelStar()
                    && null != hotelInfo[0].getHotelStar().getHotelStar()
                    && Integer.parseInt(hotelInfo[0].getHotelStar().getHotelStar()) >= 69) {
                product.setWindowType(1); // 默认为部分有窗
            }
        }
    }

    /**
     * 设置床型信息
     */
    private void setBedTypesInfo(ProductDetail product, Map<Long, List<BedTypesDetailDto>> roomIdBedMap, Map<Long, String> roomIdBedInfoMap, Long roomId, String language) {
        if (CollUtilX.isEmpty(product.getBedTypeDetails())) {
            List<BedTypesDetailDto> bedTypes = roomIdBedMap.get(roomId);
            if (CollUtilX.isNotEmpty(bedTypes)) {
                product.setBedTypeDetails(bedTypes);
                product.setBedTypeDiff(1);
            }
        } else {
            List<BedTypesDetailDto> filteredBedList = new ArrayList<>();
            for (BedTypesDetailDto bedTypeDetail : product.getBedTypeDetails()) {
                if (CollUtilX.isNotEmpty(bedTypeDetail.getBedInfos())) {
                    List<BedInfoDto> filteredBedInfos = bedTypeDetail.getBedInfos()
                            .stream()
                            .filter(item -> StrUtilX.isNotEmpty(item.getBedTypeCode()) && StrUtilX.isNotEmpty(item.getBedTypeName()))
                            .collect(toList());

                    if (CollUtilX.isNotEmpty(filteredBedInfos)) {
                        BedTypesDetailDto filteredBedTypesDetailDto = new BedTypesDetailDto();
                        filteredBedTypesDetailDto.setBedInfos(filteredBedInfos);

                        StringBuilder bedInfoDesc = new StringBuilder();

                        if (StrUtilX.isNotEmpty(bedTypeDetail.getBedInfoDesc())) {
                            bedInfoDesc.append(bedTypeDetail.getBedInfoDesc());
                        } else {
                            for (int i = 0; i < filteredBedInfos.size(); i++) {
                                BedInfoDto bedInfoDto = filteredBedInfos.get(i);
                                if (bedInfoDto.getBedNum() != null && bedInfoDto.getBedNum() > 0) {
                                    if (LanguageTypeEnum.zh_CN.getValue().equals(language)) {
                                        bedInfoDesc.append(bedInfoDto.getBedNum()).append("张");
                                    } else {
                                        bedInfoDesc.append(bedInfoDto.getBedNum()).append(" ");
                                    }
                                }
//                            bedInfoDesc.append(bedInfoDto.getBedTypeName());
                                bedInfoDto.setBedTypeName(bedNameConfig.getBedTypeName(bedInfoDto.getBedTypeCode(), language));
                                bedInfoDesc.append(bedInfoDto.getBedTypeName());
                                if (i != filteredBedInfos.size() - 1) {
                                    if (LanguageTypeEnum.zh_CN.getValue().equals(language)) {
                                        bedInfoDesc.append("和");
                                    } else {
                                        bedInfoDesc.append(" and ");
                                    }
                                }
                            }
                        }
                        filteredBedTypesDetailDto.setBedInfoDesc(bedInfoDesc.toString());
                        filteredBedList.add(filteredBedTypesDetailDto);
                    }
                }
            }

            if (CollUtilX.isNotEmpty(filteredBedList)) {
                product.setBedTypeDetails(filteredBedList);
            } else {
                product.setBedTypeDetails(null);
            }
        }

        if (CollUtilX.isNotEmpty(product.getBedTypeDetails())) {
            StringBuilder bedInfoDesc = new StringBuilder();
            for (int i = 0; i < product.getBedTypeDetails().size(); i++) {
                BedTypesDetailDto bedTypesDetailDto = product.getBedTypeDetails().get(i);
                if (StringUtils.isEmpty(bedTypesDetailDto.getBedInfoDesc())) {
                    continue;
                }
                bedInfoDesc.append(bedTypesDetailDto.getBedInfoDesc());
                if (i != product.getBedTypeDetails().size() - 1) {
                    if (LanguageTypeEnum.zh_CN.getValue().equals(language)) {
                        bedInfoDesc.append("或");
                    } else {
                        bedInfoDesc.append(" or ");
                    }
                }
            }
            String bedInfoDescStr = bedInfoDesc.toString();
            if (bedInfoDescStr.endsWith("或")) {
                bedInfoDescStr = bedInfoDescStr.substring(0, bedInfoDescStr.length() - 1);
            } else if (bedInfoDescStr.endsWith(" or ")) {
                bedInfoDescStr = bedInfoDescStr.substring(0, bedInfoDescStr.length() - 3);
            }
            product.setBedInfoDesc(bedInfoDescStr);

            if (product.getBedTypeDiff() == null) {
                String bedDesc = bedInfoDesc.toString();
                String debInfo = roomIdBedInfoMap.get(roomId);

                if (StrUtilX.isEmpty(debInfo) && StrUtilX.isEmpty(bedDesc)) {
                    //都为空
                    product.setBedTypeDiff(0);
                } else if (StrUtilX.isEmpty(debInfo) || StrUtilX.isEmpty(bedDesc)) {
                    //任意一个为空
                    product.setBedTypeDiff(1);
                } else {
                    // 都不为空则进行对比
                    // 2024-07-03 author:湫 对比去空格并且全部小写对比
                    bedDesc = bedDesc.replaceAll(" ", "");
                    bedDesc = bedDesc.toLowerCase();

                    debInfo = debInfo.replaceAll(" ", "");
                    debInfo = debInfo.toLowerCase();
                    if (!bedDesc.equals(debInfo)) {
                        product.setBedTypeDiff(1);
                    } else {
                        product.setBedTypeDiff(0);
                    }
                }
            }
        }
    }

    /**
     * 根据分销商编码查询供应商白名单
     */
    private List<String> getAgentSupplyAvailable(String agentCode) {
        String supplyCodes = (String) RedisTemplateX.hashGet(RedisKey.AGENT_SUPPLY_KEY, agentCode);
        List<String> supplyCodeList = StrUtilX.stringToList(supplyCodes, ",");
        if (CollUtilX.isEmpty(supplyCodeList)) {
            return null;
        }
        return supplyCodeList;
    }

    /**
     * 协议酒店产品
     */
    private ProductDetailResponse agreement(ProductDetailRequest request,
                                            Map<String, SupplierAddDTO> supplyMap,
                                            int orgCurrency,
                                            AgentAccountConfig agentAccountConfig) {
        Date start1 = new Date();
        ProductDetailResponse productDetailResponse = new ProductDetailResponse();
        productDetailResponse.setHotelId(request.getHotelId());
        productDetailResponse.setRoomItems(new ArrayList<>());
        if (request.getOnlyHourRoom() == 1) {
            return productDetailResponse;
        }

        // 判断供应商集合是否为空
        if (supplyMap == null || supplyMap.isEmpty()) {
            return productDetailResponse;
        }

        //自签酒店缓存中不存在，则不执行查询，直接返回空
        if (null == RedisTemplateX.hGet(RedisKey.CONTRACT_HOTEL_KEY, request.getHotelId().toString())) {
            return productDetailResponse;
        }

        // 1.根据酒店id查询出产品ids
        QueryProductIdsRequest queryProductIdsRequest = new QueryProductIdsRequest();
        queryProductIdsRequest.setHotelId(String.valueOf(request.getHotelId()));
        queryProductIdsRequest.setCompanyCode(request.getCompanyCode());
        queryProductIdsRequest.setSupplyCodes(new ArrayList<>(supplyMap.keySet()));
        // 国内 含税价 全球 不含税价格
        queryProductIdsRequest.setPriceType(agentAccountConfig.getDomesticOrOverseas() == 1 ? 0 : null);
        queryProductIdsRequest.setPayMethod(request.getPayMethod());
        List<HotelProductIdsResponse> hotelProductIdsResponses = productInfoService.queryProductIdsByHotelId(queryProductIdsRequest);
        if (CollUtilX.isEmpty(hotelProductIdsResponses)) {
            log.error("根据酒店id查询出产品ids 结果为空" + JSON.toJSONString(queryProductIdsRequest) + ",language" + request.getLanguage());
            return productDetailResponse;
        }
        if (agentAccountConfig.getDomesticOrOverseas() != null
                && agentAccountConfig.getDomesticOrOverseas() == 2
                && CollectionUtil.isEmpty(request.getRoomGuestNumbers())) {
            // 海外 房间和客人关系是必填的
            log.error("海外 房间和客人关系是必填的");
            return productDetailResponse;
        }

        // 提取所有产品id
        List<String> productIds = hotelProductIdsResponses.stream().map(HotelProductIdsResponse::getProductId).collect(Collectors.toList());

        // 入住离店时间集合
        List<String> saleDates = getSaleDates(request);

        // productId_saleDate集合
        List<String> productAndsaleDates = Lists.newArrayList();
        productIds.forEach(productId -> saleDates.forEach(saleDate -> productAndsaleDates.add(productId.concat(Constant.SEPARATOR).concat(saleDate))));

        // 产品基础信息
        Map<String, ProductDTO> productMap = getProductMap(productIds);
        if (CollUtil.isEmpty(productMap)) {
            log.error("产品基础信息为空");
            return productDetailResponse;
        }

        // 商家币种转客户币种汇率
        BigDecimal orgToAgentRate = RateUtil.getRate(orgCurrency, agentAccountConfig.getSettlementCurrency());
        if (orgToAgentRate == null || orgToAgentRate.compareTo(BigDecimal.ZERO) == 0) {
            Map<String, String> map = new HashMap<>();
            map.put(SlsEnum.LEVEL.getType(), SlsLogLevelEnum.ERROR.getLevel());
            map.put(SlsEnum.NAME.getType(), "queryProductDetailTmp-direct");
            map.put(SlsEnum.MESSAGE.getType(), "商家转客户汇率不存在，orgCurrency=" + orgCurrency + "，settlementCurrency=" + agentAccountConfig.getSettlementCurrency() + "，requestId=" + request.getRequestId());
            slsLoggerUtil.saveLog(map, "queryProductDetailTmp-direct", "tiangong-product-server");
            return productDetailResponse;
        }

        // 获取酒店信息
        Date start2 = new Date();
        Response<HotelInfoCollectionDTO> hotelInfoResp = getHotelInfo(request);
        HotelInfoCollectionDTO hotelInfo = hotelInfoResp.getModel();
        if (hotelInfoResp.isError() || hotelInfo == null || CollUtilX.isEmpty(hotelInfo.getRooms())) {
            saveSlsLog("查询自签产品，获取酒店信息为空，req=" + JSONUtil.toJsonStr(request) + "，resp=" + JSONUtil.toJsonStr(hotelInfoResp), start2, request.getRequestId());
            return productDetailResponse;
        }
        Map<Long, String> roomNameMap = hotelInfo.getRooms().stream().collect(toMap(RoomtypeDTO::getRoomtypeId, RoomtypeDTO::getRoomtypeName));

        // 产品id 和税费规则关系
        Map<String, String> productIdAndTaxRuleConfigId = new HashMap<>();
        // 产品Id和产品币种关系
        Map<String, Integer> productIdAndCurrencyMap = new HashMap<>();
        for (String key : productMap.keySet()) {
            ProductDTO productDTO = productMap.get(key);
            // 存在税费规则并且 价格类型为不含税价格
            if (productDTO != null
                    && (productDTO.getPriceType() != null && productDTO.getPriceType() == 1)
                    && productDTO.getTaxRuleConfigId() != null) {
                productIdAndTaxRuleConfigId.put(String.valueOf(productDTO.getProductId()), String.valueOf(productDTO.getTaxRuleConfigId()));
            }

            if (productDTO != null && productDTO.getCurrency() != null) {
                productIdAndCurrencyMap.put(String.valueOf(productDTO.getProductId()), productDTO.getCurrency());
            }
        }
        // 获取产品税费规则
        Map<String, ProTaxRuleConfigDTO> taxRuleMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(productIdAndTaxRuleConfigId)) {
            List<ProTaxRuleConfigDTO> proTaxRuleConfigs = StrUtilX.parseObject(RedisTemplateX.hMultiGet(RedisKey.proTaxRuleConfigKey, new ArrayList<>(productIdAndTaxRuleConfigId.values())), new TypeReference<List<ProTaxRuleConfigDTO>>() {
            });
            if (CollectionUtil.isNotEmpty(proTaxRuleConfigs)) {
                for (ProTaxRuleConfigDTO proTaxRuleConfig : proTaxRuleConfigs) {
                    taxRuleMap.put(String.valueOf(proTaxRuleConfig.getId()), proTaxRuleConfig);
                }
            }
        }

        // 每个产品 按次税费
        Map<Integer, RoomTaxDetail> roomTaxDetailMap = new HashMap<>();

        // 产品每日报价信息(加幅后) 转换币种
        try {
            List<com.tiangong.dto.product.request.RoomGuestNumber> roomGuestNumbers = request.getRoomGuestNumbers();
            Map<String, ProductBasePriceAndRoomStatusDTO> roomStatusAndPrice = getRoomStatusAndPrice(request.getCheckInDate(),
                    request.getCompanyCode(), productAndsaleDates, productMap, agentAccountConfig, roomTaxDetailMap, productIdAndTaxRuleConfigId, taxRuleMap, productIdAndCurrencyMap, orgCurrency, orgToAgentRate);
            // 条款信息,每日条款没有设置的,按产品级别设置  每日的 key 为 productId_saleDate 默认的  key 为 productId  所以优先获取 每天的 不存在在获取默认的
            List<String> tempProductIds = new ArrayList<>();
            tempProductIds.addAll(productIds);
            tempProductIds.addAll(productAndsaleDates);
            Map<String, ProductRestrictDTO> productRestrict = getProductRestrict(tempProductIds);
            Collection<ProductDTO> values = productMap.values();
            Map<Integer, List<ProductDTO>> roomProductMap = values.stream().collect(Collectors.groupingBy(ProductDTO::getRoomId, Collectors.toList()));
            // 获取产品id映射
            Map<String, String> productIdMapping = queryUtil.getCacheProductIdsWithUUID(productIds, request.getHotelId());
            List<RoomItem> roomItems = assmbleRoomItem(roomGuestNumbers, saleDates, roomStatusAndPrice, productRestrict, roomProductMap, productIds, roomTaxDetailMap, request, agentAccountConfig, orgCurrency, orgToAgentRate, supplyMap, productIdMapping, roomNameMap);
            productDetailResponse.setRoomItems(roomItems);
        } catch (Exception e) {
            log.error("自签产品转换异常", e);
        }
        saveSlsLog("排查慢问题：自签产品查询总耗时", start1, request.getRequestId());
        return productDetailResponse;
    }


    /**
     * 产品每日报价信息(加幅后)
     */
    public Map<String, ProductBasePriceAndRoomStatusDTO> getRoomStatusAndPrice(String checkInDate,
                                                                               String companyCode,
                                                                               List<String> productAndsaleDates,
                                                                               Map<String, ProductDTO> productMap,
                                                                               AgentAccountConfig agentAccountConfig,
                                                                               Map<Integer, RoomTaxDetail> roomTaxDetailMap,
                                                                               Map<String, String> productIdAndTaxRuleConfigId,
                                                                               Map<String, ProTaxRuleConfigDTO> taxRuleMap,
                                                                               Map<String, Integer> productIdAndCurrencyMap,
                                                                               int orgCurrency,
                                                                               BigDecimal orgToAgentRate) {

        List<Object> objects = RedisTemplateX.hMultiGet(RedisKey.productBasePriceAndRoomStatusKey, productAndsaleDates);
        if (CollectionUtil.isEmpty(objects)) {
            return new HashMap<>();
        }

        List<ProductBasePriceAndRoomStatusDTO> productBasePriceAndRoomStatusDTOS = StrUtilX.parseObject(objects, new TypeReference<List<ProductBasePriceAndRoomStatusDTO>>() {
        });
        if (CollectionUtil.isEmpty(productBasePriceAndRoomStatusDTOS)) {
            return new HashMap<>();
        }
        List<String> salePriceSaleKeys = productAndsaleDates.stream().map(e -> companyCode.concat(Constant.SEPARATOR).concat(e)).collect(Collectors.toList());

        List<Object> objectslist = RedisTemplateX.hMultiGet(RedisKey.productSalePriceKey, salePriceSaleKeys);
        if (CollectionUtil.isEmpty(objectslist)) {
            return new HashMap<>();
        }

        List<ProductSaleIncreaseDTO> productSaleIncreaseDTOS = StrUtilX.parseObject(objectslist, new TypeReference<List<ProductSaleIncreaseDTO>>() {
        });
        if (CollectionUtil.isEmpty(productSaleIncreaseDTOS)) {
            return new HashMap<>();
        }


        Map<String, ProductSaleIncreaseDTO> saleIncreaseMap = new HashMap<>();
        for (ProductSaleIncreaseDTO dto : productSaleIncreaseDTOS) {
            if (dto != null && StrUtilX.isNotEmpty(dto.getProductId()) && StrUtilX.isNotEmpty(dto.getSaleDate())) {
                // 创建键，使用产品ID与销售日期的组合
                String key = String.valueOf(dto.getProductId()).concat(Constant.SEPARATOR).concat(dto.getSaleDate());
                // 将 dto 放入 map 中，如果存在重复键，则覆盖值
                saleIncreaseMap.put(key, dto);
            }
        }
        if (saleIncreaseMap.isEmpty()) {
            return new HashMap<>();
        }

        return productBasePriceAndRoomStatusDTOS.stream()
                .filter(DisCommonUtils::filterAgreeProduct) // 过滤符合条件的产品
                .map(e -> {
                    // 调用 priceMakup 方法并捕获可能返回的 null
                    return priceMakup(checkInDate, saleIncreaseMap, e,
                            productIdAndTaxRuleConfigId, taxRuleMap, productIdAndCurrencyMap,
                            agentAccountConfig, roomTaxDetailMap, orgCurrency, orgToAgentRate, productMap);
                })
                .filter(i -> i != null && i.getBasePrice() != null && i.getBasePrice().doubleValue() > 0) // 过滤不符合条件的项
                .collect(Collectors.toMap(
                        e -> String.valueOf(e.getProductId()).concat(Constant.SEPARATOR).concat(e.getSaleDate()),
                        Function.identity(),
                        (v1, v2) -> v2 // 合并函数
                ));
    }


    /**
     * 自签加幅
     */
    private ProductBasePriceAndRoomStatusDTO priceMakup(String checkInDate,
                                                        Map<String, ProductSaleIncreaseDTO> map,
                                                        ProductBasePriceAndRoomStatusDTO productBasePriceAndRoomStatusDTO,
                                                        Map<String, String> productIdAndTaxRuleConfigId,
                                                        Map<String, ProTaxRuleConfigDTO> taxRuleMap,
                                                        Map<String, Integer> productIdAndCurrencyMap,
                                                        AgentAccountConfig agentAccountConfig,
                                                        Map<Integer, RoomTaxDetail> taxDetailDtoMap,
                                                        int orgCurrency,
                                                        BigDecimal orgToAgentRate,
                                                        Map<String, ProductDTO> productMap) {
        ProductSaleIncreaseDTO productSaleIncreaseDTO = map.get(productBasePriceAndRoomStatusDTO.getRedisKey());

        BigDecimal basePrice = Objects.isNull(productBasePriceAndRoomStatusDTO.getBasePrice()) ? BigDecimal.ZERO : productBasePriceAndRoomStatusDTO.getBasePrice();
        productBasePriceAndRoomStatusDTO.setTmpBasePrice(basePrice);

        Integer productId = productBasePriceAndRoomStatusDTO.getProductId();
        // 根据productId获取对应的税费规则ID
        String taxRuleConfigId = productIdAndTaxRuleConfigId.get(String.valueOf(productId));
        ProTaxRuleConfigDTO proTaxRuleConfigDTO = null;
        // 获取具体税费规则
        if (StrUtilX.isNotEmpty(taxRuleConfigId)) {
            proTaxRuleConfigDTO = taxRuleMap.get(taxRuleConfigId);
        }

        Integer b2bAdjustmentType;
        BigDecimal b2bModifiedAmt;
        BigDecimal b2bMiniAddRadiation = null;
        if (productSaleIncreaseDTO == null) {
            ProductDTO productDTO = productMap.get(String.valueOf(productId));
            if (productDTO != null && productDTO.getPayMethod() != null && productDTO.getPayMethod().equals(PayMethodEnum.PAY.key)) {
                // 现付 +0
                b2bAdjustmentType = 0;
                b2bModifiedAmt = new BigDecimal("0");
            } else {
                // 预付 且没有加幅
                return null;
            }
        } else {
            b2bAdjustmentType = productSaleIncreaseDTO.getB2bAdjustmentType();
            b2bModifiedAmt = productSaleIncreaseDTO.getB2bModifiedAmt();
            b2bMiniAddRadiation = productSaleIncreaseDTO.getB2bMiniAddRadiation();
        }
        adjustmentPrice2(checkInDate, productBasePriceAndRoomStatusDTO, proTaxRuleConfigDTO, basePrice, b2bAdjustmentType, b2bModifiedAmt, b2bMiniAddRadiation, productIdAndCurrencyMap, agentAccountConfig, taxDetailDtoMap, orgCurrency, orgToAgentRate, productMap);
        return productBasePriceAndRoomStatusDTO;
    }


    /**
     * 底价计算加幅后的价格 统一使用 默认0 四舍五入
     *
     * @param basePrice      底价
     * @param adjustmentType 加幅类型 SaleAdjustmentTypeEnum （0加数值 1减数值 2加百分比 3减百分比 4等于）
     * @param modifiedAmt    加幅值
     */
    public static void adjustmentPrice2(String checkInDate,
                                        ProductBasePriceAndRoomStatusDTO productBasePriceAndRoomStatus,
                                        ProTaxRuleConfigDTO proTaxRuleConfigDTO,
                                        BigDecimal basePrice,
                                        Integer adjustmentType,
                                        BigDecimal modifiedAmt,
                                        BigDecimal miniAddRadiation,
                                        Map<String, Integer> productIdAndCurrencyMap,
                                        AgentAccountConfig agentAccountConfig,
                                        Map<Integer, RoomTaxDetail> roomTaxDetailMap,
                                        int orgCurrency,
                                        BigDecimal orgToAgentRate,
                                        Map<String, ProductDTO> productMap) {

        ProductDTO productDTO = productMap.get(String.valueOf(productBasePriceAndRoomStatus.getProductId()));
        if (productDTO == null) {
            return;
        }
        if (Objects.isNull(basePrice)) {//非空情况
            productBasePriceAndRoomStatus.setBasePrice(basePrice);
            productBasePriceAndRoomStatus.setTmpBasePrice(basePrice);
        } else {
            // 获取产品币种 一定存在
            Integer productCurrency = productIdAndCurrencyMap.get(String.valueOf(productBasePriceAndRoomStatus.getProductId()));


            // 产品币种转 商家币种汇率
            BigDecimal merchantRate = RateUtil.getRate(productCurrency, orgCurrency);

            // 如果产品币种等于商家币种  那么汇率=1
            if (productCurrency.equals(orgCurrency)) {
                merchantRate = new BigDecimal("1");
            }

            BigDecimal agentRate = orgToAgentRate;
            // 如果 商家币种等于结算币种  那么汇率=1
            if (agentAccountConfig.getSettlementCurrency().equals(orgCurrency)) {
                agentRate = new BigDecimal("1");
            }
            // 1. 如果不含税 则需要计算税费 公式=底价+税费
            if (proTaxRuleConfigDTO != null && CollectionUtil.isNotEmpty(proTaxRuleConfigDTO.getProTaxDetails())) {
                getAllFaxPrice(checkInDate, productBasePriceAndRoomStatus, proTaxRuleConfigDTO.getProTaxDetails(), productCurrency, roomTaxDetailMap, merchantRate);
            } else {
                productBasePriceAndRoomStatus.setTmpBasePrice(basePrice);
                // 底价 由产品币种变更为商家币种 因为需要加辐 所以需要*汇率
                basePrice = basePrice.multiply(merchantRate);
                productBasePriceAndRoomStatus.setRoomPrice(basePrice);
            }
            // 2. 加幅处理
            String saleDate = productBasePriceAndRoomStatus.getSaleDate();

            // 房费 商家币种
            BigDecimal roomPrice = productBasePriceAndRoomStatus.getRoomPrice();
            // 现付产品加幅为0 所以不需要加幅
            if (productDTO.getPayMethod() != null && productDTO.getPayMethod().equals(PayMethodEnum.PAY.key)) {
                // 税费 客户币种
                if (productBasePriceAndRoomStatus.getSalesTax() != null) {
                    productBasePriceAndRoomStatus.setSalesTax(CommonTgUtils.setScale(productBasePriceAndRoomStatus.getSalesTax().multiply(agentRate), agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType()));
                }
                if (productBasePriceAndRoomStatus.getTax() != null) {
                    productBasePriceAndRoomStatus.setTax(CommonTgUtils.setScale(productBasePriceAndRoomStatus.getTax().multiply(agentRate), agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType()));
                }
                if (productBasePriceAndRoomStatus.getOtherTaxFee() != null) {
                    productBasePriceAndRoomStatus.setOtherTaxFee(CommonTgUtils.setScale((productBasePriceAndRoomStatus.getOtherTaxFee().multiply(agentRate)), agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType()));
                }
                productBasePriceAndRoomStatus.setRoomPrice(CommonTgUtils.setScale(roomPrice.multiply(agentRate), agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType()));


                // 是否首日  如果存在此税费 则需要计算在今天
                boolean isFirstDay = saleDate.equals(checkInDate);
                RoomTaxDetail roomTaxDetail = roomTaxDetailMap.get(productBasePriceAndRoomStatus.getProductId());
                // 首日税费
                BigDecimal fastTax = new BigDecimal("0");
                if (isFirstDay && roomTaxDetail != null) {
                    if (roomTaxDetail.getSalesTax() != null) {
                        roomTaxDetail.setSalesTax(CommonTgUtils.setScale(roomTaxDetail.getSalesTax().multiply(agentRate), agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType()));
                        fastTax = fastTax.add(roomTaxDetail.getSalesTax());
                    }
                    if (roomTaxDetail.getOtherTax() != null) {
                        roomTaxDetail.setOtherTax(CommonTgUtils.setScale(roomTaxDetail.getOtherTax().multiply(agentRate), agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType()));
                        fastTax = fastTax.add(roomTaxDetail.getOtherTax());
                    }
                    if (roomTaxDetail.getTaxFee() != null) {
                        roomTaxDetail.setTaxFee(CommonTgUtils.setScale(roomTaxDetail.getTaxFee().multiply(agentRate), agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType()));
                        fastTax = fastTax.add(roomTaxDetail.getTaxFee());
                    }
                }
                // 售价=房费+税费+加幅+首日税费
                BigDecimal salePrice = addPrice(productBasePriceAndRoomStatus.getRoomPrice(), productBasePriceAndRoomStatus.getSalesTax(), productBasePriceAndRoomStatus.getTax(), productBasePriceAndRoomStatus.getOtherTaxFee(), fastTax);
                productBasePriceAndRoomStatus.setBasePrice(salePrice);
            } else if (adjustmentType == null || modifiedAmt == null) {
                //防止出现没有设置加幅的情况 此时 已经是商家商家 币种价格了.所以只需要*客户币种汇率 | 现付产品 不需要加幅
                productBasePriceAndRoomStatus.setBasePrice(setScale((roomPrice.multiply(agentRate)), 2, BigDecimal.ROUND_HALF_UP));
            } else {
                //加幅值 商家币种 当+ 时存在 -时 为0  这个很重要
                BigDecimal adjustment = new BigDecimal("0");
                //折扣字段
                BigDecimal discount = BigDecimal.ZERO;
                // 加幅类型
                if (Objects.equals(SaleAdjustmentTypeEnum.PLUS_NUMBER.no, adjustmentType)) {
                    adjustment = modifiedAmt;
                } else if (Objects.equals(SaleAdjustmentTypeEnum.DIVIDE_NUMBER.no, adjustmentType)) {
//                    roomPrice = roomPrice.subtract(modifiedAmt);
                    discount = modifiedAmt.negate();
                } else if (Objects.equals(SaleAdjustmentTypeEnum.PLUS_PERCENTAGE.no, adjustmentType)) {
                    // 这里需要再次进行比较
                    BigDecimal addRadiation = roomPrice.multiply(modifiedAmt);
                    // 存在最小加辐值 那么则需要获取最小加辐值
                    if (miniAddRadiation != null) {
                        // 产品加辐值小于 最小加辐值, 则使用商家币种的产品加辐值
                        if (addRadiation.compareTo(miniAddRadiation) < 0) {
                            adjustment = miniAddRadiation;
                        } else {
                            // 百分比售价加辐+房费
                            adjustment = addRadiation;
                        }
                    } else {
                        // 不存在最小加辐值 则使用百分比加幅
                        adjustment = addRadiation;
                    }
                } else if (Objects.equals(SaleAdjustmentTypeEnum.SUBTRACT_PERCENTAGE.no, adjustmentType)) {
//                    roomPrice = roomPrice.subtract(modifiedAmt);
                    discount = roomPrice.multiply(modifiedAmt).negate();
                } else if (Objects.equals(SaleAdjustmentTypeEnum.EQUALS.no, adjustmentType)) {
                    // 修改金额大于房费则 加幅=修改金额-房费
                    // 否则 房费=修改金额 加幅=0
                    if (modifiedAmt.compareTo(roomPrice) > 0) {
                        adjustment = modifiedAmt.subtract(roomPrice);
                    } else {
                        // 否则直接修改房费 加幅=0  避免出现 房费和加幅
                        roomPrice = modifiedAmt; // 售价=多少
                    }
                }

                // 设置折扣
                productBasePriceAndRoomStatus.setDiscount(discount);

                // 加幅值 客户币种
                adjustment = CommonTgUtils.setScale(adjustment.multiply(agentRate), agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType());

                // 税费 客户币种
                if (productBasePriceAndRoomStatus.getSalesTax() != null) {
                    productBasePriceAndRoomStatus.setSalesTax(CommonTgUtils.setScale(productBasePriceAndRoomStatus.getSalesTax().multiply(agentRate), agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType()));
                }
                if (productBasePriceAndRoomStatus.getTax() != null) {
                    productBasePriceAndRoomStatus.setTax(CommonTgUtils.setScale(productBasePriceAndRoomStatus.getTax().multiply(agentRate), agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType()));
                }
                if (productBasePriceAndRoomStatus.getOtherTaxFee() != null) {
                    productBasePriceAndRoomStatus.setOtherTaxFee(CommonTgUtils.setScale((productBasePriceAndRoomStatus.getOtherTaxFee().multiply(agentRate)), agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType()));
                }
                if(productBasePriceAndRoomStatus.getDiscount() != null){
                    productBasePriceAndRoomStatus.setDiscount(CommonTgUtils.setScale((productBasePriceAndRoomStatus.getDiscount().multiply(agentRate)), agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType()));
                }
                productBasePriceAndRoomStatus.setRoomPrice(CommonTgUtils.setScale(roomPrice.multiply(agentRate), agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType()));

                // 是否首日  如果存在此税费 则需要计算在今天
                boolean isFirstDay = saleDate.equals(checkInDate);
                RoomTaxDetail roomTaxDetail = roomTaxDetailMap.get(productBasePriceAndRoomStatus.getProductId());
                // 首日税费
                BigDecimal fastTax = new BigDecimal("0");
                if (isFirstDay && roomTaxDetail != null) {
                    if (roomTaxDetail.getSalesTax() != null) {
                        roomTaxDetail.setSalesTax(CommonTgUtils.setScale(roomTaxDetail.getSalesTax().multiply(agentRate), agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType()));
                        fastTax = fastTax.add(roomTaxDetail.getSalesTax());
                    }
                    if (roomTaxDetail.getOtherTax() != null) {
                        roomTaxDetail.setOtherTax(CommonTgUtils.setScale(roomTaxDetail.getOtherTax().multiply(agentRate), agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType()));
                        fastTax = fastTax.add(roomTaxDetail.getOtherTax());
                    }
                    if (roomTaxDetail.getTaxFee() != null) {
                        roomTaxDetail.setTaxFee(CommonTgUtils.setScale(roomTaxDetail.getTaxFee().multiply(agentRate), agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType()));
                        fastTax = fastTax.add(roomTaxDetail.getTaxFee());
                    }
                }
                // 售价=房费+税费+加幅+首日税费+折扣
                BigDecimal salePrice = addPrice(productBasePriceAndRoomStatus.getRoomPrice(), productBasePriceAndRoomStatus.getSalesTax(), productBasePriceAndRoomStatus.getTax(), productBasePriceAndRoomStatus.getOtherTaxFee(), fastTax, productBasePriceAndRoomStatus.getDiscount());
                // 其他税费=其他税费+加幅
                productBasePriceAndRoomStatus.setOtherTaxFee(addPrice(productBasePriceAndRoomStatus.getOtherTaxFee(), adjustment));
                salePrice = salePrice.add(adjustment);
                productBasePriceAndRoomStatus.setBasePrice(salePrice);
            }
        }
    }

    /**
     * 相加
     */
    public static BigDecimal addPrice(BigDecimal... prices) {
        BigDecimal flag = new BigDecimal("0");
        for (BigDecimal price : prices) {
            if (price != null) {
                flag = flag.add(price);
            }
        }
        return flag;
    }

    /**
     * 获取税后价格
     */
    private static void getAllFaxPrice(String checkInDate, ProductBasePriceAndRoomStatusDTO productDayQuotationDTO, List<ProTaxDetailDTO> proTaxDetails, Integer productCurrency, Map<Integer, RoomTaxDetail> roomTaxDetailMap, BigDecimal merchantRate) {
        // 税后价格   房费 = 底价
        BigDecimal roomPrice = productDayQuotationDTO.getBasePrice();
        if (roomPrice == null || roomPrice.compareTo(BigDecimal.ZERO) == 0) {
        } else if (CollectionUtil.isNotEmpty(proTaxDetails)) {
            String saleDate = productDayQuotationDTO.getSaleDate();

            // 是否首日  如果存在此税费 则需要计算在今天
            boolean isFirstDay = saleDate.equals(checkInDate);
            if (isFirstDay) {
                roomTaxDetailMap.put(productDayQuotationDTO.getProductId(), new RoomTaxDetail());
            }

            // 存在税费
            BigDecimal basePrice;
            BigDecimal salesTax = new BigDecimal("0");
            BigDecimal tax = new BigDecimal("0");
            BigDecimal otherTaxFee = new BigDecimal("0");

            for (ProTaxDetailDTO proTaxDetail : proTaxDetails) {
                switch (proTaxDetail.getTaxType()) {
                    case 0:
                        //销售税
                        if (Objects.equals(proTaxDetail.getTaxStandard(), TaxStandardsEnum.NIGHT.getCode())) {
                            salesTax = salesTax.add(getTax(roomPrice, proTaxDetail));
                        } else if (isFirstDay && Objects.equals(proTaxDetail.getTaxStandard(), TaxStandardsEnum.MIDNIGHT.getCode())) {
                            RoomTaxDetail roomTaxDetail = roomTaxDetailMap.get(productDayQuotationDTO.getProductId());
                            BigDecimal oldSaleTax = roomTaxDetail.getSalesTax();
                            if (oldSaleTax == null) {
                                oldSaleTax = new BigDecimal("0");
                            }
                            oldSaleTax = oldSaleTax.add(getTax(roomPrice, proTaxDetail));
                            roomTaxDetail.setSalesTax(oldSaleTax);
                        }
                        break;
                    case 1:
                        //税费
                        if (Objects.equals(proTaxDetail.getTaxStandard(), TaxStandardsEnum.NIGHT.getCode())) {
                            tax = tax.add(getTax(roomPrice, proTaxDetail));
                        } else if (isFirstDay && Objects.equals(proTaxDetail.getTaxStandard(), TaxStandardsEnum.MIDNIGHT.getCode())) {
                            RoomTaxDetail roomTaxDetail = roomTaxDetailMap.get(productDayQuotationDTO.getProductId());
                            BigDecimal oldTax = roomTaxDetail.getTaxFee();
                            if (oldTax == null) {
                                oldTax = new BigDecimal("0");
                            }
                            oldTax = oldTax.add(getTax(roomPrice, proTaxDetail));
                            roomTaxDetail.setTaxFee(oldTax);
                        }
                        break;
                    case 2:
                        // 到店付 只能增加到 每次
                        RoomTaxDetail countTaxDetail = roomTaxDetailMap.get(productDayQuotationDTO.getProductId());
                        BigDecimal oldPayInStorePrice = countTaxDetail.getPayInStorePrice();
                        if (oldPayInStorePrice == null) {
                            oldPayInStorePrice = new BigDecimal("0");
                        }
                        oldPayInStorePrice = oldPayInStorePrice.add(getTax(roomPrice, proTaxDetail));
                        countTaxDetail.setPayInStorePrice(oldPayInStorePrice);
                        countTaxDetail.setPayInStoreCurrency(productCurrency);
                        break;
                    case 3:
                        // 其他税费
                        if (Objects.equals(proTaxDetail.getTaxStandard(), TaxStandardsEnum.NIGHT.getCode())) {
                            otherTaxFee = otherTaxFee.add(getTax(roomPrice, proTaxDetail));
                        } else if (isFirstDay && Objects.equals(proTaxDetail.getTaxStandard(), TaxStandardsEnum.MIDNIGHT.getCode())) {
                            RoomTaxDetail roomTaxDetail = roomTaxDetailMap.get(productDayQuotationDTO.getProductId());
                            BigDecimal oldOtherTax = roomTaxDetail.getOtherTax();
                            if (oldOtherTax == null) {
                                oldOtherTax = new BigDecimal("0");
                            }
                            oldOtherTax = oldOtherTax.add(getTax(roomPrice, proTaxDetail));
                            roomTaxDetail.setOtherTax(oldOtherTax);
                        }
                        break;
                }
            }
            RoomTaxDetail roomTaxDetail = roomTaxDetailMap.get(productDayQuotationDTO.getProductId());
            // 产品原始底价输出
            if (isFirstDay && roomTaxDetail != null) {
                // 含次税
                productDayQuotationDTO.setTmpBasePrice(getTotal(roomPrice, salesTax, tax, otherTaxFee, roomTaxDetail.getSalesTax(), roomTaxDetail.getTaxFee(), roomTaxDetail.getOtherTax()));
            } else {
                // 不含次税
                productDayQuotationDTO.setTmpBasePrice(getTotal(roomPrice, salesTax, tax, otherTaxFee));
            }

            basePrice = roomPrice.multiply(merchantRate);
            salesTax = salesTax.multiply(merchantRate);
            tax = tax.multiply(merchantRate);
            otherTaxFee = otherTaxFee.multiply(merchantRate);

            // 到店另付币种 为产品原始币种
            productDayQuotationDTO.setRoomPrice(roomPrice);
            if (salesTax.compareTo(BigDecimal.ZERO) != 0) {
                productDayQuotationDTO.setSalesTax(salesTax);
            }
            if (tax.compareTo(BigDecimal.ZERO) != 0) {
                productDayQuotationDTO.setTax(tax);
            }
            if (otherTaxFee.compareTo(BigDecimal.ZERO) != 0) {
                productDayQuotationDTO.setOtherTaxFee(otherTaxFee);
            }

            // 每次税费处理 如果是首日并且存在每次税费 则需要在底价中加入次税费
            if (isFirstDay && roomTaxDetail != null) {
                // 修改后的价格
                if (roomTaxDetail.getSalesTax() != null) {
                    roomTaxDetail.setSalesTax(roomTaxDetail.getSalesTax().multiply(merchantRate));
                }
                if (roomTaxDetail.getTaxFee() != null) {
                    roomTaxDetail.setTaxFee(roomTaxDetail.getTaxFee().multiply(merchantRate));
                }
                if (roomTaxDetail.getOtherTax() != null) {
                    roomTaxDetail.setOtherTax(roomTaxDetail.getOtherTax().multiply(merchantRate));
                }
            }
            productDayQuotationDTO.setRoomPrice(basePrice);
        } else {
            // 原始底价 不转换币种 产品原始币种
            productDayQuotationDTO.setTmpBasePrice(roomPrice);

            roomPrice = roomPrice.multiply(merchantRate);
            // 含税价
            productDayQuotationDTO.setRoomPrice(roomPrice);
        }
    }

    private static BigDecimal getTotal(BigDecimal... price) {
        BigDecimal total = BigDecimal.ZERO;
        if (price != null) {
            for (BigDecimal bigDecimal : price) {
                if (bigDecimal != null) {
                    total = total.add(bigDecimal);
                }
            }
        }
        return total;
    }

    /**
     * 获取税费
     */
    private static BigDecimal getTax(BigDecimal roomPrice, ProTaxDetailDTO proTaxDetail) {
        if (proTaxDetail.getTaxIncreaseType() != null && proTaxDetail.getTaxIncreaseValue() != null) {
            if (proTaxDetail.getTaxIncreaseType().equals(TaxIncreaseEnum.FIXED.getCode())) {
                // 固定
                return proTaxDetail.getTaxIncreaseValue();
            } else {
                // 百分比
                return roomPrice.multiply(proTaxDetail.getTaxIncreaseValue().divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
            }
        }
        return BigDecimal.ZERO;
    }

    /**
     * 设置n位小数点
     */
    public static BigDecimal setScale(BigDecimal bigDecimal, int newScale, int roundingMode) {
        return bigDecimal.setScale(newScale, roundingMode);
    }

    public List<String> getSaleDates(ProductDetailRequest request) {
        List<String> saleStatus = Lists.newArrayList();
        long day = DateUtilX.getDay(DateUtilX.stringToDate(request.getCheckInDate()), DateUtilX.stringToDate(request.getCheckOutDate()));
        for (int i = 0; i < day; i++) {
            saleStatus.add(DateUtilX.dateToString(DateUtilX.addDate(DateUtilX.stringToDate(request.getCheckInDate()), i)));
        }
        return saleStatus;
    }

    /**
     * 根据产品Ids获取产品
     */
    private Map<String, ProductDTO> getProductMap(List<String> productIds) {

        List<Object> objects = RedisTemplateX.hMultiGet(RedisKey.productInfoKey, productIds);

        if (CollectionUtil.isEmpty(objects)) {
            return Maps.newHashMap();
        }
        List<ProductDTO> productDTOS = StrUtilX.parseObject(objects, new TypeReference<List<ProductDTO>>() {
        });
        if (CollectionUtil.isNotEmpty(productDTOS)) {
            return productDTOS.stream().collect(Collectors.toMap(e -> String.valueOf(e.getProductId()), Function.identity(), (v1, v2) -> v2));
        }
        return Maps.newHashMap();
    }

    /**
     * 条款信息: 查询product_saledate, product汇总
     * 每日条款没有设置的,按产品级别设置
     * 每日的 key 为 productId_saleDate
     * 产品级别的 key 为 productId 所以优先获取 每天的 不存在在获取默认的
     */
    private Map<String, ProductRestrictDTO> getProductRestrict(List<String> productIds) {
        List<Object> list = RedisTemplateX.hMultiGet(RedisKey.productRestrictKey, productIds);
        if (CollectionUtil.isEmpty(list)) {
            return new HashMap<>();
        }
        List<ProductRestrictDTO> productRestrictDTOS = StrUtilX.parseObject(list, new TypeReference<List<ProductRestrictDTO>>() {
        });
        if (productRestrictDTOS == null) {
            return new HashMap<>();
        }
        Map<String, ProductRestrictDTO> restrictDTOMap = new HashMap<>();
        for (ProductRestrictDTO productRestrictDTO : productRestrictDTOS) {
            if (productRestrictDTO != null) {
                String key = String.valueOf(productRestrictDTO.getProductId());
                if (StrUtilX.isNotEmpty(productRestrictDTO.getSaleDate())) {
                    key = productRestrictDTO.getProductId() + "_" + productRestrictDTO.getSaleDate();
                }
                restrictDTOMap.put(key, productRestrictDTO);
            }
        }
        return restrictDTOMap;
    }

    /**
     * 组装房型item
     */
    private List<RoomItem> assmbleRoomItem(List<com.tiangong.dto.product.request.RoomGuestNumber> roomGuestNumbers,
                                           List<String> saleDates,
                                           Map<String, ProductBasePriceAndRoomStatusDTO> roomStatusAndPrice,
                                           Map<String, ProductRestrictDTO> productRestrict,
                                           Map<Integer, List<ProductDTO>> roomProductMap,
                                           List<String> productIds,
                                           Map<Integer, RoomTaxDetail> roomTaxDetailMap,
                                           ProductDetailRequest request,
                                           AgentAccountConfig agentAccountConfig,
                                           int orgCurrency,
                                           BigDecimal orgToAgentRate,
                                           Map<String, SupplierAddDTO> supplierConfig,
                                           Map<String, String> productIdMapping,
                                           Map<Long, String> roomNameMap) {
        List<RoomItem> roomItems = Lists.newArrayList();
        Set<Integer> rooms = roomProductMap.keySet();
        for (Integer room : rooms) {
            RoomItem roomItem = new RoomItem();
            roomItem.setRoomId(Long.valueOf(room));
            List<ProductDTO> productDTOS = roomProductMap.get(room);
            ProductDTO productDTO = productDTOS.stream().findFirst().get();
            if (productDTO.getRoomId() != null) {
                roomItem.setRoomName(roomNameMap.get(productDTO.getRoomId().longValue()));
            }
            List<ProductDetail> products = assembleProduct(roomGuestNumbers, saleDates, roomStatusAndPrice, productRestrict, productDTOS, productIds, roomTaxDetailMap, agentAccountConfig, request, orgCurrency, orgToAgentRate, supplierConfig, productIdMapping);
            if (CollectionUtils.isNotEmpty(products)) {
                roomItem.setProducts(products);
            } else {
                continue;
            }
            roomItems.add(roomItem);
        }
        return roomItems;
    }

    /**
     * 组装产品信息
     */
    private List<ProductDetail> assembleProduct(List<com.tiangong.dto.product.request.RoomGuestNumber> roomGuestNumbers,
                                                List<String> saleDates,
                                                Map<String, ProductBasePriceAndRoomStatusDTO> roomStatusAndPrice,
                                                Map<String, ProductRestrictDTO> productRestrict,
                                                List<ProductDTO> productDTOS,
                                                List<String> productIds,
                                                Map<Integer, RoomTaxDetail> roomTaxDetailMap,
                                                AgentAccountConfig agentAccountConfig,
                                                ProductDetailRequest request,
                                                int orgCurrency,
                                                BigDecimal orgToAgentRate,
                                                Map<String, SupplierAddDTO> supplierMap,
                                                Map<String, String> productIdMapping) {
        List<ProductDetail> products = new ArrayList<>();
        for (ProductDTO dto : productDTOS) {
            ProductDetail productDetail = new ProductDetail();
            productDetail.setRatePlanId(productIdMapping.get(String.valueOf(dto.getProductId())));
            productDetail.setRatePlanName(dto.getProductName());
            // 价格计划名称存在中文直接转换成指定英文
            if (LanguageTypeEnum.en_US.getValue().equals(request.getLanguage()) && StrUtilX.isNotEmpty(productDetail.getRatePlanName())) {
                if (StrUtilX.notIsEnglishAndSpace(productDetail.getRatePlanName())) {
                    productDetail.setRatePlanName("Business travel rate");
                }
            }
            // 设置币种
            productDetail.setBaseCurrency(dto.getCurrency());

            productDetail.setBedTypeDetails(convertBedType(dto.getBedTypes(), request.getLanguage()));
            productDetail.setBroadNet(null);
            productDetail.setSupplyCode(dto.getSupplierCode());
            SupplierAddDTO supplierAddDTO = supplierMap.get(dto.getSupplierCode());
            if (supplierAddDTO != null) {
                productDetail.setSupplyName(supplierAddDTO.getSupplierName());
            } else {
                productDetail.setSupplyName(dto.getSupplierName());
            }


            BigDecimal merchantRaate = RateUtil.getRate(dto.getCurrency(), orgCurrency);
            if (CollectionUtil.isNotEmpty(roomTaxDetailMap)
                    && roomTaxDetailMap.get(dto.getProductId()) != null) {
                RoomTaxDetail roomTaxDetail = roomTaxDetailMap.get(dto.getProductId());
                TaxDetailDto taxDetailDto = new TaxDetailDto();
                if (roomTaxDetail.getSalesTax() != null
                        || roomTaxDetail.getTaxFee() != null
                        || roomTaxDetail.getOtherTax() != null
                        || roomTaxDetail.getPayInStorePrice() != null) {
                    taxDetailDto.setSalesTax(roomTaxDetail.getSalesTax());
                    taxDetailDto.setTaxFee(roomTaxDetail.getTaxFee());
                    taxDetailDto.setOtherTax(roomTaxDetail.getOtherTax());

                    productDetail.setPayInStorePrice(roomTaxDetail.getPayInStorePrice());
                    productDetail.setPayInStoreCurrency(roomTaxDetail.getPayInStoreCurrency());
                    productDetail.setTaxDetail(taxDetailDto);
                    // 根据产品币种转换 客户币种金额和
                    if (roomTaxDetail.getPayInStorePrice() != null && roomTaxDetail.getPayInStoreCurrency() != null) {
                        // 供应商币种*商家汇率*客户汇率=客户到店付金额
                        productDetail.setPayInStorePriceAgentCurrencyAmt(roomTaxDetail.getPayInStorePrice().multiply(merchantRaate).multiply(orgToAgentRate));
                        // 汇率= 商家汇率/客户汇率
                        productDetail.setPayInStoreRate(merchantRaate.divide(orgToAgentRate, 8, RoundingMode.HALF_UP));
                    }
                } else {
                    taxDetailDto.setSalesTax(new BigDecimal("0"));
                    taxDetailDto.setTaxFee(new BigDecimal("0"));
                    taxDetailDto.setOtherTax(new BigDecimal("0"));
                    productDetail.setTaxDetail(taxDetailDto);
                }
            } else {
                TaxDetailDto taxDetailDto = new TaxDetailDto();
                taxDetailDto.setSalesTax(new BigDecimal("0"));
                taxDetailDto.setTaxFee(new BigDecimal("0"));
                taxDetailDto.setOtherTax(new BigDecimal("0"));
                productDetail.setTaxDetail(taxDetailDto);
            }

            List<String> priceDates = Lists.newArrayList();
            saleDates.forEach(saleDate -> priceDates.add(String.valueOf(dto.getProductId()).concat(Constant.SEPARATOR).concat(saleDate)));
            PriceDto totalPrice = new PriceDto();

            AtomicBoolean bookTypeFlag = new AtomicBoolean(true);
            // 不含税价格计划，存在税费规则 则按照全球标准输出
            List<RoomItemDetail> roomItemDetails = assembleRoomItems(request, roomGuestNumbers, roomStatusAndPrice, productRestrict, dto, priceDates, productIds, totalPrice, roomTaxDetailMap, agentAccountConfig, orgCurrency, bookTypeFlag);
            if (CollectionUtil.isNotEmpty(roomItemDetails)) {
                RoomItemDetail roomItemDetail = roomItemDetails.get(0);
                List<PriceItem> priceItems = roomItemDetail.getPriceItems();
                if (CollectionUtil.isEmpty(priceItems) || priceItems.size() != saleDates.size()) {
                    continue;
                }
                productDetail.setRoomItemDetails(roomItemDetails);
                productDetail.setPriceItems(priceItems);
            } else {
                continue;
            }

            productDetail.setBookType(bookTypeFlag.get() ? 1 : 2);
            // 总卖价
            productDetail.setTotalSalePrice(totalPrice.getTotalSalePrice());
            productDetail.setTotalFinallySalePrice(totalPrice.getTotalSalePrice());
            // 总售价
            productDetail.setTotalBasePrice(totalPrice.getTotalBasePrice());

            productDetail.setAvgPrice(totalPrice.getTotalSalePrice().divide(new BigDecimal(request.getRoomNum()), 2, RoundingMode.HALF_DOWN).divide(new BigDecimal(saleDates.size()), 2, RoundingMode.HALF_DOWN));
            if (dto.getPriceType() != null && dto.getPayMethod().equals(1)) {
                // 到店付
                productDetail.setPayAtHotelFlag(1);
            } else {
                productDetail.setPayAtHotelFlag(0);
            }
            productDetail.setInvoiceModel(1);
            productDetail.setInvoiceType(1);
            productDetail.setHourlyRoom(0);
            productDetail.setAddBed(0);
            //最大入住人数 自签产品默认为2
            if (dto.getMaxAdultQty() == null || dto.getMaxAdultQty() <= 0) {
                dto.setMaxAdultQty(2);
            }
            productDetail.setMaxGuestPerson(dto.getMaxAdultQty());
            productDetail.setSmallProductLabel(0);
            products.add(productDetail);
        }
        return products;
    }

    /**
     * 转换床型
     */
    private List<BedTypesDetailDto> convertBedType(String bedType, String language) {
        if (StrUtilX.isNotEmpty(bedType)) {
            try {
                List<BedTypesDetailDto> bedTypeDetails = JSON.parseArray(bedType, BedTypesDetailDto.class);
                for (BedTypesDetailDto bedTypeDetail : bedTypeDetails) {
                    for (BedInfoDto bedInfo : bedTypeDetail.getBedInfos()) {
                        bedInfo.setBedTypeName(bedNameConfig.getBedTypeName(bedInfo.getBedTypeCode(), language));
                    }
                }
            } catch (Exception e) {
                log.error("实时查询自签产品系统异常", e);
            }
        }
        return null;
    }

    /**
     * 组装每日报价
     */
    private List<PriceItem> assemblePriceItems(Map<String, ProductBasePriceAndRoomStatusDTO> roomStatusAndPrice, Map<String, ProductRestrictDTO> productRestrict, ProductDTO dto, List<String> priceDates, List<String> productIds, PriceDto totalPrice, AgentAccountConfig agentAccountConfig, int orgCurrency,AtomicBoolean bookTypeFlag) {
        List<Object> objects = RedisTemplateX.hMultiGet(RedisKey.productInfoKey, productIds);
        if (CollectionUtil.isEmpty(objects)) {
            return new ArrayList<>();
        }
        List<ProductDTO> productDTOS = StrUtilX.parseObject(objects, new TypeReference<List<ProductDTO>>() {
        });
        Map<Integer, Integer> productIdAndBreakfastQty = productDTOS.stream().filter(Objects::nonNull).collect(toMap(ProductDTO::getProductId, ProductDTO::getBreakfastQty));
        List<PriceItem> priceItems = Lists.newArrayList();
        for (String productAndsaleDate : priceDates) {
            ProductBasePriceAndRoomStatusDTO productBasePriceAndRoomStatusDTO = roomStatusAndPrice.getOrDefault(productAndsaleDate, new ProductBasePriceAndRoomStatusDTO());

            PriceItem priceItem = new PriceItem();
            if (Objects.nonNull(productBasePriceAndRoomStatusDTO)) {
                priceItem.setSaleDate(productAndsaleDate.split("_")[1]);
                // 价格为0 进行过滤
                if (productBasePriceAndRoomStatusDTO.getBasePrice() == null
                        || productBasePriceAndRoomStatusDTO.getBasePrice().compareTo(BigDecimal.ZERO) == 0) {
                    continue;
                }
                // 获取每日 条款
                ProductRestrictDTO productRestrictDTO = productRestrict.get(dto.getProductId() + "_" + productBasePriceAndRoomStatusDTO.getSaleDate());
                if (productRestrictDTO == null) {
                    // 每日条款获取不到则查询默认条款 产品级别
                    productRestrictDTO = productRestrict.getOrDefault(String.valueOf(dto.getProductId()), new ProductRestrictDTO());
                }
                TaxDetailDto taxDetail = new TaxDetailDto();
                if (productBasePriceAndRoomStatusDTO.getTax() != null
                        || productBasePriceAndRoomStatusDTO.getRoomPrice() != null
                        || productBasePriceAndRoomStatusDTO.getPayAtHotelFee() != null
                        || productBasePriceAndRoomStatusDTO.getOtherTaxFee() != null) {
                    taxDetail.setRoomPrice(productBasePriceAndRoomStatusDTO.getRoomPrice());
                    taxDetail.setSalesTax(productBasePriceAndRoomStatusDTO.getSalesTax());
                    taxDetail.setOtherTax(productBasePriceAndRoomStatusDTO.getOtherTaxFee());
                    taxDetail.setTaxFee(productBasePriceAndRoomStatusDTO.getTax());
                } else {
                    taxDetail.setRoomPrice(productBasePriceAndRoomStatusDTO.getBasePrice());
                    taxDetail.setSalesTax(new BigDecimal("0"));
                    taxDetail.setOtherTax(new BigDecimal("0"));
                    taxDetail.setTaxFee(new BigDecimal("0"));
                }
                priceItem.setTaxDetail(taxDetail);
                priceItem.setSettlementCurrency(agentAccountConfig.getSettlementCurrency());
                priceItem.setSalePrice(productBasePriceAndRoomStatusDTO.getBasePrice());
                priceItem.setBasePrice(productBasePriceAndRoomStatusDTO.getTmpBasePrice());
                // 底价币种
                priceItem.setBaseCurrency(dto.getCurrency());
                // 计算总售价
                totalPrice.setTotalSalePrice(totalPrice.getTotalSalePrice().add(productBasePriceAndRoomStatusDTO.getBasePrice()));
                // 计算总底价
                totalPrice.setTotalBasePrice(totalPrice.getTotalBasePrice().add(productBasePriceAndRoomStatusDTO.getTmpBasePrice()));
                priceItem.setBreakfastNum(productIdAndBreakfastQty.get(productBasePriceAndRoomStatusDTO.getProductId()));
                if (null != productBasePriceAndRoomStatusDTO.getRoomStatus()) {//房态已经过滤了 理论上这里不需要处理了
                    if (productBasePriceAndRoomStatusDTO.getRemainingQuota() == null) {
                        productBasePriceAndRoomStatusDTO.setRemainingQuota(0);
                    }
                    if (productBasePriceAndRoomStatusDTO.getOverDraftStatus() == null) {
                        productBasePriceAndRoomStatusDTO.setOverDraftStatus(0);
                    }
                    if (productBasePriceAndRoomStatusDTO.getRoomStatus().equals(1)) {//开房
                        //有房 不可超 剩余配额为0 已经设置默认值了
                        if (productBasePriceAndRoomStatusDTO.getOverDraftStatus().equals(0) && productBasePriceAndRoomStatusDTO.getRemainingQuota().equals(0)) {
                            priceItem.setRoomStatus(RoomStatusEnum.FULL.getType());//满房
                            bookTypeFlag.set(false);
                        } else {
                            priceItem.setRoomStatus(RoomStatusEnum.EXIST.getType());
                        }
                    } else {
                        continue;//没关房过滤
                    }
                } else {
                    continue;//没设置过滤
                }
                priceItem.setMinRoomCount(productRestrictDTO.getReservationLimitRooms());
                int bookRestrictionType = 0;
                if (Objects.equals(productRestrictDTO.getComparisonType(), 0)) {
                    bookRestrictionType = 1;
                }
                priceItem.setBookRestrictionType(bookRestrictionType);
                priceItem.setBookRestrictionDay(productRestrictDTO.getReservationAdvanceDays());
                String reservationDueTime = Objects.isNull(productRestrictDTO.getReservationDueTime()) ? null : productRestrictDTO.getReservationDueTime().replace(":", "");
                priceItem.setBookRestrictionTime(reservationDueTime);
                int occupancyRestrictionType = 0;
                if (productRestrictDTO.getReservationLimitNights() != null && !Objects.equals(productRestrictDTO.getReservationLimitNights(), 0)) {
                    occupancyRestrictionType = 1;
                }
                priceItem.setOccupancyRestrictionType(occupancyRestrictionType);
                priceItem.setOccupancyRestrictionDay(productRestrictDTO.getReservationLimitNights());
                priceItem.setMinRoomCount(productRestrictDTO.getReservationLimitRooms());
//                // 自签产品特殊处理
//                if (productRestrictDTO.getCancellationType() != null && productRestrictDTO.getCancellationType() == 2) {
//                    productRestrictDTO.setCancellationType(3);
//                }
                Integer cancelRestrictionType = null;
                if (Objects.equals(productRestrictDTO.getCancellationType(), 1)) {
                    cancelRestrictionType = 1;
                } else if (Objects.equals(productRestrictDTO.getCancellationType(), 2)) {
                    cancelRestrictionType = 2;
                } else if (Objects.equals(productRestrictDTO.getCancellationType(), 3)) {
                    cancelRestrictionType = 3;
                } else if (Objects.equals(productRestrictDTO.getCancellationType(), 4)) {
                    cancelRestrictionType = 4;
                }

                // 担保条款
                if (CollectionUtil.isNotEmpty(productRestrictDTO.getGuarantees())) {
                    GuaranteeDTO guaranteeDTO = productRestrictDTO.getGuarantees().get(0);
                    priceItem.setGuaranteeType(guaranteeDTO.getGuaranteeType());
                    priceItem.setGuaranteeFeeType(guaranteeDTO.getGuaranteeFeeType());
                    priceItem.setGuaranteeCondition(guaranteeDTO.getGuaranteeCondition());
                }

                List<CancelRestriction> cancelRestrictions = Lists.newArrayList();
                CancelRestriction cancelRestriction = new CancelRestriction();
                cancelRestriction.setCancelRestrictionType(cancelRestrictionType);
                cancelRestriction.setCancelRestrictionDay(productRestrictDTO.getCancellationAdvanceDays());
                if (productRestrictDTO.getCancellationAdvanceDays() != null) {
                    cancelRestriction.setCancelRestrictionDate(DateUtilX.dateToString(DateUtilX.getDate(DateUtilX.stringToDate(priceItem.getSaleDate()), -productRestrictDTO.getCancellationAdvanceDays(), 0)));
                }


                String cancellationDueTime = Objects.isNull(productRestrictDTO.getCancellationDueTime()) ? null : productRestrictDTO.getCancellationDueTime().replace(":", "");
                cancelRestriction.setCancelRestrictionTime(cancellationDueTime);

                // 取消罚金处理
                if (productRestrictDTO.getCancelPenaltiesType() != null
                        && productRestrictDTO.getCancelPenaltiesValue() != null) {
                    CancelPenalDTO cancelPenalDTO = new CancelPenalDTO();
                    cancelPenalDTO.setPenaltiesType(productRestrictDTO.getCancelPenaltiesType());
                    if (productRestrictDTO.getCancelPenaltiesType().equals(3) || productRestrictDTO.getCancelPenaltiesType().equals(4)) {
                        cancelPenalDTO.setPenaltiesValue(productRestrictDTO.getCancelPenaltiesValue() + "%");
                        cancelPenalDTO.setCurrencyCode(SettlementCurrencyEnum.getCodeByKey(String.valueOf(productRestrictDTO.getCurrency())));
                    } else if (productRestrictDTO.getCancelPenaltiesType().equals(2) && productRestrictDTO.getCurrency() != null && productRestrictDTO.getCurrency().compareTo(agentAccountConfig.getSettlementCurrency()) != 0) {
                        // 转换商家币种
                        BigDecimal orgRate = BigDecimal.ONE;
                        if (new BigDecimal(productRestrictDTO.getCurrency()).compareTo(new BigDecimal(orgCurrency)) != 0) {
                            orgRate = RateUtil.getRate(productRestrictDTO.getCurrency(), orgCurrency);
                            if (orgRate == null) {
                                throw new SysException(ErrorCodeEnum.EXCHANGE_RATE_IS_NOT_ALREADT);
                            }
                        }
                        // 转换汇率
                        BigDecimal cancelPenaltiesAmt = CommonTgUtils.setRate(BigDecimal.valueOf(productRestrictDTO.getCancelPenaltiesValue()), orgRate);
                        // 转换客户币种
                        BigDecimal agentRate = BigDecimal.ONE;
                        if (new BigDecimal(orgCurrency).compareTo(new BigDecimal(agentAccountConfig.getSettlementCurrency())) != 0) {
                            agentRate = RateUtil.getRate(orgCurrency, agentAccountConfig.getSettlementCurrency());
                            if (agentRate == null) {
                                throw new SysException(ErrorCodeEnum.EXCHANGE_RATE_IS_NOT_ALREADT);
                            }
                        }
                        // 转换汇率
                        cancelPenaltiesAmt = CommonTgUtils.setRate(cancelPenaltiesAmt, agentRate);
                        // 根据客户配置进位
                        BigDecimal cancelPenaltiesValue = CommonTgUtils.setScale(cancelPenaltiesAmt, agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType());
                        cancelPenalDTO.setPenaltiesValue(String.valueOf(cancelPenaltiesValue));
                        cancelPenalDTO.setCurrencyCode(SettlementCurrencyEnum.getCodeByKey(String.valueOf(agentAccountConfig.getSettlementCurrency())));
                    } else {
                        cancelPenalDTO.setPenaltiesValue(String.valueOf(productRestrictDTO.getCancelPenaltiesValue()));
                        cancelPenalDTO.setCurrencyCode(SettlementCurrencyEnum.getCodeByKey(String.valueOf(productRestrictDTO.getCurrency())));
                    }

                    String dateStr = (DateUtilX.dateToString((DateUtilX.getDate(DateUtilX.stringToDate(priceItem.getSaleDate()), -cancelRestriction.getCancelRestrictionDay())), "yyyy-MM-dd") + " " + cancellationDueTime.substring(0, 2) + ":" + cancellationDueTime.substring(2, 4));
                    Date date = DateUtilX.stringToDate(dateStr, "yyyy-MM-dd HH:mm");
                    cancelPenalDTO.setStartDate(DateUtilX.dateToString(date, "yyyy-MM-dd'T'HH:mm:ss.SSSXXX"));
                    cancelPenalDTO.setEndData(cancelPenalDTO.getStartDate());
                    StringBuilder stringBuilder = new StringBuilder();
                    if (productRestrictDTO.getCancelPenaltiesType().equals(4)) {
                        if (productRestrictDTO.getCancelPenaltiesValue().equals(100.0)) {
                            stringBuilder.append("取消或更改,收取首晚房费");
                        } else {
                            stringBuilder.append("取消或更改,扣首晚房费的").append(productRestrictDTO.getCancelPenaltiesValue()).append("%，作为退订费。");
                        }
                    } else if (productRestrictDTO.getCancelPenaltiesType().equals(3)) {
                        stringBuilder.append("取消或更改,扣全额房费的").append(productRestrictDTO.getCancelPenaltiesValue()).append("%，作为退订费");
                    }
                    cancelRestriction.setCancelRestrictionRemark(stringBuilder.toString());
                }

                cancelRestrictions.add(cancelRestriction);
                priceItem.setCancelRestrictions(cancelRestrictions);
                priceItems.add(priceItem);
            }
        }
        return priceItems;
    }

    /**
     * 组装产品详情
     */
    private List<RoomItemDetail> assembleRoomItems(ProductDetailRequest request,
                                                   List<com.tiangong.dto.product.request.RoomGuestNumber> roomGuestNumbers,
                                                   Map<String, ProductBasePriceAndRoomStatusDTO> roomStatusAndPrice,
                                                   Map<String, ProductRestrictDTO> productRestrict,
                                                   ProductDTO dto,
                                                   List<String> priceDates,
                                                   List<String> productIds,
                                                   PriceDto totalPrice,
                                                   Map<Integer, RoomTaxDetail> roomTaxDetailMap,
                                                   AgentAccountConfig agentAccountConfig,
                                                   int orgCurrency,
                                                   AtomicBoolean bookTypeFlag) {
        List<Object> objects = RedisTemplateX.hMultiGet(RedisKey.productInfoKey, productIds);
        if (CollectionUtil.isEmpty(objects)) {
            return new ArrayList<>();
        }
        List<RoomItemDetail> roomItemDetails = Lists.newArrayList();
        if (CollectionUtil.isNotEmpty(roomGuestNumbers)) {
            for (com.tiangong.dto.product.request.RoomGuestNumber roomGuestNumber : roomGuestNumbers) {
                RoomItemDetail roomItemDetail = new RoomItemDetail();
                roomItemDetail.setRoomIndex(roomGuestNumber.getRoomIndex());
                roomItemDetail.setAdultNum(roomGuestNumber.getAdultNum());
                if (CollectionUtil.isNotEmpty(roomGuestNumber.getChildrenInfos())) {
                    StringBuilder childAges = new StringBuilder();
                    for (ChildrenInfo childrenInfo : roomGuestNumber.getChildrenInfos()) {
                        childAges.append(childrenInfo.getChildrenAge()).append(",");
                    }
                    roomItemDetail.setChildAges(childAges.substring(0, childAges.length() - 1));
                }
                if (CollectionUtil.isNotEmpty(roomTaxDetailMap) && roomTaxDetailMap.get(dto.getProductId()) != null) {
                    RoomTaxDetail roomTaxDetail = roomTaxDetailMap.get(dto.getProductId());

                    TaxDetailDto taxDetailDto = new TaxDetailDto();
                    if (roomTaxDetail.getSalesTax() != null
                            || roomTaxDetail.getTaxFee() != null
                            || roomTaxDetail.getOtherTax() != null) {
                        taxDetailDto.setSalesTax(roomTaxDetail.getSalesTax());
                        taxDetailDto.setTaxFee(roomTaxDetail.getTaxFee());
                        taxDetailDto.setOtherTax(roomTaxDetail.getOtherTax());
                    } else {
                        taxDetailDto.setSalesTax(new BigDecimal("0"));
                        taxDetailDto.setTaxFee(new BigDecimal("0"));
                        taxDetailDto.setOtherTax(new BigDecimal("0"));
                    }
                    roomItemDetail.setTaxDetail(taxDetailDto);
                    roomItemDetail.setPayInStorePrice(roomTaxDetail.getPayInStorePrice());
                    roomItemDetail.setPayInStoreCurrency(roomTaxDetail.getPayInStoreCurrency());
                } else {
                    TaxDetailDto taxDetailDto = new TaxDetailDto();
                    taxDetailDto.setSalesTax(new BigDecimal("0"));
                    taxDetailDto.setTaxFee(new BigDecimal("0"));
                    taxDetailDto.setOtherTax(new BigDecimal("0"));
                    roomItemDetail.setTaxDetail(taxDetailDto);
                }
                List<PriceItem> priceItems = assemblePriceItems(roomStatusAndPrice, productRestrict, dto, priceDates, productIds, totalPrice, agentAccountConfig, orgCurrency,bookTypeFlag);
                roomItemDetail.setPriceItems(priceItems);
                roomItemDetails.add(roomItemDetail);
            }
        } else {
            for (Integer i = 1; i <= request.getRoomNum(); i++) {
                RoomItemDetail roomItemDetail = new RoomItemDetail();
                roomItemDetail.setRoomIndex(i);
                TaxDetailDto taxDetailDto = new TaxDetailDto();
                taxDetailDto.setSalesTax(new BigDecimal("0"));
                taxDetailDto.setTaxFee(new BigDecimal("0"));
                taxDetailDto.setOtherTax(new BigDecimal("0"));
                roomItemDetail.setTaxDetail(taxDetailDto);
                List<PriceItem> priceItems = assemblePriceItems(roomStatusAndPrice, productRestrict, dto, priceDates, productIds, totalPrice, agentAccountConfig, orgCurrency,bookTypeFlag);
                roomItemDetail.setPriceItems(priceItems);
                roomItemDetails.add(roomItemDetail);
            }
        }

        return roomItemDetails;
    }

    /**
     * 保存日志
     */
    private void saveSlsLog(String message, Date start, String logId) {
        Map<String, String> map = new HashMap<>();
        map.put(SlsEnum.LEVEL.getType(), SlsLogLevelEnum.INFO.getLevel());
        map.put(SlsEnum.NAME.getType(), "queryProductDetailTmp");
        map.put("logFlag", "排查慢问题");
        map.put("logId", logId);
        map.put(SlsEnum.MESSAGE.getType(), message);
        map.put("start", DateUtilX.dateToString(start, DateUtilX.hour_ms_format));
        slsLoggerUtil.saveLog(map, "queryProductDetailTmp", "tiangong-product-server");
    }

    /**
     * 直连供应商产品
     */
    private ProductDetailResponse direct(ProductDetailRequest request,
                                         Map<String, SupplierAddDTO> supplyMap,
                                         int orgCurrency,
                                         AgentAccountConfig agentAccountConfig) {
        Date start = new Date();
        Date start1 = new Date();

        // 定义产品对象
        ProductDetailResponse productDetailDTO = new ProductDetailResponse();
        productDetailDTO.setHotelId(request.getHotelId());
        productDetailDTO.setRoomItems(new ArrayList<>());

        // 判断供应商集合是否为空
        if (supplyMap == null || supplyMap.isEmpty()) {
            return productDetailDTO;
        }

        // 获取供应商产品数据
        request.setSaleCurrency(agentAccountConfig.getSettlementCurrency());
        Response<List<ProductMiddleDto>> response = getSupplyProductInfo(request, new ArrayList<>(supplyMap.keySet()));
        saveSlsLog("排查慢问题：查询供应商产品信息，response=" + JSONUtil.toJsonStr(response), start1, request.getRequestId());

        // 判断请求供应商接口是否正常
        if (response.isSuccess()) {
            List<ProductMiddleDto> productMiddles = response.getModel();
            if (CollUtilX.isEmpty(productMiddles)) {
                return productDetailDTO;
            }

            // 转换数据结构
            productDetailDTO = assembleProductDetailDTO(productMiddles, request.getHotelId(), agentAccountConfig, orgCurrency);

            // 存放所有供应商编码
            Set<String> supplyCodeSet = new HashSet<>();
            // 存放所有产品id
            List<String> productIds = new ArrayList<>();
            for (RoomItem roomItem : productDetailDTO.getRoomItems()) {
                for (ProductDetail productDetail : roomItem.getProducts()) {
                    supplyCodeSet.add(productDetail.getSupplyCode());
                    productIds.add(productDetail.getRatePlanId());
                }
            }

            // 供应商为空直接返回
            if (CollUtilX.isEmpty(supplyCodeSet)) {
                return productDetailDTO;
            }

            // 商家转客户汇率
            BigDecimal orgToAgentRate = RateUtil.getRate(orgCurrency, agentAccountConfig.getSettlementCurrency());
            if (orgToAgentRate == null || orgToAgentRate.compareTo(BigDecimal.ZERO) == 0) {
                Map<String, String> map = new HashMap<>();
                map.put(SlsEnum.LEVEL.getType(), SlsLogLevelEnum.ERROR.getLevel());
                map.put(SlsEnum.NAME.getType(), "queryProductDetailTmp-direct");
                map.put(SlsEnum.MESSAGE.getType(), "商家转客户汇率不存在，orgCurrency=" + orgCurrency + "，settlementCurrency=" + agentAccountConfig.getSettlementCurrency() + "，requestId=" + request.getRequestId());
                slsLoggerUtil.saveLog(map, "queryProductDetailTmp-direct", "tiangong-product-server");
                return null;
            }

            // 查询分销商-供应商加幅
            Date start2 = new Date();
            Map<String, SupplyIncrease> agentSupplyIncreaseMap = queryUtil.getSupplyIncrease(request.getAgentCode(), new ArrayList<>(supplyCodeSet));
            saveSlsLog("排查慢问题：查询分销商-供应商加幅，agentCode=" + request.getAgentCode() + ",supplyCodes=" + JSONUtil.toJsonStr(supplyCodeSet), start2, request.getRequestId());

            // 查询分销商-供应商-酒店加幅
            Date start3 = new Date();
            Map<String, SupplyHotelIncrease> agentSupplyHotelIncreaseMap = queryUtil.getSupplyHotelIncrease(request.getAgentCode(), new ArrayList<>(supplyCodeSet), Collections.singletonList(request.getHotelId()));
            saveSlsLog("排查慢问题：查询分销商-供应商-酒店加幅，agentCode=" + request.getAgentCode() + ",supplyCodes=" + JSONUtil.toJsonStr(supplyCodeSet) + ",hotelId" + request.getHotelId(), start3, request.getRequestId());

            // 过滤产品
            filterProduct(request, productDetailDTO, supplyMap, orgCurrency);

            // 获取产品id映射
            Map<String, String> productIdMapping = queryUtil.getCacheProductIdsWithUUID(productIds, request.getHotelId());

            // 设置产品基本信息和对价格进行加幅
            Date start6 = new Date();

            // 使用线程安全的并发集合 begin
            ConcurrentLinkedQueue<RoomItem> roomItemsQueue = new ConcurrentLinkedQueue<>();
            // 线程计数器
            CountDownLatch countDownLatch = new CountDownLatch(productDetailDTO.getRoomItems().size());
            // 并发执行
            productDetailDTO.getRoomItems().forEach(roomItem -> processingProductInfoExecutor.execute(() -> {
                try {
                    roomItemsQueue.add(convertRoomItem(agentAccountConfig, request, roomItem, agentSupplyIncreaseMap, agentSupplyHotelIncreaseMap, supplyMap, orgToAgentRate, orgCurrency, productIdMapping));
                } catch (Exception e) {
                    log.error("异步处理房型产品信息系统异常", e);
                } finally {
                    countDownLatch.countDown();
                }
            }));
            try {
                boolean await = countDownLatch.await(settingsConstant.getQuerySupplyProductWaitTimeOut(), TimeUnit.MILLISECONDS);
                if (!await) {
                    log.error("异步处理产品信息超时，耗时：{}ms，requestId={}", System.currentTimeMillis() - start6.getTime(), request.getRequestId());
                }
            } catch (Exception e) {
                log.error("处理房型产品信息异常");
            }
            List<RoomItem> roomItems = new ArrayList<>(roomItemsQueue);
            saveSlsLog("排查慢问题：设置产品基本信息和对价格进行加幅，productDetailDTO=" + JSONUtil.toJsonStr(productDetailDTO), start6, request.getRequestId());

            // 封装每日价格
            Date start7 = new Date();
            List<RoomItem> newRoomItems = assemblyDaysSalePrice(roomItems, agentAccountConfig, orgToAgentRate, request.getShowSupplyName(), request.getLanguage(), orgCurrency);
            saveSlsLog("排查慢问题：设置产品每日价格，newRoomItems=" + JSONUtil.toJsonStr(newRoomItems), start7, request.getRequestId());
            productDetailDTO.setRoomItems(newRoomItems);
        }
        saveSlsLog("排查慢问题：直连产品查询总耗时", start, request.getRequestId());
        return productDetailDTO;
    }

    /**
     * 组装产品信息
     */
    private ProductDetailResponse assembleProductDetailDTO(List<ProductMiddleDto> productMiddles, Long hotelId, AgentAccountConfig agentAccountConfig, int orgCurrency) {
        ProductDetailResponse productDetailDTO = new ProductDetailResponse();
        productDetailDTO.setHotelId(hotelId);
        Map<String, String> roomNameMap = new HashMap<>();
        Map<String, List<ProductDetail>> roomMap = new HashMap<>();
        productMiddles.parallelStream().forEach(productMiddleDto -> {
            ProductDetail productDetail = convertToProductDetail(productMiddleDto, agentAccountConfig, orgCurrency);
            if (roomMap.containsKey(productMiddleDto.getSpRoomTypeId())) {
                roomMap.get(productMiddleDto.getSpRoomTypeId()).add(productDetail);
            } else {
                List<ProductDetail> productDetails = new ArrayList<>();
                productDetails.add(productDetail);
                roomMap.put(productMiddleDto.getSpRoomTypeId(), productDetails);
            }
            if (!roomNameMap.containsKey(productMiddleDto.getSpRoomTypeId())) {
                roomNameMap.put(productMiddleDto.getSpRoomTypeId(), productMiddleDto.getSpRoomName());
            }
        });
        List<RoomItem> roomItems = roomMap.entrySet().stream().filter(item -> item.getKey().matches("\\d+")).map(item -> {
            RoomItem roomItem = new RoomItem();
            roomItem.setRoomId(Long.valueOf(item.getKey()));
            roomItem.setRoomName(roomNameMap.get(item.getKey()));
            roomItem.setProducts(item.getValue());
            return roomItem;
        }).collect(toList());
        productDetailDTO.setRoomItems(roomItems);
        return productDetailDTO;
    }

    /**
     * 价格计划转换
     */
    private ProductDetail convertToProductDetail(ProductMiddleDto productMiddleDto, AgentAccountConfig agentAccountConfig, int orgCurrency) {
        ProductDetail productDetail = new ProductDetail();
        productDetail.setRatePlanId(productMiddleDto.getSpPricePlanId().concat("#").concat(productMiddleDto.getSpPricePlanName()));
        productDetail.setRatePlanName(productMiddleDto.getSpPricePlanName());
        if (CollUtilX.isNotEmpty(productMiddleDto.getBedTypeDetails())) {
            List<BedTypesDetailDto> bedTypesDetails = BeanUtil.copyToList(productMiddleDto.getBedTypeDetails(), BedTypesDetailDto.class);
            productDetail.setBedTypeDetails(bedTypesDetails);
        }
        productDetail.setWindowType(productMiddleDto.getWindowType());
        productDetail.setMaxGuestPerson(productMiddleDto.getMaxGuestPerson());
        productDetail.setMinGuestPerson(productMiddleDto.getMinGuestPerson());
        productDetail.setImmediateConfirm(productMiddleDto.getImmediateConfirm());
        if (productMiddleDto.getHourlyRoom() == 1) {
            productDetail.setHourlyRoom(1);
            productDetail.setHourlyRoomInfo(productMiddleDto.getHourlyRoomInfo());
        }
        productDetail.setLabelType(productMiddleDto.getLabelType());
        productDetail.setAddBed(productMiddleDto.getAddBed());
        productDetail.setAddBedPrice(productMiddleDto.getAddBedPrice());
        productDetail.setAddBedPriceDesc(productMiddleDto.getAddBedPriceDesc());
        productDetail.setAvgPrice(productMiddleDto.getAvgPrice());
        productDetail.setCertificateFlag(productMiddleDto.getCertificateFlag());
        productDetail.setBroadNet(productMiddleDto.getBroadBand());
        productDetail.setSupplyCode(productMiddleDto.getSupplyCode());
        productDetail.setBookType(productMiddleDto.getBookType());
        productDetail.setInvoiceModel(productMiddleDto.getInvoiceModel());
        productDetail.setInvoiceType(productMiddleDto.getInvoiceType());
        productDetail.setPayAtHotelFlag(productMiddleDto.getPayAtHotelFlag());
        productDetail.setProductLabel(productMiddleDto.getProductLabel());
        productDetail.setSmallProductLabel(productMiddleDto.getSmallProductLabel());
        productDetail.setTips(productMiddleDto.getTips());
        List<RoomItemDetail> roomItemDetails = convertToRoomItemDetails(productMiddleDto.getRoomItemDetails(), agentAccountConfig, orgCurrency);
        productDetail.setRoomItemDetails(roomItemDetails);
        return productDetail;
    }

    /**
     * 组装每间房每日价格计划
     */
    private List<RoomItemDetail> convertToRoomItemDetails(List<RoomItemDetailDto> roomItemDetails, AgentAccountConfig agentAccountConfig, int orgCurrency) {
        return roomItemDetails.stream().map(roomItemDetail -> {
            RoomItemDetail roomItem = new RoomItemDetail();
            roomItem.setRoomIndex(roomItemDetail.getRoomIndex());
            roomItem.setAdultNum(roomItemDetail.getAdultNum());
            roomItem.setChildAges(roomItemDetail.getChildAges());
            // 每间 价格明细(税费)
            if (roomItemDetail.getTaxDetail() != null) {
                TaxDetailDto taxDetailDto = BeanUtil.copyProperties(roomItemDetail.getTaxDetail(), TaxDetailDto.class);
                roomItem.setTaxDetail(taxDetailDto);
            }
            roomItem.setPayInStorePrice(roomItemDetail.getPayInStorePrice());
            roomItem.setPayInStoreCurrency(roomItemDetail.getPayInStoreCurrency());
            roomItem.setSupplyPayInStorePrice(roomItemDetail.getSupplyPayInStorePrice());
            roomItem.setSupplyPayInStoreCurrency(roomItemDetail.getSupplyPayInStoreCurrency());
            if (CollUtilX.isNotEmpty(roomItemDetail.getProductDetails())) {
                List<PriceItem> priceItems = roomItemDetail.getProductDetails().stream().map(productDetailMiddleDto -> {
                    PriceItem priceItem = new PriceItem();
                    priceItem.setSaleDate(productDetailMiddleDto.getSaleDate());
                    priceItem.setTaxDetail(CommonConvert.INSTANCE.convertTaxDetail(productDetailMiddleDto.getTaxDetail()));
                    priceItem.setBasePrice(productDetailMiddleDto.getBasePrice());
                    priceItem.setBaseCurrency(productDetailMiddleDto.getCurrency());
                    priceItem.setBreakfastType(productDetailMiddleDto.getBreakfastType());
                    priceItem.setBreakfastNum(productDetailMiddleDto.getBreakfastNum());
                    priceItem.setRoomStatus(productDetailMiddleDto.getRoomStatus());
                    priceItem.setOverDraft(productDetailMiddleDto.getOverDraft());
                    priceItem.setQuotaNum(productDetailMiddleDto.getQuotaNum());
                    priceItem.setBookRestrictionType(productDetailMiddleDto.getBookRestrictType());
                    priceItem.setBookRestrictionDay(productDetailMiddleDto.getBookRestrictDays());
                    priceItem.setBookRestrictionTime(productDetailMiddleDto.getBookRestrictTime());
                    priceItem.setOccupancyRestrictionType(productDetailMiddleDto.getOccupancyRestrictType());
                    priceItem.setOccupancyRestrictionDay(productDetailMiddleDto.getOccupancyRestrictDays());
                    priceItem.setMinRoomCount(productDetailMiddleDto.getMinBookRoomsRestrict());
                    priceItem.setMaxRoomCount(productDetailMiddleDto.getMaxBookRoomsRestrict());
                    priceItem.setCancelRestrictions(CommonConvert.INSTANCE.convertCancelRestrictions(productDetailMiddleDto.getCancelRestrictions()));
                    if (CollUtilX.isNotEmpty(priceItem.getCancelRestrictions())) {
                        priceItem.getCancelRestrictions().forEach(cancelRestriction -> {
                            if (CollUtilX.isNotEmpty(cancelRestriction.getCancelPenalties())) {
                                List<CancelPenalDTO> dtoList = cancelRestriction.getCancelPenalties().stream().filter(item -> item.getPenaltiesType() != null
                                        && item.getPenaltiesValue() != null).map(item -> {
                                    CancelPenalDTO cancelPenalDTO = new CancelPenalDTO();
                                    cancelPenalDTO.setPenaltiesType(item.getPenaltiesType());
                                    cancelPenalDTO.setPenaltiesValue(item.getPenaltiesValue());
                                    cancelPenalDTO.setStartDate(item.getStartDate());
                                    cancelPenalDTO.setEndData(item.getEndData());

                                    if (item.getPenaltiesType().equals(2) && item.getCurrency() != null && item.getCurrency().compareTo(agentAccountConfig.getSettlementCurrency()) != 0) {
                                        // 转换商家币种
                                        BigDecimal orgRate = BigDecimal.ONE;
                                        if (new BigDecimal(item.getCurrency()).compareTo(new BigDecimal(orgCurrency)) != 0) {
                                            orgRate = RateUtil.getRate(item.getCurrency(), orgCurrency);
                                            if (orgRate == null) {
                                                throw new SysException(ErrorCodeEnum.EXCHANGE_RATE_IS_NOT_ALREADT);
                                            }
                                        }
                                        // 转换汇率
                                        BigDecimal cancelPenaltiesAmt = CommonTgUtils.setRate(new BigDecimal(item.getPenaltiesValue()), orgRate);
                                        // 转换客户币种
                                        BigDecimal agentRate = BigDecimal.ONE;
                                        if (new BigDecimal(orgCurrency).compareTo(new BigDecimal(agentAccountConfig.getSettlementCurrency())) != 0) {
                                            agentRate = RateUtil.getRate(orgCurrency, agentAccountConfig.getSettlementCurrency());
                                            if (agentRate == null) {
                                                throw new SysException(ErrorCodeEnum.EXCHANGE_RATE_IS_NOT_ALREADT);
                                            }
                                        }
                                        // 转换汇率
                                        cancelPenaltiesAmt = CommonTgUtils.setRate(cancelPenaltiesAmt, agentRate);
                                        // 根据客户配置进位
                                        BigDecimal cancelPenaltiesValue = CommonTgUtils.setScale(cancelPenaltiesAmt, agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType());
                                        cancelPenalDTO.setPenaltiesValue(String.valueOf(cancelPenaltiesValue));
                                        cancelPenalDTO.setCurrencyCode(SettlementCurrencyEnum.getCodeByKey(String.valueOf(agentAccountConfig.getSettlementCurrency())));
                                    } else {
                                        cancelPenalDTO.setPenaltiesValue(String.valueOf(item.getPenaltiesValue()));
                                        cancelPenalDTO.setCurrencyCode(SettlementCurrencyEnum.getCodeByKey(String.valueOf(item.getCurrency())));
                                    }
                                    return cancelPenalDTO;
                                }).collect(Collectors.toList());
                                cancelRestriction.setCancelRestrictionRemark(JSONUtil.toJsonStr(dtoList));
                            }
                        });
                    }
                    priceItem.setGuaranteeType(productDetailMiddleDto.getGuaranteeType());
                    priceItem.setGuaranteeCondition(productDetailMiddleDto.getGuaranteeCondition());
                    priceItem.setGuaranteeFeeType(productDetailMiddleDto.getGuaranteeFeeType());
                    return priceItem;
                }).collect(toList());
                roomItem.setPriceItems(priceItems);
            }
            return roomItem;
        }).collect(Collectors.toList());
    }

    /**
     * 转换房型数据
     */
    private RoomItem convertRoomItem(AgentAccountConfig agentAccountConfig,
                                     ProductDetailRequest request,
                                     RoomItem roomItem,
                                     Map<String, SupplyIncrease> agentSupplyIncreaseMap,
                                     Map<String, SupplyHotelIncrease> agentSupplyHotelIncreaseMap,
                                     Map<String, SupplierAddDTO> supplyConfigMap,
                                     BigDecimal orgToAgentRate,
                                     int orgCurrency,
                                     Map<String, String> productIdMapping) {
        RoomItem rsult = new RoomItem();
        rsult.setRoomName(roomItem.getRoomName());
        rsult.setRoomId(roomItem.getRoomId());
        rsult.setLowPrice(roomItem.getLowPrice());
        // 并行流处理
        List<ProductDetail> productDetails = roomItem.getProducts().stream().peek(productDetail -> {
            // 分销商-供应商加幅
            SupplyIncrease agentSupplyIncrease = agentSupplyIncreaseMap.get(productDetail.getSupplyCode());
            // 分销商-供应商-酒店加幅
            SupplyHotelIncrease agentSupplyHotelIncrease = agentSupplyHotelIncreaseMap.get(productDetail.getSupplyCode().concat(StrUtilX.SPLIT_CODE).concat(request.getHotelId().toString()));
            // 供应商配置，加幅，发票等
            SupplierAddDTO supplierAddDTO = supplyConfigMap.get(productDetail.getSupplyCode());

            // 1、处理每日数据
            List<RoomItemDetail> priceItemList = productDetail.getRoomItemDetails().stream().peek(priceItem -> {
                // 预付产品加幅，到店付产品不加幅
                if (Objects.isNull(productDetail.getPayAtHotelFlag()) || Objects.equals(productDetail.getPayAtHotelFlag(), 0)) {
                    // 预付
                    agentIncrease(priceItem, agentAccountConfig, supplierAddDTO, agentSupplyIncrease, agentSupplyHotelIncrease, request.getCheckInDate(), orgToAgentRate, orgCurrency);
                } else {
                    // 现付
                    agentIncrease(priceItem, agentAccountConfig, request.getCheckInDate(), orgToAgentRate, orgCurrency);
                }
            }).collect(Collectors.toList());
            productDetail.setRoomItemDetails(priceItemList);

            // 2、处理预订状态
            int quotaNum = handleBookStatus(request, productDetail);
            productDetail.setQuotaNum(quotaNum);

            // 3、设置产品id信息
            productDetail.setRatePlanId(productIdMapping.get(productDetail.getRatePlanId()));

            // 4、设置发票信息
            setInvoiceInfo(request, productDetail, supplierAddDTO);
        }).collect(Collectors.toList());
        rsult.setProducts(productDetails);
        return rsult;
    }

    /**
     * 获取供应商产品数据
     */
    private Response<List<ProductMiddleDto>> getSupplyProductInfo(ProductDetailRequest request, List<String> supplyCodes) {
        QueryProductInfoRequest queryProductInfoRequest = new QueryProductInfoRequest();
        queryProductInfoRequest.setSpHotelId(request.getHotelId() == null ? null : String.valueOf(request.getHotelId()));
        if (request.getRoomId() != null) {
            queryProductInfoRequest.setSpRoomId(request.getRoomId().intValue());
        }
        queryProductInfoRequest.setCheckInDate(request.getCheckInDate());
        queryProductInfoRequest.setCheckOutDate(request.getCheckOutDate());
        queryProductInfoRequest.setGuestQuantity(request.getGuestQuantity());
        queryProductInfoRequest.setRoomQty(request.getRoomNum());
        queryProductInfoRequest.setSupplyCodes(new HashSet<>(supplyCodes));
        queryProductInfoRequest.setOnlyHourRoom(request.getOnlyHourRoom());
        queryProductInfoRequest.setSupplyType(request.getSupplyType());
        if (CollUtilX.isNotEmpty(request.getRoomGuestNumbers())) {
            queryProductInfoRequest.setRoomGuestNumbers(CommonConvert.INSTANCE.convertRoomGuestNumber(request.getRoomGuestNumbers()));
        }
        queryProductInfoRequest.setUserAccount(request.getUserAccount());
        queryProductInfoRequest.setRequestId(request.getRequestId());
        queryProductInfoRequest.setCurrency(request.getSaleCurrency());
        queryProductInfoRequest.setLanguage(request.getLanguage());
        queryProductInfoRequest.setNationality(request.getNationality());
        // 获取供应商产品数据
        return supplyDirectShubProductRemote.queryProductList(queryProductInfoRequest);
    }

    /**
     * 落地和不落地数据合并
     */
    private ProductDetailResponse detailMerge(ProductDetailResponse agreementProductDetailResponse, ProductDetailResponse directProductDetailResponse, ProductDetailRequest request) {
        ProductDetailResponse productDetailResponse = new ProductDetailResponse();
        Date start = new Date();
        try {
            Long hotelId = Objects.isNull(agreementProductDetailResponse.getHotelId()) ? directProductDetailResponse.getHotelId() : agreementProductDetailResponse.getHotelId();
            if (hotelId == null) {
                return null;
            }
            productDetailResponse.setHotelId(hotelId);

            //赋值时区
            if (StringUtils.isNotEmpty(agreementProductDetailResponse.getTimeZone())) {
                productDetailResponse.setTimeZone(agreementProductDetailResponse.getTimeZone());
            } else if (Objects.nonNull(directProductDetailResponse) && StringUtils.isNotEmpty(directProductDetailResponse.getTimeZone())) {
                productDetailResponse.setTimeZone(directProductDetailResponse.getTimeZone());
            }

            if (directProductDetailResponse.getHourlyRoomItems() == null) {
                directProductDetailResponse.setHourlyRoomItems(new ArrayList<>());
            }
            List<RoomItem> hourlyRoomItems = Lists.newArrayList();
            Map<Long, List<RoomItem>> hourlyRoomItemMap = directProductDetailResponse.getHourlyRoomItems().stream().collect(groupingBy(RoomItem::getRoomId));
            hourlyRoomItemMap.forEach((key, value) -> {
                RoomItem roomItem = new RoomItem();
                String roomName = value.stream().findFirst().get().getRoomName();
                roomItem.setRoomId(key);
                roomItem.setRoomName(roomName);
                List<ProductDetail> productDetails = value.stream().map(RoomItem::getProducts).flatMap(Collection::stream).collect(toList());
                roomItem.setProducts(productDetails);
                hourlyRoomItems.add(roomItem);
            });
            productDetailResponse.setHourlyRoomItems(hourlyRoomItems);

            if (directProductDetailResponse.getRoomItems() == null) {
                directProductDetailResponse.setRoomItems(new ArrayList<>());
            }
            Map<Long, List<RoomItem>> roomItemMap = Stream.of(agreementProductDetailResponse, directProductDetailResponse)
                    .map(ProductDetailResponse::getRoomItems)
                    .filter(CollUtilX::isNotEmpty)
                    .flatMap(Collection::stream)
                    .collect(groupingBy(RoomItem::getRoomId, toList()));
            //获取客户的优选产品是否开启
            Boolean isPreferredProducts;
            if (Objects.nonNull(request) && StrUtil.isNotBlank(request.getAgentCode())) {
                Object preferredProductsStatusObject = RedisTemplateX.hashGet(RedisKey.AGENT_PREFERRED_PRODUCTS_STATUS, request.getAgentCode());
                if (Objects.isNull(preferredProductsStatusObject) || "1".equals(preferredProductsStatusObject.toString())) {
                    //默认开启
                    isPreferredProducts = Boolean.TRUE;
                } else {
                    isPreferredProducts = Boolean.FALSE;
                }
            } else {
                isPreferredProducts = Boolean.FALSE;
            }
            List<RoomItem> roomItems = Lists.newArrayList();
            roomItemMap.forEach((key, value) -> {
                RoomItem roomItem = new RoomItem();
                String roomName = value.stream().findFirst().get().getRoomName();
                roomItem.setRoomId(key);
                roomItem.setRoomName(roomName);
                List<ProductDetail> productDetails = value.stream().map(RoomItem::getProducts).flatMap(Collection::stream).peek(p -> setSupplierLabelInfo(p, request.getAgentCode())).collect(toList());
                //进行优选产品
                if (isPreferredProducts) {
                    productDetails = getPreferredProductsByRoom(productDetails);
                }
                roomItem.setProducts(productDetails);
                roomItems.add(roomItem);
            });
            productDetailResponse.setRoomItems(roomItems);
        } catch (Exception e) {
            log.error("detailMerge error!", e);
        } finally {
            saveSlsLog("排查慢问题：落地和不落地数据合并", start, request.getRequestId());
        }

        return productDetailResponse;
    }

    /**
     * 过滤产品
     */
    private void filterProduct(ProductDetailRequest request, ProductDetailResponse productDetailDTO,
                               Map<String, SupplierAddDTO> supplyConfigMap, int orgCurrency) {
        Date start5 = new Date();
        Iterator<RoomItem> iterator = productDetailDTO.getRoomItems().iterator();
        while (iterator.hasNext()) {
            RoomItem roomItem = iterator.next();
            List<ProductDetail> productDetailList = roomItem.getProducts();

            Iterator<ProductDetail> productDetailIterator = productDetailList.iterator();
            while (productDetailIterator.hasNext()) {
                ProductDetail productDetail = productDetailIterator.next();

                // 未设置供应商加幅，过滤掉产品
                if ((MapUtil.isEmpty(supplyConfigMap) || !supplyConfigMap.containsKey(productDetail.getSupplyCode()) ||
                        supplyConfigMap.get(productDetail.getSupplyCode()).getAdjustmentType() == null ||
                        supplyConfigMap.get(productDetail.getSupplyCode()).getModifiedAmt() == null)) {
                    productDetailIterator.remove();
                    continue;
                }

                // 未设置供应商-商家汇率，过滤掉产品
                BigDecimal rate = RateUtil.getRate(productDetail.getRoomItemDetails().get(0).getPriceItems().get(0).getBaseCurrency(), orgCurrency);
                if (Objects.isNull(rate) || rate.compareTo(BigDecimal.ZERO) == 0) {
                    productDetailIterator.remove();
                    continue;
                }
                if (productDetail.getRoomItemDetails().get(0).getPayInStoreCurrency() != null) {
                    BigDecimal payInStoreRate = RateUtil.getRate(productDetail.getRoomItemDetails().get(0).getPayInStoreCurrency(), orgCurrency);
                    if (Objects.isNull(payInStoreRate) || payInStoreRate.compareTo(BigDecimal.ZERO) == 0) {
                        productDetailIterator.remove();
                        continue;
                    }
                }

                // 判断产品类型是否一致、不一致过滤掉产品
                if (request.getPayMethod() != null && !request.getPayMethod().equals(productDetail.getPayAtHotelFlag())) {
                    productDetailIterator.remove();
                    continue;
                }

                // 添加供货单查询产品 判断是否过滤币种
                if (request.getShowSupplyName() != null && request.getShowSupplyName() == 1 && request.getBaseCurrency() != null) {
                    if (productDetail.getRoomItemDetails().get(0).getPriceItems().get(0).getBaseCurrency() == null ||
                            productDetail.getRoomItemDetails().get(0).getPriceItems().get(0).getBaseCurrency().compareTo(request.getBaseCurrency()) != 0) {
                        productDetailIterator.remove();
                    }
                }
            }

            // 如果房型下产品为空，删除房型
            if (productDetailList.isEmpty()) {
                iterator.remove();
            }
        }
        saveSlsLog("排查慢问题：过滤产品信息", start5, request.getRequestId());
    }

    /**
     * 处理预订状态
     */
    private int handleBookStatus(ProductDetailRequest request, ProductDetail productDetail) {
        // 不可定日期
        Set<String> notBookDateList = new HashSet<>();
        // 获取时间列表
        List<Date> dateList = DateUtilX.getDateList(DateUtilX.stringToDate(request.getCheckInDate()), DateUtilX.getDate(DateUtilX.stringToDate(request.getCheckOutDate()), -1, 0));

        int sum = 0;// 总天数
        int canBookCount = 0;
        int quotaNum = 4;// 判断是否需要展示仅剩多少间，三间及以下展示
        for (RoomItemDetail roomItemDetail : productDetail.getRoomItemDetails()) {
            sum += dateList.size();
            Map<String, PriceItem> priceItemMap = roomItemDetail.getPriceItems().stream().collect(Collectors.toMap(PriceItem::getSaleDate, Function.identity()));
            for (Date date : dateList) {
                String dateStr = DateUtilX.dateToString(date);
                if (priceItemMap.containsKey(dateStr)) {
                    PriceItem priceItem = priceItemMap.get(dateStr);
                    if (priceItem.getRoomStatus() != null && priceItem.getRoomStatus() == RoomStateEnum.HAVA_ROOM.key && (priceItem.getOverDraft() == null || !priceItem.getOverDraft())) {
                        if (priceItem.getQuotaNum() != null && priceItem.getQuotaNum() >= 0 && (request.getRoomNum() == null || priceItem.getQuotaNum().compareTo(request.getRoomNum()) >= 0)) {
                            canBookCount++;
                        } else {
                            // 不可订日期
                            notBookDateList.add(dateStr);
                        }
                        if (priceItem.getQuotaNum() != null && priceItem.getQuotaNum() < quotaNum) {
                            quotaNum = priceItem.getQuotaNum();
                        }
                    } else {
                        canBookCount++;
                    }
                } else {
                    // 不可订日期
                    notBookDateList.add(dateStr);
                }
            }
        }

        if (sum == 0 || canBookCount == 0) {// 不可订
            productDetail.setBookType(CanBookEnum.CAN_NOT_BOOK.value);
        } else if (sum == canBookCount) {// 可订
            productDetail.setBookType(CanBookEnum.CAN_BOOK.value);
        } else {// 部分可订
            productDetail.setBookType(CanBookEnum.CAN_PART_BOOK.value);
            productDetail.setNotBookDateList(notBookDateList);// 不可定日期
        }
        return quotaNum;
    }

    /**
     * 设置发票信息
     */
    private void setInvoiceInfo(ProductDetailRequest request, ProductDetail productDetail, SupplierAddDTO supplierAddDTO) {
        if (Objects.nonNull(supplierAddDTO)) {
            productDetail.setInvoiceModel(supplierAddDTO.getInvoiceModel());
            productDetail.setInvoiceType(supplierAddDTO.getInvoiceType());
            // 当是商家账号登录时才设置供应商名称
            if (request.getUserType() != null && request.getUserType().equals(UserTypeEnum.SUPER_ADMIN.type) || (request.getShowSupplyName() != null && request.getShowSupplyName() == 1)) {
                productDetail.setSupplyName(supplierAddDTO.getSupplierName());
            }
            productDetail.setQuickProcessingSwitch(supplierAddDTO.getQuickProcessingSwitch() != null ? supplierAddDTO.getQuickProcessingSwitch() : 0);
        } else {
            productDetail.setQuickProcessingSwitch(0);
        }
    }

    /**
     * 计算分销商加幅
     */
    private void agentIncrease(RoomItemDetail roomItemDetail, AgentAccountConfig agentAccountConfig, SupplierAddDTO supplierIncreaseConfig,
                               SupplyIncrease agentSupplyIncrease, SupplyHotelIncrease agentSupplyHotelIncrease, String checkInDate, BigDecimal orgToAgentRate, int orgCurrency) {
        try {
            // 查询汇率
            BigDecimal rate = BigDecimal.ONE;
            if (Objects.nonNull(agentAccountConfig) && CollUtilX.isNotEmpty(roomItemDetail.getPriceItems())) {
                rate = RateUtil.getRate(roomItemDetail.getPriceItems().get(0).getBaseCurrency(), orgCurrency);
            }
            // 遍历价格
            for (PriceItem priceItem : roomItemDetail.getPriceItems()) {
                // 设置条款日期
                for (CancelRestriction cancelRestriction : priceItem.getCancelRestrictions()) {
                    if (Objects.equals(2, cancelRestriction.getCancelRestrictionType()) || Objects.equals(3, cancelRestriction.getCancelRestrictionType())) {
                        if (null != cancelRestriction.getCancelRestrictionDay()) {
                            cancelRestriction.setCancelRestrictionDate(DateUtilX.dateToString(DateUtilX.getDate(DateUtilX.stringToDate(checkInDate), -cancelRestriction.getCancelRestrictionDay(), 0)));
                        }
                    }
                }
                // 设置结算币种
                if (Objects.nonNull(agentAccountConfig.getSettlementCurrency())) {
                    priceItem.setSettlementCurrency(agentAccountConfig.getSettlementCurrency());
                } else {
                    priceItem.setSettlementCurrency(Integer.valueOf(CompanyDTO.DEFAULT_COIN_KEY));
                }
                // 每次税费是单独计算
                if (priceItem.getSaleDate().equals(checkInDate) && Objects.nonNull(roomItemDetail.getTaxDetail())) {
                    TaxDetailDto taxDetail = roomItemDetail.getTaxDetail();
                    // 设置供应商价格，计算底价用
                    taxDetail.setSupplyRoomPrice(taxDetail.getRoomPrice());
                    taxDetail.setSupplySalesTax(taxDetail.getSalesTax());
                    taxDetail.setSupplyTaxFee(taxDetail.getTaxFee());
                    taxDetail.setSupplyOtherTax(taxDetail.getOtherTax());

                    // 币种不一样
                    if (priceItem.getBaseCurrency() != null && !priceItem.getBaseCurrency().equals(orgCurrency)) {
                        // 转换汇率 供应商转商家
                        taxDetail.setSalesTax(CommonTgUtils.setRate(taxDetail.getSalesTax(), rate));
                        taxDetail.setTaxFee(CommonTgUtils.setRate(taxDetail.getTaxFee(), rate));
                        taxDetail.setOtherTax(CommonTgUtils.setRate(taxDetail.getOtherTax(), rate));
                    }
                    // 币种不一样
                    if (priceItem.getSettlementCurrency() != null && !priceItem.getSettlementCurrency().equals(orgCurrency)) {
                        // 转换汇率 商家转客户
                        taxDetail.setSalesTax(CommonTgUtils.setRate(taxDetail.getSalesTax(), orgToAgentRate));
                        taxDetail.setTaxFee(CommonTgUtils.setRate(taxDetail.getTaxFee(), orgToAgentRate));
                        taxDetail.setOtherTax(CommonTgUtils.setRate(taxDetail.getOtherTax(), orgToAgentRate));
                    }

                    // 统一进位
                    taxDetail.setSalesTax(CommonTgUtils.setScale(taxDetail.getSalesTax(), agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType()));
                    taxDetail.setTaxFee(CommonTgUtils.setScale(taxDetail.getTaxFee(), agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType()));
                    taxDetail.setOtherTax(CommonTgUtils.setScale(taxDetail.getOtherTax(), agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType()));
                }

                // 每日信息
                TaxDetailDto taxDetail = priceItem.getTaxDetail();

                // 币种不一样
                if (priceItem.getBaseCurrency() != null && !priceItem.getBaseCurrency().equals(orgCurrency)) {
                    // 转换汇率 供应商转商家
                    taxDetail.setRoomPrice(CommonTgUtils.setRate(taxDetail.getRoomPrice(), rate));
                    taxDetail.setSalesTax(CommonTgUtils.setRate(taxDetail.getSalesTax(), rate));
                    taxDetail.setTaxFee(CommonTgUtils.setRate(taxDetail.getTaxFee(), rate));
                    taxDetail.setOtherTax(CommonTgUtils.setRate(taxDetail.getOtherTax(), rate));
                }

                // 底价
                BigDecimal basePrice = BigDecimal.ZERO;
                basePrice = basePrice.add(CommonTgUtils.formatBigDecimal(taxDetail.getRoomPrice()));

                // 供应商加幅值
                BigDecimal baseSupplierAdjustment = BigDecimal.ZERO;
                if (Objects.nonNull(supplierIncreaseConfig)) {
                    baseSupplierAdjustment = CommonTgUtils.adjustmentAmt(basePrice, supplierIncreaseConfig.getAdjustmentType(), supplierIncreaseConfig.getModifiedAmt());
                    // 当是加百分百、加幅金额比最低加幅金额小时，取最低加幅金额
                    if (supplierIncreaseConfig.getAdjustmentType() != null && Objects.equals(SaleAdjustmentTypeEnum.PLUS_PERCENTAGE.no, supplierIncreaseConfig.getAdjustmentType())
                            && supplierIncreaseConfig.getLowestIncreaseAmt() != null && baseSupplierAdjustment.compareTo(supplierIncreaseConfig.getLowestIncreaseAmt()) < 0) {
                        baseSupplierAdjustment = supplierIncreaseConfig.getLowestIncreaseAmt();
                    }
                }

                // 分销商-供应商-酒店加幅值
                BigDecimal baseAgentHotelAdjustment = BigDecimal.ZERO;
                if(agentSupplyHotelIncrease != null){
                    // 优先酒店级别加幅
                    // 供应商加幅后的价格
                    BigDecimal supplierPrice = baseSupplierAdjustment.add(basePrice);
                    baseAgentHotelAdjustment = CommonTgUtils.adjustmentAmt(supplierPrice, agentSupplyHotelIncrease.getAdjustmentType(), agentSupplyHotelIncrease.getModifiedAmt());
                    // 当是加百分百、加幅金额比最低加幅金额小时，取最低加幅金额
                    if (agentSupplyHotelIncrease.getAdjustmentType() != null && Objects.equals(SaleAdjustmentTypeEnum.PLUS_PERCENTAGE.no, agentSupplyHotelIncrease.getAdjustmentType())
                            && agentSupplyHotelIncrease.getLowestIncreaseAmt() != null && baseAgentHotelAdjustment.compareTo(agentSupplyHotelIncrease.getLowestIncreaseAmt()) < 0) {
                        baseAgentHotelAdjustment = agentSupplyHotelIncrease.getLowestIncreaseAmt();
                    }
                } else if (agentSupplyIncrease != null) {
                    // 分销商级别加幅
                    // 供应商加幅后的价格
                    BigDecimal supplierPrice = baseSupplierAdjustment.add(taxDetail.getRoomPrice());
                    baseAgentHotelAdjustment = CommonTgUtils.adjustmentAmt(supplierPrice, agentSupplyIncrease.getAdjustmentType(), agentSupplyIncrease.getModifiedAmt());
                    // 当是加百分百、加幅金额比最低加幅金额小时，取最低加幅金额
                    if (agentSupplyIncrease.getAdjustmentType() != null && Objects.equals(SaleAdjustmentTypeEnum.PLUS_PERCENTAGE.no, agentSupplyIncrease.getAdjustmentType())
                            && agentSupplyIncrease.getLowestIncreaseAmt() != null && baseAgentHotelAdjustment.compareTo(agentSupplyIncrease.getLowestIncreaseAmt()) < 0) {
                        baseAgentHotelAdjustment = agentSupplyIncrease.getLowestIncreaseAmt();
                    }
                }

                // 折扣
                BigDecimal discount = BigDecimal.ZERO;
                // 其他税
                BigDecimal otherTax = CommonTgUtils.formatBigDecimal(taxDetail.getOtherTax());
                if (baseSupplierAdjustment.compareTo(BigDecimal.ZERO) < 0) {
                    discount = discount.add(baseSupplierAdjustment);
                } else {
                    otherTax = otherTax.add(baseSupplierAdjustment);
                }
                if (baseAgentHotelAdjustment.compareTo(BigDecimal.ZERO) < 0) {
                    discount = discount.add(baseAgentHotelAdjustment);
                } else {
                    otherTax = otherTax.add(baseAgentHotelAdjustment);
                }
                // 其他税 = 其他税 + 供应商加幅值 + 分销商加幅值 (加幅金额算到其他税里面)
                taxDetail.setOtherTax(otherTax);
                // 折扣
                taxDetail.setDiscount(discount);

                // 币种不一样
                if (priceItem.getSettlementCurrency() != null && !priceItem.getSettlementCurrency().equals(orgCurrency)) {
                    // 转换汇率 商家转客户
                    taxDetail.setRoomPrice(CommonTgUtils.setRate(taxDetail.getRoomPrice(), orgToAgentRate));
                    taxDetail.setSalesTax(CommonTgUtils.setRate(taxDetail.getSalesTax(), orgToAgentRate));
                    taxDetail.setTaxFee(CommonTgUtils.setRate(taxDetail.getTaxFee(), orgToAgentRate));
                    taxDetail.setOtherTax(CommonTgUtils.setRate(taxDetail.getOtherTax(), orgToAgentRate));
                    taxDetail.setDiscount(CommonTgUtils.setRate(taxDetail.getDiscount(), orgToAgentRate));
                }

                // 统一进位
                taxDetail.setRoomPrice(CommonTgUtils.setScale(taxDetail.getRoomPrice(), agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType()));
                taxDetail.setSalesTax(CommonTgUtils.setScale(taxDetail.getSalesTax(), agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType()));
                taxDetail.setTaxFee(CommonTgUtils.setScale(taxDetail.getTaxFee(), agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType()));
                taxDetail.setOtherTax(CommonTgUtils.setScale(taxDetail.getOtherTax(), agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType()));
                taxDetail.setDiscount(CommonTgUtils.setScale(taxDetail.getDiscount(), agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType()));

                // 售价 = 房费 + 税费(销售税、税费、其他税)
                priceItem.setSalePrice(taxDetail.getRoomPrice().add(taxDetail.getSalesTax()).add(taxDetail.getTaxFee()).add(taxDetail.getOtherTax()).add(taxDetail.getDiscount()));
                // 按次税费，计算在首日价格里
                if (priceItem.getSaleDate().equals(checkInDate) && Objects.nonNull(roomItemDetail.getTaxDetail())) {
                    TaxDetailDto roomTaxDetail = roomItemDetail.getTaxDetail();
                    priceItem.setSalePrice(priceItem.getSalePrice().add(roomTaxDetail.getSalesTax()).add(roomTaxDetail.getTaxFee()).add(roomTaxDetail.getOtherTax()));
                }
            }
        } catch (Exception e) {
            log.error("agentIncrease error!", e);
        }
    }

    /**
     * 计算分销商加幅-现付
     */
    private void agentIncrease(RoomItemDetail roomItemDetail, AgentAccountConfig agentAccountConfig, String checkInDate, BigDecimal orgToAgentRate, int orgCurrency) {
        try {
            // 查询汇率
            BigDecimal rate = BigDecimal.ONE;
            if (Objects.nonNull(agentAccountConfig) && CollUtilX.isNotEmpty(roomItemDetail.getPriceItems())) {
                rate = RateUtil.getRate(roomItemDetail.getPriceItems().get(0).getBaseCurrency(), orgCurrency);
            }
            for (PriceItem priceItem : roomItemDetail.getPriceItems()) {
                // 设置条款日期
                for (CancelRestriction cancelRestriction : priceItem.getCancelRestrictions()) {
                    if (Objects.equals(2, cancelRestriction.getCancelRestrictionType()) || Objects.equals(3, cancelRestriction.getCancelRestrictionType())) {
                        if (null != cancelRestriction.getCancelRestrictionDay()) {
                            cancelRestriction.setCancelRestrictionDate(DateUtilX.dateToString(DateUtilX.getDate(DateUtilX.stringToDate(checkInDate), -cancelRestriction.getCancelRestrictionDay(), 0)));
                        }
                    }
                }
                // 设置结算币种
                if (Objects.nonNull(agentAccountConfig.getSettlementCurrency())) {
                    priceItem.setSettlementCurrency(agentAccountConfig.getSettlementCurrency());
                } else {
                    priceItem.setSettlementCurrency(Integer.valueOf(CompanyDTO.DEFAULT_COIN_KEY));
                }
                // 每次税费是单独计算
                if (priceItem.getSaleDate().equals(checkInDate) && Objects.nonNull(roomItemDetail.getTaxDetail())) {
                    TaxDetailDto taxDetail = roomItemDetail.getTaxDetail();
                    // 设置供应商价格，计算底价用
                    taxDetail.setSupplyRoomPrice(taxDetail.getRoomPrice());
                    taxDetail.setSupplySalesTax(taxDetail.getSalesTax());
                    taxDetail.setSupplyTaxFee(taxDetail.getTaxFee());
                    taxDetail.setSupplyOtherTax(taxDetail.getOtherTax());
                    // 币种不一样
                    if (priceItem.getBaseCurrency() != null && !priceItem.getBaseCurrency().equals(orgCurrency)) {
                        // 转换汇率 供应商转商家
                        taxDetail.setSalesTax(CommonTgUtils.setRate(taxDetail.getSalesTax(), rate));
                        taxDetail.setTaxFee(CommonTgUtils.setRate(taxDetail.getTaxFee(), rate));
                        taxDetail.setOtherTax(CommonTgUtils.setRate(taxDetail.getOtherTax(), rate));
                    }
                    // 币种不一样
                    if (priceItem.getSettlementCurrency() != null && !priceItem.getSettlementCurrency().equals(orgCurrency)) {
                        // 转换汇率 商家转客户
                        taxDetail.setSalesTax(CommonTgUtils.setRate(taxDetail.getSalesTax(), orgToAgentRate));
                        taxDetail.setTaxFee(CommonTgUtils.setRate(taxDetail.getTaxFee(), orgToAgentRate));
                        taxDetail.setOtherTax(CommonTgUtils.setRate(taxDetail.getOtherTax(), orgToAgentRate));
                    }

                    // 统一进位
                    taxDetail.setSalesTax(CommonTgUtils.setScale(taxDetail.getSalesTax(), agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType()));
                    taxDetail.setTaxFee(CommonTgUtils.setScale(taxDetail.getTaxFee(), agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType()));
                    taxDetail.setOtherTax(CommonTgUtils.setScale(taxDetail.getOtherTax(), agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType()));
                }

                TaxDetailDto taxDetail = priceItem.getTaxDetail();
                // 币种不一样
                if (priceItem.getBaseCurrency() != null && !priceItem.getBaseCurrency().equals(orgCurrency)) {
                    // 转换汇率 供应商转商家
                    taxDetail.setRoomPrice(CommonTgUtils.setRate(taxDetail.getRoomPrice(), rate));
                    taxDetail.setSalesTax(CommonTgUtils.setRate(taxDetail.getSalesTax(), rate));
                    taxDetail.setTaxFee(CommonTgUtils.setRate(taxDetail.getTaxFee(), rate));
                    taxDetail.setOtherTax(CommonTgUtils.setRate(taxDetail.getOtherTax(), rate));
                }
                // 币种不一样
                if (priceItem.getSettlementCurrency() != null && !priceItem.getSettlementCurrency().equals(orgCurrency)) {
                    // 转换汇率 商家转客户
                    taxDetail.setRoomPrice(CommonTgUtils.setRate(taxDetail.getRoomPrice(), orgToAgentRate));
                    taxDetail.setSalesTax(CommonTgUtils.setRate(taxDetail.getSalesTax(), orgToAgentRate));
                    taxDetail.setTaxFee(CommonTgUtils.setRate(taxDetail.getTaxFee(), orgToAgentRate));
                    taxDetail.setOtherTax(CommonTgUtils.setRate(taxDetail.getOtherTax(), orgToAgentRate));
                }

                // 统一进位
                taxDetail.setRoomPrice(CommonTgUtils.setScale(taxDetail.getRoomPrice(), agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType()));
                taxDetail.setSalesTax(CommonTgUtils.setScale(taxDetail.getSalesTax(), agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType()));
                taxDetail.setTaxFee(CommonTgUtils.setScale(taxDetail.getTaxFee(), agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType()));
                taxDetail.setOtherTax(CommonTgUtils.setScale(taxDetail.getOtherTax(), agentAccountConfig.getDecimalPlaces(), agentAccountConfig.getRoundingType()));

                // 售价 = 房费 + 税费(销售税、税费、其他税)
                priceItem.setSalePrice(taxDetail.getRoomPrice().add(taxDetail.getSalesTax()).add(taxDetail.getTaxFee()).add(taxDetail.getOtherTax()));
                // 按次税费，计算在首日价格里
                if (priceItem.getSaleDate().equals(checkInDate) && Objects.nonNull(roomItemDetail.getTaxDetail())) {
                    TaxDetailDto roomTaxDetail = roomItemDetail.getTaxDetail();
                    priceItem.setSalePrice(priceItem.getSalePrice().add(roomTaxDetail.getSalesTax()).add(roomTaxDetail.getTaxFee()).add(roomTaxDetail.getOtherTax()));
                }
            }
        } catch (Exception e) {
            log.error("agentIncrease error!", e);
        }
    }

    private List<RoomItem> assemblyDaysSalePrice(List<RoomItem> roomItems,
                                                 AgentAccountConfig agentAccountConfig,
                                                 BigDecimal orgToAgentRate,
                                                 Integer showSupplyName,
                                                 String language,
                                                 int orgCurrency) {
        if (CollUtilX.isEmpty(roomItems)) {
            return Collections.emptyList();
        }

        return roomItems.stream()
                .filter(item -> CollUtilX.isNotEmpty(item.getProducts()))
                .map(roomItem -> processRoomItem(roomItem, agentAccountConfig, orgToAgentRate, showSupplyName, language, orgCurrency))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 处理每间数据
     */
    private RoomItem processRoomItem(RoomItem roomItem,
                                     AgentAccountConfig agentAccountConfig,
                                     BigDecimal orgToAgentRate,
                                     Integer showSupplyName,
                                     String language,
                                     int orgCurrency) {
        List<ProductDetail> processedProducts = roomItem.getProducts().stream()
                .map(product -> processProduct(product, agentAccountConfig, orgToAgentRate, showSupplyName, language, orgCurrency))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (CollUtilX.isEmpty(processedProducts)) {
            return null;
        }
        roomItem.setProducts(processedProducts);
        return roomItem;
    }

    /**
     * 处理产品数据
     */
    private ProductDetail processProduct(ProductDetail productDetail,
                                         AgentAccountConfig agentAccountConfig,
                                         BigDecimal orgToAgentRate,
                                         Integer showSupplyName,
                                         String language,
                                         int orgCurrency) {
        // 初始化聚合数据容器
        ProductAggregate aggregate = new ProductAggregate();

        // 扁平化处理所有价格项
        Map<String, List<PriceItem>> salePriceMap = productDetail.getRoomItemDetails().stream().peek(item -> {
                    aggregate.payInStoreCurrency = item.getPayInStoreCurrency();
                    aggregate.payInStorePrice = aggregate.payInStorePrice.add(CommonTgUtils.formatBigDecimal(item.getPayInStorePrice()));
                    accumulateTax(aggregate.totalTax, item.getTaxDetail());
                    accumulateTax(aggregate, item.getTaxDetail());
                }).flatMap(rid -> rid.getPriceItems().stream())
                .collect(Collectors.groupingBy(PriceItem::getSaleDate));

        // 处理每日价格
        List<PriceItem> priceItems = processDailyPrices(salePriceMap, agentAccountConfig, aggregate);

        // 设置计算结果
        return buildFinalProduct(productDetail, aggregate, priceItems, agentAccountConfig, orgToAgentRate, showSupplyName, language, orgCurrency);
    }

    // 辅助类用于聚合计算结果
    private static class ProductAggregate {
        BigDecimal totalSalePrice = BigDecimal.ZERO;
        BigDecimal totalBasePrice = BigDecimal.ZERO;
        TaxDetailDto totalTax = new TaxDetailDto();
        BigDecimal payInStorePrice = BigDecimal.ZERO;
        Integer payInStoreCurrency;
        Integer baseCurrency;
    }

    /**
     * 处理每日价格数据
     */
    private List<PriceItem> processDailyPrices(Map<String, List<PriceItem>> salePriceMap,
                                               AgentAccountConfig agentAccountConfig,
                                               ProductAggregate aggregate) {
        List<PriceItem> priceItems = new ArrayList<>(salePriceMap.size());
        int decimalPlaces = agentAccountConfig.getDecimalPlaces();
        int roundingType = agentAccountConfig.getRoundingType();

        for (Map.Entry<String, List<PriceItem>> entry : salePriceMap.entrySet()) {
            List<PriceItem> dailyPrices = entry.getValue();
            PriceItem template = CommonConvert.INSTANCE.convert(dailyPrices.get(0));
            TaxDetailDto dailyTax = new TaxDetailDto();

            BigDecimal dailySalePrice = dailyPrices.stream()
                    .map(price -> {
                        aggregate.baseCurrency = price.getBaseCurrency();
                        aggregate.totalSalePrice = aggregate.totalSalePrice.add(price.getSalePrice());
                        aggregate.totalBasePrice = aggregate.totalBasePrice.add(price.getBasePrice());
                        accumulateTax(aggregate.totalTax, price.getTaxDetail());
                        accumulateTax(dailyTax, price.getTaxDetail());
                        return price.getSalePrice();
                    })
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            template.setSalePrice(CommonTgUtils.divideSetScale(
                    dailySalePrice,
                    new BigDecimal(dailyPrices.size()),
                    decimalPlaces,
                    roundingType
            ));
            template.setTaxDetail(dailyTax);
            priceItems.add(template);
        }
        return priceItems;
    }

    /**
     * 累加税
     */
    private void accumulateTax(TaxDetailDto target, TaxDetailDto source) {
        if (source == null) return;
        target.setSalesTax(addBigDecimal(target.getSalesTax(), source.getSalesTax()));
        target.setTaxFee(addBigDecimal(target.getTaxFee(), source.getTaxFee()));
        target.setOtherTax(addBigDecimal(target.getOtherTax(), source.getOtherTax()));
    }

    /**
     * 累加税
     */
    private void accumulateTax(ProductAggregate aggregate, TaxDetailDto source) {
        if (source == null) return;
        aggregate.totalBasePrice = addBigDecimal(aggregate.totalBasePrice, source.getSupplySalesTax());
        aggregate.totalBasePrice = addBigDecimal(aggregate.totalBasePrice, source.getSupplyTaxFee());
        aggregate.totalBasePrice = addBigDecimal(aggregate.totalBasePrice, source.getSupplyOtherTax());
    }

    private BigDecimal addBigDecimal(BigDecimal a, BigDecimal b) {
        return (a == null ? BigDecimal.ZERO : a).add(b == null ? BigDecimal.ZERO : b);
    }

    /**
     * 构建产品对象
     */
    private ProductDetail buildFinalProduct(ProductDetail productDetail,
                                            ProductAggregate aggregate,
                                            List<PriceItem> priceItems,
                                            AgentAccountConfig agentAccountConfig,
                                            BigDecimal orgToAgentRate,
                                            Integer showSupplyName,
                                            String language,
                                            int orgCurrency) {
        // 设置基础信息
        productDetail.setTotalSalePrice(aggregate.totalSalePrice);
        productDetail.setTaxDetail(aggregate.totalTax);
        productDetail.setPriceItems(priceItems);

        // 处理支付信息
        processPaymentInfo(productDetail, aggregate, agentAccountConfig, orgToAgentRate, showSupplyName, orgCurrency);

        // 处理价格计划名称
        if (LanguageTypeEnum.en_US.getValue().equals(language)) {
            productDetail.setRatePlanName(processRatePlanName(productDetail.getRatePlanName()));
        }

        // 验证最终价格
        return validateFinalPrice(productDetail, agentAccountConfig);
    }

    /**
     * 处理产品信息
     */
    private void processPaymentInfo(ProductDetail productDetail,
                                    ProductAggregate aggregate,
                                    AgentAccountConfig agentAccountConfig,
                                    BigDecimal orgToAgentRate,
                                    Integer showSupplyName,
                                    int orgCurrency) {
        productDetail.setPayInStoreCurrency(aggregate.payInStoreCurrency);
        productDetail.setPayInStorePrice(aggregate.payInStorePrice);

        BigDecimal agentCurrencyAmt = calculateAgentCurrencyAmount(
                aggregate.payInStoreCurrency,
                agentAccountConfig.getSettlementCurrency(),
                aggregate.payInStorePrice,
                orgToAgentRate,
                agentAccountConfig,
                orgCurrency
        );
        productDetail.setPayInStorePriceAgentCurrencyAmt(agentCurrencyAmt);
        productDetail.setTotalFinallySalePrice(
                aggregate.totalSalePrice.add(agentCurrencyAmt != null ? agentCurrencyAmt : BigDecimal.ZERO)
        );

        if (shouldShowBasePrice(productDetail, showSupplyName)) {
            productDetail.setTotalBasePrice(aggregate.totalBasePrice);
            productDetail.setBaseCurrency(aggregate.baseCurrency);
        }
    }

    /**
     * 是否显示基础价格
     */
    private boolean shouldShowBasePrice(ProductDetail productDetail, Integer showSupplyName) {
        if (showSupplyName == null) return false;
        if (showSupplyName == 1) return true;
        return productDetail.getSupplyCode() != null && !productDetail.getSupplyCode().isEmpty();
    }

    /**
     * 计算金额
     */
    private BigDecimal calculateAgentCurrencyAmount(Integer sourceCurrency,
                                                    Integer targetCurrency,
                                                    BigDecimal amount,
                                                    BigDecimal orgToAgentRate,
                                                    AgentAccountConfig config,
                                                    int orgCurrency) {
        if (sourceCurrency == null || targetCurrency == null) return null;
        if (sourceCurrency.equals(targetCurrency)) return amount;

        BigDecimal rate = RateUtil.getRate(sourceCurrency, orgCurrency) == null ? BigDecimal.ONE : RateUtil.getRate(sourceCurrency, orgCurrency);
        BigDecimal multiplyRate = rate.multiply(orgToAgentRate);
        return CommonTgUtils.setScale(amount.multiply(multiplyRate), config.getDecimalPlaces(), config.getRoundingType());
    }

    /**
     * 处理产品名称
     */
    private String processRatePlanName(String originalName) {
        return StrUtilX.isNotEmpty(originalName) && StrUtilX.notIsEnglishAndSpace(originalName)
                ? "Business travel rate"
                : originalName;
    }

    /**
     * 验证最终价格
     */
    private ProductDetail validateFinalPrice(ProductDetail productDetail,
                                             AgentAccountConfig config) {
        BigDecimal avgPrice = CommonTgUtils.divideSetScale(
                productDetail.getPriceItems().stream()
                        .map(PriceItem::getSalePrice)
                        .reduce(BigDecimal.ZERO, BigDecimal::add),
                new BigDecimal(productDetail.getPriceItems().size()),
                config.getDecimalPlaces(),
                config.getRoundingType()
        );
        productDetail.setAvgPrice(avgPrice);
        return avgPrice.compareTo(BigDecimal.ZERO) > 0 ? productDetail : null;
    }

    /**
     * 组装客户可售酒店列表Entity
     */
    private AgentHotelLowestPriceEntity assemblyLowestPriceEntity(String agentCode, String hotelId,
                                                                  BigDecimal lowestPrice, Integer saleCurrency,
                                                                  BigDecimal payAtHotelPrice, Integer payAtHotelCurrency) {
        AgentHotelLowestPriceEntity agentHotelLowestPriceEntity = new AgentHotelLowestPriceEntity();
        agentHotelLowestPriceEntity.setAgentCode(agentCode);
        agentHotelLowestPriceEntity.setHotelId(Long.parseLong(hotelId));
        agentHotelLowestPriceEntity.setCreatedBy(Constant.SYSTEM);
        agentHotelLowestPriceEntity.setCreatedDt(new Date());
        agentHotelLowestPriceEntity.setDeleted(0);
        agentHotelLowestPriceEntity.setLowestPrice(lowestPrice);
        agentHotelLowestPriceEntity.setSaleCurrency(saleCurrency);
        agentHotelLowestPriceEntity.setPayAtHotelFee(payAtHotelPrice);
        agentHotelLowestPriceEntity.setPayAtHotelCurrency(payAtHotelCurrency);
        return agentHotelLowestPriceEntity;
    }

    /**
     * 针对房型的下面的所有产品进行优选
     */
    private List<ProductDetail> getPreferredProductsByRoom(List<ProductDetail> productDetails) {
        //用来保存有任意标签的产品(不参与优选)
        List<PreferredProductDTO> productDetailListTmp = new ArrayList<>();
        Map<String, List<PreferredProductDTO>> resultMap = new HashMap<>();
        for (ProductDetail productDetail : productDetails) {
            Map<Integer, Set<String>> tipsDetailGroup = new HashMap<>();

            //如果存在产品标签，则临时保存不做任何处理，等最后再和优选之后的产品进行合并输出。
            if ((Objects.nonNull(productDetail.getProductLabel()) && !productDetail.getProductLabel().equals(0))
                    || (Objects.nonNull(productDetail.getSupplierLabel()) && !productDetail.getSupplierLabel().equals(0))) {
                productDetailListTmp.add(new PreferredProductDTO(productDetail, tipsDetailGroup));
                continue;
            }

            //不存在产品标签，则按照产品类型->早餐数量->取消条款->提示信息依次进行拼接
            StringBuilder key = new StringBuilder();

            //产品类型
            key.append(productDetail.getPayAtHotelFlag()).append("_");

            //取房间列表的第一个对象的对应的售卖列表的第一个对象作为代表(正常情况下是第一间的第一天)
            if (CollectionUtil.isNotEmpty(productDetail.getRoomItemDetails())
                    && CollectionUtil.isNotEmpty(productDetail.getRoomItemDetails().get(0).getPriceItems())) {

                List<PriceItem> priceItems = productDetail.getRoomItemDetails().get(0).getPriceItems();
                PriceItem priceItem = priceItems.get(0);

                //早餐数量
                key.append(priceItem.getBreakfastNum()).append("_");

                //取消条款
                if (CollectionUtil.isNotEmpty(priceItem.getCancelRestrictions())) {
                    key.append(priceItem.getCancelRestrictions().get(0).getCancelRestrictionType()).append("_");
                } else {
                    //占位
                    key.append("null_");
                }
            } else {
                //占位
                key.append("null_null_");
            }

            //提示类型
            if (CollectionUtil.isNotEmpty(productDetail.getTips())) {
                //按照对应的数量先进行排列,以免相同的数据因排列的顺序不同而分成了不同组
                productDetail.getTips().sort(Comparator.comparingInt((TipInfosDTO t) -> Objects.nonNull(t.getTipsType()) ? t.getTipsType() : 0)
                        .thenComparingInt(t -> CollectionUtil.isNotEmpty(t.getTipsDetails()) ? t.getTipsDetails().size() : 0));
                for (TipInfosDTO tipInfo : productDetail.getTips()) {
                    //提示类型以及对应的提示列表数量
                    key.append(tipInfo.getTipsType()).append("_")
                            .append(CollectionUtil.isNotEmpty(tipInfo.getTipsDetails()) ? tipInfo.getTipsDetails().size() : 0).append("_");

                    //根据提示类型进行分组,同一组的则把提示信息进行拼接,用于后面的比较
                    Set<String> tipsDetailSet = tipsDetailGroup.get(tipInfo.getTipsType());
                    if (CollectionUtil.isEmpty(tipsDetailSet)) {
                        tipsDetailSet = new HashSet<>();
                    }
                    for (TipsDetail tipsDetail : tipInfo.getTipsDetails()) {
                        tipsDetailSet.add(tipsDetail.getTitle() + "_" + tipsDetail.getDetails());
                    }
                    tipsDetailGroup.put(tipInfo.getTipsType(), tipsDetailSet);
                }
            } else {
                //占位
                key.append("null_null_");
            }

            //查看当前Map里面是否存在,不存在则表示是新的对象,则存入其中,存在则进一步进行比较
            List<PreferredProductDTO> productDetailsCurrent = resultMap.get(key.toString());
            if (CollectionUtil.isEmpty(productDetailsCurrent)) {
                productDetailsCurrent = new ArrayList<>();
                productDetailsCurrent.add(new PreferredProductDTO(productDetail, tipsDetailGroup));
                resultMap.put(key.toString(), productDetailsCurrent);
            } else if (CollectionUtil.isEmpty(tipsDetailGroup)) {
                //存在则依次进行对比,当内容存在不一样的,则直接保存对象,当全部内容都是一样,则对比较totalSalePrice,如果当前对象的更小则替换列表里面的对象
                //没有卖点信息
                boolean isBreak = Boolean.FALSE;
                for (int i = 0; i < productDetailsCurrent.size(); i++) {
                    PreferredProductDTO preferredProductDTO = productDetailsCurrent.get(i);
                    //当前列表存在同样都是无卖点信息的,进行价格对之后,决定是否要过滤掉
                    if (CollectionUtil.isEmpty(preferredProductDTO.getTipsDetailGroup())) {
                        //替换进去
                        if (productDetail.getTotalSalePrice().compareTo(Objects.isNull(preferredProductDTO.getProductDetail().getTotalSalePrice()) ? BigDecimal.ZERO : preferredProductDTO.getProductDetail().getTotalSalePrice()) < 0) {
                            productDetailsCurrent.set(i, new PreferredProductDTO(productDetail, tipsDetailGroup));
                        }
                        //此产品不符合条件,被过滤掉
                        isBreak = Boolean.TRUE;
                        break;
                    }
                }
                if (!isBreak) {
                    //当前列表里面都是有卖点信息的
                    productDetailsCurrent.add(new PreferredProductDTO(productDetail, tipsDetailGroup));
                }
            } else {
                //存在则依次进行对比,当内容存在不一样的,则直接保存对象,当全部内容都是一样,则对比较totalSalePrice,如果当前对象的更小则替换列表里面的对象
                //有卖点信息
                Boolean isBreak = Boolean.FALSE;
                for (int i = 0; i < productDetailsCurrent.size(); i++) {
                    PreferredProductDTO preferredProductDTO = productDetailsCurrent.get(i);
                    if (CollectionUtil.isNotEmpty(preferredProductDTO.getTipsDetailGroup())) {
                        Map<Integer, Set<String>> tipsDetailGroupMap = preferredProductDTO.getTipsDetailGroup();

                        /**
                         * 当前对象的卖点信息和列表对象的卖点信息进行比较，
                         * 如果全部内容都是一样，则对比较totalSalePrice，如果当前对象的更小则替换列表里面的对象，
                         * 如果内容不一样，则跳过这个列表对象，与下一个列表对象进行比较，如果最终内容都是不一样的，则保存当前对象到列表里面
                         */
                        boolean isSame = Boolean.TRUE;
                        for (Map.Entry<Integer, Set<String>> tipsDetailGroupEntry : tipsDetailGroup.entrySet()) {
                            Set<String> tipsType = tipsDetailGroupMap.get(tipsDetailGroupEntry.getKey());
                            if (CollectionUtil.isEmpty(tipsType) || !tipsType.containsAll(tipsDetailGroupEntry.getValue())) {
                                //存在不一样的卖点信息
                                isSame = Boolean.FALSE;
                                break;
                            }
                        }
                        if (isSame) {
                            //全部内容都是一样，则对比较totalSalePrice，如果当前对象的更小则替换列表里面的对象，否则则过滤掉当前对象
                            if (productDetail.getTotalSalePrice().compareTo(Objects.isNull(preferredProductDTO.getProductDetail().getTotalSalePrice()) ? BigDecimal.ZERO : preferredProductDTO.getProductDetail().getTotalSalePrice()) < 0) {
                                productDetailsCurrent.set(i, new PreferredProductDTO(productDetail, tipsDetailGroup));
                            }
                            //此产品不符合条件,被过滤掉
                            isBreak = Boolean.TRUE;
                            break;
                        }

                    }
                }
                if (!isBreak) {
                    //当前列表里面只有一个无卖点信息的，或者列表里面的对象的卖点信息都和当前的不一样，则直接保存对象
                    productDetailsCurrent.add(new PreferredProductDTO(productDetail, tipsDetailGroup));
                }
            }
        }

        //合并对象进行输出
        return Stream.concat(
                        productDetailListTmp.stream(),
                        resultMap.values().stream().flatMap(List::stream)
                ).map(PreferredProductDTO::getProductDetail)
                .collect(Collectors.toList());
    }
}
