package com.tiangong.product.server;

import com.alibaba.nacos.shaded.com.google.common.util.concurrent.RateLimiter;
import com.github.pagehelper.PageInfo;
import com.tiangong.cloud.common.anno.AnonymousAccess;
import com.tiangong.cloud.commonbean.common.BaseController;
import com.tiangong.cloud.commonbean.config.security.TokenManager;
import com.tiangong.common.Response;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.hotel.remote.HotelHeatRemote;
import com.tiangong.hotel.req.HotelHeatReq;
import com.tiangong.keys.RedisKey;
import com.tiangong.product.dto.*;
import com.tiangong.product.service.ProductSaleService;
import com.tiangong.product.task.InitCalculateHotelAvgPriceHotelIdToRedisTask;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.util.CollUtilX;
import com.tiangong.util.DateUtilX;
import com.tiangong.util.StrUtilX;
import com.xxl.job.core.context.XxlJobHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

/**
 * @Auther: Owen
 * @Date: 2019/4/24 11:32
 * @Description: 产品售卖server
 */
@RestController
@Slf4j
@RequestMapping("/product/sale")
public class ProductSaleServer extends BaseController {

    @Autowired
    private ProductSaleService productSaleService;

    @Autowired
    private HotelHeatRemote hotelHeatRemote;

    @Autowired
    HttpServletRequest request;

    /**
     * 查询酒店列表
     */
    @PostMapping("/queryHotelList")
    @PreAuthorize("@syyo.check('product')")
    public Response<PaginationSupportDTO<HotelProductsDTO>> queryHotelList(@RequestBody QueryProductRequestDTO queryProductRequestDTO) {
        queryProductRequestDTO.setCompanyCode(getCompanyCode());
        queryProductRequestDTO.setLanguage(getLanguage());
        return productSaleService.queryHotelList(queryProductRequestDTO);
    }

    /**
     * 查询产品售价列表
     */
    @PostMapping("/querySalePriceList")
    @AnonymousAccess
    public Response<ProductSalePriceDetailDTO> querySalePriceList(@RequestBody QueryProductRequestDTO queryProductRequestDTO) {
        String token = TokenManager.getToken(request);
        if (StrUtilX.isNotEmpty(token)) {
            queryProductRequestDTO.setCompanyCode(getCompanyCode());
            queryProductRequestDTO.setLanguage(getLanguage());
        }
        return productSaleService.querySalePriceList(queryProductRequestDTO);
    }


    /**
     * 单产品上下架
     */
    @PostMapping("/singleProductModifySaleStatus")
    @PreAuthorize("@syyo.check('product')")
    public Response<Object> singleProductModifySaleStatus(@RequestBody Map<String, String> requestMap) {
        requestMap.put("companyCode", getCompanyCode());
        requestMap.put("updatedBy", getUserName());
        requestMap.put("updatedDt", DateUtilX.getCurrentDateStr(1));
        productSaleService.singleProductModifySaleStatus(requestMap);
        return Response.success();
    }

    /**
     * 单天调整售价
     */
    @PostMapping("/dailyModifySalePrice")
    @PreAuthorize("@syyo.check('product')")
    public Response<Object> dailyModifySalePrice(@RequestBody Map<String, String> requestMap) {
        requestMap.put("companyCode", getCompanyCode());
        requestMap.put("updatedBy", getUserName());
        productSaleService.dailyModifySalePrice(requestMap);
        return Response.success();
    }

    /**
     * 批量调整售价
     */
    @PostMapping("/batchModifySalePrice")
    @PreAuthorize("@syyo.check('product')")
    public Response<Object> batchModifySalePrice(@RequestBody BatchSaleDTO batchSaleDTO) {
        batchSaleDTO.setUpdatedBy(getUserName());
        batchSaleDTO.setUpdatedDt(DateUtilX.getCurrentDateStr(1));
        batchSaleDTO.setCompanyCode(getCompanyCode());
        batchSaleDTO.setLanguage(getLanguage());
        productSaleService.batchModifySalePrice(batchSaleDTO);
        return Response.success();
    }

    /**
     * 批量调整售卖状态
     */
    @PostMapping("/batchModifySaleStatus")
    @AnonymousAccess
    public Response<Object> batchModifySaleStatus(@RequestBody BatchSaleDTO batchSaleDTO) {
        batchSaleDTO.setUpdatedBy(getUserName());
        batchSaleDTO.setUpdatedDt(DateUtilX.getCurrentDateStr(1));
        batchSaleDTO.setLanguage(getLanguage());
        batchSaleDTO.setCompanyCode(getCompanyCode());
        productSaleService.batchModifySaleStatus(batchSaleDTO);
        return Response.success();
    }

    /**
     * 查询产品详情供订单使用
     */
    @PostMapping("/queryOrderProductPrice")
    @PreAuthorize("@syyo.check('product')")
    public Response<List<ProductSalePriceItemDTO>> queryOrderProductPrice(@RequestBody QueryProductRequestDTO queryProductRequestDTO) {
        return productSaleService.queryOrderProductPrice(queryProductRequestDTO);
    }

    /**
     * 查询产品销售日志
     */
    @PostMapping(value = "/queryProductSaleLogList", produces = {"application/json;charset=UTF-8"})
    @PreAuthorize("@syyo.check('product')")
    public Response<PaginationSupportDTO<ProductSaleLogDTO>> queryProductSaleLogList(@RequestBody Map<String, String> requestMap) {
        requestMap.put("companyCode", getCompanyCode());
        PageInfo<ProductSaleLogDTO> item = productSaleService.queryProductSaleLogList(requestMap);
        if (item != null) {
            PaginationSupportDTO<ProductSaleLogDTO> supportDTO = new PaginationSupportDTO<ProductSaleLogDTO>();
            supportDTO.copyProperties(item, ProductSaleLogDTO.class);
            return Response.success(supportDTO);
        }

        return Response.success(null);
    }

    /**
     * 分解每日加幅数据
     */
    @PostMapping(value = "/resolveDayIncrease", produces = {"application/json;charset=UTF-8"})
    @PreAuthorize("@syyo.check('product')")
    public Response<String> resolveDayIncrease(@RequestBody Map<String, String> requestMap) {
        return productSaleService.resolveDayIncrease(requestMap.get("startDate"), requestMap.get("endDate"));
    }

    /**
     * 将售价信息推送到对应的渠道
     */
    @PostMapping("/updateSalePrice")
    @PreAuthorize("@syyo.check('product')")
    public Response<Object> updateSalePrice(@RequestBody UpdateSalePriceDTO updateSalePriceDTO) {
        updateSalePriceDTO.setCreatedBy(getUserName());
        updateSalePriceDTO.setCompanyCode(getCompanyCode());
        productSaleService.updateSalePrice(updateSalePriceDTO);
        return Response.success();
    }

    /**
     * 重置酒店售卖信息
     */
    @PostMapping("/resetHotelList")
    @PreAuthorize("@syyo.check('product')")
    public Response<Object> resetHotelList() {
//        StopWatch sw = new StopWatch();
//        sw.start("1. 删除所有酒店售卖信息");
//        hotelSaleInfoService.deleteAllHotelSaleInfo();
//        sw.stop();
//        sw.start("2. 初始化所有酒店售卖信息");
//        hotelSaleInfoService.addAllHotelSaleInfo();
//        sw.stop();
        return Response.success();
    }

    /**
     * 计算分销商可售酒店列表
     */
    @PostMapping(value = "/calculateAgentAvailableHotel", produces = {"application/json;charset=UTF-8"})
    @PreAuthorize("@syyo.check('product')")
    public Response<Object> calculateAgentAvailableHotel(@RequestBody Map<String, List<String>> request) {
        for (String agentCode : request.get("agentCode")) {
            productSaleService.calculateAgentAvailableHotel(agentCode, request.get("hotelIdList"));
        }
        return Response.success();
    }

    /**
     * 计算酒店热度可订分数任务
     */
    @PostMapping("/calculateHotelHeatBookableScoreTask")
    @AnonymousAccess
    public Response<Object> calculateHotelHeatBookableScoreTask(@RequestBody Map<String, String> paramMap) {
        productSaleService.calculateHotelHeatBookableScoreTask(paramMap.get("hotelIds"));
        return Response.success();
    }

    /**
     * 计算酒店热度酒店平均房价任务
     */
    @PostMapping("/calculateHotelHeatHotelAvgPriceTask")
    @AnonymousAccess
    public Response<Object> calculateHotelHeatHotelAvgPriceTask(@RequestBody Map<String, String> paramMap) {
        productSaleService.calculateHotelHeatHotelAvgPriceTask(paramMap.get("hotelIds"));
        return Response.success();
    }

    /**
     * 初始化需要计算酒店热度可订分数的酒店id到缓存任务
     */
    @PostMapping("/initCalculateBookableScoreHotelIdToRedisTask")
    @AnonymousAccess
    public Response<Object> initCalculateBookableScoreHotelIdToRedisTask() {
        ExecutorService executor = null;
        try {
            XxlJobHelper.log("执行初始化需要计算酒店热度可订分数的酒店id到缓存任务开始");

            // 配置参数
            final int pageSize = 2000;
            final int maxConcurrency = 8;
            final AtomicLong lastId = new AtomicLong(0);
            final RateLimiter rateLimiter = RateLimiter.create(5);
            executor = Executors.newFixedThreadPool(maxConcurrency);

            // 分页循环
            while (true) {
                rateLimiter.acquire();

                // 分页查询
                HotelHeatReq req = new HotelHeatReq();
                req.setLastId(lastId.get());
                req.setBatchSize(pageSize);

                Response<List<String>> response = hotelHeatRemote.queryAllHotelHeatHotelIdPage(req);

                // 错误处理
                if (response.isError()) {
                    log.error("分页查询失败 lastId={}, error={}", lastId, response.getFailReason());
                    XxlJobHelper.log("分页查询失败: " + response.getFailReason());
                    break;
                }

                List<String> hotelIds = response.getModel();
                if (CollUtilX.isEmpty(hotelIds)) break;

                // 提交处理
                executor.submit(() -> processWithRetry(hotelIds));

                // 更新游标
                updateLastIdAtomically(hotelIds, lastId);
            }

            // 等待完成
            executor.shutdown();
            while (!executor.awaitTermination(1, TimeUnit.MINUTES)) {
                XxlJobHelper.log("等待线程池关闭...");
            }
            XxlJobHelper.log("执行初始化需要计算酒店热度可订分数的酒店id到缓存任务结束，最终lastId=" + lastId.get());
        } catch (Exception e) {
            log.error("执行初始化需要计算酒店热度可订分数的酒店id到缓存任务异常", e);
            XxlJobHelper.log("执行初始化需要计算酒店热度可订分数的酒店id到缓存任务异常", e);
        } finally {
            if (executor != null) executor.shutdownNow();
        }
        return Response.success();
    }

    /**
     * 更新lastId
     */
    private void updateLastIdAtomically(List<String> hotelIds, AtomicLong lastId) {
        hotelIds.stream()
                .mapToLong(Long::parseLong)
                .max()
                .ifPresent(max -> {
                    long current;
                    do {
                        current = lastId.get();
                    } while (max > current && !lastId.compareAndSet(current, max));
                });
    }

    /**
     * 处理数据
     */
    private void processWithRetry(List<String> hotelIds) {
        try (RedisConnection conn = RedisTemplateX.getConnectionFactory()) {
            conn.openPipeline();
            byte[] key = RedisKey.CALCULATE_BOOKABLE_SCORE_HOTEL_ID_KEY.getBytes();

            hotelIds.stream()
                    .map(String::getBytes)
                    .forEach(bytes -> conn.sAdd(key, bytes));

            conn.closePipeline();
        } catch (Exception e) {
            log.error("Redis批量写入失败", e);
        }
    }

    @Autowired
    private InitCalculateHotelAvgPriceHotelIdToRedisTask initCalculateHotelAvgPriceHotelIdToRedisTask;

    /**
     * 初始化需要计算酒店热度酒店平均房价的酒店id到缓存任务
     */
    @PostMapping("/initCalculateHotelAvgPriceHotelIdToRedisTask")
    @AnonymousAccess
    public Response<Object> initCalculateHotelAvgPriceHotelIdToRedisTask() {
        initCalculateHotelAvgPriceHotelIdToRedisTask.initCalculateHotelAvgPriceHotelIdToRedisTask();
        return Response.success();
    }

    /**
     * 初始化计算自签酒店起价任务
     */
    @PostMapping("/initCalculateSelfHotelLowestPriceTask")
    @AnonymousAccess
    public Response<Object> initCalculateSelfHotelLowestPriceTask() {
        productSaleService.initCalculateSelfHotelLowestPriceTask(null);
        return Response.success();
    }

    /**
     * 处理需要计算自签酒店起价
     */
    @PostMapping("/calculateNeedSelfHotelLowestPrice")
    @AnonymousAccess
    public Response<Object> calculateNeedSelfHotelLowestPrice(@RequestBody CalculateSelfHotelLowestDTO dto) {
        productSaleService.calculateNeedSelfHotelLowestPrice(dto);
        return Response.success();
    }
}
