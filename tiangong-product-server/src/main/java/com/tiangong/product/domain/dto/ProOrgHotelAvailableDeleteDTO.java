package com.tiangong.product.domain.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 供应商酒店黑白名单删除DTO
 * 服务层传输对象
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
public class ProOrgHotelAvailableDeleteDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 修改人
     */
    private String updatedBy;

    /**
     * 运营商编码
     */
    private String companyCode;
}
