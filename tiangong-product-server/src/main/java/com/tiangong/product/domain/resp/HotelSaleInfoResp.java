package com.tiangong.product.domain.resp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class HotelSaleInfoResp {
    /**
     * =唯一标识
     */
    private String logId;

    /**
     * 需要计算酒店起价数据
     */
    private List<HotelSaleInfoItemResp> respList;
}