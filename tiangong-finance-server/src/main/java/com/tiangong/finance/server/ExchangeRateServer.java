package com.tiangong.finance.server;


import cn.hutool.core.collection.CollUtil;
import com.tiangong.cloud.common.anno.AnonymousAccess;
import com.tiangong.cloud.commonbean.anno.OperateLog;
import com.tiangong.cloud.commonbean.common.BaseController;
import com.tiangong.common.Response;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.dto.exchange.ExchangeRateDTO;
import com.tiangong.dto.exchange.QueryExchangeReq;
import com.tiangong.enums.SettlementCurrencyEnum;
import com.tiangong.finance.OrgDTO;
import com.tiangong.finance.domain.LastRateReq;
import com.tiangong.finance.dto.ExchangeRateLogDTO;
import com.tiangong.finance.remote.request.QueryExchangeRateLogDTO;
import com.tiangong.finance.service.ExchangeRateService;
import com.tiangong.initializer.CommonInitializer;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;

@RestController
@Slf4j
public class ExchangeRateServer extends BaseController {

    @Autowired
    private ExchangeRateService exchangeRateService;

    /**
     * 查询汇率
     */
    @OperateLog("查询汇率，queryExchangeRate")
    @PostMapping("/finance/queryExchangeRate")
    @AnonymousAccess
    public Response<PaginationSupportDTO<ExchangeRateDTO>> queryExchangeRate(@RequestBody QueryExchangeReq request) {
        if (StrUtilX.isEmpty(request.getCompanyCode())) {
            request.setCompanyCode(getCompanyCode());
        }
        PaginationSupportDTO<ExchangeRateDTO> data = exchangeRateService.queryExchangeRate(request);
        if (CollUtil.isNotEmpty(data.getItemList())) {
            // 返回数据处理
            OrgDTO orgDTO = CommonInitializer.getOrgInfo();
            String orgCurrency = SettlementCurrencyEnum.getCodeByKey(String.valueOf(orgDTO.getOrgCurrency()));
            for (ExchangeRateDTO exchangeRateDTO : data.getItemList()) {
                int changeType = orgCurrency.equals(exchangeRateDTO.getOriginalCurrency()) ? 1 : 2;
                exchangeRateService.setRateStrAndExchangeRateStr(exchangeRateDTO, changeType);
            }
        }
        return Response.success(data);
    }


    /**
     * 新增汇率
     */
    @PostMapping("/finance/addExchangeRate")
    @PreAuthorize("@syyo.check('finance')")
    public Response<Object> addExchangeRate(@Validated @RequestBody ExchangeRateDTO request) {
        request.setCreatedBy(getUserName());
        request.setCompanyCode(getCompanyCode());
        exchangeRateService.addExchangeRate(request);
        return Response.success();
    }

    /**
     * 编辑汇率
     */
    @PostMapping("/finance/modifyExchangeRate")
    @PreAuthorize("@syyo.check('finance')")
    public Response<Object> updateExchangeRate(@RequestBody ExchangeRateDTO request) {
        request.setUpdatedBy(getUserName());
        request.setCompanyCode(getCompanyCode());
        exchangeRateService.updateExchangeRate(request);
        return Response.success();
    }

    /**
     * 查询操作日志
     */
    @PostMapping("/finance/queryExchangeRateLog")
    @PreAuthorize("@syyo.check('finance')")
    public Response<PaginationSupportDTO<ExchangeRateLogDTO>> updateExchangeRate(@RequestBody QueryExchangeRateLogDTO request) {
        return Response.success(exchangeRateService.queryExchangeRateLog(request));
    }

    /**
     * 获取币种兑换商家币种汇率
     */
    @AnonymousAccess
    @GetMapping("/finance/getRateToOrgCurrency")
    public BigDecimal getRateToOrgCurrency(@RequestParam("originalCurrency") Integer originalCurrency, @RequestParam("companyCode") String companyCode) {
        return exchangeRateService.getRateToOrgCurrency(originalCurrency, companyCode);
    }

    /**
     * 获取币种兑换指定币种的汇率
     */
    @AnonymousAccess
    @GetMapping("/finance/getRateToTargetCurrency")
    public BigDecimal getRateToTargetCurrency(@RequestParam("originalCurrency") Integer originalCurrency,
                                              @RequestParam("companyCode") String companyCode,
                                              @RequestParam("targetCurrency") Integer targetCurrency) {
        return exchangeRateService.getRateToTargetCurrency(originalCurrency, companyCode, targetCurrency);
    }

    /**
     * 获取最终汇率
     */
    @PostMapping("/finance/getLastRate")
    @AnonymousAccess
    public Response<String> getLastRate(@RequestBody LastRateReq req) {
        return Response.success(exchangeRateService.getLastRate(req).toPlainString());
    }
}
