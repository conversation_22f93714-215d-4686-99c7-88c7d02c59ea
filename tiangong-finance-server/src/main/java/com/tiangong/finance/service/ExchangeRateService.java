package com.tiangong.finance.service;

import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.dto.exchange.ExchangeRateDTO;
import com.tiangong.dto.exchange.QueryExchangeReq;
import com.tiangong.finance.domain.LastRateReq;
import com.tiangong.finance.dto.ExchangeRateLogDTO;
import com.tiangong.finance.remote.request.QueryExchangeRateLogDTO;

import java.math.BigDecimal;

public interface ExchangeRateService {

    /**
     * 查询汇率
     */
    PaginationSupportDTO<ExchangeRateDTO> queryExchangeRate(QueryExchangeReq req);

    /**
     * 新增汇率
     */
    void addExchangeRate(ExchangeRateDTO exchangeRateDTO);

    /**
     * 修改汇率
     */
    void updateExchangeRate(ExchangeRateDTO exchangeRateDTO);

    /**
     * 查询操作日志
     */
    PaginationSupportDTO<ExchangeRateLogDTO> queryExchangeRateLog(QueryExchangeRateLogDTO request);

    /**
     * 获取兑换商家币种的汇率
     */
    BigDecimal getRateToOrgCurrency(Integer originalCurrency, String companyCode);

    /**
     * 获取兑换指定币种的汇率
     */
    BigDecimal getRateToTargetCurrency(Integer originalCurrency, String companyCode, Integer targetCurrency);

    /**
     * 获取最终汇率
     */
    BigDecimal getLastRate(LastRateReq req);

    /**
     * 获取外部系统同步汇率保留
     *
     * @param origin
     * @return
     */
    BigDecimal getScaleOuterSystemRate(BigDecimal origin);

    /**
     * 根据类型获取同步汇率保留
     *
     * @param origin     汇率
     * @param changeType 1外部系统同步汇率 2系统转化汇率
     * @return
     */
    BigDecimal getScaleChangeTypeChangeType(BigDecimal origin, Integer changeType);

    /**
     * 获取最终汇率
     *
     * @param adjustmentType 调整策略
     * @param modifiedAmt    修改值
     * @param changeRate     汇率
     * @param changeType     1外部系统同步汇率 2系统转化汇率
     * @return
     */
    BigDecimal getRate(Integer adjustmentType, BigDecimal modifiedAmt, BigDecimal changeRate, Integer changeType);

    /**
     * 设置汇率和最终汇率展示字段
     *
     * @param exchangeRateDTO
     * @param changeType
     */
    void setRateStrAndExchangeRateStr(ExchangeRateDTO exchangeRateDTO, Integer changeType);
}
