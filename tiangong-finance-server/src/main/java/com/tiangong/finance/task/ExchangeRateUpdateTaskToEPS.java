package com.tiangong.finance.task;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.tiangong.cloud.common.anno.AnonymousAccess;
import com.tiangong.common.Constant;
import com.tiangong.config.SettingsConstant;
import com.tiangong.dis.remote.InitRemote;
import com.tiangong.dto.common.CompanyDTO;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.dto.exchange.ExchangeRateDTO;
import com.tiangong.dto.exchange.QueryExchangeReq;
import com.tiangong.enums.SettlementCurrencyEnum;
import com.tiangong.finance.OrgDTO;
import com.tiangong.finance.convert.DTOConvert;
import com.tiangong.finance.domain.ExchangeRateLogPO;
import com.tiangong.finance.domain.ExchangeRatePO;
import com.tiangong.finance.mapper.ExchangeRateLogMapper;
import com.tiangong.finance.mapper.ExchangeRateMapper;
import com.tiangong.finance.service.ExchangeRateService;
import com.tiangong.initializer.CommonInitializer;
import com.tiangong.util.DateUtilX;
import com.tiangong.util.StrUtilX;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

@Slf4j
@Component
@RequiredArgsConstructor
public class ExchangeRateUpdateTaskToEPS extends IJobHandler {

    @Autowired
    private ExchangeRateService exchangeRateService;

    @Resource
    private ExchangeRateMapper exchangeRateMapper;

    @Autowired
    private InitRemote initRemote;

    @Autowired
    private ExchangeRateLogMapper exchangeRateLogMapper;

    @Autowired
    private SettingsConstant settingsConstant;

    @XxlJob("exchangeRateUpdateTaskToEPS")
    @Override
    @AnonymousAccess
    public void execute() throws Exception {
        OrgDTO orgDTO = CommonInitializer.getOrgInfo();
        String currency = SettlementCurrencyEnum.getCodeByKey(String.valueOf(orgDTO.getOrgCurrency()));
        if (StrUtilX.isEmpty(currency)) {
            log.info("商户币种不存在");
            return;
        }

        //获取汇率
        Map<String, BigDecimal> rateMap = getRateMap();


        //查询所有汇率表
        QueryExchangeReq req = new QueryExchangeReq();
        req.setCurrentPage(1);
        req.setPageSize(500);
        req.setCompanyCode(CompanyDTO.COMPANY_CODE);
        PaginationSupportDTO<ExchangeRateDTO> exchangeRateDTOPaginationSupportDTO = exchangeRateService.queryExchangeRate(req);
        List<ExchangeRateDTO> itemList = new ArrayList<>();
        if (exchangeRateDTOPaginationSupportDTO != null && exchangeRateDTOPaginationSupportDTO.getItemList() != null && exchangeRateDTOPaginationSupportDTO.getItemList().size() > 0) {
            itemList = exchangeRateDTOPaginationSupportDTO.getItemList();
        } else {
            return;
        }

        String currentDate = DateUtilX.dateToString(DateUtilX.getCurrentDate(), DateUtilX.hour_format);
        for (ExchangeRateDTO dto : itemList) {
            if (dto.getAutoSync() == 0) {
                continue;
            }
            if (!currency.equals(dto.getOriginalCurrency()) && !currency.equals(dto.getTargetCurrency())) {
                continue;
            }
            //取兑换的汇率
            BigDecimal bigDecimal = rateMap.get(dto.getOriginalCurrency() + "-" + dto.getTargetCurrency());
            if (Objects.isNull(bigDecimal)) continue;

            //先记录之前的汇率
            BigDecimal oldRate = dto.getExchangeRate();

            dto.setExchangeRate(bigDecimal);
            dto.setRate(getRate(dto.getAdjustmentType(), dto.getModifiedAmt(), dto.getExchangeRate()));
//            ExchangeRatePO po = new ExchangeRatePO();
//            BeanUtils.copyProperties(dto, po);
            ExchangeRatePO po = DTOConvert.INSTANCE.exchangeRatePOConvert(dto);
            po.setActive(1);
            po.setCompanyCode(CompanyDTO.COMPANY_CODE);
            po.setRate(po.getRate().stripTrailingZeros());
            po.setExchangeRate(po.getExchangeRate().stripTrailingZeros());
            po.setUpdatedDt(currentDate);
            po.setUpdatedBy(Constant.SYSTEM);
            exchangeRateMapper.updateByPrimaryKey(po);
            initRemote.initExchangeRate(DTOConvert.INSTANCE.currencyExchangeRateDTOConvert(po));

            ExchangeRateLogPO exchangeRateLogPO = new ExchangeRateLogPO();
            exchangeRateLogPO.setContent("修改了汇率,汇率由 " + oldRate.stripTrailingZeros() + "改成了 " + po.getExchangeRate() +
                    ",最终汇率为\" 1" + po.getOriginalCurrency() + "=" + po.getRate() + " " + po.getTargetCurrency() + "\"");
            exchangeRateLogPO.setCreatedBy(Constant.SYSTEM);
            exchangeRateLogPO.setCreatedDt(currentDate);
            exchangeRateLogPO.setCurrencyId(po.getExchangeRateId());
            exchangeRateLogPO.setActive(1);
            exchangeRateLogMapper.insert(exchangeRateLogPO);
        }
    }

    public BigDecimal getRate(Integer adjustmentType, BigDecimal modifiedAmt, BigDecimal changeRate) {
        BigDecimal rate = BigDecimal.ZERO;
        if (adjustmentType == 0) {
            rate = changeRate.add(modifiedAmt);
        } else if (adjustmentType == 1) {
            rate = changeRate.subtract(modifiedAmt);
        } else if (adjustmentType == 2) {
            rate = (BigDecimal.ONE.add(modifiedAmt.divide(new BigDecimal("100")))).multiply(changeRate);
        } else if (adjustmentType == 3) {
            rate = (BigDecimal.ONE.subtract(modifiedAmt.divide(new BigDecimal("100")))).multiply(changeRate);
        }
        return rate;
    }

    public Map<String, BigDecimal> getRateMap() {
        Map<String, BigDecimal> map = new HashMap<>();
        String result = HttpUtil.get(settingsConstant.getRateUrl2());
        if (!StringUtils.isEmpty(result)) {
            JSONArray array = JSONArray.parseArray(result);
            for (int i = 0; i < array.size(); i++) {
                JSONObject jsonObject = array.getJSONObject(i);
                if (Objects.isNull(jsonObject)) continue;
                String currencyValue = jsonObject.getString("currency_value");
                currencyValue = currencyValue.replace(",", "");
                BigDecimal value = new BigDecimal(currencyValue);
                map.put(jsonObject.getString("currency_from") + "-" + jsonObject.getString("currency_to"), value);
                map.put(jsonObject.getString("currency_to") + "-" + jsonObject.getString("currency_from"), BigDecimal.ONE.divide(value, 8, BigDecimal.ROUND_DOWN));
            }
        }

        return map;
    }
}
