package com.tiangong.finance.mapper;

import com.tiangong.dto.common.MyMapper;
import com.tiangong.finance.domain.ExchangeRateLogPO;
import com.tiangong.finance.dto.ExchangeRateLogDTO;
import com.tiangong.finance.remote.request.QueryExchangeRateLogDTO;

import java.util.List;

public interface ExchangeRateLogMapper extends MyMapper<ExchangeRateLogPO> {

    List<ExchangeRateLogDTO> queryExchangeRateLog(QueryExchangeRateLogDTO exchangeRateLogDTO);
}
