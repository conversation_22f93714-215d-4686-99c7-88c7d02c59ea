package com.tiangong.finance.domain;


import com.tiangong.dto.common.BasePO;
import lombok.Data;

import javax.persistence.*;
import java.math.BigDecimal;


@Data
@Table(name = "t_exchangerate")
public class ExchangeRatePO extends BasePO {

    @Id
    @Column(name = "exchangerate_Id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer exchangeRateId;

    /**
     * 原币种
     */
    @Column(name = "original_currency")
    private String originalCurrency;

    /**
     * 目标币种
     */
    @Column(name = "target_currency")
    private String targetCurrency;

    /**
     * 原币种名称
     */
    @Column(name = "original_currency_name")
    private String originalCurrencyName;

    /**
     * 目标币种名称
     */
    @Column(name = "target_currency_name")
    private String targetCurrencyName;

    /**
     * 最终汇率
     */
    @Column(name = "rate")
    private BigDecimal rate;

    /**
     *
     */
    @Column(name = "active")
    private Integer active;

    /**
     * 商家编码
     */
    @Column(name = "company_code")
    private String companyCode;

    /**
     * 增量类型0加数值 1减数值 2加百分比 3减百分比 4等于
     */
    @Column(name = "adjustment_type")
    private Integer adjustmentType;

    /**
     * 调整金额
     */
    @Column(name = "modified_amt")
    private BigDecimal modifiedAmt;

    /**
     * 汇率
     */
    @Column(name = "exchange_rate")
    private BigDecimal exchangeRate;

    /**
     * 是否自动同步汇率：0否 1是
     */
    @Column(name = "auto_sync")
    private Integer autoSync;


}
