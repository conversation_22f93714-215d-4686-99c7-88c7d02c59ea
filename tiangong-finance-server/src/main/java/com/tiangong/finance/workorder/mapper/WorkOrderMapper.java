package com.tiangong.finance.workorder.mapper;

import com.tiangong.dto.common.MyMapper;
import com.tiangong.finance.remote.workorder.request.QueryWorkExportRequestDTO;
import com.tiangong.finance.remote.workorder.response.NotificationLogDTO;
import com.tiangong.finance.remote.workorder.request.BusinessCodeDTO;
import com.tiangong.finance.remote.workorder.request.QueryWorkOrderListDTO;
import com.tiangong.finance.remote.workorder.response.WorkOrderExportResponseDTO;
import com.tiangong.finance.remote.workorder.response.WorkOrderListResponseDTO;
import com.tiangong.finance.workorder.domain.WorkOrderPO;

import java.util.List;

public interface WorkOrderMapper extends MyMapper<WorkOrderPO> {

    /**
     * 财务工单查询
     */
    List<WorkOrderListResponseDTO> queryWorkOrderList(QueryWorkOrderListDTO request);

    /**
     * 导出财务工单
     */
    List<WorkOrderExportResponseDTO> exportWorkOrderList(QueryWorkExportRequestDTO request);

    /**
     * 通知记录查询
     */
    List<NotificationLogDTO> financeNotificationLogList(BusinessCodeDTO request);
}