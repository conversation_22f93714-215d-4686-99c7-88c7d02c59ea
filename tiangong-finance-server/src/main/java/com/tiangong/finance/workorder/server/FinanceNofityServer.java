package com.tiangong.finance.workorder.server;

import com.tiangong.cloud.common.anno.AnonymousAccess;
import com.tiangong.common.Response;
import com.tiangong.finance.remote.workorder.request.BusinessCodeDTO;
import com.tiangong.finance.remote.workorder.request.NotifyCollectionDTO;
import com.tiangong.finance.remote.workorder.request.NotifyPaymentDTO;
import com.tiangong.finance.remote.workorder.response.NotificationLogDTO;
import com.tiangong.finance.workorder.service.FinanceNofityService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@Slf4j
public class FinanceNofityServer {

    @Autowired
    private FinanceNofityService financeNofityService;

    /**
     * 通知收款
     */
    @PostMapping("/finance/notify/notifyCollection")
    @AnonymousAccess
    public Response<Object> notifyCollection(@RequestBody NotifyCollectionDTO request) {
        financeNofityService.notifyCollection(request);
        return Response.success();
    }

    /**
     * 通知付款
     */
    @PostMapping("/finance/notify/notifyPayment")
    @AnonymousAccess
    public Response<Integer> notifyPayment(@RequestBody NotifyPaymentDTO request) {
        return Response.success(financeNofityService.notifyPayment(request));
    }

    /**
     * 通知记录查询
     */
    @PostMapping("/finance/notify/financeNotificationLogList")
    @PreAuthorize("@syyo.check('finance')")
    public Response<List<NotificationLogDTO>> financeNotificationLogList(@RequestBody BusinessCodeDTO request) {
        List<NotificationLogDTO> notificationLogDTOList = financeNofityService.financeNotificationLogList(request);
        return Response.success(notificationLogDTOList);
    }
}
