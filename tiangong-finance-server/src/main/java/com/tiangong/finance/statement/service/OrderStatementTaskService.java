package com.tiangong.finance.statement.service;

import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.finance.remote.statement.request.*;
import com.tiangong.finance.remote.statement.response.OrderStatementTaskResponseDTO;
import com.tiangong.finance.statement.domain.OrderStatementTaskPO;
import com.tiangong.finance.statement.dto.OrderStatementTaskDTO;

public interface OrderStatementTaskService {

    /**
     * 新增自助结算订单记账
     */
    void insertOrderStatementTask(OrderStatementTaskDTO orderStatementTaskDTO);

    /**
     * 查询订单记账处理列表
     */
    PaginationSupportDTO<OrderStatementTaskResponseDTO> queryOrderStatementTaskPage(QueryOrderStatementTaskPageDTO request);

    /**
     * 订单记账任务重试
     */
    void retryOrderStatementTask(RetryOrderStatementTaskDTO request);

    /**
     * 查询订单记账任务根据任务编码
     */
    OrderStatementTaskPO queryOrderStatementTask(String taskCode);

    /**
     * 更新订单记账任务
     */
    void updateOrderStatementTask(OrderStatementTaskDTO orderStatementTaskDTO);

    /**
     * 记账任务工单处理
     */
    void settleProcessWorkOrder(String taskCode);

    /**
     * 根据账单编码查询是否自助结算
     * @param statementCode
     * @return
     */
    String selectOneByStatementCode(String statementCode);
}
