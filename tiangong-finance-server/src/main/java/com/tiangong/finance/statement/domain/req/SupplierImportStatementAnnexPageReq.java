package com.tiangong.finance.statement.domain.req;

import com.tiangong.cloud.common.domain.BasePage;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2023/5/31 15:00
 * @Description:
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class SupplierImportStatementAnnexPageReq extends BasePage implements Serializable {

    /**
     * 导入的供应商账单id
     */
    private Integer supplierImportStatementId;

}
