package com.tiangong.finance.statement.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tiangong.cloud.common.constant.DynamicTableNameConstant;
import com.tiangong.cloud.common.domain.LoginUser;
import com.tiangong.cloud.commonbean.config.page.MybatisPlusConfig;
import com.tiangong.cloud.commonbean.config.security.TokenManager;
import com.tiangong.cloud.commonbean.utils.PageVo;
import com.tiangong.enums.ErrorCodeEnum;
import com.tiangong.enums.SettlementCurrencyEnum;
import com.tiangong.exception.SysException;
import com.tiangong.finance.enums.ReconciliationResultTypeEnum;
import com.tiangong.finance.remote.statement.request.QueryStatementSupplyOrderListPageDTO;
import com.tiangong.finance.remote.statement.response.StatementSupplyOrderDTO;
import com.tiangong.finance.statement.constant.SignsConstant;
import com.tiangong.finance.statement.domain.SupplierStatementOrderPO;
import com.tiangong.finance.statement.domain.SupplierStatementPO;
import com.tiangong.finance.statement.domain.entity.SupplierImportStatementAnnexEntity;
import com.tiangong.finance.statement.domain.entity.SupplierImportStatementEntity;
import com.tiangong.finance.statement.domain.entity.SupplyAutoReconciliationEntity;
import com.tiangong.finance.statement.domain.req.*;
import com.tiangong.finance.statement.domain.resp.QueryStatementStatusResp;
import com.tiangong.finance.statement.domain.resp.ReconciliationResultTypeCountResp;
import com.tiangong.finance.statement.domain.resp.SupplyAutoReconciliationPageResp;
import com.tiangong.finance.statement.dto.ComparisonBillDTO;
import com.tiangong.finance.statement.mapper.*;
import com.tiangong.finance.statement.service.SupplyAutoReconciliationService;
import com.tiangong.keys.RedisKey;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.util.CollUtilX;
import com.tiangong.util.StrUtilX;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.util.IOUtils;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import tk.mybatis.mapper.entity.Example;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 自动对账结果
 *
 * <AUTHOR>
 * @Date 2023/5/19 17:44
 * @Description:
 */
@Slf4j
@Service
public class SupplyAutoReconciliationServiceImpl extends ServiceImpl<SupplyAutoReconciliationMapper, SupplyAutoReconciliationEntity> implements SupplyAutoReconciliationService {

    /**
     * 批量插入的数量
     */
    private final Integer batchInsertCount = 2000;

    private final Integer batchQueryCount = 5000;

    private final Integer breakNum = 100000;

    @Autowired
    private SupplyAutoReconciliationMapper supplyAutoReconciliationMapper;

    @Autowired
    private SupplierImportStatementMapper supplierImportStatementMapper;

    @Autowired
    private SupplierImportStatementAnnexMapper supplierImportStatementAnnexMapper;

    @Autowired
    private SupplierStatementOrderMapper supplierStatementOrderMapper;

    @Autowired
    private SupplierStatementMapper supplierStatementMapper;

    @Autowired
    HttpServletRequest request;


    /**
     * 添加/减少/重新比对账单
     * <p>
     * 0.判断账单状态
     * 1.将所有供应商账单进行聚合 --> map<供货单号, <供应商单号, Obj>>
     * 2.查出系统账单下的所有订单(可考虑流进行优化提速)
     * 3.删除结果
     * 4.
     * *4.1
     * * * *遍历2的每一个订单
     * *4.2
     * * * *去跟1的进行比较
     *
     * @param req
     * @return
     */
    @Override
    @Transactional
    public void comparisonBillAdd(ComparisonBillAddReq req) {
        LoginUser loginUser = TokenManager.getUser(request);
        try {
            //校验传入的账单id和账单编号是否正确
            Example supplierStatementExample = new Example(SupplierStatementPO.class);
            supplierStatementExample.createCriteria().andEqualTo("id", req.getStatementId()).andEqualTo("statementCode", req.getStatementCode());
            SupplierStatementPO supplierStatementPO = supplierStatementMapper.selectOneByExample(supplierStatementExample);
            if (supplierStatementPO == null) {
                throw new SysException(ErrorCodeEnum.FAIL.errorCode, "系统不存在对应的账单");
            }

            List<SupplierImportStatementEntity> supplierImportStatementEntities = supplierImportStatementMapper.selectList(new QueryWrapper<SupplierImportStatementEntity>().lambda()
                    .eq(SupplierImportStatementEntity::getDeleted, 0)
                    .eq(StrUtilX.isNotEmpty(req.getStatementCode()), SupplierImportStatementEntity::getStatementCode, req.getStatementCode())
                    .eq(SupplierImportStatementEntity::getStatementStatus, 1));
            Set<Integer> allSupplierImportStatementIds = supplierImportStatementEntities.stream().map(SupplierImportStatementEntity::getId).collect(Collectors.toSet());

            CompletableFuture<Void> completableFuture = CompletableFuture.runAsync(() -> {
                if (CollUtilX.isNotEmpty(req.getSupplierStatementIdList())) {
                    //更新f_supplier_import_statement
                    supplierImportStatementMapper.update(null, new UpdateWrapper<SupplierImportStatementEntity>().lambda()
                            .in(SupplierImportStatementEntity::getId, req.getSupplierStatementIdList())
                            .set(SupplierImportStatementEntity::getStatementId, req.getStatementId())
                            .set(SupplierImportStatementEntity::getStatementCode, req.getStatementCode())
                            .set(SupplierImportStatementEntity::getStatementStatus, 1)
                            .set(SupplierImportStatementEntity::getUpdatedBy, loginUser.getFullUserName())
                            .set(SupplierImportStatementEntity::getUpdatedDt, new Date()));
                }
            });

            Example supplierStatementOrderExample = new Example(SupplierStatementOrderPO.class);
            supplierStatementOrderExample.createCriteria().andEqualTo("statementId", req.getStatementId());
            int supplierStatementOrderCount = supplierStatementOrderMapper.selectCountByExample(supplierStatementOrderExample);
//            if (supplierStatementOrderCount == 0) {
//                MybatisPlusConfig.dynamicTableNameSuffix.set(String.valueOf(req.getStatementId() % DynamicTableNameConstant.SUPPLY_AUTO_RECONCILIATION_DIVIDE_TABLE_NUMBER));
//                Integer deleteRows = supplyAutoReconciliationMapper.delete(new QueryWrapper<SupplyAutoReconciliationEntity>().lambda()
//                        .eq(SupplyAutoReconciliationEntity::getStatementId, req.getStatementId()));
//
//                supplierImportStatementMapper.update(null, new UpdateWrapper<SupplierImportStatementEntity>().lambda()
//                        .in(SupplierImportStatementEntity::getId, allSupplierImportStatementIds)
//                        .set(SupplierImportStatementEntity::getStatementId, null)
//                        .set(SupplierImportStatementEntity::getStatementCode, null)
//                        .set(SupplierImportStatementEntity::getStatementStatus, 0)
//                        .set(SupplierImportStatementEntity::getUpdatedBy, loginUser.getFullUserName())
//                        .set(SupplierImportStatementEntity::getUpdatedDt, new Date()));
//
//                //删除redis中的账单对比状态
//                stringRedisTemplate.delete(StrUtilX.concat(RedisKey.SUPPLY_AUTO_RECONCILIATION, req.getStatementCode()));
//                return R.error("此账单不存在订单,对账失败!");
//            }

            //0.判断账单状态
            boolean isExist = RedisTemplateX.setIfAbsent(StrUtilX.concat(RedisKey.SUPPLY_AUTO_RECONCILIATION, req.getStatementCode()), "doing", 6 * 60 * 60 * 1000L);
            if (!isExist) {
                throw new SysException(ErrorCodeEnum.FAIL.errorCode, "账单比对中，请稍后!");
            }

            allSupplierImportStatementIds.removeAll(new HashSet<>(req.getSupplierStatementIdList()));

            //没有对比账单
            if (CollUtilX.isEmpty(req.getSupplierStatementIdList()) && CollUtilX.isNotEmpty(supplierImportStatementEntities)) {
                MybatisPlusConfig.dynamicTableNameSuffix.set(String.valueOf(req.getStatementId() % DynamicTableNameConstant.SUPPLY_AUTO_RECONCILIATION_DIVIDE_TABLE_NUMBER));
                Integer deleteRows = supplyAutoReconciliationMapper.delete(new QueryWrapper<SupplyAutoReconciliationEntity>().lambda()
                        .eq(SupplyAutoReconciliationEntity::getStatementId, req.getStatementId()));

                supplierImportStatementMapper.update(null, new UpdateWrapper<SupplierImportStatementEntity>().lambda()
                        .in(SupplierImportStatementEntity::getId, allSupplierImportStatementIds)
                        .set(SupplierImportStatementEntity::getStatementId, null)
                        .set(SupplierImportStatementEntity::getStatementCode, null)
                        .set(SupplierImportStatementEntity::getStatementStatus, 0)
                        .set(SupplierImportStatementEntity::getUpdatedBy, loginUser.getFullUserName())
                        .set(SupplierImportStatementEntity::getUpdatedDt, new Date()));

                // 删除redis中的账单对比状态
                RedisTemplateX.delete(StrUtilX.concat(RedisKey.SUPPLY_AUTO_RECONCILIATION, req.getStatementCode()));
                return;
            } else if (CollUtilX.isEmpty(req.getSupplierStatementIdList()) && CollUtilX.isEmpty(supplierImportStatementEntities)) {//没有对比账单
                //删除redis中的账单对比状态
                RedisTemplateX.delete(StrUtilX.concat(RedisKey.SUPPLY_AUTO_RECONCILIATION, req.getStatementCode()));
                return;
            }

            CompletableFuture<Void> completableFuture2 = CompletableFuture.runAsync(() -> {
                String statementCode = req.getStatementCode();
                Integer statementId = req.getStatementId();
                try {
                    List<SupplyAutoReconciliationEntity> autoReconciliationList = new ArrayList<>();
                    //map<供货单号,Map<金额, ComparisonBillDTO>>
                    Map<String, Map<BigDecimal, ComparisonBillDTO>> allSupplierBillOrderMap = new HashMap<>();
                    List<String> comparedBillsList = new ArrayList<>();

                    Map<Integer, String> supplierStatementIdAndStatementCodeMap = supplierImportStatementMapper.selectBatchIds(req.getSupplierStatementIdList()).stream().collect(Collectors.toMap(SupplierImportStatementEntity::getId, SupplierImportStatementEntity::getSupplierStatementCode));
                    //1.将所有供应商账单进行聚合 --> map<供货单号,Map<金额, ComparisonBillDTO>>
                    List<Integer> supplierStatementIdList = req.getSupplierStatementIdList();
                    if (supplierStatementOrderCount == 0) {//此账单不存在订单

                        //删除原本的结果
                        MybatisPlusConfig.dynamicTableNameSuffix.set(String.valueOf(statementId % DynamicTableNameConstant.SUPPLY_AUTO_RECONCILIATION_DIVIDE_TABLE_NUMBER));
                        Integer deleteRows = supplyAutoReconciliationMapper.delete(new QueryWrapper<SupplyAutoReconciliationEntity>().lambda()
                                .eq(SupplyAutoReconciliationEntity::getStatementId, statementId));

                        for (Integer supplierStatementId : supplierStatementIdList) {
                            //分页读取数据，以免一次性读取的数据太多，导致服务挂了
                            List<SupplierImportStatementAnnexEntity> supplierImportStatementAnnexList = new ArrayList<>();
                            Integer indexNum = 0;
                            while (true) {
                                StringBuilder sb = new StringBuilder("limit " + (indexNum * batchQueryCount) + ", " + batchQueryCount);
                                MybatisPlusConfig.dynamicTableNameSuffix.set(String.valueOf(supplierStatementId % DynamicTableNameConstant.SUPPLIER_IMPORT_STATEMENT_ANNEX_DIVIDE_TABLE_NUMBER));
                                List<SupplierImportStatementAnnexEntity> list = supplierImportStatementAnnexMapper.selectList(new QueryWrapper<SupplierImportStatementAnnexEntity>().lambda()
                                        .eq(SupplierImportStatementAnnexEntity::getSupplierImportStatementId, supplierStatementId)
                                        .eq(SupplierImportStatementAnnexEntity::getDeleted, 0)
                                        .orderByAsc(SupplierImportStatementAnnexEntity::getId)
                                        .last(sb.toString()));
                                if (CollUtilX.isNotEmpty(list)) {
                                    supplierImportStatementAnnexList.addAll(list);
                                } else {
                                    break;
                                }
                                indexNum++;
                            }


                            for (SupplierImportStatementAnnexEntity supplierImportStatementAnnexEntity : supplierImportStatementAnnexList) {
                                autoReconciliationList.add(SupplyAutoReconciliationEntity.builder()
                                        .reconciliationResultType(3)
                                        .statementId(statementId)
                                        .statementCode(statementCode)
                                        .supplyOrderCode(supplierImportStatementAnnexEntity.getSupplyOrderCode())
                                        .supplierStatementId(supplierImportStatementAnnexEntity.getSupplierImportStatementId())
                                        .supplierStatementCode(supplierStatementIdAndStatementCodeMap.get(supplierImportStatementAnnexEntity.getSupplierImportStatementId()))
                                        .supplierOrderCode(supplierImportStatementAnnexEntity.getSupplierOrderCode())
                                        .supplierReceivedAmt(supplierImportStatementAnnexEntity.getReceivableAmt())
                                        .supplierReceivedAmtCurrency(supplierImportStatementAnnexEntity.getCurrency())
                                        .createdBy(loginUser.getFullUserName()).createdDt(new Date())
                                        .updatedBy(loginUser.getFullUserName()).updatedDt(new Date()).build());
                                if (CollUtilX.isNotEmpty(autoReconciliationList) && autoReconciliationList.size() >= batchInsertCount) {
                                    MybatisPlusConfig.dynamicTableNameSuffix.set(String.valueOf(statementId % DynamicTableNameConstant.SUPPLY_AUTO_RECONCILIATION_DIVIDE_TABLE_NUMBER));
                                    supplyAutoReconciliationMapper.insertBatchSomeColumn(autoReconciliationList);
                                    autoReconciliationList = new ArrayList<>();
                                }
                            }
                            if (CollUtilX.isNotEmpty(autoReconciliationList) && autoReconciliationList.size() > 0) {
                                MybatisPlusConfig.dynamicTableNameSuffix.set(String.valueOf(statementId % DynamicTableNameConstant.SUPPLY_AUTO_RECONCILIATION_DIVIDE_TABLE_NUMBER));
                                supplyAutoReconciliationMapper.insertBatchSomeColumn(autoReconciliationList);
                                autoReconciliationList = new ArrayList<>();
                            }
                        }

                        //更新f_supplier_import_statement
                        if (CollUtilX.isNotEmpty(req.getSupplierStatementIdList())) {
                            supplierImportStatementMapper.update(null, new UpdateWrapper<SupplierImportStatementEntity>().lambda()
                                    .in(SupplierImportStatementEntity::getId, req.getSupplierStatementIdList())
                                    .set(SupplierImportStatementEntity::getStatementId, req.getStatementId())
                                    .set(SupplierImportStatementEntity::getStatementCode, req.getStatementCode())
                                    .set(SupplierImportStatementEntity::getStatementStatus, 1)
                                    .set(SupplierImportStatementEntity::getUpdatedBy, loginUser.getFullUserName())
                                    .set(SupplierImportStatementEntity::getUpdatedDt, new Date()));
                        }

                        if (CollUtilX.isNotEmpty(allSupplierImportStatementIds)) {
                            supplierImportStatementMapper.update(null, new UpdateWrapper<SupplierImportStatementEntity>().lambda()
                                    .in(SupplierImportStatementEntity::getId, allSupplierImportStatementIds)
                                    .set(SupplierImportStatementEntity::getStatementId, null)
                                    .set(SupplierImportStatementEntity::getStatementCode, null)
                                    .set(SupplierImportStatementEntity::getStatementStatus, 0)
                                    .set(SupplierImportStatementEntity::getUpdatedBy, loginUser.getFullUserName())
                                    .set(SupplierImportStatementEntity::getUpdatedDt, new Date()));
                        }

                        //删除redis中的账单对比状态
                        RedisTemplateX.delete(StrUtilX.concat(RedisKey.SUPPLY_AUTO_RECONCILIATION, req.getStatementCode()));
                        //return R.ok("自动对账成功");
                        return;
                    }

                    for (Integer supplierStatementId : supplierStatementIdList) {
                        List<SupplierImportStatementAnnexEntity> supplierImportStatementAnnexList = new ArrayList<>();
                        Integer indexNum = 0;
                        while (true) {
                            StringBuilder sb = new StringBuilder("limit " + (indexNum * batchQueryCount) + ", " + batchQueryCount);
                            MybatisPlusConfig.dynamicTableNameSuffix.set(String.valueOf(supplierStatementId % DynamicTableNameConstant.SUPPLIER_IMPORT_STATEMENT_ANNEX_DIVIDE_TABLE_NUMBER));
                            List<SupplierImportStatementAnnexEntity> list = supplierImportStatementAnnexMapper.selectList(new QueryWrapper<SupplierImportStatementAnnexEntity>().lambda()
                                    .eq(SupplierImportStatementAnnexEntity::getSupplierImportStatementId, supplierStatementId)
                                    .eq(SupplierImportStatementAnnexEntity::getDeleted, 0)
                                    .orderByAsc(SupplierImportStatementAnnexEntity::getId)
                                    .last(sb.toString()));
                            if (CollUtilX.isNotEmpty(list)) {
                                supplierImportStatementAnnexList.addAll(list);
                            } else {
                                break;
                            }
                            indexNum++;
                        }


                        for (SupplierImportStatementAnnexEntity supplierImportStatementAnnexEntity : supplierImportStatementAnnexList) {
                            Map<BigDecimal, ComparisonBillDTO> bigDecimalComparisonBillDTOMap = allSupplierBillOrderMap.get(supplierImportStatementAnnexEntity.getSupplyOrderCode());
                            if (bigDecimalComparisonBillDTOMap != null) {
                                bigDecimalComparisonBillDTOMap.put(supplierImportStatementAnnexEntity.getReceivableAmt(), ComparisonBillDTO.builder().supplierOrderCode(supplierImportStatementAnnexEntity.getSupplierOrderCode())
                                        .supplyOrderCode(supplierImportStatementAnnexEntity.getSupplyOrderCode())
                                        .comparativeAmount(supplierImportStatementAnnexEntity.getReceivableAmt())
                                        .currency(supplierImportStatementAnnexEntity.getCurrency())
                                        .supplierStatementId(supplierImportStatementAnnexEntity.getSupplierImportStatementId())
                                        .supplierStatementCode(supplierStatementIdAndStatementCodeMap.get(supplierImportStatementAnnexEntity.getSupplierImportStatementId()))
                                        .build());
                                allSupplierBillOrderMap.put(supplierImportStatementAnnexEntity.getSupplyOrderCode(), bigDecimalComparisonBillDTOMap);
                            } else {
                                allSupplierBillOrderMap.put(supplierImportStatementAnnexEntity.getSupplyOrderCode(), new HashMap<BigDecimal, ComparisonBillDTO>() {{
                                    put(supplierImportStatementAnnexEntity.getReceivableAmt(), ComparisonBillDTO.builder().supplierOrderCode(supplierImportStatementAnnexEntity.getSupplierOrderCode())
                                            .supplyOrderCode(supplierImportStatementAnnexEntity.getSupplyOrderCode())
                                            .comparativeAmount(supplierImportStatementAnnexEntity.getReceivableAmt())
                                            .currency(supplierImportStatementAnnexEntity.getCurrency())
                                            .supplierStatementId(supplierImportStatementAnnexEntity.getSupplierImportStatementId())
                                            .supplierStatementCode(supplierStatementIdAndStatementCodeMap.get(supplierImportStatementAnnexEntity.getSupplierImportStatementId()))
                                            .build());
                                }});
                            }
                        }
                    }

                    //2.查出系统账单下的所有订单(可考虑流进行优化提速)
                    List<StatementSupplyOrderDTO> queryStatementOrderList = new ArrayList<>();
                    QueryStatementSupplyOrderListPageDTO queryStatementSupplyOrderListPageDTO = new QueryStatementSupplyOrderListPageDTO();
                    queryStatementSupplyOrderListPageDTO.setStatementId(req.getStatementId());
                    queryStatementSupplyOrderListPageDTO.setDateQueryType(2);
                    queryStatementSupplyOrderListPageDTO.setPageSize(batchQueryCount);
                    Integer indexNum = 0;
                    while (true) {
                        queryStatementSupplyOrderListPageDTO.setCurrentPage(indexNum * batchQueryCount);
                        List<StatementSupplyOrderDTO> list = supplierStatementOrderMapper.queryStatementOrderListPage(queryStatementSupplyOrderListPageDTO);
                        if (CollUtilX.isNotEmpty(list)) {
                            queryStatementOrderList.addAll(list);
                        } else {
                            break;
                        }
                        indexNum++;
                    }

                    //3.删除结果
                    MybatisPlusConfig.dynamicTableNameSuffix.set(String.valueOf(statementId % DynamicTableNameConstant.SUPPLY_AUTO_RECONCILIATION_DIVIDE_TABLE_NUMBER));
                    Integer deleteRows = supplyAutoReconciliationMapper.delete(new QueryWrapper<SupplyAutoReconciliationEntity>().lambda()
                            .eq(SupplyAutoReconciliationEntity::getStatementId, statementId));

                    //4遍历2的每一个订单去跟1的进行比较
                    List<SupplyAutoReconciliationEntity> autoReconciliationResultList = new ArrayList<>();
                    //从allSupplierBillOrderMap移除,剩下的就是我方少单的
                    //0:一致  1:金额不对  2:供应商少单  3:我方少单
                    for (StatementSupplyOrderDTO statementSupplyOrderDTO : queryStatementOrderList) {
                        Map<BigDecimal, ComparisonBillDTO> bigDecimalComparisonBillDTOMap = allSupplierBillOrderMap.get(statementSupplyOrderDTO.getSupplyOrderCode());
                        if (bigDecimalComparisonBillDTOMap != null) {
                            ComparisonBillDTO comparisonBillDTO = bigDecimalComparisonBillDTOMap.get(statementSupplyOrderDTO.getPayableAmt());
                            if (comparisonBillDTO != null) {//0:一致
                                autoReconciliationList.add(SupplyAutoReconciliationEntity.builder().reconciliationResultType(0)
                                        .statementId(statementId)
                                        .statementCode(statementCode).supplyOrderCode(statementSupplyOrderDTO.getSupplyOrderCode())
                                        //.supplyPaidAmt(statementSupplyOrderDTO.getPayableAmt()).supplyPaidAmtCurrency(SettlementCurrencyEnum.getCodeByKey(statementSupplyOrderDTO.getCurrency().toString()))
                                        .supplyPaidAmt(statementSupplyOrderDTO.getPayableAmt()).supplyPaidAmtCurrency(SettlementCurrencyEnum.getCodeByKey(String.valueOf(statementSupplyOrderDTO.getCurrency())))
                                        .supplierStatementId(comparisonBillDTO.getSupplierStatementId())
                                        .supplierStatementCode(comparisonBillDTO.getSupplierStatementCode()).supplierOrderCode(comparisonBillDTO.getSupplierOrderCode())
                                        .supplierReceivedAmt(comparisonBillDTO.getComparativeAmount()).supplierReceivedAmtCurrency(comparisonBillDTO.getCurrency())
                                        .createdBy(loginUser.getFullUserName()).createdDt(new Date())
                                        .updatedBy(loginUser.getFullUserName()).updatedDt(new Date()).build());
                                if (bigDecimalComparisonBillDTOMap.keySet().size() == 1) {
                                    bigDecimalComparisonBillDTOMap = null;
                                    allSupplierBillOrderMap.remove(statementSupplyOrderDTO.getSupplyOrderCode());
                                } else {
                                    bigDecimalComparisonBillDTOMap.remove(statementSupplyOrderDTO.getPayableAmt());
                                }
                            }

                            //如果还有剩下的,则全部都为金额不对
                            if (bigDecimalComparisonBillDTOMap != null) {//1:金额不对
                                for (Map.Entry<BigDecimal, ComparisonBillDTO> bigDecimalComparisonBillDTOEntry : bigDecimalComparisonBillDTOMap.entrySet()) {
                                    autoReconciliationList.add(SupplyAutoReconciliationEntity.builder().reconciliationResultType(1)
                                            .statementId(statementId)
                                            .statementCode(statementCode).supplyOrderCode(statementSupplyOrderDTO.getSupplyOrderCode())
                                            //.supplyPaidAmt(statementSupplyOrderDTO.getPayableAmt()).supplyPaidAmtCurrency(SettlementCurrencyEnum.getCodeByKey(statementSupplyOrderDTO.getCurrency().toString()))
                                            .supplyPaidAmt(statementSupplyOrderDTO.getPayableAmt()).supplyPaidAmtCurrency(SettlementCurrencyEnum.getCodeByKey(String.valueOf(statementSupplyOrderDTO.getCurrency())))
                                            .supplierStatementId(bigDecimalComparisonBillDTOEntry.getValue().getSupplierStatementId())
                                            .supplierStatementCode(bigDecimalComparisonBillDTOEntry.getValue().getSupplierStatementCode()).supplierOrderCode(bigDecimalComparisonBillDTOEntry.getValue().getSupplierOrderCode())
                                            .supplierReceivedAmt(bigDecimalComparisonBillDTOEntry.getValue().getComparativeAmount()).supplierReceivedAmtCurrency(bigDecimalComparisonBillDTOEntry.getValue().getCurrency())
                                            .createdBy(loginUser.getFullUserName()).createdDt(new Date())
                                            .updatedBy(loginUser.getFullUserName()).updatedDt(new Date()).build());

                                }
                            }
                            //从allSupplierBillOrderMap移除，剩下的就是我方少单的
                            allSupplierBillOrderMap.remove(statementSupplyOrderDTO.getSupplyOrderCode());
                        } else {//2:供应商少单
                            autoReconciliationList.add(SupplyAutoReconciliationEntity.builder().reconciliationResultType(2)
                                    .statementId(statementId)
                                    .statementCode(statementCode).supplyOrderCode(statementSupplyOrderDTO.getSupplyOrderCode())
                                    //.supplyPaidAmt(statementSupplyOrderDTO.getPayableAmt()).supplyPaidAmtCurrency(SettlementCurrencyEnum.getCodeByKey(statementSupplyOrderDTO.getCurrency().toString()))
                                    .supplyPaidAmt(statementSupplyOrderDTO.getPayableAmt()).supplyPaidAmtCurrency(SettlementCurrencyEnum.getCodeByKey(String.valueOf(statementSupplyOrderDTO.getCurrency())))
                                    .supplierOrderCode(statementSupplyOrderDTO.getSupplierOrderCode())
                                    .createdBy(loginUser.getFullUserName()).createdDt(new Date())
                                    .updatedBy(loginUser.getFullUserName()).updatedDt(new Date()).build());
                        }
                        if (CollUtilX.isNotEmpty(autoReconciliationList) && autoReconciliationList.size() >= batchInsertCount) {
                            MybatisPlusConfig.dynamicTableNameSuffix.set(String.valueOf(statementId % DynamicTableNameConstant.SUPPLY_AUTO_RECONCILIATION_DIVIDE_TABLE_NUMBER));
                            supplyAutoReconciliationMapper.insertBatchSomeColumn(autoReconciliationList);
                            autoReconciliationList = new ArrayList<>();
                        }
                    }

                    //3:我方少单
                    for (Map.Entry<String, Map<BigDecimal, ComparisonBillDTO>> mapTmpEntry : allSupplierBillOrderMap.entrySet()) {
                        for (Map.Entry<BigDecimal, ComparisonBillDTO> bigDecimalComparisonBillDTOEntry : mapTmpEntry.getValue().entrySet()) {
                            autoReconciliationList.add(SupplyAutoReconciliationEntity.builder()
                                    .reconciliationResultType(3)
                                    .statementId(statementId)
                                    .statementCode(statementCode)
                                    .supplyOrderCode(bigDecimalComparisonBillDTOEntry.getValue().getSupplyOrderCode())
                                    .supplierStatementId(bigDecimalComparisonBillDTOEntry.getValue().getSupplierStatementId())
                                    .supplierStatementCode(bigDecimalComparisonBillDTOEntry.getValue().getSupplierStatementCode())
                                    .supplierOrderCode(bigDecimalComparisonBillDTOEntry.getValue().getSupplierOrderCode())
                                    .supplierReceivedAmt(bigDecimalComparisonBillDTOEntry.getValue().getComparativeAmount())
                                    .supplierReceivedAmtCurrency(bigDecimalComparisonBillDTOEntry.getValue().getCurrency())
                                    .createdBy(loginUser.getFullUserName()).createdDt(new Date())
                                    .updatedBy(loginUser.getFullUserName()).updatedDt(new Date()).build());
                        }
                        if (CollUtilX.isNotEmpty(autoReconciliationList) && autoReconciliationList.size() >= batchInsertCount) {
                            MybatisPlusConfig.dynamicTableNameSuffix.set(String.valueOf(statementId % DynamicTableNameConstant.SUPPLY_AUTO_RECONCILIATION_DIVIDE_TABLE_NUMBER));
                            supplyAutoReconciliationMapper.insertBatchSomeColumn(autoReconciliationList);
                            autoReconciliationList = new ArrayList<>();
                        }
                    }

                    if (CollUtilX.isNotEmpty(autoReconciliationList)) {
                        MybatisPlusConfig.dynamicTableNameSuffix.set(String.valueOf(statementId % DynamicTableNameConstant.SUPPLY_AUTO_RECONCILIATION_DIVIDE_TABLE_NUMBER));
                        supplyAutoReconciliationMapper.insertBatchSomeColumn(autoReconciliationList);
                        autoReconciliationList = new ArrayList<>();
                    }


                    //更新f_supplier_import_statement
                    if (CollUtilX.isNotEmpty(req.getSupplierStatementIdList())) {
                        supplierImportStatementMapper.update(null, new UpdateWrapper<SupplierImportStatementEntity>().lambda()
                                .in(SupplierImportStatementEntity::getId, req.getSupplierStatementIdList())
                                .set(SupplierImportStatementEntity::getStatementId, req.getStatementId())
                                .set(SupplierImportStatementEntity::getStatementCode, req.getStatementCode())
                                .set(SupplierImportStatementEntity::getStatementStatus, 1)
                                .set(SupplierImportStatementEntity::getUpdatedBy, loginUser.getFullUserName())
                                .set(SupplierImportStatementEntity::getUpdatedDt, new Date()));
                    }

                    if (CollUtilX.isNotEmpty(allSupplierImportStatementIds)) {
                        supplierImportStatementMapper.update(null, new UpdateWrapper<SupplierImportStatementEntity>().lambda()
                                .in(SupplierImportStatementEntity::getId, allSupplierImportStatementIds)
                                .set(SupplierImportStatementEntity::getStatementId, null)
                                .set(SupplierImportStatementEntity::getStatementCode, null)
                                .set(SupplierImportStatementEntity::getStatementStatus, 0)
                                .set(SupplierImportStatementEntity::getUpdatedBy, loginUser.getFullUserName())
                                .set(SupplierImportStatementEntity::getUpdatedDt, new Date()));
                    }

                    //删除redis中的账单对比状态
                    RedisTemplateX.delete(StrUtilX.concat(RedisKey.SUPPLY_AUTO_RECONCILIATION, req.getStatementCode()));
                } catch (Exception e) {
                    //更新f_supplier_import_statement
                    if (CollUtilX.isNotEmpty(req.getSupplierStatementIdList())) {
                        supplierImportStatementMapper.update(null, new UpdateWrapper<SupplierImportStatementEntity>().lambda()
                                .in(SupplierImportStatementEntity::getId, req.getSupplierStatementIdList())
                                .set(SupplierImportStatementEntity::getStatementId, req.getStatementId())
                                .set(SupplierImportStatementEntity::getStatementCode, req.getStatementCode())
                                .set(SupplierImportStatementEntity::getStatementStatus, 1)
                                .set(SupplierImportStatementEntity::getUpdatedBy, loginUser.getFullUserName())
                                .set(SupplierImportStatementEntity::getUpdatedDt, new Date()));
                    }

                    if (CollUtilX.isNotEmpty(allSupplierImportStatementIds)) {
                        supplierImportStatementMapper.update(null, new UpdateWrapper<SupplierImportStatementEntity>().lambda()
                                .in(SupplierImportStatementEntity::getId, allSupplierImportStatementIds)
                                .set(SupplierImportStatementEntity::getStatementId, null)
                                .set(SupplierImportStatementEntity::getStatementCode, null)
                                .set(SupplierImportStatementEntity::getStatementStatus, 0)
                                .set(SupplierImportStatementEntity::getUpdatedBy, loginUser.getFullUserName())
                                .set(SupplierImportStatementEntity::getUpdatedDt, new Date()));
                    }

                    //删除结果
                    MybatisPlusConfig.dynamicTableNameSuffix.set(String.valueOf(statementId % DynamicTableNameConstant.SUPPLY_AUTO_RECONCILIATION_DIVIDE_TABLE_NUMBER));
                    Integer deleteRows = supplyAutoReconciliationMapper.delete(new QueryWrapper<SupplyAutoReconciliationEntity>().lambda()
                            .eq(SupplyAutoReconciliationEntity::getStatementId, statementId));

                    RedisTemplateX.delete(StrUtilX.concat(RedisKey.SUPPLY_AUTO_RECONCILIATION, req.getStatementCode()));
                    log.error("comparisonBillAdd--error:", e);
                }
            });
        } catch (Exception e) {
            if (CollUtilX.isNotEmpty(req.getSupplierStatementIdList())) {
                supplierImportStatementMapper.update(null, new UpdateWrapper<SupplierImportStatementEntity>().lambda()
                        .in(SupplierImportStatementEntity::getId, req.getSupplierStatementIdList())
                        .set(SupplierImportStatementEntity::getStatementId, null)
                        .set(SupplierImportStatementEntity::getStatementCode, null)
                        .set(SupplierImportStatementEntity::getStatementStatus, 0)
                        .set(SupplierImportStatementEntity::getUpdatedBy, loginUser.getFullUserName())
                        .set(SupplierImportStatementEntity::getUpdatedDt, new Date()));
            }

            RedisTemplateX.delete(StrUtilX.concat(RedisKey.SUPPLY_AUTO_RECONCILIATION, req.getStatementCode()));
            log.error("comparisonBillAdd--error:", e);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            throw new SysException(ErrorCodeEnum.FAIL.errorCode, "对账失败!");
        }
    }

    /**
     * 自动对账结果列表（分页）
     */
    @Override
    public PageVo supplyAutoReconciliationPage(SupplyAutoReconciliationPageReq req) {
        if (req.getReconciliationResultType() == null || req.getStatementId() == null || StrUtilX.isEmpty(req.getStatementCode())) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }

        //获取redis中的标志，判断是否正在比对中
        String flag = RedisTemplateX.get(StrUtilX.concat(RedisKey.SUPPLY_AUTO_RECONCILIATION, req.getStatementCode()));
        if (StrUtilX.isNotEmpty(flag)) {
            throw new SysException(ErrorCodeEnum.FAIL.errorCode, "此账单正在对比中,请稍后再查看结果!");
        }

        Page<SupplyAutoReconciliationEntity> supplyAutoReconciliationPage = new Page<>(req.getCurrentPage(), req.getPageSize());
        QueryWrapper<SupplyAutoReconciliationEntity> wrapper = new QueryWrapper<>();

        wrapper.lambda().eq(SupplyAutoReconciliationEntity::getStatementId, req.getStatementId())
                .eq(SupplyAutoReconciliationEntity::getReconciliationResultType, req.getReconciliationResultType());

        MybatisPlusConfig.dynamicTableNameSuffix.set(String.valueOf(req.getStatementId() % DynamicTableNameConstant.SUPPLY_AUTO_RECONCILIATION_DIVIDE_TABLE_NUMBER));
        IPage<SupplyAutoReconciliationEntity> ipage = supplyAutoReconciliationMapper.selectPage(supplyAutoReconciliationPage, wrapper);

        List<SupplyAutoReconciliationPageResp> collect = ipage.getRecords().stream().map((item) -> {
            SupplyAutoReconciliationPageResp resp = new SupplyAutoReconciliationPageResp();
            BeanUtils.copyProperties(item, resp);
            if (item.getSupplyPaidAmt() != null) {
                resp.setSupplyPaidAmtWithCurrency(item.getSupplyPaidAmtCurrency() + item.getSupplyPaidAmt());
            }
            if (item.getSupplierReceivedAmt() != null && item.getSupplierReceivedAmt().compareTo(SignsConstant.MONEY_SIGN) > 0) {
                resp.setSupplierReceivedAmtWithCurrency(item.getSupplierReceivedAmtCurrency() + item.getSupplierReceivedAmt());
            } else if (item.getSupplierReceivedAmt() != null && item.getSupplierReceivedAmt().compareTo(SignsConstant.MONEY_SIGN) == 0) {
                resp.setSupplierReceivedAmtWithCurrency(item.getSupplierReceivedAmtCurrency());
            }
            return resp;
        }).collect(Collectors.toList());

        return PageVo.result(ipage, collect);
    }

    /**
     * 自动对账结果各种类型数量的统计
     */
    @Override
    public ReconciliationResultTypeCountResp reconciliationResultTypeCount(ReconciliationResultTypeCountReq req) {
        if (req.getStatementId() == null || StrUtilX.isEmpty(req.getStatementCode())) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }

        //获取redis中的标志，判断是否正在比对中
        String flag = RedisTemplateX.get(StrUtilX.concat(RedisKey.SUPPLY_AUTO_RECONCILIATION, req.getStatementCode()));
        if (StrUtilX.isNotEmpty(flag)) {
            throw new SysException(ErrorCodeEnum.FAIL.errorCode, "此账单正在对比中,请稍后再查看结果!");
        }

        MybatisPlusConfig.dynamicTableNameSuffix.set(String.valueOf(req.getStatementId() % DynamicTableNameConstant.SUPPLY_AUTO_RECONCILIATION_DIVIDE_TABLE_NUMBER));
        Integer consistentCount = supplyAutoReconciliationMapper.selectCount(new QueryWrapper<SupplyAutoReconciliationEntity>().lambda()
                .eq(SupplyAutoReconciliationEntity::getStatementId, req.getStatementId())
                .eq(SupplyAutoReconciliationEntity::getReconciliationResultType, ReconciliationResultTypeEnum.CONSISTENT.key));

        MybatisPlusConfig.dynamicTableNameSuffix.set(String.valueOf(req.getStatementId() % DynamicTableNameConstant.SUPPLY_AUTO_RECONCILIATION_DIVIDE_TABLE_NUMBER));
        Integer amountIncorrectCount = supplyAutoReconciliationMapper.selectCount(new QueryWrapper<SupplyAutoReconciliationEntity>().lambda()
                .eq(SupplyAutoReconciliationEntity::getStatementId, req.getStatementId())
                .eq(SupplyAutoReconciliationEntity::getReconciliationResultType, ReconciliationResultTypeEnum.AMOUNTINCORRECT.key));

        MybatisPlusConfig.dynamicTableNameSuffix.set(String.valueOf(req.getStatementId() % DynamicTableNameConstant.SUPPLY_AUTO_RECONCILIATION_DIVIDE_TABLE_NUMBER));
        Integer supplierLessOrderCount = supplyAutoReconciliationMapper.selectCount(new QueryWrapper<SupplyAutoReconciliationEntity>().lambda()
                .eq(SupplyAutoReconciliationEntity::getStatementId, req.getStatementId())
                .eq(SupplyAutoReconciliationEntity::getReconciliationResultType, ReconciliationResultTypeEnum.SUPPLIERLESSORDER.key));

        MybatisPlusConfig.dynamicTableNameSuffix.set(String.valueOf(req.getStatementId() % DynamicTableNameConstant.SUPPLY_AUTO_RECONCILIATION_DIVIDE_TABLE_NUMBER));
        Integer lessOrderCount = supplyAutoReconciliationMapper.selectCount(new QueryWrapper<SupplyAutoReconciliationEntity>().lambda()
                .eq(SupplyAutoReconciliationEntity::getStatementId, req.getStatementId())
                .eq(SupplyAutoReconciliationEntity::getReconciliationResultType, ReconciliationResultTypeEnum.LESSORDERCOUNT.key));

        ReconciliationResultTypeCountResp result = ReconciliationResultTypeCountResp.builder()
                .consistentCount(consistentCount)
                .amountIncorrectCount(amountIncorrectCount)
                .supplierLessOrderCount(supplierLessOrderCount)
                .lessOrderCount(lessOrderCount)
                .build();
        return result;
    }

    /**
     * 导出比对结果
     */
    @Override
    public void reconciliationResultExport(ReconciliationResultExportReq req, HttpServletResponse response) {

        if (req.getStatementId() == null || StrUtilX.isEmpty(req.getStatementCode())) {
            return;
        }

        LocalDateTime startTime = LocalDateTime.now();
        OutputStream output = null;
        BufferedInputStream bis = null;
        BufferedOutputStream bos = null;
        ByteArrayInputStream byteArrayInputStream = null;


        try {
            //一致
            SXSSFWorkbook workbook = new SXSSFWorkbook(5000);
            Row row = null;
            Sheet sheet = workbook.createSheet(ReconciliationResultTypeEnum.CONSISTENT.value);
            Row rowTop = sheet.createRow(0);
            rowTop.createCell(0).setCellValue("我方供货单号");
            rowTop.createCell(1).setCellValue("我方要付");
            rowTop.createCell(2).setCellValue("供应商账单号");
            rowTop.createCell(3).setCellValue("供应商单号");
            rowTop.createCell(4).setCellValue("供应商要收");

            Integer indexNum = 0;
            int rowNum = 1;
            while (indexNum < breakNum) {
                String sb = "limit " + (indexNum * batchQueryCount) + ", " + batchQueryCount;
                MybatisPlusConfig.dynamicTableNameSuffix.set(String.valueOf(req.getStatementId() % DynamicTableNameConstant.SUPPLY_AUTO_RECONCILIATION_DIVIDE_TABLE_NUMBER));
                List<SupplyAutoReconciliationEntity> consistent = supplyAutoReconciliationMapper.selectList(new QueryWrapper<SupplyAutoReconciliationEntity>().lambda()
                        .eq(SupplyAutoReconciliationEntity::getStatementId, req.getStatementId())
                        .eq(SupplyAutoReconciliationEntity::getReconciliationResultType, ReconciliationResultTypeEnum.CONSISTENT.key)
                        .last(sb));
                if (CollUtilX.isNotEmpty(consistent)) {
                    for (SupplyAutoReconciliationEntity supplyAutoReconciliation : consistent) {
                        row = sheet.createRow(rowNum);
                        row.createCell(0).setCellValue(supplyAutoReconciliation.getSupplyOrderCode());
                        row.createCell(1).setCellValue(supplyAutoReconciliation.getSupplyPaidAmtCurrency() + supplyAutoReconciliation.getSupplyPaidAmt());
                        row.createCell(2).setCellValue(supplyAutoReconciliation.getSupplierStatementCode());
                        row.createCell(3).setCellValue(supplyAutoReconciliation.getSupplierOrderCode());
                        row.createCell(4).setCellValue(supplyAutoReconciliation.getSupplierReceivedAmtCurrency() + supplyAutoReconciliation.getSupplierReceivedAmt());
                        rowNum++;
                    }
                } else {
                    break;
                }
                log.info("自动对账结果导出中--一致:{}", indexNum);
                indexNum++;
            }
            sheet.setColumnWidth(0, 25 * 256);
            sheet.setColumnWidth(1, 25 * 256);
            sheet.setColumnWidth(2, 25 * 256);
            sheet.setColumnWidth(3, 25 * 256);
            sheet.setColumnWidth(4, 25 * 256);

            //金额不对
            sheet = workbook.createSheet(ReconciliationResultTypeEnum.AMOUNTINCORRECT.value);
            rowTop = sheet.createRow(0);
            rowTop.createCell(0).setCellValue("我方供货单号");
            rowTop.createCell(1).setCellValue("我方要付");
            rowTop.createCell(2).setCellValue("供应商账单号");
            rowTop.createCell(3).setCellValue("供应商单号");
            rowTop.createCell(4).setCellValue("供应商要收");

            indexNum = 0;
            rowNum = 1;
            while (indexNum < breakNum) {
                String sb = "limit " + (indexNum * batchQueryCount) + ", " + batchQueryCount;
                MybatisPlusConfig.dynamicTableNameSuffix.set(String.valueOf(req.getStatementId() % DynamicTableNameConstant.SUPPLY_AUTO_RECONCILIATION_DIVIDE_TABLE_NUMBER));
                List<SupplyAutoReconciliationEntity> amountIncorrect = supplyAutoReconciliationMapper.selectList(new QueryWrapper<SupplyAutoReconciliationEntity>().lambda()
                        .eq(SupplyAutoReconciliationEntity::getStatementId, req.getStatementId())
                        .eq(SupplyAutoReconciliationEntity::getReconciliationResultType, ReconciliationResultTypeEnum.AMOUNTINCORRECT.key)
                        .last(sb));
                if (CollUtilX.isNotEmpty(amountIncorrect)) {
                    for (SupplyAutoReconciliationEntity supplyAutoReconciliation : amountIncorrect) {
                        row = sheet.createRow(rowNum);
                        row.createCell(0).setCellValue(supplyAutoReconciliation.getSupplyOrderCode());
                        if (supplyAutoReconciliation.getSupplyPaidAmt() != null) {
                            row.createCell(1).setCellValue(supplyAutoReconciliation.getSupplyPaidAmtCurrency() + supplyAutoReconciliation.getSupplyPaidAmt());
                        }
                        row.createCell(2).setCellValue(supplyAutoReconciliation.getSupplierStatementCode());
                        row.createCell(3).setCellValue(supplyAutoReconciliation.getSupplierOrderCode());
                        if (supplyAutoReconciliation.getSupplierReceivedAmt() != null && supplyAutoReconciliation.getSupplierReceivedAmt().compareTo(SignsConstant.MONEY_SIGN) > 0) {
                            row.createCell(4).setCellValue(supplyAutoReconciliation.getSupplierReceivedAmtCurrency() + supplyAutoReconciliation.getSupplierReceivedAmt());
                        } else if (supplyAutoReconciliation.getSupplierReceivedAmt() != null && supplyAutoReconciliation.getSupplierReceivedAmt().compareTo(SignsConstant.MONEY_SIGN) == 0) {
                            row.createCell(4).setCellValue(supplyAutoReconciliation.getSupplierReceivedAmtCurrency());
                        }
                        rowNum++;
                    }
                } else {
                    break;
                }
                log.info("自动对账结果导出中--金额不对:{}", indexNum);
                indexNum++;
            }
            sheet.setColumnWidth(0, 25 * 256);
            sheet.setColumnWidth(1, 25 * 256);
            sheet.setColumnWidth(2, 25 * 256);
            sheet.setColumnWidth(3, 25 * 256);
            sheet.setColumnWidth(4, 25 * 256);

            //供应商少单
            sheet = workbook.createSheet(ReconciliationResultTypeEnum.SUPPLIERLESSORDER.value);
            rowTop = sheet.createRow(0);
            rowTop.createCell(0).setCellValue("我方供货单号");
            rowTop.createCell(1).setCellValue("我方要付");
            rowTop.createCell(2).setCellValue("供应商账单号");
            rowTop.createCell(3).setCellValue("供应商单号");
            rowTop.createCell(4).setCellValue("供应商要收");

            indexNum = 0;
            rowNum = 1;
            while (indexNum < breakNum) {
                String sb = "limit " + (indexNum * batchQueryCount) + ", " + batchQueryCount;
                MybatisPlusConfig.dynamicTableNameSuffix.set(String.valueOf(req.getStatementId() % DynamicTableNameConstant.SUPPLY_AUTO_RECONCILIATION_DIVIDE_TABLE_NUMBER));
                List<SupplyAutoReconciliationEntity> supplierLessOrder = supplyAutoReconciliationMapper.selectList(new QueryWrapper<SupplyAutoReconciliationEntity>().lambda()
                        .eq(SupplyAutoReconciliationEntity::getStatementId, req.getStatementId())
                        .eq(SupplyAutoReconciliationEntity::getReconciliationResultType, ReconciliationResultTypeEnum.SUPPLIERLESSORDER.key)
                        .last(sb));
                if (CollUtilX.isNotEmpty(supplierLessOrder)) {
                    for (SupplyAutoReconciliationEntity supplyAutoReconciliation : supplierLessOrder) {
                        row = sheet.createRow(rowNum);
                        row.createCell(0).setCellValue(supplyAutoReconciliation.getSupplyOrderCode());
                        row.createCell(1).setCellValue(supplyAutoReconciliation.getSupplyPaidAmtCurrency() + supplyAutoReconciliation.getSupplyPaidAmt());
                        row.createCell(2).setCellValue(supplyAutoReconciliation.getSupplierStatementCode());
                        row.createCell(3).setCellValue(supplyAutoReconciliation.getSupplierOrderCode());
                        rowNum++;
                    }
                } else {
                    break;
                }
                log.info("自动对账结果导出中--供应商少单:{}", indexNum);
                indexNum++;
            }
            sheet.setColumnWidth(0, 25 * 256);
            sheet.setColumnWidth(1, 25 * 256);
            sheet.setColumnWidth(2, 25 * 256);
            sheet.setColumnWidth(3, 25 * 256);
            sheet.setColumnWidth(4, 25 * 256);

            //我方少单
            sheet = workbook.createSheet(ReconciliationResultTypeEnum.LESSORDERCOUNT.value);
            rowTop = sheet.createRow(0);
            rowTop.createCell(0).setCellValue("我方供货单号");
            rowTop.createCell(1).setCellValue("我方要付");
            rowTop.createCell(2).setCellValue("供应商账单号");
            rowTop.createCell(3).setCellValue("供应商单号");
            rowTop.createCell(4).setCellValue("供应商要收");

            indexNum = 0;
            rowNum = 1;
            while (indexNum < breakNum) {
                String sb = "limit " + (indexNum * batchQueryCount) + ", " + batchQueryCount;
                MybatisPlusConfig.dynamicTableNameSuffix.set(String.valueOf(req.getStatementId() % DynamicTableNameConstant.SUPPLY_AUTO_RECONCILIATION_DIVIDE_TABLE_NUMBER));
                List<SupplyAutoReconciliationEntity> lessOrderCount = supplyAutoReconciliationMapper.selectList(new QueryWrapper<SupplyAutoReconciliationEntity>().lambda()
                        .eq(SupplyAutoReconciliationEntity::getStatementId, req.getStatementId())
                        .eq(SupplyAutoReconciliationEntity::getReconciliationResultType, ReconciliationResultTypeEnum.LESSORDERCOUNT.key)
                        .last(sb));
                if (CollUtilX.isNotEmpty(lessOrderCount)) {
                    for (SupplyAutoReconciliationEntity supplyAutoReconciliation : lessOrderCount) {
                        row = sheet.createRow(rowNum);
                        row.createCell(0).setCellValue(supplyAutoReconciliation.getSupplyOrderCode());
                        row.createCell(2).setCellValue(supplyAutoReconciliation.getSupplierStatementCode());
                        row.createCell(3).setCellValue(supplyAutoReconciliation.getSupplierOrderCode());
                        if (supplyAutoReconciliation.getSupplierReceivedAmt() != null && supplyAutoReconciliation.getSupplierReceivedAmt().compareTo(SignsConstant.MONEY_SIGN) > 0) {
                            row.createCell(4).setCellValue(supplyAutoReconciliation.getSupplierReceivedAmtCurrency() + supplyAutoReconciliation.getSupplierReceivedAmt());
                        } else if (supplyAutoReconciliation.getSupplierReceivedAmt() != null && supplyAutoReconciliation.getSupplierReceivedAmt().compareTo(SignsConstant.MONEY_SIGN) == 0) {
                            row.createCell(4).setCellValue(supplyAutoReconciliation.getSupplierReceivedAmtCurrency());
                        }
                        rowNum++;
                    }
                } else {
                    break;
                }
                log.info("自动对账结果导出中--我方少单:{}", indexNum);
                indexNum++;
            }
            sheet.setColumnWidth(0, 25 * 256);
            sheet.setColumnWidth(1, 25 * 256);
            sheet.setColumnWidth(2, 25 * 256);
            sheet.setColumnWidth(3, 25 * 256);
            sheet.setColumnWidth(4, 25 * 256);

            ByteArrayOutputStream outputTmp = new ByteArrayOutputStream();
            workbook.write(outputTmp);
            outputTmp.flush();
            byte[] byteArray = outputTmp.toByteArray();
            IOUtils.closeQuietly(outputTmp);
            byteArrayInputStream = new ByteArrayInputStream(byteArray, 0, byteArray.length);

//                告诉浏览器用什么软件可以打开此文件
            response.setContentType("application/vnd.ms-excel;charset=utf-8");
            // 下载文件的默认名称
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("自动对账结果.xlsx", "UTF-8"));
            output = response.getOutputStream();
            bis = new BufferedInputStream(byteArrayInputStream);
            bos = new BufferedOutputStream(output);
            //每次50M
            byte[] buff = new byte[1024 * 1024 * 50];
            int bytesRead;
            while (-1 != (bytesRead = bis.read(buff, 0, buff.length))) {
                bos.write(buff, 0, bytesRead);
            }
        } catch (Exception e) {
            log.error("exportStatement has error", e);
        } finally {
            if (null != bos) {
                IOUtils.closeQuietly(bos);
            }
            if (null != bis) {
                IOUtils.closeQuietly(bis);
            }
            if (null != output) {
                IOUtils.closeQuietly(output);
            }
            if (null != byteArrayInputStream) {
                IOUtils.closeQuietly(byteArrayInputStream);
            }
        }

    }

    /**
     * 查询账单状态
     */
    @Override
    public QueryStatementStatusResp queryStatementStatus(QueryStatementStatusReq req) {
        if (req.getStatementId() == null || StrUtilX.isEmpty(req.getStatementCode())) {
            throw new SysException(ErrorCodeEnum.INVALID_INPUTPARAM);
        }

        String statementStatus = RedisTemplateX.get(StrUtilX.concat(RedisKey.SUPPLY_AUTO_RECONCILIATION, req.getStatementCode()));
        if (StrUtilX.isNotEmpty(statementStatus)) {//正在比对中
            return QueryStatementStatusResp.builder().billStatus(1).build();
        } else {
            return QueryStatementStatusResp.builder().billStatus(0).build();
        }
    }

}
