package com.tiangong.finance.statement.server;

import com.tiangong.cloud.commonbean.utils.PageVo;
import com.tiangong.common.Response;
import com.tiangong.finance.statement.domain.req.*;
import com.tiangong.finance.statement.domain.resp.QueryStatementStatusResp;
import com.tiangong.finance.statement.domain.resp.ReconciliationResultTypeCountResp;
import com.tiangong.finance.statement.service.SupplyAutoReconciliationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;

/**
 * 自动对账
 *
 * <AUTHOR>
 * @Date 2023/5/19 17:50
 * @Description:
 */
@RestController
@RequestMapping("/finance/supplyAutoReconciliation")
public class SupplyAutoReconciliationController {

    @Autowired
    private SupplyAutoReconciliationService supplyAutoReconciliationService;

    /**
     * 添加/减少/重新比对账单
     */
    @PostMapping("/comparisonBillAdd")
    public Response<Object> comparisonBillAdd(@RequestBody ComparisonBillAddReq req) {
        supplyAutoReconciliationService.comparisonBillAdd(req);
        return Response.success();
    }

    /**
     * 自动对账列表（分页）
     */
    @PostMapping("/supplyAutoReconciliationPage")
    public Response<PageVo> supplyAutoReconciliationPage(@RequestBody SupplyAutoReconciliationPageReq req) {
        return Response.success(supplyAutoReconciliationService.supplyAutoReconciliationPage(req));
    }


    /**
     * 自动对账结果各种类型数量的统计
     */
    @PostMapping("/reconciliationResultTypeCount")
    public Response<ReconciliationResultTypeCountResp> reconciliationResultTypeCount(@RequestBody ReconciliationResultTypeCountReq req) {
        return Response.success(supplyAutoReconciliationService.reconciliationResultTypeCount(req));
    }

    /**
     * 导出比对结果
     */
    @PostMapping("/reconciliationResultExport")
    public void reconciliationResultExport(@RequestBody ReconciliationResultExportReq req, HttpServletResponse response) {
        supplyAutoReconciliationService.reconciliationResultExport(req, response);
    }

    /**
     * 查询账单状态
     */
    @PostMapping("/queryStatementStatus")
    public Response<QueryStatementStatusResp> queryStatementStatus(@RequestBody QueryStatementStatusReq req) {
        return Response.success(supplyAutoReconciliationService.queryStatementStatus(req));
    }

}

