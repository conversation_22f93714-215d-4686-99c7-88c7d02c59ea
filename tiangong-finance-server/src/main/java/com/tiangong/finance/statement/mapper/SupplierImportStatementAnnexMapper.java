package com.tiangong.finance.statement.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tiangong.finance.statement.domain.entity.SupplierImportStatementAnnexEntity;
import org.springframework.stereotype.Repository;

import java.util.Collection;


/**
 * 供应商导入的账单的附件
 *
 * <AUTHOR>
 * @Date 2023/5/19 17:22
 * @Description:
 */
@Repository
public interface SupplierImportStatementAnnexMapper extends BaseMapper<SupplierImportStatementAnnexEntity> {

    Integer insertBatchSomeColumn(Collection<SupplierImportStatementAnnexEntity> entityList);

}
