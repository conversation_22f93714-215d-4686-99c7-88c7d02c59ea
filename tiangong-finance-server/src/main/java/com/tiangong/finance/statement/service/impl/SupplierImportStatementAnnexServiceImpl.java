package com.tiangong.finance.statement.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tiangong.cloud.common.constant.DynamicTableNameConstant;
import com.tiangong.cloud.commonbean.config.page.MybatisPlusConfig;
import com.tiangong.cloud.commonbean.utils.PageVo;
import com.tiangong.finance.statement.constant.SignsConstant;
import com.tiangong.finance.statement.domain.entity.SupplierImportStatementAnnexEntity;
import com.tiangong.finance.statement.domain.req.SupplierImportStatementAnnexPageReq;
import com.tiangong.finance.statement.domain.resp.SupplierImportStatementAnnexPageResp;
import com.tiangong.finance.statement.mapper.SupplierImportStatementAnnexMapper;
import com.tiangong.finance.statement.service.SupplierImportStatementAnnexService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 供应商导入的账单的附件
 *
 * <AUTHOR>
 * @Date 2023/5/19 17:38
 * @Description:
 */
@Slf4j
@Service
public class SupplierImportStatementAnnexServiceImpl extends ServiceImpl<SupplierImportStatementAnnexMapper, SupplierImportStatementAnnexEntity> implements SupplierImportStatementAnnexService {

    @Autowired
    private SupplierImportStatementAnnexMapper supplierImportStatementAnnexMapper;

    @Autowired
    HttpServletRequest request;


    /**
     * 供应商导入的账单的附件列表（分页）
     */
    @Override
    public PageVo supplierImportStatementAnnexPage(SupplierImportStatementAnnexPageReq req) {
        Page<SupplierImportStatementAnnexEntity> supplierImportStatementAnnexPage = new Page<>(req.getCurrentPage(), req.getPageSize());
        QueryWrapper<SupplierImportStatementAnnexEntity> wrapper = new QueryWrapper<>();

        wrapper.lambda().eq(SupplierImportStatementAnnexEntity::getDeleted, 0)
                .eq(req.getSupplierImportStatementId() != null, SupplierImportStatementAnnexEntity::getSupplierImportStatementId, req.getSupplierImportStatementId());

        MybatisPlusConfig.dynamicTableNameSuffix.set(String.valueOf(req.getSupplierImportStatementId() % DynamicTableNameConstant.SUPPLIER_IMPORT_STATEMENT_ANNEX_DIVIDE_TABLE_NUMBER));
        IPage<SupplierImportStatementAnnexEntity> ipage = supplierImportStatementAnnexMapper.selectPage(supplierImportStatementAnnexPage, wrapper);

        List<SupplierImportStatementAnnexPageResp> collect = ipage.getRecords().stream().map((item) -> {
            SupplierImportStatementAnnexPageResp resp = new SupplierImportStatementAnnexPageResp();
            BeanUtils.copyProperties(item, resp);
            if (item.getReceivableAmt() != null && item.getReceivableAmt().compareTo(SignsConstant.MONEY_SIGN) > 0) {
                resp.setReceivableAmtWithCurrency(item.getCurrency() + item.getReceivableAmt());
            } else if (item.getReceivableAmt() != null && item.getReceivableAmt().compareTo(SignsConstant.MONEY_SIGN) == 0) {
                resp.setReceivableAmtWithCurrency(item.getCurrency());
            }
            return resp;
        }).collect(Collectors.toList());

        return PageVo.result(ipage, collect);
    }

}
