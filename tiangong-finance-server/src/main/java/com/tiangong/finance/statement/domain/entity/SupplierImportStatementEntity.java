package com.tiangong.finance.statement.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 供应商导入账单
 *
 * <AUTHOR>
 * @Date 2023/5/19 17:14
 * @Description:
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@TableName("f_supplier_import_statement")
public class SupplierImportStatementEntity extends Model<SupplierImportStatementEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * id号
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    /**
     * 供应商账单编号
     * 供应商账单编号规则：G+年月日+四位随机数
     */
    private String supplierStatementCode;
    /**
     * 我方账单Id
     */
    private Integer statementId;
    /**
     * 我方账单编号
     */
    private String statementCode;
    /**
     * 供应商编号
     */
    private String supplierCode;
    /**
     * 供应商名称
     */
    private String supplierName;
    /**
     * 日期查询类型
     * 0下单日期
     * 1入住日期
     * 2离店日期
     */
    private Integer dateQueryType;
    /**
     * 开始日期
     */
    private Date startDate;
    /**
     * 结束日期
     */
    private Date endDate;
    /**
     * 供应商账单名称
     * 命名默认自动取：供应商名称+口径+日期范围，名称支持修改
     */
    private String supplierStatementName;
    /**
     * 结算日期
     */
    private Date settlementDate;
    /**
     * 供应商总要收
     */
    private BigDecimal supplierTotalReceivables;
    /**
     * 币种
     */
    private String currency;
    /**
     * 账单状态
     * 0:待对比
     * 1:对比中
     * 2:已对比
     * 3:导入中
     * 4:导入失败
     */
    private Integer statementStatus;
    /**
     * 是否删除
     * 0 否
     * 1 是
     */
    private Integer deleted;
    /**
     * 数据创建人
     */
    private String createdBy;
    /**
     * 数据创建时间
     */
    private Date createdDt;
    /**
     * 修改人
     */
    private String updatedBy;
    /**
     * 修改时间
     */
    private Date updatedDt;

}
