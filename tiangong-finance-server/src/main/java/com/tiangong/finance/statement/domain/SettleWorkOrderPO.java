package com.tiangong.finance.statement.domain;

import lombok.Data;

import javax.persistence.*;
import java.util.Date;

@Data
@Table(name = "f_settle_work_order")
public class SettleWorkOrderPO {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 任务编码
     */
    @Column(name = "task_code")
    private String taskCode;

    /**
     * 自助结算请求信息
     */
    @Column(name = "settle_json")
    private String settleJson;

    /**
     * 自助结算任务编码
     */
    @Column(name = "settle_task_code")
    private String settleTaskCode;

    /**
     * 账单创建状态 0-未处理 1-已处理 2-处理失败
     */
    @Column(name = "statement_create_status")
    private Integer statementCreateStatus;

    /**
     * 账单确认状态 0-未处理 1-已处理 2-处理失败
     */
    @Column(name = "statement_confirm_status")
    private Integer statementConfirmStatus;

    /**
     * 工单处理状态 0-未处理 1-已处理 2-处理失败
     */
    @Column(name = "work_order_status")
    private Integer workOrderStatus;

    /**
     * 账单id
     */
    @Column(name = "statement_id")
    private Integer statementId;

    /**
     * 账单编码
     */
    @Column(name = "statement_code")
    private String statementCode;

    /**
     * 供应商编码
     */
    @Column(name = "supplier_code")
    private String supplierCode;

    /**
     * 失败原因
     */
    @Column(name = "fail_reason")
    private String failReason;

    /**
     * 任务状态 0-未开始 1-账单创建 2-账单确认 3-工单处理 4-已完成
     */
    @Column(name = "task_status")
    private Integer taskStatus;

    /**
     * 创建人
     */
    @Column(name = "created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @Column(name = "created_dt")
    private Date createdDt;

    /**
     * 修改人
     */
    @Column(name = "updated_by")
    private String updatedBy;

    /**
     * 修改时间
     */
    @Column(name = "updated_dt")
    private Date updatedDt;

}