package com.tiangong.finance.statement.service.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tiangong.common.Response;
import com.tiangong.dto.common.PaginationSupportDTO;
import com.tiangong.enums.*;
import com.tiangong.exception.SysException;
import com.tiangong.finance.enums.ExcelTitleEnum;
import com.tiangong.finance.remote.statement.request.ProtocolOrderSettlementCostVO;
import com.tiangong.finance.remote.statement.response.ProtocolOrderSettlementCostResponseDTO;
import com.tiangong.finance.statement.domain.ProtocolOrderSettlementCostPO;
import com.tiangong.finance.statement.domain.req.ProtocolOrderSettlementCostReq;
import com.tiangong.finance.statement.domain.resp.ProtocolOrderSettleInfoResp;
import com.tiangong.finance.statement.mapper.ProtocolOrderSettlementCostMapper;
import com.tiangong.finance.statement.service.ProtocolOrderSettlementCostService;
import com.tiangong.keys.RedisKey;
import com.tiangong.order.remote.SupplyOrderRemote;
import com.tiangong.order.remote.dto.OrderCodeInfoDTO;
import com.tiangong.order.remote.request.SupplyOrderIdDTO;
import com.tiangong.organization.remote.dto.AgentAccountConfig;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 协议订单结算成本
 */
@Slf4j
@Service
public class ProtocolOrderSettlementCostServiceImpl extends ServiceImpl<ProtocolOrderSettlementCostMapper, ProtocolOrderSettlementCostPO> implements ProtocolOrderSettlementCostService {

    @Autowired
    private ProtocolOrderSettlementCostMapper protocolOrderSettlementCostMapper;

    @Autowired
    private SupplyOrderRemote supplyOrderRemote;

    @Autowired
    private SlsLoggerUtil slsLoggerUtil;

    @Override
    @Transactional
    public Response<Object> importProtocolOrderSettlementCost(MultipartFile file, String operator) {
        Response<Object> response = Response.success();
        // 解析文件
        InputStream in = null;
        // 返回结果
        Map<String, Integer> resultMap = new HashMap<>();
        resultMap.put("successCount", null);// 成功数量
        resultMap.put("fileCount", null);// 失败数量
        try {
            in = file.getInputStream();
            Workbook wb = WorkbookFactory.create(in);
            Sheet sheet = wb.getSheetAt(0);
            int rowSize = sheet.getPhysicalNumberOfRows();
            if (rowSize > 5001) {
                throw new SysException(ErrorCodeEnum.IMPORT_MAX_5000);
            }
            // 校验标题是否符合规范
            Row titleRow = sheet.getRow(0);
            for (int k = 0; k < 8; k++) {
                Cell cell = titleRow.getCell(k);
                DataFormatter formatter = new DataFormatter();
                String headName = formatter.formatCellValue(cell);
                if (ExcelTitleEnum.getKeyByValue(headName) != k) {
                    log.error("导入协议订单结算成本失败，k={}，headName={}", k, headName);
                    throw new SysException(ErrorCodeEnum.IMPORT_FILE_FORMAT_ERROR);
                }
            }

            // 解析excel内容
            List<ProtocolOrderSettlementCostPO> poList = analyzeExcel(wb, operator);
            // 保存到数据库
            if (CollUtilX.isNotEmpty(poList)) {
                this.saveBatch(poList);
            }
            resultMap.put("successCount", poList.size());// 成功数量
            resultMap.put("fileCount", (rowSize - 1) - poList.size());// 失败数量
            response.setModel(resultMap);

            // 匹配协议订单数据
            if (CollUtilX.isNotEmpty(poList)) {
                matchingProtocolOrder(poList, operator);
            }
        } catch (SysException e) {
            log.error("导入协议订单结算成本失败！", e);
            response = Response.error(e.getCode(), e.getMessage(), resultMap);
        } catch (Exception e) {
            log.error("导入协议订单结算成本，系统异常！", e);
            response = Response.error(ErrorCodeEnum.SYSTEM_EXCEPTION.errorCode, ErrorCodeEnum.SYSTEM_EXCEPTION.errorDesc, resultMap);
        } finally {
            if (in != null) {
                try {
                    in.close();
                } catch (IOException e) {
                    log.error("导入协议订单结算成本，系统异常！", e);
                }
            }
        }
        return response;
    }

    /**
     * 匹配协议订单数据
     */
    private void matchingProtocolOrder(List<ProtocolOrderSettlementCostPO> poList, String operator) {
        Date currentDate = DateUtilX.getCurrentDate();
        Set<String> codes = poList.stream().map(ProtocolOrderSettlementCostPO::getSupplyOrderCode).collect(Collectors.toSet());
        try {
            // 根据供货单编码查询订单编码、客户单号
            SupplyOrderIdDTO supplyOrderIdDTO = new SupplyOrderIdDTO();
            supplyOrderIdDTO.setSupplyOrderCodeList(codes);
            Response<List<OrderCodeInfoDTO>> response = supplyOrderRemote.queryOrderCodeBySupplyOrderCode(supplyOrderIdDTO);
            if (response.isSuccess() && CollUtilX.isNotEmpty(response.getModel())) {
                // 修改协议订单数据
                Set<String> errorSupplyOrderList = updateProtocolOrderSettlementCost(response.getModel(), currentDate, operator);
                // 判断数据是否一致
                if (new BigDecimal(codes.size()).compareTo(new BigDecimal(response.getModel().size())) != 0 || CollUtilX.isNotEmpty(errorSupplyOrderList)) {
                    Set<String> supplyOrderCodeList = response.getModel().stream().map(OrderCodeInfoDTO::getSupplyOrderCode).collect(Collectors.toSet());
                    List<ProtocolOrderSettlementCostPO> collect = new ArrayList<>();
                    for (String supplyOrderCode : codes) {
                        String failDesc = "匹配不到对应供货单";
                        if (errorSupplyOrderList.contains(supplyOrderCode)) {
                            failDesc = "匹配不到对应客户单号";
                        } else if (!supplyOrderCodeList.contains(supplyOrderCode)) {
                            continue;
                        }
                        ProtocolOrderSettlementCostPO po = new ProtocolOrderSettlementCostPO();
                        po.setSupplyOrderCode(supplyOrderCode);
                        po.setMatchingStatus(2);// 匹配失败
                        po.setFailDesc(failDesc);
                        po.setUpdatedBy(operator);
                        po.setUpdatedDt(currentDate);
                        collect.add(po);
                    }
                    if (CollUtilX.isNotEmpty(collect)) {
                        protocolOrderSettlementCostMapper.batchUpdateProtocolOrderSettlementCost(collect);
                    }
                }
            } else {
                if (response.isError()) {
                    log.error("匹配协议订单数据失败，根据供货单编码查询订单编码失败，req={}，resp={}", JSONUtil.toJsonStr(supplyOrderIdDTO), JSONUtil.toJsonStr(response));
                    // 修改协议订单数据
                    updateProtocolOrderSettlementCost(codes, currentDate, operator, "系统异常");
                } else {
                    // 修改协议订单数据
                    updateProtocolOrderSettlementCost(codes, currentDate, operator, "匹配不到对应供货单");
                }
            }
        } catch (Exception e) {
            log.error("匹配协议订单数据异常", e);
            // 修改协议订单数据
            updateProtocolOrderSettlementCost(codes, currentDate, operator, "系统异常");
        }
    }

    /**
     * 修改协议订单数据
     */
    private Set<String> updateProtocolOrderSettlementCost(List<OrderCodeInfoDTO> dtoList, Date currentDate, String operator) {
        Set<String> errorSupplyOrderList = new HashSet<>();
        List<ProtocolOrderSettlementCostPO> collect = new ArrayList<>();
        for (OrderCodeInfoDTO dto : dtoList) {
            if (StrUtilX.isEmpty(dto.getChannelOrderCode())) {
                errorSupplyOrderList.add(dto.getSupplyOrderCode());
                continue;
            }
            ProtocolOrderSettlementCostPO po = new ProtocolOrderSettlementCostPO();
            po.setSupplyOrderCode(dto.getSupplyOrderCode());
            po.setOrderCode(dto.getOrderCode());
            po.setChannelOrderCode(dto.getChannelOrderCode());
            po.setAgentCode(dto.getAgentCode());
            po.setMatchingStatus(1);// 匹配成功
            po.setUpdatedBy(operator);
            po.setUpdatedDt(currentDate);
            collect.add(po);
        }
        if (CollUtilX.isNotEmpty(collect)) {
            protocolOrderSettlementCostMapper.batchUpdateProtocolOrderSettlementCost(collect);
        }
        return errorSupplyOrderList;
    }

    /**
     * 修改协议订单数据
     */
    private void updateProtocolOrderSettlementCost(Set<String> codes, Date currentDate, String operator, String failDesc) {
        List<ProtocolOrderSettlementCostPO> collect = codes.stream().map(item -> {
            ProtocolOrderSettlementCostPO po = new ProtocolOrderSettlementCostPO();
            po.setSupplyOrderCode(item);
            po.setMatchingStatus(2);// 匹配失败
            po.setFailDesc(failDesc);
            po.setUpdatedBy(operator);
            po.setUpdatedDt(currentDate);
            return po;
        }).collect(Collectors.toList());
        protocolOrderSettlementCostMapper.batchUpdateProtocolOrderSettlementCost(collect);
    }

    /**
     * 解析excel内容
     */
    public List<ProtocolOrderSettlementCostPO> analyzeExcel(Workbook wb, String operator) {
        List<ProtocolOrderSettlementCostPO> list = new ArrayList<>();
        Date currentDate = DateUtilX.getCurrentDate();
        Sheet sheet = wb.getSheetAt(0);
        int rowSize = sheet.getPhysicalNumberOfRows();

        // 供货单编码集合
        Set<String> supplyOrderCodes = new HashSet<>();
        // 旧数据
        Set<String> oldCodes = new HashSet<>();
        for (int j = 1; j < rowSize; j++) {// 遍历每一行
            ProtocolOrderSettlementCostPO po = new ProtocolOrderSettlementCostPO();
            Row row = sheet.getRow(j);
            if (row == null) {
                continue;
            }
            boolean flag = false;
            for (int k = 0; k < 8; k++) {
                Cell cell = null;
                try {
                    cell = row.getCell(k);
                    analyzeExcelDetail(po, k, cell);
                } catch (Exception e) {
                    log.error("批量导单，解析excel第{}行，【{}】列出现异常，cell={}", j, k, JSONUtil.toJsonStr(cell), e);
                    flag = true;
                }
            }
            // 判断是否异常
            if (flag) {
                continue;
            }
            if (po.getSwipingCardRate() != null) {
                po.setSwipingCardRate(po.getSwipingCardRate().setScale(8, RoundingMode.UP));
            }
            po.setMatchingStatus(0);
            po.setPushStatus(0);
            po.setImportBy(operator);
            po.setImportDt(currentDate);
            po.setCreatedBy(operator);
            po.setCreatedDt(currentDate);
            // 重复数据不添加
            if (!oldCodes.contains(po.getSupplyOrderCode() + "_" + po.getPaySerialNo())) {
                list.add(po);

                // 记录供货单
                supplyOrderCodes.add(po.getSupplyOrderCode());
                // 记录旧数据
                oldCodes.add(po.getSupplyOrderCode() + "_" + po.getPaySerialNo());
            } else {
                log.error("解析excel内容，结算成本数据重复1：{}", po.getSupplyOrderCode() + "_" + po.getPaySerialNo());
            }
        }
        // 不为空，验重
        if (CollUtilX.isNotEmpty(list)) {
            List<ProtocolOrderSettlementCostPO> poList = protocolOrderSettlementCostMapper.selectList(new LambdaQueryWrapper<ProtocolOrderSettlementCostPO>()
                    .in(ProtocolOrderSettlementCostPO::getSupplyOrderCode, supplyOrderCodes).eq(ProtocolOrderSettlementCostPO::getDeleted, 0));
            if (CollUtilX.isNotEmpty(poList)) {
                Set<String> collect = poList.stream().map(item -> item.getSupplyOrderCode() + "_" + item.getPaySerialNo()).collect(Collectors.toSet());
                List<ProtocolOrderSettlementCostPO> newList = new ArrayList<>();
                for (ProtocolOrderSettlementCostPO protocolOrderSettlementCostPO : list) {
                    if (collect.contains(protocolOrderSettlementCostPO.getSupplyOrderCode() + "_" + protocolOrderSettlementCostPO.getPaySerialNo())) {
                        log.error("解析excel内容，结算成本数据重复2：{}", protocolOrderSettlementCostPO.getSupplyOrderCode() + "_" + protocolOrderSettlementCostPO.getPaySerialNo());
                        continue;
                    }
                    newList.add(protocolOrderSettlementCostPO);
                }
                list = newList;
            }
        }
        return list;
    }

    /**
     * 解析excel详情
     */
    public void analyzeExcelDetail(ProtocolOrderSettlementCostPO po, int k, Cell cell) {
        switch (k) {
            case 0:
                if (cell != null) {
                    DataFormatter formatter = new DataFormatter();
                    po.setSupplyOrderCode(formatter.formatCellValue(cell).trim());
                }
                if (StrUtilX.isEmpty(po.getSupplyOrderCode())) {
                    throw new SysException(ParamErrorEnum.EMPTY_PARAM_SUPPLYORDERCODE);
                }
                break;
            case 1:
                if (cell != null) {
                    DataFormatter formatter = new DataFormatter();
                    po.setPaySerialNo(formatter.formatCellValue(cell).trim());
                }
                if (StrUtilX.isEmpty(po.getPaySerialNo())) {
                    throw new SysException(ParamErrorEnum.EMPTY_PARAM_PAYSERIALNO);
                }
                break;
            case 2:
                if (cell != null) {
                    DataFormatter formatter = new DataFormatter();
                    String value = formatter.formatCellValue(cell).trim();
                    if (StrUtilX.isNotEmpty(value)) {
                        String code = SettlementCurrencyEnum.getKeyByCode(value);
                        if (StrUtilX.isNotEmpty(code)) {
                            po.setSwipingCardAmountCurrency(Integer.parseInt(code));
                        }
                    }
                }
                if (po.getSwipingCardAmountCurrency() == null) {
                    throw new SysException(ParamErrorEnum.EMPTY_PARAM_SWIPINGCARDAMOUNTCURRENCY);
                }
                break;
            case 3:
                if (cell != null) {
                    DataFormatter formatter = new DataFormatter();
                    String value = formatter.formatCellValue(cell).trim();
                    if (StrUtilX.isNotEmpty(value)) {
                        po.setSwipingCardAmount(new BigDecimal(value));
                    }
                }
                if (po.getSwipingCardAmount() == null) {
                    throw new SysException(ParamErrorEnum.EMPTY_PARAM_SWIPINGCARDAMOUNT);
                }
                break;
            case 4:
                if (cell != null) {
                    DataFormatter formatter = new DataFormatter();
                    String value = formatter.formatCellValue(cell).trim();
                    if (StrUtilX.isNotEmpty(value)) {
                        po.setSwipingCardRate(new BigDecimal(value));
                    }
                }
                if (po.getSwipingCardRate() == null) {
                    throw new SysException(ParamErrorEnum.EMPTY_PARAM_SWIPINGCARDRATE);
                }
                break;
            case 5:
                if (cell != null) {
                    DataFormatter formatter = new DataFormatter();
                    String value = formatter.formatCellValue(cell).trim();
                    if (StrUtilX.isNotEmpty(value)) {
                        String code = SettlementCurrencyEnum.getKeyByCode(value);
                        if (StrUtilX.isNotEmpty(code)) {
                            po.setSettleAmountCurrency(Integer.parseInt(code));
                        }
                    }
                }
                if (po.getSettleAmountCurrency() == null) {
                    throw new SysException(ParamErrorEnum.EMPTY_PARAM_SETTLEAMOUNTCURRENCY);
                }
                break;
            case 6:
                if (cell != null) {
                    DataFormatter formatter = new DataFormatter();
                    String value = formatter.formatCellValue(cell).trim();
                    if (StrUtilX.isNotEmpty(value)) {
                        po.setSettleAmount(new BigDecimal(value));
                    }
                }
                if (po.getSettleAmount() == null) {
                    throw new SysException(ParamErrorEnum.EMPTY_PARAM_SETTLEAMOUNT);
                }
                break;
            case 7:
                if (cell != null) {
                    DataFormatter formatter = new DataFormatter();
                    String value = formatter.formatCellValue(cell).trim();
                    if (StrUtilX.isNotEmpty(value)) {
                        po.setSwipingCardDt(DateUtilX.stringToDate(value, DateUtilX.appleFormat));
                    }
                }
                break;
        }
    }

    @Override
    public PaginationSupportDTO<ProtocolOrderSettleInfoResp> queryProtocolOrderSettlementCostPage(ProtocolOrderSettlementCostReq req) {
        IPage<ProtocolOrderSettlementCostPO> ipage = new Page<>(req.getCurrentPage(), req.getPageSize());
        IPage<ProtocolOrderSettlementCostPO> iPage = protocolOrderSettlementCostMapper.selectProtocolOrderSettlementCostPage(ipage, req);
        List<ProtocolOrderSettleInfoResp> respList = null;
        if (CollUtilX.isNotEmpty(iPage.getRecords())) {
            respList = iPage.getRecords().stream().map(item -> {
                ProtocolOrderSettleInfoResp resp = new ProtocolOrderSettleInfoResp();
                resp.setId(item.getId());
                resp.setPaySerialNo(item.getPaySerialNo());
                if (item.getSwipingCardAmountCurrency() != null) {
                    resp.setSwipingCardAmountCurrency(SettlementCurrencyEnum.getCodeByKey(String.valueOf(item.getSwipingCardAmountCurrency())));
                }
                resp.setSwipingCardAmount(item.getSwipingCardAmount());
                resp.setSwipingCardRate(item.getSwipingCardRate());
                if (item.getSwipingCardDt() != null) {
                    resp.setSwipingCardDt(DateUtilX.dateToString(item.getSwipingCardDt(), DateUtilX.hour_format));
                }
                if (item.getSettleAmountCurrency() != null) {
                    resp.setSettleAmountCurrency(SettlementCurrencyEnum.getCodeByKey(String.valueOf(item.getSettleAmountCurrency())));
                }
                resp.setSettleAmount(item.getSettleAmount());
                resp.setSupplyOrderCode(item.getSupplyOrderCode());
                resp.setOrderCode(item.getOrderCode());
                resp.setFailDesc(item.getFailDesc());
                resp.setChannelOrderCode(item.getChannelOrderCode());
                resp.setImportBy(item.getImportBy());
                if (item.getImportDt() != null) {
                    resp.setImportDt(DateUtilX.dateToString(item.getImportDt(), DateUtilX.hour_format));
                }
                resp.setMatchingStatus(item.getMatchingStatus());
                resp.setPushStatus(item.getPushStatus());
                return resp;
            }).collect(Collectors.toList());
        }
        PaginationSupportDTO<ProtocolOrderSettleInfoResp> paginationSupportDTO = new PaginationSupportDTO<>();
        paginationSupportDTO.setItemList(respList);
        paginationSupportDTO.setCurrentPage((int) iPage.getCurrent());
        paginationSupportDTO.setPageSize((int) iPage.getSize());
        paginationSupportDTO.setTotalCount(iPage.getTotal());
        paginationSupportDTO.setTotalPage((int) iPage.getPages());
        return paginationSupportDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delProtocolOrderSettlementCost(ProtocolOrderSettlementCostReq request) {
        if (CollUtilX.isEmpty(request.getIds())) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_ID);
        }
        Date currentDate = DateUtilX.getCurrentDate();
        String operator = request.getOperator();
        List<ProtocolOrderSettlementCostPO> collect = request.getIds().stream().map(item -> {
            ProtocolOrderSettlementCostPO po = new ProtocolOrderSettlementCostPO();
            po.setId(item);
            po.setDeleted(1);
            po.setUpdatedBy(operator);
            po.setUpdatedDt(currentDate);
            return po;
        }).collect(Collectors.toList());
        this.updateBatchById(collect);
    }

    @Override
    public void retryMatchingProtocolOrder(ProtocolOrderSettlementCostReq request) {
        if (StrUtilX.isEmpty(request.getSupplyOrderCode())) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_SUPPLYORDERCODE);
        }
        Date currentDate = DateUtilX.getCurrentDate();
        // 根据供货单编码查询订单编码、客户单号
        Set<String> codes = new HashSet<>();
        codes.add(request.getSupplyOrderCode());
        SupplyOrderIdDTO supplyOrderIdDTO = new SupplyOrderIdDTO();
        supplyOrderIdDTO.setSupplyOrderCodeList(codes);
        Response<List<OrderCodeInfoDTO>> response = supplyOrderRemote.queryOrderCodeBySupplyOrderCode(supplyOrderIdDTO);
        if (response.isSuccess() && CollUtilX.isNotEmpty(response.getModel())) {
            // 修改协议订单数据
            Set<String> errorSupplyOrderList = updateProtocolOrderSettlementCost(response.getModel(), DateUtilX.getCurrentDate(), request.getOperator());
            if (CollUtilX.isNotEmpty(errorSupplyOrderList)) {
                // 修改协议订单数据
                updateProtocolOrderSettlementCost(codes, currentDate, request.getOperator(), "匹配不到对应客户单号");
            }
        } else {
            if (response.isError()) {
                // 修改协议订单数据
                updateProtocolOrderSettlementCost(codes, currentDate, request.getOperator(), "系统异常");
                log.error("匹配协议订单数据失败，根据供货单编码查询订单编码失败，req={}，resp={}", JSONUtil.toJsonStr(supplyOrderIdDTO), JSONUtil.toJsonStr(response));
            } else {
                // 修改协议订单数据
                updateProtocolOrderSettlementCost(codes, currentDate, request.getOperator(), "匹配不到对应供货单");
            }
        }
    }

    @Override
    public List<ProtocolOrderSettlementCostResponseDTO> queryProtocolOrderSettlementCostList(ProtocolOrderSettlementCostVO request) {
        if (StrUtilX.isEmpty(request.getOrderCode())) {
            throw new SysException(ParamErrorEnum.EMPTY_PARAM_ORDERCODE);
        }
        List<ProtocolOrderSettlementCostPO> poList = protocolOrderSettlementCostMapper.selectList(new LambdaQueryWrapper<ProtocolOrderSettlementCostPO>()
                .eq(ProtocolOrderSettlementCostPO::getOrderCode, request.getOrderCode()).eq(ProtocolOrderSettlementCostPO::getDeleted, 0).orderByDesc(ProtocolOrderSettlementCostPO::getSwipingCardDt));
        if (CollUtilX.isNotEmpty(poList)) {
             return poList.stream().map(item -> {
                 ProtocolOrderSettlementCostResponseDTO resp = new ProtocolOrderSettlementCostResponseDTO();
                 resp.setPaySerialNo(item.getPaySerialNo());
                 if (item.getSwipingCardAmountCurrency() != null) {
                     resp.setSwipingCardAmountCurrency(SettlementCurrencyEnum.getCodeByKey(String.valueOf(item.getSwipingCardAmountCurrency())));
                 }
                 resp.setSwipingCardAmount(item.getSwipingCardAmount());
                 resp.setSwipingCardRate(item.getSwipingCardRate());
                 if (item.getSwipingCardDt() != null) {
                     resp.setSwipingCardDt(DateUtilX.dateToString(item.getSwipingCardDt(), DateUtilX.hour_format));
                 }
                 if (item.getSettleAmountCurrency() != null) {
                     resp.setSettleAmountCurrency(SettlementCurrencyEnum.getCodeByKey(String.valueOf(item.getSettleAmountCurrency())));
                 }
                 resp.setSettleAmount(item.getSettleAmount());
                 resp.setSupplyOrderCode(item.getSupplyOrderCode());
                 resp.setOrderCode(item.getOrderCode());
                 resp.setChannelOrderCode(item.getChannelOrderCode());
                 resp.setImportBy(item.getImportBy());
                 if (item.getImportDt() != null) {
                     resp.setImportDt(DateUtilX.dateToString(item.getImportDt(), DateUtilX.hour_format));
                 }
                 resp.setMatchingStatus(item.getMatchingStatus());
                 return resp;
            }).collect(Collectors.toList());
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void pushProtocolOrderSettlementCostInfoTask(List<Integer> ids) {
        // 错误描述
        String errorMsg = null;
        // 开始时间
        Date start = new Date();
        // 参数信息
        Map<String, String> param = new HashMap<>();
        boolean logFlag = false;// 记录日志标识
        try {
            if (CollUtilX.isNotEmpty(ids)) {
                logFlag = true;
                List<ProtocolOrderSettlementCostPO> poList = protocolOrderSettlementCostMapper.selectBatchIds(ids);
                if (CollUtilX.isNotEmpty(poList)) {
                    Map<String, List<ProtocolOrderSettlementCostPO>> protocolOrderSettlementCostMap = poList.stream().filter(item -> StrUtilX.isNotEmpty(item.getAgentCode())).collect(Collectors.groupingBy(ProtocolOrderSettlementCostPO::getAgentCode));
                    param.put("agentCode", JSONUtil.toJsonStr(protocolOrderSettlementCostMap.keySet()));
                    for (Map.Entry<String, List<ProtocolOrderSettlementCostPO>> entry : protocolOrderSettlementCostMap.entrySet()) {
                        if (StrUtilX.isNotEmpty(entry.getKey())) {
                            AgentAccountConfig agentAccountConfig = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.AGENT_ACCOUNT_CONFIG, entry.getKey()), AgentAccountConfig.class);
                            if (agentAccountConfig == null || StrUtilX.isEmpty(agentAccountConfig.getPushSettleCostUrl())) {
                                continue;
                            }
                            // 推送协议订单结算成本信息
                            pushSettleCostInfo(entry.getValue(), agentAccountConfig, param);
                        }
                    }
                }
            } else {
                if (RedisTemplateX.hasKey(RedisKey.PUSH_PROTOCOL_ORDER_SETTLEMENT_COST_AGENT_CODE)) {
                    String agentCode = RedisTemplateX.sPop(RedisKey.PUSH_PROTOCOL_ORDER_SETTLEMENT_COST_AGENT_CODE);
                    if (StrUtilX.isNotEmpty(agentCode)) {
                        logFlag = true;
                        AgentAccountConfig agentAccountConfig = StrUtilX.parseObject(RedisTemplateX.hashGet(RedisKey.AGENT_ACCOUNT_CONFIG, agentCode), AgentAccountConfig.class);
                        if (agentAccountConfig == null) {
                            throw new SysException(ErrorCodeEnum.AGENT_INFO_NOT_EXIST);
                        }
                        param.put("agentCode", agentCode);
                        if (StrUtilX.isEmpty(agentAccountConfig.getPushSettleCostUrl())) {
                            return;
                        }
                        // 需要推送的状态
                        List<Integer> pushStatus = new ArrayList<>();
                        pushStatus.add(0);
                        pushStatus.add(2);
                        // 查询需要推送的数据
                        List<ProtocolOrderSettlementCostPO> poList = protocolOrderSettlementCostMapper.selectList(new LambdaQueryWrapper<ProtocolOrderSettlementCostPO>()
                                .eq(ProtocolOrderSettlementCostPO::getDeleted, 0)
                                .eq(ProtocolOrderSettlementCostPO::getMatchingStatus, 1)
                                .in(ProtocolOrderSettlementCostPO::getPushStatus, pushStatus)
                                .eq(ProtocolOrderSettlementCostPO::getAgentCode, agentCode)
                                .apply("DATE_ADD(created_dt, INTERVAL 48 HOUR) > NOW()"));
                        if (CollUtilX.isNotEmpty(poList)) {
                            // 推送协议订单结算成本信息
                            pushSettleCostInfo(poList, agentAccountConfig, param);
                        }
                    }
                }
            }
        } catch (Exception e) {
            // 记录其他异常
            errorMsg = e.getMessage();
            log.error("推送协议订单结算成本信息任务异常", e);
        } finally {
            if (logFlag) {
                Map<String, String> map = new HashMap<>();
                map.put(SlsEnum.LEVEL.getType(), SlsLogLevelEnum.INFO.getLevel());
                map.put(SlsEnum.NAME.getType(), "推送协议订单结算成本信息任务");
                map.put(SlsEnum.MESSAGE.getType(), errorMsg == null ? "none" : "异常信息：" + errorMsg);
                map.put("agentCode", param.get("agentCode"));
                map.put("request", param.get("request"));
                map.put("response", param.get("result"));
                map.put("url", param.get("url"));
                map.put("start", DateUtilX.dateToString(start, DateUtilX.hour_ms_format));
                slsLoggerUtil.saveLog(map, "推送协议订单结算成本信息任务", "tiangong-finance-server");
            }
        }
    }

    /**
     * 推送协议订单结算成本信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void pushSettleCostInfo(List<ProtocolOrderSettlementCostPO> poList, AgentAccountConfig agentAccountConfig, Map<String, String> param) {
        Date currentDate = DateUtilX.getCurrentDate();
        List<ProtocolOrderSettlementCostVO> voList = new ArrayList<>();
        List<ProtocolOrderSettlementCostPO> updatePoList = new ArrayList<>();
        for (ProtocolOrderSettlementCostPO po : poList) {
            ProtocolOrderSettlementCostVO vo = new ProtocolOrderSettlementCostVO();
            vo.setPaySerialNo(po.getPaySerialNo());
            vo.setSupplyOrderCode(po.getChannelOrderCode());// 使用客户单号作为供货单号推送
            vo.setSwipingCardAmountCurrency(po.getSwipingCardAmountCurrency());
            vo.setSwipingCardAmount(po.getSwipingCardAmount());
            vo.setSettleAmountCurrency(po.getSettleAmountCurrency());
            vo.setSettleAmount(po.getSettleAmount());
            if (po.getSwipingCardDt() != null) {
                vo.setSwipingCardDt(DateUtilX.dateToString(po.getSwipingCardDt(), DateUtilX.hour_format));
            }
            vo.setSwipingCardRate(po.getSwipingCardRate());
            voList.add(vo);

            ProtocolOrderSettlementCostPO costPO = new ProtocolOrderSettlementCostPO();
            costPO.setId(po.getId());
            costPO.setUpdatedDt(currentDate);
            costPO.setUpdatedBy("system");
            costPO.setPushStatus(1);
            updatePoList.add(costPO);
        }
        String request = JSONUtil.toJsonStr(voList);
        param.put("request", request);
        String url = agentAccountConfig.getPushSettleCostUrl();
        param.put("url", url);
        String result = HttpUtilX.post(url, request);
        param.put("result", result);
        if (StrUtilX.isEmpty(result)) {
            log.error("推送协议订单结算成本信息失败，返回数据为空，req={}，resp={}", request, result);
        } else {
            Response<Object> response = JSONObject.parseObject(result, new TypeReference<Response<Object>>() {
            });
            if (response.isError()) {
                log.error("推送协议订单结算成本信息失败，返回结果失败，req={}，resp={}", request, result);
                List<ProtocolOrderSettlementCostPO> collect = updatePoList.stream().peek(item -> item.setPushStatus(2)).collect(Collectors.toList());
                // 修改推送状态
                this.updateBatchById(collect);
            } else {
                // 修改推送状态
                this.updateBatchById(updatePoList);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void receiveProtocolOrderSettlementCost(List<ProtocolOrderSettlementCostVO> voList) {
        if (CollUtilX.isEmpty(voList)) {
            return;
        }
        Set<String> supplyOrderCodes = new HashSet<>();
        Date currentDate = DateUtilX.getCurrentDate();
        List<ProtocolOrderSettlementCostPO> poList = new ArrayList<>();
        // 校验参数
        for (ProtocolOrderSettlementCostVO vo : voList) {
            if (StrUtilX.isEmpty(vo.getPaySerialNo())) {
                throw new SysException(ParamErrorEnum.EMPTY_PARAM_PAYSERIALNO);
            }
            if (vo.getSwipingCardAmountCurrency() == null) {
                throw new SysException(ParamErrorEnum.EMPTY_PARAM_SWIPINGCARDAMOUNTCURRENCY);
            }
            if (vo.getSwipingCardAmount() == null) {
                throw new SysException(ParamErrorEnum.EMPTY_PARAM_SWIPINGCARDAMOUNT);
            }
            if (vo.getSwipingCardRate() == null) {
                throw new SysException(ParamErrorEnum.EMPTY_PARAM_SWIPINGCARDRATE);
            }
            if (vo.getSettleAmountCurrency() == null) {
                throw new SysException(ParamErrorEnum.EMPTY_PARAM_SETTLEAMOUNTCURRENCY);
            }
            if (vo.getSettleAmount() == null) {
                throw new SysException(ParamErrorEnum.EMPTY_PARAM_SETTLEAMOUNT);
            }
            if (StrUtilX.isEmpty(vo.getSupplyOrderCode())) {
                throw new SysException(ParamErrorEnum.EMPTY_PARAM_SUPPLYORDERCODE);
            }
            supplyOrderCodes.add(vo.getSupplyOrderCode());

            ProtocolOrderSettlementCostPO po = new ProtocolOrderSettlementCostPO();
            po.setPaySerialNo(vo.getPaySerialNo());
            po.setSupplyOrderCode(vo.getSupplyOrderCode());
            po.setSwipingCardAmount(vo.getSwipingCardAmount());
            po.setSwipingCardAmountCurrency(vo.getSwipingCardAmountCurrency());
            po.setSettleAmount(vo.getSettleAmount());
            po.setSettleAmountCurrency(vo.getSettleAmountCurrency());
            po.setSwipingCardRate(vo.getSwipingCardRate());
            if (StrUtilX.isNotEmpty(vo.getSwipingCardDt())) {
                po.setSwipingCardDt(DateUtilX.stringToDate(vo.getSwipingCardDt(), DateUtilX.hour_format));
            }
            po.setMatchingStatus(0);
            po.setPushStatus(0);
            po.setImportBy("system");
            po.setImportDt(currentDate);
            po.setCreatedBy("system");
            po.setCreatedDt(currentDate);
            poList.add(po);
        }

        // 验重
        List<ProtocolOrderSettlementCostPO> oldPoList = protocolOrderSettlementCostMapper.selectList(new LambdaQueryWrapper<ProtocolOrderSettlementCostPO>()
                .in(ProtocolOrderSettlementCostPO::getSupplyOrderCode, supplyOrderCodes).eq(ProtocolOrderSettlementCostPO::getDeleted, 0));
        if (CollUtilX.isNotEmpty(oldPoList)) {
            Set<String> collect = oldPoList.stream().map(item -> item.getSupplyOrderCode() + "_" + item.getPaySerialNo()).collect(Collectors.toSet());
            for (ProtocolOrderSettlementCostVO protocolOrderSettlementCostPO : voList) {
                if (collect.contains(protocolOrderSettlementCostPO.getSupplyOrderCode() + "_" + protocolOrderSettlementCostPO.getPaySerialNo())) {
                    log.error("解析excel内容，结算成本数据重复：{}", protocolOrderSettlementCostPO.getSupplyOrderCode() + "_" + protocolOrderSettlementCostPO.getPaySerialNo());
                    throw new SysException(ErrorCodeEnum.FAIL.errorCode, "结算成本数据重复：" + protocolOrderSettlementCostPO.getSupplyOrderCode() + "_" + protocolOrderSettlementCostPO.getPaySerialNo());
                }
            }
        }

        // 保存到数据库
        this.saveBatch(poList);

        // 匹配协议订单数据
        matchingProtocolOrder(poList, "system");
    }
}