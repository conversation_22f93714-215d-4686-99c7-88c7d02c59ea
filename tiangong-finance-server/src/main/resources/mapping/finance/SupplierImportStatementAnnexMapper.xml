<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.tiangong.finance.statement.mapper.SupplierImportStatementAnnexMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap id="BaseResultMap"
               type="com.tiangong.finance.statement.domain.entity.SupplierImportStatementAnnexEntity">
        <result property="id" column="id"/>
        <result property="supplierOrderCode" column="supplier_order_code"/>
        <result property="supplyOrderCode" column="supply_order_code"/>
        <result property="receivableAmt" column="receivable_amt"/>
        <result property="currency" column="currency"/>
        <result property="supplierImportStatementId" column="supplier_import_statement_id"/>
        <result property="deleted" column="deleted"/>
        <result property="createdBy" column="created_by"/>
        <result property="createdDt" column="created_dt"/>
        <result property="updatedBy" column="updated_by"/>
        <result property="updatedDt" column="updated_dt"/>
    </resultMap>


</mapper>