<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tiangong.finance.statement.mapper.NonVccAutoBillConfigMapper">

    <select id="nonVccAutoBillConfigPage"
            resultType="com.tiangong.finance.statement.domain.resp.NonVccAutoBillConfigPageResp">
        SELECT
        id,
        supplier_code,
        supplier_name,
        hotel_id,
        hotel_name,
        billing_rule,
        billing_date_type,
        billing_date_days,
        TIME_FORMAT(hotel_local_start_time, '%H:%i:%s') as hotelLocalStartTime,
        TIME_FORMAT(hotel_local_end_time, '%H:%i:%s') as hotelLocalEndTime,
        TIME_FORMAT(system_start_time, '%H:%i:%s') as systemStartTime,
        TIME_FORMAT(system_end_time, '%H:%i:%s') as systemEndTime,
        created_by,
        DATE_FORMAT(created_dt, '%Y-%m-%d %H:%i:%s') as created_dt
        FROM f_non_vcc_auto_bill_config
        WHERE 1=1
        <if test="supplierCode != null and supplierCode != ''">
            AND supplier_code = #{supplierCode}
        </if>
        <if test="hotelId != null">
            AND hotel_id = #{hotelId}
        </if>
        ORDER BY id DESC
    </select>

    <!--校验供应商编码是否存在-->
    <select id="checkSupplierCodeExists" resultType="java.lang.Integer">
        SELECT count(*)
        FROM t_org_organization
        WHERE type = 0
          AND org_code = #{orgCode}
    </select>

    <select id="queryAllSupplierSupplyOrderCurrency" resultType="java.lang.Integer">
        SELECT DISTINCT base_currency
        FROM o_supply_order
        WHERE supplier_code = #{supplierCode}
          AND hotel_id = #{hotelId}
        <choose>
            <when test="dateQueryType==1">
                <if test="startDate!=null and startDate!=''">
                    AND start_date >= #{startDate}
                </if>
                <if test="endDate!=null and endDate!=''">
                    AND start_date &lt;= #{endDate}
                </if>
            </when>
            <when test="dateQueryType==2">
                <if test="startDate!=null and startDate!=''">
                    AND end_date >= #{startDate}
                </if>
                <if test="endDate!=null and endDate!=''">
                    AND end_date &lt;= #{endDate}
                </if>
            </when>
        </choose>
    </select>

    <select id="queryAutoBillConfigsPage" resultType="com.tiangong.finance.dto.QueryAutoBillConfigsPageTaskDTO">
        SELECT id,
               supplier_code,
               supplier_name,
               hotel_id,
               hotel_name,
               hotel_local_timezone,
               payment_method,
               billing_rule,
               billing_date_type,
               billing_date_days,
               TIME_FORMAT(hotel_local_start_time, '%H:%i:%s') as hotelLocalStartTime,
               TIME_FORMAT(hotel_local_end_time, '%H:%i:%s')   as hotelLocalEndTime,
               TIME_FORMAT(system_start_time, '%H:%i:%s')      as systemStartTime,
               TIME_FORMAT(system_end_time, '%H:%i:%s')        as systemEndTime
        FROM f_non_vcc_auto_bill_config
        WHERE billing_rule = #{billingRule}
          AND (
            (system_start_time &lt;= system_end_time AND #{currentTime} BETWEEN system_start_time AND system_end_time)
                OR
            (system_start_time &gt; system_end_time AND
             (#{currentTime} &gt;= system_start_time OR #{currentTime} &lt;= system_end_time))
            )
        ORDER BY id
            LIMIT #{offset}, #{limit}
    </select>

</mapper>