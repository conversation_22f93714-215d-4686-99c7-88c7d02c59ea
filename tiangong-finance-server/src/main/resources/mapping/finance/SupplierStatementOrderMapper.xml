<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tiangong.finance.statement.mapper.SupplierStatementOrderMapper">
  <resultMap id="BaseResultMap" type="com.tiangong.finance.statement.domain.SupplierStatementOrderPO">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="statement_id" jdbcType="INTEGER" property="statementId" />
    <result column="supply_order_id" jdbcType="INTEGER" property="supplyOrderId" />
    <result column="supply_order_code" jdbcType="VARCHAR" property="supplyOrderCode" />
    <result column="hotel_name" jdbcType="VARCHAR" property="hotelName" />
    <result column="room_name" jdbcType="VARCHAR" property="roomName" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="guest" jdbcType="VARCHAR" property="guest" />
    <result column="start_date" jdbcType="DATE" property="startDate" />
    <result column="end_date" jdbcType="DATE" property="endDate" />
    <result column="room_qty" jdbcType="INTEGER" property="roomQty" />
    <result column="currency" jdbcType="INTEGER" property="currency" />
    <result column="payable_amt" jdbcType="DECIMAL" property="payableAmt" />
    <result column="order_create_date" jdbcType="TIMESTAMP" property="orderCreateDate" />
    <result column="confirmation_status" jdbcType="INTEGER" property="confirmationStatus" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_dt" jdbcType="TIMESTAMP" property="createdDt" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
    <result column="updated_dt" jdbcType="TIMESTAMP" property="updatedDt" />
  </resultMap>

  <resultMap id="queryStatementSupplyOrderListBaseResultMap" type="com.tiangong.finance.remote.statement.response.StatementSupplyOrderDTO">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="statementOrderId" />
    <result column="supply_order_code" jdbcType="VARCHAR" property="supplyOrderCode" />
    <result column="hotel_name" jdbcType="VARCHAR" property="hotelName" />
    <result column="room_name" jdbcType="VARCHAR" property="roomName" />
    <result column="guest" jdbcType="VARCHAR" property="guest" />
    <result column="start_date" jdbcType="DATE" property="startDate" />
    <result column="end_date" jdbcType="DATE" property="endDate" />
    <result column="room_qty" jdbcType="INTEGER" property="roomQty" />
    <result column="currency" jdbcType="INTEGER" property="currency" />
    <result column="payable_amt" jdbcType="DECIMAL" property="payableAmt" />
    <result column="paid_amt" jdbcType="DECIMAL" property="paidAmt" />
    <result column="unpaid_amt" jdbcType="DECIMAL" property="unpaidAmt" />
    <result column="confirmation_status" jdbcType="INTEGER" property="confirmationStatus" />
    <result column="order_create_date" jdbcType="TIMESTAMP" property="createdDt" />
    <result column="supplier_order_code" property="supplierOrderCode" />
    <result column="confirmation_code" property="confirmationCode" />
    <result column="supplyOrderId" property="supplyOrderId" />
  </resultMap>

  <resultMap id="queryStatementSupplyOrderListBaseResultMapWithOrderCode" type="com.tiangong.finance.remote.statement.response.StatementSupplyOrderWithOrderCodeDTO">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="statementOrderId" />
    <result column="id" jdbcType="INTEGER" property="statementOrderId" />
    <result column="supply_order_code" jdbcType="VARCHAR" property="supplyOrderCode" />
    <result column="hotel_name" jdbcType="VARCHAR" property="hotelName" />
    <result column="room_name" jdbcType="VARCHAR" property="roomName" />
    <result column="guest" jdbcType="VARCHAR" property="guest" />
    <result column="room_numbers" jdbcType="VARCHAR" property="roomNumbers" />
    <result column="start_date" jdbcType="DATE" property="startDate" />
    <result column="end_date" jdbcType="DATE" property="endDate" />
    <result column="room_qty" jdbcType="INTEGER" property="roomQty" />
    <result column="currency" jdbcType="INTEGER" property="currency" />
    <result column="sale_rate" jdbcType="DECIMAL" property="saleRate" />
    <result column="payable_amt" jdbcType="DECIMAL" property="payableAmt" />
    <result column="paid_amt" jdbcType="DECIMAL" property="paidAmt" />
    <result column="unpaid_amt" jdbcType="DECIMAL" property="unpaidAmt" />
    <result column="pay_amt" jdbcType="DECIMAL" property="payAmt" />
    <result column="confirmation_status" jdbcType="INTEGER" property="confirmationStatus" />
    <result column="order_create_date" jdbcType="TIMESTAMP" property="createdDt" />
    <result column="supplier_order_code" property="supplierOrderCode" />
    <result column="confirmation_code" property="confirmationCode" />
    <result column="order_code" property="orderCode" />
    <result column="commission" jdbcType="DECIMAL" property="commission" />
    <result column="additional_charges" jdbcType="DECIMAL" property="additionalCharges" />
    <result column="refund_fee" jdbcType="DECIMAL" property="refundFee" />
    <result column="sum_base_price" jdbcType="DECIMAL" property="sumBasePrice" />

    <!-- 入住人信息 -->
    <collection property="guests" select="com.tiangong.finance.lock.mapper.FinanceLockMapper.selectOrderGuests"
                column="orderId" javaType="ArrayList" ofType="com.tiangong.finance.remote.lock.response.GuestDTO">
      <result column="name" property="name"/>
      <result column="roomNumber" property="roomNumber"/>
    </collection>
  </resultMap>

  <resultMap id="queryUnCheckOutSupplyOrderMap" type="com.tiangong.finance.remote.statement.response.UnCheckOutSupplyOrderDTO">
    <result column="supplyOrderId" property="supplyOrderId"/>
    <result column="supplyOrderCode" property="supplyOrderCode"/>
    <result column="hotelName" property="hotelName"/>
    <result column="roomName" property="roomName"/>
    <result column="guest" property="guest"/>
    <result column="roomNumbers" property="roomNumbers"/>
    <result column="startDate" property="startDate"/>
    <result column="endDate" property="endDate"/>
    <result column="roomQty" property="roomQty"/>
    <result column="currency" property="currency"/>
    <result column="unpaidAmt" property="unpaidAmt"/>
    <result column="payableAmt" property="payableAmt"/>
    <result column="paidAmt" property="paidAmt"/>
    <result column="saleRate" property="saleRate"/>
    <result column="createdDt" property="createdDt"/>
    <result column="confirmationStatus" property="confirmationStatus"/>

    <!-- 入住人信息 -->
    <collection property="guests" select="com.tiangong.finance.lock.mapper.FinanceLockMapper.selectOrderGuests"
                column="orderId" javaType="ArrayList" ofType="com.tiangong.finance.remote.lock.response.GuestDTO">
      <result column="name" property="name"/>
      <result column="roomNumber" property="roomNumber"/>
    </collection>
  </resultMap>

  <insert id="saveBatchStatementOrder" parameterType="com.tiangong.finance.statement.dto.InsertStatementSupplyOrderDTO">
    INSERT INTO f_supplier_statement_order (
    statement_id,
    supply_order_id,
    supply_order_code,
    hotel_name,
    room_name,
    product_name,
    guest,
    start_date,
    end_date,
    room_qty,
    currency,
    paid_amt,
    payable_amt,
    unpaid_amt,
    pay_amt,
    order_create_date,
    confirmation_status,
    created_by,
    created_dt
    ) SELECT
    #{statementId},
    so.id,
    so.supply_order_code,
    so.hotel_name,
    so.room_name,
    so.product_name,
    o.guest,
    so.start_date,
    so.end_date,
    so.room_qty,
    so.base_currency,
    sf.paid_amt,
    <if test="statementType == 0">
      so.supply_order_amt,
      so.supply_order_amt-sf.paid_amt,
      so.supply_order_amt-sf.paid_amt,
    </if>
    <if test="statementType == 1">
      so.reward_amt,
      so.reward_amt-sf.paid_amt,
      so.reward_amt-sf.paid_amt,
    </if>
    <if test="statementType == 2">
      so.rebate_amt,
      so.rebate_amt-sf.paid_amt,
      so.rebate_amt-sf.paid_amt,
    </if>
    o.created_dt,
    so.confirmation_status,
    #{operator},
    SYSDATE()
    FROM o_order o INNER JOIN o_supply_order so ON o.id=so.order_id
    INNER JOIN o_supply_order_finance sf ON so.id = sf.supply_order_id AND sf.finance_type = #{statementType}
    WHERE o.company_code=#{companyCode}
    AND so.settlement_type!=3
    AND so.confirmation_status IN (1,2,3)
    AND sf.check_status=1
    <if test="statementType == 0">
      AND so.supply_order_amt - sf.paid_amt!=0
    </if>
    <if test="statementType == 1">
      AND so.reward_amt - sf.paid_amt!=0
    </if>
    <if test="statementType == 2">
      AND so.rebate_amt - sf.paid_amt!=0
    </if>
--     AND o.order_confirmation_status IN (1,2,3)
    <choose>
      <when test="dateQueryType==0">
        <if test="startDate!=null and startDate!=''">
          AND o.created_dt >= #{startDate}
        </if>
        <if test="endDate!=null and endDate!=''">
          AND o.created_dt &lt; date_add(#{endDate},interval 1 day)
        </if>
      </when>
      <when test="dateQueryType==1">
        <if test="startDate!=null and startDate!=''">
          AND so.start_date >= #{startDate}
        </if>
        <if test="endDate!=null and endDate!=''">
          AND so.start_date &lt;= #{endDate}
        </if>
      </when>
      <when test="dateQueryType==2">
        <if test="startDate!=null and startDate!=''">
          AND so.end_date >= #{startDate}
        </if>
        <if test="endDate!=null and endDate!=''">
          AND so.end_date &lt;= #{endDate}
        </if>
      </when>
    </choose>
    <choose>
      <when test="supplierCode!=null and supplierCode!=''">
        AND so.supplier_code=#{supplierCode}
      </when>
      <otherwise>
        <if test="supplierName != null and supplierName != ''">
          AND so.supplier_name LIKE concat(concat('%',#{supplierName}),'%')
        </if>
      </otherwise>
    </choose>
    <if test="supplyOrderIdList != null and supplyOrderIdList.size > 0">
      AND so.id IN
      <foreach collection="supplyOrderIdList" item="item" open="(" separator="," close=")">#{item}</foreach>
    </if>
    ORDER BY o.created_dt
  </insert>

  <update id="updateStatementOrderList" parameterType="com.tiangong.finance.remote.statement.request.StatementIdDTO">
    UPDATE  f_supplier_statement_order sso INNER JOIN o_supply_order so ON sso.supply_order_code=so.supply_order_code
    INNER JOIN o_supply_order_finance sf ON so.id = sf.supply_order_id AND sf.finance_type = #{statementType}
    INNER JOIN o_order o ON o.id = so.order_id
    SET
    sso.hotel_name=so.hotel_name,
    sso.room_name=so.room_name,
    sso.product_name=so.product_name,
    sso.guest=o.guest,
    sso.start_date=so.start_date,
    sso.end_date=so.end_date,
    sso.room_qty=so.room_qty,
    sso.currency=so.base_currency,
    <if test="statementType == 0">
      sso.payable_amt = so.supply_order_amt,
      sso.paid_amt = sf.paid_amt,
      sso.unpaid_amt=so.supply_order_amt-sf.paid_amt,
      sso.pay_amt=so.supply_order_amt-sf.paid_amt,
    </if>
    <if test="statementType == 1">
      sso.payable_amt = so.reward_amt,
      sso.paid_amt = sf.paid_amt,
      sso.unpaid_amt=so.reward_amt-sf.paid_amt,
      sso.pay_amt=so.reward_amt-sf.paid_amt,
    </if>
    <if test="statementType == 2">
      sso.payable_amt = so.rebate_amt,
      sso.paid_amt = sf.paid_amt,
      sso.unpaid_amt=so.rebate_amt-sf.paid_amt,
      sso.pay_amt=so.rebate_amt-sf.paid_amt,
    </if>
    sso.confirmation_status=so.confirmation_status,
    sso.updated_by=#{operator},
    sso.updated_dt=SYSDATE()
    WHERE sso.statement_id=#{statementId}
    <if test="statementType == 0">
      AND sso.unpaid_amt!=so.supply_order_amt-sf.paid_amt
    </if>
    <if test="statementType == 1">
      AND sso.unpaid_amt!=so.reward_amt-sf.paid_amt
    </if>
    <if test="statementType == 2">
      AND sso.unpaid_amt!=so.rebate_amt-sf.paid_amt
    </if>
  </update>

  <select id="queryStatementOrderList" parameterType="com.tiangong.finance.remote.statement.request.QueryStatementSupplyOrderListDTO"
          resultMap="queryStatementSupplyOrderListBaseResultMap">
    SELECT
    so.*,
    o.supplier_order_code,
    o.confirmation_code,
    o.id as supplyOrderId
    FROM f_supplier_statement_order so
    LEFT JOIN o_supply_order o ON so.supply_order_code = o.supply_order_code
    WHERE so.statement_id=#{statementId}
    <if test="supplyOrderCode!=null and supplyOrderCode!=''">
      AND so.supply_order_code like concat(concat('%',#{supplyOrderCode}),'%')
    </if>
    <choose>
      <when test="dateQueryType==0">
        <if test="startDate!=null and startDate!=''">
          AND so.order_create_date >= #{startDate}
        </if>
        <if test="endDate!=null and endDate!=''">
          AND so.order_create_date &lt; date_add(#{endDate},interval 1 day)
        </if>
      </when>
      <when test="dateQueryType==1">
        <if test="startDate!=null and startDate!=''">
          AND so.start_date >= #{startDate}
        </if>
        <if test="endDate!=null and endDate!=''">
          AND so.start_date &lt;= #{endDate}
        </if>
      </when>
      <when test="dateQueryType==2">
        <if test="startDate!=null and startDate!=''">
          AND so.end_date >= #{startDate}
        </if>
        <if test="endDate!=null and endDate!=''">
          AND so.end_date &lt;= #{endDate}
        </if>
      </when>
    </choose>
    ORDER BY so.id DESC
  </select>

  <select id="queryStatementOrderListPage" parameterType="com.tiangong.finance.remote.statement.request.QueryStatementSupplyOrderListPageDTO"
          resultMap="queryStatementSupplyOrderListBaseResultMap">
    SELECT
    so.*,
    o.supplier_order_code,
    o.confirmation_code
    FROM f_supplier_statement_order so
    LEFT JOIN o_supply_order o ON so.supply_order_code = o.supply_order_code
    WHERE so.statement_id=#{statementId}
    <if test="supplyOrderCode!=null and supplyOrderCode!=''">
      AND so.supply_order_code like concat(concat('%',#{supplyOrderCode}),'%')
    </if>
    <choose>
      <when test="dateQueryType==0">
        <if test="startDate!=null and startDate!=''">
          AND so.order_create_date >= #{startDate}
        </if>
        <if test="endDate!=null and endDate!=''">
          AND so.order_create_date &lt; date_add(#{endDate},interval 1 day)
        </if>
      </when>
      <when test="dateQueryType==1">
        <if test="startDate!=null and startDate!=''">
          AND so.start_date >= #{startDate}
        </if>
        <if test="endDate!=null and endDate!=''">
          AND so.start_date &lt;= #{endDate}
        </if>
      </when>
      <when test="dateQueryType==2">
        <if test="startDate!=null and startDate!=''">
          AND so.end_date >= #{startDate}
        </if>
        <if test="endDate!=null and endDate!=''">
          AND so.end_date &lt;= #{endDate}
        </if>
      </when>
    </choose>
    ORDER BY so.id DESC
    limit #{currentPage}, #{pageSize}
  </select>

  <select id="queryStatementOrderWithOrderCodeList" parameterType="com.tiangong.finance.remote.statement.request.QueryStatementSupplyOrderListDTO"
          resultMap="queryStatementSupplyOrderListBaseResultMapWithOrderCode">
    SELECT
    so.id,
    so.supply_order_code,
    so.hotel_name,
    so.room_name,
    so.guest,
    o.room_numbers,
    so.start_date,
    so.end_date,
    so.room_qty,
    so.currency,
    IFNULL(so.payable_amt,0) payable_amt,
    IFNULL(so.paid_amt,0) paid_amt,
    IFNULL(so.unpaid_amt,0) unpaid_amt,
    IFNULL(so.pay_amt,0) pay_amt,
    so.confirmation_status,
    so.order_create_date,
    o.supplier_order_code,
    o.confirmation_code,
    oo.order_code,
    oo.id orderId,
    IFNULL(o.rate, 0) sale_rate,
    o.commission,
    o.additional_charges,
    o.refund_fee,
    ifnull(so.payable_amt, 0) - ifnull(o.additional_charges, 0) + ifnull(o.commission, 0) sum_base_price
    FROM f_supplier_statement_order so
    LEFT JOIN o_supply_order o ON so.supply_order_code = o.supply_order_code
    LEFT JOIN o_order oo ON o.order_id = oo.id
    WHERE so.statement_id=#{statementId}
    <if test="supplyOrderCode!=null and supplyOrderCode!=''">
      AND so.supply_order_code like concat(concat('%',#{supplyOrderCode}),'%')
    </if>
    <if test="currency != null ">
      AND o.base_currency = #{currency}
    </if>
    <choose>
      <when test="dateQueryType==0">
        <if test="startDate!=null and startDate!=''">
          AND so.order_create_date >= #{startDate}
        </if>
        <if test="endDate!=null and endDate!=''">
          AND so.order_create_date &lt; date_add(#{endDate},interval 1 day)
        </if>
      </when>
      <when test="dateQueryType==1">
        <if test="startDate!=null and startDate!=''">
          AND so.start_date >= #{startDate}
        </if>
        <if test="endDate!=null and endDate!=''">
          AND so.start_date &lt;= #{endDate}
        </if>
      </when>
      <when test="dateQueryType==2">
        <if test="startDate!=null and startDate!=''">
          AND so.end_date >= #{startDate}
        </if>
        <if test="endDate!=null and endDate!=''">
          AND so.end_date &lt;= #{endDate}
        </if>
      </when>
    </choose>
    ORDER BY so.id DESC
  </select>

  <update id="updateStatementAmount" parameterType="com.tiangong.finance.remote.statement.request.QueryStatementTotalAmountDTO">
    UPDATE
    f_supplier_statement s
    SET
    <if test="currency != null and currency != ''">
      s.currency=#{currency},
    </if>
    s.statement_amt=#{amount},
    <if test='amount=="0"'>
       s.unpaid_amt=0
    </if>
    <if test='amount!="0"'>
       s.unpaid_amt=#{amount}-s.paid_amt
    </if>

    WHERE s.id = #{statementId}
  </update>

  <select id="queryStatementAmount" parameterType="com.tiangong.finance.remote.statement.request.StatementIdDTO"
    resultType="com.tiangong.finance.remote.statement.request.QueryStatementTotalAmountDTO">
    SELECT
     so.statement_id statementId,
     so.currency currency,
     sum(so.pay_amt) amount
    FROM
     f_supplier_statement_order so
    WHERE
     so.statement_id = #{statementId}
    GROUP BY
     so.statement_id,
     so.currency
  </select>

  <select id="queryNewStatementAmount" parameterType="com.tiangong.finance.remote.statement.request.StatementIdDTO"
          resultType="java.math.BigDecimal">
    SELECT
     sum(sso.pay_amt)

    FROM
    f_supplier_statement_order sso
    INNER JOIN o_supply_order so ON sso.supply_order_code = so.supply_order_code
    INNER JOIN o_supply_order_finance sf ON so.id = sf.supply_order_id AND sf.finance_type = #{statementType}
    WHERE sso.statement_id=#{statementId}
  </select>

  <update id="updateSupplyOrderFinance" parameterType="com.tiangong.finance.statement.dto.UpdateSupplyOrderFinanceDTO">
    UPDATE f_supplier_statement_order sso INNER JOIN o_supply_order so ON sso.supply_order_code = so.supply_order_code
    INNER JOIN o_supply_order_finance sf ON so.id = sf.supply_order_id AND sf.finance_type = #{financeType}
    <trim prefix="set" suffixOverrides=",">
      <if test="checkStatus != null">
        sf.check_status = #{checkStatus},
      </if>
      <if test="isUpdateSettlementStatus != null and isUpdateSettlementStatus == 1">
        <if test="financeType == 0">
          sf.settlement_status =
          (CASE WHEN (sf.paid_amt+sso.pay_amt)!=so.supply_order_amt THEN
          0
          ELSE
          1
          END),

          sf.settlement_date =
          (CASE WHEN (sf.paid_amt+sso.pay_amt)!=so.supply_order_amt THEN
          sf.settlement_date
          ELSE
          SYSDATE()
          END),

          sf.check_status =
          (CASE WHEN (sf.paid_amt+sso.pay_amt)!=so.supply_order_amt THEN
          1
          ELSE
          <if test="checkStatus != null">
            #{checkStatus}
          </if>
          <if test="checkStatus == null">
            sf.check_status
          </if>
          END),
        </if>
        <if test="financeType == 1">
          sf.settlement_status =
          (CASE WHEN (sf.paid_amt+sso.pay_amt)!=so.reward_amt THEN
          0
          ELSE
          1
          END),
          sf.settlement_date =
          (CASE WHEN (sf.paid_amt+sso.pay_amt)!=so.reward_amt THEN
          sf.settlement_date
          ELSE
          SYSDATE()
          END),
          sf.check_status =
          (CASE WHEN (sf.paid_amt+sso.pay_amt)!=so.reward_amt THEN
          1
          ELSE
          <if test="checkStatus != null">
            #{checkStatus}
          </if>
          <if test="checkStatus == null">
            sf.check_status
          </if>
          END),
        </if>
        <if test="financeType == 2">
          sf.settlement_status =
          (CASE WHEN (sf.paid_amt+sso.pay_amt)!=so.rebate_amt THEN
          0
          ELSE
          1
          END),
          sf.settlement_date =
          (CASE WHEN (sf.paid_amt+sso.pay_amt)!=so.rebate_amt THEN
          sf.settlement_date
          ELSE
          SYSDATE()
          END),
          sf.check_status =
          (CASE WHEN (sf.paid_amt+sso.pay_amt)!=so.rebate_amt THEN
          1
          ELSE
          <if test="checkStatus != null">
            #{checkStatus}
          </if>
          <if test="checkStatus == null">
            sf.check_status
          </if>
          END),
        </if>
      </if>
      <if test="isUpdateSettlementAmount != null and isUpdateSettlementAmount == 1">
        sf.paid_amt=sf.paid_amt+sso.pay_amt,
        sf.unpaid_amt=sf.unpaid_amt-sso.pay_amt,
      </if>
      <if test="financeLockStatus != null">
        sf.finance_lock_status=#{financeLockStatus},
      </if>
    </trim>
    WHERE sso.statement_id=#{statementId}
    <if test="supplyOrderIdList != null and supplyOrderIdList.size > 0">
      AND so.id IN
      <foreach collection="supplyOrderIdList" item="item" open="(" separator="," close=")">#{item}</foreach>
    </if>
    <if test="statementOrderIdList != null and statementOrderIdList.size > 0">
      AND sso.id IN
      <foreach collection="statementOrderIdList" item="item" open="(" separator="," close=")">#{item}</foreach>
    </if>
  </update>

  <update id="updateStatementSupplyOrderPayAmt">
    UPDATE f_supplier_statement_order
    SET pay_amt = #{payAmt}
    WHERE id = #{statementSupplyOrderId}
  </update>

  <select id="queryUnCheckOutSupplyOrder" resultMap="queryUnCheckOutSupplyOrderMap" parameterType="com.tiangong.finance.remote.statement.request.QueryUnCheckOutSupplyOrderDTO">
    SELECT
    so.id supplyOrderId,
    so.supply_order_code supplyOrderCode,
    so.hotel_name hotelName,
    so.room_name roomName,
    o.guest guest,
    o.id orderId,
    so.room_numbers roomNumbers,
    so.start_date startDate,
    so.end_date endDate,
    so.room_qty roomQty,
    so.base_currency currency,
    <if test="statementType == 0">
      IFNULL(so.supply_order_amt-sf.paid_amt,0) unpaidAmt,
      IFNULL(so.supply_order_amt,0) payableAmt,
    </if>
    <if test="statementType == 1">
      IFNULL(so.reward_amt-sf.paid_amt,0) unpaidAmt,
      IFNULL(so.reward_amt,0) payableAmt,
    </if>
    <if test="statementType == 2">
      IFNULL(so.rebate_amt-sf.paid_amt,0) unpaidAmt,
      IFNULL(so.rebate_amt,0) payableAmt,
    </if>
    IFNULL(sf.paid_amt,0) paidAmt,
    so.rate saleRate,
    o.created_dt createdDt,
    so.confirmation_status confirmationStatus
    FROM o_order o INNER JOIN o_supply_order so ON o.id=so.order_id
      INNER JOIN o_supply_order_finance sf ON so.id=sf.supply_order_id AND sf.finance_type = #{statementType}
    WHERE o.company_code=#{companyCode}
    AND so.settlement_type!=3
    AND so.confirmation_status IN (1,2,3)
    AND sf.check_status=1
    <if test="currency!=null">
      AND so.base_currency = #{currency}
    </if>
    <if test="statementType == 0">
      AND so.supply_order_amt - sf.paid_amt!=0
    </if>
    <if test="statementType == 1">
      AND so.reward_amt - sf.paid_amt != 0
    </if>
    <if test="statementType == 2">
      AND so.rebate_amt - sf.paid_amt != 0
    </if>
--     AND o.order_confirmation_status IN (1,2,3)
    <choose>
      <when test="dateQueryType==0">
        <if test="startDate!=null and startDate!=''">
          AND o.created_dt >= #{startDate}
        </if>
        <if test="endDate!=null and endDate!=''">
          AND o.created_dt &lt; date_add(#{endDate},interval 1 day)
        </if>
      </when>
      <when test="dateQueryType==1">
        <if test="startDate!=null and startDate!=''">
          AND so.start_date >= #{startDate}
        </if>
        <if test="endDate!=null and endDate!=''">
          AND so.start_date &lt;= #{endDate}
        </if>
      </when>
      <when test="dateQueryType==2">
        <if test="startDate!=null and startDate!=''">
          AND so.end_date >= #{startDate}
        </if>
        <if test="endDate!=null and endDate!=''">
          AND so.end_date &lt;= #{endDate}
        </if>
      </when>
    </choose>
    <if test="supplyOrderCode!=null and supplyOrderCode!=''">
      AND so.supply_order_code=#{supplyOrderCode}
    </if>
    <if test="supplierCode!=null and supplierCode!=''">
      AND so.supplier_code = #{supplierCode}
    </if>


    <if test="channelOrderCode!=null and channelOrderCode!=''">
      AND o.channel_order_code LIKE "%"#{channelOrderCode}"%"
    </if>
    <if test="agentCode!=null and agentCode!=''">
      AND o.agent_code LIKE "%"#{agentCode}"%"
    </if>
    <if test="agentName!=null and agentName!=''">
      AND o.agent_name LIKE "%"#{agentName}"%"
    </if>
    <if test="hotelId!=null and hotelId!=''">
      AND o.hotel_id = #{hotelId}
    </if>

    ORDER BY o.created_dt DESC
  </select>

  <select id="queryUpdatedStatementOrderList" parameterType="com.tiangong.finance.remote.statement.request.QueryUpdatedStatementOrderListDTO"
          resultType="com.tiangong.finance.remote.statement.response.UpdatedStatementSupplyOrderDTO">
    SELECT
    sso.supply_order_code supplyOrderCode,
    so.hotel_name hotelName,
    so.room_name roomName,
    log.operatedBy,
    <if test="statementType == 0">
      CONCAT(if(sso.room_name!=so.room_name,CONCAT('将房型修改为 "',so.room_name,'"'),''),
      if(sso.payable_amt!=so.supply_order_amt-sf.paid_amt,CONCAT('将供货单未付金额从',
      TRIM(TRAILING '.' FROM TRIM(TRAILING '0' FROM sso.unpaid_amt)),'修改为 "',
      TRIM(TRAILING '.' FROM TRIM(TRAILING '0' FROM so.supply_order_amt-sf.paid_amt)),'"'),''),
      if(sso.guest!=o.guest,CONCAT('将入住人修改为 "',o.guest,'"'),''))content
    </if>
    <if test="statementType == 1">
      CONCAT(if(sso.room_name!=so.room_name,CONCAT('将房型修改为 "',so.room_name,'"'),''),
      if(sso.payable_amt!=so.reward_amt-sf.paid_amt,CONCAT('将供应商奖励未收金额从',
      TRIM(TRAILING '.' FROM TRIM(TRAILING '0' FROM sso.unpaid_amt)),'修改为 "',
      TRIM(TRAILING '.' FROM TRIM(TRAILING '0' FROM so.reward_amt-sf.paid_amt)),'"'),''),
      if(sso.guest!=o.guest,CONCAT('将入住人修改为 "',o.guest,'"'),''))content
    </if>
    <if test="statementType == 2">
      CONCAT(if(sso.room_name!=so.room_name,CONCAT('将房型修改为 "',so.room_name,'"'),''),
      if(sso.payable_amt!=so.rebate_amt-sf.paid_amt,CONCAT('将供货单返佣未付金额从',
      TRIM(TRAILING '.' FROM TRIM(TRAILING '0' FROM sso.unpaid_amt)),'修改为 "',
      TRIM(TRAILING '.' FROM TRIM(TRAILING '0' FROM so.rebate_amt-sf.paid_amt)) ,'"'),''),
      if(sso.guest!=o.guest,CONCAT('将入住人修改为 "',o.guest,'"'),''))content
    </if>
    FROM f_supplier_statement_order sso INNER JOIN o_supply_order so ON sso.supply_order_code=so.supply_order_code
    INNER JOIN o_supply_order_finance sf ON so.id = sf.supply_order_id AND sf.finance_type = #{statementType}
    INNER JOIN o_order o ON o.id=so.order_id
    INNER JOIN (SELECT l.order_id,l.created_by operatedBy FROM o_order_log l WHERE l.id IN (SELECT max(id) FROM o_order_log GROUP BY order_id)) log
    ON o.id=log.order_id
    WHERE sso.statement_id=#{statementId}
    <if test="currency != null">
      AND so.base_currency = #{currency}
    </if>
    AND (sso.room_name!=so.room_name
    <if test="statementType == 0">
      OR sso.unpaid_amt!=so.supply_order_amt-sf.paid_amt
    </if>
    <if test="statementType == 1">
      OR sso.unpaid_amt!=so.reward_amt-sf.paid_amt
    </if>
    <if test="statementType == 2">
      OR sso.unpaid_amt!=so.rebate_amt-sf.paid_amt
    </if>
    OR sso.guest!=o.guest)
    ORDER BY sso.id DESC
  </select>
  <select id="getStatementOrder" resultType="com.tiangong.finance.statement.domain.StatementOrderPO">
      SELECT
      #{supplyOrder.statementId} statementId,
      so.id supplyOrderId,
      so.supply_order_code supplyOrderCode,
      so.hotel_name hotelName,
      so.room_name roomName,
      so.product_name productName,
      o.guest guest,
      so.start_date startDate,
      so.end_date endDate,
      so.room_qty roomQty,
      so.base_currency baseCurrency,
      sf.paid_amt paidAmt,
      <if test="supplyOrder.statementType == 0">
        so.supply_order_amt payableAmt,
        so.supply_order_amt-sf.paid_amt amt,
      </if>
      <if test="supplyOrder.statementType == 1">
        so.reward_amt payableAmt,
        so.reward_amt-sf.paid_amt amt,
      </if>
      <if test="supplyOrder.statementType == 2">
        so.rebate_amt payableAmt,
        so.rebate_amt-sf.paid_amt amt,
      </if>
      o.created_dt createdDt,
      so.confirmation_status confirmationStatus,
      #{supplyOrder.operator}operator,
      SYSDATE() sysDate
      FROM o_order o INNER JOIN o_supply_order so ON o.id=so.order_id
      INNER JOIN o_supply_order_finance sf ON so.id = sf.supply_order_id and sf.finance_type = #{supplyOrder.statementType}
      WHERE o.company_code=#{supplyOrder.companyCode}
      AND so.settlement_type!=3
      AND so.confirmation_status IN (1,2,3)
      AND sf.check_status=1
    <if test="supplyOrder.currency != null">
      AND so.base_currency = #{supplyOrder.currency}
    </if>
      <if test="supplyOrder.statementType == 0">
        AND so.supply_order_amt - sf.paid_amt!=0
      </if>
      <if test="supplyOrder.statementType == 1">
        AND so.reward_amt - sf.paid_amt!=0
      </if>
      <if test="supplyOrder.statementType == 2">
        AND so.rebate_amt - sf.paid_amt!=0
      </if>
--       AND o.order_confirmation_status IN (1,2,3)
--       AND o.pay_method != 1
      <choose>
          <when test="supplyOrder.dateQueryType==0">
              <if test="supplyOrder.startDate!=null and supplyOrder.startDate!=''">
                  AND o.created_dt >= #{supplyOrder.startDate}
              </if>
              <if test="supplyOrder.endDate!=null and supplyOrder.endDate!=''">
                  AND o.created_dt &lt; date_add(#{supplyOrder.endDate},interval 1 day)
              </if>
          </when>
          <when test="supplyOrder.dateQueryType==1">
              <if test="supplyOrder.startDate!=null and supplyOrder.startDate!=''">
                  AND so.start_date >= #{supplyOrder.startDate}
              </if>
              <if test="supplyOrder.endDate!=null and supplyOrder.endDate!=''">
                  AND so.start_date &lt;= #{supplyOrder.endDate}
              </if>
          </when>
          <when test="supplyOrder.dateQueryType==2">
              <if test="supplyOrder.startDate!=null and supplyOrder.startDate!=''">
                  AND so.end_date >= #{supplyOrder.startDate}
              </if>
              <if test="supplyOrder.endDate!=null and supplyOrder.endDate!=''">
                  AND so.end_date &lt;= #{supplyOrder.endDate}
              </if>
          </when>
      </choose>
      <choose>
          <when test="supplyOrder.supplierCode!=null and supplyOrder.supplierCode!=''">
              AND so.supplier_code=#{supplyOrder.supplierCode}
          </when>
          <otherwise>
              <if test="supplyOrder.supplierName != null and supplyOrder.supplierName != ''">
                  AND so.supplier_name LIKE concat(concat('%',#{supplyOrder.supplierName}),'%')
              </if>
          </otherwise>
      </choose>
      ORDER BY o.created_dt
  </select>

  <select id="getStatementOrderByCodes" resultType="com.tiangong.finance.statement.domain.StatementOrderPO">
    SELECT
    #{statementId} statementId,
    so.id supplyOrderId,
    so.supply_order_code supplyOrderCode,
    so.hotel_name hotelName,
    so.room_name roomName,
    so.product_name productName,
    o.guest guest,
    so.start_date startDate,
    so.end_date endDate,
    so.room_qty roomQty,
    so.base_currency baseCurrency,
    sf.paid_amt paidAmt,
    so.supply_order_amt payableAmt,
    so.supply_order_amt-sf.paid_amt amt,
    o.created_dt createdDt,
    so.confirmation_status confirmationStatus,
    #{operator} operator,
    SYSDATE() sysDate
    FROM o_order o INNER JOIN o_supply_order so ON o.id=so.order_id
    INNER JOIN o_supply_order_finance sf ON so.id = sf.supply_order_id and sf.finance_type = 0
    WHERE o.company_code=#{companyCode}
    AND so.base_currency = #{currency}
    AND so.supply_order_code IN
    <foreach collection="supplyOrderCodeList" separator="," open="(" close=")" item="item">
        #{item}
    </foreach>
    ORDER BY o.created_dt
  </select>

  <delete id="deleteStatementOrderList">
    DELETE FROM f_supplier_statement_order WHERE id in
    <foreach collection="list" item="item" open="(" separator="," close=")">#{item}</foreach>
  </delete>

  <select id="selectStatementOrderListByOrderId" resultType="java.lang.Integer">
    select supply_order_id FROM f_supplier_statement_order WHERE id in
    <foreach collection="list" item="item" open="(" separator="," close=")">#{item}</foreach>
  </select>

  <insert id="saveBatchStatementOrder2" parameterType="com.tiangong.finance.statement.dto.InsertStatementSupplyOrderDTO">
  INSERT INTO f_supplier_statement_order (
  statement_id,
  supply_order_id,
  supply_order_code,
  hotel_name,
  room_name,
  product_name,
  guest,
  start_date,
  end_date,
  room_qty,
  currency,
  paid_amt,
  unpaid_amt,
  payable_amt,
  pay_amt,
  order_create_date,
  confirmation_status,
  created_by,
  created_dt
  ) values
    <foreach collection="list" item="item" index="index" separator="," >
      (#{item.statementId},
      #{item.supplyOrderId},
      #{item.supplyOrderCode},
      #{item.hotelName},
      #{item.roomName},
      #{item.productName},
      #{item.guest},
      #{item.startDate},
      #{item.endDate},
      #{item.roomQty},
      #{item.baseCurrency},
      #{item.paidAmt},
      #{item.amt},
      #{item.payableAmt},
      #{item.amt},
      #{item.createdDt},
      #{item.confirmationStatus},
      #{item.operator},
      SYSDATE())
    </foreach>
  </insert>

    <!-- 查询供应商（奖励、返佣）账单id -->
    <select id="queryStatementIds" parameterType="com.tiangong.finance.remote.statement.request.QuerySupplierStatementListDTO" resultType="java.lang.Integer">
        SELECT
        so.statement_id as statementId
        FROM o_order oo
        LEFT JOIN o_supply_order o ON o.order_id = oo.id
        LEFT JOIN f_supplier_statement_order so ON so.supply_order_code = o.supply_order_code
        LEFT JOIN f_supplier_statement s ON so.statement_id = s.id
        WHERE s.statement_type=#{statementType}
        <if test="supplyOrderCodeOrOrderCode!=null and supplyOrderCodeOrOrderCode!=''">
            AND (oo.order_code LIKE CONCAT('%', #{supplyOrderCodeOrOrderCode}, '%')  or  o.supply_order_code LIKE CONCAT('%', #{supplyOrderCodeOrOrderCode}, '%') )
        </if>
        <if test="hotelId != null and hotelId != ''">
            AND oo.hotel_id = #{hotelId}
        </if>
        <if test="supplyOrderCode != null and supplyOrderCode != ''">
            AND o.supply_order_code = #{supplyOrderCode}
        </if>
      and so.statement_id is not null
    </select>
</mapper>