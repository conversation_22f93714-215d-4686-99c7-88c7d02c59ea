<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.tiangong.finance.statement.mapper.SupplyAutoReconciliationMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap id="BaseResultMap" type="com.tiangong.finance.statement.domain.entity.SupplyAutoReconciliationEntity">
        <result property="id" column="id"/>
        <result property="reconciliationResultType" column="reconciliation_result_type"/>
        <result property="statementId" column="statement_id"/>
        <result property="statementCode" column="statement_code"/>
        <result property="supplyPaidAmt" column="supply_paid_amt"/>
        <result property="supplyPaidAmtCurrency" column="supply_paid_amt_currency"/>
        <result property="supplierStatementId" column="supplier_statement_id"/>
        <result property="supplierStatementCode" column="supplier_statement_code"/>
        <result property="supplierReceivedAmt" column="supplier_received_amt"/>
        <result property="supplierReceivedAmtCurrency" column="supplier_received_amt_currency"/>
        <result property="createdBy" column="created_by"/>
        <result property="createdDt" column="created_dt"/>
        <result property="updatedBy" column="updated_by"/>
        <result property="updatedDt" column="updated_dt"/>
    </resultMap>


</mapper>