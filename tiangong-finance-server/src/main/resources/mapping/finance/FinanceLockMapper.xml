<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tiangong.finance.lock.mapper.FinanceLockMapper">

    <resultMap id="QuerySupplyOrderMap" type="com.tiangong.finance.remote.lock.response.SupplyOrderFinanceLockDTO">
        <result column="supplyOrderId" property="supplyOrderId"/>
        <result column="supplyOrderCode" property="supplyOrderCode"/>
        <result column="createdDt" property="createdDt"/>
        <result column="supplierName" property="supplierName"/>
        <result column="hotelName" property="hotelName"/>
        <result column="roomName" property="roomName"/>
        <result column="startDate" property="startDate"/>
        <result column="endDate" property="endDate"/>
        <result column="roomQty" property="roomQty"/>
        <result column="guest" property="guest"/>
        <result column="roomNumbers" property="roomNumbers"/>
        <result column="lockStatus" property="lockStatus"/>
        <result column="rewardLockStatus" property="rewardLockStatus"/>
        <result column="rebateLockStatus" property="rebateLockStatus"/>
        <result column="orderCode" property="orderCode"/>

        <!-- 入住人信息 -->
        <collection property="guests" select="com.tiangong.finance.lock.mapper.FinanceLockMapper.selectOrderGuests"
                    column="orderId" javaType="ArrayList" ofType="com.tiangong.finance.remote.lock.response.GuestDTO">
            <result column="name" property="name"/>
            <result column="roomNumber" property="roomNumber"/>
        </collection>
    </resultMap>

    <select id="queryOrderList" parameterType="com.tiangong.finance.remote.lock.request.QueryOrderFinanceLockListDTO"
            resultType="com.tiangong.finance.remote.lock.response.OrderFinanceLockDTO">
        SELECT
        o.id orderId,
        o.order_code orderCode,
        o.created_dt createdDt,
        o.agent_name agentName,
        o.hotel_Name hotelName,
        o.room_name roomName,
        o.start_date startDate,
        o.end_date endDate,
        o.guest guest,
        o.room_qty roomQty,
        f.finance_lock_status lockStatus
        FROM o_order o,o_order_finance f
        WHERE o.id=f.order_id
        AND o.company_code=#{companyCode}
        <choose>
            <when test="agentCode!=null and agentCode!=''">
                AND o.agent_code=#{agentCode}
            </when>
            <otherwise>
                <if test="agentName != null and agentName != ''">
                    AND o.agent_name LIKE concat(concat('%',#{agentName}),'%')
                </if>
            </otherwise>
        </choose>
        <if test="lockStatus != null and lockStatus  != ''">
            AND f.finance_lock_status = #{lockStatus}
        </if>
        <if test="orderCode!=null and orderCode!=''">
            AND o.order_code=#{orderCode}
        </if>
        ORDER BY o.id DESC
    </select>

    <update id="lockOrder" parameterType="com.tiangong.finance.remote.lock.request.FinanceLockOrderDTO">
        UPDATE o_order_finance f SET f.finance_lock_status=#{lockStatus} WHERE f.order_id=#{orderId}
    </update>

    <select id="checkOrderCanLock" parameterType="java.lang.Integer" resultType="java.lang.Integer">
        SELECT
        f.id
        FROM f_agent_statement_order f
        LEFT JOIN f_agent_statement s ON s.id = f.statement_id
        WHERE f.order_id = #{orderId} AND s.statement_status = 2
    </select>


    <select id="selectSupplyOrderPage" resultMap="QuerySupplyOrderMap" parameterType="com.tiangong.finance.remote.lock.request.QuerySupplyOrderFinanceLockListDTO">
        SELECT
        so.id supplyOrderId,
        so.supply_order_code supplyOrderCode,
        o.created_dt createdDt,
        so.supplier_name supplierName,
        so.hotel_Name hotelName,
        so.room_name roomName,
        so.start_date startDate,
        so.end_date endDate,
        so.room_qty roomQty,
        o.guest guest,
        so.room_numbers roomNumbers,
        sf.finance_lock_status lockStatus,
        sf1.finance_lock_status rewardLockStatus,
        sf2.finance_lock_status rebateLockStatus,
        o.order_code orderCode,
        o.id orderId
        FROM o_order o
        LEFT JOIN o_supply_order so ON o.id=so.order_id
        LEFT JOIN o_supply_order_finance sf ON so.id=sf.supply_order_id AND sf.finance_type = 0
        LEFT JOIN o_supply_order_finance sf1 ON so.id=sf1.supply_order_id AND sf1.finance_type = 1
        LEFT JOIN o_supply_order_finance sf2 ON so.id=sf2.supply_order_id AND sf2.finance_type = 2
        WHERE o. company_code=#{req.companyCode}
        <choose>
            <when test="req.supplierCode!=null and req.supplierCode!=''">
                AND so.supplier_code=#{req.supplierCode}
            </when>
            <otherwise>
                <if test="req.supplierName != null and req.supplierName != ''">
                    AND so.supplier_name LIKE concat(concat('%',#{req.supplierName}),'%')
                </if>
            </otherwise>
        </choose>
        <if test="req.lockStatus !=null and req.lockStatus != ''">
            AND sf.finance_lock_status = #{req.lockStatus}
        </if>
        <if test="req.supplyOrderCode!=null and req.supplyOrderCode!=''">
            AND so.supply_order_code=#{req.supplyOrderCode}
        </if>
        ORDER BY o.id DESC
    </select>

    <update id="lockSupplyOrder" parameterType="com.tiangong.finance.remote.lock.request.FinanceLockSupplyOrderDTO">
        UPDATE o_supply_order_finance sf SET sf.finance_lock_status=#{lockStatus} WHERE
        sf.supply_order_id=#{supplyOrderId} AND sf.finance_type = #{financeType}
    </update>

    <select id="checkSupplyOrderCanLock" parameterType="java.lang.Integer" resultType="java.lang.Integer">
        SELECT
            sf.id
        FROM o_supply_order_finance sf
        WHERE sf.supply_order_id = #{supplyOrderId}
        AND sf.finance_type = 0
        AND sf.finance_lock_status = 2
    </select>

    <update id="lockSupplyOrderList" parameterType="com.tiangong.finance.remote.lock.request.FinanceLockSupplyOrderDTO">
        UPDATE o_supply_order_finance sf
        SET sf.finance_lock_status=#{lockStatus}
        WHERE sf.finance_type = #{financeType}
        AND sf.supply_order_code IN
        <foreach collection="supplyOrderList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </update>

    <select id="selectOrderGuests" resultType="com.tiangong.finance.remote.lock.response.GuestDTO">
        SELECT
            t.name as name,
            t.room_number as roomNumber
        FROM o_guest t WHERE t.order_id = #{orderId}
    </select>
</mapper>