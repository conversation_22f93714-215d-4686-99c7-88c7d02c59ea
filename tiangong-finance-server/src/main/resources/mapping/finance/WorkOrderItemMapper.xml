<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tiangong.finance.workorder.mapper.WorkOrderItemMapper">
    <resultMap id="BaseResultMap" type="com.tiangong.finance.workorder.domain.WorkOrderItemPO">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="work_order_id" jdbcType="INTEGER" property="workOrderId"/>
        <result column="business_code" jdbcType="VARCHAR" property="businessCode"/>
        <result column="amount" jdbcType="DECIMAL" property="amount"/>
        <result column="created_by" jdbcType="VARCHAR" property="createdBy"/>
        <result column="created_dt" jdbcType="TIMESTAMP" property="createdDt"/>
        <result column="updated_by" jdbcType="VARCHAR" property="updatedBy"/>
        <result column="updated_dt" jdbcType="TIMESTAMP" property="updatedDt"/>
    </resultMap>
</mapper>