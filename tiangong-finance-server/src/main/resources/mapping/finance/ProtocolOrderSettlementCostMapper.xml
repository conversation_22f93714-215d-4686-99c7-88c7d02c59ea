<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tiangong.finance.statement.mapper.ProtocolOrderSettlementCostMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap id="BaseResultMap" type="com.tiangong.finance.statement.domain.ProtocolOrderSettlementCostPO">
        <result property="id" column="id"/>
        <result property="paySerialNo" column="paySerialNo"/>
        <result property="swipingCardAmountCurrency" column="swiping_card_amount_currency"/>
        <result property="swipingCardAmount" column="swiping_card_amount"/>
        <result property="swipingCardRate" column="swiping_card_rate"/>
        <result property="swipingCardDt" column="swiping_card_dt"/>
        <result property="settleAmountCurrency" column="settle_amount_currency"/>
        <result property="settleAmount" column="settle_amount"/>
        <result property="supplyOrderCode" column="supply_order_code"/>
        <result property="orderCode" column="order_code"/>
        <result property="channelOrderCode" column="channel_order_code"/>
        <result property="importBy" column="import_by"/>
        <result property="importDt" column="import_dt"/>
        <result property="matchingStatus" column="matching_status"/>
        <result property="failDesc" column="fail_desc"/>
        <result property="deleted" column="deleted"/>
        <result property="createdBy" column="created_by"/>
        <result property="createdDt" column="created_dt"/>
        <result property="updatedBy" column="updated_by"/>
        <result property="updatedDt" column="updated_dt"/>
    </resultMap>

    <update id="batchUpdateProtocolOrderSettlementCost" parameterType="java.util.List">
        <foreach collection="list" item="update" separator=";">
            UPDATE f_protocol_order_settlement_cost
            <set>
                <if test="update.matchingStatus != null">
                    matching_status = #{update.matchingStatus},
                </if>
                <if test="update.orderCode != null and update.orderCode != ''">
                    order_code = #{update.orderCode},
                </if>
                <if test="update.channelOrderCode != null and update.channelOrderCode != ''">
                    channel_order_code = #{update.channelOrderCode},
                </if>
                <if test="update.agentCode != null and update.agentCode != ''">
                    agent_code = #{update.agentCode},
                </if>
                <if test="update.failDesc != null and update.failDesc != ''">
                    fail_desc = #{update.failDesc},
                </if>
                <if test="update.updatedDt != null">
                    updated_dt = #{update.updatedDt},
                </if>
                <if test="update.updatedBy != null and update.updatedBy != ''">
                    updated_by = #{update.updatedBy},
                </if>
            </set>
            WHERE supply_order_code = #{update.supplyOrderCode}
        </foreach>
    </update>

    <select id="selectProtocolOrderSettlementCostPage" resultType="com.tiangong.finance.statement.domain.ProtocolOrderSettlementCostPO">
        SELECT
            id,
            pay_serial_no,
            swiping_card_amount,
            swiping_card_amount_currency,
            swiping_card_rate,
            swiping_card_dt,
            settle_amount,
            settle_amount_currency,
            supply_order_code,
            order_code,
            channel_order_code,
            import_by,
            import_dt,
            matching_status,
            fail_desc,
            push_status,
            agent_code,
            deleted,
            created_by,
            created_dt,
            updated_by,
            updated_dt
        FROM f_protocol_order_settlement_cost
        WHERE deleted = 0
        <if test="req.paySerialNo != null">
            and pay_serial_no = #{req.paySerialNo}
        </if>
        <if test="req.supplyOrderCode != null and req.supplyOrderCode != ''">
            and supply_order_code = #{req.supplyOrderCode}
        </if>
        <if test="req.orderCode != null and req.orderCode != ''">
            and order_code = #{req.orderCode}
        </if>
        <if test="req.channelOrderCode != null and req.channelOrderCode != ''">
            and channel_order_code = #{req.channelOrderCode}
        </if>
        <if test="req.matchingStatus != null">
            and matching_status = #{req.matchingStatus}
        </if>
        <if test="req.pushStatus != null">
            and push_status = #{req.pushStatus}
        </if>
        ORDER BY FIELD(matching_status, 2, 0, 1, 999) ASC, created_dt desc
    </select>

</mapper>