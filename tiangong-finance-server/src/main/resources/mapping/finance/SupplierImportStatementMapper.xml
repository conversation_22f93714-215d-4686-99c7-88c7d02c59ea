<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.tiangong.finance.statement.mapper.SupplierImportStatementMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap id="BaseResultMap" type="com.tiangong.finance.statement.domain.entity.SupplierImportStatementEntity">
        <result property="id" column="id"/>
        <result property="supplierStatementCode" column="supplier_statement_code"/>
        <result property="statementId" column="statement_id"/>
        <result property="statementCode" column="statement_code"/>
        <result property="supplierCode" column="supplier_code"/>
        <result property="supplierName" column="supplier_name"/>
        <result property="dateQueryType" column="date_query_type"/>
        <result property="startDate" column="start_date"/>
        <result property="endDate" column="end_date"/>
        <result property="supplierStatementName" column="supplier_statement_name"/>
        <result property="settlementDate" column="settlement_date"/>
        <result property="supplierTotalReceivables" column="supplier_total_receivables"/>
        <result property="currency" column="currency"/>
        <result property="statementStatus" column="statement_status"/>
        <result property="deleted" column="deleted"/>
        <result property="createdBy" column="created_by"/>
        <result property="createdDt" column="created_dt"/>
        <result property="updatedBy" column="updated_by"/>
        <result property="updatedDt" column="updated_dt"/>
    </resultMap>

    <update id="updateStatementOrderPaidAmt">
        UPFDATE supplier_import_statement SET supplier_total_receivables = #{supplierTotalReceivables} WHERE id = #{id}
    </update>


</mapper>