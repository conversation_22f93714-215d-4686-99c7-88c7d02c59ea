<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tiangong.finance.statement.mapper.AgentStatementOrderMapper">
  <resultMap id="BaseResultMap" type="com.tiangong.finance.statement.domain.AgentStatementOrderPO">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="statement_id" jdbcType="INTEGER" property="statementId" />
    <result column="order_id" jdbcType="INTEGER" property="orderId" />
    <result column="order_code" jdbcType="VARCHAR" property="orderCode" />
    <result column="hotel_name" jdbcType="VARCHAR" property="hotelName" />
    <result column="room_name" jdbcType="VARCHAR" property="roomName" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="guest" jdbcType="VARCHAR" property="guest" />
    <result column="start_date" jdbcType="DATE" property="startDate" />
    <result column="end_date" jdbcType="DATE" property="endDate" />
    <result column="room_qty" jdbcType="INTEGER" property="roomQty" />
    <result column="currency" jdbcType="INTEGER" property="currency" />
    <result column="receivable_amt" jdbcType="DECIMAL" property="receivableAmt" />
    <result column="order_create_date" jdbcType="TIMESTAMP" property="orderCreateDate" />
    <result column="confirmation_status" jdbcType="INTEGER" property="confirmationStatus" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_dt" jdbcType="TIMESTAMP" property="createdDt" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
    <result column="updated_dt" jdbcType="TIMESTAMP" property="updatedDt" />
  </resultMap>

    <resultMap id="queryStatementOrderListBaseResultMap" type="com.tiangong.finance.remote.statement.response.StatementOrderDTO">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="INTEGER" property="statementOrderId" />
        <result column="order_code" jdbcType="VARCHAR" property="orderCode" />
        <result column="channel_order_code" jdbcType="VARCHAR" property="channelOrderCode" />
        <result column="hotel_name" jdbcType="VARCHAR" property="hotelName" />
        <result column="room_name" jdbcType="VARCHAR" property="roomName" />
        <result column="guest" jdbcType="VARCHAR" property="guest" />
        <result column="start_date" jdbcType="DATE" property="startDate" />
        <result column="end_date" jdbcType="DATE" property="endDate" />
        <result column="room_qty" jdbcType="INTEGER" property="roomQty" />
        <result column="currency" jdbcType="INTEGER" property="currency" />
        <result column="sale_rate" jdbcType="DECIMAL" property="saleRate" />
        <result column="org_to_agent_rate" jdbcType="DECIMAL" property="orgToAgentRate" />
        <result column="receivable_amt" jdbcType="DECIMAL" property="receivableAmt" />
        <result column="received_amt" jdbcType="DECIMAL" property="receivedAmt" />
        <result column="unreceived_amt" jdbcType="DECIMAL" property="unreceivedAmt" />
        <result column="receive_amt" jdbcType="DECIMAL" property="receiveAmt" />
        <result column="confirmation_status" jdbcType="INTEGER" property="confirmationStatus" />
        <result column="order_create_date" jdbcType="TIMESTAMP" property="createdDt" />
        <result column="additional_charges" jdbcType="DECIMAL" property="additionalCharges" />
        <result column="refund_fee" jdbcType="DECIMAL" property="refundFee" />
        <result column="sum_sale_price" jdbcType="DECIMAL" property="sumSalePrice" />
        <result column="city_name" jdbcType="DECIMAL" property="cityName" />
        <result column="nightQty" jdbcType="DECIMAL" property="nightQty" />
        <result column="orderId" jdbcType="INTEGER" property="orderId" />
        <result column="swiping_card_amount" jdbcType="DECIMAL" property="swipingCardAmount" />
        <result column="swiping_card_amount_currency" jdbcType="INTEGER" property="swipingCardAmountCurrency" />
        <result column="swiping_card_rate" jdbcType="DECIMAL" property="swipingCardRate" />
        <result column="swiping_card_dt" jdbcType="DATE" property="swipingCardDt" />
        <result column="settle_amount" jdbcType="DECIMAL" property="settleAmount" />
        <result column="settle_amount_currency" jdbcType="INTEGER" property="settleAmountCurrency" />
        <result column="collection_rate" jdbcType="DECIMAL" property="collectionRate" />
        <result column="collection_amount" jdbcType="DECIMAL" property="collectionAmount" />
        <result column="rate_difference_value" jdbcType="DECIMAL" property="rateDifferenceValue" />
        <result column="settlement_cost_error_desc" jdbcType="VARCHAR" property="settlementCostErrorDesc" />
        <result column="supplier_label" jdbcType="INTEGER" property="supplierLabel" />
    </resultMap>

  <insert id="saveBatchStatementOrder" parameterType="com.tiangong.finance.statement.dto.InsertStatementOrderDTO">
    INSERT INTO f_agent_statement_order (
        statement_id,
        order_id,
        order_code,
        hotel_name,
        room_name,
        product_name,
        guest,
        start_date,
        end_date,
        room_qty,
        currency,
        receivable_amt,
        received_amt,
        unreceived_amt,
        receive_amt,
        order_create_date,
        confirmation_status,
        created_by,
        created_dt
    ) SELECT
        #{statementId},
        o.id,
        o.order_code,
        o.hotel_name,
        o.room_name,
        o.product_name,
        o.guest,
        o.start_date,
        o.end_date,
        o.room_qty,
        o.sale_currency,
        o.order_amt,
        f.received_amt,
        o.order_amt-f.received_amt,
        o.order_amt-f.received_amt,
        o.created_dt,
        o.order_confirmation_status,
        #{operator},
        SYSDATE()
    FROM o_order o INNER JOIN o_order_finance f ON o.id = f.order_id
    WHERE o.company_code=#{companyCode}
    AND o.settlement_type!=3
    AND o.order_confirmation_status IN (1,2,3)
    AND f.check_status=1
    AND o.order_amt - f.received_amt!=0
    <choose>
      <when test="dateQueryType==0">
        <if test="startDate!=null and startDate!=''">
          AND o.created_dt >= #{startDate}
        </if>
        <if test="endDate!=null and endDate!=''">
          AND o.created_dt &lt; date_add(#{endDate},interval 1 day)
        </if>
      </when>
      <when test="dateQueryType==1">
        <if test="startDate!=null and startDate!=''">
          AND o.start_date >= #{startDate}
        </if>
        <if test="endDate!=null and endDate!=''">
          AND o.start_date &lt;= #{endDate}
        </if>
      </when>
      <when test="dateQueryType==2">
        <if test="startDate!=null and startDate!=''">
          AND o.end_date >= #{startDate}
        </if>
        <if test="endDate!=null and endDate!=''">
          AND o.end_date &lt;= #{endDate}
        </if>
      </when>
    </choose>
    <choose>
      <when test="agentCode!=null and agentCode!=''">
        AND o.agent_code=#{agentCode}
      </when>
      <otherwise>
        <if test="agentName != null and agentName != ''">
          AND a.agent_name LIKE concat(concat('%',#{agentName}),'%')
        </if>
      </otherwise>
    </choose>
      <if test="orderIdList != null and orderIdList.size > 0">
          AND o.id IN
          <foreach collection="orderIdList" item="item" open="(" separator="," close=")">#{item}</foreach>
      </if>
    ORDER BY o.created_dt
  </insert>

    <insert id="autoSaveBatchStatementOrder" parameterType="com.tiangong.finance.statement.dto.InsertStatementOrderDTO">
        INSERT INTO f_agent_statement_order (
        statement_id,
        order_id,
        order_code,
        hotel_name,
        room_name,
        product_name,
        guest,
        start_date,
        end_date,
        room_qty,
        currency,
        receivable_amt,
        received_amt,
        unreceived_amt,
        receive_amt,
        order_create_date,
        confirmation_status,
        created_by,
        created_dt
        ) SELECT
        #{statementId},
        o.id,
        o.order_code,
        o.hotel_name,
        o.room_name,
        o.product_name,
        o.guest,
        o.start_date,
        o.end_date,
        o.room_qty,
        o.sale_currency,
        o.order_amt,
        f.received_amt,
        o.order_amt-f.received_amt,
        o.order_amt-f.received_amt,
        o.created_dt,
        o.order_confirmation_status,
        #{operator},
        SYSDATE()
        FROM o_order o INNER JOIN o_order_finance f ON o.id = f.order_id
        WHERE o.company_code=#{companyCode}
        AND o.settlement_type!=3
        AND o.order_confirmation_status IN (1,2,3)
        AND f.check_status=1
        AND o.order_amt - f.received_amt!=0
        <if test="orderIdList != null and orderIdList.size > 0">
            AND o.id IN
            <foreach collection="orderIdList" item="item" open="(" separator="," close=")">#{item}</foreach>
        </if>
        ORDER BY o.created_dt
    </insert>

    <!--获取账单明细-->
    <select id="getStatementOrder" resultType="com.tiangong.finance.statement.domain.StatementOrderPO">
        SELECT
        #{list.statementId} statementId,
        o.id supplyOrderId,
        o.order_code supplyOrderCode,
        o.hotel_name hotelName,
        o.room_name roomName,
        o.product_name productName,
        o.guest guest,
        o.start_date startDate,
        o.end_date endDate,
        o.room_qty roomQty,
        o.sale_currency baseCurrency,
        o.order_amt receivableAmt,
        f.received_amt receivedAmt,
        o.order_amt-f.received_amt amt,
        o.order_amt-f.received_amt receiveAmt,
        o.created_dt createdDt,
        o.order_confirmation_status confirmationStatus,
        #{list.operator} operator,
        SYSDATE() sysDate
        FROM o_order o INNER JOIN o_order_finance f ON o.id = f.order_id
        WHERE o.company_code=#{list.companyCode}
        AND o.settlement_type!=3
        AND o.order_confirmation_status IN (1,2,3)
        AND f.check_status=1
        AND o.order_amt - f.received_amt!=0
--         AND o.pay_method != 1
        <choose>
            <when test="list.dateQueryType==0">
                <if test="list.startDate!=null and list.startDate!=''">
                    AND o.created_dt >= #{list.startDate}
                </if>
                <if test="list.endDate!=null and list.endDate!=''">
                    AND o.created_dt &lt; date_add(#{list.endDate},interval 1 day)
                </if>
            </when>
            <when test="list.dateQueryType==1">
                <if test="list.startDate!=null and list.startDate!=''">
                    AND o.start_date >= #{list.startDate}
                </if>
                <if test="list.endDate!=null and list.endDate!=''">
                    AND o.start_date &lt;= #{list.endDate}
                </if>
            </when>
            <when test="list.dateQueryType==2">
                <if test="list.startDate!=null and list.startDate!=''">
                    AND o.end_date >= #{list.startDate}
                </if>
                <if test="list.endDate!=null and list.endDate!=''">
                    AND o.end_date &lt;= #{list.endDate}
                </if>
            </when>
        </choose>
        <choose>
            <when test="list.agentCode!=null and list.agentCode!=''">
                AND o.agent_code=#{list.agentCode}
            </when>
            <otherwise>
                <if test="list.agentName != null and list.agentName != ''">
                    AND a.agent_name LIKE concat(concat('%',#{list.agentName}),'%')
                </if>
            </otherwise>
        </choose>
    </select>

    <update id="updateStatementOrderList" parameterType="com.tiangong.finance.remote.statement.request.StatementIdDTO">
        UPDATE  f_agent_statement_order so INNER JOIN o_order o ON so.order_code=o.order_code
        INNER JOIN o_order_finance f ON o.id = f.order_id
        SET
        so.hotel_name=o.hotel_name,
        so.room_name=o.room_name,
        so.product_name=o.product_name,
        so.guest=o.guest,
        so.start_date=o.start_date,
        so.end_date=o.end_date,
        so.room_qty=o.room_qty,
        so.currency=o.sale_currency,
        so.receivable_amt = o.order_amt,
        so.received_amt = f.received_amt,
        so.unreceived_amt=o.order_amt-f.received_amt,
        so.receive_amt=o.order_amt-f.received_amt,
        so.confirmation_status=o.order_confirmation_status,
        so.updated_by=#{operator},
        so.updated_dt=SYSDATE()
        WHERE so.statement_id=#{statementId}
        AND so.unreceived_amt!=o.order_amt-f.received_amt
    </update>

    <select id="queryStatementOrderList"
            parameterType="com.tiangong.finance.remote.statement.request.QueryStatementOrderListDTO"
            resultMap="queryStatementOrderListBaseResultMap">
        SELECT
        so.id,
        so.order_code,
        oo.channel_order_code,
        so.hotel_name,
        so.room_name,
        so.guest,
        so.start_date,
        so.end_date,
        so.room_qty,
        so.currency,
        so.receivable_amt,
        so.received_amt,
        so.unreceived_amt,
        so.receive_amt,
        so.confirmation_status,
        so.order_create_date,
        oo.additional_charges,
        oo.sale_rate saleRate,
        oo.org_to_agent_rate,
        oo.refund_fee,
        oo.city_name,
        oo.supplier_label,
        IF( ( oo.hourly = 1 ), 0.5, 0+DATEDIFF ( oo.end_date, oo.start_date ) ) nightQty,
        oo.id as orderId,
        ifnull(so.receivable_amt, 0) - ifnull(oo.additional_charges, 0) as sum_sale_price,
        so.swiping_card_amount,
        so.swiping_card_amount_currency,
        so.swiping_card_rate,
        so.swiping_card_dt,
        so.settle_amount,
        so.settle_amount_currency,
        so.collection_rate,
        so.collection_amount,
        so.rate_difference_value,
        so.settlement_cost_error_desc
        FROM f_agent_statement_order so,o_order oo
        WHERE so.statement_id=#{statementId}
          AND so.order_id = oo.id
        <if test="orderCode !=null and orderCode !='' ">
            AND so.order_code = #{orderCode}
        </if>
        <choose>
            <when test="dateQueryType==0">
                <if test="startDate!=null and startDate!=''">
                    AND so.order_create_date >= #{startDate}
                </if>
                <if test="endDate!=null and endDate!=''">
                    AND so.order_create_date &lt; date_add(#{endDate},interval 1 day)
                </if>
            </when>
            <when test="dateQueryType==1">
                <if test="startDate!=null and startDate!=''">
                    AND so.start_date >= #{startDate}
                </if>
                <if test="endDate!=null and endDate!=''">
                    AND so.start_date &lt;= #{endDate}
                </if>
            </when>
            <when test="dateQueryType==2">
                <if test="startDate!=null and startDate!=''">
                    AND so.end_date >= #{startDate}
                </if>
                <if test="endDate!=null and endDate!=''">
                    AND so.end_date &lt;= #{endDate}
                </if>
            </when>
        </choose>
        ORDER BY IF(so.settlement_cost_error_desc is null, 0, 1) DESC, so.id DESC
    </select>

    <update id="updateStatementAmount" parameterType="com.tiangong.finance.remote.statement.request.QueryStatementTotalAmountDTO">
        UPDATE
            f_agent_statement s
        SET
            <if test="currency != null and currency != ''">
                s.currency=#{currency},
            </if>
            s.statement_amt=#{amount},
            <if test='amount == "0"'>
                s.unreceived_amt = 0
            </if>
            <if test='amount != "0"'>
                s.unreceived_amt=#{amount}-s.received_amt
            </if>
        WHERE s.id = #{statementId}
    </update>

    <select id="queryStatementTotalAmount" parameterType="com.tiangong.finance.remote.statement.request.StatementIdDTO"
      resultType="com.tiangong.finance.remote.statement.request.QueryStatementTotalAmountDTO">
            SELECT
                so.statement_id statementId,
                so.currency currency,
                sum( so.receive_amt ) amount
            FROM
                f_agent_statement_order so
            WHERE
        <if test='statementId != null and statementId !="" '>
            so.statement_id = #{statementId}
        </if>
            GROUP BY
                so.statement_id,
                so.currency
    </select>

    <select id="queryNewStatementAmount" parameterType="com.tiangong.finance.remote.statement.request.StatementIdDTO"
            resultType="java.math.BigDecimal">
        SELECT
            sum(o.order_amt - IFNULL(f.received_amt,0))
        FROM
            f_agent_statement_order so INNER JOIN o_order o ON so.order_code = o.order_code
            INNER JOIN o_order_finance f ON o.id = f.order_id
        WHERE so.statement_id=#{statementId}
    </select>

    <select id="queryStatementUpdateOrder" parameterType="com.tiangong.finance.remote.statement.request.StatementIdDTO"
        resultType="java.lang.Integer">
        SELECT
            count(1)
        FROM
            f_agent_statement_order so INNER JOIN o_order o ON so.order_code = o.order_code
                                       INNER JOIN o_order_finance f ON o.id = f.order_id
        WHERE so.statement_id=#{statementId}
          AND so.unreceived_amt!=o.order_amt-f.received_amt
    </select>

    <update id="updateOrderFinance" parameterType="com.tiangong.finance.statement.dto.UpdateOrderFinanceDTO">
        UPDATE f_agent_statement_order so INNER JOIN o_order o ON so.order_code=o.order_code
        INNER JOIN o_order_finance f ON o.id = f.order_id
        <trim prefix="set" suffixOverrides=",">
            <if test="checkStatus != null">
                f.check_status = #{checkStatus},
            </if>
            <if test="isUpdateSettlementStatus != null and isUpdateSettlementStatus == 1">
                f.settlement_status =
                (CASE WHEN (f.unreceived_amt-so.receive_amt)!=0 THEN
                0
                ELSE
                1
                END),
                f.settlement_date =
                (CASE WHEN (f.unreceived_amt+so.received_amt)!=o.order_amt THEN
                f.settlement_date
                ELSE
                SYSDATE()
                END),
                f.check_status =
                (CASE WHEN (f.received_amt+so.receive_amt)!=o.order_amt THEN
                1
                ELSE
                <if test="checkStatus != null">
                    #{checkStatus}
                </if>
                <if test="checkStatus == null">
                    f.check_status
                </if>
                END),
            </if>
            <if test="isUpdateSettlementAmount != null and isUpdateSettlementAmount == 1">
                f.received_amt=f.received_amt+so.receive_amt,
                f.unreceived_amt=f.unreceived_amt-so.receive_amt,
            </if>
            <if test="financeLockStatus != null">
                f.finance_lock_status= #{financeLockStatus},
            </if>
        </trim>
        WHERE so.statement_id=#{statementId}
        <if test="orderIdList != null and orderIdList.size > 0">
            AND o.id IN
            <foreach collection="orderIdList" item="item" open="(" separator="," close=")">#{item}</foreach>
        </if>
        <if test="statementOrderIdList != null and statementOrderIdList.size > 0">
            AND so.id IN
            <foreach collection="statementOrderIdList" item="item" open="(" separator="," close=")">#{item}</foreach>
        </if>
    </update>

    <select id="queryUnCheckOutOrder" parameterType="com.tiangong.finance.remote.statement.request.QueryUnCheckOutOrderDTO"
            resultType="com.tiangong.finance.remote.statement.response.UnCheckOutOrderDTO">
      SELECT
        o.id orderId,
        o.order_code orderCode,
        o.channel_order_code channelOrderCode,
        o.hotel_name hotelName,
        o.room_name roomName,
        o.guest guest,
        o.start_date startDate,
        o.end_date endDate,
        o.room_qty roomQty,
        o.sale_currency currency,
        o.order_amt receivableAmt,
        f.received_amt receivedAmt,
        o.order_amt-f.received_amt unreceivedAmt,
        o.created_dt createdDt,
        o.sale_rate saleRate,
        o.org_to_agent_rate orgToAgentRate,
        o.order_confirmation_status confirmationStatus,
        o.city_name cityName,
        IF( ( o.hourly = 1 ), 0.5, 0+DATEDIFF ( o.end_date, o.start_date ) ) nightQty
    FROM o_order o INNER JOIN o_order_finance f ON o.id=f.order_id
    WHERE o.company_code=#{companyCode}
        AND o.settlement_type!=3
        AND o.order_confirmation_status IN (1,2,3)
        AND f.check_status=1
        AND o.order_amt - f.received_amt!=0
        <choose>
            <when test="dateQueryType==0">
                <if test="startDate!=null and startDate!=''">
                    AND o.created_dt >= #{startDate}
                </if>
                <if test="endDate!=null and endDate!=''">
                    AND o.created_dt &lt; date_add(#{endDate},interval 1 day)
                </if>
            </when>
            <when test="dateQueryType==1">
                <if test="startDate!=null and startDate!=''">
                    AND o.start_date >= #{startDate}
                </if>
                <if test="endDate!=null and endDate!=''">
                    AND o.start_date &lt;= #{endDate}
                </if>
            </when>
            <when test="dateQueryType==2">
                <if test="startDate!=null and startDate!=''">
                    AND o.end_date >= #{startDate}
                </if>
                <if test="endDate!=null and endDate!=''">
                    AND o.end_date &lt;= #{endDate}
                </if>
            </when>
        </choose>
        <if test="orderCode!=null and orderCode!=''">
            AND o.order_code=#{orderCode}
        </if>


        <if test="channelOrderCode!=null and channelOrderCode!=''">
            AND o.channel_order_code LIKE "%"#{channelOrderCode}"%"
        </if>
        <if test="agentCode!=null and agentCode!=''">
            AND o.agent_code LIKE "%"#{agentCode}"%"
        </if>
        <if test="agentName!=null and agentName!=''">
            AND o.agent_name LIKE "%"#{agentName}"%"
        </if>
        <if test="hotelId!=null and hotelId!=''">
            AND o.hotel_id = #{hotelId}
        </if>

        ORDER BY o.created_dt DESC
    </select>

    <select id="queryUpdatedStatementOrderList" parameterType="com.tiangong.finance.remote.statement.request.QueryUpdatedStatementOrderListDTO"
            resultType="com.tiangong.finance.remote.statement.response.UpdatedStatementOrderDTO">
        SELECT
          so.order_code orderCode,
          o.channel_order_code channelOrderCode,
          o.hotel_name hotelName,
          o.room_name roomName,
          log.operatedBy,
          CONCAT(if(so.room_name!=o.room_name,CONCAT('将房型修改为 "',o.room_name,'"'),''),
            if(so.receivable_amt!=o.order_amt-f.received_amt,CONCAT('将订单金额从 "',
            TRIM(TRAILING '.' FROM TRIM(TRAILING '0' FROM so.unreceived_amt)),'修改为 "',
            TRIM(TRAILING '.' FROM TRIM(TRAILING '0' FROM o.order_amt-f.received_amt)),'"'),''),
            if(so.guest!=o.guest,CONCAT('将入住人修改为 "',o.guest,'"'),''))content
        FROM f_agent_statement_order so INNER JOIN o_order o ON so.order_code=o.order_code
        INNER JOIN o_order_finance f ON o.id = f.order_id
        INNER JOIN (SELECT l.order_id,l.created_by operatedBy FROM o_order_log l WHERE l.id IN (SELECT max(id) FROM o_order_log GROUP BY order_id)) log
        ON o.id=log.order_id
        WHERE so.statement_id=#{statementId}
        AND (so.room_name!=o.room_name
        OR so.unreceived_amt!=o.order_amt-f.received_amt
        OR so.guest!=o.guest)
        ORDER BY so.id DESC
    </select>

    <delete id="deleteAgentStatementOrderList">
        DELETE FROM f_agent_statement_order WHERE id in
        <foreach collection="list" item="item" open="(" separator="," close=")">#{item}</foreach>
    </delete>

    <select id="selectAgentStatementOrderListByOrderId" resultType="java.lang.Integer">
        select order_id FROM f_agent_statement_order WHERE id in
        <foreach collection="list" item="item" open="(" separator="," close=")">#{item}</foreach>
    </select>

    <insert id="saveBatchStatementOrder2">
        INSERT INTO f_agent_statement_order (
        statement_id,
        order_id,
        order_code,
        hotel_name,
        room_name,
        product_name,
        guest,
        start_date,
        end_date,
        room_qty,
        currency,
        receivable_amt,
        received_amt,
        unreceived_amt,
        receive_amt,
        order_create_date,
        confirmation_status,
        created_by,
        created_dt,
        swiping_card_amount,
        swiping_card_amount_currency,
        swiping_card_rate,
        swiping_card_dt,
        settle_amount,
        settle_amount_currency,
        collection_rate,
        collection_amount,
        rate_difference_value,
        settlement_cost_error_desc
        ) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.statementId},
            #{item.supplyOrderId},
            #{item.supplyOrderCode},
            #{item.hotelName},
            #{item.roomName},
            #{item.productName},
            #{item.guest},
            #{item.startDate},
            #{item.endDate},
            #{item.roomQty},
            #{item.baseCurrency},
            #{item.receivableAmt},
            #{item.receivedAmt},
            #{item.amt},
            #{item.receiveAmt},
            #{item.createdDt},
            #{item.confirmationStatus},
            #{item.operator},
            SYSDATE(),
            #{item.swipingCardAmount},
            #{item.swipingCardAmountCurrency},
            #{item.swipingCardRate},
            #{item.swipingCardDt},
            #{item.settleAmount},
            #{item.settleAmountCurrency},
            #{item.collectionRate},
            #{item.collectionAmount},
            #{item.rateDifferenceValue},
            #{item.settlementCostErrorDesc})
        </foreach>
    </insert>


    <update id="updateOrderFinance2">
        UPDATE f_agent_statement_order so INNER JOIN o_order_finance f
        <trim prefix="set" suffixOverrides=",">
            <if test="checkStatus != null">
                f.check_status = #{checkStatus},
            </if>
            <if test="financeLockStatus != null">
                f.finance_lock_status= #{financeLockStatus},
            </if>
        </trim>
        WHERE so.statement_id=#{statementId}
    </update>

    <select id="queryOrderCodeByOrderId" resultType="java.lang.String">
        select order_code from o_order where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">#{item}</foreach>
    </select>

    <select id="querySupplyOrderCodeByOrderId" resultType="java.lang.String">
        select supply_order_code from o_supply_order where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">#{item}</foreach>
    </select>

    <update id="updateStatementOrderReceiveAmtById">
        UPDATE f_agent_statement_order
        SET receive_amt = #{receiveAmt}
        WHERE id = #{statementOrderId}
    </update>

    <!--获取账单明细-->
    <select id="getStatementOrderByOrderCode" resultType="com.tiangong.finance.statement.domain.StatementOrderPO">
        SELECT
        #{statementId} statementId,
        o.id supplyOrderId,
        o.order_code supplyOrderCode,
        o.hotel_name hotelName,
        o.room_name roomName,
        o.product_name productName,
        o.guest guest,
        o.start_date startDate,
        o.end_date endDate,
        o.room_qty roomQty,
        o.sale_currency baseCurrency,
        o.order_amt receivableAmt,
        f.received_amt receivedAmt,
        o.order_amt-f.received_amt amt,
        o.created_dt createdDt,
        o.order_confirmation_status confirmationStatus,
        #{operator} operator,
        SYSDATE() sysDate
        FROM o_order o INNER JOIN o_order_finance f ON o.id = f.order_id
        WHERE o.company_code=#{companyCode}
            AND o.order_code = #{orderCode}
    </select>

    <select id="selectStatementOrderByStatementCode" resultType="com.tiangong.finance.statement.domain.AgentStatementOrderPO">
        SELECT
            t2.statement_id,
            t2.order_id,
            t2.order_code,
            t2.hotel_name,
            t2.room_name,
            t2.product_name,
            t2.guest,
            t2.start_date,
            t2.end_date,
            t2.room_qty,
            t2.currency,
            t2.receivable_amt,
            t2.received_amt,
            t2.unreceived_amt,
            t2.receive_amt,
            t2.order_create_date,
            t2.confirmation_status,
            t2.created_by,
            t2.created_dt,
            t2.swiping_card_amount,
            t2.swiping_card_amount_currency,
            t2.swiping_card_rate,
            t2.swiping_card_dt,
            t2.settle_amount,
            t2.settle_amount_currency,
            t2.collection_rate,
            t2.collection_amount,
            t2.rate_difference_value,
            t2.settlement_cost_error_desc
        FROM f_agent_statement t1
        LEFT JOIN f_agent_statement_order t2 ON t1.id = t2.statement_id
        WHERE t1.statement_code = #{statementCode}
    </select>
</mapper>