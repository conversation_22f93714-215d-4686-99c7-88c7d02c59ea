<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tiangong.finance.mapper.ExchangeRateMapper">

    <select id="queryExchangeRate" parameterType="com.tiangong.dto.exchange.QueryExchangeReq"
            resultType="com.tiangong.dto.exchange.ExchangeRateDTO">
       SELECT
	     e.exchangerate_Id exchangeRateId,
	     e.original_currency originalCurrency,
	     e.target_currency targetCurrency,
		 original.content originalCurrencyName,
		 target.content targetCurrencyName,
	     e.rate,
	     e.updated_dt updatedDt,
	     e.created_by createdBy,
	     e.created_dt createdDt,
	     e.updated_by updatedBy,
	     e.adjustment_type,
	     e.modified_amt,
	     e.exchange_rate,
	     e.auto_sync,
	     e.active
        FROM
	      t_exchangerate e
			INNER JOIN t_dictionary original ON original.type = 'coin'
				AND original.t_value = e.original_currency
			INNER JOIN t_dictionary target ON target.type = 'coin'
				AND target.t_value = e.target_currency
		WHERE
		e.active=1 and e.company_code = #{req.companyCode}
         <if test="req.originalCurrency != null and req.originalCurrency!=''">
         AND e.original_currency = #{req.originalCurrency}
		 </if>
		<if test="req.targetCurrency != null and req.targetCurrency!=''">
			AND e.target_currency = #{req.targetCurrency}
		</if>
        ORDER BY  e.exchangerate_Id   DESC
    </select>
</mapper>