//package com.tiangong.supplierdirect;
//
//import com.tiangong.supplierdirect.job.CleanHistorySupplyHotelLowestPriceJob;
//import com.tiangong.supplierdirect.service.SupplyDirectBaseInfoService;
//import com.tiangong.supply.direct.entity.request.QueryCityReq;
//import com.tiangong.supply.direct.enums.SupplierDirectClass;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringRunner;
//
//import javax.annotation.Resource;
//
//@SpringBootTest
//@RunWith(SpringRunner.class)
//public class CleanHistorySupplyHotelLowestPriceJobTest {
//
//    @Resource
//    private CleanHistorySupplyHotelLowestPriceJob cleanHistorySupplyHotelLowestPriceJob;
//
//    @Resource
//    private SupplyDirectBaseInfoService supplyDirectBaseInfoService;
//
//    @Test
//    public void cleanHistorySupplyHotelLowestPriceJob() {
//        cleanHistorySupplyHotelLowestPriceJob.cleanHistorySupplyHotelLowestPriceJob(null);
//    }
//
//    @Test
//    public void querySupplyCity() {
//        QueryCityReq queryCityReq = new QueryCityReq();
//        queryCityReq.setSupplyClass(SupplierDirectClass.CTRIP_UNION.supplierDirectClass);
//        queryCityReq.setPageNum(0);
//        queryCityReq.setPageSize(100);
//        supplyDirectBaseInfoService.querySupplyCity(queryCityReq);
//    }
//}