server:
  port: 14006

spring:
  main:
    allow-bean-definition-overriding: true
    jackson:
      time-zone: GMT+8
  application:
    name: tiangong-supply-direct-common-server
  profiles:
    active: pro

#mybatis 分页插件
pagehelper:
  support-methods-arguments: true
  params: count=countSql
  reasonable: false
  helper-dialect: mysql
  page-size-zero: true

mybatis:
  mapper-locations: classpath*:/mappers/*Mapper.xml
  type-aliases-package: com.tiangong.supplierdirect.entity.*

mapper:
  mappers: com.tiangong.dto.common.MyMapper
  not-empty: false
  identity: MYSQL

logging:
  level:
    com:
      alibaba:
        nacos: info

ribbon:
  ReadTimeout: 60000
  ConnectTimeout: 60000

#xxljob配置
xxl:
  job:
    admin:
      addresses: http://127.0.0.1:18880/xxl-job-admin/
    accessToken: default_token
    executor:
      appname: tiangong-supply-direct-common
      address:
      logpath: ./applogs/tiangong-supply-direct-common
      logretentiondays: 3
      port: 0


logstash:
  url: ************:5044

