package com.tiangong.supplierdirect.util;

import com.tiangong.supply.direct.entity.request.ChildrenInfo;
import com.tiangong.supply.direct.entity.request.RoomGuestNumber;
import com.tiangong.util.CollUtilX;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 工具类
 */
@Slf4j
public class ConvertUtils {

    /**
     * 拼接房间信息
     * @param roomGuestNumbers 房间信息
     * @return 拼接字符
     */
    public static String roomAppend(List<RoomGuestNumber> roomGuestNumbers) {
        StringBuilder roomStr = new StringBuilder();
        if (CollUtilX.isNotEmpty(roomGuestNumbers)) {
            for (int i = 0; i < roomGuestNumbers.size(); i++) {
                RoomGuestNumber roomGuestNumber = roomGuestNumbers.get(i);
                if (roomGuestNumber.getRoomIndex() != null) {
                    roomStr.append(roomGuestNumber.getRoomIndex());
                }
                if (roomGuestNumber.getAdultNum() != null) {
                    roomStr.append(roomGuestNumber.getAdultNum());
                }
                if (CollUtilX.isNotEmpty(roomGuestNumber.getChildrenInfos())) {
                    for (int j = 0; j < roomGuestNumber.getChildrenInfos().size(); j++) {
                        ChildrenInfo childrenInfo = roomGuestNumber.getChildrenInfos().get(j);
                        if (childrenInfo.getChildrenAge() != null) {
                            roomStr.append(childrenInfo.getChildrenAge());
                            if (j != roomGuestNumber.getChildrenInfos().size() - 1) {
                                roomStr.append(",");
                            }
                        }
                    }
                }
                if (i != roomGuestNumbers.size() - 1 && roomStr.length() != 0) {
                    roomStr.append(";");
                }
            }
        }
        return roomStr.toString();
    }
}
