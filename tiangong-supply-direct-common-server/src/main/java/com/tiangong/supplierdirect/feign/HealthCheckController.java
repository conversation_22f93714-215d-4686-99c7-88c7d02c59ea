package com.tiangong.supplierdirect.feign;

import com.tiangong.cloud.common.anno.AnonymousAccess;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @create 2024/1/16 11:01
 */
@RestController
public class HealthCheckController {

    @GetMapping("/health")
    @AnonymousAccess
    public String healthCheck() {
        // 这里可以添加更多的健康检查逻辑
        return "OK";
    }
}
