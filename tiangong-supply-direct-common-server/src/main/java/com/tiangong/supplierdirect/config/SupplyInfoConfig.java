package com.tiangong.supplierdirect.config;

import cn.hutool.core.map.MapUtil;
import com.google.common.collect.Lists;
import com.tiangong.keys.RedisKey;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.supply.direct.entity.base.SupplyConfigDTO;

import java.util.List;
import java.util.Map;
import java.util.Objects;

public class SupplyInfoConfig {

    /**
     * 根据编码获取类型
     */
    public static String getSupplyClassByCode(String supplyCode) {
        return (String) RedisTemplateX.hashGet(RedisKey.SUPPLYCODE_CLASS, supplyCode);
    }


    /**
     * 根据类型获取获取编码
     */
    public static List<String> getSupplyCodeByClass(String supplyClass) {
        Map<Object, Object> hmget = RedisTemplateX.hGetAll(RedisKey.SUPPLYCODE_CLASS);
        List<String> supplyCodes = Lists.newArrayList();
        if (MapUtil.isNotEmpty(hmget)) {
            hmget.forEach((key, value) -> {
                if (Objects.equals(supplyClass, String.valueOf(value))) {
                    supplyCodes.add(String.valueOf(value));
                }
            });
        }
        return supplyCodes;
    }

    /**
     * 获取所有供应商编码
     */
    public static List<String> getAllSupplyCode() {
        Map<Object, Object> hmget = RedisTemplateX.hGetAll(RedisKey.SUPPLYCODE_CLASS);
        List<String> supplyCodes = Lists.newArrayList();
        if (MapUtil.isNotEmpty(hmget)) {
            hmget.forEach((key, value) -> supplyCodes.add(String.valueOf(key)));
        }
        return supplyCodes;
    }


    /**
     * 根据编码获取供应商配置
     */
    public static SupplyConfigDTO getSupplyConfigDTO(String supplyCode) {
        String supplyClass = (String) RedisTemplateX.hashGet(RedisKey.SUPPLYCODE_CLASS, supplyCode);
        if (supplyClass == null) {//判断不存在的情况
            return null;
        }
        SupplyConfigDTO supplyConfigDTO = new SupplyConfigDTO();
        supplyConfigDTO.setSupplyClass(supplyClass);
        supplyConfigDTO.setSupplyCode(supplyCode);
        return supplyConfigDTO;
    }


}
