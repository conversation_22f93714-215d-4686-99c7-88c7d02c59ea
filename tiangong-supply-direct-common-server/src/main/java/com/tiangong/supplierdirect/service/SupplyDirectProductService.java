package com.tiangong.supplierdirect.service;

import com.tiangong.common.Response;
import com.tiangong.supply.direct.entity.dto.ProductDetailDTO;
import com.tiangong.supply.direct.entity.dto.ProductMiddleDto;
import com.tiangong.supply.direct.entity.request.QuerySupplyProductRequest;

import java.util.List;

public interface SupplyDirectProductService {

    /**
     * 查询产品信息
     */
    Response<ProductDetailDTO> queryProductInfo(QuerySupplyProductRequest querySupplyProductRequest);


    /**
     * 查询产品供应商信息
     *
     */
    Response<List<ProductMiddleDto>> querySupplyProductByCode(QuerySupplyProductRequest querySupplyProductRequest);
}
