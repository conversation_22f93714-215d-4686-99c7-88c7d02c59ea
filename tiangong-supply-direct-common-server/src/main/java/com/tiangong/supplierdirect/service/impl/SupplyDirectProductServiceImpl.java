package com.tiangong.supplierdirect.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.tiangong.common.Response;
import com.tiangong.enums.*;
import com.tiangong.keys.RedisKey;
import com.tiangong.organization.remote.dto.SupplierAddDTO;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.supplierdirect.routing.RoutingStrategy;
import com.tiangong.supplierdirect.service.SupplyDirectProductService;
import com.tiangong.supplierdirect.util.ConvertUtils;
import com.tiangong.supply.direct.constant.SupplyDirectRedisCacheKey;
import com.tiangong.supply.direct.entity.dto.*;
import com.tiangong.supply.direct.entity.request.QueryProductInfoRequest;
import com.tiangong.supply.direct.entity.request.QuerySupplyProductRequest;
import com.tiangong.supply.direct.interfaces.BaseSupplyDirectProductRemote;
import com.tiangong.util.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.StopWatch;

import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class SupplyDirectProductServiceImpl implements SupplyDirectProductService {

    private final RoutingStrategy routingStrategy;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private SlsLoggerUtil slsLoggerUtil;

    /**
     * 查询产品信息  无论如何都会返回成功的对象 错误日志会打印 封装时为空尽量不要返回new 对象，防止后续判断问题
     */
    @Override
    public Response<ProductDetailDTO> queryProductInfo(QuerySupplyProductRequest querySupplyProductRequest) {
        ProductDetailDTO productDetailDTO = new ProductDetailDTO();
        productDetailDTO.setHotelId(querySupplyProductRequest.getHotelId());
        StopWatch stopWatch = new StopWatch();
        try {
            // 1.根据agentCode查询供应商白名单配置
            Date start1 = new Date();
            stopWatch.start("1.查询供应商白名单");
            List<String> supplyCodes = null;
            if (querySupplyProductRequest.getAgentCode() != null) {
                supplyCodes = getAgentSupplyAvailable(querySupplyProductRequest.getAgentCode());
                if (CollUtilX.isEmpty(supplyCodes)) {
                    stopWatch.stop();//不使用Assert.isTrue 是由于stopWatch要stop
                    //response.setFailReason("参数错误 分销商编码白名单不能为空");
                    log.error("Exception 参数错误 分销商编码白名单不能为空 param=" + JSONObject.toJSONString(querySupplyProductRequest));
                    return Response.error(ParamErrorEnum.EMPTY_PARAM_AGENTWHITELIST.getCode(), ParamErrorEnum.EMPTY_PARAM_AGENTWHITELIST.getDesc(), productDetailDTO);
                }
            } else {
                if (querySupplyProductRequest.getSupplierCode() != null) {
                    supplyCodes = new ArrayList<>();
                    supplyCodes.add(querySupplyProductRequest.getSupplierCode());
                } else {
                    stopWatch.stop();
                    //response.setFailReason("分销商编码不能为空");
                    log.error("Exception 参数错误 getSupplierCode 分销商编码不能为空 param=" + JSONObject.toJSONString(querySupplyProductRequest));
                    return Response.error(ParamErrorEnum.EMPTY_PARAM_SUPPLYCODE.getCode(), ParamErrorEnum.EMPTY_PARAM_SUPPLYCODE.getDesc(), productDetailDTO);
                }
            }

            // 获取供应商信息
            List<SupplierAddDTO> supplierAddList = StrUtilX.parseObject(RedisTemplateX.hMultiGet(RedisKey.SUPPLY_ACCOUNT_CONFIG, supplyCodes), new TypeReference<List<SupplierAddDTO>>() {
            });
            if (CollUtilX.isEmpty(supplierAddList)) {
                log.error("queryProductInfo 查询供应商产品，供应商缓存不存在");
                return Response.error(ErrorCodeEnum.SUPPLY_CHECK_NOT_EXIST.errorCode, ErrorCodeEnum.SUPPLY_CHECK_NOT_EXIST.errorDesc, productDetailDTO);
            }
            // 比较当前时间
            Date currentDate = DateUtilX.getCurrentDate();
            long day = DateUtilX.getDay(DateUtilX.stringToDate(querySupplyProductRequest.getCheckInDate()), currentDate);
            if (day > 1) {
                Map<String, String> map = new HashMap<>();
                map.put(SlsEnum.LEVEL.getType(), SlsLogLevelEnum.INFO.getLevel());
                map.put(SlsEnum.NAME.getType(), "queryProductInfo");
                map.put(SlsEnum.MESSAGE.getType(), "查询供应商产品，入住时间小于当前时间两天");

                map.put("request", JSONUtil.toJsonStr(querySupplyProductRequest));
                map.put("start", DateUtilX.dateToString(start1, DateUtilX.hour_ms_format));
                map.put("data", "currentDate=" + DateUtilX.dateToString(currentDate) + "checkInData=" + querySupplyProductRequest.getCheckInDate());
                slsLoggerUtil.saveLog(map, "supply-queryProductInfo", "tingong-supply-direct-common-server");
                return Response.success(productDetailDTO);
            }
            // 获取当前时间
            LocalTime now = LocalTime.now();
            // 定义时间范围的开始和结束时间
            LocalTime startTime = LocalTime.MIDNIGHT;// 0点
            LocalTime endTime = LocalTime.of(6, 0);// 6点

            // 过滤单体供应商酒店是否为当前查询的酒店 供应商是否支持凌晨房订单，不支持入住时间不能大于当前时间
            supplyCodes = supplierAddList.stream().filter(item -> (item.getSupplierForm() == null || item.getHotelId() == null
                    || !item.getSupplierForm().equals(1) || querySupplyProductRequest.getHotelId().equals(item.getHotelId()))
                    && item.getAvailableStatus() != null && item.getAvailableStatus() == 1 &&
                    (((item.getIsSupportEarlyMorningRoomOrder() == null || item.getIsSupportEarlyMorningRoomOrder() == 0) && day < 1) ||
                            (item.getIsSupportEarlyMorningRoomOrder() != null && item.getIsSupportEarlyMorningRoomOrder() == 1 &&
                                    (day < 1 || (now.isAfter(startTime) && now.isBefore(endTime)))))).map(SupplierAddDTO::getSupplierCode).collect(toList());
            saveSlsLog("排查慢问题：供应商公共服务，获取供应商白名单", start1, querySupplyProductRequest.getRequestId());
            if (CollUtilX.isEmpty(supplyCodes)) {
                Map<String, String> map = new HashMap<>();
                map.put(SlsEnum.LEVEL.getType(), SlsLogLevelEnum.INFO.getLevel());
                map.put(SlsEnum.NAME.getType(), "queryProductInfo");
                map.put(SlsEnum.MESSAGE.getType(), "查询供应商产品，不存在供应商可见");

                map.put("request", JSONUtil.toJsonStr(querySupplyProductRequest));
                map.put("start", DateUtilX.dateToString(start1, DateUtilX.hour_ms_format));

                slsLoggerUtil.saveLog(map, "supply-queryProductInfo", "tingong-supply-direct-common-server");
                return Response.success(productDetailDTO);
            }

            stopWatch.stop();
            // 4.获取组装后的产品信息
            Date start2 = new Date();
            stopWatch.start("3.获取所有产品信息");
            List<ProductDetailDTO> productMiddleDtoList = getAllProduct(querySupplyProductRequest, supplyCodes);
            saveSlsLog("排查慢问题：供应商公共服务，获取所有产品信息", start2, querySupplyProductRequest.getRequestId());
            stopWatch.stop();
            if (CollUtilX.isEmpty(productMiddleDtoList)) {
                Map<String, String> map = new HashMap<>();
                map.put(SlsEnum.LEVEL.getType(), SlsLogLevelEnum.INFO.getLevel());
                map.put(SlsEnum.NAME.getType(), "queryProductInfo");
                map.put(SlsEnum.MESSAGE.getType(), "查询供应商产品，供应商返回产品为空");

                map.put("request", JSONUtil.toJsonStr(querySupplyProductRequest));
                map.put("start", DateUtilX.dateToString(start2, DateUtilX.hour_ms_format));

                slsLoggerUtil.saveLog(map, "supply-queryProductInfo", "tingong-supply-direct-common-server");
                return Response.success(productDetailDTO);
            }
            // 5.返回结果聚合
            Date start3 = new Date();
            stopWatch.start("4.聚合返回结果");
            productDetailDTO = aggregationResult(productMiddleDtoList, querySupplyProductRequest.getHotelId());
            stopWatch.stop();
            saveSlsLog("排查慢问题：供应商公共服务，聚合返回结果", start3, querySupplyProductRequest.getRequestId());
        } catch (Exception e) {
            stopWatch.stop();
            log.error("查询供应商产品异常 Exception:", e);
        }
        return Response.success(productDetailDTO);
    }

    /**
     * 查询产品供应商信息
     */
    @Override
    public Response<List<ProductMiddleDto>> querySupplyProductByCode(QuerySupplyProductRequest querySupplyProductRequest) {
        String productId = querySupplyProductRequest.getSpProductId().substring(0, querySupplyProductRequest.getSpProductId().lastIndexOf("#"));
        List<ProductMiddleDto> productMiddleDtos = new ArrayList<>();
        ProductMiddleDto productMiddleDto = null;
        if (querySupplyProductRequest.getOnlyHourRoom() == 1) {//钟点房
            productMiddleDto = JSON.parseObject(
                    RedisTemplateX.get(SupplyDirectRedisCacheKey.PREBOOKING_CACHE.concat(querySupplyProductRequest.getSupplierCode())
                            .concat(StrUtilX.SPLIT_CODE).concat(productId)
                            .concat(StrUtilX.SPLIT_CODE).concat(querySupplyProductRequest.getCheckInDate())),
                    new TypeReference<ProductMiddleDto>() {
                    });
            productMiddleDtos.add(productMiddleDto);
        } else {
            // 拼接房间信息
            String roomStr = ConvertUtils.roomAppend(querySupplyProductRequest.getRoomGuestNumbers());
            StringBuilder productPriceKey = new StringBuilder().append(SupplyDirectRedisCacheKey.PREBOOKING_CACHE)
                    .append(querySupplyProductRequest.getSupplierCode()).append(StrUtilX.SPLIT_CODE).append(productId).append(StrUtilX.SPLIT_CODE);
            // 房间不为空
            if (roomStr.length() != 0) {
                productPriceKey.append(roomStr).append(StrUtilX.SPLIT_CODE);
            }
            productPriceKey.append(querySupplyProductRequest.getCheckInDate())
                    .append(StrUtilX.CHARACTER).append(querySupplyProductRequest.getCheckOutDate());
            productMiddleDto = JSON.parseObject(
                    RedisTemplateX.get(productPriceKey.toString()),
                    new TypeReference<ProductMiddleDto>() {
                    });
            // 为空再执行一次实时查询，防止房间信息顺序不对导致查询不到缓存直接返回空
            if (productMiddleDto == null) {
                log.info("根据产品id查询供应商产品信息缓存为空，需要调用供应商接口重新查询，key={}", productPriceKey);
                // 调用供应商产品接口
                List<String> supplyCodeList = new ArrayList<>();
                supplyCodeList.add(querySupplyProductRequest.getSupplierCode());
                querySupplyProductRequest.setLanguage(LanguageTypeEnum.zh_CN.getValue());
                productMiddleDtos = queryProductInfos(querySupplyProductRequest, supplyCodeList, productId);
            } else {
                productMiddleDtos.add(productMiddleDto);
            }
        }
        if (CollUtilX.isNotEmpty(productMiddleDtos)) {
            return Response.success(productMiddleDtos);
        } else {
            return Response.error(ErrorCodeEnum.SUPPLY_NO_HAS_PRODUCT.errorCode, ErrorCodeEnum.SUPPLY_NO_HAS_PRODUCT.errorDesc);
        }
    }

    /**
     * 获取供应商原始产品信息
     */
    private List<ProductDetailDTO> getAllProduct(QuerySupplyProductRequest querySupplyProductRequest, List<String> supplyCodes) {
        // 调用供应商产品接口
        List<ProductMiddleDto> productMiddleDtos = queryProductInfos(querySupplyProductRequest, supplyCodes, null);
        // 组装结果集
        ProductDetailDTO productDetailDTO = assembleResult(productMiddleDtos, querySupplyProductRequest.getHotelId());

        return (productDetailDTO != null) ? Collections.singletonList(productDetailDTO) : null;
    }

    /**
     * 获取产品信息
     */
    private List<ProductMiddleDto> queryProductInfos(QuerySupplyProductRequest querySupplyProductRequest, List<String> supplyCodes, String spProductId) {
        QueryProductInfoRequest queryProductInfoRequest = new QueryProductInfoRequest();
        queryProductInfoRequest.setSupplyClass(SupplierDirectClass.SHUB.supplierDirectClass);
        queryProductInfoRequest.setSpHotelId(querySupplyProductRequest.getHotelId() == null ? null : String.valueOf(querySupplyProductRequest.getHotelId()));
        queryProductInfoRequest.setSpRoomId(querySupplyProductRequest.getRoomId());
        queryProductInfoRequest.setSpProductId(spProductId);
        queryProductInfoRequest.setCheckInDate(querySupplyProductRequest.getCheckInDate());
        queryProductInfoRequest.setCheckOutDate(querySupplyProductRequest.getCheckOutDate());
        queryProductInfoRequest.setGuestQuantity(querySupplyProductRequest.getGuestQuantity());
        queryProductInfoRequest.setRoomQty(querySupplyProductRequest.getRoomQty());
        queryProductInfoRequest.setSupplyCodes(new HashSet<>(supplyCodes));
        queryProductInfoRequest.setOnlyHourRoom(querySupplyProductRequest.getOnlyHourRoom());
        queryProductInfoRequest.setSupplyType(querySupplyProductRequest.getSupplyType());
        queryProductInfoRequest.setRoomGuestNumbers(querySupplyProductRequest.getRoomGuestNumbers());
        queryProductInfoRequest.setUserAccount(querySupplyProductRequest.getUserAccount());
        queryProductInfoRequest.setRequestId(querySupplyProductRequest.getRequestId());
        queryProductInfoRequest.setCurrency(querySupplyProductRequest.getCurrency());
        queryProductInfoRequest.setLanguage(querySupplyProductRequest.getLanguage());
        queryProductInfoRequest.setNationality(querySupplyProductRequest.getNationality());

        return querySupplyProduct(queryProductInfoRequest);
    }


    /**
     * 组装结果集
     */
    private ProductDetailDTO assembleResult(List<ProductMiddleDto> result, Long hotelId) {
        if (CollUtilX.isEmpty(result)) {
            return null;
        }
        try {
            return assembleProductDetailDTO(result, hotelId);
        } catch (Exception e) {
            log.error("组装返回结果集异常", e);
        }
        return null;
    }


    /**
     * 调用供应商产品接口
     * 优化点: 查询供应商产品的时候,缓存设置过期时间
     * 再次查询的时候,先从缓存中取,如果缓存中没有,
     * 再去请求供应商产品接口
     */
    private List<ProductMiddleDto> querySupplyProduct(QueryProductInfoRequest queryProductInfoRequest) {
        BaseSupplyDirectProductRemote routingProductRemote = routingStrategy.getRoutingProductRemote(queryProductInfoRequest.getSupplyClass());
        Assert.notNull(routingProductRemote, String.format("找不到供应商%s的路由remote", queryProductInfoRequest.getSupplyClass()));
        try {
            Response<List<ProductMiddleDto>> response = routingProductRemote.queryProductList(queryProductInfoRequest);
            if (CollUtilX.isNotEmpty(response.getModel())) {
                return response.getModel();
            }
        } catch (Exception e) {
            log.error("调用供应商产品接口异常", e);
        }
        return null;
    }

    /**
     * 根据分销商编码查询供应商白名单
     */
    private List<String> getAgentSupplyAvailable(String agentCode) {
        String supplyCodes = (String) RedisTemplateX.hashGet(RedisKey.AGENT_SUPPLY_KEY, agentCode);
        return Optional.ofNullable(supplyCodes).map(e -> e.split(",")).map(Arrays::asList).orElse(new LinkedList<>());
    }

//    /**
//     * 组装产品信息
//     */
//    private ProductDetailDTO assembleProductDetailDTO(List<ProductMiddleDto> allProductMiddleDtos, Long hotelId) {
//        Map<Integer, List<ProductMiddleDto>> roomAllMaps = allProductMiddleDtos.stream()
//                .collect(Collectors.groupingBy(ProductMiddleDto::getHourlyRoom));
//
//        Map<String, String> roomNameMap = allProductMiddleDtos.stream()
//                .collect(Collectors.toMap(ProductMiddleDto::getSpRoomTypeId, ProductMiddleDto::getSpRoomName, (a, b) -> a));
//
//        ProductDetailDTO productDetailDTO = new ProductDetailDTO();
//        productDetailDTO.setHotelId(hotelId);
//
//        for (int i = 0; i < 2; i++) { // 区分分钟点房和日历房
//            List<ProductMiddleDto> productMiddleDtos = roomAllMaps.get(i);
//            if (CollUtilX.isEmpty(productMiddleDtos)) {
//                continue;
//            }
//
//            Map<String, List<ProductMiddleDto>> roomMaps = productMiddleDtos.stream()
//                    .collect(Collectors.groupingBy(ProductMiddleDto::getSpRoomTypeId));
//
//            List<RoomItem> roomItems = roomMaps.entrySet().parallelStream().map(entry -> {
//                RoomItem roomItem = new RoomItem();
//                roomItem.setRoomId(Long.valueOf(entry.getKey()));
//                roomItem.setRoomName(roomNameMap.get(entry.getKey()));
//
//                List<ProductDetail> products = convertToProductDetail(entry.getValue());
//                if (CollUtilX.isNotEmpty(products)) {
//                    roomItem.setProducts(products);
//                }
//                return roomItem;
//            }).collect(toList());
//
//            // 过滤空产品
//            List<RoomItem> newRoomItems = roomItems.stream().filter(item -> CollUtilX.isNotEmpty(item.getProducts())).collect(toList());
//
//            if (i == 0) {
//                productDetailDTO.setRoomItems(newRoomItems);
//            } else if (i == 1) {
//                productDetailDTO.setHourlyRoomItems(newRoomItems);
//            }
//        }
//
//        return productDetailDTO;
//    }

    /**
     * 组装产品信息
     */
    private ProductDetailDTO assembleProductDetailDTO(List<ProductMiddleDto> allProductMiddleDtos, Long hotelId) {
        ProductDetailDTO productDetailDTO = new ProductDetailDTO();
        productDetailDTO.setHotelId(hotelId);
        Map<String, String> roomNameMap = new HashMap<>();
        Map<String, List<ProductDetail>> roomMap = new HashMap<>();
        allProductMiddleDtos.parallelStream().forEach(allProductMiddleDto -> {
            ProductDetail productDetail = convertToProductDetail(allProductMiddleDto);
            if (roomMap.containsKey(allProductMiddleDto.getSpRoomTypeId())) {
                roomMap.get(allProductMiddleDto.getSpRoomTypeId()).add(productDetail);
            } else {
                List<ProductDetail> productDetails = new ArrayList<>();
                productDetails.add(productDetail);
                roomMap.put(allProductMiddleDto.getSpRoomTypeId(), productDetails);
            }
            if (!roomNameMap.containsKey(allProductMiddleDto.getSpRoomTypeId())) {
                roomNameMap.put(allProductMiddleDto.getSpRoomTypeId(), allProductMiddleDto.getSpRoomName());
            }
        });
        List<RoomItem> roomItems = roomMap.entrySet().stream().filter(item -> item.getKey().matches("\\d+")).map(item -> {
            RoomItem roomItem = new RoomItem();
            roomItem.setRoomId(Long.valueOf(item.getKey()));
            roomItem.setRoomName(roomNameMap.get(item.getKey()));
            roomItem.setProducts(item.getValue());
            return roomItem;
        }).collect(toList());
        productDetailDTO.setRoomItems(roomItems);
        return productDetailDTO;
    }


//    /**
//     * 价格计划转换
//     */
//    private List<ProductDetail> convertToProductDetail(List<ProductMiddleDto> productMiddleDtoList) {
//        if (CollUtilX.isEmpty(productMiddleDtoList)) {
//            return null;
//        }
//        return productMiddleDtoList.parallelStream().filter(item -> CollUtilX.isNotEmpty(item.getRoomItemDetails())).map(productMiddleDto -> {
//            ProductDetail productDetail = new ProductDetail();
//            productDetail.setRatePlanId(productMiddleDto.getSpPricePlanId().concat("#").concat(productMiddleDto.getSpPricePlanName()));
//            productDetail.setRatePlanName(productMiddleDto.getSpPricePlanName());
////            productDetail.setBedType(productMiddleDto.getBedType());
//            if (CollUtilX.isNotEmpty(productMiddleDto.getBedTypeDetails())) {
//                List<BedTypesDetailDto> bedTypesDetails = BeanUtil.copyToList(productMiddleDto.getBedTypeDetails(), BedTypesDetailDto.class);
//                productDetail.setBedTypeDetails(bedTypesDetails);
//            }
//            productDetail.setWindowType(productMiddleDto.getWindowType());
//            //统一处理 如果供应商的产品级别 最大入住人数为空 就设置为2 必须存在 无默认值 author:李佳兴 2024-03-02
////            if (productMiddleDto.getMaxGuestPerson() == null || productMiddleDto.getMaxGuestPerson() <= 0) {
////                productMiddleDto.setMaxGuestPerson(2);
////            }
//            productDetail.setMaxGuestPerson(productMiddleDto.getMaxGuestPerson());
//            productDetail.setMinGuestPerson(productMiddleDto.getMinGuestPerson());
//            productDetail.setImmediateConfirm(productMiddleDto.getImmediateConfirm());
//            if (productMiddleDto.getHourlyRoom() == 1) {
//                productDetail.setHourlyRoom(1);
//                productDetail.setHourlyRoomInfo(productMiddleDto.getHourlyRoomInfo());
//            }
//            productDetail.setLabelType(productMiddleDto.getLabelType());
//            productDetail.setAddBed(productMiddleDto.getAddBed());
//            productDetail.setAddBedPrice(productMiddleDto.getAddBedPrice());
//            productDetail.setAddBedPriceDesc(productMiddleDto.getAddBedPriceDesc());
//            productDetail.setAvgPrice(productMiddleDto.getAvgPrice());
//            productDetail.setCertificateFlag(productMiddleDto.getCertificateFlag());
//            productDetail.setBroadNet(productMiddleDto.getBroadBand());
//            productDetail.setSupplyCode(productMiddleDto.getSupplyCode());
//            productDetail.setBookType(productMiddleDto.getBookType());
//            productDetail.setInvoiceModel(productMiddleDto.getInvoiceModel());
//            productDetail.setInvoiceType(productMiddleDto.getInvoiceType());
//            productDetail.setReminder(productMiddleDto.getReminder());
//            productDetail.setPayAtHotelFlag(productMiddleDto.getPayAtHotelFlag());
//            productDetail.setProductLabel(productMiddleDto.getProductLabel());
//            productDetail.setSmallProductLabel(productMiddleDto.getSmallProductLabel());
//            productDetail.setGiftPacks(productMiddleDto.getGiftPacks());//价格礼包
//            List<RoomItemDetail> roomItemDetails = converToRoomItemDetails(productMiddleDto.getRoomItemDetails());
//            productDetail.setRoomItemDetails(roomItemDetails);
//            return productDetail;
//        }).collect(toList());
//    }

    /**
     * 价格计划转换
     */
    private ProductDetail convertToProductDetail(ProductMiddleDto productMiddleDto) {
        ProductDetail productDetail = new ProductDetail();
        productDetail.setRatePlanId(productMiddleDto.getSpPricePlanId().concat("#").concat(productMiddleDto.getSpPricePlanName()));
        productDetail.setRatePlanName(productMiddleDto.getSpPricePlanName());
//            productDetail.setBedType(productMiddleDto.getBedType());
        if (CollUtilX.isNotEmpty(productMiddleDto.getBedTypeDetails())) {
            List<BedTypesDetailDto> bedTypesDetails = BeanUtil.copyToList(productMiddleDto.getBedTypeDetails(), BedTypesDetailDto.class);
            productDetail.setBedTypeDetails(bedTypesDetails);
        }
        productDetail.setWindowType(productMiddleDto.getWindowType());
        //统一处理 如果供应商的产品级别 最大入住人数为空 就设置为2 必须存在 无默认值 author:李佳兴 2024-03-02
//            if (productMiddleDto.getMaxGuestPerson() == null || productMiddleDto.getMaxGuestPerson() <= 0) {
//                productMiddleDto.setMaxGuestPerson(2);
//            }
        productDetail.setMaxGuestPerson(productMiddleDto.getMaxGuestPerson());
        productDetail.setMinGuestPerson(productMiddleDto.getMinGuestPerson());
        productDetail.setImmediateConfirm(productMiddleDto.getImmediateConfirm());
        if (productMiddleDto.getHourlyRoom() == 1) {
            productDetail.setHourlyRoom(1);
            productDetail.setHourlyRoomInfo(productMiddleDto.getHourlyRoomInfo());
        }
        productDetail.setLabelType(productMiddleDto.getLabelType());
        productDetail.setAddBed(productMiddleDto.getAddBed());
        productDetail.setAddBedPrice(productMiddleDto.getAddBedPrice());
        productDetail.setAddBedPriceDesc(productMiddleDto.getAddBedPriceDesc());
        productDetail.setAvgPrice(productMiddleDto.getAvgPrice());
        productDetail.setCertificateFlag(productMiddleDto.getCertificateFlag());
        productDetail.setBroadNet(productMiddleDto.getBroadBand());
        productDetail.setSupplyCode(productMiddleDto.getSupplyCode());
        productDetail.setBookType(productMiddleDto.getBookType());
        productDetail.setInvoiceModel(productMiddleDto.getInvoiceModel());
        productDetail.setInvoiceType(productMiddleDto.getInvoiceType());
        productDetail.setPayAtHotelFlag(productMiddleDto.getPayAtHotelFlag());
        productDetail.setProductLabel(productMiddleDto.getProductLabel());
        productDetail.setSmallProductLabel(productMiddleDto.getSmallProductLabel());
        productDetail.setTips(productMiddleDto.getTips());
        List<RoomItemDetail> roomItemDetails = converToRoomItemDetails(productMiddleDto.getRoomItemDetails());
        productDetail.setRoomItemDetails(roomItemDetails);
        return productDetail;
    }

//    /**
//     * 拼接字符
//     */
//    private String appendBedInfo(List<BedInfoDto> bedInfos, Integer withOr) {
//        if (CollUtilX.isNotEmpty(bedInfos)) {
//            StringBuilder sb = new StringBuilder();
//            boolean isFirstBedInfo = true;
//            for (BedInfoDto bedInfo : bedInfos) {
//                if (StrUtilX.isEmpty(bedInfo.getBedTypeName())) {
//                    continue;
//                }
//
//                if (!isFirstBedInfo) {
//                    if (withOr != null) {
//                        sb.append(withOr == 0 ? "或" : "且");
//                    } else {
//                        sb.append("且");
//                    }
//                }
//
//                // 数量
//                if (bedInfo.getBedNum() != null) {
//                    sb.append(bedInfo.getBedNum()).append("张");
//                }
//                // 床宽
//                if (StrUtilX.isNotEmpty(bedInfo.getBedWidth())) {
//                    sb.append(bedInfo.getBedWidth());
//                }
//                // 床型名称
//                sb.append(bedInfo.getBedTypeName());
//
//                isFirstBedInfo = false;
//            }
//            return sb.toString();
//        }
//        return null;
//    }

    /**
     * 组装每间房每日价格计划
     */
    private List<RoomItemDetail> converToRoomItemDetails(List<RoomItemDetailDto> roomItemDetails) {
        return roomItemDetails.stream().map(roomItemDetail -> {
            RoomItemDetail roomItem = new RoomItemDetail();
            roomItem.setRoomIndex(roomItemDetail.getRoomIndex());
            roomItem.setAdultNum(roomItemDetail.getAdultNum());
            roomItem.setChildAges(roomItemDetail.getChildAges());
            // 每间 价格明细(税费)
            if (roomItemDetail.getTaxDetail() != null) {
                TaxDetailDto taxDetailDto = BeanUtil.copyProperties(roomItemDetail.getTaxDetail(), TaxDetailDto.class);
                roomItem.setTaxDetail(taxDetailDto);
            }
            roomItem.setPayInStorePrice(roomItemDetail.getPayInStorePrice());
            roomItem.setPayInStoreCurrency(roomItemDetail.getPayInStoreCurrency());
            roomItem.setSupplyPayInStorePrice(roomItemDetail.getSupplyPayInStorePrice());
            roomItem.setSupplyPayInStoreCurrency(roomItemDetail.getSupplyPayInStoreCurrency());
            if (CollUtilX.isNotEmpty(roomItemDetail.getProductDetails())) {
                List<PriceItem> priceItems = roomItemDetail.getProductDetails().stream().map(productDetailMiddleDto -> {
                    PriceItem priceItem = new PriceItem();
                    priceItem.setSaleDate(productDetailMiddleDto.getSaleDate());
                    priceItem.setTaxDetail(productDetailMiddleDto.getTaxDetail());
                    priceItem.setBasePrice(productDetailMiddleDto.getBasePrice());
                    priceItem.setBaseCurrency(productDetailMiddleDto.getCurrency());
                    priceItem.setBreakfastType(productDetailMiddleDto.getBreakfastType());
                    priceItem.setBreakfastNum(productDetailMiddleDto.getBreakfastNum());
                    priceItem.setRoomStatus(productDetailMiddleDto.getRoomStatus());
                    priceItem.setOverDraft(productDetailMiddleDto.getOverDraft());
                    priceItem.setQuotaNum(productDetailMiddleDto.getQuotaNum());
                    priceItem.setBookRestrictionType(productDetailMiddleDto.getBookRestrictType());
                    priceItem.setBookRestrictionDay(productDetailMiddleDto.getBookRestrictDays());
                    priceItem.setBookRestrictionTime(productDetailMiddleDto.getBookRestrictTime());
                    priceItem.setOccupancyRestrictionType(productDetailMiddleDto.getOccupancyRestrictType());
                    priceItem.setOccupancyRestrictionDay(productDetailMiddleDto.getOccupancyRestrictDays());
                    priceItem.setMinRoomCount(productDetailMiddleDto.getMinBookRoomsRestrict());
                    priceItem.setMaxRoomCount(productDetailMiddleDto.getMaxBookRoomsRestrict());
                    priceItem.setCancelRestrictions(productDetailMiddleDto.getCancelRestrictions());
                    priceItem.setGuaranteeType(productDetailMiddleDto.getGuaranteeType());
                    priceItem.setGuaranteeCondition(productDetailMiddleDto.getGuaranteeCondition());
                    priceItem.setGuaranteeFeeType(productDetailMiddleDto.getGuaranteeFeeType());
                    return priceItem;
                }).collect(toList());
                roomItem.setPriceItems(priceItems);
            }
            return roomItem;
        }).collect(Collectors.toList());
    }


//    /**
//     * 结果聚合
//     */
//    private ProductDetailDTO aggregationResult(List<ProductDetailDTO> productMiddleDtoList, Long hotelId) {
//        //hotelId 传过来 防止所有供应都没有结果返回导致无法封装酒店ID
//        //只有一个酒店 不存在多酒店情况
//        List<RoomItem> daylyRoomItemList = new ArrayList<>();
//        List<RoomItem> hourlyRoomItemList = new ArrayList<>();
//        //相同酒店的房型整合到一起
//        for (ProductDetailDTO productDetail : productMiddleDtoList) {
//            if (productDetail.getHotelId() == null) {//有的是临时创建的new对象 所以不能使用
//                continue;
//            }
//            if (CollUtilX.isNotEmpty(productDetail.getRoomItems())) {
//                daylyRoomItemList.addAll(productDetail.getRoomItems());
//            }
//            if (CollUtilX.isNotEmpty(productDetail.getHourlyRoomItems())) {
//                hourlyRoomItemList.addAll(productDetail.getHourlyRoomItems());
//            }
//        }
//        ProductDetailDTO productDetailDTO = new ProductDetailDTO();
//        productDetailDTO.setHotelId(hotelId);
//        if (CollUtilX.isNotEmpty(daylyRoomItemList)) {
//            productDetailDTO.setRoomItems(aggregationResultDetail(daylyRoomItemList, hotelId));
//        }
//        if (CollUtilX.isNotEmpty(hourlyRoomItemList)) {
//            productDetailDTO.setHourlyRoomItems(aggregationResultDetail(hourlyRoomItemList, hotelId));
//        }
//        return productDetailDTO;
//    }

    /**
     * 结果聚合
     */
    private ProductDetailDTO aggregationResult(List<ProductDetailDTO> productMiddleDtoList, Long hotelId) {
        List<RoomItem> daylyRoomItemList = productMiddleDtoList.stream()
                .filter(productDetail -> productDetail.getHotelId() != null)
                .flatMap(productDetail -> productDetail.getRoomItems().stream())
                .collect(Collectors.toList());

        return new ProductDetailDTO(hotelId, daylyRoomItemList);
    }

    private List<RoomItem> aggregationResultDetail(List<RoomItem> allRoomItemList, Long hotelId) {
        Map<Long, List<RoomItem>> roomItemMap = allRoomItemList.stream().collect(groupingBy(RoomItem::getRoomId));
        Map<Long, String> roomNameMap = allRoomItemList.stream().collect(toMap(RoomItem::getRoomId,
                x -> Optional.of(x).map(RoomItem::getRoomName).orElse(""),
                (k1, k2) -> k2));
        List<RoomItem> roomItemList = Lists.newArrayList();
        for (Map.Entry<Long, List<RoomItem>> roomItemEntry : roomItemMap.entrySet()) {
            //没有房型映射的情况，房型id为0，统一处理
            if (roomItemEntry.getKey().equals(0L)) {
                if (CollUtilX.isNotEmpty(roomItemEntry.getValue())) {//非空
                    List<RoomItem> roomItemList1 = handleNoRoomMappingProducts(roomItemEntry.getValue(), hotelId.toString(), "TMP:DirectProduct" + hotelId, NumberUtil.generateUUID(), System.currentTimeMillis());
                    if (null != roomItemList1) {
                        roomItemList.addAll(roomItemList1);
                    }
                }
            } else {
                RoomItem roomItem = new RoomItem();
                roomItem.setRoomId(roomItemEntry.getKey());
                roomItem.setRoomName(roomNameMap.get(roomItemEntry.getKey()));
                List<ProductDetail> productDetailList = new ArrayList<>();
                for (RoomItem roomItem1 : roomItemEntry.getValue()) {
                    productDetailList.addAll(roomItem1.getProducts());
                }
                roomItem.setProducts(productDetailList);
                roomItemList.add(roomItem);
            }
        }
        return roomItemList;
    }

    /**
     * 处理无房型映射产品返回
     */
    private List<RoomItem> handleNoRoomMappingProducts(List<RoomItem> roomItemList, String hotelId, String lockKey, String lockUid, long starTime) {
        if (System.currentTimeMillis() - starTime > 5000) {//处理时间超过10秒就返回 防止加锁死循环
            stringRedisTemplate.delete(lockKey);
            log.error("handleNoRoomMappingProducts_Lock_Time_out");
            return null;
        }
        //非空集合已经判断过
        List<RoomItem> roomItems = null;
        try {
            Boolean lockStatusBoolean = stringRedisTemplate.opsForValue().setIfAbsent(lockKey, lockUid, 5, TimeUnit.SECONDS);
            if (lockStatusBoolean != null && lockStatusBoolean) {
                roomItems = new ArrayList<>();
                Map<String, String> nameAndIdMap = new HashMap<>();
                Map<String, String> roomNameMap = roomItemList.stream().collect(toMap(RoomItem::getRoomName,
                        x -> Optional.of(x).map(RoomItem::getRoomName).orElse(""),
                        (k1, k2) -> k2));

                //房型名称-房型Id
                Object obj = RedisTemplateX.hashGet(RedisKey.NO_ROOM_MAPPING_NAME_ID, hotelId);
                int i = 0;
                if (null != obj) {
                    Map<String, String> roomNMap = JSON.parseObject(obj.toString(), new TypeReference<Map<String, String>>() {
                    });
                    for (String roomName : roomNameMap.keySet()) {
                        if (null == roomNMap.get(roomName)) {
                            //房型id用酒店Id + n代替
                            nameAndIdMap.put(roomName, String.valueOf(Long.parseLong(hotelId) * 10 + roomNMap.size() + (i++)));
                        } else {
                            nameAndIdMap.put(roomName, roomNMap.get(roomName));
                        }
                    }
                } else {
                    for (String roomName : roomNameMap.keySet()) {
                        //房型id用酒店Id + n代替
                        nameAndIdMap.put(roomName, String.valueOf(Long.parseLong(hotelId) * 10 + (i++)));
                    }
                }

                Map<String, List<RoomItem>> roomItemMap = roomItemList.stream().collect(groupingBy(RoomItem::getRoomName));
                for (Map.Entry<String, List<RoomItem>> roomItemEntry : roomItemMap.entrySet()) {
                    RoomItem roomItemNew = new RoomItem();
                    roomItemNew.setRoomId(Long.valueOf(nameAndIdMap.get(roomItemEntry.getValue().get(0).getRoomName())));
                    roomItemNew.setRoomName(roomItemEntry.getValue().get(0).getRoomName());
                    List<ProductDetail> productDetailList = new ArrayList<>();
                    for (RoomItem roomItem : roomItemEntry.getValue()) {
                        productDetailList.addAll(roomItem.getProducts());
                    }
                    roomItemNew.setProducts(productDetailList);
                    roomItems.add(roomItemNew);
                }

                // 将无房型Id和名称存放进redis，方便后续下单流程使用
                Map<String, String> idAndNameMap = new HashMap<>();
                for (String roomName : nameAndIdMap.keySet()) {
                    idAndNameMap.put(nameAndIdMap.get(roomName), roomName);
                }

                Map<String, String> redisStr = new HashMap<>();
                redisStr.put(hotelId, JSON.toJSONString(idAndNameMap));
                RedisTemplateX.hPutAll(RedisKey.NO_ROOM_MAPPING_ID_NAME, redisStr);
                redisStr.put(hotelId, JSON.toJSONString(nameAndIdMap));
                RedisTemplateX.hPutAll(RedisKey.NO_ROOM_MAPPING_NAME_ID, redisStr);
                // 无论如何都要释放锁
                stringRedisTemplate.delete(lockKey);
            } else {
                // 已经加锁 就休眠循环重试
                Thread.sleep(100);
                return handleNoRoomMappingProducts(roomItemList, hotelId, lockKey, lockUid, starTime);
            }
        } catch (Exception e) {
            //无论如何都要释放锁
            stringRedisTemplate.delete(lockKey);
            log.error("无房型映射转换失败!", e);
        }
        return roomItems;
    }

    /**
     * 保存日志
     */
    private void saveSlsLog(String message, Date start, String logId) {
        Map<String, String> map = new HashMap<>();
        map.put(SlsEnum.LEVEL.getType(), SlsLogLevelEnum.INFO.getLevel());
        map.put(SlsEnum.NAME.getType(), "supply-queryProductInfo");
        map.put("logFlag", "排查慢问题");
        map.put("logId", logId);
        map.put(SlsEnum.MESSAGE.getType(), message);
        map.put("start", DateUtilX.dateToString(start, DateUtilX.hour_ms_format));
        slsLoggerUtil.saveLog(map, "shub-queryProductList", "tiangong-supply-direct-common-server");
    }
}
