package com.tiangong.supplierdirect.service;

import com.tiangong.supply.direct.entity.request.*;
import com.tiangong.supply.direct.entity.response.*;

public interface SupplyDirectOrderService {

    /**
     * 试预订
     */
    PreBookingResponse preBooking(PreBookingRequest request);

    /**
     * 创建订单
     */
    CreateSupplyOrderResponse createSupplyOrder(CreateSupplyOrderRequest request);

    /**
     * 查询订单状态
     */
    QuerySupplyOrderResponse querySupplyOrder(QuerySupplyOrderRequest request);

    /**
     * 取消订单
     */
    CancelOrderResponse cancelSupplyOrder(CancelOrderRequest request);

    /**
     * 开票接口
     * @param request
     * @return
     */
    CreateInvoiceResponse createInvoice(CreateOrderInvoiceRequest request);


}
