package com.tiangong.supplierdirect.service;

import com.tiangong.common.Response;
import com.tiangong.order.remote.dto.InBlankCheckInfoDTO;
import com.tiangong.order.remote.dto.InvoiceNotifyRequest;
import com.tiangong.order.remote.dto.OrderCheckDetailInfoDTO;
import com.tiangong.order.remote.response.SupplyOrderDTO;
import com.tiangong.supply.direct.entity.response.QuerySupplyOrderResponse;

import java.util.List;

public interface SupplyOrderDetailService {

    /**
     * 订单状态通知
     */
    Response<Object> orderStatusNotify(QuerySupplyOrderResponse request);

    /**
     * 开票结果通知
     */
    Response<Object> invoiceStatusNotify(InvoiceNotifyRequest request);

    /**
     * 入住明细
     */
    Response<Object> orderCheckDetail(OrderCheckDetailInfoDTO orderCheckDetailInfoDTO);

    /**
     * 获取需要没有填写入住明细的供货单
     */
    Response<List<SupplyOrderDTO>> queryInBlankCheckInfoSupplyOrderCodeList(InBlankCheckInfoDTO inBlankCheckInfoDTO);
}
