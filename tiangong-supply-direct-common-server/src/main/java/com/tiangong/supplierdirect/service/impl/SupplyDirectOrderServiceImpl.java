package com.tiangong.supplierdirect.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.tiangong.enums.PayMethodEnum;
import com.tiangong.exception.CustomException;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.supplierdirect.config.SupplyInfoConfig;
import com.tiangong.supplierdirect.routing.RoutingStrategy;
import com.tiangong.supplierdirect.service.SupplyDirectOrderService;
import com.tiangong.supplierdirect.util.ConvertUtils;
import com.tiangong.supply.direct.constant.SupplyDirectRedisCacheKey;
import com.tiangong.supply.direct.entity.base.BaseOrderResponse;
import com.tiangong.supply.direct.entity.dto.*;
import com.tiangong.supply.direct.entity.request.*;
import com.tiangong.supply.direct.entity.response.*;
import com.tiangong.supply.direct.enums.ActionEnum;
import com.tiangong.supply.direct.enums.ReturnNoEnum;
import com.tiangong.supply.direct.interfaces.BaseSupplyDirectOrderRemote;
import com.tiangong.util.CollUtilX;
import com.tiangong.util.StrUtilX;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class SupplyDirectOrderServiceImpl implements SupplyDirectOrderService {

    private final RoutingStrategy routingStrategy;

    /**
     * 试预订
     */
    @Override
    public PreBookingResponse preBooking(PreBookingRequest request) {
        PreBookingResponse preBookingResponse = new PreBookingResponse();
        preBookingResponse.setReturnCode(ReturnNoEnum._010000000.returnCode);
        preBookingResponse.setReturnDesc(ReturnNoEnum._010000000.returnDesc);
        String pricePlanId = request.getSpProductId().substring(0, request.getSpProductId().lastIndexOf("#"));
        String pricePlanName = request.getSpProductId().substring(request.getSpProductId().lastIndexOf("#") + 1);
        try {
            // 获取路由remote
            BaseSupplyDirectOrderRemote routingOrderRemote = getRoutingRemote(SupplierDirectClass.SHUB.supplierDirectClass, ActionEnum.PRE_BOOKING, ReturnNoEnum._011201049);
            PreBookingDto preBookingDto = new PreBookingDto() {{
                request.setPricePlanId(pricePlanId);
                request.setSpProductId(pricePlanId);
                setPreBookingRequest(request);
            }};
            preBookingResponse = requestSupplyApi(routingOrderRemote::preBooking, preBookingDto, SupplierDirectClass.SHUB.supplierDirectClass, ActionEnum.PRE_BOOKING, ReturnNoEnum._011201901);
            BeanUtils.copyProperties(request, preBookingResponse);
            preBookingResponse.setHotelId(request.getHotelId());
            preBookingResponse.setRoomId(request.getRoomId());
        } catch (CustomException e) {
            log.error("直连通用试预定失败,error:", e);
            preBookingResponse.setReturnCode(e.getFailCode());
            preBookingResponse.setReturnDesc(e.getMsg());
        } catch (Exception e) {
            log.error("直连通用试预定失败,error:", e);
            preBookingResponse.setReturnCode(ReturnNoEnum._010701900.returnCode);
            preBookingResponse.setReturnDesc(ReturnNoEnum._010701900.returnDesc);
        }
        // 缓存试预定产品信息
        cachePreBookingInfo(preBookingResponse, request, pricePlanName);
        return preBookingResponse;
    }

    /**
     * 创建订单
     */
    @Override
    public CreateSupplyOrderResponse createSupplyOrder(CreateSupplyOrderRequest request) {
        CreateSupplyOrderResponse createSupplyOrderResponse = new CreateSupplyOrderResponse();
        createSupplyOrderResponse.setReturnCode(ReturnNoEnum._010000000.returnCode);
        createSupplyOrderResponse.setReturnDesc(ReturnNoEnum._010000000.returnDesc);
        try {
            BaseSupplyDirectOrderRemote routingOrderRemote = getRoutingRemote(SupplierDirectClass.SHUB.supplierDirectClass, ActionEnum.CREATE_ORDER, ReturnNoEnum._011202049);
            CreateOrderDto createOrderDto = new CreateOrderDto() {{
                setCreateSupplyOrderRequest(request);
            }};
            createSupplyOrderResponse = requestSupplyApi(routingOrderRemote::createSupplyOrder, createOrderDto, SupplierDirectClass.SHUB.supplierDirectClass, ActionEnum.CREATE_ORDER, ReturnNoEnum._011202901);
        } catch (CustomException e) {
            log.error("直连通用创建订单失败,error:", e);
            createSupplyOrderResponse.setReturnCode(e.getFailCode());
            createSupplyOrderResponse.setReturnDesc(e.getMsg());
        } catch (Exception e) {
            log.error("直连通用创建订单失败,error:", e);
            createSupplyOrderResponse.setReturnCode(ReturnNoEnum._010701900.returnCode);
            createSupplyOrderResponse.setReturnDesc(ReturnNoEnum._010701900.returnDesc);
        }
        return createSupplyOrderResponse;
    }

    /**
     * 查询订单状态
     */
    @Override
    public QuerySupplyOrderResponse querySupplyOrder(QuerySupplyOrderRequest request) {
        BaseSupplyDirectOrderRemote routingOrderRemote = getRoutingRemote(SupplierDirectClass.SHUB.supplierDirectClass, ActionEnum.QUERY_ORDER, ReturnNoEnum._011204049);
        return requestSupplyApi(routingOrderRemote::querySupplyOrder, request, SupplierDirectClass.SHUB.supplierDirectClass, ActionEnum.QUERY_ORDER, ReturnNoEnum._011204901);
    }

    /**
     * 取消订单
     */
    @Override
    public CancelOrderResponse cancelSupplyOrder(CancelOrderRequest request) {
        BaseSupplyDirectOrderRemote routingOrderRemote = getRoutingRemote(SupplierDirectClass.SHUB.supplierDirectClass, ActionEnum.CANCEL_ORDER, ReturnNoEnum._011203049);
        return requestSupplyApi(routingOrderRemote::cancelSupplyOrder, request, SupplierDirectClass.SHUB.supplierDirectClass, ActionEnum.CANCEL_ORDER, ReturnNoEnum._011203901);
    }

    /**
     * 开票接口
     */
    @Override
    public CreateInvoiceResponse createInvoice(CreateOrderInvoiceRequest request) {
        String supplyClass = SupplyInfoConfig.getSupplyClassByCode(request.getSupplyCode());
        BaseSupplyDirectOrderRemote routingOrderRemote = getRoutingRemote(supplyClass, ActionEnum.INVOICE_ORDER, ReturnNoEnum._011205049);
        return requestSupplyApi(routingOrderRemote::createInvoice, request, supplyClass, ActionEnum.INVOICE_ORDER, ReturnNoEnum._011205901);
    }


    /**
     * 请求供应商api
     */
    private <T, R extends BaseOrderResponse> R requestSupplyApi(Function<T, R> function, T requestParam, String supplyClass, ActionEnum action, ReturnNoEnum returnNoEnum) {
        Long time = System.currentTimeMillis();
        try {
            R result = function.apply(requestParam);
            log.info("供应商supplyClass:{},{}请求参数:{},响应参数:{},调用时长:{}ms", supplyClass, action.name, JSON.toJSONString(requestParam), JSON.toJSONString(result), System.currentTimeMillis() - time);
            Assert.notNull(result, String.format("供应商supplyClass:%s,%s失败,返回结果为空!", supplyClass, action.name, requestParam));
            return result;
        } catch (Exception e) {
            //防止空指针
            BaseOrderResponse result = new BaseOrderResponse();
            log.error("供应商supplyClass:{},{}异常,请求参数:{},响应参数:{},调用时长:{}ms", supplyClass, action.name, JSON.toJSONString(requestParam), JSON.toJSONString(result), System.currentTimeMillis() - time, e);
            result.setReturnDesc(returnNoEnum.returnDesc);
            result.setReturnNo(returnNoEnum.returnNo);
            result.setReturnCode(returnNoEnum.returnCode);
            return (R) result;
        }
    }

    /**
     * 获取routingRemote
     */
    private BaseSupplyDirectOrderRemote getRoutingRemote(String supplyClass, ActionEnum actionEnum, ReturnNoEnum returnNoEnum) {
        BaseSupplyDirectOrderRemote routingOrderRemote = routingStrategy.getRoutingOrderRemote(supplyClass);
        Optional.ofNullable(routingOrderRemote).orElseThrow(() -> {
            log.error("{}失败,找不到匹配的供应商,supplyClass: {} ", actionEnum.name, supplyClass);
            return new CustomException(returnNoEnum.returnNo, returnNoEnum.returnCode, returnNoEnum.returnDesc);
        });
        return routingOrderRemote;
    }

    /**
     * 缓存试预定产品信息
     */
    private void cachePreBookingInfo(PreBookingResponse preBookingResponse, PreBookingRequest request, String pricePlanName) {
        ProductMiddleDto productMiddleDto = new ProductMiddleDto();
        StringBuffer ppKey = null;
        if (Objects.nonNull(preBookingResponse)) {
            productMiddleDto.setBedTypeDetails(preBookingResponse.getBedTypeDetails());

            productMiddleDto.setHotelId(Long.parseLong(preBookingResponse.getHotelId()));
            productMiddleDto.setHotelName(preBookingResponse.getHotelName());
            productMiddleDto.setRoomId(Integer.valueOf(preBookingResponse.getRoomId()));
            productMiddleDto.setRoomName(preBookingResponse.getRoomName());
            productMiddleDto.setSpPricePlanId(request.getPricePlanId());
            productMiddleDto.setSpPricePlanName(pricePlanName);
            productMiddleDto.setSupplyCode(preBookingResponse.getSupplyCode());
            productMiddleDto.setInvoiceModel(preBookingResponse.getInvoiceModel());
            productMiddleDto.setInvoiceType(preBookingResponse.getInvoiceType());
            // 到店付标识
            if (preBookingResponse.getPayMethod() != null && preBookingResponse.getPayMethod().equals(PayMethodEnum.PAY.key)) {
                productMiddleDto.setPayAtHotelFlag(1);
            } else {
                productMiddleDto.setPayAtHotelFlag(0);
            }
            //productMiddleDto.setReminder(preBookingResponse.getre);
            List<CancelRestriction> list = Lists.newArrayList();
            if (CollUtilX.isNotEmpty(preBookingResponse.getCancelRestrictions())) {
                list = preBookingResponse.getCancelRestrictions().stream().map(e -> {
                            CancelRestriction cancelRestriction = new CancelRestriction();
                            cancelRestriction.setCancelRestrictionType(e.getCancelRestrictionType());
                            cancelRestriction.setCancelRestrictionDay(e.getCancelRestrictionDay());
                            cancelRestriction.setCancelRestrictionTime(e.getCancelRestrictionTime());
                            cancelRestriction.setCancellationPrice(e.getCancellationPrice());
                            cancelRestriction.setCancelRestrictionRemark(e.getCancelRestrictionRemark());
                            cancelRestriction.setCancelPenalties(e.getCancelPenalties());
                            return cancelRestriction;
                        }
                ).collect(Collectors.toList());
            }
            List<OrderRoomDetailDto> orderRoomDetails = preBookingResponse.getOrderRoomDetails();
            if (CollUtilX.isNotEmpty(orderRoomDetails)) {
                List<CancelRestriction> finalList = list;
                List<RoomItemDetailDto> roomItemDetailDtos = orderRoomDetails.stream().map(orderRoomDetailDto -> {
                    RoomItemDetailDto roomItemDetailDto = new RoomItemDetailDto();
                    roomItemDetailDto.setRoomIndex(orderRoomDetailDto.getRoomIndex());
                    roomItemDetailDto.setAdultNum(orderRoomDetailDto.getAdultNum());
                    roomItemDetailDto.setChildAges(orderRoomDetailDto.getChildAges());
                    // 每间 价格明细(税费)
                    if (orderRoomDetailDto.getTaxDetail() != null) {
                        TaxDetailDto taxDetailDto = BeanUtil.copyProperties(orderRoomDetailDto.getTaxDetail(), TaxDetailDto.class);
                        roomItemDetailDto.setTaxDetail(taxDetailDto);
                    }
                    roomItemDetailDto.setPayInStorePrice(orderRoomDetailDto.getPayInStorePrice());
                    roomItemDetailDto.setPayInStoreCurrency(orderRoomDetailDto.getPayInStoreCurrency());
                    roomItemDetailDto.setSupplyPayInStorePrice(orderRoomDetailDto.getSupplyPayInStorePrice());
                    roomItemDetailDto.setSupplyPayInStoreCurrency(orderRoomDetailDto.getSupplyPayInStoreCurrency());
                    if (CollUtilX.isNotEmpty(orderRoomDetailDto.getPriceInfoDetails())) {
                        List<ProductDetailMiddleDto> productDetailMiddleDtos = orderRoomDetailDto.getPriceInfoDetails().stream().map(priceInfoDetail -> {
                            ProductDetailMiddleDto productDetailMiddleDto = new ProductDetailMiddleDto();
                            productDetailMiddleDto.setSaleDate(priceInfoDetail.getDate());
                            productDetailMiddleDto.setBasePrice(priceInfoDetail.getPrice());
                            productDetailMiddleDto.setCurrency(priceInfoDetail.getCurrency());
                            productMiddleDto.setCurrency(priceInfoDetail.getCurrency());
                            productDetailMiddleDto.setBreakfastType(priceInfoDetail.getBreakfastType());
                            productDetailMiddleDto.setBreakfastNum(priceInfoDetail.getBreakfastNum());
                            productDetailMiddleDto.setCancelRestrictions(finalList);
                            productDetailMiddleDto.setTaxDetail(priceInfoDetail.getTaxDetail());
                            Integer roomStatus;
                            if (Objects.equals(priceInfoDetail.getRoomStatus(), 3)) {
                                roomStatus = 0;
                            } else {
                                roomStatus = priceInfoDetail.getRoomStatus();
                            }
                            productDetailMiddleDto.setRoomStatus(roomStatus);
                            // 设置担保
                            productDetailMiddleDto.setGuaranteeType(preBookingResponse.getGuaranteeType());
                            productDetailMiddleDto.setGuaranteeCondition(preBookingResponse.getGuaranteeCondition());
                            productDetailMiddleDto.setGuaranteeFeeType(preBookingResponse.getGuaranteeFeeType());
                            return productDetailMiddleDto;
                        }).collect(Collectors.toList());
                        roomItemDetailDto.setProductDetails(productDetailMiddleDtos);
                    }
                    return roomItemDetailDto;
                }).collect(Collectors.toList());

                productMiddleDto.setRoomItemDetails(roomItemDetailDtos);
                productMiddleDto.setHourlyRoom(preBookingResponse.getHourlyRoom());
                productMiddleDto.setTips(preBookingResponse.getTips());
                if (productMiddleDto.getHourlyRoom() == 1) {
                    productMiddleDto.setHourlyRoomInfo(preBookingResponse.getHourlyRoomInfo());
                    ppKey = new StringBuffer().append(SupplyDirectRedisCacheKey.PREBOOKING_CACHE.concat(preBookingResponse.getSupplyCode())).append
                                    (StrUtilX.SPLIT_CODE).append(request.getPricePlanId())
                            .append(StrUtilX.SPLIT_CODE).append(preBookingResponse.getCheckInDate());
                } else {
                    // 拼接房间信息
                    String roomStr = ConvertUtils.roomAppend(request.getRoomGuestNumbers());
                    ppKey = new StringBuffer().append(SupplyDirectRedisCacheKey.PREBOOKING_CACHE)
                            .append(preBookingResponse.getSupplyCode()).append(StrUtilX.SPLIT_CODE).append(request.getPricePlanId()).append(StrUtilX.SPLIT_CODE);
                    // 房间不为空
                    if (roomStr.length() != 0) {
                        ppKey.append(roomStr).append(StrUtilX.SPLIT_CODE);
                    }
                    ppKey.append(preBookingResponse.getCheckInDate())
                            .append(StrUtilX.CHARACTER).append(preBookingResponse.getCheckOutDate());
                }
                RedisTemplateX.setAndExpire(ppKey.toString(), JSON.toJSONString(productMiddleDto), 60 * 60);
            }
        }
    }
}
