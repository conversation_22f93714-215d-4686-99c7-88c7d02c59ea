package com.tiangong.supplierdirect.service.impl;

import com.alibaba.fastjson.JSON;
import com.tiangong.common.Constant;
import com.tiangong.common.Response;
import com.tiangong.enums.ErrorCodeEnum;
import com.tiangong.enums.OrderStatusEnum;
import com.tiangong.keys.RedisKey;
import com.tiangong.order.remote.SupplyOrderRemote;
import com.tiangong.order.remote.dto.InBlankCheckInfoDTO;
import com.tiangong.order.remote.dto.InvoiceNotifyRequest;
import com.tiangong.order.remote.dto.OrderCheckDetailInfoDTO;
import com.tiangong.order.remote.request.SaveSupplyResultDTO;
import com.tiangong.order.remote.response.SupplyOrderDTO;
import com.tiangong.redis.core.RedisTemplateX;
import com.tiangong.supplierdirect.service.SupplyOrderDetailService;
import com.tiangong.supply.direct.entity.response.QuerySupplyOrderResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
@RequiredArgsConstructor
public class SupplyOrderStatusNotifyServiceImpl implements SupplyOrderDetailService {

    private final SupplyOrderRemote supplyOrderRemote;

    @Override
    public Response<Object> orderStatusNotify(QuerySupplyOrderResponse request) {
        SaveSupplyResultDTO saveSupplyResultDTO = new SaveSupplyResultDTO();
        saveSupplyResultDTO.setSupplyOrderCode(request.getSupplyOrderCode());
        // 待确认不通知
        if (Objects.equals(request.getSupplyResult(), OrderStatusEnum.CONFIRMING.no)) {
            return Response.error(ErrorCodeEnum.NOTIFY_ORDER_FAIL.errorCode, ErrorCodeEnum.NOTIFY_ORDER_FAIL.errorDesc);
        }
        if (Objects.equals(request.getSupplyResult(), OrderStatusEnum.REFUSE.no)) {
            saveSupplyResultDTO.setConfirmationStatus(OrderStatusEnum.CANCELED.no);
        } else {
            saveSupplyResultDTO.setConfirmationStatus(request.getSupplyResult());
        }
        saveSupplyResultDTO.setRefundFee(request.getRefundFee());
        saveSupplyResultDTO.setRefundFeeCurrency(request.getRefundFeeCurrency());
        saveSupplyResultDTO.setConfirmationCode(request.getConfirmationNumber());
        saveSupplyResultDTO.setRemark(request.getReason());
        saveSupplyResultDTO.setRefusedReason(request.getRefusedReason());
        saveSupplyResultDTO.setOperator(Constant.SYSTEM);
        saveSupplyResultDTO.setSupplierOrderCode(request.getSupplierOrderCode());
        // 写入缓存，定时任务消费，减少响应时长
        List<String> list = new ArrayList<>();
        list.add(JSON.toJSONString(saveSupplyResultDTO));
        RedisTemplateX.lLeftPushAll(RedisKey.ORDER_RESULT_NOTIFY, list);//将编码写入redis

        return Response.success();

    }

    @Override
    public Response<Object> invoiceStatusNotify(InvoiceNotifyRequest request) {
        return supplyOrderRemote.pushInvoiceStatus(request);
    }

    @Override
    public Response<Object> orderCheckDetail(OrderCheckDetailInfoDTO orderCheckDetailInfoDTO) {
        return supplyOrderRemote.pushOrderCheckDetail(orderCheckDetailInfoDTO);
    }

    @Override
    public Response<List<SupplyOrderDTO>> queryInBlankCheckInfoSupplyOrderCodeList(InBlankCheckInfoDTO inBlankCheckInfoDTO) {
        return supplyOrderRemote.queryInBlankCheckInfoSupplyOrderCodeList(inBlankCheckInfoDTO);
    }
}
