package com.tiangong.supplierdirect.routing;

import com.google.common.collect.Maps;
import com.tiangong.supply.direct.annotations.SuppyDirectClass;
import com.tiangong.supply.direct.interfaces.BaseSupplyDirectOrderRemote;
import com.tiangong.supply.direct.interfaces.BaseSupplyDirectProductRemote;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.lang.annotation.Annotation;
import java.util.Map;
import java.util.Objects;

/**
 * 根据supplyClass路由到对应的FeignClient
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RoutingStrategy {

    private final ApplicationContext applicationContext;

    private Map<String, Object> beansWithAnnotation = Maps.newConcurrentMap();

    @PostConstruct
    private void RoutingStrategy() {
        beansWithAnnotation = applicationContext.getBeansWithAnnotation(SuppyDirectClass.class);
    }


    /**
     * 获取订单路由feign接口
     */
    public BaseSupplyDirectOrderRemote getRoutingOrderRemote(String supplyClass) {
        return getBaseRemote(supplyClass, BaseSupplyDirectOrderRemote.class);
    }


    /**
     * 获取查询产品路由feign接口
     */
    public BaseSupplyDirectProductRemote getRoutingProductRemote(String supplyClass) {
        return getBaseRemote(supplyClass, BaseSupplyDirectProductRemote.class);
    }

    /**
     * 根据供应商类型获取对应的feign接口
     */
    private <T> T getBaseRemote(String supplyClass, Class<T> t) {
        for (Map.Entry<String, Object> stringObjectEntry : beansWithAnnotation.entrySet()) {
            try {
                Class aClass = Class.forName(stringObjectEntry.getKey());
                if (t.isAssignableFrom(aClass)) {
                    for (Annotation annotation : aClass.getAnnotations()) {
                        if (Objects.equals(annotation.annotationType(), SuppyDirectClass.class)) {
                            SuppyDirectClass supplyClassAnnotation = (SuppyDirectClass) annotation;
                            if (Objects.equals(supplyClassAnnotation.value(), supplyClass)) {
                                return (T) stringObjectEntry.getValue();
                            }
                        }
                    }
                }
            } catch (Exception e) {
                log.error("找不到供应商:{}的路由remote", supplyClass, e);
            }
        }
        return null;
    }


}
