package com.tiangong.supplierdirect.server;

import com.tiangong.annotations.SlsLog;
import com.tiangong.cloud.common.anno.AnonymousAccess;
import com.tiangong.supplierdirect.service.SupplyDirectOrderService;
import com.tiangong.supply.direct.entity.request.*;
import com.tiangong.supply.direct.entity.response.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("supply/direct")
public class SupplyDirectOrderController {

    private final SupplyDirectOrderService supplyDirectOrderService;

    /**
     * 试预订
     */
    @AnonymousAccess
    @RequestMapping(value = "/pre/booking", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @SlsLog(level = "info", name = "查询", message = "pre/booking", topic = "/supply/direct/pre/booking", source = "tiangong-supply-direct-common-server")
    public PreBookingResponse preBooking(@RequestBody @Validated PreBookingRequest request) {
        return supplyDirectOrderService.preBooking(request);
    }

    /**
     * 创建订单
     */
    @AnonymousAccess
    @RequestMapping(value = "/create/order", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @SlsLog(level = "info", name = "查询", message = "create/order", topic = "/supply/direct/create/order", source = "tiangong-supply-direct-common-server")
    public CreateSupplyOrderResponse createSupplyOrder(@RequestBody @Validated CreateSupplyOrderRequest request) {
        return supplyDirectOrderService.createSupplyOrder(request);
    }

    /**
     * 查询订单状态
     */
    @AnonymousAccess
    @RequestMapping(value = "/query/order", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @SlsLog(level = "info", name = "查询", message = "query/order", topic = "/supply/direct/query/order", source = "tiangong-supply-direct-common-server")
    public QuerySupplyOrderResponse querySupplyOrder(@RequestBody @Validated QuerySupplyOrderRequest request) {
        return supplyDirectOrderService.querySupplyOrder(request);
    }

    /**
     * 取消订单
     */
    @AnonymousAccess
    @RequestMapping(value = "/cancel/order", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @SlsLog(level = "info", name = "查询", message = "cancel/order", topic = "/supply/direct/cancel/order", source = "tiangong-supply-direct-common-server")
    public CancelOrderResponse cancelSupplyOrder(@RequestBody @Validated CancelOrderRequest request) {
        return supplyDirectOrderService.cancelSupplyOrder(request);
    }

    /**
     * 开票接口
     */
    @AnonymousAccess
    @RequestMapping(value = "/createInvoice", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @SlsLog(level = "info", name = "查询", message = "createInvoicer", topic = "/supply/direct/createInvoice", source = "tiangong-supply-direct-common-server")
    public CreateInvoiceResponse createInvoice(@RequestBody @Validated CreateOrderInvoiceRequest request){
        return supplyDirectOrderService.createInvoice(request);
    }


}
