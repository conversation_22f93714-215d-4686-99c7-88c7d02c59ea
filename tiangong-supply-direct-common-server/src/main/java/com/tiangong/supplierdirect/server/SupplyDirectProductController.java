package com.tiangong.supplierdirect.server;

import com.tiangong.annotations.SlsLog;
import com.tiangong.cloud.common.anno.AnonymousAccess;
import com.tiangong.common.Response;
import com.tiangong.supplierdirect.service.SupplyDirectProductService;
import com.tiangong.supply.direct.entity.dto.ProductDetailDTO;
import com.tiangong.supply.direct.entity.dto.ProductMiddleDto;
import com.tiangong.supply.direct.entity.request.QuerySupplyProductRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @date:2022/8/9
 **/

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("supply/direct")
public class SupplyDirectProductController {

    private final SupplyDirectProductService supplyDirectProductService;

    /**
     * 获取产品详情数据
     */
    @AnonymousAccess
    @RequestMapping(value = "/query/product", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @SlsLog(level = "info", name = "查询", message = "query/product", topic = "/supply/direct/query/product", source = "tiangong-supply-direct-common-server")
    Response<ProductDetailDTO> queryProductInfo(@RequestBody @Validated QuerySupplyProductRequest querySupplyProductRequest) {
        return supplyDirectProductService.queryProductInfo(querySupplyProductRequest);
    }


    /**
     * 获取指定供应商产品详情
     */
    @AnonymousAccess
    @RequestMapping(value = "/query/product/supplyCode", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @SlsLog(level = "info", name = "查询", message = "query/product/supplyCode", topic = "/supply/direct/query/product/supplyCode", source = "tiangong-supply-direct-common-server")
    Response<List<ProductMiddleDto>> queryProductInfoBySupplyCode(@RequestBody  @Validated QuerySupplyProductRequest querySupplyProductRequest){
        return supplyDirectProductService.querySupplyProductByCode(querySupplyProductRequest);
    }


}
