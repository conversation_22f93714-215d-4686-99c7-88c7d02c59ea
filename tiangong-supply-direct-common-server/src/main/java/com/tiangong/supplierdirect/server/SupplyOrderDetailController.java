package com.tiangong.supplierdirect.server;

import cn.hutool.json.JSONUtil;
import com.tiangong.annotations.SlsLog;
import com.tiangong.cloud.common.anno.AnonymousAccess;
import com.tiangong.common.Response;
import com.tiangong.order.remote.dto.InBlankCheckInfoDTO;
import com.tiangong.order.remote.dto.InvoiceNotifyRequest;
import com.tiangong.order.remote.dto.OrderCheckDetailInfoDTO;
import com.tiangong.order.remote.response.SupplyOrderDTO;
import com.tiangong.supplierdirect.service.SupplyOrderDetailService;
import com.tiangong.supply.direct.entity.response.QuerySupplyOrderResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("supply/direct")
public class SupplyOrderDetailController {

    private final SupplyOrderDetailService supplyOrderDetailService;

    /**
     * 订单状态通知
     */
    @AnonymousAccess
    @PostMapping("order/status/notify")
    @SlsLog(level = "info", name = "查询", message = "order/status/notify", topic = "/supply/direct/order/status/notify", source = "tiangong-supply-direct-common-server")
    Response<Object> orderStatusNotify(@RequestBody @Validated QuerySupplyOrderResponse request) {
        return supplyOrderDetailService.orderStatusNotify(request);
    }

    /**
     * 开票推送接口
     */
    @AnonymousAccess
    @PostMapping("/invoice/status/notify")
    @SlsLog(level = "info", name = "查询", message = "invoice/status/notify", topic = "/supply/direct/invoice/status/notify", source = "tiangong-supply-direct-common-server")
    Response<Object> invoiceStatusNotify(@RequestBody @Validated InvoiceNotifyRequest request) {
        return supplyOrderDetailService.invoiceStatusNotify(request);
    }

    /**
     * 订单入住明细
     */
    @AnonymousAccess
    @PostMapping("/order/check/detail")
    @SlsLog(level = "info", name = "查询", message = "order/check/detail", topic = "/supply/direct/order/check/detail", source = "tiangong-supply-direct-common-server")
    Response<Object> orderCheckDetail(@RequestBody @Validated OrderCheckDetailInfoDTO orderCheckDetailInfoDTO) {
        return supplyOrderDetailService.orderCheckDetail(orderCheckDetailInfoDTO);
    }

    /**
     * 获取没有填写入住明细的供货单
     */
    @AnonymousAccess
    @PostMapping("/order/queryInBlankCheckInfoSupplyOrderCodeList")
    @SlsLog(level = "info", name = "查询", message = "queryInBlankCheckInfoSupplyOrderCodeList", topic = "order/queryInBlankCheckInfoSupplyOrderCodeList", source = "tiangong-supply-direct-common-server")
    Response<List<SupplyOrderDTO>> queryInBlankCheckInfoSupplyOrderCodeList(@RequestBody InBlankCheckInfoDTO inBlankCheckInfoDTO) {
        try {
            return supplyOrderDetailService.queryInBlankCheckInfoSupplyOrderCodeList(inBlankCheckInfoDTO);
        } catch (Exception e) {
            log.error("获取没有填写入住明细的供货单异常，inBlankCheckInfoDTO={}", JSONUtil.toJsonStr(inBlankCheckInfoDTO));
        }
        return Response.success(null);
    }
}
