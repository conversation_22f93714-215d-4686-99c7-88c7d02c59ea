package com.tiangong.invoice.enums;

public enum InvoiceTypeEnums {
    NORMAL(1, "普通发票", "normal_invoice"),
    SPECIAL(2, "专用发票", "special_vat_invoice"),
    E_NORMAL(3, "电子普通发票", "elec_normal_invoice"),
    E_SPECIAL(4, "电子专用发票", "elec_special_vat_invoice"),
    E_TOLL_NORMAL(5, "通行费电子普票", "toll_elec_normal_invoice"),
    BLOCKCHAIN(6, "区块链发票", "blockchain_invoice"),
    E_GENERAL_PRINT(7, "通用机打电子发票", "special_vat_invoice"),
    //两种全电发票
    A_E_NORMAL(8, "电子发票(普通发票)", "elec_invoice_normal"),
    A_E_SPECIAL(9, "电子发票(专用发票)", "elec_invoice_special"),

    ROLL_TYPE(98, "增值税卷式发票", "roll_normal_invoice"),
    OTHER(99, "其它发票", "special_vat_invoice");

    public int key;
    public String value;
    public String baiDuCode;

    InvoiceTypeEnums(int key, String value, String baiDuCode) {
        this.key = key;
        this.value = value;
        this.baiDuCode = baiDuCode;
    }

    public static int getKeyByValue(String value) {
        Integer key = null;
        for (InvoiceTypeEnums invoiceTypeEnum : InvoiceTypeEnums.values()) {
            if (invoiceTypeEnum.value.equals(value)) {
                key = invoiceTypeEnum.key;
                break;
            }
        }
        return key;
    }

    public static String getValueByKey(int key) {
        String value = null;
        for (InvoiceTypeEnums invoiceTypeEnum : InvoiceTypeEnums.values()) {
            if (invoiceTypeEnum.key == key) {
                value = invoiceTypeEnum.value;
                break;
            }
        }
        return value;
    }

    public static InvoiceTypeEnums getEnumByKey(int key) {
        for(InvoiceTypeEnums billStatus : InvoiceTypeEnums.values()) {
            if(billStatus.key == key) {
                return billStatus;
            }
        }
        return null;
    }

    public int getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }
}
