package com.tiangong.invoice.resp;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 财务日志表
 * 返回参数
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-22 10:49:56
 */
@Data
public class FinanceLogResp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增长主键id
     */
    private Long id;
    /**
     * 日志类型 日志类型：0-进项票、1-销项票
     */
    private Integer logType;
    /**
     * 对象id
     */
    private String objId;
    /**
     * 对象状态
     */
    private Integer objStatus;
    /**
     * 操作内容
     */
    private String content;
    /**
     * 删除状态
     */
    private Integer deleted;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private Date createdDt;
    /**
     * 更新人
     */
    private String updatedBy;
    /**
     * 更新时间
     */
    private Date updatedDt;
}