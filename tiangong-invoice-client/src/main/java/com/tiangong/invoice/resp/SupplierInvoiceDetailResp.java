package com.tiangong.invoice.resp;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName:
 * @Description:
 * @date 2023/12/25
 */
@Data
public class SupplierInvoiceDetailResp implements Serializable {
    /**
     * 发票id
     */
    private String invoiceId;

    /**
     * 发票状态 发票状态：0-未使用，1-待检查，2-部分使用,3已使用
     */
    private Integer invoiceStatus;

    /**
     * 发票类型 发票类型：1-普通发票、2-专用发票、3-电子普通发票、4-电子专用发票、5-通行费电子普票、6-区块链发票、7-通用机打电子发票、8-电子发票(普通发票)、9-电子发票（专用发票）
     */
    private Integer invoiceType;

    /**
     * 购买方名称
     */
    private String purchaserName;

    /**
     * 购买方纳税人识别号
     */
    private String purchaserRegisterNum;

    /**
     * 发票号码
     */
    private String invoiceNum;

    /**
     * 发票代码
     */
    private String invoiceCode;

    /**
     * 开票时间
     */
    private Date invoiceDate;

    /**
     * 发票内容
     */
    private String invoiceContent;

    /**
     * 销售方名称
     */
    private String sellerName;

    /**
     * 销售方纳税人识别号
     */
    private String sellerRegisterNum;

    /**
     * 发票金额
     */
    private BigDecimal invoiceAmt;

    /**
     * 合计税额
     */
    private BigDecimal totalTaxAmt;

    /**
     * 发票税率
     */
    private BigDecimal invoiceTaxRate;

    /**
     * 备注
     */
    private String remark;

    /**
     * 发票图片url
     */
    private String url;

    /**
     * 发票来源类型 发票来源类型：0-手动录入，1-ocr识别，2系统同步，3-ireve指定客户上传，10-其它
     */
    private Integer invoiceSourceType;

    /**
     * 余额
     */
    private BigDecimal balance;

    /**
     * 票单领用金额
     */
    private BigDecimal billUsedAmt;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdDt;

    /**
     * 关联票单列表
     */
    private List<SupplierInvoiceBillResp> invoiceBillList;

}
