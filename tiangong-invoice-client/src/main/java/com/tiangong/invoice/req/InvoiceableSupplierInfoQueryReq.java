package com.tiangong.invoice.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.tiangong.common.PageDto;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @ClassName:
 * @Description:
 * @date 2023/12/26
 */
@Data
public class InvoiceableSupplierInfoQueryReq extends PageDto implements Serializable {

    private static final long serialVersionUID = 4015707204897408042L;
    /**
     * 查询口径 0-下单日期、1-入住日期、2-离店日期
     */
    private Integer dateQueryType;

    /**
     * 结算方式： -1-全部、0-月结、1-半月结、2-周结、3-单结、4-日结
     */
    private Integer settlementType;


    /**
     * 供货单号
     */
    private String supplyOrderCode;

    /**
     * 酒店名称
     */
    private String hotelName;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 商家编码
     */
    private String merchantCode;

    /**
     * 开始日期
     */
//    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String startDate;

    /**
     * 结束日期
     */
//    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String endDate;

}
