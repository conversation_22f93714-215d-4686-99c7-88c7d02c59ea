package com.tiangong.invoice.req;

import com.tiangong.common.PageDto;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单历史开票金额及状态表
 * 请求参数
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-22 10:49:56
 */
@Data
public class AgentInvoiceBillOrderStateReq extends PageDto implements Serializable {


    private static final long serialVersionUID = 3218612512815436516L;
    /**
     * 自增长主键id
     */
    private Long id;
    /**
     * 订单号
     */
    private String orderCode;
    /**
     * 支付方式
     * @see com.tiangong.enums.PayMethodEnum
     */
    private Integer payMethod;
    /**
     * 票单状态 票单状态： 0未绑定，9已坏账，10已平账
     */
    private Integer invoiceBillStatus;
    /**
     * 订单累计开票金额
     */
    private BigDecimal alreadyInvoiceAmt;
    /**
     * 订单当前开票中票单id
     */
    private Long currentInvoiceBillId;
    /**
     * 乐观锁版本号
     */
    private Integer revision;
    /**
     * 删除状态
     */
    private Integer deleted;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private Date createdDt;
    /**
     * 更新人
     */
    private String updatedBy;
    /**
     * 更新时间
     */
    private Date updatedDt;
}