package com.tiangong.invoice.req;

import lombok.Data;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 票单关联发票表
 * 请求参数
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-22 10:49:56
 */
@Data
public class SupplierInvoiceBillRelationReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增长主键id
     */
    private Long id;
    /**
     * 票单id
     */
    private Long invoiceBillId;

    /**
     * 票单状态
     */
    private Integer invoiceBillStatus;

    /**
     * 发票id
     */
    private Long invoiceId;

    /**
     * 本票单使用金额
     */
    @DecimalMin(value = "0", message = "本票单使用金额0！")
    @NotNull(message = "本票单使用金额不允许为空！")
    private BigDecimal currentAmt;

    /**
     * 修改后本票单使用金额
     */
    @DecimalMin(value = "0", message = "本票单使用金额0！")
    @NotNull(message = "本票单使用金额不允许为空！")
    private BigDecimal modifyAmount;

    /**
     * 修改后增加金额
     */
    @NotNull(message = "修改后增加金额不允许为空！")
    private BigDecimal amount;

    /**
     * 未绑定金额
     */
    @NotNull(message = "未绑定金额不允许为空！")
    private BigDecimal uninvoicedAmt;

    /**
     * 乐观锁版本号
     */
    private Integer revision;
    /**
     * 删除状态
     */
    private Integer deleted;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private Date createdDt;
    /**
     * 更新人
     */
    private String updatedBy;
    /**
     * 更新时间
     */
    private Date updatedDt;
}