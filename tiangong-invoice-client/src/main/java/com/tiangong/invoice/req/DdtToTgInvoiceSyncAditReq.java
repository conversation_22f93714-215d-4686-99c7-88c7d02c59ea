package com.tiangong.invoice.req;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @ClassName:
 * @Description:
 * @date 2023/12/28
 */
@Data
public class DdtToTgInvoiceSyncAditReq implements Serializable {
    /**
     * 发票类型
     */
    private Integer invoiceType;
    /**
     * 购方地址及电话
     */
    private String purchaserAddress;
    /**
     * 购方开户行及账号
     */
    private String purchaserBank;
    /**
     * 购买方名称
     */
    private String purchaserName;
    /**
     * 购买方纳税人识别号
     */
    private String purchaserRegisterNum;
    /**
     * 发票代码
     */
    private String invoiceCode;
    /**
     * 发票号码
     */
    private String invoiceNum;
    /**
     * 开票时间
     */
    private String invoiceDate;
    /**
     * 发票内容
     */
    private String commodityName;
    /**
     * 销售方
     */
    private String sellerAddress;
    /**
     * 销售方
     */
    private String sellerBank;
    /**
     * 销售方名称
     */
    private String sellerName;
    /**
     * 销售方纳税人识别号
     */
    private String sellerRegisterNum;
    /**
     * 发票金额（价税合计）
     */
    private BigDecimal amountInFiguers;
    /**
     * 合计税额
     */
    private BigDecimal totaltax;
    /**
     * 发票税率，0-100
     */
    private BigDecimal invoicetaxrate;
    /**
     * ireve客户编码
     */
    private String ireveAgentCode;
    /**
     * 备注
     */
    private String remarks;
    /**
     * 发票文件路径
     */
    private String url;
    /**
     * 校验码
     */
    private String checkCode;
    /**
     * 合计金额,不含税
     */
    private BigDecimal totalAmount;
    /**
     * saas商家编码
     */
    private String saasMerchantCode;
}
