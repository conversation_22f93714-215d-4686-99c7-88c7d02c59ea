package com.tiangong.invoice.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName:
 * @Description:
 * @date 2023/12/27
 */
@Data
public class DdtToTgInvoiceBillSyncAditReq implements Serializable {

    /**
     * ireve票单ID
     */
    private String ireveInvoiceBillId;

    /**
     * 分销商编码
     */
    private String ireveAgentCode;

    /**
     * 天宫商家编码
     */
    private String saasMerchantCode;

    /**
     * 天宫供应商编码
     */
    private String saasSupplierCode;

    /**
     * 酒店id
     */
    private Long hotelId;

    /**
     * 开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String beginDate;

    /**
     * 结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String endDate;

    /**
     * 日期类型 0-下单日期、1-入住日期、2-离店日期
     */
    private Integer dateQueryType;

    /**
     * ireve票单明细
     */
    private List<DdtToTgInvoiceBillOrderSyncAditReq> ireveOrderCodeList;

    /**
     * 是否自动票单：0-手动票单、1-自动票单
     */
    private Integer isAuto;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 结算客户来源 1：saas 2：加力天宫
     *
     */
    private Integer settleAgentSource;


}
