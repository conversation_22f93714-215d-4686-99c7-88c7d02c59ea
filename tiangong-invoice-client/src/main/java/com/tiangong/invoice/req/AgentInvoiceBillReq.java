package com.tiangong.invoice.req;

import com.tiangong.common.PageDto;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 供应商票单表
 * 请求参数
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-22 10:49:56
 */
@Data
public class AgentInvoiceBillReq extends PageDto implements Serializable {

    private static final long serialVersionUID = -5088940805190312156L;
    /**
     * 票单id
     */
    private Long invoiceBillId;
    /**
     * 票单编码
     */
    private String invoiceBillCode;
    /**
     * 票单名称
     */
    private String invoiceBillName;
    /**
     * 客户编码
     */
    private String agentCode;
    /**
     * 票单状态 票单状态：0未绑定，9已坏账，10已平账
     */
    private Integer invoiceBillStatus;
    /**
     * 开始日期
     */
    private Date startDate;
    /**
     * 结束日期
     */
    private Date endDate;
    /**
     * 日期类型 日期类型：0-下单日期、1-入住日期、2-离店日期
     */
    private Integer dateType;
    /**
     * 乐观锁版本号
     */
    private Integer revision;
    /**
     * 备注
     */
    private String remark;
    /**
     * 确认时间
     */
    private Date confirmDt;
    /**
     * 删除状态
     */
    private Integer deleted;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private Date createdDt;
    /**
     * 更新人
     */
    private String updatedBy;
    /**
     * 更新时间
     */
    private Date updatedDt;
}